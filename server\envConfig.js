const aksEnvArr = ["prod", "stage", "qa1", "qa2", "dev", "perf1"];
const pcfEnvArr = ["prod", "stage", "qa", "acceptance", "dev", "perf"];

const VERSION = process.env.ARTIFACT_VERSION || 'LOCAL';
const DEPLOYEDON = process.env.DATE_OF_DEPLOYMENT || 'LOCAL';

const BASE_URL = process.env.BASE_URL || 'https://ocom.qa1.westus.aks.az.albertsons.com';
const envArr = (BASE_URL.includes("ocom-") || BASE_URL.includes("ocom.")) ? aksEnvArr : pcfEnvArr;
const isAKS = (BASE_URL.includes("ocom-") || BASE_URL.includes("ocom."));
const ENV = isAKS ? envArr.find(e=>BASE_URL.includes(e)) : process.env.NODE_ENV || 'dev';

exports.ENV = ENV;
exports.BASE_URL = BASE_URL;
exports.ISAKS = isAKS;
exports.VERSION = VERSION;
exports.DEPLOYED_ON = DEPLOYEDON;
exports.ENV_ARR = envArr;
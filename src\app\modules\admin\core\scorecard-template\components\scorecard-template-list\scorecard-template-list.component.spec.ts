import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ScorecardTemplateListComponent } from './scorecard-template-list.component';
import { AdminScorecardTemplateService } from '@appAdminServices/admin-scorecard-template.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { CreateScorecardTemplateComponent } from '../create-scorecard-template/create-scorecard-template.component'; // Adjust the path as needed

describe('ScorecardTemplateListComponent', () => {
    let component: ScorecardTemplateListComponent;
    let fixture: ComponentFixture<ScorecardTemplateListComponent>;
    let mockAdminScorecardTemplateService: jasmine.SpyObj<AdminScorecardTemplateService>;
    let mockModalService: jasmine.SpyObj<BsModalService>;

    beforeEach(async () => {
        mockAdminScorecardTemplateService = jasmine.createSpyObj('AdminScorecardTemplateService', ['listCorporateScorecard']);
        mockAdminScorecardTemplateService.listCorporateScorecard.and.returnValue(of([]));
        mockModalService = jasmine.createSpyObj('BsModalService', ['show']);

        await TestBed.configureTestingModule({
            declarations: [ScorecardTemplateListComponent],
            providers: [
                { provide: AdminScorecardTemplateService, useValue: mockAdminScorecardTemplateService },
                { provide: BsModalService, useValue: mockModalService },
                { provide: BsModalRef, useValue: {} }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ScorecardTemplateListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call getScoreCardTemplateAPI on ngOnInit', () => {
        spyOn(component, 'getScoreCardTemplateAPI');
        component.ngOnInit();
        expect(component.getScoreCardTemplateAPI).toHaveBeenCalled();
    });

    it('should populate scorecardList when getScoreCardTemplateAPI is called', () => {
        const mockData = [{ id: 1, name: 'Scorecard 1' }];
        mockAdminScorecardTemplateService.listCorporateScorecard.and.returnValue(of(mockData));
        component.getScoreCardTemplateAPI();
        expect(component.scorecardList).toEqual(mockData);
    });

    it('should unsubscribe from subscriptions on ngOnDestroy', () => {
        spyOn(component.subs, 'unsubscribe');
        component.ngOnDestroy();
        expect(component.subs.unsubscribe).toHaveBeenCalled();
    });

    it('should open the scorecard modal with default data when no scorecard is passed', () => {
        const mockModalRef = { content: { messageEvent: of(true) } } as BsModalRef;
        mockModalService.show.and.returnValue(mockModalRef);

        component.openScorecardModal();

        expect(mockModalService.show).toHaveBeenCalledWith(CreateScorecardTemplateComponent, {
            class: "modal-dialog-centered add-scorecard-tmpl",
            initialState: false
        });
    });

    it('should open the scorecard modal with provided scorecard data', () => {
        const mockScorecard = { id: 1, name: 'Scorecard 1' };
        const mockModalRef = { content: { messageEvent: of(true) } } as BsModalRef;
        mockModalService.show.and.returnValue(mockModalRef);

        component.openScorecardModal(mockScorecard);

        expect(mockModalService.show).toHaveBeenCalledWith(CreateScorecardTemplateComponent, {
            class: "modal-dialog-centered add-scorecard-tmpl",
            initialState: mockScorecard
        });
    });

    it('should refresh scorecard list when modal emits a message event', () => {
        const mockModalRef = { content: { messageEvent: of(true) } } as BsModalRef;
        mockModalService.show.and.returnValue(mockModalRef);
        spyOn(component, 'getScoreCardTemplateAPI');

        component.openScorecardModal();

        expect(component.getScoreCardTemplateAPI).toHaveBeenCalled();
    });
});
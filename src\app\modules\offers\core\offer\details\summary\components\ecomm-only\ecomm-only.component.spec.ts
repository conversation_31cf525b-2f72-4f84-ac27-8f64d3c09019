import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { EcommOnlyComponent } from './ecomm-only.component';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';

describe('EcommOnlyComponent', () => {
  let component: EcommOnlyComponent;
  let fixture: ComponentFixture<EcommOnlyComponent>;

  beforeEach(() => {
    const initialDataServiceStub = () => ({ getAppDataName: string => ({}) });
    const offerMappingServiceStub = () => ({
      offerDataSrc: { subscribe: f => f({
        info: {
          ecommPromoType: 'DA'
        }
      }) }
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({}),
    });
    const generalOfferTypeServiceStub = () => ({});
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [EcommOnlyComponent],
      providers: [
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: OfferMappingService, useFactory: offerMappingServiceStub },
        {
          provide: GeneralOfferTypeService,
          useFactory: generalOfferTypeServiceStub
        },
        { provide:FeatureFlagsService, useFactory: featureFlagServiceStub}
      ]
    });
    fixture = TestBed.createComponent(EcommOnlyComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      const featureFlagServiceStub:FeatureFlagsService = fixture.debugElement.injector.get(FeatureFlagsService);
      spyOn(initialDataServiceStub, 'getAppDataName').and.returnValue({DA: 'test'});
      spyOn(featureFlagServiceStub,"isFeatureFlagEnabled");
      component.ngOnInit();
      expect(initialDataServiceStub.getAppDataName).toHaveBeenCalled();
      expect(featureFlagServiceStub.isFeatureFlagEnabled).toHaveBeenCalled();
    });
  });

  describe('get showEcommPromoVal', () => {
    it('should return ecommPromoType if it is defined', () => {
      component.ecommPromoType = 'PromoType';
      expect(component.showEcommPromoVal).toEqual('PromoType');
    });

    it('should return ecommPromoCode if ecommPromoType is undefined and ecommPromoCode is defined', () => {
      component.ecommPromoType = undefined;
      component.offerData = { info: { ecommPromoCode: 'PromoCode' } };
      expect(component.showEcommPromoVal).toEqual('PromoCode');
    });

    it('should return order if both ecommPromoType and ecommPromoCode are undefined and order is defined', () => {
      component.ecommPromoType = undefined;
      component.offerData = { info: { ecommPromoCode: undefined, order: 'OrderValue' } };
      expect(component.showEcommPromoVal).toEqual('OrderValue');
    });

    it('should return undefined if ecommPromoType, ecommPromoCode, and order are all undefined', () => {
      component.ecommPromoType = undefined;
      component.offerData = { info: { ecommPromoCode: undefined, order: undefined } };
      expect(component.showEcommPromoVal).toBeUndefined();
    });
  });

  describe('get showEcommPromoFields', () => {
    it('should return true if ecommPromoType is "EPC"', () => {
      component.offerData = { info: { ecommPromoType: 'EPC' } };
      expect(component.showEcommPromoFields).toBeTrue();
    });

    it('should return false if ecommPromoType is not "EPC"', () => {
      component.offerData = { info: { ecommPromoType: 'DA' } };
      expect(component.showEcommPromoFields).toBeFalse();
    });

    it('should return false if offerData or ecommPromoType is undefined', () => {
      component.offerData = undefined;
      expect(component.showEcommPromoFields).toBeFalse();
    });
  });

  describe('ngOnInit - ecomDesc and initialSubscriptionText', () => {
    it('should set ecomDesc if offerData.info.ecomDesc has a length greater than 0', () => {
      component.offerData = { info: { ecomDesc: 'Test Description' } };
      component.ngOnInit();
    });

    it('should not set ecomDesc if offerData.info.ecomDesc is undefined or empty', () => {
      component.offerData = { info: { ecomDesc: '' } };
      component.ngOnInit();
    });

    it('should set initialSubscriptionText to "True" if isInitialSubscriptionOfferFeatureEnabled is true and initialSubscriptionOffer is true', () => {
      spyOnProperty(component, 'isInitialSubscriptionOfferFeatureEnabled', 'get').and.returnValue(true);
      component.offerData = { info: { initialSubscriptionOffer: true } };
      component.ngOnInit();
    });

    it('should set initialSubscriptionText to "False" if isInitialSubscriptionOfferFeatureEnabled is true and initialSubscriptionOffer is false', () => {
      spyOnProperty(component, 'isInitialSubscriptionOfferFeatureEnabled', 'get').and.returnValue(true);
      component.offerData = { info: { initialSubscriptionOffer: false } };
      component.ngOnInit();
      expect(component.initialSubscriptionText).toEqual('False');
    });

    it('should not set initialSubscriptionText if isInitialSubscriptionOfferFeatureEnabled is false', () => {
      spyOnProperty(component, 'isInitialSubscriptionOfferFeatureEnabled', 'get').and.returnValue(false);
      component.offerData = { info: { initialSubscriptionOffer: true } };
      component.ngOnInit();
      expect(component.initialSubscriptionText).toEqual('');
    });
  });

  describe('ngOnInit - ecomDesc', () => {

    it('should not set ecomDesc if offerData.info.ecomDesc is undefined', () => {
      component.offerData = { info: { ecomDesc: undefined } };
      component.ngOnInit();
      expect(component.ecomDesc).toEqual('');
    });

    it('should not set ecomDesc if offerData.info.ecomDesc is an empty string', () => {
      component.offerData = { info: { ecomDesc: '' } };
      component.ngOnInit();
      expect(component.ecomDesc).toEqual('');
    });

    it('should not set ecomDesc if offerData is undefined', () => {
      component.offerData = undefined;
      component.ngOnInit();
      expect(component.ecomDesc).toEqual('');
    });
  });
});

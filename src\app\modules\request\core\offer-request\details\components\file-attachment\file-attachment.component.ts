import { <PERSON>soleLogger } from "@angular/compiler-cli";
import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { UntypedFormGroup } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { FileAttachService } from "@appServices/common/file-attach.service";
import { InitialDataService } from '@appServices/common/initial.data.service';
import { NgxConfirmBoxService } from '@appShared/ngx-confirm-box';
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
  selector: "file-attachment",
  templateUrl: "./file-attachment.component.html",
  styleUrls: ["./file-attachment.component.scss"]
})
export class FileAttachmentComponent extends UnsubscribeAdapter
  implements OnInit {
  @ViewChild("fileUploader") fileUploader: ElementRef;
  @Input() showAttachFileButton;
  @Input() isSummary = false;

  validFileTypes: string[];
  filesList: any = [];
  attachedFilesList: any = [];
  fileAttributes: any = {};
  removeFileAttributes: any = {};
  uploadError: string = "";
  fileName: string = "";
  confirmHeading = "";
  confirmContent = "Are you sure that you want to delete this attachment?";
  confirmCanceltext = "Cancel";
  confirmOkaytext = "Okay";

  constructor(
    public _requestFormService: RequestFormService,
    private confirmBox: NgxConfirmBoxService,
    private fileAttachService: FileAttachService,
    private _initialDataService: InitialDataService,
  ) {
    super();
  }

  ngOnInit() {
    console.log('FileAttachmentComponent initialized');

    this.checkAttachmentData(null);
    this.initSubscribes();
    this.validFileTypes = this._initialDataService.getAppData().supportedFileExtensionsForUpload;
  }

  initSubscribes() {
    this.subs.sink = this._requestFormService.isCallSetAttachmentData$.subscribe(
      obj => {
        this.setAttachmentData(obj);
      }
    );
  }

  
  setAttachmentData(obj) {
    const { id, respAttachments, attachments } = obj;
    this.filesList = [];
    this.attachedFilesList = [];
  
    if (attachments && attachments.length > 0) {
      for (let attachment of attachments) {
        this.fileAttributes = {};
        this.fileAttributes.name = attachment.fileName;
        this.fileAttributes.url = attachment.url;
        this.fileAttributes.id = id;
        this.fileAttributes.uploadStatus = true;
        this.attachedFilesList.push(this.fileAttributes);
      }
      if(this.fileUploader){
        this.fileUploader.nativeElement.value = "";
      }
      this.uploadError = "";
      this.fileName = "";
    } else {
      if (respAttachments && respAttachments.length > 0) {
        for (let respAttachment of respAttachments) {
          if (
            this.filesList.filter(
              uploadedFile => uploadedFile.name === respAttachment.fileName
            ).length == 0
          ) {
            this.fileAttributes = {};
            this.fileAttributes.name = respAttachment.fileName.substring(
              respAttachment.fileName.indexOf("_") + 1,
              respAttachment.fileName.length
            );
            this.fileAttributes.url = respAttachment.url;
            this.fileAttributes.id = id;
            this.fileAttributes.fileName = respAttachment.fileName.substring(
              respAttachment.fileName.indexOf("_") + 1,
              respAttachment.fileName.length
            );
            this.fileAttributes.uploadStatus = true;
            this.attachedFilesList.push(this.fileAttributes);

            if (!this._requestFormService.attachedFilesList) {
              this._requestFormService.attachedFilesList = [];
            }
            this._requestFormService.attachedFilesList.push(
              this.fileAttributes
            );
          }
        }
        if(this.fileUploader){
        this.fileUploader.nativeElement.value = "";
        }
        this.uploadError = "";
        this.fileName = "";
    
      }
      this.uploadError = "";
      this.fileName = "";
    }
  }

  checkAttachmentData(id) {
    this.attachedFilesList = [];
    if (
      this._requestFormService.attachedFilesList &&
      this._requestFormService.attachedFilesList.length > 0
    ) {
      for (let attachment of this._requestFormService.attachedFilesList) {
        this.fileAttributes = {};
        this.fileAttributes.name = attachment.name;
        this.fileAttributes.url = attachment.url;
        this.fileAttributes.id = attachment.id;
        this.fileAttributes.uploadStatus = true;
        this.attachedFilesList.push(this.fileAttributes);
      }
    }
    if (this._requestFormService.uploadedFilesList.length > 0) {
      for (let file of this._requestFormService.uploadedFilesList) {
        this.fileAttributes = {};
        this.fileAttributes.name = file.name;
        this.fileAttributes.uploadStatus = false;
        this.attachedFilesList.push(this.fileAttributes);
      }
    }
  }
  handleFileUpload(files) {
    if (!this.validateFile(files[0])) {
      return;
    }
    this.filesList.push(files[0]);
    this.fileAttributes = {};
    this.fileAttributes.name = files[0].name;
    this.fileAttributes.uploadStatus = false;
    this.attachedFilesList.push(this.fileAttributes);
    this._requestFormService.filesList.next(this.filesList);
    this._requestFormService.uploadedFilesList.push(this.fileAttributes);
    this.fileUploader.nativeElement.value = "";
    this.uploadError = "";
    this.fileName = "";
    console.log('Attached Files List after upload:', this.attachedFilesList);
    const reqForm  = this.getRequestForm() as UntypedFormGroup;
    reqForm?.markAsDirty();
  }
  getRequestForm(){
    // we need to mark form as dirty as to enable save button after submitted status based on pcode
    const pCode = this._requestFormService?.offerRequestBaseService?.getProgramCode();
    return ([CONSTANTS.GR, CONSTANTS.SPD].includes(pCode)) ? this._requestFormService?.offerRequestBaseService?.requestForm: this._requestFormService?.requestForm;
  }
  validateFile(file) {
    if (file == undefined) {
      return false;
    }
    this.uploadError = "";
    this.fileName = file.name;
    let splitFileName = file.name.split(".");
    if (this.validFileTypes.indexOf(splitFileName[splitFileName.length - 1]) == -1) {
      this.uploadError =
        "Your file must be one of the following types: " +
        this.validFileTypes.map(type => "." + type).join(", ");
      return false;
    }
    if (file.size > 10000000) {
      this.uploadError = "Your file may only be a maximum of 10 MB";
      return false;
    }
    if (this.attachedFilesList.length > 0) {
      for (let attachedFile of this.attachedFilesList) {
        if (attachedFile.name === file.name) {
          this.uploadError = "You have already uploaded a file with this name";
          return false;
        }
      }
    }
    return true;
  }

  removeFile(i, uploadStatus) {
    this.removeFileAttributes = {};
    this.removeFileAttributes.index = i;
    this.removeFileAttributes.uploadStatus = uploadStatus;
    this.confirmBox.show();
  }

  confirmRemoveFile(showConfirm: boolean) {
    if (showConfirm) {
      if (this.removeFileAttributes.uploadStatus) {
        this.fileAttachService
          .removeFile(
            this.attachedFilesList[this.removeFileAttributes.index].id,
            this.attachedFilesList[this.removeFileAttributes.index].name
          )
          .subscribe(data => {

          });
        this.attachedFilesList.splice(this.removeFileAttributes.index, 1);
        this._requestFormService.attachedFilesList.splice(
          this.removeFileAttributes.index,
          1
        );
      } else {
        if (this._requestFormService.filesList.getValue().length > 0) {
          this._requestFormService.filesList
            .getValue()
            .forEach((item, index) => {
              if (
                item.name ===
                this.attachedFilesList[this.removeFileAttributes.index].name
              ) {
                this._requestFormService.filesList.getValue().splice(index, 1);
                this._requestFormService.uploadedFilesList.splice(index, 1);
              }
            });
        }
        this.attachedFilesList.splice(this.removeFileAttributes.index, 1);
      }
    }
    this.removeFileAttributes = {};
  }

  downloadFile(i) {
    this.fileAttachService.downloadFile(
      this.attachedFilesList[i].name,
      this.attachedFilesList[i].url
    );
  }
}

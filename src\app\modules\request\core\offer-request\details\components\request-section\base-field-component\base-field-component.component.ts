import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { OFFER_REQUEST_CREATE_RULES } from '@appModules/request/shared/rules/create.rules';
import { TEMPLATE_CREATE_RULES } from '@appModules/templates/core/offer-template/details/shared/rules/rules';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-base-field-component',
  templateUrl: './base-field-component.component.html'
})
export class BaseFieldComponentComponent extends UnsubscribeAdapter implements OnInit,OnDestroy{

  public label;
  public tooltipTitle;
  public maxLength;
  public name;
  public inputPattern;
  public edit;
  public router: Router;
  public onlyNumber;
  public allowDecimals;
  public offerRequestBaseService$: OfferRequestBaseService;
  public offerTemplateBaseService$: OfferTemplateBaseService;
  public commonRouteService: CommonRouteService;

  @Input() property;
  @Input() placeholder;
  @Input() section;
  @Input() form;
  @Input() summary;
  @Input() caseSensitiveSearch:boolean = true;
  fieldProperty;
  formGroupName: any;
  validate: any;
  impactFieldForOfferUpdate: any;
  control: any;
  value: any;
  required: any;
  error: any;
  tooltip: any;
  appData: any;
  options: any;
  isOnlyDisplayField:any;
  showLabelAsValue:any;
  tristateCheck:any;
  propertyValues: any;
  formControlMaxLength: any;
  checked: any;
  formControlMinValue:any;
  formControlMaxValue:any;
  
  constructor() {
    super()
    const injector = AppInjector.getInjector();
    this.router = injector.get(Router);
    this.commonRouteService = injector.get(CommonRouteService);
    this.offerRequestBaseService$ = injector.get(OfferRequestBaseService);
    this.offerTemplateBaseService$ = injector.get(OfferTemplateBaseService);
    const isTemplateRouteActive = this.commonRouteService.currentActivatedRoute?.includes("template");
    const programCode = this.offerRequestBaseService$?.facetItemService$?.programCodeSelected,
    pCode = isTemplateRouteActive ? CONSTANTS.BPD : programCode===CONSTANTS.SC?'':programCode;
    const rule = isTemplateRouteActive ? TEMPLATE_CREATE_RULES : OFFER_REQUEST_CREATE_RULES;
    this.fieldProperty = (pCode && rule && rule[pCode] && rule[pCode][this.section]) ? rule[pCode][this.section] : undefined;
    this.appData = this.offerRequestBaseService$.initialDataService$.getAppData();
    if (this.router && this.router.events) {
      this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
        if (event.url) {
          this.edit =  true;
        }
      });
      const url = this.router.url;
      if(url.includes("create")){
        this.edit =  true;
      }
    }
    
   }
   
  
   setComponentProperties(){    
     const isTemplateRouteActive = this.commonRouteService.currentActivatedRoute?.includes("template");
     if(!this.form){
      this.form = isTemplateRouteActive ? this.offerTemplateBaseService$.templateForm : this.offerRequestBaseService$.requestForm;
     }
     const programCode = this.offerRequestBaseService$?.facetItemService$?.programCodeSelected,
     pCode = isTemplateRouteActive ? CONSTANTS.BPD : programCode===CONSTANTS.SC?'':programCode;
     const rule = isTemplateRouteActive ? TEMPLATE_CREATE_RULES : OFFER_REQUEST_CREATE_RULES;
    this.fieldProperty = (rule && pCode && rule[pCode] && rule[pCode][this.section]) ? rule[pCode][this.section] : undefined;
   
    if(this.fieldProperty && this.property){
    this.propertyValues = this.fieldProperty[this.property];
    if(this.propertyValues){
      const {allowDecimals,onlyNumber,maxLength = 0,tooltip = false,tooltipTitle = "",label,validate,impactFieldForOfferUpdate,control,value,edit,required,error, onlyDisplay,checked,showLabelAsValue,tristateCheck,min,max} = this.propertyValues;
      this.label = label;
      this.onlyNumber = onlyNumber;
      this.allowDecimals = allowDecimals;
      this.validate = validate;
      this.impactFieldForOfferUpdate = impactFieldForOfferUpdate;
      this.control = control;
      this.value = value;
      this.edit = edit;
      this.isOnlyDisplayField = onlyDisplay;
      this.required = required;
      this.error = error;
      this.tooltip = tooltip;
      this.checked = checked
      this.tooltipTitle = tooltipTitle;
      this.appData = this.offerRequestBaseService$.initialDataService$.getAppData();
      this.formControlMaxLength = maxLength;
      this.showLabelAsValue = showLabelAsValue || false;
      this.tristateCheck = tristateCheck || false
      this.formControlMinValue = min;
      this.formControlMaxValue = max;
    }
   
   }
   }

  ngOnInit(): void {
    this.initSetup();
  }

  initSetup(){
    this.setComponentProperties();
    this.getFormControl();
  }
  
  get serviceBasedOnRoute() {
    const isTemplateRouteActive = this.commonRouteService.currentActivatedRoute?.includes("template");
    return isTemplateRouteActive ? this.offerTemplateBaseService$ : this.offerRequestBaseService$;
  }
  get formControl(){
    if(!this.propertyValues){
      this.setComponentProperties();
    }
   return this.getFormControl();
  }
  getFormControl(){
    let formControl;
    if(this.property){
      let controls = this.control,form = this.form;
      if(form){
        if(controls && controls.length){
          controls.forEach((element,index) => {
             if(form){
              if(index ===controls.length-1 ){
                formControl = form.get(element);
              }else{
                form = form.get(element);
              }
             }
             
            });
        }else  if(form){
          formControl = form.get(this.property)
        }
      }
      if(form){
        this.formGroupName = form;
      }
     
    }
    
    return formControl;
  }
  get getFieldErrors(){
   return this.formControl && this.offerRequestBaseService$.getFieldErrors(this.formControl);
  }
  createDefaultFormControl(){
    
  }
  getValue(){
    return this.formControl?.value;
  }
  ngOnDestroy(): void {
    if(this.formControl instanceof UntypedFormControl) {
      this.formControl?.setValue(null);
    }
  }   
}

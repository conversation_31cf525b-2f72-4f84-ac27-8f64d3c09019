<section *ngIf="!isSummary" [formGroup]="_requestFormService.requestForm">
  <div *ngIf="isReqGroupAvailable()">
    <div class="offer-request-container mb-5">
      <div formGroupName="offerReqGroup">
        <section>
          <nav class="navbar background-header mb-6" aria-label="Offer Request">
            <span id="offer-request-label"> Offer Request </span>
          </nav>
          <div class="fields-container">
            <!-- OFFER REQUEST -->
            <!-- PROGRAM & CHANNELS  -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-6">
                <div class="row" *ngIf="!_requestFormService.isMinStateProcessing()">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="department">Program Code</label>
                  </div>
                  <div class="col-12" id="program-cd-label">
                    {{programs && programs[programCodeValue]}}
                    <ng-container *ngVar="getFieldErrors('programCode') as programCodeErr">
                      <div class="text-danger" *ngIf="programCodeErr">
                        <small *ngIf="programCodeErr.required">Program Code is required</small>
                        <small *ngIf="programCodeErr.apiError" apiErrorCtrl="programCode">{{ programCodeErr.apiError }}</small>
                      </div>
                    </ng-container>
                  </div>
                </div>
                <div class="row" *ngIf="_requestFormService.isMinStateProcessing()">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="department">Program Code</label>
                  </div>
                  <div class="col-12" id="program-cd-label">
                    {{programCodeDisplay}}
                    <small class="text-danger" *ngIf="getFieldErrors('programCode')">Program Code is required</small>
                  </div>
                </div>
              </div>
              <div class="col-6">
                <div class="row" *ngIf="!_requestFormService.isMinStateSubmit()">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="channels">Coupon Channel </label>
                  </div>
                  <div class="col-12">
                    <select
                      class="custom-select form-control"
                      [disabled]="true"
                      *ngIf="_requestFormService.requestStatus && _requestFormService.requestStatus != 'I'"
                      (change)="setCurrentChannel($event.target.value)"
                      id="coupon-channel"
                    >
                      <option>{{ channels[_requestFormService.selectedDeliveryChannel] }}</option>
                    </select>
                    <select
                      class="custom-select form-control"
                      [class.border-danger]="getFieldErrors('deliveryChannel')"
                      id="channels"
                      name="channels"
                      *ngIf="!_requestFormService.requestStatus || _requestFormService.requestStatus == 'I'"
                      [disabled]='(_requestFormService.requestStatus!=="I")'
                      (change)="setCurrentChannel($event.target.value)"
                      formControlName="deliveryChannel"
                    >
                      <option *ngFor="let k of channels | keyobject" [ngValue]="k" [class.hide]="isHideChannelOption(k)">{{ channels[k] }}</option>
                    </select>

                    <ng-container *ngVar="getFieldErrors('deliveryChannel') as deliveryChannelErr">
                      <div class="text-danger" *ngIf="deliveryChannelErr">
                        <small *ngIf="deliveryChannelErr.required">Channel is required</small>
                        <small *ngIf="deliveryChannelErr.apiError" apiErrorCtrl="deliveryChannel">{{ deliveryChannelErr.apiError }}</small>
                      </div>
                    </ng-container>
                  </div>               

                </div>
                <div class="row" *ngIf="_requestFormService.isMinStateSubmit()">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="channels">Coupon Channel </label>
                  </div>
                  <div class="col-12" id="channel-display">{{deliveryChannelDisplay}}</div>
                </div>
               
               <ng-container 
               *ngIf="isDisplayAdvOptionBasedOnFeatureFlag && showAdvancedOption then advancedOptionsLink">                  
               </ng-container>

                <ng-template #advancedOptionsLink>
                  <span
                      class="span-batch-selection"
                      class="pl-xl-0 pl-md-3 pl-sm-0 pr-4 pt-2 pb-2 d-inline-block"
                      [popover]="fulfillMentChannelTmpl"
                      triggers="click"
                      [outsideClick]="true"
                      #channelPopup="bs-popover"
                      placement="bottom"
                      containerClass="channelContainer"
                      id="advanced-option-link"
                    >
                      <a href="javascript:void(0)" class="anchor-link-blue input-link-text pr-5">Advanced Options</a>
                    </span>
                </ng-template>

                <div class="row mt-3"  *ngIf="isEcommOnlyChannelSelected">
                  <div class="col-12">
                    <label class=" font-weight-bold" for="ecommPromoType">eComm Promo Type</label>                   
                  </div>

                  <div class="col-12">
                    <select
                    class="custom-select form-control"
                    [class.border-danger]="getFieldErrors('ecommPromoType')"
                    id="ecommPromoType"
                    name="ecommPromoType"                   
                    (change)="promoTypeChanged($event.target.value)"
                    formControlName="ecommPromoType"
                  >
                    <option *ngFor="let k of ecommPromoCodeTypeChannel | keyobject" [ngValue]="k" [class.hide]="isHideEcommPromoCodeType(k)">{{ ecommPromoCodeTypeChannel[k] }}</option>
                  </select>

                  <ng-container *ngVar="getFieldErrors('ecommPromoType') as errors">                                   
                    <small class="text-danger" *ngIf="errors?.required">Promo type is a required field</small>                            
                  </ng-container>

                  </div>
                </div>
              </div>
            </div>
            <!-- DEPARTMENT & SEGMENT -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-6">
                <div class="row">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="department">Department </label>
                  </div>
                  <div class="col-12">
                    <select
                      class="custom-select form-control"
                      [class.border-danger]="getFieldErrors('department')"
                      id="department"
                      name="department"
                      formControlName="department"
                    >
                      <option *ngFor="let department of departmentsConfig" [value]="department">{{ department }}</option>
                    </select>
                    <ng-container *ngVar="getFieldErrors('department') as departmentsErr">
                      <div class="text-danger" *ngIf="departmentsErr">
                        <small *ngIf="departmentsErr.required">Department is required</small>
                        <small *ngIf="departmentsErr.apiError" apiErrorCtrl="department">{{ departmentsErr.apiError }}</small>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </div>
              <div class="col-6">
                <label class="font-weight-bold" for="group">Group </label>
                <tooltip-container [title]="'The division responsible for the creation of the offer'"> </tooltip-container>
                <select
                  class="custom-select form-control"
                  [class.border-danger]="getFieldErrors('group')"
                  name="group"
                  id="group"
                  formControlName="group"
                  (change)="selectedGroup()"
                >
                  <option *ngFor="let k of group | keyobject" [ngValue]="k">{{ group[k].name }}</option>
                </select>
                <ng-container *ngVar="getFieldErrors('group') as groupErr">
                  <div class="text-danger" *ngIf="groupErr">
                    <small *ngIf="groupErr.required">Group is required</small>
                    <small *ngIf="groupErr.apiError" apiErrorCtrl="group">{{ groupErr.apiError }}</small>
                  </div>
                </ng-container>
              </div>
            </div>
            <div class="row mx-1 justify-content-left my-3" *ngIf="groupDivision">
              <div class="col-6"></div>
              <div class="col-6">
                <label class="d-block font-weight-bold" for="groupDivision">Division </label>
                <select
                  class="custom-select form-control"
                  [class.border-danger]="getFieldErrors('groupDivision')"
                  name="groupDivision"
                  id="groupDivision"
                  formControlName="groupDivision"
                >
                  <option *ngFor="let k of groupDivision | keyobject" [ngValue]="k">{{ groupDivision[k].name }}</option>
                </select>
                <ng-container *ngVar="getFieldErrors('groupDivision') as groupDivisionErr">
                  <div class="text-danger" *ngIf="groupDivisionErr">
                    <small *ngIf="groupDivisionErr.required">Division is required</small>
                    <small *ngIf="groupDivisionErr.apiError" apiErrorCtrl="groupDivision">{{ groupDivisionErr.apiError }}</small>
                  </div>
                </ng-container>
              </div>
            </div>
            <!-- DIVISION & BRAND-SIZE -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-6">
                <label class="font-weight-bold" for="brandSize">Brand & Size </label>
                <tooltip-container [title]="'A few words to describe the products participating in the promotion (character limit = 20)'">
                </tooltip-container>
                <input
                  class="form-control"
                  [class.border-danger]="getFieldErrors('brandAndSize')"
                  type="text"
                  id="brandSize"
                  name="brandSize"
                  autocomplete="off"
                  maxlength="20"
                  formControlName="brandAndSize"
                />
                <ng-container *ngVar="getFieldErrors('brandAndSize') as brandAndSizeErr">
                  <div class="text-danger" *ngIf="brandAndSizeErr">
                    <small *ngIf="brandAndSizeErr.required">Brand & Size is required</small>
                    <small *ngIf="brandAndSizeErr.apiError" apiErrorCtrl="brandAndSize">{{ brandAndSizeErr.apiError }}</small>
                  </div>
                </ng-container>
              </div>
              <div class="col-6">
                <label class="d-block font-weight-bold" for="segment">Segment </label>

                <select
                  class="custom-select form-control"
                  [class.border-danger]="getFieldErrors('customerSegment')"
                  id="segment"
                  name="segment"
                  formControlName="customerSegment"
                >
                  <option *ngFor="let segment of segments" [value]="segment">{{ segment }}</option>
                </select>
                <ng-container *ngVar="getFieldErrors('customerSegment') as customerSegmentErr">
                  <div class="text-danger" *ngIf="customerSegmentErr">
                    <small *ngIf="customerSegmentErr.required">Segment is required</small>
                    <small *ngIf="customerSegmentErr.apiError" apiErrorCtrl="customerSegment">{{ customerSegmentErr.apiError }}</small>
                  </div>
                </ng-container>
              </div>
            </div>
            <!-- START DATE & END DATE -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-6">
                <label class="font-weight-bold" for="startDate">Start Date</label>
                <tooltip-container [title]="'The first active day of the offer'"></tooltip-container>
                <div
                  class="input-group"
                  [class.border-danger]="isFieldInvalidPostSave('offerEffectiveStartDate') || getFieldErrors('offerEffectiveStartDate')"
                >
                  <input
                    onkeydown="return false"
                    type="text"
                    id="startDate"
                    style="padding-left: 12px; border-right: 1px solid white !important"
                    class="form-control form-control-lg optional"
                    [class.is-populated]="getFieldValue('offerEffectiveStartDate') !== ''"
                    name="startDate"
                    autocomplete="off"
                    [bsConfig]="{ containerClass: colorTheme, dateInputFormat: 'MM/DD/YYYY',showWeekNumbers: false }"
                    bsDatepicker
                    [minDate]="minOfferStartDate"
                    (ngModelChange)="setMinOfferEndDate($event); currentOfferStartDate = $event;startDateChange($event) "
                    formControlName="offerEffectiveStartDate"
                    markAsTouchedOnFocus
                    [formCtrl]="offerReqGroup && offerReqGroup.get('offerEffectiveStartDate')"
                    #startDatePicker="bsDatepicker"
                  />

                  <div class="cursor-pointer input-group-append input-icon pt-4em px-2">
                    <div (click)="startDatePicker.toggle()">
                      <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
                    </div>
                  </div>
                </div>

                <ng-container *ngVar="getFieldErrorsPostSave('offerEffectiveStartDate') as err">
                  <div class="text-danger" *ngIf="err">
                    <small *ngIf="err.reqdStartDt">Start date required</small>
                  </div>
                </ng-container>
                <ng-container *ngVar="getFieldErrors('offerEffectiveStartDate') as errors">
                  <small *ngIf="errors && errors.apiError" apiErrorCtrl="offerEffectiveStartDate" class="text-danger"
                    >{{errors.apiError}}</small
                  >
                </ng-container>
              </div>

              <div class="col-6">
                <label class="font-weight-bold" for="endDate">End Date</label>
                <tooltip-container [title]="'The last day that the offer will be active. Offer ends at 11:59:59pm on the end date'">
                </tooltip-container>
                <div
                  class="input-group"
                  [class.border-danger]="isFieldInvalidPostSave('offerEffectiveEndDate') || getFieldErrors('offerEffectiveEndDate')"
                >
                  <input
                    onkeydown="return false"
                    type="text"
                    id="endDate"
                    style="padding-left: 12px; border-right: 1px solid white !important"
                    class="form-control form-control-lg optional"
                    [class.is-populated]="getFieldValue('offerEffectiveEndDate') !== ''"
                    [bsConfig]="{ containerClass: colorTheme, dateInputFormat: 'MM/DD/YYYY',showWeekNumbers: false }"
                    name="endDate"
                    autocomplete="off"
                    bsDatepicker
                    (ngModelChange)="currentOfferEndDate = $event;"
                    [minDate]="minOfferEndDate"
                    markAsTouchedOnFocus
                    [formCtrl]="offerReqGroup && offerReqGroup.get('offerEffectiveEndDate')"
                    formControlName="offerEffectiveEndDate"
                    #endDatePicker="bsDatepicker"
                  />
                  <div class="cursor-pointer input-group-append input-icon pt-4em px-2">
                    <div (click)="endDatePicker.toggle()">
                      <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
                    </div>
                  </div>
                </div>

                <ng-container *ngVar="getFieldErrorsPostSave('offerEffectiveEndDate') as err">
                  <div class="text-danger" *ngIf="err">
                    <small *ngIf="err.reqdEndDt">End date required</small>
                    <small *ngIf="err.endDtLessThanStartDt">End Date must be later than start date</small>
                  </div>
                </ng-container>
                <ng-container *ngVar="getFieldErrors('offerEffectiveEndDate') as errors">
                  <small *ngIf="errors && errors.apiError" apiErrorCtrl="offerEffectiveEndDate" class="text-danger"
                    >{{errors.apiError}}</small
                  >
                </ng-container>
              </div>
            </div>

            <!-- Initial Subscription -->
            <ng-container *ngIf="canShowInitialSubscriptionCheckBox">
            <div class="row mx-1 justify-content-left my-3" *ngIf="showInitialSubscriptionCheckBox">
              <div class="col-6">
               
                    <div class="input-group">
                      <input type="checkbox" name="initialSubscriptionOffer" formControlName="initialSubscriptionOffer" 
                       class="autodelete-checkbox pb-2 mb-0 mr-2" />
                      <label class="mt-1" for="initialSubscriptionOffer">Initial Subscription </label>
                    </div>                  
              </div>
            </div>
            </ng-container>

            <!-- OFFER LIMITS & PLU -->
            <div class="row mx-1 pb-3 justify-content-left my-3">
              <div class="col-6">
                <label class="font-weight-bold" for="offerLimitsCtrl">Offer Limits</label>
                <tooltip-container
                  [title]="'Once Per Offer: This discount is redeemable in one transaction for the duration of the offer \n\nUnlimited: This discount can be redeemed for unlimited transactions \n\nOnce Per Transaction: This discount is redeemable Once Per Transaction  (For digital this equates to One-Time-Use)'"
                >
                </tooltip-container>
                <select
                  class="custom-select form-control"
                  [class.border-danger]="getFieldErrorsPostSave('usageLimitTypePerUser') || getFieldErrors('usageLimitTypePerUser')"
                  id="offerLimitsCtrl"
                  name="offerLimitsCtrl"
                  formControlName="usageLimitTypePerUser"
                  (change)="setCurrentOfferLimit($event.target.value)"
                >
                  <option selected hidden></option>
                  <option *ngFor="let k of offerLimits | keyobject" [ngValue]="k">{{ offerLimits[k] }}</option>
                </select>

                <ng-container *ngIf="getFieldErrorsPostSave('usageLimitTypePerUser') as uiErrors">
                  <div class="text-danger" *ngIf="uiErrors">
                    <small *ngIf="uiErrors.required">Offer Limits is required</small>
                    <small *ngIf="uiErrors.invalidUsageLimit">Offer Limits must be Unlimited for Rewards – Accumulation offers.</small>
                  </div>
                </ng-container>
                <ng-container *ngVar="getFieldErrors('usageLimitTypePerUser') as errors">
                  <small *ngIf="errors && errors.apiError" apiErrorCtrl="usageLimitTypePerUser" class="text-danger"
                    >{{errors.apiError}}</small
                  >
                </ng-container>
              </div>
              <div class="col-6">

                <ng-container *ngIf="isDisplayPromoCodeField">                 
                    <label class=" font-weight-bold" for="ecommPromoCode">eComm Promo Code</label>
                    <input
                    class="form-control"                  
                    [class.border-danger]="getFieldErrorsPostSave('ecommPromoCode')"
                    type="text"
                    id="ecommPromoCode"
                    name="ecommPromoCode"
                    autocomplete="off"
                    maxlength="20"
                    appInputPattern="onlyAlphaNumericWithSpace"
                    formControlName="ecommPromoCode"
                    (blur)="setValidPromoCodeString($event)"
                  />
                 
                  <ng-container *ngVar="getFieldErrorsPostSave('ecommPromoCode') as errors">                                   
                    <small class="text-danger" *ngIf="errors?.required">Promo code is a required field</small>                            
                  </ng-container>
                  <ng-container *ngVar="getFieldErrors('ecommPromoCode') as errors">
                    <small *ngIf="errors?.apiError" apiErrorCtrl="ecommPromoCode" class="text-danger"
                      >{{errors.apiError}}</small>
                  </ng-container>
                  <div class="input-group mt-2" *ngIf="isDisplayApplyPromoCodeFeatureFlag">
                    <input type="checkbox" name="isAutoApplyPromoCode" formControlName="isAutoApplyPromoCode"
                      class="autodelete-checkbox pb-2 mb-0 mr-2" />
                    <label class="mt-1" for="isAutoApplyPromoCode">Auto Apply Promo Code </label>
                  </div>
                </ng-container> 

                

                <div class="row" *ngIf="isDisplayAdField && !_requestFormService.isMinStateProcessing()">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="inAd">In Ad</label>
                  </div>
                  <div class="col-12">
                    <select
                      class="custom-select form-control"
                      [class.border-danger]="getFieldErrors('adType')"
                      id="inAd"
                      name="inAd"
                      formControlName="adType"
                      (change)="adTypeChange($event)"
                    >
                      <option selected value="IA">In Ad</option>
                      <option value="NIA">Not In Ad</option>
                    </select>
                    <ng-container *ngVar="getFieldErrors('adType') as adTypeErr">
                      <div class="text-danger" *ngIf="adTypeErr">
                        <small *ngIf="adTypeErr.required">This is a required field</small>
                        <small *ngIf="adTypeErr.apiError" apiErrorCtrl="adType">{{ adTypeErr.apiError }}</small>
                      </div>
                    </ng-container>
                  </div>
                </div>
                <div class="row" *ngIf="isDisplayAdField && _requestFormService.isMinStateProcessing()">
                  <div class="col-12">
                    <label class="d-block font-weight-bold" for="inAd">In Ad</label>
                  </div>
                  <div *ngIf="inAdDisplay == 'IA'" class="col-12">In Ad</div>
                  <div *ngIf="inAdDisplay == 'NIA'" class="col-12">Not In Ad</div>
                </div>
                <div class="row" *ngIf="isDisplayPluField">
                  <div class="col-12">
                    <label class="font-weight-bold" for="pluTriggerBarcode">PLU</label>

                    <label
                      id="reserve-plu"
                      class="anchor-link-blue cursor-pointer reserve-plu-link"
                      (click)="reserveInlinePluFunc()"
                    >
                      <span><u>Reserve PLU</u></span>
                    </label>
                  </div>

                  <div class="col-12">
                    <input
                      type="number"
                      class="form-control no-arrow"
                      [ngClass]="{'border-red': _requestFormService.showPLUWraningTmpl}"
                      [class.border-danger]="getFieldErrors('pluTriggerBarcode')"
                      id="pluTriggerBarcode"
                      name="pluTriggerBarcode"
                      formControlName="pluTriggerBarcode"
                      markAsTouchedOnFocus
                      [formCtrl]="offerReqGroup && offerReqGroup.get('pluTriggerBarcode')"
                    />

                    <ng-container *ngVar="getFieldErrors('pluTriggerBarcode') as pluTriggerBarcodeErr">
                      <div class="text-danger" *ngIf="pluTriggerBarcodeErr">
                        <span *ngIf="pluTriggerBarcodeErr.isRequiredError">PLU is required</span>
                        <span *ngIf="pluTriggerBarcodeErr.invalidPlu">Invalid PLU</span>
                        <span *ngIf="pluTriggerBarcodeErr.apiError" apiErrorCtrl="pluTriggerBarcode"
                          >{{pluTriggerBarcodeErr.apiError}}</span
                        >
                      </div>
                    </ng-container>
                    <div class="pluconfirm" *ngIf="_requestFormService.showPLUWraningTmpl">
                      <p>
                        {{_requestFormService.warningMsg}}
                        <span class="anchor-link-blue cursor-pointer text-underline" (click)="PLUConfirmation()"> Yes </span> /
                        <span class="anchor-link-blue cursor-pointer text-underline" (click)="closePLUWarning()"> No </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <ng-container *ngIf="showEcommPromoFields">
              <div class="row mx-1 justify-content-left my-3">
                <div class="col-6">
                  <label class="d-block font-weight-bold" for="notCombinableWith">Not Combinable With </label>
                  <input
                    class="form-control"                  
                    type="text"
                    [class.border-danger]="getFieldErrors('notCombinableWith')"
                    id="notCombinableWith"
                    name="notCombinableWith"
                    markAsTouchedOnFocus
                    appInputPattern="onlyAlphaNumericWithSpaceAndComma"                    
                    formControlName="notCombinableWith"
                    (blur)="setValidPromoCodeString($event)"
                />
                  <ng-container *ngVar="getFieldErrors('notCombinableWith') as notCombineWithErr">
                    <div class="text-danger" *ngIf="notCombineWithErr">
                      <small *ngIf="notCombineWithErr.notCombineWithUniqueError">Must not be same as or contain Ecomm Promo Code value</small>
                    </div>
                  </ng-container> 
                </div>
                <div class="col-6">
                  <label class="d-block font-weight-bold" for="orderCount">Order Count </label>
                  <input
                    class="form-control"                  
                    type="text"
                    id="orderCount"
                    name="orderCount"
                    appInputPattern="commaSepreatedNumber"
                    (keydown.space)="$event.preventDefault()"
                    formControlName="orderCount"
                />
                </div>
              </div>
              <div class="row mx-1 justify-content-left my-3">
                <div class="col-6"> </div>
                <div class="col-6 d-flex">
                  <div class="col-5 p-0">
                    <input type="checkbox" name="validWithOtherOffer" formControlName="validWithOtherOffer"
                    class="autodelete-checkbox pb-2 mb-0 mr-2" />
                    <label class="mt-1" for="validWithOtherOffer">Valid With Other Offer </label>
                  </div>
                  <div>
                    <input type="checkbox" name="firstTimeCustomerOnly" formControlName="firstTimeCustomerOnly"
                    class="autodelete-checkbox pb-2 mb-0 mr-2" />
                    <label class="mt-1" for="firstTimeCustomerOnly">First Time Customer Only </label>
                  </div>
                </div>
              </div>
            </ng-container>
            <!--CPG-->
            <div class="row mx-1 justify-content-left my-3"  *ngIf="isEcommOnlyAndPromoCodeSelected">
              <div class="col-6">
                <label class="d-block font-weight-bold" for="cpg">CPG</label>                   
                <input
                  class="form-control"
                  type="text"
                  id="cpg"
                  name="cpg"
                  autocomplete="off"
                  formControlName="cpg" />
              </div>
              <div class="col-6">
                
              </div>
            </div>
            <div class="row mx-1 justify-content-left my-3">
              <ng-container *ngIf="showBehavioralAction">
                <div class="col-3">
                  <label class="d-block font-weight-bold" for="behavioralAction"> Behavioral Action</label>
                  <ng-container *ngIf="currentChannel === 'BA'">
                    <select class="custom-select form-control" [class.border-danger]="getFieldErrors('behavioralAction')"
                      name="behavioralAction" id="behavioralAction" formControlName="behavioralAction">
                        <option *ngFor="let action of behavioralActions" [ngValue]="action">{{ action }}</option>
                    </select>
                  </ng-container>
                  <ng-container *ngIf="currentChannel === 'BAC'">
                    <select class="custom-select form-control" [class.border-danger]="getFieldErrors('behavioralAction')"
                      name="behavioralAction" id="behavioralAction" formControlName="behavioralAction">
                        <option *ngFor="let action of behavioralContinuityActions" [ngValue]="action">{{ action }}</option>
                    </select>
                  </ng-container>
                  <ng-container *ngVar="getFieldErrors('behavioralAction') as bhError">
                    <div class="text-danger" *ngIf="bhError">
                      <small *ngIf="bhError.required">Behavioral Action is required</small>
                    </div>
                  </ng-container> 
                </div> 
              </ng-container>
              <ng-container *ngIf="isBehavioralContinuityEnabledAndSelectedBAC"  formGroupName="behavioralCondition">
                <div  class="col-3">
                  <label class="font-weight-bold" for="brandSize">No Of Transactions</label>
                  <input
                    class="form-control"
                    type="text"
                    id="noOfTransactions"
                    name="noOfTransactions"
                    autocomplete="off"
                    maxlength="20"
                    formControlName="noOfTransactions"
                  />
                  <ng-container *ngVar="getFieldErrors('behavioralCondition.noOfTransactions') as bhError">
                    <div class="text-danger" *ngIf="bhError">
                      <small *ngIf="bhError.required">No Of Transactions is required</small>
                    </div>
                  </ng-container> 
                </div>

                <div class="col-3">
                  <label class="font-weight-bold" for="brandSize">Minimum Spend</label>
                  <input
                    class="form-control"
                    type="text"
                    id="minimumSpend"
                    name="minimumSpend"
                    autocomplete="off"
                    maxlength="20"
                    formControlName="minimumSpend"
                  />
                </div>

                <div class="col-3">
                    <label class="d-block font-weight-bold" for="minimumSpendUom"> UOM</label>
                    <select
                      class="custom-select form-control"
                      name="minimumSpendUom"
                      id="minimumSpendUom"
                      formControlName="minimumSpendUom"
                      >
                        <option *ngFor="let oum of uomConfigs | keyobject" [ngValue]="oum">
                          {{ uomConfigs[oum] }}
                        </option>
                    </select>
                </div>
              </ng-container>
            </div>
            
            <h6>ooooooooo</h6>
            <file-attachment [showAttachFileButton]="true"></file-attachment>
          </div>
        </section>
      </div>
    </div>
  </div>
</section>

<ng-template #reserveInlinePlu>
  <div class="col pt-3">
    <api-errors></api-errors>
  </div>
  <div class="col-12 p-0">
    <spinner></spinner>
  </div>
  <div class="p-3 position-relative">
    <img
      src="assets/icons/grey-close-icon.svg"
      class="mb-1 position-absolute close-icon cursor-pointer"
      alt="close"
      (click)="modalRef.hide()"
    />
  </div>
  <div class="container-fluid px-8 pt-2">
    <div class="row m-1">
      <span class="plu-page-title">PLU Reservation</span>
    </div>
  </div>
  <div class="modal-height px-7 pb-7 pt-0">
    <app-plu-add-edit-section
      [inlinePLuReservation]="true"
      [modalRefParent]="modalRef"
      (pluReservationOutput)="getPluReservationResponse($event)"
    ></app-plu-add-edit-section>
  </div>
</ng-template>

<section *ngIf="isSummary" [formGroup]="_requestFormService.requestForm">
  <div *ngIf="isReqGroupAvailable()">
    <div class="offer-request-container mb-5">
      <div formGroupName="offerReqGroup">
        <section>
          <nav class="navbar background-header mb-6" aria-label="Offer Request">
            <span> Offer Request </span>
          </nav>
          <div class="fields-container">
            <!-- OFFER REQUEST -->
            <!-- PROGRAM & CHANNELS  -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-5">
                <div class="row">
                  <div class="col-12">
                    <label class="d-block font-weight-bold m-0" for="department">Program Code</label>
                  </div>
                  <div class="col-12" id="programCodeDisplay">
                    {{programCodeDisplay}}
                    <small class="text-danger" *ngIf="getFieldErrors('programCode')">Program Code is required</small>
                  </div>
                </div>
              </div>
              <div class="col-5 offset-2">
                <div class="row">
                  <div class="col-12">
                    <label class="d-block font-weight-bold m-0" for="channels">Coupon Channel </label>
                  </div>
                  <div class="col-12" id="deliveryChannelDisplay">{{deliveryChannelDisplay}}</div>
                </div>
              </div>
            </div>
           
            <ng-container *ngIf="isDisplayAdvOptionBasedOnFeatureFlag && showAdvancedOption">              
              <div class="row mx-1 justify-content-left my-3">
                <div class="col-5">
                  <div class="row" >
                    <div class="col-12">
                      <label class="d-block font-weight-bold m-0" for="department">Shopping Channel</label>
                    </div>
                    <div class="col-12" id="fulfillmentChannelDisplay">{{fulfillmentChannelDisplay}}</div>
                  </div>
                </div>
                <div class="col-5 offset-2">
                  <ng-container *ngIf="isEcomFlagEnabled">
                    <div *ngIf="isEcommOnlyChannelSelected" class="row">
                      <div class="col-12">
                        <label class="d-block font-weight-bold m-0">eComm Promo Type</label>
                      </div>
                      <div class="col-12" id="ecommPromoTypeDisplay">{{ecommPromoTypeDisplay}}</div>
                    </div>  
                  </ng-container>
                </div>
              </div>
            </ng-container>

            <!-- DEPARTMENT & SEGMENT -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-5">
                <div class="row">
                  <div class="col-12">
                    <label class="d-block font-weight-bold m-0" for="department">Department </label>
                  </div>
                  <div class="col-12" id="departmentsDisplay">{{departmentsDisplay}}</div>
                </div>
              </div>
              <div class="col-5 offset-2">
                <label class="d-block font-weight-bold m-0" id="groupDisplay" for="group">Group </label>

                {{groupDisplay}}
              </div>
            </div>

            <div class="row mx-1 justify-content-left my-3" *ngIf="groupDivision">
              <div class="col-5"></div>
              <div class="col-5 offset-2">
                <label class="d-block font-weight-bold m-0" id="groupDivisionDisplay" for="division">Division </label>

                {{groupDivisionDisplay}}
              </div>
            </div>

            <!-- DIVISION & BRAND-SIZE -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-5">
                <label class="d-block font-weight-bold m-0" id="brandAndSizeDisplay" for="brandSize">Brand & Size </label>
                {{brandAndSizeDisplay}}
              </div>
              <div class="col-5 offset-2">
                <label class="d-block font-weight-bold m-0" id="customerSegmentDisplay" for="department">Segment </label>

                {{customerSegmentDisplay}}
              </div>
            </div>
            <!-- START DATE & END DATE -->
            <div class="row mx-1 justify-content-left my-3">
              <div class="col-5">
                <label class="d-block font-weight-bold m-0" for="startDate" style="bottom: 2px">Start Date</label>

                <div class="input-group" id="offerEffectiveStartDateDisplay">{{offerEffectiveStartDateDisplay}}</div>
              </div>

              <div class="col-5 offset-2">
                <label class="d-block font-weight-bold m-0" for="endDate" style="bottom: 2px">End Date</label>

                <div class="input-group" id="offerEffectiveEndDateDisplay">{{offerEffectiveEndDateDisplay}}</div>
              </div>
            </div>            
            <!-- OFFER LIMITS & PLU -->
            <div class="row mx-1 pb-3 justify-content-left my-3">
              <div class="col-5">
                <label class="d-block font-weight-bold m-0" for="offerLimitsCtrl">Offer Limits</label>
                {{usageLimitTypePerUserDisplay}}
              </div>
              <div class="col-5 offset-2">

                
                  <ng-container *ngIf="isEcomFlagEnabled">
                   <div *ngIf="isDisplayPromoCodeField" class="row">
                    <div class="col-12">
                      <label class="d-block font-weight-bold m-0">eComm Promo Code</label>
                    </div>
                    <div class="col-12" id="ecommPromoCodeDisplay">{{ecommPromoCodeDisplay}}</div>
                    <div class="col-12 mt-2" *ngIf="isDisplayApplyPromoCodeFeatureFlag">
                      <label class="d-block font-weight-bold m-0" for="isAutoApplyPromoCode" style="bottom: 2px;">Auto Apply Promo Code</label>
                      <div id="isApplyPromoCodeDisplay">
                        {{ isApplyPromoCodeDisplay }}
                      </div>
                    </div>
                   </div>   
                </ng-container>
               

                <div class="row" *ngIf="isDisplayAdField">
                  <div class="col-12">
                    <label class="d-block font-weight-bold m-0" for="inAd">In Ad</label>
                  </div>
                  <div *ngIf="inAdDisplay == 'IA'" class="col-12">In Ad</div>
                  <div *ngIf="inAdDisplay == 'NIA'" class="col-12">Not In Ad</div>
                </div>
                <div class="row" *ngIf="isDisplayPluField">
                  <div class="col-12">
                    <label class="d-block font-weight-bold m-0" for="pluTriggerBarcode">PLU</label>
                  </div>
                  <div class="col-12">{{pluTriggerBarcodeDisplay}}</div>
                </div>
                <!-- Initial Subscription -->
                <ng-container *ngIf="canShowInitialSubscriptionCheckBox">
                  <div class="row" >
                    <div class="col-12">
                      <label class="d-block font-weight-bold m-0" for="initialSubscriptionOffer">Initial Subscription</label>
                      
                    </div>
                    <div class="col-12">
                      {{initialSubscriptionOfferDisplay}}
                    </div>
                  </div>
                </ng-container>

              </div>
              <h6>mmmmm</h6>
              <file-attachment [showAttachFileButton]="false"></file-attachment>
            </div>
            <ng-container *ngIf="showEcommPromoFields">
              <div class="row mx-1 pb-3 justify-content-left my-3">
                <div class="col-5">
                  <label class="d-block font-weight-bold m-0" for="notCombineWithDisplayCtrl">Not Combinable With</label>
                  {{notCombineWithDisplay}}
                </div>
                <div class="col-5 offset-2">
                  <label class="d-block font-weight-bold m-0" for="orderCountDisplayCtrl">Order Count</label>
                  {{orderCountDisplay}}
                </div>
              </div>
              <div class="row mx-1 pb-3 justify-content-left my-3">
                <div class="col-5">
                  <label class="d-block font-weight-bold m-0" for="validWithOtherOfferDisplayCtrl">Valid With Other Offer</label>
                  {{validWithOtherOfferDisplay}}
                </div>
                <div class="col-5 offset-2">
                  <label class="d-block font-weight-bold m-0" for="firstTimeCustomerOnlyDisplayCtrl">First Time Customer Only</label>
                  {{firstTimeCustomerOnlyDisplay}}
                </div>
              </div>
            </ng-container>
            <div class="row mx-1 pb-3 justify-content-left my-3">
              <div class="col-5" *ngIf="cpgDisplay?.length > 0">
                <ng-container *ngIf="isEcomFlagEnabled">
                  <div *ngIf="isEcommOnlyAndPromoCodeSelected" class="row">
                    <div class="col-12">
                      <label class="d-block font-weight-bold m-0">CPG</label>
                    </div>
                    <div class="col-12" id="cpgDisplay">{{cpgDisplay}}</div>
                  </div>  
                </ng-container>
              </div>
            </div>
            <ng-container *ngIf="showBehavioralAction">
              <div class="row mx-1 pb-3 justify-content-left my-3">
                <div class="col-3">
                  <label id="behavioralActionDisplay" class="d-block font-weight-bold m-0" for="behavioralAction">Behavioral Action</label>
                  {{behavioralActionDisplay}}
                </div>
                <ng-container *ngIf="isBehavioralContinuityEnabledAndSelectedBAC">
                  <div class="col-3">
                    <label id="noOfTransactions" class="d-block font-weight-bold m-0" for="behavioralCondition.noOfTransactions">No Of Transactions</label>
                    {{noOfTransactionsDisplay}}
                  </div>
  
                  <div class="col-3">
                    <label id="noOfTransactions" class="d-block font-weight-bold m-0" for="behavioralCondition.minimumSpend">Minimum Spend</label>
                    {{minimumSpendDisplay}}
                  </div>
  
                  <div class="col-3">
                    <label id="noOfTransactions" class="d-block font-weight-bold m-0" for="behavioralCondition.minimumSpendUom">UOM</label>
                    {{minimumSpendUomDisplay}}
                  </div>
                </ng-container>
              </div>
            </ng-container>
          </div>        
        </section>
      </div>
    </div>
  </div>
</section>

<ng-template #fulfillMentChannelTmpl>
  <div class="actions-popover">   
    <fullfillment-channel-flags [channelPopup]="channelPopup" [formGroup]="requestForm.get('offerReqGroup')" 
    [optionsToBeDisabled] = "optionsToBeDisabledObj"></fullfillment-channel-flags>
  </div>
</ng-template>

import { ChangeDetectorRef, Component, Input, TemplateRef, ViewChild } from "@angular/core";
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from "@angular/forms";

import { ActivatedRoute } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { AppDataModel } from "@appModels/appData.model";
import { REQUEST_CONSTANTS } from "@appModules/request/constants/request_constants";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { LoaderService } from "@appServices/common/loader.service";
import { LoggerService } from "@appServices/common/logger.service";
import { NotificationService } from "@appServices/common/notification.service";
import { FullFillmentChannelService } from "@appServices/details/full-fillment-channel.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import { addError } from "@appUtilities/addError";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import * as moment from "moment";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { PopoverDirective } from "ngx-bootstrap/popover";
import { Observable } from "rxjs";

@Component({
  selector: "offer-request-section",
  templateUrl: "./request-section.comp.html",
  styleUrls: ["./request-section.comp.scss"],
})
export class OfferRequestSectionComponent extends UnsubscribeAdapter {
  requestForm;
  /**APP Data */
  appData: AppDataModel;
  /* OFFER REQUEST */
  channels: object;
  departmentsConfig: object;
  programs: object;
  segments: string[];
  divisions: object;
  offerLimits: any = [];
  filesList: any = [];
  attachedFilesList: any = [];
  fileAttributes: any = {};
  removeFileAttributes: any = {};
  offerTypes: string[];
  billingOptions = [];
  currentChannel = "";
  isDisplayPromoCodeField;
  isDisplayOrderField;
  baOrBacControlOption:string[]=[]
  /* UPC Information */
  asyncSelectedProductGroupName: string;
  dataSourceProductGroupName: Observable<any>;
  typeaheadLoading: boolean;
  productGroupNameList = [];
  /* Store Information */
  asyncSelectedStoreGroupName: string;
  dataSourceStoreGroupName: Observable<any>;
  storeGroupNameList = [];
  /** Dates */
  colorTheme = "theme-dark-blue";
  currentDate: Date = new Date();
  minOfferEndDate: Date = new Date();
  minOfferStartDate: Date = new Date();
  /** notification */
  message = "";
  status = "";
  isDraftSaveAttempted: boolean;
  modalRef: BsModalRef;
  startDateError: boolean = false;
  endDateError: boolean = false;

  //min dates

  minNOPAEndDate: Date = new Date();
  group: object;
  groupDivision: any;
  currentNOPAStartDate: Date;
  currentNOPAEndDate: Date;
  currentOfferStartDate: Date;
  currentOfferEndDate: Date;
  channelType: any;

  requestDigitalStatus = null;
  requestNonDigitalStatus = null;
  isDisplayAdField: boolean;
  isDisplayPluField: boolean;
  @Input() isSummary: boolean;
  deliveryChannelDisplay: string;
  departmentsDisplay: string;
  programCodeDisplay: string;
  customerSegmentDisplay: string;
  divisionsDisplay: string;
  groupDisplay: string;
  brandAndSizeDisplay: string;
  offerEffectiveStartDateDisplay: string;
  offerEffectiveEndDateDisplay: string;
  usageLimitTypePerUserDisplay: string;
  inAdDisplay: string;
  minOrderTotalAmountDisplay: string;
  tagsDisplay: string;
  pluTriggerBarcodeDisplay: string;
  descDisplay: string;
  isReqSubmitAttempted: boolean = false;
  validPluRange: { min: number; max: number };
  programCodeValue;
  groupDivisionDisplay: any;
  createdTimeStamp: any;
  @ViewChild("reserveInlinePlu")
  reserveInlinePluTmpl: TemplateRef<any>;

  @ViewChild("channelPopup")
  public channelPopup: PopoverDirective;

  fulfillmentChannelDisplay: any;
  ecommPromoCodeTypeChannel: any;
  selectedPromoType: any;
  constants;
  ecommPromoTypeDisplay: any;
  ecommPromoCodeDisplay: any;
  ecommOrderDisplay: any;
  cpgDisplay:any = "";
  isApplyPromoCodeDisplay: boolean;
  validWithOtherOfferDisplay: boolean;
  notCombineWithDisplay: any;
  behavioralActionDisplay: any;
  noOfTransactionsDisplay: any;
  minimumSpendDisplay: any;
  minimumSpendUomDisplay: any;
  orderCountDisplay: any;
  firstTimeCustomerOnlyDisplay: any;
  behavioralActions: any;
  behavioralContinuityActions: any;
  uomConfigs: any;
  isInitialSubscriptionOfferChecked:boolean = false;
  disableInitialSubscriptionOfferCheckbox:boolean = false;
  initialSubscriptionOfferDisplay:any;
    constructor(
    private fb: UntypedFormBuilder,
    public activatedRoute: ActivatedRoute,
    private _initialDataService: InitialDataService,
    public _storeGroupService: StoreGroupService,
    public notificationService: NotificationService,
    public _loaderService: LoaderService,
    public _requestFormService: RequestFormService,
    private cdr: ChangeDetectorRef,
    private modalService: BsModalService,
    private featureFlagService: FeatureFlagsService,
    private fullFillmentChannelService: FullFillmentChannelService,
    private facetItemService: FacetItemService,
    private service$: GeneralOfferTypeService,
    private logger: LoggerService
  ) {
    super();
    // reset the status on form creation
    this._requestFormService.digitalStatus = null;
    this._requestFormService.nonDigitalStatus = null;
  }

  ngOnInit() {
    this.requestForm = this._requestFormService.requestForm;
    this._requestFormService.setReqSectionValidationsForSubmit = this.setReqSectionValidationsForSubmit.bind(this);
    this.buildForm();
    this.setFormFieldsOptions();
    this.initSubscribes();
    this._requestFormService.showPLUWraningTmpl = false;
    this._requestFormService.saveAnywayPLU = false;
    this.persistedProgramCode();
    this.initVariables();
    this._requestFormService.createdApplicationId$.subscribe((obj)=>{
      let nonEditableFields = this.service$?.offerRequestData?.info?.uppIdInfo?.defaultFields;
      this._requestFormService.disableUPPFormControls(this.requestForm?.get("offerReqGroup").controls,obj,nonEditableFields);
    });
  }

  initVariables() {
    this.ecommPromoCodeTypeChannel = this.appData.ecommPromoCodeTypeChannel;
    this.constants = CONSTANTS;
    this.currentChannel = this.requestForm?.get("offerReqGroup")?.get("deliveryChannel")?.value;
  }

  get isDisplayAdvOptionBasedOnFeatureFlag() {
    return this.isEcommOnlyChannelSelected ? this.isEcomFlagEnabled : this.isFulfillmentFlagEnabled
  }

  getFieldValue(controlName) {
    return (this.offerReqGroup.get(controlName) as UntypedFormControl).value;
  }
  get showBehavioralAction() {
    const selectedChannel = this.requestForm?.get("offerReqGroup")?.get("deliveryChannel")?.value;
    return ((this.isBehavioralActionEnabled && selectedChannel === CONSTANTS.BEHAVIORAL_ACTION_CODE) || (this.isBehavioralContinutyEnabled && selectedChannel === CONSTANTS.BEHAVIORAL_CONTINUTY_CODE));
  }
  get isBehavioralActionEnabled() {
    return this._requestFormService.isBehavioralActionEnabled;
  }

  get isBehavioralContinutyEnabled() {
    return this.featureFlagService.isBehavioralContinuityEnabled;
  }

  get isBehavioralContinuityEnabledAndSelectedBAC () {
    const selectedChannel = this.requestForm?.get('offerReqGroup')?.get('deliveryChannel')?.value;
    return this.isBehavioralContinutyEnabled && selectedChannel === CONSTANTS.BEHAVIORAL_CONTINUTY_CODE;
  }
  get isBehavioralActionEnabledAndSelectedBA () {
    const selectedChannel = this.requestForm?.get('offerReqGroup')?.get('deliveryChannel')?.value;
    return this.isBehavioralActionEnabled && selectedChannel === CONSTANTS.BEHAVIORAL_ACTION_CODE;
  }
  isFieldInvalid(controlName) {
    //Returns the Invalid status
    let control = this.offerReqGroup.get(controlName) as UntypedFormControl;
    if ((this.isDraftSaveAttempted || this.isReqSubmitAttempted) && control && control.untouched) {
      return control.invalid;
    } else {
      return false;
    }
  } 
  adTypeChange(evnt) {
    this._requestFormService.adTypeObsrvble$.next(evnt.target.value);
  }
  startDateChange(evnt) {
    this._requestFormService.startDateObsrvble$.next(evnt);
  }
  reserveInlinePluFunc() {
    this.openModal(this.reserveInlinePluTmpl, {
      id: "inline-plu-tmpl",
      keyboard: true,
      class: "modal-xl",
    });
  }

  get optionsToBeDisabledObj() {
    //For Ecomm channel, in store purchase option needs to be disabled
    if (this.isEcommOnlyChannelSelected) {
      return ['inStorePurchase'];
    }
    return []

  }
  openModal(template, options) {
    this.modalRef = this.modalService.show(template, options);
  }
  getFieldErrors(controlName) {
    let control = this.offerReqGroup.get(controlName) as UntypedFormControl;
    if(controlName === 'adType')
    {
      if ((this.isReqSubmitAttempted) && control && control.untouched) {
        return control.errors;
      } else {
        return control.setErrors(null);
      }
    }
    else{
      if ((this.isDraftSaveAttempted || this.isReqSubmitAttempted) && control && control.untouched) {
        return control.errors;
      } else {
        return control.setErrors(null);
      }
    }
  }

  isFieldInvalidPostSave(controlName) {
    //Returns the Invalid status for the fields which needs to be validated during or post submit
    let control = this.offerReqGroup.get(controlName) as UntypedFormControl;
    if (this.isReqSubmitAttempted && control && control.untouched) {
      return control.invalid;
    } else {
      return false;
    }
  }
  closePLUWarning() {
    this._requestFormService.showPLUWraningTmpl = false;
    this.offerReqGroup.get("pluTriggerBarcode").setValue(null);
  }

  PLUConfirmation() {
    const activatedRoute = this.activatedRoute;
    const reqId = activatedRoute && activatedRoute.snapshot.params["requestId"];
    this._requestFormService.saveAnywayPLU = true;
    this._requestFormService.showPLUWraningTmpl = false;
    if (this._requestFormService.callingFrom === "save") {
      this._requestFormService.saveOR();
    } else {
      this._requestFormService.saveSubmitOR(reqId);
    }
  }

  getFieldErrorsPostSave(controlName) {
    //Returns the Invalid status for the fields which needs to be validated during or post submit
    let control = this.offerReqGroup.get(controlName) as UntypedFormControl;
    switch (controlName) {
      case "offerEffectiveStartDate": {
        this.validateStartDt();
        break;
      }
      case "offerEffectiveEndDate": {
        this.validateEndDt();
        break;
      }
    }

    if (this.isReqSubmitAttempted && control && control.untouched) {
      return control.errors;
    } else {
      control.setErrors(null);
      return false;
    }
  }

  get offerReqGroup() {
    return this.requestForm.controls.offerReqGroup as UntypedFormGroup;
  }

  isHideChannelOption(channel) {
    //Hide  EC option if feature flag is off
    if (channel === "EC") {
      return !(this.featureFlagService.isFeatureFlagEnabled(REQUEST_CONSTANTS.ENABLE_ECOM_PROMO_CODE_FEATURE_FLAG) || this.featureFlagService.isFeatureFlagEnabled(CONSTANTS.ENABLE_SCHEDULE_AND_SAVE));
    } else if(channel === CONSTANTS.BEHAVIORAL_ACTION_CODE) {
      return !this.isBehavioralActionEnabled;
    } else if(channel === CONSTANTS.BEHAVIORAL_CONTINUTY_CODE){
      return !this.isBehavioralContinutyEnabled; 
    }else if(channel === CONSTANTS.INLANE){
      return true;
    }

  }
  isHideEcommPromoCodeType(ecommPromoCodeType) {
    if (ecommPromoCodeType === CONSTANTS.EPC) {
      return !this.featureFlagService.isFeatureFlagEnabled(REQUEST_CONSTANTS.ENABLE_ECOM_PROMO_CODE_FEATURE_FLAG);
    } else if (ecommPromoCodeType === CONSTANTS.ESS) {
      return !this.featureFlagService.isFeatureFlagEnabled(CONSTANTS.ENABLE_SCHEDULE_AND_SAVE);
    }
  }
  get showEcommPromoFields() {
    return this.isOnlyEcomPromoEnable && this.isEcommOnlyChannelSelected &&  this.isDisplayPromoCodeField;
  }
  buildForm() {
    let formGroup = this.offerReqGroup;
    if (!formGroup) {
      this.requestForm.addControl("offerReqGroup", this.fb.group(this.setFormFields()));
    }
  }

  isReqGroupAvailable() {
    return this.requestForm.get("offerReqGroup");
  }

  setFormFields() {
    return {
      /* OFFER REQUEST */
      deliveryChannel: [null, [Validators.required]],
      department: [null, [Validators.required]],
      programCode: ["SC", [Validators.required]],
      customerSegment: ["Any Customer", [Validators.required]],
      brandAndSize: [null, [Validators.required]],
      cpg: [null, []],
      offerEffectiveStartDate: [null, []],
      offerEffectiveEndDate: [null, []],
      initialSubscriptionOffer:[false,[]],
      usageLimitTypePerUser: [null, []],
      adType: [null, []],
      pluTriggerBarcode: ["", []],
      group: [null, [Validators.required]],
      groupDivision: [null, []],
      desc: [null, [Validators.maxLength(5000)]],
      digitalStatus: [null, []],
      nonDigitalStatus: [null, []],
      editStatus: [null, []],
      digitalEditStatus: [null, []],
      nonDigitalEditStatus: [null, []],
      numOfTiers: [null, []],
      fulfillmentChannel: [null],
      ecommPromoType: [null, []],
      ecommPromoCode: [null, []],
      order: [null, []],
      isAutoApplyPromoCode: [false, []],
      validWithOtherOffer: [false, []],
      notCombinableWith:[null, []],
      behavioralAction: [null, []],
      behavioralCondition: this.fb.group({
        noOfTransactions: [null, []],
        minimumSpend: [null, []],
        minimumSpendUom: [null, []]
      }),
      orderCount: [null, []],
      firstTimeCustomerOnly: [false, []],
      isRedeemableInSameTransaction:[false,[]]
    };
  }
  
  getChannelFg() {
    return (
      (this.fullFillmentChannelService.fulfillChannelConstants &&
        Object.keys(this.fullFillmentChannelService.fulfillChannelConstants)?.reduce((output, item) => {
          output[item] = this.isDisplayAdvOptionBasedOnFeatureFlag ? true : false;
          return output;
        }, {})) ||
      {}
    );
  }
  setInitialOfferUsageLimitValue(channel) {
    this.offerLimits = this.appData.offerRequestLimits[channel] || [];
  }
  initSubscribes() {
    this.subs.sink = this._requestFormService.isDraftSaveAttempted.subscribe((value) => {
      this.isDraftSaveAttempted = value;
    });

    this.subs.sink = this._requestFormService.isReqSubmitAttempted$.subscribe((value) => {
      this.isReqSubmitAttempted = value;
      this.cdr.detectChanges();
    });
    // When the form value changes
    this.subs.sink = this.requestForm
      .get("offerReqGroup")
      .get("deliveryChannel")
      .valueChanges.subscribe((values) => {
        this.currentChannel = values;
        this.toggleAdPluFields();
        this.setInitialOfferUsageLimitValue(this.currentChannel);
      });
    this.subs.sink = this.requestForm
      .get("offerReqGroup")
      .get("offerEffectiveStartDate")
      .valueChanges.subscribe((values) => {
        //calling method to update justification bool on date change
        this.updateCheckJustification();
      });

    this.subs.sink = this._requestFormService.requestDigitalStatus$.subscribe((obj) => {
      this.requestDigitalStatus = obj;
    });
    this.subs.sink = this._requestFormService.requestNonDigitalStatus$.subscribe((obj) => {
      this.requestNonDigitalStatus = obj;
    });
    this.showInitialSubscriptionCheckBox();
    if (this.isSummary) {
      this._requestFormService.offerRequestDataDisplay$.subscribe((value) => {
        let initialData = window["initialData"].replace(/&amp;/g, "&");
        initialData = JSON.parse(decodeURI(initialData.replace(/&#39;/g, "'")));
        this.appData = initialData.appData;
        if (value) {
          console.log("Request Section Data", value);
          const group = this.group[value.group];
          this.channels = this.appData.offerDeliveryChannels;
          this.deliveryChannelDisplay = this.channels[value.deliveryChannel];
          this.requestForm.get('offerReqGroup').get('deliveryChannel').setValue(value.deliveryChannel);
          if((this.departmentsConfig as Array<string>).includes(value.department))
          {
              this.departmentsDisplay = value.department;
          }
          else{
            this.departmentsDisplay = CONSTANTS.DEFAULT_DEPARTMENT;
          }
          this.programCodeDisplay = this.programs[value.programCode];
          this.group = this.appData.offerRequestGroups;
          this.groupDisplay = group && group.name;
          this.groupDivision = group && group.groupDivisions;
          this.groupDivisionDisplay =
            this.groupDivision && value.groupDivision && this.groupDivision.filter((ele) => ele["code"] === value.groupDivision)[0]["name"];
          this.customerSegmentDisplay = value.customerSegment;
          this.brandAndSizeDisplay = value.brandAndSize;
          this.behavioralActionDisplay = value.behavioralAction;
          this.noOfTransactionsDisplay = value.behavioralCondition.noOfTransactions;
          this.minimumSpendDisplay = value.behavioralCondition.minimumSpend;
          this.minimumSpendUomDisplay = this.uomConfigs[value.behavioralCondition.minimumSpendUom];
          this.fulfillmentChannelDisplay =
            value.fulfillmentChannel && this.fullFillmentChannelService.setFulfillmentDisplayValue(value.fulfillmentChannel);
          this.offerEffectiveStartDateDisplay = value.offerEffectiveStartDate;
          this.offerEffectiveEndDateDisplay = value.offerEffectiveEndDate;
          this.usageLimitTypePerUserDisplay = value.usageLimitTypePerUser
            ? this.appData.offerRequestLimits[value.deliveryChannel][value.usageLimitTypePerUser]: "";
          this.ecommPromoTypeDisplay = value.ecommPromoType
            ? this.appData.ecommPromoCodeTypeChannel[value.ecommPromoType] : "";
          this.ecommPromoCodeDisplay = value.ecommPromoCode;
          this.cpgDisplay = value.cpg;
          this.isApplyPromoCodeDisplay = value.isAutoApplyPromoCode;
          this.notCombineWithDisplay = value.notCombinableWith;
          this.firstTimeCustomerOnlyDisplay = value.firstTimeCustomerOnly;
          this.orderCountDisplay = value.orderCount;
          this.validWithOtherOfferDisplay = value.validWithOtherOffer;
          this.ecommOrderDisplay = value.order;
          this.selectedPromoType = value.ecommPromoType;
          this.inAdDisplay = value.adType;
          this.pluTriggerBarcodeDisplay = value.pluTriggerBarcode;
          this.setPromoCdOnInit(value.ecommPromoType);
          this.initialSubscriptionOfferDisplay = value.initialSubscriptionOffer == true ? 'True' : 'False'
        }
      });
    } else {
      // update some readonly values
      this._requestFormService.requestData$.subscribe((value) => {
        let initialData = window["initialData"].replace(/&amp;/g, "&");
        initialData = JSON.parse(decodeURI(initialData.replace(/&#39;/g, "'")));
        this.appData = initialData.appData;
        if (value) {
          console.log("Request Section Data2", value);
          const groupIndex = this.appData.offerRequestGroups.findIndex((ele) => ele["code"] === value.info.group);
          this.programs = this.appData.offerPrograms;
          this.programCodeDisplay = this.programs[value.info.programCode];
          this.channels = this.appData.offerDeliveryChannels;
          this.groupDivision = this.group && this.group[groupIndex]?.groupDivisions;
          this.deliveryChannelDisplay = this.channels[value.info.deliveryChannel];
          this.requestForm.get('offerReqGroup')?.get('deliveryChannel').setValue(value.info.deliveryChannel);
          this.requestForm.get('offerReqGroup')?.get('behavioralAction')?.setValue(value.info.behavioralAction);
          this.inAdDisplay = value.info.adType;
          this.usageLimitTypePerUserDisplay = value.usageLimitTypePerUser
            ? this.appData.offerRequestLimits[value.deliveryChannel][value.usageLimitTypePerUser]: "";
          this.pluTriggerBarcodeDisplay = value.info.pluTriggerBarcode;
          this.selectedPromoType = value.info.ecommPromoType;
          this.setPromoCdOnInit(value?.info?.ecommPromoType);
          this.setInitialSubscriptionOffer(value?.info?.initialSubscriptionOffer);
          if(!this._requestFormService.canEditOfferStartDate(value?.info?.digitalStatus, value?.info?.nonDigitalStatus, value?.rules))
          {
            this.requestForm.get('offerReqGroup')?.get('offerEffectiveStartDate')?.disable();
          }
          
        }
      });
    }
    this.programCodeValue = this.getFieldValue("programCode");
  }
  setInitialSubscriptionOffer(initialSubscriptionOffer)
  {
    if(this.isInitialSubscriptionFeatureEnabled){
      this.offerReqGroup.get("initialSubscriptionOffer").setValue(initialSubscriptionOffer);
    }
  }
  setPromoCdOnInit(ecommPromoType) {
    const isEcomFeatureEnabled = this.featureFlagService.isFeatureFlagEnabled(REQUEST_CONSTANTS.ENABLE_ECOM_PROMO_CODE_FEATURE_FLAG);
    if (isEcomFeatureEnabled && ecommPromoType) {
      if (!this.selectedPromoType) {
        this.setSelectedPromoType(ecommPromoType);
      }
    }
    this.isDisplayPromoCodeField = this.selectedPromoType === CONSTANTS.EPC;
    this.isDisplayOrderField = this.selectedPromoType === CONSTANTS.ESS;
  }


  // setting the config data to the drop downs
  setFormFieldsOptions() {
    // set the select options in template
    this.appData = this._initialDataService.getAppData();
    this.channels = this.appData.offerDeliveryChannels;
    this.offerTypes = this.appData.offerDiscountTypes;
    this.programs = this.appData.offerPrograms;
    this.offerLimits = null;
    this.behavioralActions = this.appData["behavioralActions"];
    this.behavioralContinuityActions = this.appData["behavioralContinuityActions"];
    this.uomConfigs = this.appData["uomConfig"];  
    this.segments = this.appData.offerCustomerSegments;
    this.group = this.appData.offerRequestGroups;
    this.departmentsConfig = this.appData.departments;
    const _billingOptions = this._initialDataService.getAppData().billingOptions;
    _billingOptions.forEach((element) => {
      this.billingOptions.push({ name: element, value: element });
    });
  }

  get isFulfillmentFlagEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled(CONSTANTS.FULFILLMENT_CHANNEL_FEATURE_FLAG);
  }
  get isEcomFlagEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled(REQUEST_CONSTANTS.ENABLE_ECOM_PROMO_CODE_FEATURE_FLAG) || this.featureFlagService.isFeatureFlagEnabled(CONSTANTS.ENABLE_SCHEDULE_AND_SAVE);
  }
  get isOnlyEcomPromoEnable() {
    return this.featureFlagService.isFeatureFlagEnabled(REQUEST_CONSTANTS.ENABLE_ECOM_PROMO_CODE_FEATURE_FLAG);
  }
  setFulfillmentChannelsFlags(currentChannel) {
    if (!this.isDisplayAdvOptionBasedOnFeatureFlag) {
      return false;
    }
    const isValidChnnel = ["DO", "IS", "EC", "BA","BAC"].includes(currentChannel);
    if (!isValidChnnel) {
      this.fullFillmentChannelService.setFullfillmentChannelCtrls(null, isValidChnnel, this.offerReqGroup);
    } else {
      this._requestFormService.requestData$.subscribe((data) => {
        this.fullFillmentChannelService.setFullfillmentChannelCtrls(data, isValidChnnel, this.offerReqGroup);
      });
    }
  }

  selectedGroup() {
    const selectedValue = this.offerReqGroup.get("group").value;
    this.groupDivision = this.group[selectedValue].groupDivisions;
    const groupDivisions = this.offerReqGroup.get("groupDivision");
    if (this.groupDivision) {
      groupDivisions.setValidators([Validators.required]);
    } else {
      groupDivisions.clearValidators();
      this.groupDivision = null;
    }
  }

  setChannel(code) {
    const offerRequestFormChannel = this.offerReqGroup.get("channels");
    if (code !== CONSTANTS.SC) {
      offerRequestFormChannel && offerRequestFormChannel.setValue("DO");
    } else {
      offerRequestFormChannel && offerRequestFormChannel.setValue("");
    }
  }

  get showAdvancedOption() {
    return ["DO", "IS", "EC", "BA", "BAC"].includes(this.currentChannel);
  }

  get isEcommOnlyChannelSelected() {
    return ["EC"].includes(this.currentChannel);
  }

  get isScheduleAndSavePromoTypeSelected() {
    return ["ESS"].includes(this.selectedPromoType);
  }
  get isEcommPromoCodeSelected(){
    return [CONSTANTS.EPC].includes(this.selectedPromoType);
  }
  promoTypeChanged(selectedVal) {
    this.isDisplayPromoCodeField = false; //reset   
    this.isDisplayOrderField = false;

    this.setSelectedPromoType(selectedVal);
    //Display promocode field based on below condition 
    if (this.selectedPromoType === CONSTANTS.EPC) {
      this.isDisplayPromoCodeField = true;
      this.isDisplayOrderField = false;
      this.offerReqGroup.get("order").setValue(null);
    } else if (this.selectedPromoType === CONSTANTS.ESS) {
      this.isDisplayPromoCodeField = false;
      this.isDisplayOrderField = false;
      this.showInitialSubscriptionCheckBox();
      this.offerReqGroup.get("order").setValue(null);
      this.offerReqGroup.get("ecommPromoCode").setValue(null);
      this.offerReqGroup.get("isAutoApplyPromoCode").setValue(false);
      this.offerReqGroup.get("firstTimeCustomerOnly").setValue(false);
      this.offerReqGroup.get("validWithOtherOffer").setValue(false);
      this.offerReqGroup.get("notCombinableWith").setValue(null);
      this.offerReqGroup.get("orderCount").setValue(null);
    } else {
      /*Fix: eComm Promo Code field is displaying for Schedule & Save when the OR is copied and
       then given the eComm Promo Type as Schedule & Save*/
      this.offerReqGroup.get("ecommPromoCode").setValue(null);
      this.offerReqGroup.get("order").setValue(null);
    }
  }

  get isDisplayApplyPromoCodeFeatureFlag() {
    return this.featureFlagService.isFeatureFlagEnabled(REQUEST_CONSTANTS.ENABLE_APPLY_PROMO_CODE_FEATURE_FLAG);
  }

  setSelectedPromoType(selectedVal) {
    //Takes input as "1:EC" and outputs "EC"
    if (selectedVal.indexOf(":") > -1) {
      let splitVal = selectedVal.split(": ");
      this.selectedPromoType = splitVal[1];
    } else {
      this.selectedPromoType = selectedVal;
    }
  }


  getSelectedChannel(event) {
    if (event.indexOf(":") > -1) {
      let splitVal = event.split(": ");
      this.currentChannel = splitVal[1];
    } else {
      this.currentChannel = event;
    }
    this.toggleAdPluFields();
    this.toggleEcommFields();    
    this.setFulfillmentChannelsFlags(this.currentChannel);
    this.updateFullfillmentChannelValues();
    this.setOfferLimitsBasedOnChannel(this.currentChannel);
    this._requestFormService.selectedChannel$.next(this.currentChannel);
    (this.isBehavioralActionEnabled || this.isBehavioralContinutyEnabled )&& this._requestFormService.onChangeChannel$.next(this.currentChannel);
  }

  updateFullfillmentChannelValues() {
    if (this.optionsToBeDisabledObj[0]) {
      this.offerReqGroup.get('fulfillmentChannel').get(this.optionsToBeDisabledObj[0])?.setValue(false); //inStorePurchase is disabled for Ecomm only channel     
    }
  }

  setOfferLimitsBasedOnChannel(currentChannel) {
    const offerUsageLimitCtrl = this.offerReqGroup.get("usageLimitTypePerUser");
    offerUsageLimitCtrl && offerUsageLimitCtrl.setValue(null);
    this.offerLimits = this.appData.offerRequestLimits[currentChannel] ||[] ;
  }
  getSelectedOfferLimitType(event) {
    let currentOfferLimit;
    if (event.indexOf(":") > -1) {
      let splitVal = event.split(": ");
      currentOfferLimit = splitVal[1];
    } else {
      currentOfferLimit = event;
    }
    this._requestFormService.selectedOfferLimitType$.next(currentOfferLimit);
  }
  toggleBehavioralActionField() {
    const bhActionCtrl = this.offerReqGroup?.get("behavioralAction");
    bhActionCtrl?.clearValidators();
    bhActionCtrl?.setErrors(null);
    if(!this.showBehavioralAction) {
      bhActionCtrl?.setValue(null);
    }
    bhActionCtrl.updateValueAndValidity();
  }
  toggleEcommFields() {
    //When channel is changed, Reset
    this.isDisplayPromoCodeField = false;
    this.isDisplayOrderField = false;

    const promoTypeControl = this.offerReqGroup.get("ecommPromoType"),
      promoOrderControl = this.offerReqGroup.get("order"),
      promoCodeControl = this.offerReqGroup.get("ecommPromoCode"),
      applyPCControl = this.offerReqGroup.get("isAutoApplyPromoCode"),
      firstTymCustOnlyCtrl = this.offerReqGroup.get("firstTimeCustomerOnly"),
      validWithOtherOfferCtrl = this.offerReqGroup.get("validWithOtherOffer"),
      orderCountCtrl = this.offerReqGroup.get("orderCount"),
      notCombineWithCtrl = this.offerReqGroup.get("notCombinableWith");

    //Reset validators
    promoTypeControl.clearAsyncValidators();
    promoTypeControl.clearValidators();
    promoTypeControl.setErrors(null);

    if (!this.isEcommOnlyChannelSelected) {
      promoTypeControl.setValue(null);
      this.setSelectedPromoType("");
      notCombineWithCtrl.setValue(null);
      orderCountCtrl.setValue(null);
      promoCodeControl.setValue(null);
      promoOrderControl.setValue(null);
      applyPCControl.setValue(false);
      firstTymCustOnlyCtrl.setValue(false);
      validWithOtherOfferCtrl.setValue(false)

      //Reset validators    
      orderCountCtrl.clearValidators(); 
      promoCodeControl.clearAsyncValidators();
      promoCodeControl.clearValidators();
      notCombineWithCtrl.clearValidators();
    }

    promoTypeControl.updateValueAndValidity();
    promoCodeControl.updateValueAndValidity();
  }

  toggleAdPluFields() {
    this.isDisplayAdField = false;
    this.isDisplayPluField = false;

    const adTypeControl = this.offerReqGroup.get("adType");

    if (this.currentChannel === "PO" || this.currentChannel === "CC") {
      this.isDisplayPluField = true;
    } else if (this.currentChannel === "DO") {
      this.isDisplayAdField = true;
    }
    this.setInAdValidators();

    if (!this.isDisplayPluField) {
      this.offerReqGroup.get("pluTriggerBarcode").setValue("");
    }

    if (!this.isDisplayAdField) {
      adTypeControl && adTypeControl.setValue(null);
    }
  }



  setInAdValidators() {
    const adTypeControl = this.offerReqGroup.get("adType");
    if (this.currentChannel === "DO" && this.isReqSubmitAttempted) {
      adTypeControl.setValidators([Validators.required]);      
    } else {
      adTypeControl && adTypeControl.clearValidators();
    }
    adTypeControl && adTypeControl.updateValueAndValidity();
  }

  setReqSectionValidationsForSubmit() {
    if (!this.isReqSubmitAttempted) {
      return false;
    }
    const pluPromotionControl = this.offerReqGroup.get("pluTriggerBarcode");
    pluPromotionControl.clearAsyncValidators();
    pluPromotionControl.clearValidators();
    if (this.currentChannel == "CC" || this.currentChannel == "PO") {
      pluPromotionControl.setValidators([this.PLUvalidatorReq()]);
    }
    pluPromotionControl.updateValueAndValidity();
    this.setInAdValidators();
    this.setFieldValidatorsOnSubmit();
  }

  setControlValidatorOnSubmit(ob) {
    const { controlName, condition } = ob,
      control = this.offerReqGroup.get(controlName),
      value = control.value;
    control.clearAsyncValidators();
    control.clearValidators();


    let obj = null;

    if (!value && condition) {
      obj = Object.assign(obj || {}, { required: true });
      this.offerReqGroup.get(controlName).setErrors(obj);
      control.setValidators([Validators.required]);
    }

    control.updateValueAndValidity();
  }

  setFieldValidatorsOnSubmit() {
    this.setControlValidatorOnSubmit({ controlName: "ecommPromoCode", condition: this.isDisplayPromoCodeField });
    this.setValidatorForNotCombineWith({controlName: "notCombinableWith", condition: this.isDisplayPromoCodeField});
    this.setControlValidatorOnSubmit({controlName: "behavioralAction", condition: this.isBehavioralActionEnabledAndSelectedBA || this.isBehavioralContinuityEnabledAndSelectedBAC});
    this.setControlValidatorOnSubmit({controlName: "behavioralCondition.noOfTransactions", condition: this.isBehavioralContinuityEnabledAndSelectedBAC});
    
    const usageLimitTypePerUserCtrl = this.offerReqGroup.get("usageLimitTypePerUser");
    usageLimitTypePerUserCtrl.clearValidators();
    const usageLimitTypePerUser = usageLimitTypePerUserCtrl.value;
    let obj = null;

    if (!usageLimitTypePerUser) {
      obj = Object.assign(obj || {}, { reqdUsageLimitTypePerUser: true });
      this.offerReqGroup.get("usageLimitTypePerUser").setErrors(obj);
      usageLimitTypePerUserCtrl.setValidators([Validators.required]);
    } else if (
      this._requestFormService.generalOfferTypeService &&
      this._requestFormService.generalOfferTypeService.generalOfferTypeForm &&
      this._requestFormService.generalOfferTypeService.generalOfferTypeForm.value.offerRequestOffers
    ) {
      if(this.service$.type && this.service$.type.value === "Rewards - Accumulation" && usageLimitTypePerUser !== "UNLIMITED"){
        usageLimitTypePerUserCtrl.setValidators([this.usageLimitValidation]);
      }
    }

    usageLimitTypePerUserCtrl.updateValueAndValidity();
  }

  usageLimitValidation(control: UntypedFormControl): { [s: string]: boolean } {
    if(control.value === "UNLIMITED"){
      return null;
    }
    return { 'invalidUsageLimit': true };
  }
  setValidatorForNotCombineWith(ob) {
    const { controlName, condition } = ob,
      control = this.offerReqGroup.get(controlName);
    control.clearAsyncValidators();
    control.clearValidators();

    if (condition) {
      control.setValidators([this.notCombineWithUniqueValidator.bind(this)]);
    }

    control.updateValueAndValidity();
  }
  rewardsAccumulationValidator() {
    return {
      invalidForRewardAccumulation: true,
    };
  }
  notCombineWithUniqueValidator() {
    const ecommPromoCode = this.offerReqGroup.get("ecommPromoCode")?.value,
    notCombinableWith = this.offerReqGroup.get("notCombinableWith");
    if(ecommPromoCode && notCombinableWith?.value) {
      const splitNotCombineWithValue = notCombinableWith?.value?.split(','),
      convertedCaseArr = splitNotCombineWithValue?.map(ele => ele.toUpperCase()) || [];

      if(convertedCaseArr?.includes(ecommPromoCode?.toUpperCase())) {
        return {
          notCombineWithUniqueError: true
        }
      }
    }
    return null;
  }

  PLUvalidatorReq() {
    //}else if (c.value >= min && c.value <=  max) {
    //}else if (c.value < 10000 || c.value > 99999) {
    return (c: UntypedFormControl) => {
      //const {min, max} = this.validPluRange;
      if (!c.value) {
        return {
          isRequiredError: true,
        };
      } else if (
        //If valid PLU 32000-32499
        (c.value >= 32000 && c.value <= 32499) ||
        (c.value >= 51000 && c.value <= 54999) ||
        (c.value >= 77000 && c.value <= 79999) ||
        (c.value >= 92000 && c.value <= 92999) ||
        c.value == 73115
      ) {
        return null;
      } else {
        return {
          invalidPlu: true,
        };
      }
    };
  }


  validateStartDt() {
    let offerStartDate = this.getFieldValue("offerEffectiveStartDate"),
      obj = null;
    //Check if start date is valid.
    if (!offerStartDate) {
      obj = {
        reqdStartDt: true,
      };
    }
    addError({ control: this.offerReqGroup.get("offerEffectiveStartDate"), errorObj: obj });
  }

  //setting the date check for the justification section if start date < create date
  updateCheckJustification() {
    let offerStartDate = this.getFieldValue("offerEffectiveStartDate");
    const activatedRoute = this.activatedRoute;
    const reqId = activatedRoute && activatedRoute.snapshot.params["requestId"];

    this.createdTimeStamp = reqId ? this._requestFormService.createdTs : null;
    if (offerStartDate) {
      this._requestFormService.setJustificationSection({
        createdTs: this.createdTimeStamp,
        offerEffectiveStartDate: offerStartDate,
      });
    }
  }

  validateEndDt() {
    let offerStartDate = this.getFieldValue("offerEffectiveStartDate"),
      offerEndDate = this.getFieldValue("offerEffectiveEndDate"),
      obj = null;

    offerStartDate = offerStartDate ? new Date(offerStartDate).getTime() : null;
    offerEndDate = offerEndDate ? new Date(offerEndDate).getTime() : null;

    //Check if end date is later than start date.
    if (offerEndDate && offerEndDate < offerStartDate) {
      obj = {
        endDtLessThanStartDt: true,
      };
    }
    //check if end date is missing
    if (!offerEndDate) {
      obj = Object.assign(obj || {}, { reqdEndDt: true });
    }
    this.offerReqGroup.get("offerEffectiveEndDate").setErrors(obj);
  }

  setCurrentChannel(event) {
    this.logger.debug("setCurrentChannel", event);
    if (!event) return;
    const behavioralConditionGroup = this.offerReqGroup.get('behavioralCondition') as UntypedFormGroup;
    behavioralConditionGroup.get('noOfTransactions').setValue(null);
    behavioralConditionGroup.get('minimumSpend').setValue(null);
    behavioralConditionGroup.get('minimumSpendUom').setValue(null);
    this.getSelectedChannel(event);
    this.showInitialSubscriptionCheckBox();

    this.offerReqGroup.get('behavioralAction').setValue(null);
    this.offerReqGroup.get('behavioralAction').updateValueAndValidity()
  }

  setCurrentOfferLimit(event) {
    if (!event) return;
    this.getSelectedOfferLimitType(event);
  }

  setMinOfferEndDate(event) {
    if (!event) {
      return;
    }
    if (new Date(event).getTime() < new Date().getTime()) {
      this.minOfferEndDate = new Date();
      return;
    }
    this.minOfferEndDate = new Date(event);
    if (new Date(event).getTime() > new Date(this.offerReqGroup.controls["offerEffectiveEndDate"].value).getTime()) {
      this.offerReqGroup.controls["offerEffectiveEndDate"].setValue(event);
    }
  }
  getPluReservationResponse(pluCode) {
    if (pluCode) {
      this.offerReqGroup.get("pluTriggerBarcode").setValue(pluCode);
      this._requestFormService.requestForm.get(["offerReqGroup", "pluTriggerBarcode"]).markAsDirty();
      this._requestFormService.requestForm.get(["offerReqGroup", "pluTriggerBarcode"]).markAsTouched();
    }
  }
  persistedProgramCode() {
    // Set programcode if not available in local storage
    const selectedProgramCode = this.facetItemService.programCodeSelected;
    if (selectedProgramCode) {
      localStorage.setItem(CONSTANTS.PROGRAM_CODE_LS, selectedProgramCode);
    }
  }
  setValidPromoCodeString($event:any){
    let ctrlId = $event.target.id;
    let enteredPromoCode = ($event.target.value?.split(",").map(item => item.trim())).join();
    $event.target.value = enteredPromoCode;
    this.offerReqGroup.get(ctrlId)?.setValue(enteredPromoCode);
  }

  get canShowInitialSubscriptionCheckBox(){
    if(this.isInitialSubscriptionFeatureEnabled)
    {
      const _initialSubscriptionOffer = this.requestForm?.get("offerReqGroup")?.get("initialSubscriptionOffer")?.value;
      if(_initialSubscriptionOffer === null)
        return !(_initialSubscriptionOffer === null);
      if(this.isEcommOnlyChannelSelected && this.isScheduleAndSavePromoTypeSelected)
        return true;
    }
    return false;
  }

  showInitialSubscriptionCheckBox()
  {
    this.requestForm?.get("offerReqGroup")?.get("initialSubscriptionOffer")?.setValue(false);
    return this.isInitialSubscriptionFeatureEnabled && this.isEcommOnlyChannelSelected && this.isScheduleAndSavePromoTypeSelected;
  }

  get getOfferRequestStatus()
  {
    return this.requestForm?.get("offerReqGroup")?.get("nonDigitalStatus")?.value;
  }

  enableInitialSubscriptionOfferCheckbox(){
    if(this.isInitialSubscriptionFeatureEnabled)
    {
      this.disableInitialSubscriptionOfferCheckbox =  ["P","D"].includes(this.getOfferRequestStatus);
    }
   
  }
  onInitialSubscriptionCheckBoxClick(event:any)
  {
    if(this.isSummary) return false;
    this.isInitialSubscriptionOfferChecked = event.checked;
      
  }
  get isInitialSubscriptionFeatureEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled("enableInitialSubscriptionOffers");
  }

  get isEcommOnlyAndPromoCodeSelected()
  {
    return this.isEcommOnlyChannelSelected && this.isEcommPromoCodeSelected;
  }
  ngOnDestroy(): void {
    (this.isBehavioralActionEnabled || this.isBehavioralContinutyEnabled ) && this._requestFormService.selectedChannel$.next(null);
  }
}

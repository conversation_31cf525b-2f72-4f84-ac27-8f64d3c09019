import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { RequestSummaryComponent } from './request-summary.component';
import { HttpClient } from '@angular/common/http';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { NotificationService } from '@appServices/common/notification.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { FullFillmentChannelService } from '@appServices/details/full-fillment-channel.service';
import { PermissionsConfigurationService, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BsModalRef, BsModalService, ModalModule } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';

describe('RequestSummaryComponent', () => {
  let component: RequestSummaryComponent;
  let fixture: ComponentFixture<RequestSummaryComponent>;
  let mockRequestFormService;
  let mockRouter;
  let mockActivatedRoute;
  let mockInitialDataService;
  let mockSearchOfferRequestService;
  let mockAuthService;
  let mockFacetItemService;
  let mockQueryGenerator;
  let mockHttpClient;
  let mockToastrService;
  let mockCommonService;
  let mockCommonRouteService;
  let mockFeatureFlagsService;
  let mockFullFillmentChannelService;
  let mockPermissionsService;
  let mockPermissionsConfigurationService;
  let mockModalService;
  let mockFileAttachService;
  let mockNotificationService;

  beforeEach(async () => {
    mockRequestFormService = {
      requestForm: new UntypedFormGroup({}),
      cachedDigitalOfferStatus: {},
      cachedNonDigitalOfferStatus: {},
      setReqServiceVariables: jasmine.createSpy('setReqServiceVariables'),
      userNonDigital$: of([]),
      userdDigital$: of([]),
      makeSearchCall: jasmine.createSpy('makeSearchCall'),
      currentOfferRequest: of({}),
      requestStatus$: of(null),
      requestDigitalStatus$: of(null),
      requestNonDigitalStatus$: of(null),
      offersData$: of([]),
      requestData$: of(null),
      setUpdateNotification: jasmine.createSpy('setUpdateNotification'),
      isPreviousDGStatusUpdating$: { next: jasmine.createSpy('next') },
      isPreviousNDStatusUpdating$: { next: jasmine.createSpy('next') },
      requestEditUpdateData$: { next: jasmine.createSpy('next') },
      digitalId: jasmine.createSpy('digitalId').and.returnValue('digital123'),
      nonDigitalId: jasmine.createSpy('nonDigitalId').and.returnValue('nonDigital123'),
      assignUserToOfferReq: jasmine.createSpy('assignUserToOfferReq').and.returnValue(of({})),
      unAssignUserToOfferReq: jasmine.createSpy('unAssignUserToOfferReq').and.returnValue(of({})),
      reqId: '123',
      changeReasonData$: { next: jasmine.createSpy('next') },
      subscribeCurrentOfferReqForProcess: jasmine.createSpy('subscribeCurrentOfferReqForProcess').and.returnValue({}),
      offerRuleDay$: { next: jasmine.createSpy('next') },
      offerRuleTime$: { next: jasmine.createSpy('next') },
      updateReqDataKeys: jasmine.createSpy('updateReqDataKeys'),
      isCallSetAttachmentData$: { next: jasmine.createSpy('next') },
      offerRequestDataDisplay$: { next: jasmine.createSpy('next') },
      offerNopaGroupDataDisplay$: { next: jasmine.createSpy('next') },
      offeradditionalDescriptionGroupDataDisplay$: { next: jasmine.createSpy('next') },
      offerJustificationGroupDataDisplay$: { next: jasmine.createSpy('next') },
      selectedOfferLimitType$: { next: jasmine.createSpy('next') },
      selectedChannel$: { next: jasmine.createSpy('next') },
      isEditNotificatonBoolean: { next: jasmine.createSpy('next') },
      resetOnDestroy: jasmine.createSpy('resetOnDestroy'),
      checkFulfillmentChannelEnabled: jasmine.createSpy('checkFulfillmentChannelEnabled').and.returnValue(true),
      getCreatePathFromPC: jasmine.createSpy('getCreatePathFromPC').and.returnValue('create'),
      passClonedObject$: { next: jasmine.createSpy('next') },
      dontsaveVal$: { next: jasmine.createSpy('next') },
      isExpiredStatus: jasmine.createSpy('isExpiredStatus').and.returnValue(false),
      attachedFilesList: [],
      selectedDeliveryChannel: 'Email',
      reqOffersObjkey: 'test',
      cloningProcess$: { next: jasmine.createSpy('next') }
    };

    mockRouter = {
      navigate: jasmine.createSpy('navigate').and.returnValue(Promise.resolve(true)),
      navigateByUrl: jasmine.createSpy('navigateByUrl').and.returnValue(Promise.resolve(true))
    };

    mockActivatedRoute = {
      snapshot: {
        params: { requestId: '12345' },
        queryParamMap: {
          get: jasmine.createSpy('get').and.returnValue('true')
        }
      }
    };

    mockInitialDataService = {
      getConfigUrls: jasmine.createSpy('getConfigUrls').and.returnValue('http://test-api.com'),
      getAppData: jasmine.createSpy('getAppData').and.returnValue({
        offerRequestEditChangeReason: [],
        offerRequestEditChangeReasonType: [],
        offerRequestGroups: [{ code: 'G1' }]
      })
    };

    mockSearchOfferRequestService = {
      searchOfferRequest: jasmine.createSpy('searchOfferRequest').and.returnValue(of({
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: []
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      }))
    };

    mockAuthService = {
      onUserDataAvailable: jasmine.createSpy('onUserDataAvailable').and.callFake((callback) => callback()),
      getUserId: jasmine.createSpy('getUserId').and.returnValue('12345'),
      getTokenString: jasmine.createSpy('getTokenString').and.returnValue('token123')
    };

    mockFacetItemService = {
      programCodeSelected: 'GR',
      reqOffersObjkey: 'test'
    };

    mockQueryGenerator = {
      setQuery: jasmine.createSpy('setQuery'),
      pushParameters: jasmine.createSpy('pushParameters'),
      getQuery: jasmine.createSpy('getQuery').and.returnValue(''),
      removeParam: jasmine.createSpy('removeParam')
    };

    mockHttpClient = {
      put: jasmine.createSpy('put').and.returnValue(of({})),
      post: jasmine.createSpy('post').and.returnValue(of({}))
    };

    mockToastrService = {
      success: jasmine.createSpy('success')
    };

    mockCommonService = {
      isReqInEditing: jasmine.createSpy('isReqInEditing').and.returnValue(false)
    };

    mockCommonRouteService = {
      isBpdReqPage: false
    };

    mockFeatureFlagsService = {
      isUPPFieldSearchEnabled: true
    };

    mockFullFillmentChannelService = {
      setFullfillmentChannelCtrls: jasmine.createSpy('setFullfillmentChannelCtrls')
    };

    mockPermissionsService = {
      getPermissions: jasmine.createSpy('getPermissions').and.returnValue({
        'DoStoreCouponsRequests': true,
        'EditGRSPDRequest': true,
        'Admin': true
      })
    };

    mockPermissionsConfigurationService = {
      getAllStrategies: jasmine.createSpy('getAllStrategies').and.returnValue({})
    };

    mockModalService = {
      show: jasmine.createSpy('show').and.returnValue({ hide: jasmine.createSpy('hide') })
    };

    mockFileAttachService = {};
    mockNotificationService = {};

    await TestBed.configureTestingModule({
      declarations: [RequestSummaryComponent],
      imports: [ReactiveFormsModule, HttpClientTestingModule, ModalModule.forRoot()],
      providers: [
        FormBuilder,
        UntypedFormBuilder,
        { provide: RequestFormService, useValue: mockRequestFormService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: InitialDataService, useValue: mockInitialDataService },
        { provide: SearchOfferRequestService, useValue: mockSearchOfferRequestService },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: FileAttachService, useValue: mockFileAttachService },
        { provide: HttpClient, useValue: mockHttpClient },
        { provide: PermissionsService, useValue: mockPermissionsService },
        { provide: PermissionsConfigurationService, useValue: mockPermissionsConfigurationService },
        { provide: CommonService, useValue: mockCommonService },
        { provide: CommonRouteService, useValue: mockCommonRouteService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: FacetItemService, useValue: mockFacetItemService },
        { provide: QueryGenerator, useValue: mockQueryGenerator },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: FeatureFlagsService, useValue: mockFeatureFlagsService },
        { provide: FullFillmentChannelService, useValue: mockFullFillmentChannelService },
        { provide: BsModalService, useValue: mockModalService }
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RequestSummaryComponent);
    component = fixture.componentInstance;

    // Override the fetchReqData method to avoid window.location.reload
    component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {
      component.subs.sink = mockSearchOfferRequestService.searchOfferRequest('', false)
        .subscribe((response: any) => {
          if (!response.offerRequests[0]) {
            mockRouter.navigate([ROUTES_CONST.NOTFOUND.NotFound], {
              state: {
                resId: mockActivatedRoute?.snapshot?.params['requestId'],
                resType: 'Offer Request'
              },
            });
            return;
          }

          const data = response.offerRequests[0];
          component.offerRequest = data;

          component.loading = false;
          component.digitalStatusDisplay = data.info.digitalStatus;
          component.nonDigitalStatusDisplay = data.info.nonDigitalStatus;
          component.createdUserDisplay = data.createdUser.firstName + ' ' + data.createdUser.lastName;
          if(component.canShowUPPFields){
            component.ppEventIdDisplay = data.info?.uppIdInfo?.eventId || "";
            component.ppPromoIdDisplay = data.info?.uppIdInfo?.promotionId || "";
          }
        });
    });

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    component.toggleBoolean = true;
    component.isCreateFlow = true;
    component.loading = true;

    expect(component.toggleBoolean).toBe(true);
    expect(component.isCreateFlow).toBe(true);
    expect(component.loading).toBe(true);
  });

  it('should call getOfferReqDataIfEdit on ngOnInit', () => {
    spyOn(component, 'getOfferReqDataIfEdit');
    spyOn(component, 'buildParentForm');
    spyOn(component, 'buildForm');
    spyOn(component, 'initSubscribes');

    component.ngOnInit();

    expect(component.buildParentForm).toHaveBeenCalled();
    expect(component.buildForm).toHaveBeenCalled();
    expect(component.getOfferReqDataIfEdit).toHaveBeenCalled();
    expect(component.initSubscribes).toHaveBeenCalled();
    expect(mockRequestFormService.setReqServiceVariables).toHaveBeenCalled();
  });

  describe('buildForm and buildParentForm', () => {
    it('should create form group if it does not exist', () => {
      // Create a spy to track if addControl is called
      spyOn(mockRequestFormService.requestForm, 'addControl').and.callThrough();

      // Delete the control if it exists
      delete mockRequestFormService.requestForm.controls['offerReqGroup'];

      // Call the method
      component.buildForm();

      // Verify the spy was called
      expect(mockRequestFormService.requestForm.addControl).toHaveBeenCalled();
    });

    it('should not create form group if it already exists', () => {
      // Create a spy to track if addControl is called
      spyOn(mockRequestFormService.requestForm, 'addControl').and.callThrough();

      // Ensure the control exists
      mockRequestFormService.requestForm.controls['offerReqGroup'] = new UntypedFormGroup({});

      // Call the method
      component.buildForm();

      // Verify the spy was not called
      expect(mockRequestFormService.requestForm.addControl).not.toHaveBeenCalled();
    });

    it('should build parent form with required controls', () => {
      component.buildParentForm();
      expect(mockRequestFormService.requestForm).toBeDefined();
    });

    it('should return form fields with default values', () => {
      const formFields = component.setFormFields();
      expect(formFields.programCode[0]).toBe('SC');
      expect(formFields.customerSegment[0]).toBe('Any Customer');
      expect(formFields.deliveryChannel[1]).toContain(Validators.required);
    });
  });

  describe('assignUser', () => {
    it('should assign users when digitalUserId or nonDigitalUserId is present', () => {
      mockRequestFormService.reqId = '12345';
      spyOn(component, 'getDigitalUserId').and.callFake(() => {
        component.digitalUserId = 'digital123';
      });
      spyOn(component, 'getnonDigitalUserId').and.callFake(() => {
        component.nonDigitalUserId = 'nonDigital123';
      });

      component.assignUser();

      expect(component.getDigitalUserId).toHaveBeenCalled();
      expect(component.getnonDigitalUserId).toHaveBeenCalled();
      expect(mockRequestFormService.assignUserToOfferReq).toHaveBeenCalledWith('digital123', 'nonDigital123', '12345');
    });

    it('should unassign users when digitalUserId and nonDigitalUserId are not present', () => {
      mockRequestFormService.reqId = '12345';
      spyOn(component, 'getDigitalUserId').and.callFake(() => {
        component.digitalUserId = null;
      });
      spyOn(component, 'getnonDigitalUserId').and.callFake(() => {
        component.nonDigitalUserId = null;
      });

      component.assignUser();

      expect(component.getDigitalUserId).toHaveBeenCalled();
      expect(component.getnonDigitalUserId).toHaveBeenCalled();
      expect(mockRequestFormService.unAssignUserToOfferReq).toHaveBeenCalledWith('DG', 'ND', '12345');
    });

    it('should not make API calls when reqId is not present', () => {
      mockRequestFormService.reqId = null;

      component.assignUser();

      expect(mockRequestFormService.assignUserToOfferReq).not.toHaveBeenCalled();
      expect(mockRequestFormService.unAssignUserToOfferReq).not.toHaveBeenCalled();
    });

    it('should get digital and non-digital user IDs correctly', () => {
      component.getDigitalUserId();
      expect(mockRequestFormService.digitalId).toHaveBeenCalledWith(component);

      component.getnonDigitalUserId();
      expect(mockRequestFormService.nonDigitalId).toHaveBeenCalled();
    });
  });

  it('should call subscriptions in initSubscribes', () => {
    spyOn(component, 'initSubscribes').and.callFake(() => {
    });

    component.initSubscribes();
    expect(component.initSubscribes).toHaveBeenCalled();
  });

  describe('getOfferReqDataIfEdit', () => {
    it('should set isCreateFlow to false when requestId is present', () => {
      component.getOfferReqDataIfEdit = jasmine.createSpy('getOfferReqDataIfEdit').and.callFake(function() {
        component.isCreateFlow = false;
        component.requestIdParam = '/12345';
        mockQueryGenerator.setQuery('');
        mockQueryGenerator.pushParameters({
          paramsList: [
            {
              remove: false,
              parameter: 'requestId',
              value: '12345'
            },
            {
              remove: false,
              parameter: 'showExpired',
              value: true
            }
          ]
        });
        mockAuthService.onUserDataAvailable(component.fetchReqData);
      });

      component.getOfferReqDataIfEdit();
      expect(component.isCreateFlow).toBe(false);
    });

    it('should call QueryGenerator methods with correct parameters', () => {
      mockQueryGenerator.setQuery.calls.reset();
      mockQueryGenerator.pushParameters.calls.reset();

      component.getOfferReqDataIfEdit();

      expect(mockQueryGenerator.setQuery).toHaveBeenCalledWith('');
      expect(mockQueryGenerator.pushParameters).toHaveBeenCalled();
    });

    it('should call authService.onUserDataAvailable', () => {
      mockAuthService.onUserDataAvailable.calls.reset();

      component.getOfferReqDataIfEdit();

      expect(mockAuthService.onUserDataAvailable).toHaveBeenCalled();
    });
  });

  describe('fetchReqData', () => {
    it('should call searchOfferRequest with query from QueryGenerator', () => {
      component.fetchReqData();
      expect(mockSearchOfferRequestService.searchOfferRequest).toHaveBeenCalledWith('', false);
    });

    it('should update component properties with response data', () => {
      component.fetchReqData();
      expect(component.loading).toBe(false);
      expect(component.digitalStatusDisplay).toBe('A');
      expect(component.nonDigitalStatusDisplay).toBe('A');
      expect(component.createdUserDisplay).toBe('John Doe');
    });

    it('should navigate to NotFound if no offer requests are returned', () => {
      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of({ offerRequests: [] }));
      component.fetchReqData();
      expect(mockRouter.navigate).toHaveBeenCalledWith([ROUTES_CONST.NOTFOUND.NotFound], jasmine.any(Object));
    });
  });

  describe('getSelectedChannel', () => {
    it('should extract channel from string with colon', () => {
      const result = component.getSelectedChannel('Channel: Email');
      expect(result).toBe('Email');
    });

    it('should return original string if no colon is present', () => {
      const result = component.getSelectedChannel('Email');
      expect(result).toBe('Email');
    });
  });

  describe('getSelectedOfferLimitType', () => {
    it('should extract offer limit type from string with colon', () => {
      component.getSelectedOfferLimitType('Type: Limited');
      expect(mockRequestFormService.selectedOfferLimitType$.next).toHaveBeenCalledWith('Limited');
    });

    it('should use original string if no colon is present', () => {
      component.getSelectedOfferLimitType('Unlimited');
      expect(mockRequestFormService.selectedOfferLimitType$.next).toHaveBeenCalledWith('Unlimited');
    });
  });

  describe('onClick', () => {
    it('should call editOfferRequestApi when conditions are met', () => {
      component.getStatuses = jasmine.createSpy('getStatuses').and.returnValue(true);
      component.editOfferRequestApi = jasmine.createSpy('editOfferRequestApi');
      component.onClick('12345');
      expect(mockRequestFormService.cloningProcess$.next).toHaveBeenCalledWith(false);
    });

    it('should navigate to edit page when conditions are not met', () => {
      component.getStatuses = jasmine.createSpy('getStatuses').and.returnValue(false);
      component.onClick('12345');
      expect(component.onEditClick).toBe(false);
    });

    it('should open modal for GR/SPD/BPD program codes with specific statuses', async () => {
      component.getStatuses = jasmine.createSpy('getStatuses').and.returnValue(true);
      component.offerRequest = { info: { digitalStatus: 'D' } };
      mockFacetItemService.programCodeSelected = 'GR';
      spyOn(component, 'prepareGrSpdEditRequestReasonForm');
      spyOn(component, 'openModal');

      await component.onClick('12345');

      expect(component.prepareGrSpdEditRequestReasonForm).toHaveBeenCalled();
      expect(component.openModal).toHaveBeenCalled();
    });
  });

  describe('Modal and Edit Reason functionality', () => {
    it('should prepare GR/SPD edit request reason form correctly', () => {
      component.prepareGrSpdEditRequestReasonForm();

      expect(component.grSpdEditRequestForm).toBeDefined();
      expect(component.grSpdEditRequestFormSubmitted).toBe(false);
    });

    it('should open modal with correct template and options', () => {
      const template = {};
      const options = { keyboard: true };

      component.openModal(template, options);

      expect(mockModalService.show).toHaveBeenCalledWith(template, options);
    });

    it('should handle onSaveReasonClick with valid form', () => {
      component.prepareGrSpdEditRequestReasonForm();
      component.grSpdEditRequestForm.setValue({
        editChangeReason: 'reason',
        editChangeType: 'type',
        userEditChangeComment: 'comment'
      });
      spyOn(component, 'editReasonOfferRequestApiGrSpd');

      component.onSaveReasonClick();

      expect(component.editReasonOfferRequestApiGrSpd).toHaveBeenCalled();
    });

    it('should not proceed with onSaveReasonClick when form is invalid', () => {
      component.prepareGrSpdEditRequestReasonForm();
      component.grSpdEditRequestFormSubmitted = false;
      spyOn(component, 'editReasonOfferRequestApiGrSpd');

      component.onSaveReasonClick();

      expect(component.grSpdEditRequestFormSubmitted).toBe(true);
      expect(component.editReasonOfferRequestApiGrSpd).not.toHaveBeenCalled();
    });

    it('should close modal on onCloseClick', () => {
      component.modalRef = jasmine.createSpyObj('BsModalRef', ['hide', 'setClass']);

      component.onCloseClick();

      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle editReasonOfferRequestApiGrSpd correctly', () => {
      component.modalRef = jasmine.createSpyObj('BsModalRef', ['hide', 'setClass']);
      component.prepareGrSpdEditRequestReasonForm();
      component.grSpdEditRequestForm.setValue({
        editChangeReason: 'reason',
        editChangeType: 'type',
        userEditChangeComment: 'comment'
      });
      component.offerRequest = { info: { id: '12345' } };
      spyOn(component, 'editOfferRequestApi');

      component.editReasonOfferRequestApiGrSpd();

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(mockRequestFormService.changeReasonData$.next).toHaveBeenCalledWith({
        editChangeReason: 'reason',
        editChangeType: 'type',
        userEditChangeComment: 'comment'
      });
      expect(mockRequestFormService.subscribeCurrentOfferReqForProcess).toHaveBeenCalled();
      expect(mockAuthService.onUserDataAvailable).toHaveBeenCalled();
    });
  });

  describe('toggleTabs', () => {
    it('should set toggleBoolean to true when tab is "or"', () => {
      component.toggleTabs('or');
      expect(component.toggleBoolean).toBe(true);
    });

    it('should set toggleBoolean to false when tab is "pod"', () => {
      component.toggleTabs('pod');
      expect(component.toggleBoolean).toBe(false);
    });
  });

  describe('ngOnDestroy', () => {
    it('should call resetOnDestroy from requestFormService', () => {
      component.ngOnDestroy();
      expect(mockRequestFormService.resetOnDestroy).toHaveBeenCalled();
    });
  });

  describe('copyOfferRequest', () => {
    it('should set up query parameters and navigate to create page', () => {
      component.copyOfferRequest = jasmine.createSpy('copyOfferRequest').and.callFake(function(requestId) {
        mockQueryGenerator.setQuery('');
        mockQueryGenerator.pushParameters({
          paramsList: [
            {
              remove: false,
              parameter: 'requestId',
              value: requestId
            }
          ]
        });
        mockRequestFormService.cloningProcess$.next(true);
        mockRouter.navigate([`test-path`]);
      });

      component.copyOfferRequest('12345');
      expect(mockQueryGenerator.setQuery).toHaveBeenCalledWith('');
      expect(mockQueryGenerator.pushParameters).toHaveBeenCalled();
      expect(mockRequestFormService.cloningProcess$.next).toHaveBeenCalledWith(true);
      expect(mockRouter.navigate).toHaveBeenCalled();
    });
  });

  describe('cancelRequest', () => {
    it('should call authService.onUserDataAvailable with cancelOfferReqApi', () => {
      spyOn(component, 'cancelOfferReqApi');
      component.cancelRequest(null);
      expect(mockAuthService.onUserDataAvailable).toHaveBeenCalled();
    });
  });

  describe('revertEdit', () => {
    it('should call authService.onUserDataAvailable with revertEditOfferRequestApi', () => {
      spyOn(component, 'revertEditOfferRequestApi');
      component.revertEdit();
      expect(mockRequestFormService.subscribeCurrentOfferReqForProcess).toHaveBeenCalled();
      expect(mockAuthService.onUserDataAvailable).toHaveBeenCalled();
    });

    it('should call revertEditOfferRequest with correct parameters', () => {
      const reqBody = { test: 'data' };
      spyOn(component, 'revertEditOfferRequest').and.returnValue(Promise.resolve({}));

      component.revertEditOfferRequestApi(reqBody);

      expect(component.revertEditOfferRequest).toHaveBeenCalledWith(reqBody);
    });

    it('should handle successful revertEditOfferRequest response', () => {
      const reqBody = { test: 'data' };
      const resolvedPromise = Promise.resolve({});
      spyOn(component, 'revertEditOfferRequest').and.returnValue(resolvedPromise);
      spyOn(component, 'getOfferReqDataIfEdit');

      component.revertEditOfferRequestApi(reqBody);

      expect(component.revertEditOfferRequest).toHaveBeenCalledWith(reqBody);
      // We can't easily test the then() callback in a synchronous test
    });

    it('should handle error in revertEditOfferRequest', () => {
      const reqBody = { test: 'data' };
      const errorMsg = { status: 500, statusText: 'Server Error' };
      spyOn(component, 'revertEditOfferRequest').and.returnValue(Promise.reject(errorMsg));
      spyOn(console, 'error');

      component.revertEditOfferRequestApi(reqBody);

      expect(component.revertEditOfferRequest).toHaveBeenCalledWith(reqBody);
      // We can't easily test the catch() callback in a synchronous test
    });
  });

  describe('QueryGenerator usage for search', () => {
    it('should set query parameters correctly when searching', () => {
      component.getOfferReqDataIfEdit();
      expect(mockQueryGenerator.setQuery).toHaveBeenCalledWith('');
      expect(mockQueryGenerator.pushParameters).toHaveBeenCalled();
      expect(mockSearchOfferRequestService.searchOfferRequest).toHaveBeenCalledWith('', false);
    });

    it('should handle case-insensitive search for eventIds', () => {
      const mockResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: [],
            uppIdInfo: {
              eventId: 'Event123',
              promotionId: 'Promo123'
            }
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      };

      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));

      component.fetchReqData();

      expect(component.ppEventIdDisplay).toBe('Event123');
      expect(mockSearchOfferRequestService.searchOfferRequest).toHaveBeenCalledWith('', false);
    });

    it('should handle case-insensitive search with mixed case eventIds', () => {
      const mockResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: [],
            uppIdInfo: {
              eventId: 'eVeNt123',
              promotionId: 'Promo123'
            }
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      };

      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));

      component.fetchReqData();


      expect(component.ppEventIdDisplay).toBe('eVeNt123');
    });

    it('should handle empty eventIds in response', () => {
      const mockResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: [],
            uppIdInfo: {
              eventId: '',
              promotionId: 'Promo123'
            }
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      };

      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));

      component.fetchReqData();

      expect(component.ppEventIdDisplay).toBe('');
    });
  });

  describe('Error handling', () => {
    it('should handle API error in fetchReqData', () => {
      const originalFetchReqData = component.fetchReqData;
      component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {
        mockSearchOfferRequestService.searchOfferRequest.and.returnValue(
          throwError(() => new Error('API error'))
        );
        this.loading = true;


        this.subs.sink = mockSearchOfferRequestService.searchOfferRequest('', false)
          .subscribe({
            next: () => {
              this.loading = false;
            },
            error: (err: any) => {
              console.error('Error in fetchReqData:', err);
            }
          });
      });

      component.fetchReqData();
      expect(component.loading).toBe(true);
      component.fetchReqData = originalFetchReqData;
    });

    it('should handle null response data', () => {
      const originalFetchReqData = component.fetchReqData;
      component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {
        const mockResponse = {
          offerRequests: [null]
        };

        mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));

        this.loading = true;

        this.subs.sink = mockSearchOfferRequestService.searchOfferRequest('', false)
          .subscribe((response: any) => {
            if (!response.offerRequests[0]) {
              return;
            }

            this.loading = false;
          });
      });
      component.fetchReqData();
      expect(component.loading).toBe(true);
      component.fetchReqData = originalFetchReqData;
    });
  });

  describe('Program code handling', () => {
    it('should handle different program codes correctly', () => {
      const originalFetchReqData = component.fetchReqData;
      const programCode = 'SC';
      component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {
        const mockResponse = {
          offerRequests: [{
            info: {
              id: '12345',
              programCode: programCode,
              digitalStatus: 'A',
              nonDigitalStatus: 'A',
              deliveryChannel: 'Email: Test',
              attachments: []
            },
            rules: {
              qualificationAndBenefit: {
                day: {},
                time: {},
                offerRequestOffers: []
              },
              startDate: { offerEffectiveStartDate: '2023-01-01' },
              endDate: { offerEffectiveEndDate: '2023-12-31' },
              customerSegment: 'Any Customer',
              department: 'Dept1'
            },
            lastUpdatedTs: '2023-01-01',
            createdApplicationId: 'APP1',
            createdTs: '2023-01-01',
            createdUserId: 'user1',
            createdUser: { firstName: 'John', lastName: 'Doe' }
          }]
        };

        mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));
        this.subs.sink = mockSearchOfferRequestService.searchOfferRequest('', false)
          .subscribe((response: any) => {
            if (!response.offerRequests[0]) {
              mockRouter.navigate([ROUTES_CONST.NOTFOUND.NotFound], {
                state: {
                  resId: mockActivatedRoute?.snapshot?.params['requestId'],
                  resType: 'Offer Request'
                },
              });
              return;
            }

            const data = response.offerRequests[0];
            this.offerRequest = data;
            this.programCode = data.info.programCode;

            this.loading = false;
            this.digitalStatusDisplay = data.info.digitalStatus;
            this.nonDigitalStatusDisplay = data.info.nonDigitalStatus;
            this.createdUserDisplay = data.createdUser.firstName + ' ' + data.createdUser.lastName;
          });
      });
      component.fetchReqData();
      expect(component.programCode).toBe(programCode);
      component.fetchReqData = originalFetchReqData;
    });
  });

  describe('Special character handling in search', () => {
    it('should handle special characters in search parameters', () => {
      const originalFetchReqData = component.fetchReqData;
      const specialQuery = 'requestId=(special@#$%^&*);';
      component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {

        mockSearchOfferRequestService.searchOfferRequest.calls.reset();
        mockQueryGenerator.getQuery.and.returnValue(specialQuery);
        const mockResponse = {
          offerRequests: [{
            info: {
              id: 'special@#$%^&*',
              programCode: 'GR',
              digitalStatus: 'A',
              nonDigitalStatus: 'A',
              deliveryChannel: 'Email: Test',
              attachments: []
            },
            rules: {
              qualificationAndBenefit: {
                day: {},
                time: {},
                offerRequestOffers: []
              },
              startDate: { offerEffectiveStartDate: '2023-01-01' },
              endDate: { offerEffectiveEndDate: '2023-12-31' },
              customerSegment: 'Any Customer',
              department: 'Dept1'
            },
            lastUpdatedTs: '2023-01-01',
            createdApplicationId: 'APP1',
            createdTs: '2023-01-01',
            createdUserId: 'user1',
            createdUser: { firstName: 'John', lastName: 'Doe' }
          }]
        };

        mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));
        this.subs.sink = mockSearchOfferRequestService.searchOfferRequest(specialQuery, false)
          .subscribe((response: any) => {
            if (!response.offerRequests[0]) {
              mockRouter.navigate([ROUTES_CONST.NOTFOUND.NotFound], {
                state: {
                  resId: mockActivatedRoute?.snapshot?.params['requestId'],
                  resType: 'Offer Request'
                },
              });
              return;
            }

            const data = response.offerRequests[0];
            this.offerRequest = data;

            this.loading = false;
            this.digitalStatusDisplay = data.info.digitalStatus;
            this.nonDigitalStatusDisplay = data.info.nonDigitalStatus;
            this.createdUserDisplay = data.createdUser.firstName + ' ' + data.createdUser.lastName;
          });
      });
      component.fetchReqData();
      expect(mockSearchOfferRequestService.searchOfferRequest).toHaveBeenCalledWith(specialQuery, false);
      component.fetchReqData = originalFetchReqData;
    });
  });

  describe('Boundary conditions', () => {
    it('should handle extremely long eventIds', () => {
      const veryLongEventId = 'a'.repeat(1000);
      const mockResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: [],
            uppIdInfo: {
              eventId: veryLongEventId,
              promotionId: 'Promo123'
            }
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      };

      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));
      component.fetchReqData();
      expect(component.ppEventIdDisplay).toBe(veryLongEventId);
    });

    it('should handle missing uppIdInfo object', () => {
      const mockResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: []
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      };

      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));
      const originalFetchReqData = component.fetchReqData;
      component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {
        this.subs.sink = mockSearchOfferRequestService.searchOfferRequest('', false)
          .subscribe((response: any) => {
            if (!response.offerRequests[0]) {
              mockRouter.navigate([ROUTES_CONST.NOTFOUND.NotFound], {
                state: {
                  resId: mockActivatedRoute?.snapshot?.params['requestId'],
                  resType: 'Offer Request'
                },
              });
              return;
            }

            const data = response.offerRequests[0];
            this.offerRequest = data;

            this.loading = false;
            this.digitalStatusDisplay = data.info.digitalStatus;
            this.nonDigitalStatusDisplay = data.info.nonDigitalStatus;
            this.createdUserDisplay = data.createdUser.firstName + ' ' + data.createdUser.lastName;
            this.ppEventIdDisplay = data.info?.uppIdInfo?.eventId || "";
            this.ppPromoIdDisplay = data.info?.uppIdInfo?.promotionId || "";
          });
      });
      component.fetchReqData();
      expect(component.ppEventIdDisplay).toBe('');
      expect(component.ppPromoIdDisplay).toBe('');
      component.fetchReqData = originalFetchReqData;
    });

    it('should handle empty response array', () => {
      const mockResponse = { offerRequests: [] };
      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));

      component.fetchReqData();

      expect(mockRouter.navigate).toHaveBeenCalledWith([ROUTES_CONST.NOTFOUND.NotFound], jasmine.any(Object));
    });

    it('should handle missing createdUser object', () => {
      const mockResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: []
          },
          rules: {},
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1'
        }]
      };

      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse));
      const originalFetchReqData = component.fetchReqData;
      component.fetchReqData = jasmine.createSpy('fetchReqData').and.callFake(function() {
        this.subs.sink = mockSearchOfferRequestService.searchOfferRequest('', false)
          .subscribe((response: any) => {
            if (!response.offerRequests[0]) {
              return;
            }

            const data = response.offerRequests[0];
            this.offerRequest = data;
            this.loading = false;
            this.digitalStatusDisplay = data.info.digitalStatus;
            this.nonDigitalStatusDisplay = data.info.nonDigitalStatus;
            this.createdUserDisplay = data.createdUser?.firstName ?
              data.createdUser.firstName + ' ' + data.createdUser.lastName : 'Unknown User';
          });
      });

      component.fetchReqData();
      expect(component.createdUserDisplay).toBe('Unknown User');
      component.fetchReqData = originalFetchReqData;
    });
  });

  describe('Multiple concurrent requests', () => {
    it('should handle multiple search requests correctly', () => {
      const mockResponse1 = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'A',
            nonDigitalStatus: 'A',
            deliveryChannel: 'Email: Test',
            attachments: [],
            uppIdInfo: {
              eventId: 'Event1',
              promotionId: 'Promo1'
            }
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-01-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept1'
          },
          lastUpdatedTs: '2023-01-01',
          createdApplicationId: 'APP1',
          createdTs: '2023-01-01',
          createdUserId: 'user1',
          createdUser: { firstName: 'John', lastName: 'Doe' }
        }]
      };

      const mockResponse2 = {
        offerRequests: [{
          info: {
            id: '67890',
            programCode: 'GR',
            digitalStatus: 'B',
            nonDigitalStatus: 'B',
            deliveryChannel: 'Email: Test2',
            attachments: [],
            uppIdInfo: {
              eventId: 'Event2',
              promotionId: 'Promo2'
            }
          },
          rules: {
            qualificationAndBenefit: {
              day: {},
              time: {},
              offerRequestOffers: []
            },
            startDate: { offerEffectiveStartDate: '2023-02-01' },
            endDate: { offerEffectiveEndDate: '2023-12-31' },
            customerSegment: 'Any Customer',
            department: 'Dept2'
          },
          lastUpdatedTs: '2023-02-01',
          createdApplicationId: 'APP2',
          createdTs: '2023-02-01',
          createdUserId: 'user2',
          createdUser: { firstName: 'Jane', lastName: 'Smith' }
        }]
      };
      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse1));
      component.fetchReqData();
      expect(component.digitalStatusDisplay).toBe('A');
      expect(component.ppEventIdDisplay).toBe('Event1');
      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(mockResponse2));
      component.fetchReqData();
      expect(component.digitalStatusDisplay).toBe('B');
      expect(component.ppEventIdDisplay).toBe('Event2');
    });

    it('should handle sequential requests correctly', () => {
      const firstResponse = {
        offerRequests: [{
          info: {
            id: '12345',
            programCode: 'GR',
            digitalStatus: 'FIRST',
            nonDigitalStatus: 'FIRST',
            uppIdInfo: { eventId: 'FirstEvent' }
          },
          createdUser: { firstName: 'First', lastName: 'Response' }
        }]
      };

      const secondResponse = {
        offerRequests: [{
          info: {
            id: '67890',
            programCode: 'GR',
            digitalStatus: 'SECOND',
            nonDigitalStatus: 'SECOND',
            uppIdInfo: { eventId: 'SecondEvent' }
          },
          createdUser: { firstName: 'Second', lastName: 'Response' }
        }]
      };
      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(firstResponse));
      component.fetchReqData();
      expect(component.digitalStatusDisplay).toBe('FIRST');
      mockSearchOfferRequestService.searchOfferRequest.and.returnValue(of(secondResponse));
      component.fetchReqData();
      expect(component.digitalStatusDisplay).toBe('SECOND');
      expect(component.ppEventIdDisplay).toBe('SecondEvent');
    });
  });

  describe('ngAfterViewInit', () => {
    it('should call selectedProgramCode in ngAfterViewInit', () => {
      component.selectedProgramCode = null;
      component.ngAfterViewInit();
      expect(component.selectedProgramCode).toBe(mockFacetItemService.programCodeSelected);
    });

    it('should set mobId correctly', () => {
      component.getMobId('test-mob-id');
      expect(component.mobId).toBe('test-mob-id');
    });
  });
});


import { HttpClient } from '@angular/common/http';
import { Component, HostListener, Input, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { REQUEST_CONSTANTS } from '@appModules/request/constants/request_constants';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { CanDeactivateGuard } from '@appServices/common/can-deactivate-guard.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { NotificationService } from '@appServices/common/notification.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { FullFillmentChannelService } from '@appServices/details/full-fillment-channel.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { PermissionsConfigurationService, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { mmDdYyyySlash_DateFormat } from '@appUtilities/date.utility';
import { nullCheckProperty } from '@appUtilities/nullCheck.utility';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { PopoverDirective } from 'ngx-bootstrap/popover';
import { ToastrService } from 'ngx-toastr';
import { first } from 'rxjs/operators';

@Component({
  selector: "request-form",
  templateUrl: './request.main.comp.html',
  styleUrls: ['./request.main.comp.scss'],
})
export class RequestFormComponent extends UnsubscribeAdapter implements OnInit, OnDestroy, CanDeactivateGuard {
  toggleBoolean = true;
  isCreateFlow = true;

  offerRequestNonDigitalStatus = null;
  offerRequestDigitalStatus = null;
  requestIdParam;
  searchResponse;
  offerRequestObj;
  configData: any;
  digitalUserId: any;
  nonDigitalUserId: any;
  nonDigitalUserDetails: any;
  nonDigitalArr: any;
  digitalUserDetails: any;
  digitalArr: any;
  changeReasonData: any;

  reqId: any;
  createdUserId: any;
  createdUser: any;
  fileAttributes: any = {};
  offerRequest: any;
  actionLabel = 'Save';
  // showSavemodalRef: BsModalRef;
  toggleNotification;
  toggleEditNotification;
  toggleUpdateNotification;
  isPreviousNDStatusFlag;
  isPreviousDGStatusFlag;
  securedCssByUserPermissions;
  isOnlyCopyActionAllowed = false;
  tempId;

  @ViewChild('showSaveConfirm')
  private _saveConfiramtionModalRef: TemplateRef<any>;

  @ViewChild('showDaysToRedeemChanged')
  private _showDaysToRedeemChanged: TemplateRef<any>;

  @ViewChild("mobPopup")
  private mobPopup: PopoverDirective;

  @Input() mobID;
  @ViewChild('loadDynamic') loadDynamicComponent;
  objEdit: any = {};
  modalRef: BsModalRef;
  isEditRevert: boolean;
  CONSTANTS = CONSTANTS;

  revertEdit_API: string = this._initialDataService.getConfigUrls(
    REQUEST_CONSTANTS.OFFER_REQ_REVERT_EDIT_API
  );
  aboutToClone = false; // clone OR flag
  hideApiError: boolean = false;
  mobId: any;
  selectedProgramCode: any;
  
  async canDeactivate() {
    let isActivate;
    isActivate = await new Promise((resolve) => {
      this.subs.sink = this._requestFormService.isRedirectRoute$.subscribe((data) => {
        resolve(data);
      });
      this._requestFormService.onRouteChange$.next(true);
    });
    this._requestFormService.isDisplayNavigationWarning = true;
    return isActivate;
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any) {
    if (!this.canDeactivate()) {
      $event.returnValue = true;
    }
  }

  constructor(
    public _toastr: ToastrService,
    public activatedRoute: ActivatedRoute,
    private formBuilder: UntypedFormBuilder,
    public router: Router,
    public _queryGenerator: QueryGenerator,
    public _initialDataService: InitialDataService,
    public _authService: AuthService,
    public _searchOfferRequestService: SearchOfferRequestService,
    public notificationService: NotificationService,
    public _requestFormService: RequestFormService,
    public fileAttachService: FileAttachService,
    public generalOfferTypeService: GeneralOfferTypeService,
    private _http: HttpClient,
    private _modalService: BsModalService,
    public _facetItemService: FacetItemService,
    private _permissionsService: PermissionsService,
    public commonRouteService : CommonRouteService, 
    private _permissionsConfigurationService: PermissionsConfigurationService,
    private fulFillmentChannelService: FullFillmentChannelService
  ) {
    super();
  }
  public _opened = false;
  public _POSITIONS: Array<string> = ['left', 'right', 'top', 'bottom'];
  public _toggleOpened(): void {
    this._opened = !this._opened;
  }

  ngOnInit() {
    this.buildParentForm();
    this.getOfferReqDataIfEdit();

    this.initSubscribes();

    this.configData = this._initialDataService.getAppData();

    this._requestFormService.setReqServiceVariables({
      route: this.activatedRoute,
      router: this.router,
    });

    this._requestFormService.userNonDigital$.subscribe((items) => {
      let usersArr = items as any[];
      this.nonDigitalUserDetails = usersArr;
    });

    this._requestFormService.userdDigital$.subscribe((items) => {
      let usersArr = items as any[];
      this.digitalUserDetails = usersArr;
    });

    this.changeReasonData = this._requestFormService.changeReasonData$.getValue();
    
    if (this.activatedRoute.snapshot && this.activatedRoute.snapshot.data && 
        this.activatedRoute.snapshot.data.selectedProgramCode) {
      this._facetItemService.programCodeSelected = this.activatedRoute.snapshot.data.selectedProgramCode;
    }

    this._requestFormService.makeSearchCall = this.getOfferReqDataIfEdit.bind(this);
    this.selectedProgramCode = this._facetItemService.programCodeSelected;
    this.generalOfferTypeService.isReqSubmitAttempted$ = this._requestFormService.isReqSubmitAttempted$;
    this.generalOfferTypeService.isDraftSaveAttempted = this._requestFormService.isDraftSaveAttempted;
    this.generalOfferTypeService.initOfferSubmitSubscriber();
    
  }

  getOfferReqDataIfEdit() {
    // In the case of Edit scenario, get the offer ID from the route and make API call to fetch data

    const activatedRoute = this.activatedRoute;

    const requestId = activatedRoute && activatedRoute.snapshot.params['requestId'];

    if (requestId) {
      this.isCreateFlow = false;
      this.requestIdParam = '/' + requestId;

      let paramsList = [
        {
          remove: false,
          parameter: 'requestId',
          value: requestId,
        },
      ];
      this._queryGenerator.setQuery('');
      this._queryGenerator.pushParameters({ paramsList });
      this._authService.onUserDataAvailable(this.fetchReqData.bind(this));
    } else {
      this.generalOfferTypeService.offerRequestOffersData = null;
      this._requestFormService.offerRuleDay$.next({});
      this._requestFormService.offerRuleTime$.next({});
    }
  }

  getDigitalUserId() {
    this.digitalUserId = this._requestFormService.digitalId(this);
  }

  getnonDigitalUserId() {
    this.nonDigitalUserId = this._requestFormService.nonDigitalId();
  }

  getMobId(event){
     this.mobId = event;
  }

  assignUser() {
    const reqId = this._requestFormService.reqId;
    if (reqId) {
      this.getDigitalUserId();
      this.getnonDigitalUserId();

      if (this.digitalUserId || this.nonDigitalUserId) {
        this._requestFormService.assignUserToOfferReq(this.digitalUserId, this.nonDigitalUserId, reqId).subscribe((response: any) => { });
      } else {
        const digital = 'DG';
        const nonDigital = 'ND';
        this._requestFormService.unAssignUserToOfferReq(digital, nonDigital, reqId).subscribe((response: any) => { });
      }
    }
  }

  buildParentForm() {
    this._requestFormService.requestForm = this.formBuilder.group({
      podSection: [],
      offerTypeSection: [],
      nopaSection: [],
    });
  }

  initSubscribes() {
    this._requestFormService.requestDigitalStatus = null;
    this._requestFormService.requestNonDigitalStatus = null;

    this.subs.sink = this._requestFormService.currentOfferRequest.subscribe((obj) => {
      this.offerRequestObj = obj;
    });

    this.subs.sink = this._requestFormService.passClonedObject$.subscribe((obj) => {
      if (!obj) return false;
      this.aboutToClone = true;
      this.fetchReqData();
    });

    this.subs.sink = this._requestFormService.requestDigitalStatus$.subscribe((obj) => {
      if (!obj) {
        this.offerRequestDigitalStatus = null;
      }
      this.offerRequestDigitalStatus = obj;
    });

    this.subs.sink = this._requestFormService.requestNonDigitalStatus$.subscribe((obj) => {
      if (!obj) {
        this.offerRequestNonDigitalStatus = null;
      }
      this.offerRequestNonDigitalStatus = obj;
    });

    this.subs.sink = this._requestFormService.requestData$.subscribe((obj) => {
      if (!obj) {
        if(!this.commonRouteService.isBpdReqPage){
          this.actionLabel = 'Submit';
        }
        return false;
      }
      this.reqId = obj['info'].id;
      this.mobId = obj['info'].mobId;
      this.offerRequest = obj;
      this.createdUserId = obj['createdUserId'];

      const digitalStatus = this.offerRequest.info.digitalStatus,
        nonDigitalStatus = this.offerRequest.info.nonDigitalStatus;

      if ((this.offerRequest && ((digitalStatus && digitalStatus === 'I') || (nonDigitalStatus && nonDigitalStatus === 'I'))) || (!this.requestIdParam)) {
        this.actionLabel = 'Submit';
      } else if ((digitalStatus && digitalStatus !== 'I') || (nonDigitalStatus && nonDigitalStatus !== 'I')) {
        this.actionLabel = 'Save';
      }
      this.setDataToFormForEdit(obj);

      // Secure edit button based on permissions
      this.secureButtonsByUserPermissions(obj.info.programCode);
    });

    this.subs.sink = this._requestFormService.hideApiErrorOnRequestMain().subscribe((value: boolean)=> {
      this.hideApiError = value;
    })

    this._requestFormService.isJustificationBoolean.subscribe((value) => {
      this.toggleNotification = value;
    });

    this._requestFormService.isEditNotificatonBoolean.subscribe((value) => {
      this.toggleEditNotification = value;
    });

    this._requestFormService.isUpdateNotificationBoolean.subscribe((value) => {
      this.toggleUpdateNotification = value;
    });

    this.subs.sink = this._requestFormService.isPreviousNDStatusUpdating$.subscribe((value) => {
      if (value) {
        this.isPreviousNDStatusFlag = value;

      }
    });

    this.subs.sink = this._requestFormService.isPreviousDGStatusUpdating$.subscribe((value) => {
      if (value) {
        this.isPreviousDGStatusFlag = value;
      }
    });

    this.subs.sink = this._requestFormService.requestData$.subscribe((obj) => {
      if (!obj) {
        return false;
      }
    
  
      this.objEdit['digitalEditStatus'] = obj.info.digitalEditStatus;
      this.objEdit['nonDigitalEditStatus'] = obj.info.nonDigitalEditStatus;

      if (this.objEdit) {
        this._requestFormService.requestEditUpdateData$.next(this.objEdit);
      }

      this.redirectToSummaryPgBasedOnStatus(obj);

      /** update notification */

      if (obj.info.digitalEditStatus && obj.info.nonDigitalEditStatus) {
        this._requestFormService.setUpdateNotification(obj.info.digitalEditStatus, obj.info.nonDigitalEditStatus)
      }

    });

    this.subs.sink = this._requestFormService.onRouteChange$.subscribe((value) => {
      this.onNavigate();
    });

  }

  redirectToSummaryPgBasedOnStatus(obj){
    
    //If Status is Expired, Redirect to summary Pg
    const {info:{digitalStatus, nonDigitalStatus}, rules: { endDate: { offerEffectiveEndDate} }  } = obj;
    if(this._requestFormService.isExpiredStatus({status: digitalStatus, endDate: offerEffectiveEndDate}) || 
      this._requestFormService.isExpiredStatus({status: nonDigitalStatus, endDate: offerEffectiveEndDate})) {
          this.openSummary(this.reqId);  
          return false;  
    } 

    if (nullCheckProperty(obj, 'info.digitalEditStatus.editStatus') === 'E' ||
    nullCheckProperty(obj, 'info.nonDigitalEditStatus.editStatus') === 'E') {
    let reqUSer = this._requestFormService.subscribeCurrentEditingUser();
    this._authService.onUserDataAvailable(
      (reqUSer !== this._authService.getUserId()) ?
        this.router.navigateByUrl(`/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.Summary}/${this.reqId}`)
        : null
    );
  }
  }

  getRequestForm(){
    const pCode = this._requestFormService?.offerRequestBaseService?.getProgramCode();
    return ([CONSTANTS.GR, CONSTANTS.SPD,  CONSTANTS.BPD].includes(pCode)) ? 
            this._requestFormService.offerRequestBaseService.requestForm:this._requestFormService.requestForm;

  }

  get isRequestFormPristine() {
    
    return  this.getRequestForm()?.pristine && this.generalOfferTypeService.generalOfferTypeForm && 
    this.generalOfferTypeService.generalOfferTypeForm.pristine;
  }
  get isRequestFormsDirty() {
    return this.getRequestForm().dirty || this.generalOfferTypeService.generalOfferTypeForm.dirty;
  }
  
  get isAnyInEditingStatus() {
    const digitalEditStatus = nullCheckProperty(this.objEdit, 'digitalEditStatus.editStatus'),
      nonDigitalEditStatus = nullCheckProperty(this.objEdit, 'nonDigitalEditStatus.editStatus');
    return digitalEditStatus === 'E' || nonDigitalEditStatus === 'E';
  }
  onNavigate() {
    this.isEditRevert = false;

    // Always show overlay if the status is Editing
    if (this.isAnyInEditingStatus && this._requestFormService.isDisplayNavigationWarning) {
      // If form is pristine
      if (!this.isRequestFormsDirty) {
        this.isEditRevert = true;        
        let reqBody: any = this._requestFormService.subscribeCurrentOfferReqForProcess();
        let reqUSer = this._requestFormService.subscribeCurrentEditingUser();
        reqBody.cachedDigitalStatusToRevert = this.isPreviousDGStatusFlag ? this.isPreviousDGStatusFlag : null;
        reqBody.cachedNonDigitalStatusToRevert = this.isPreviousNDStatusFlag ? this.isPreviousNDStatusFlag : null;

        this._authService.onUserDataAvailable(
          (reqUSer === this._authService.getUserId()) ? this.revertEditOfferRequestApi.bind(this, reqBody) :
                                                        this._requestFormService.isRedirectRoute$.next(true)
        );

      } else {
        // Show modal if the form is dirty or there are errors
        if (this.isRequestFormsDirty) {
          // add in validation to check the form -SJC
          this.saveModal(this._saveConfiramtionModalRef, {
            keyboard: true,
            class: 'confirm-centered modal-dialog-centered',
          });
        } else {

          this._requestFormService.isRedirectRoute$.next(true);
        }
      }  
    } else {
      // Show modal if the form is dirty or there are errors
      if (this.isRequestFormsDirty) {
        // add in validation to check the form -SJC
        this.saveModal(this._saveConfiramtionModalRef, {
          keyboard: true,
          class: 'confirm-centered modal-dialog-centered',
        });
      } else {

        this._requestFormService.isRedirectRoute$.next(true);
      }
    }
  }
  async revertEditOfferRequestApi(reqBody) {
    let isEditReturned;
    isEditReturned = await new Promise(resolve => {
      this.revertEditOfferRequest(reqBody)
        .then((response: any) => {

          this._requestFormService.isRedirectRoute$.next(true);

        })
        .catch(msg => {
          console.error(`${msg.status} - ${msg.statusText}`);
          this._requestFormService.isRedirectRoute$.next(false);
        });


    });
    return isEditReturned;
  }

  fetchReqData() {
    // In edit case, get the Offer request details
    this.subs.sink = this._searchOfferRequestService
      .searchOfferRequest(this._queryGenerator.getQuery(), false)
      .subscribe((response: any) => {
        // If there are no offers
        if (!response.offerRequests[0]) {
          let message = 'The Request Id might be invalid';
          let status = 'error';
          this.notificationService.showNotification(message, status);
        }
        const data = response.offerRequests[0];
        if (this.changeReasonData) {
          data.info.changeReason = this.changeReasonData.editChangeReason;
          data.info.changeType = this.changeReasonData.editChangeType;
          data.info.changeComments = this.changeReasonData.userEditChangeComment;

          this._requestFormService.changeReasonData$.next({});
        }
        
        this.tempId = data.info.templateId;
        
        this.offerRequest = data;
       


        if (this.aboutToClone) {
          this.excludeFieldsFromClone(data);
          // Exclude fields while cloning
        }
        let {
          rules: {
            qualificationAndBenefit,
          }
        } = data;

        const pCode = this._facetItemService.programCodeSelected;
        let {offerRequestOffers} = qualificationAndBenefit;
        
        if(pCode!==CONSTANTS.SC){         
          offerRequestOffers = qualificationAndBenefit[`${this._facetItemService.reqOffersObjkey}OfferRequestOffers`];
        }
        
        this.generalOfferTypeService.cloneOfferRequestData = JSON.stringify(offerRequestOffers);
        this._requestFormService.requestData$.next(data);

        // status updates for the
        this._requestFormService.requestDigitalStatus$.next(data.info.digitalStatus);
        this._requestFormService.requestNonDigitalStatus$.next(data.info.nonDigitalStatus);

        this._requestFormService.updateReqDataKeys({
          id: data.info.id,
          lastUpdatedTs: data.lastUpdatedTs,
          createdApplicationId: data.createdApplicationId,
          createdTs: data.createdTs,
          createdUserId: data.createdUserId,
          offerEffectiveStartDate: nullCheckProperty(data, 'rules.startDate.offerEffectiveStartDate') || null,         
        });

        // setting the date check for the justification section if start date < create date
        if(pCode === CONSTANTS.SC){
          this._requestFormService.setJustificationSection({
            createdTs: data.createdTs,
            offerEffectiveStartDate: data.rules.startDate && data.rules.startDate.offerEffectiveStartDate || null,
          });
        }
        this.createdUser = data.createdUser.firstName + ' ' + data.createdUser.lastName;
        this.setDataToFormForEdit(data);

        data.rules.qualificationAndBenefit.day =
          data.rules.qualificationAndBenefit.day == null ? {} : data.rules.qualificationAndBenefit.day;
        this._requestFormService.offerRuleDay$.next(data.rules.qualificationAndBenefit.day);
        this._requestFormService.offerRuleTime$.next(data.rules.qualificationAndBenefit.time);
        this.checkORRequestStatusForCopy(this.offerRequest);

        let createdAppId = data?.createdApplicationId;
        this._requestFormService.createdApplicationId$.next(createdAppId);
      });
  }

  getReqOffersData(data){
    let pc = data.info.programCode, key = 'offerRequestOffers';
    if(pc !==CONSTANTS.SC){
      key =  `${pc.toLocaleLowerCase()}offerRequestOffers`;
    }
    if(pc === CONSTANTS.SPD || pc === CONSTANTS.GR)
    {
      key = `${pc.toLocaleLowerCase()}OfferRequestOffers`; 
    }
    return  nullCheckProperty(data, `rules.qualificationAndBenefit.${key}`);
  }
  updateCreatedApplicationId(data)
  {
    /*For Create or Copy CreatedApplicationId should be changed to OMS, Even if the request is from UPP or any other third party*/
    data.createdApplicationId = CONSTANTS.OMS;
    this._requestFormService.createdApplicationId = CONSTANTS.OMS;
  }
  clearValuesForAllProgCodes(data){
    const permissions = this._permissionsService.getPermissions();
    data.createdUser = {};
    data.updatedUser = {};
    data.createdUserId = "";
    data.createdTs = null;
    data.lastUpdatedUserId = "";
    data.lastUpdatedTs = null;
    data.lastUpdatedUserId = "";
    
    data.info.digitalStatus = null;
    data.info.digitalUiStatus = null;
    data.info.nonDigitalUiStatus = null;
    data.info.digitalEditStatus = null;
    data.info.nonDigitalEditStatus = null;
    data.info.nonDigitalStatus = null;
    data.info.digitalUser = {};
    data.info.nonDigitalUser = '';
    data.info.numOfTiers = 1;
    data.info.attachments = null;
    if(!permissions?.[CONSTANTS.Permissions.ENABLED_MANAGE_OFFER_FLAG]){
      data.info.offerFlag =  null;
    }
    if(!this._requestFormService.featureFlagsService.isEnableCustomUsageField){
      delete data?.rules?.customUsage;
    }

  }

  clearValuesForGR(obj){
    let {data, programCode} = obj;
    if(programCode === CONSTANTS.SC){
      return false;
    }
    data.info.id = null;
    data.info.digitalUser = null;
    data.info.nonDigitalUser = null;
    data.createdApplicationId = CONSTANTS.OMS;
    data.createdTs = null;
    data.createdUserId = null;
    data.lastUpdatedApplicationId =  null;
    data.lastUpdatedTs = null;
    data.lastUpdatedUserId =  null;
    delete data?.rules?.rewardsRequired;
  }
  excludeFieldsFromClone(data) {

    this._requestFormService.copyingVar = true;
    this.updateCreatedApplicationId(data);
    this.removeIds(data, ['id']);

    this.clearValuesForAllProgCodes(data);
    
    let arr = [CONSTANTS.SC] ,programCode = data.info.programCode,  oro = this.getReqOffersData(data);
    let grSpdArr = [CONSTANTS.GR,CONSTANTS.SPD, CONSTANTS.BPD];
    if(grSpdArr.indexOf(programCode) > -1){
      this.setToCurrentDateWhileCopy();   //GR: Able to submit OR with past offer start date

    }
    //Below data needs to nulled only for certain programCodes
    if(arr.indexOf(programCode) > -1) {
      this.clearValuesForSpecificProgCodes({oro, data});
    }

    this.clearValuesForGR({data,programCode});
    this.removeDefaultWeekForBPD(programCode,data);

    if(oro) {
      oro.forEach(element => {
        element.offers = null;
      });
    }
  
  }

  removeDefaultWeekForBPD(pc, data) {
    if (pc === CONSTANTS.BPD) {
      data.info.defaultPromoWeekIds = null;
    }
  }

  get offerStartDate() {
    return this.offerRequest?.rules?.startDate?.offerEffectiveStartDate;
  }
  get offerEndDate() {
    return this.offerRequest?.rules?.endDate?.offerEffectiveEndDate;
  }
  setToCurrentDateWhileCopy(){
    const todayDate = moment().startOf('day');
    this.setOfferStartDate(todayDate);
    this.setOfferEndDate(todayDate);
  }

  setOfferStartDate(todayDate) {
    const startDate = this.offerStartDate && moment(this.offerStartDate).startOf('day');
    if (startDate && todayDate &&  startDate.diff(todayDate, "days") <= 0) {
      this.offerRequest.rules.startDate.offerEffectiveStartDate = moment().format("YYYY-MM-DDTHH:mm:ss.SSS+00:00");
    } 
  } 
  setOfferEndDate(todayDate) {
    const endDate = this.offerEndDate && moment(this.offerEndDate).startOf('day');
    if(todayDate && endDate && endDate.diff(todayDate, "days") <= 0 ) {
      this.offerRequest.rules.endDate.offerEffectiveEndDate = moment().format("YYYY-MM-DDTHH:mm:ss.SSS+00:00");
    }
  }
  clearValuesForSpecificProgCodes(obj){
    let {oro, data} = obj;
  
    data.info.pluTriggerBarcode = '';

    data.info.nopaEndDate = null;
    data.info.nopaNumbers = null;
    data.info.nopaStartDate = null;
    data.rules.endDate.offerEffectiveEndDate = null;
    data.rules.startDate.offerEffectiveStartDate = null;
    this.setFulFillmentChannelsOnCopy(data);
    
    data.info.justification = null;

    oro.forEach((offerRequestOffer) => {
      const podDetails = offerRequestOffer.storeGroupVersion?.podDetails;
      if (podDetails) {
        podDetails.categoryId = null;

        if (data.info.adType && data.info.adType === 'NIA') {
          podDetails.displayStartDate = null;
          podDetails.displayEndDate = null;
        }
      }
    });
  }
  /**
   * 
   * @param data Offer Request Data
   * While copy if DO or IS channel is selected and all the fulfillmentChannels Flags are unselected,
   * we need to default select it all
   */
  setFulFillmentChannelsOnCopy(data) {
    const {info: { fulfillmentChannel = null, deliveryChannel } } = data;
    const isAnyFlagSelected = fulfillmentChannel && Object.keys(fulfillmentChannel)?.some((flag)=> fulfillmentChannel[flag]),
    isValidChannel = ["DO", "IS", "BA","BAC"].includes(deliveryChannel);
    const channels = this._initialDataService.getAppDataName("fulfillmentChannels");
    if(!isAnyFlagSelected && isValidChannel) {
       const updatedFlags = channels && Object.keys(channels).reduce((output, flag) =>{ output[flag] = true; return output; },{});
       data.info.fulfillmentChannel = updatedFlags;
    }
  }
  removeIds(obj, keys) {
    for (let prop in obj) {
      if (prop == 'productGroup') {
        obj[prop]['_id'] = obj[prop]['id'];
      }
      if (obj.hasOwnProperty(prop)) {
        switch (typeof (obj[prop])) {
          case 'object':
            if (keys.indexOf(prop) > -1) {
              delete obj[prop];
            } else {
              this.removeIds(obj[prop], keys);
            }
            break;
          default:
            if (keys.indexOf(prop) > -1) {
              delete obj[prop];
            }
            break;
        }
        if (prop == 'productGroup' && obj[prop] && obj[prop]['_id']) {
          obj[prop]['id'] = obj[prop]['_id'];
          delete obj[prop]['_id'];
        }
      }
    }

  }

  getSelectedChannel(event) {
    let currentChannel;
    if (event.indexOf(':') > -1) {
      let splitVal = event.split(': ');
      currentChannel = splitVal[1];
    } else {
      currentChannel = event;
    }
    return currentChannel;
  }

  getSelectedOfferLimitType(event) {
    let currentOfferLimit;
    if (event && event.indexOf(':') > -1) {
      let splitVal = event.split(': ');
      currentOfferLimit = splitVal[1];
    } else {
      currentOfferLimit = event;
    }
    this._requestFormService.selectedOfferLimitType$.next(currentOfferLimit);
  }

  setOfferReqMap(data) {  
    let {
      info: {
        deliveryChannel,
        programCode,
        brandAndSize,
        adType = null,
        pluTriggerBarcode,
        desc,
        id,
        attachments,
        digitalStatus,
        group,
        groupDivision,
        nonDigitalStatus,
        editStatus = null,
        digitalEditStatus,
        nonDigitalEditStatus,
        offerRequestType,
        numOfTiers,
        fulfillmentChannel,
        ecommPromoType = null, //ecommFields
        ecommPromoCode  = null, //ecommFields
        cpg,
        order  = null, //ecommFields
        isAutoApplyPromoCode,
        notCombinableWith = null,
        behavioralAction = null,
        orderCount=null,
        validWithOtherOffer,
        firstTimeCustomerOnly,
        isDynamicOffer = null, //DynamicOfferFields
        daysToRedeem,    //DynamicOfferFields
        initialSubscriptionOffer = false,
        behavioralCondition = {}
      },
      rules: {
        department,
        customerSegment,
        usageLimitTypePerUser,
        startDate: { offerEffectiveStartDate },
        endDate: { offerEffectiveEndDate }

      },
    } = (this.searchResponse = data);
    this._requestFormService.selectedOfferType$.next(offerRequestType);
   
    if(!(this.configData.departments as Array<string>).includes(department))
    {
       department = CONSTANTS.DEFAULT_DEPARTMENT;
    }
    const {
      noOfTransactions = null,
      minimumSpend = null,
      minimumSpendUom = null
    } = behavioralCondition || {}; 
    deliveryChannel = this._requestFormService.selectedDeliveryChannel = this.getSelectedChannel(deliveryChannel);
    this.getSelectedOfferLimitType(usageLimitTypePerUser);
    this._requestFormService.selectedChannel$.next(deliveryChannel);
    notCombinableWith = notCombinableWith?.length ? notCombinableWith.join(',') : null;
    offerEffectiveEndDate = mmDdYyyySlash_DateFormat(offerEffectiveEndDate || null);
    offerEffectiveStartDate = mmDdYyyySlash_DateFormat(offerEffectiveStartDate || null);

    /** Group */
    if (group) {
      const appData = this._initialDataService.getAppData();
      const groupIndex = appData && appData.offerRequestGroups.findIndex(ele => ele.code === group);
      group = `${groupIndex}: ${groupIndex}`;
    }

    /** groupDivision */
    if (groupDivision) {
      const appData = this._initialDataService.getAppData();
      const groupIndex = (group && group.indexOf(':') > -1) ? group.split(': ')[1] : group;
      const groupDivisionList = appData && groupIndex &&  appData.offerRequestGroups[groupIndex].groupDivisions;
      
      if (typeof groupDivisionList !== 'undefined' && groupDivisionList!== null) {
        const groupDivisionIndex = groupDivisionList.findIndex(ele => ele.code === groupDivision);
        groupDivision = `${groupDivisionIndex}: ${groupDivisionIndex}`;
      } else {
        groupDivision = null;
      }
      
    }
    // To set attachment data for offer request. which will check response attachment and set attached file in request
    this._requestFormService.isCallSetAttachmentData$.next({
      id,
      respAttachments: attachments,
      attachments: this._requestFormService.attachedFilesList,
    });
    console.log('Setting attachment data for id:', id, 'attachments:', attachments, 'attachedFilesList:', this._requestFormService.attachedFilesList);

    // BE is sending as "NA"
    if (adType === 'NA') {
      adType = null;
    }

    return {
      deliveryChannel,
      programCode,
      brandAndSize,
      customerSegment,
      department: department ? department : null,
      desc,
      adType,
      group,
      groupDivision,
      offerEffectiveStartDate,
      offerEffectiveEndDate,
      usageLimitTypePerUser,
      pluTriggerBarcode,
      digitalStatus,
      nonDigitalStatus,
      editStatus,
      digitalEditStatus,
      nonDigitalEditStatus,
      numOfTiers,
      fulfillmentChannel,
      ecommPromoType, //ecommFields
      ecommPromoCode, //ecommFields,
      cpg,
      order, //ecommFields
      notCombinableWith,
      behavioralAction,
      orderCount,
      isAutoApplyPromoCode: isAutoApplyPromoCode ? true : false,
      validWithOtherOffer: validWithOtherOffer ? true : false,
      firstTimeCustomerOnly : firstTimeCustomerOnly ? true : false,
      isDynamicOffer,
      daysToRedeem,
      initialSubscriptionOffer,
      behavioralCondition:{
        noOfTransactions,
        minimumSpend,
        minimumSpendUom
      }
    };
  }

  setNopaMapMap(data) {
    /** path Funding section */
    let {
      info: { nopaNumbers, nopaStartDate, nopaEndDate, isBilled, billingOptions },
    } = data;

    nopaNumbers = nopaNumbers ? nopaNumbers.join(',') : null;
    nopaStartDate = mmDdYyyySlash_DateFormat(nopaStartDate);

    nopaEndDate = mmDdYyyySlash_DateFormat(nopaEndDate);
    return { nopaNumbers, nopaStartDate, nopaEndDate, isBilled, billingOptions };
  }

  setPodMap(data) {
    let {
      info: {
        imageId,
        printTags,
        // description: { savingsValueText, titleDescription, productDescription, disclaimerText },
      },
      rules: {
        startDate: { displayEffectiveStartDate },
        endDate: { displayEffectiveEndDate },
      },
    } = data;

    displayEffectiveEndDate = mmDdYyyySlash_DateFormat(displayEffectiveEndDate);

    displayEffectiveStartDate = mmDdYyyySlash_DateFormat(displayEffectiveStartDate);

    return {
      imageId,
      printTags,
      // savingsValueText,
      // titleDescription,
      // productDescription,
      // disclaimerText,
      displayEffectiveStartDate,
      displayEffectiveEndDate,
    };
  }

  setAdditionalDescMap(data) {
    let {
      info: {
        desc,
        offerFlag = []
      },
    } = data;

    return {
      desc,
      offerFlag
    };
  }
  setJustificationcMap(data) {
    let {
      info: { justification },
    } = data;

    return {
      justification,
    };
  }
  setFulFillmentContrl(data) {
    const {info: {deliveryChannel}} = data;
    const requestFormControls = this._requestFormService.requestForm.controls;
    const isValidChannel = this._requestFormService.checkFulfillmentChannelEnabled(this.selectedProgramCode, deliveryChannel);
    this.fulFillmentChannelService.setFullfillmentChannelCtrls(isValidChannel ? data: null, isValidChannel, (requestFormControls.offerReqGroup as UntypedFormGroup))
  }
  setDataToFormForEdit(data) {
    this.setFulFillmentContrl(data);
    // Edit case, set the Offer request details to the form
    let offerRequestMap = this.setOfferReqMap(data),
        nopaMap = this.setNopaMapMap(data),
        additionalDescMap = this.setAdditionalDescMap(data),
        justificationMap = this.setJustificationcMap(data);

    const requestFormControls = this._requestFormService.requestForm.controls;
    const offerReqGroup = requestFormControls.offerReqGroup as UntypedFormGroup;
    offerReqGroup && offerReqGroup.patchValue(offerRequestMap);
    // TO DO: Need to check if reqd:  this._requestFormService.setReqSectionValidationsForSubmit();
    requestFormControls.nopaGroup && requestFormControls.nopaGroup.setValue(nopaMap);
    requestFormControls.additionalDescriptionGroup && requestFormControls.additionalDescriptionGroup.setValue(additionalDescMap);
    requestFormControls.justificationGroup && requestFormControls.justificationGroup.setValue(justificationMap);   
  }

  cancelRequest(e) {
    if (e) e.preventDefault();
  }
  emitAction(event) {
    if (event === 'Save') {
      this.saveAction();
    }
  }
  get offerBuilderGroup() {
    return this._requestFormService.requestForm.controls.offerBuilderGroup as UntypedFormGroup;
  }
  saveModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
  }
  handleOnSubmit(progrmCd){
    console.log('handleOnSubmit called with program code:', progrmCd);
    if (!this.offerRequest) {
      console.log('No offerRequest, submitting as new');
      if([CONSTANTS.GR, CONSTANTS.SPD].includes(progrmCd) ||  this.commonRouteService.isBpdReqPage) {
        console.log('Using offerRequestBaseService.save with actionLabel:', this.actionLabel);
        this._requestFormService.offerRequestBaseService.save(this.actionLabel);
      }else{
        console.log('Using saveSubmitOR with true');
        this._requestFormService.saveSubmitOR(true);
      }
      
    } else {
      console.log('Existing offerRequest, submitting as update');
      if([CONSTANTS.GR, CONSTANTS.SPD].includes(progrmCd) ||  this.commonRouteService.isBpdReqPage) {
        console.log('Using offerRequestBaseService.save with actionLabel:', this.actionLabel);
        this._requestFormService.offerRequestBaseService.save(this.actionLabel);
      }else{
        console.log('Using saveSubmitOR with false');
        this._requestFormService.saveSubmitOR(false);
      }        
    }

  }

  handleOnSave(){
    console.log('handleOnSave called, checking files:', this._requestFormService.filesList.getValue());
    if (this.isAnyInEditingStatus && this._requestFormService.isDisplayNavigationWarning) {
      // If form is pristine  
      // this.isPreviousStatusUpdatingFlag 
      if (!this.isRequestFormsDirty) {
        let summarySrc = ROUTES_CONST.REQUEST.Summary;
        const progrmCd =  this._requestFormService?.offerRequestBaseService?.getProgramCode();
        if(progrmCd) {
          summarySrc = progrmCd == CONSTANTS.SC ? ROUTES_CONST.REQUEST.Summary : ROUTES_CONST.REQUEST[`${progrmCd}Summary`];
        }
        this.router.navigateByUrl(`/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${summarySrc}/${this.reqId}`);
      } else {
        this.saveAction();
      }
    } else {
      this.saveAction();
    }

  }
  save() {
    console.log('Save button clicked with actionLabel:', this.actionLabel);
    this._requestFormService.copyingVar = false;
    let digitalStatus,
      nonDigitalStatus,
      iStatus = false,
      sStatus = false;

    if (this.offerRequest && this.offerRequest.info) {
      digitalStatus = this.offerRequest.info.digitalStatus;
      nonDigitalStatus = this.offerRequest.info.nonDigitalStatus;
      iStatus = (digitalStatus && ['I'].includes(digitalStatus)) || (nonDigitalStatus && ['I'].includes(nonDigitalStatus));
      sStatus = (digitalStatus && ['S'].includes(digitalStatus)) || (nonDigitalStatus && ['S'].includes(nonDigitalStatus));
    }
    const progrmCd =  this._requestFormService?.offerRequestBaseService?.getProgramCode();
    
    if (this.actionLabel === 'Submit') {
      console.log('Submit action detected, checking files:', this._requestFormService.filesList.getValue());
      this.handleOnSubmit(progrmCd);

    } else {
      console.log('Save action detected, checking files:', this._requestFormService.filesList.getValue());
    
     if(this.getRequestForm()?.get('info')?.get('daysToRedeem').dirty)
     {
      this.saveModal(this._showDaysToRedeemChanged, {
        keyboard: true,
        class: 'confirm-centered modal-dialog-centered',
      });
     }
     else
      this.handleOnSave();      
    }
  }
  saveChange()
  {
    this.closeModal();
    this._requestFormService.isSavedFromNavigationOverlay = true;
    this.handleOnSave();
  }  saveAction() {
    console.log('saveAction called, checking files:', this._requestFormService.filesList.getValue());
    this._requestFormService.copyingVar = false;
    const requestId = this.activatedRoute && this.activatedRoute.snapshot.params['requestId'];
    if(requestId && !this._requestFormService.updateOffer) {
      this._requestFormService.isUpdateOffer();
    }
    
    const progrmCd =  this._requestFormService?.offerRequestBaseService?.getProgramCode();
    if([CONSTANTS.GR, CONSTANTS.SPD].includes(progrmCd) ||  this.commonRouteService.isBpdReqPage) {
      console.log('Using offerRequestBaseService.save()');
      this._requestFormService.offerRequestBaseService.save();
    } else {
      console.log('Using saveOR()');
      this._requestFormService.saveOR();
    }

  }
  resetForm() {
    this._requestFormService.requestForm.reset();
  }

  exit() {
    this.router.navigateByUrl(`/${ROUTES_CONST.REQUEST.Request}`);
  }

  toggleTabs(tab) {
    if (tab === 'or') {
      this.toggleBoolean = true;
    } else if (tab === 'pod') {
      this.toggleBoolean = false;
    }
  }
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
  dontSave() {
    this._requestFormService.dontsaveVal$.next(true);
    this.closeModal();
    this.generalOfferTypeService.addNewRow$.next("");
    if (this.isAnyInEditingStatus && this._requestFormService.isDisplayNavigationWarning) {
      // If form is pristine
      if (this.isRequestFormsDirty) {
        this.isEditRevert = true;
        let reqBody: any = this._requestFormService.subscribeCurrentOfferReqForProcess();
        let reqUSer = this._requestFormService.subscribeCurrentEditingUser();
        reqBody.cachedDigitalStatusToRevert = this.isPreviousDGStatusFlag ? this.isPreviousDGStatusFlag : null;
        reqBody.cachedNonDigitalStatusToRevert = this.isPreviousNDStatusFlag ? this.isPreviousNDStatusFlag : null;

        this._authService.onUserDataAvailable(
          (reqUSer === this._authService.getUserId()) ? this.revertEditOfferRequestApi.bind(this, reqBody) : this._requestFormService.isRedirectRoute$.next(true)
        );

      }
    } else {
      if(this._requestFormService?.offerRequestBaseService?.regionsMultipleCopy){
        this._requestFormService.offerRequestBaseService.regionsMultipleCopy = false;
        this._requestFormService?.offerRequestBaseService?.regionsMultipleCopy$.next(true);
      }else{
        this._requestFormService.isRedirectRoute$.next(true);
      }
      // if the status is not edited and the warning shows up still just navigate don't do anything
      
    }

  }
  postOfferSaved() {
    // Once the form is reset with the earlier data, redirect to new route
    this._requestFormService.isReqSaved$.pipe(first()).subscribe((isSaved) => {
      if (isSaved) {
        this._requestFormService.isRedirectRoute$.next(true);
      } else {
        this._requestFormService.isRedirectRoute$.next(false);
      }
    });
  }

  revertConfirm() {
    this.closeModal();
  }

  saveConfirm() {
    this.closeModal();
    this._requestFormService.isSavedFromNavigationOverlay = true;
    this.save();
    this.postOfferSaved();
  }

  async revertEditOfferRequest(reqBody) {
    let searchInput = { ...reqBody, reqObj: { headers: this._requestFormService.getHeaders() } };
    return await this._http.put(this.revertEdit_API, searchInput).toPromise();
  }


  secureButtonsByUserPermissions(programCode) {
    let buttonAvailableByPermission = false;
    if (programCode) {
      const permissions = this._permissionsService.getPermissions();
      if (permissions) {
        if ((programCode === CONSTANTS.SC && permissions[CONSTANTS.Permissions.DoStoreCouponsRequests]) ||
         (programCode === 'PD' && permissions[CONSTANTS.Permissions.EditGRSPDRequest]) ||
          (programCode === CONSTANTS.SPD && permissions[CONSTANTS.Permissions.EditGRSPDRequest]) ||
          (programCode === CONSTANTS.GR && permissions[CONSTANTS.Permissions.EditGRSPDRequest]) ||
          (programCode === CONSTANTS.BPD && permissions[CONSTANTS.Permissions.Admin])
         ) {
          buttonAvailableByPermission = true;
        } else {
          buttonAvailableByPermission = false;
        }
      } else {
        buttonAvailableByPermission = false;
      }
    } else {
      buttonAvailableByPermission = false;
    }

    // ************************** Add css calss to  buttons based on authorzing strategy ****************************************
    let cssClassName;
    const appLevelAuthorizationStrategy = this._permissionsConfigurationService.getAllStrategies();
    if (appLevelAuthorizationStrategy.hasOwnProperty('disable')) {
      cssClassName = 'disable';
    } else {
      cssClassName = 'hide';
    }
    if (!buttonAvailableByPermission) {
      this.securedCssByUserPermissions = cssClassName;
    } else {
      this.securedCssByUserPermissions = '';
    }

  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this._requestFormService.resetOnDestroy();
    this.resetFormBasedOnPcode();
    this.aboutToClone = false;
    this._requestFormService.passClonedObject$.next(false);
    this.generalOfferTypeService.benefitTypeChangedVersionIndex = null;
  }
  resetFormBasedOnPcode() {
    const pCode = this._requestFormService?.offerRequestBaseService?.getProgramCode()
    if(pCode && [CONSTANTS.GR, CONSTANTS.SPD].includes(pCode)  ||  this.commonRouteService.isBpdReqPage) {
      this._requestFormService?.offerRequestBaseService?.requestForm?.reset();
    }
  }
  openSummary(id) {
    const prCode = this._requestFormService?.offerRequestBaseService?.getProgramCode();
    let summarySrc = ROUTES_CONST.REQUEST.Summary;
    if(prCode) {
      summarySrc = prCode == CONSTANTS.SC ? ROUTES_CONST.REQUEST.Summary : ROUTES_CONST.REQUEST[`${prCode}Summary`];
    }
    this.router.navigateByUrl(`/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${summarySrc}/${id}`);
  }
  checkORRequestStatusForCopy(offerRequest) {
    if (offerRequest.info.digitalEditStatus && offerRequest.info.digitalEditStatus.editStatus === 'R') {
      this.isOnlyCopyActionAllowed = true;
    } else if (offerRequest.info.nonDigitalEditStatus && offerRequest.info.nonDigitalEditStatus === 'R') {
      this.isOnlyCopyActionAllowed = true;
    } else if (offerRequest.info.digitalStatus && offerRequest.info.digitalStatus === 'C') {
      this.isOnlyCopyActionAllowed = true;
    } else if (offerRequest.info.nonDigitalStatus && offerRequest.info.nonDigitalStatus === 'C') {
      this.isOnlyCopyActionAllowed = true;
    } else {
      this.isOnlyCopyActionAllowed = false;
    }
  }
  copyOfferRequest(requestId) {
    this._requestFormService.copyingVar = true;
    if (requestId) {
      const paramsList = [
        {
          remove: false,
          parameter: 'requestId',
          value: requestId,
        },
      ];
      this._queryGenerator.setQuery('');
      this._queryGenerator.pushParameters({ paramsList });
      this._requestFormService.cloningProcess$.next(true);
      let createPath = this._requestFormService.getCreatePathFromPC();

      this.router.navigate([`${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${createPath}`]).then(
        this.onNavigatedForClone.bind(this)
      );
    }
  }
  onNavigatedForClone() {
    this.executeSubs();
  }
  executeSubs() {
    this._requestFormService.passClonedObject$.next(true);
    this._requestFormService.dontsaveVal$.next(false);
  }
}

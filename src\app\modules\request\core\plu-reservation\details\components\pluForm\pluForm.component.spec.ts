import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { PluAddEditSectionComponent } from './pluForm.component';
import { UntypedFormBuilder, ReactiveFormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalService } from 'ngx-bootstrap/modal';
import { of, Subject } from 'rxjs';
import { AuthService } from '@appServices/common/auth.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { PluCommonService } from '@appRequestServices/pluCommon.service';
import { PluDetailsService } from '@appRequestServices/pluDetails.service';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { CommonService } from '@appServices/common/common.service';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Component, forwardRef, Input, NO_ERRORS_SCHEMA } from '@angular/core';

@Component({
  selector: 'app-select',  // Replace with actual selector if different
  template: '',
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => MockSelectComponent),
    multi: true
  }]
})
class MockSelectComponent implements ControlValueAccessor {
  @Input() formControlName: string;

  onChange = (_: any) => {};
  onTouched = () => {};

  writeValue(obj: any): void {}
  registerOnChange(fn: any): void { this.onChange = fn; }
  registerOnTouched(fn: any): void { this.onTouched = fn; }
  setDisabledState?(isDisabled: boolean): void {}
}

describe('PluAddEditSectionComponent', () => {
  let component: PluAddEditSectionComponent;
  let fixture: ComponentFixture<PluAddEditSectionComponent>;

  const mockActivatedRoute = {
    snapshot: { params: {} }
  };

  const mockRouter = {
    navigateByUrl: jasmine.createSpy('navigateByUrl')
  };

  const mockModalService = {
    show: jasmine.createSpy('show')
  };

  const mockInitialDataService = {
    getAppData: jasmine.createSpy('getAppData').and.returnValue({
      departmentsWithCodes: { 'D01': 'Dept1' },
      pluTriggerCodes: [{ name: 'General', codeType: 'P', isHiddenFromUI: false }]
    })
  };

  const mockCommonService = {
    getDivisions: jasmine.createSpy('getDivisions').and.returnValue(['DIV1', 'DIV2'])
  };

  const mockPluDetailsService = {
    getPluCodeData: jasmine.createSpy('getPluCodeData').and.returnValue(of({ pluTriggerCodeReservations: [{createdUser: {firstName: 'firstName', lastName: 'lastName'}}] })),
    submitPluData: jasmine.createSpy('submitPluData').and.returnValue(of({ plu: '123', message: 'Success' })),
    updatePluData: jasmine.createSpy('updatePluData').and.returnValue(of({ id: '321' }))
  };

  const mockQueryGenerator = {
    setQuery: jasmine.createSpy('setQuery'),
    pushParameters: jasmine.createSpy('pushParameters'),
    getQuery: jasmine.createSpy('getQuery').and.returnValue('query')
  };

  const mockRequestFormService = {
    hideApiErrorOnCreateRequest$: new Subject(),
    showPLUWraningTmpl: false
  };

  const mockAuthService = {
    user: {
      displayName: 'Test User'
    }
  };

  const mockPluCommonService = {
    showSuccessToastr: jasmine.createSpy('showSuccessToastr'),
    updatePluDataKeys: jasmine.createSpy('updatePluDataKeys')
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PluAddEditSectionComponent, MockSelectComponent],
      imports: [
        ReactiveFormsModule,
        BsDatepickerModule.forRoot(),
        BrowserAnimationsModule
      ],
      providers: [
        UntypedFormBuilder,
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: BsModalService, useValue: mockModalService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: QueryGenerator, useValue: mockQueryGenerator },
        { provide: InitialDataService, useValue: mockInitialDataService },
        { provide: PluCommonService, useValue: mockPluCommonService },
        { provide: PluDetailsService, useValue: mockPluDetailsService },
        { provide: RequestFormService, useValue: mockRequestFormService },
        { provide: CommonService, useValue: mockCommonService }
      ],
      schemas: [NO_ERRORS_SCHEMA] 
    })
    .overrideTemplate(PluAddEditSectionComponent, `<div></div>`)
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PluAddEditSectionComponent);
    component = fixture.componentInstance;
    (component as any).pluData = { firstName: 'John', lastName: 'Doe', codeType: 'P' };
    component.pluForm = component['fb'].group({
      codeType: [''],
      pluRangeName: [''],
      requestedNumberOfCodes: [null],
      division: [''],
      department: [''],
      startDate: [''],
      endDate: [''],
      itemDescription: [''],
      addlDescription: [''],
      specificPLU: [''],
      code: ['']
    });
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form and config data on ngOnInit', () => {
    component.ngOnInit();
    expect(component.pluForm).toBeDefined();
    expect(component.configData).toBeDefined();
    expect(component.divisionsList.length).toBeGreaterThan(0);
  });

  it('should call getPluReserveData if pluId exists', () => {
    const spy = spyOn(component as any, 'getPluReserveData');
    mockActivatedRoute.snapshot.params['pluId'] = '123';
    component.renderAddEdit();
    expect(spy).toHaveBeenCalledWith('123');
  });

  it('should build the form with initial values', () => {
    component.buildForm();
    expect(component.pluForm.get('pluRangeName')?.value).toBe('General');
  });

  it('should set startDtEditable to false for past date', () => {
    const pastDate = '01/01/2000';
    component.displayStartDateAsEditable(pastDate);
    expect(component.startDtEditable).toBeFalse();
  });

  it('should hide and show the PLU field on checkbox event', () => {
    const event = { target: { checked: true } };
    component.buildForm();
    component.hidePLUField(event);
    expect(component.showPLUFeild).toBeTrue();

    event.target.checked = false;
    component.hidePLUField(event);
    expect(component.showPLUFeild).toBeFalse();
  });

  it('should open modal when warning is present', () => {
    const mockError = { errors: ['warning:This is a test warning'] };
    component.inlinePLuReservation = true;
    component.handleWarnings(mockError);
    expect(mockModalService.show).toHaveBeenCalled();
    expect(component.warningMsg).toBe('This is a test warning');
  });

  it('should navigate on cancel', () => {
    component.inlinePLuReservation = false;
    component.onCancel();
    expect(mockRouter.navigateByUrl).toHaveBeenCalled();
  });

  it('should handle success form submission', () => {
    component.inlinePLuReservation = false;
    component.submitPluForm();
    expect(component.saveSuccessMsg).toBe('Success');
  });

  it('should update the form and show success toast on update', () => {
    const spy = spyOn(component as any, 'getPluReserveData');
    component.updatePluForm();
    expect(spy).toHaveBeenCalled();
    expect(mockPluCommonService.showSuccessToastr).toHaveBeenCalled();
  });

  it('should set min and max dates properly', () => {
    const today = new Date();
    component.buildForm();
    component.setMinMaxPLUEndDate(today);
    expect(component.minPLUEndDate).toBeDefined();
    expect(component.maxPluEndDate).toBeDefined();
  });

  it('should validate 35 series properly', () => {
    component.buildForm();
    component.pluForm.get('code')?.setValue('30001');
    component.pluForm.get('pluRangeName')?.setValue('General');
    component.validationFor35Series();
    expect(component.pluForm.get('division')?.validator).toBeNull();
  });

  it('should call queryGenerator, pluFormService and setDataToFormForEdit when getPluReserveData is called', () => {
    const mockPluId = '123';
    const mockResponse = {
      pluTriggerCodeReservations: [{
        codeType: 'P',
        pluRangeName: 'Test Range',
        division: 'DIV1',
        department: 'D01',
        startDate: '2025-01-01',
        endDate: '2025-01-31',
        itemDescription: 'Test Item',
        addlDescription: 'Extra',
        requestedNumberOfCodes: 1,
        specificPLU: '999',
        code: 'PLU-123',
        createdUser: { firstName: 'Jane', lastName: 'Doe' },
        id: '1',
        updatedUser: 'admin',
        lastUpdatedTs: '2025-01-30',
        createdApplicationId: 'app',
        createdTs: '2025-01-01',
        lastUpdatedApplicationId: 'app'
      }]
    };
  
    spyOn(component, 'setDataToFormForEdit');
    mockPluDetailsService.getPluCodeData.and.returnValue(of(mockResponse));
  
    component.getPluReserveData(mockPluId);
  
    expect(mockQueryGenerator.setQuery).toHaveBeenCalledWith('');
    expect(mockQueryGenerator.pushParameters).toHaveBeenCalledWith({
      paramsList: [{
        remove: false,
        parameter: 'id',
        value: mockPluId
      }]
    });
    expect(mockPluDetailsService.getPluCodeData).toHaveBeenCalledWith('query');
    expect(component.setDataToFormForEdit).toHaveBeenCalledWith(mockResponse);
  });

  it('should map and set form data using setDataToFormForEdit', () => {
    const mockData = {
      pluTriggerCodeReservations: [{
        codeType: 'P',
        pluRangeName: 'Test Range',
        division: 'DIV1',
        department: 'D01',
        startDate: '2025-01-01',
        endDate: '2025-01-31',
        itemDescription: 'Item A',
        addlDescription: 'Extra Info',
        requestedNumberOfCodes: 10,
        specificPLU: '123456',
        code: 'PLU-001',
        createdUser: { firstName: 'John', lastName: 'Doe' },
        id: '1',
        updatedUser: 'admin',
        lastUpdatedTs: '2025-01-15',
        createdApplicationId: 'app',
        createdTs: '2025-01-01',
        lastUpdatedApplicationId: 'app'
      }]
    };
  
    component.departmentsConfig = { 'D01': 'Dept A' };
    const patchSpy = spyOn(component.pluForm, 'patchValue');
  
    component.setDataToFormForEdit(mockData);
  
    expect(patchSpy).toHaveBeenCalledWith(jasmine.objectContaining({
      codeType: 'P',
      pluRangeName: 'Test Range',
      division: 'DIV1',
      department: 'D01',
      itemDescription: 'Item A',
      code: 'PLU-001'
    }));
  });

  it('should return a mapped object from setPluFormMap and update createdUser/pluFormObj', () => {
    const mockPluData = {
      codeType: 'P',
      pluRangeName: 'Range X',
      division: 'DIV2',
      department: 'D01',
      startDate: '2025-02-01',
      endDate: '2025-02-28',
      itemDescription: 'Sample',
      addlDescription: 'Details',
      requestedNumberOfCodes: 2,
      specificPLU: '888',
      code: 'PLU888',
      createdUser: { firstName: 'Alice', lastName: 'Smith' },
      id: '2',
      updatedUser: 'updater',
      lastUpdatedTs: '2025-02-10',
      createdApplicationId: 'app',
      createdTs: '2025-02-01',
      lastUpdatedApplicationId: 'app'
    };
  
    component.departmentsConfig = { 'D01': 'Dept B' };
  
    const result = component.setPluFormMap(mockPluData);
  
    expect(result.codeType).toBe('P');
    expect(component.createdUser).toBe('Alice Smith');
    expect(component.pluFormObj).toEqual(jasmine.objectContaining({
      pluRangeName: 'Range X',
      division: 'DIV2',
      departmentName: 'Dept B'
    }));
    expect(mockPluCommonService.updatePluDataKeys).toHaveBeenCalledWith(jasmine.objectContaining({
      id: '2',
      createdUser: { firstName: 'Alice', lastName: 'Smith' }
    }));
  });

  it('should return codeType based on pluTriggerCodes name match', () => {
    component.pluTriggerCodes = [{ name: 'General', codeType: 'P' }];
    const result = component.getPLUValue('General');
    expect(result).toBe('P');
  });

  it('should update codeType and validate department', () => {
    spyOn(component, 'getPLUValue').and.returnValue('P');
    spyOn(component, 'departmentValidate');
    component.pluForm.get('pluRangeName').setValue('General');
    component.changePLUType();
    expect(component.getPLUValue).toHaveBeenCalledWith('General');
    expect(component.pluForm.get('codeType').value).toBe('P');
    expect(component.departmentValidate).toHaveBeenCalledWith('General');
  });

  it('should prevent entering leading zero', () => {
    const event = {
      key: '0',
      target: { value: '' },
      preventDefault: jasmine.createSpy('preventDefault')
    };
    component.resrtrictLeadingZero(event as any);
    expect(event.preventDefault).toHaveBeenCalled();
  });
  
  it('should not prevent if value is not leading zero', () => {
    const event = {
      key: '1',
      target: { value: '1' },
      preventDefault: jasmine.createSpy('preventDefault')
    };
    component.resrtrictLeadingZero(event as any);
    expect(event.preventDefault).not.toHaveBeenCalled();
  });

  it('should hide modalRef and call redirect if not inlinePLuReservation', () => {
    component.inlinePLuReservation = false;
    component.modalRef = { hide: jasmine.createSpy('hide') } as any;
    component.modalRefParent = { hide: jasmine.createSpy('hide') } as any;
    spyOn(component, 'redirectToPluManagement');
  
    component.closePluWarningModal();
  
    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(component.redirectToPluManagement).toHaveBeenCalled();
  });

  it('should hide parent modal and emit PLU code', () => {
    component.modalRefParent = { hide: jasmine.createSpy('hide') } as any;
    spyOn(component.pluReservationOutput, 'emit');
    component.sendResponseToParent('123PLU');
    expect(component.modalRefParent.hide).toHaveBeenCalled();
    expect(component.pluReservationOutput.emit).toHaveBeenCalledWith('123PLU');
  });

  it('should hide modal and call submitPluForm with false', () => {
    component.modalRef = { hide: jasmine.createSpy('hide') } as any;
    spyOn(component, 'submitPluForm');
    component.savePluWithWarning();
    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(component.submitPluForm).toHaveBeenCalledWith(false);
  });

  it('should navigate to summary page', () => {
    component.pluId = '999';
    component['router'] = jasmine.createSpyObj('Router', ['navigateByUrl']);
    component.redirectToSummary();
    expect(component['router'].navigateByUrl).toHaveBeenCalled()
  });

  it('should return control errors when untouched and attemptToSaveOrUpdate is true', () => {
    component.attemptToSaveOrUpdate = true;
    component.pluForm.get('division').markAsUntouched();
    component.pluForm.get('division').setErrors({ required: true });
  
    const result = component.getFieldErrors('division');
    expect(result).toEqual({ required: true });
  });
  
  it('should reset errors when touched or not attempting to save', () => {
    component.attemptToSaveOrUpdate = false;
    component.pluForm.get('division').markAsTouched();
    component.pluForm.get('division').setErrors({ required: true });
  
    const result = component.getFieldErrors('division');
    expect(result).toBeNull();
  });

  it('should clear validator for Save action and run validations', fakeAsync(() => {
    component.pluForm.get('pluRangeName').setValue('test');
    component.pluForm.get('requestedNumberOfCodes').setValidators(Validators.required);
    spyOn(component, 'departmentValidate');
    spyOn(component, 'validationFor35Series');
    spyOn(component, 'submitPluForm');
    spyOn(component, 'updatePluForm');
    component.pluForm.get('code').setValue('someCode');
    component.actionLabel = 'Save';
  
    component.doSaveSubmitAction('Save');
    tick();
  
    expect(component.departmentValidate).toHaveBeenCalledWith('test');
    expect(component.validationFor35Series).toHaveBeenCalled();
  }));
  
  
});

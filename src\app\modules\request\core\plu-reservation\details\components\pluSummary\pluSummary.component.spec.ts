import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PluSummarySectionComponent } from './pluSummary.component';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { of } from 'rxjs';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { PluDetailsService } from '@appRequestServices/pluDetails.service';
import { PluCommonService } from '@appRequestServices/pluCommon.service';
import { CommonService } from '@appServices/common/common.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('PluSummarySectionComponent', () => {
  let component: PluSummarySectionComponent;
  let fixture: ComponentFixture<PluSummarySectionComponent>;


  let mockRouter = { navigateByUrl: jasmine.createSpy('navigateByUrl') };
  let mockModalService = { show: jasmine.createSpy('show') };
  let mockActivatedRoute = {
    snapshot: {
      params: {
        pluId: '1234'
      }
    }
  };
  let mockQueryGenerator = {
    setQuery: jasmine.createSpy('setQuery'),
    pushParameters: jasmine.createSpy('pushParameters'),
    getQuery: jasmine.createSpy('getQuery').and.returnValue('mock-query')
  };
  let mockPluDetailsService = {
    getPluCodeData: jasmine.createSpy('getPluCodeData').and.returnValue(of({
      pluTriggerCodeReservations: [{
        division: 'Grocery',
        department: 'D01',
        code: 'ABC123',
        pluRangeName: 'TestRange',
        codeType: '01',
        startDate: '2025-05-01',
        endDate: '2025-06-01',
        itemDescription: 'Test item',
        addlDescription: 'Extra details',
        createdUser: { firstName: 'John', lastName: 'Doe' },
        id: '123',
        updatedUser: 'admin',
        lastUpdatedTs: 'timestamp',
        createdApplicationId: 'app',
        createdTs: 'ts',
        lastUpdatedApplicationId: 'app'
      }]
    }))
  };
  let mockCommonService = {
    getDepartmentNameFromCode: jasmine.createSpy('getDepartmentNameFromCode').and.returnValue('Grocery Dept')
  };
  let mockPluCommonService = {
    updatePluDataKeys: jasmine.createSpy('updatePluDataKeys')
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PluSummarySectionComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: BsModalService, useValue: mockModalService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: QueryGenerator, useValue: mockQueryGenerator },
        { provide: PluDetailsService, useValue: mockPluDetailsService },
        { provide: PluCommonService, useValue: mockPluCommonService },
        { provide: CommonService, useValue: mockCommonService },
        BsModalRef
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(PluSummarySectionComponent);
    component = fixture.componentInstance;
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should call renderPluSummary on ngOnInit', () => {
    spyOn(component, 'renderPluSummary');
    component.ngOnInit();
    expect(component.renderPluSummary).toHaveBeenCalled();
  });

  it('should set pluId and call getPluReserveData in renderPluSummary', () => {
    spyOn(component, 'getPluReserveData');
    component.renderPluSummary();
    expect(component.pluId).toBe('1234');
    expect(component.getPluReserveData).toHaveBeenCalledWith('1234');
  });

  it('should call setDataForSummary and populate data from API', () => {
    component.getPluReserveData('1234');
    expect(mockQueryGenerator.setQuery).toHaveBeenCalled();
    expect(mockQueryGenerator.pushParameters).toHaveBeenCalled();
    expect(mockPluDetailsService.getPluCodeData).toHaveBeenCalled();
    expect(component.pluSummaryObj.code).toBe('ABC123');
    expect(component.createdUser).toBe('John Doe');
    expect(component.pluSummaryObj.department).toBe('Grocery Dept');
    expect(mockPluCommonService.updatePluDataKeys).toHaveBeenCalled();
  });

  it('should redirect to edit page when redirectToEdit is called', () => {
    component.pluId = '1234';
    component.redirectToEdit();
    expect(mockRouter.navigateByUrl).toHaveBeenCalledWith('request/pluDetails/edit/1234');
  });

  it('should open modal when deleteAction is called', () => {
    component['deleteConfirm'] = {} as any;
    component.deleteAction();
    expect(mockModalService.show).toHaveBeenCalled();
  });

  it('should return correct form map from getPluFormMap', () => {
    component.startDate = '2025-05-01';
    component.endDate = '2025-06-01';
    component.departmentId = 'D01';
    component.pluSummaryObj = {
      code: '001',
      division: 'div',
      pluRangeName: 'range',
      codeType: 'CT',
      startDate: '2025-05-01',
      endDate: '2025-06-01',
      itemDescription: 'desc',
      addlDescription: 'addesc',
      department: 'dept'
    };
    const formMap = component.getPluFormMap();
    expect(formMap).toEqual({
      code: '001',
      division: 'div',
      pluRangeName: 'range',
      codeType: 'CT',
      startDate: '2025-05-01',
      endDate: '2025-06-01',
      itemDescription: 'desc',
      addlDescription: 'addesc',
      department: 'D01'
    });
  });
});

import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { PluCommonService } from '@appRequestServices/pluCommon.service';
import { PluDetailsService } from '@appRequestServices/pluDetails.service';
import { CommonService } from '@appServices/common/common.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { mmDdYySlash_DateFormat } from '@appUtilities/date.utility';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-plu-summary-section',
  templateUrl: './pluSummary.component.html',
  styleUrls: ['./pluSummary.component.scss']
})
export class PluSummarySectionComponent implements OnInit {
  pluId: any;
  departmentsConfig: any;
  allowedPermissions = [CONSTANTS.Permissions.ManagePluReservation];
  pluSummaryObj:any = {
    division: null,
    department: null,
    code: null,
    pluRangeName: null,
    codeType: null ,
    startDate: null,
    endDate: null,
    itemDescription: null,
    addlDescription: null,
  }
  modalRef: BsModalRef;
  @ViewChild('deleteConfirm')
  private deleteConfirm: TemplateRef<any>;
  createdUser: string;
  startDate: any;
  endDate: any;
  departmentId: string = '';


  constructor(
    private router: Router,
    private modalService: BsModalService,
    public activatedRoute: ActivatedRoute,
    public _queryGenerator: QueryGenerator,
    private _pluFormService: PluDetailsService,
    private pluCommonService: PluCommonService,
    private commonService: CommonService

  ) { 
    // intentionally left empty
  }

  ngOnInit(): void {
    this.renderPluSummary();
  }
  renderPluSummary() {
    const activatedRoute = this.activatedRoute;
    const pluId = activatedRoute && activatedRoute.snapshot.params['pluId'];
    this.pluId = pluId;
    this.getPluReserveData(pluId);
  }
  getPluReserveData (pluId) {
    let paramsList = [
      {
        remove: false,
        parameter: 'id',
        value: pluId
      },
    ];
    this._queryGenerator.setQuery('');
    this._queryGenerator.pushParameters({ paramsList });
    this._pluFormService.getPluCodeData(this._queryGenerator.getQuery()).subscribe((res: any)=> {
      if(res && res.pluTriggerCodeReservations) {
        this.setDataForSummary(res);
      }
    });
  }
  setDataForSummary(data) {
    let pluCodeData = data && data.pluTriggerCodeReservations[0];
    if(pluCodeData) {
      this.departmentId = pluCodeData.department;
      const departmentName = this.commonService.getDepartmentNameFromCode(this.departmentId);
      this.startDate = pluCodeData.startDate;
      this.endDate = pluCodeData.endDate;
      const {firstName = '', lastName = ''}  = pluCodeData.createdUser;
      this.createdUser = `${firstName} ${lastName}`;
      this.pluSummaryObj = {
        division: pluCodeData.division,
        department: departmentName,
        code: pluCodeData.code,
        pluRangeName: pluCodeData.pluRangeName ? pluCodeData.pluRangeName : 'Monopoly',
        codeType: pluCodeData.codeType ,
        startDate: mmDdYySlash_DateFormat(pluCodeData.startDate || null),
        endDate: mmDdYySlash_DateFormat(pluCodeData.endDate || null),
        itemDescription: pluCodeData.itemDescription,
        addlDescription: pluCodeData.addlDescription
      };
      this.pluCommonService.updatePluDataKeys({
        id: pluCodeData.id,
        updatedUser: pluCodeData.updatedUser,
        lastUpdatedTs: pluCodeData.lastUpdatedTs,
        createdApplicationId: pluCodeData.createdApplicationId,
        createdTs: pluCodeData.createdTs,
        createdUser: pluCodeData.createdUser,
        lastUpdatedApplicationId: pluCodeData.lastUpdatedApplicationId
      });
    }
  }

  redirectToEdit(){
    if(this.pluId) {
      const editUrl = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluDetails}/${ROUTES_CONST.REQUEST.Edit}/${this.pluId}`;
      this.router.navigateByUrl(editUrl);
    }
  }
  deleteAction () {
    this.openModal(this.deleteConfirm, { keyboard: true, class: 'modal-m' });
  }
  openModal(template, options) {
    this.modalRef = this.modalService.show(template, options);
  }
  getPluFormMap () {
    return {
      ...this.pluSummaryObj,
      startDate: this.startDate,
      endDate: this.endDate,
      department: this.departmentId
    }
  }
}

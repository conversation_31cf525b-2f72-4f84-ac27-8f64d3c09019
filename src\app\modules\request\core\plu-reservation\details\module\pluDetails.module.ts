import { BsDatepickerModule } from "ngx-bootstrap/datepicker";
import { BsModalRef, ModalModule } from "ngx-bootstrap/modal";
import { ProgressbarModule } from "ngx-bootstrap/progressbar";

import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { CommonModule } from "@angular/common";
import { ApiErrorsModule } from "@appShared/components/common/api-errors/api-errors.module";

import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { OnlyNumberDirectiveModule } from '@appDirectives/only-number/only.number.module';
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { AppCommonModule } from "@appModules/common/app.common.module";
import { pluReservationFormContainer } from "@appRequestPLU/details/components/pluContainer/pluContainer.comp";
import { PluDetailsComponent } from "@appRequestPLU/details/components/pluDetails.component";
import { PluAddEditSectionComponent } from "@appRequestPLU/details/components/pluForm/pluForm.component";
import { PluSummarySectionComponent } from "@appRequestPLU/details/components/pluSummary/pluSummary.component";
import { PluDetailsRoutingModule } from '@appRequestPLU/details/routing/pluDetails.routing.module';
import { DeletePluModule } from '@appRequestPLU/shared/components/modal-views/deletePlu.module';
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { NgxConfirmBoxModule, NgxConfirmBoxService } from '@appShared/ngx-confirm-box';
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { TypeaheadModule } from "ngx-bootstrap/typeahead";



@NgModule({
  declarations: [pluReservationFormContainer,PluDetailsComponent, PluAddEditSectionComponent, PluSummarySectionComponent],
  imports: [
    OnlyNumberDirectiveModule,
    PluDetailsRoutingModule,
    AppCommonModule,
    CommonModule,
    DeletePluModule,
    FormsModule,
    VarDirectiveModule,
    ReactiveFormsModule,
    NgxDatatableModule,
    BsDatepickerModule.forRoot(),
    ModalModule.forRoot(),
    TypeaheadModule.forRoot(),
    ApiErrorsModule,
    ProgressbarModule.forRoot(),
    NgxConfirmBoxModule,
    NgSelectModule,
    NgOptionHighlightModule,
    PermissionsModule.forChild(),
    MarkAsTouchedOnFocusDirectiveModule
  ],
  exports: [pluReservationFormContainer,PluDetailsComponent, PluAddEditSectionComponent, PluSummarySectionComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [{ provide: BsModalRef, useValue: undefined }, NgxConfirmBoxService],
})
export class PluDetailsModule {}

import { NgModule } from '@angular/core';
import { ActivatedRouteSnapshot, RouterModule, Routes } from '@angular/router';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { pluReservationFormContainer } from '@appRequestPLU/details/components/pluContainer/pluContainer.comp';
import { PluDetailsComponent } from '@appRequestPLU/details/components/pluDetails.component';
import { CanDeactivateGuard } from '@appServices/common/can-deactivate-guard.service';
import { PermissionsGuard } from '@appShared/albertsons-angular-authorization';
import { AuthGuard } from 'guard/auth.guard';

export function redirectFunc(rejectedPermissionName: string, activateRouteSnapshot: ActivatedRouteSnapshot) {
  if (activateRouteSnapshot.params.pluId) {
    return `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluDetails}/${ROUTES_CONST.REQUEST.Summary}/${activateRouteSnapshot.params.pluId}`;
  } else {
    return `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluManagement}`;
  }
}

export const routes: Routes = [
  {
    path: '',
    component: pluReservationFormContainer,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: `/${ROUTES_CONST.REQUEST.PluManagement}`
      }, {
        path: ROUTES_CONST.REQUEST.Create,
        component: PluDetailsComponent,
        canActivate: [AuthGuard, PermissionsGuard],

        data: {
          permissions: {
            only: ['VIEW_PLU_RESERVATION'],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`
          }
        }
      },
      {
        path: ROUTES_CONST.REQUEST.Edit + '/:pluId',
        component: PluDetailsComponent,
        canDeactivate: [CanDeactivateGuard],
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
         
          permissions: {
            only: ['MANAGE_PLU_RESERVATION'],
            redirectTo: redirectFunc
          }
        }
      },
      {
        path: ROUTES_CONST.REQUEST.Summary + '/:pluId',
        component: PluDetailsComponent,
        canActivate: [AuthGuard, PermissionsGuard], //FeatureFlagGuard],
        data: {
          // //Feature flag check for the path
          // features: {
          //   requiredFeatureFlag: 'pluReservation',
          //   featureFlagRedirect: `/${ROUTES_CONST.REQUEST.Request}`,
          // },
          permissions: {
            only: ['VIEW_PLU_RESERVATION'],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`
          }
        },
        canDeactivate: [CanDeactivateGuard]
      }
    ]

  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PluDetailsRoutingModule { }

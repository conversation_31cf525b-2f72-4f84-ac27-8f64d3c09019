<div class="row justify-content-center">
  <div class="col-12">
    <api-errors *ngIf="!hideApiErrorOnRequestHome"></api-errors>
  </div>
</div>
<div class="row justify-content-between">
  <div class="col-sm-4 col-6 d-flex justify-content-start">
    <div class="page-title">
      <span class="page-title">PLU Reservation</span>
    </div>
  </div>
  <div class="col-sm-4 col-6 d-flex justify-content-end">
    <button class="btn btn-sm btn-primary create-request-button" (click)="onCreateNew()" *permissionsOnly="allowedPermissions">
      Create New
    </button>
  </div>
</div>
<div class="d-flex navbar px-0 navbar-expand-lg navbar-light search-section">
  <div class="col pr-0 row no-gutters saved-searches-align" id="navbarNavAltSearch">
    <div class="col-8">
      <input-search
        [headerPage]="'pluManagement'"
        class="full-search"
        [items]="items"
        [defaultValue]="'PLU'"
        aria-placeholder="Enter Keywords"
      ></input-search>
    </div>
  </div> 
</div>
<div class="d-flex">
  <facet-chips-list (facetChipListClick)="onFacetClipClick($event)" [facetPage]="'pluManagement'"></facet-chips-list>
</div>

<div class="d-flex">
  <div class="row no-gutters" style="width: 100%">
    <div class="col p-0">
      <div class="parent-container">
        <app-pod-filter [headerPage]="'pluManagement'"></app-pod-filter>
      </div>

      <div>
        <div class="alert alert-warning align-items- d-flex justify-content-center mt-5 ml-3 mb-5 text" role="alert" *ngIf="!pluItems.length">
          <strong>No records found!</strong>
        </div>

        <div
          *ngIf="pluItems && pluItems.length"
          class="col pr-0"
          [ngClass]="{ 'product-offset-2': pluItems.length % 3 === 1, 'product-offset-1': pluItems.length % 3 === 2 }"
        >
          <div class="d-flex">
            <div class="col-1 set-max-width ml-50">
              <label class="bold-label text-left">PLU</label>
            </div>
            <div class="col-md-2 col-xl-2 set-name-max-width">
              <label class="bold-label text-left">Division</label>
            </div>
            <div class="col-2 set-max-width-offer-type">
              <label class="bold-label text-left">Department</label>
            </div>
            <div class="col-1">
              <label class="bold-label text-left">Start Date</label>
            </div>
            <div class="col-1">
              <label class="bold-label text-left">End Date</label>
            </div>
            <div class="col-2 p-0">
              <label class="bold-label text-left">Description</label>
            </div>
            <div class="col-2">
              <label class="bold-label mr-0">Requester</label>
            </div>
            <div class="col-1 position-absolute actions-col">
              <label class="bold-label text-left">Actions</label>
            </div>
          </div>
          <ng-container *ngFor="let item of pluItems; trackBy: trackByFn; index as i">
            <div class="offer-req-list col p-0">
              <plu-list [pluItem]="item"></plu-list>
            </div>
          </ng-container>
        </div>
      </div>

      <div class="d-flex">
        <pagination [headerPage]="'pluManagement'" class="col-12 text-right p-0 mt-1 mb-3"></pagination>
      </div>
    </div>
  </div>
</div>

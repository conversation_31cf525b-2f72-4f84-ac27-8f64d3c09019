@import "scss/variables";
.spinner-border {
  position: fixed;
  top: 60%;
  left: 55%;
}
.page-title {
  font-size: $header-font-size;
}
.pagination {
  display: flex;
  justify-content: flex-end;
}
.custom-select {
  width: 50%;
}
.parent-container {
  align-items: center;
}
.bold-label{
  font-weight: 700;
  font-size: 14px;
}
.facets-wrapper {
  width: 265px;
}
.min-height {
  min-height: 100%;
}
.saved-searches-align {
  align-items: flex-start;
}
.actions-col{
  right: 22px;
}
.create-request-button {
  font-size: $main-font-size;
  font-weight: 800;
  letter-spacing: 1.2px;
  line-height: 22px;
  text-align: center;
}

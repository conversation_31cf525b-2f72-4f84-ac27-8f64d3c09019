@media (min-width: 320px) {
    .bold-label {
      font-weight: 700;
      font-size: 10px;
    }
    .text-label {
      font-size: 10px;
    }
  }
  
  @media (min-width: 576px) {
    .bold-label {
      font-weight: 700;
      font-size: 12px;
    }
    .text-label {
      font-size: 12px;
    }
  }
  
  @media (min-width: 768px) {
    .bold-label {
      font-weight: 700;
      font-size: 12px;
    }
    .text-label {
      font-size: 12px;
    }
    .set-max-width {
        max-width: 6em;
    }
      .set-max-width-offer-type {
          max-width: 6em;
      }
      .set-status-max-width {
          max-width: 96px;
      }
    .set-margin {
        margin-left: 1.5rem;
    }
    .set-name-max-width{
        max-width: 19%;
    }
  }
  @media (min-width: 930px) {
    .bold-label {
      font-weight: 700;
      font-size: 12px;
    }
    .text-label {
      font-size: 12px;
    }
    .set-max-width {
        max-width: 6em;
    }

      .set-max-width-offer-type {
          max-width: 6em;
      }
      .set-status-max-width {
          max-width: 96px;
      }
      .set-name-max-width{
        max-width: 19%;
    }
  }
  @media (min-width: 1010px) {
       .bold-label {
         font-weight: 700;
         font-size: 12px;
       }
       .text-label {
         font-size: 12px;
       }
       .set-max-width {
           max-width: 7em;
       }
   
         .set-max-width-offer-type {
             max-width: 9em;
         }
         .set-status-max-width {
             max-width: 96px;
         }
         .set-name-max-width{
            max-width: 19%;
        }
     }
     @media (min-width: 1040px) {
        .bold-label {
          font-weight: 700;
          font-size: 12px;
        }
        .text-label {
          font-size: 12px;
        }
        .set-max-width {
            max-width: 8em;
        }
    
          .set-max-width-offer-type {
              max-width: 9em;
          }
          .set-status-max-width {
              max-width: 96px;
          }
          .set-name-max-width{
             max-width: 19%;
         }
      }
  
  @media (min-width: 1158px) {
    .bold-label {
      font-weight: 700;
      font-size: 12px;
    }
    .text-label {
      font-size: 12px;
    }
    .set-max-width {
        max-width: 10em;
    }
    .set-max-width-offer-type {
        max-width: 10em;
    }
    .set-status-maxwidth {
        max-width: 8.333%
    }
    .set-name-max-width{
        max-width: 19%;
    }
  }
  
  @media (min-width: 1281px) {
    .bold-label {
      font-weight: 700;
      font-size: 14px;
    }
    .text-label {
      font-size: 14px;
    }
    .set-max-width {
        max-width: 10em;
    }
    .set-name-max-width{
        max-width: 22%;
    }
    .set-max-width-offer-type {
        max-width: 13em;
    }
  }
  @media (min-width: 1400px) {
    .bold-label {
      font-weight: 700;
      font-size: 14px;
    }
    .text-label {
      font-size: 14px;
    }
    .set-max-width {
        max-width: 10em;
    }
    .set-name-max-width{
        max-width: 25%;
    }
    .set-margin {
        margin-left: 1.5rem;
    }
  }
  @media (min-width: 1480px) {
    .bold-label {
      font-weight: 700;
      font-size: 14px;
    }
    .text-label {
      font-size: 14px;
    }
    .set-max-width {
        max-width: 10em;
    }
    .set-name-max-width{
        max-width: 25%;
    }
    .set-margin {
        margin-left: 3rem;
    }
  }

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { PluDetailsService } from '@appRequestServices/pluDetails.service';
import { PluSearchService } from '@appRequestServices/pluSearch.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { of, Subject } from 'rxjs';
import { PluManagementMainComponent } from './pluManagementMain.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('PluManagementMainComponent', () => {
  let component: PluManagementMainComponent;
  let fixture: ComponentFixture<PluManagementMainComponent>;

  const mockRouter = {
    navigate: jasmine.createSpy('navigate')
  };

  const mockAuthService = {
    isUserDataAvailable: new Subject<boolean>(),
    getUserId: jasmine.createSpy('getUserId').and.returnValue('user123')
  };

  const mockFacetItemService = {
    setOfferFilter: jasmine.createSpy('setOfferFilter'),
    createdAppIdChecked: { OMS: true },
    showSearchError: false,
    getQueryForDatesSearch: jasmine.createSpy('getQueryForDatesSearch')
  };

  const mockPluSearchService = {
    pluListSearchData$: new Subject<any>(),
    fetchPluList: jasmine.createSpy('fetchPluList')
  };

  const mockInitialDataService = {
    getPluSearchOptions: jasmine.createSpy('getPluSearchOptions').and.returnValue([
      { label: 'abc' },
      { label: 'xyz' }
    ])
  };

  const mockPluDetailsService = {};

  const mockSearchOfferRequestService = {
    populateHomeFilterSearch: jasmine.createSpy('populateHomeFilterSearch')
  };

  const mockQueryGenerator = {
    setQueryWithFilter: jasmine.createSpy('setQueryWithFilter'),
    setQuery: jasmine.createSpy('setQuery'),
    pushParameters: jasmine.createSpy('pushParameters'),
    removeParameters: jasmine.createSpy('removeParameters'),
    removeParam: jasmine.createSpy('removeParam')
  };

  const mockFeatureFlagService = {
    isUPPFieldSearchEnabled: false
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PluManagementMainComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: AuthService, useValue: mockAuthService },
        { provide: FacetItemService, useValue: mockFacetItemService },
        { provide: PluSearchService, useValue: mockPluSearchService },
        { provide: InitialDataService, useValue: mockInitialDataService },
        { provide: PluDetailsService, useValue: mockPluDetailsService },
        { provide: SearchOfferRequestService, useValue: mockSearchOfferRequestService },
        { provide: QueryGenerator, useValue: mockQueryGenerator },
        { provide: FeatureFlagsService, useValue: mockFeatureFlagService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    })
      .overrideTemplate(PluManagementMainComponent, `<div></div>`)
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PluManagementMainComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize data in ngOnInit', () => {
    spyOn(component, 'initSubscribes');
    component.ngOnInit();
    expect(mockFacetItemService.setOfferFilter).toHaveBeenCalledWith(null);
    expect(mockQueryGenerator.setQueryWithFilter).toHaveBeenCalledWith([]);
    expect(mockQueryGenerator.setQuery).toHaveBeenCalledWith('');
    expect(mockSearchOfferRequestService.populateHomeFilterSearch).toHaveBeenCalledWith({ facetFilter: null });
    expect(component.initSubscribes).toHaveBeenCalled();
  });

  it('should set and sort search options', () => {
    component.setSearchOptions();
    expect(component.items.length).toBe(2);
    expect(component.items[0].label).toBe('abc');
  });

  it('should subscribe to user and plu list data', () => {
    component.initSubscribes();
    mockAuthService.isUserDataAvailable.next(true);
    mockPluSearchService.pluListSearchData$.next([{ id: 1 }]);
    expect(component.pluItems).toEqual([{ id: 1 }]);
  });

  it('should navigate to create PLU page', () => {
    component.onCreateNew();
    expect(mockRouter.navigate).toHaveBeenCalled();
  });

  it('should handle facet chip click', () => {
    const element = { chip: 'status' };
    component.onFacetClipClick(element);
    expect(component.showFacets).toBeTrue();
    expect(mockFacetItemService.getQueryForDatesSearch).toHaveBeenCalled();
    expect(mockPluSearchService.fetchPluList).toHaveBeenCalledWith(null);
  });

  it('should set query parameters without feature flag', () => {
    component.setQueryParameters();
    expect(mockQueryGenerator.pushParameters).toHaveBeenCalled();
  });

  it('should set query parameters with feature flag enabled', () => {
    mockFeatureFlagService.isUPPFieldSearchEnabled = true;
    component.setQueryParameters();
    expect(mockQueryGenerator.pushParameters).toHaveBeenCalled();
  });

  it('should call getPluData', () => {
    spyOn(component, 'setQueryParameters');
    component.getPluData();
    expect(component.setQueryParameters).toHaveBeenCalled();
    expect(mockPluSearchService.fetchPluList).toHaveBeenCalledWith(null);
  });

  it('should return user ID', () => {
    const id = component.getUserId();
    expect(id).toBe('user123');
  });

  it('should track item by info', () => {
    const item = { info: 'info123' };
    expect(component.trackByFn(0, item)).toBe('info123');
  });
});

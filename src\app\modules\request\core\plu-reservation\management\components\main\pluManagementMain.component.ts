import { Component, OnInit } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";

import { Router } from "@angular/router";
import { PluDetailsService } from "@appRequestServices/pluDetails.service";
import { PluSearchService } from "@appRequestServices/pluSearch.service";
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from "@appServices/common/auth.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
  selector: "plu-management",
  templateUrl: "./pluManagement.component.html",
  styleUrls: ["./pluManagement.component.scss"],
})
export class PluManagementMainComponent extends UnsubscribeAdapter implements OnInit {
  applyFilter = true;
  showPodListSpinner = false;
  sid;
  facetItems;
  facetChipShow = false;
  search;
  showList = true;
  showGrid = false;
  expand = false;
  collapse = true;
  facetFilterQueryKeys = CONSTANTS.QUERY_WITH_FILTER_KEYS.facetFilter;
  showFacets = false;
  route: string;
  items: any;
  defaultValue: string = "PLU";
  isAllBatchSelected: any;
  allowedPermissions = [
    CONSTANTS.Permissions.ManagePluReservation
  ]; 
  hideApiErrorOnRequestHome: boolean = false;
  pluItems: any;

  constructor(
    private _router: Router,
    private _authService: AuthService,
    private facetItemService: FacetItemService,
    private pluSearchService: PluSearchService,
    private _initialDataService: InitialDataService,
    public pluDetailsService: PluDetailsService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private queryGenerator: QueryGenerator,
    private featureFlagService: FeatureFlagsService
  ) {
    super();
  }

  ngOnInit() {
    this.setSearchOptions();

    this.facetItemService.setOfferFilter(null);
    this.queryGenerator.setQueryWithFilter([]);
    this.queryGenerator.setQuery("");
    this._searchOfferRequestService.populateHomeFilterSearch({ facetFilter: null });

    this.initSubscribes();
  }

  setSearchOptions() {
    this.items = this._initialDataService.getPluSearchOptions();
    this.items.sort((a, b) => (a.label.toUpperCase() < b.label.toUpperCase() ? -1 : 1));
  }

  initSubscribes() {
    this.subs.sink = this._authService.isUserDataAvailable.subscribe((user: boolean) => {
      user && this.getPluData();
    });
    this.subs.sink = this.pluSearchService.pluListSearchData$.subscribe((data:any) => {
      this.pluItems = data;
    });
  }

  onCreateNew() {
    this._router.navigate([`${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluDetails}/${ROUTES_CONST.REQUEST.Create}`]);
  }

  onFacetClipClick(element: any) {
    this.facetItemService.showSearchError = false;
    this.showFacets = true;

    let paramsList = [];

    this.queryGenerator.removeParameters([CONSTANTS.NEXT, CONSTANTS.SID]);
    //paramsList.push({ remove: true, parameter: CONSTANTS.CREATED_APP_ID, value: "OMS" });

    this.queryGenerator.removeParam(element.chip);
    this.queryGenerator.pushParameters({ paramsList });
    this.facetItemService.getQueryForDatesSearch();
    this.pluSearchService.fetchPluList(null);
  }

  setQueryParameters() {
    this.queryGenerator.setQuery("");
    let paramsList = [
      {
        remove: false,
        parameter: CONSTANTS.LIMIT,
        value: CONSTANTS.PAGE_LIMIT,
      },
      {
        remove: false,
        parameter: CONSTANTS.SORT_BY,
        value: `${CONSTANTS.LAST_MODIFY_DATE}${CONSTANTS.DESC}`,
      }
    ];
    if(!this.featureFlagService.isUPPFieldSearchEnabled)
    {
      paramsList.push({remove: false, parameter: CONSTANTS.CREATED_APP_ID, value: "OMS" });
    }
    else{
      let createdAppId =[]
      Object.keys(this.facetItemService.createdAppIdChecked).forEach((key)=>{
        let value = this.facetItemService.createdAppIdChecked?.[key];
        if(value)
          createdAppId.push(key);
      });
      let searchInput =`(${createdAppId.join(' OR ')})`;
      paramsList.push({remove: false, parameter: CONSTANTS.CREATED_APP_ID, value: searchInput });
    }
    this.queryGenerator.pushParameters({ paramsList });
  }

  getPluData() {
    this.setQueryParameters();
    this.pluSearchService.fetchPluList(null);
  }

  getUserId() {
    return this._authService.getUserId();
  }

  trackByFn(index, item) {
    return item.info;
  }
}

<div class="list-expanded list-item-container pt-2 pb-2 pl-0 pr-0 mb-2 d-flex flex-column container-fluid">
  <div class="d-flex align-items-center">
    <div class="col-1">
      <a class="requestIdLink" [routerLink]="getSummaryPath(pluItemData.id)"
        ><label class="bold-label text-left mb-0">{{ pluItemData.code }}</label></a
      >
    </div>
    <div class="col-md-2 col-xl-2 set-name-max-width">
      <label class="text-label offer-name">{{ pluItemData.division }}</label>
    </div>
    <div class="col-2 set-max-width-offer-type">
      <label class="text-label">{{ departmentData }}</label>
    </div>
    <div class="col-1">
      <label class="text-label">{{ mmDdYySlash_DateFormat(pluItemData.startDate) }}</label>
    </div>
    <div class="col-1">
      <label class="text-label">{{ mmDdYySlash_DateFormat(pluItemData.endDate) }}</label>
    </div>
    <div class="col-2 p-0">
      <label class="text-label">{{ pluItemData.itemDescription }}</label>
    </div>
    <div class="col-2">
      <span class="d-inline-flex">
        <label *ngIf="pluItemData.createdUser">{{ pluItemData.createdUser.firstName }} {{ pluItemData.createdUser.lastName }}</label></span
      >
    </div>
    <div class="col-1 position-absolute actions-col">
      <label class="text-label">
        <div class="dropdown show" *ngIf="pluItemData.offerStatus !== 'Expired'">
          <app-actions-and-more
            [action]="'Manage'"
            [type]="'Actions'"
            [page]="'Manage'"
            [module]="'pluManagement'"
            [payload]="pluItemData"
          ></app-actions-and-more>
        </div>
      </label>
    </div>
  </div>
</div>

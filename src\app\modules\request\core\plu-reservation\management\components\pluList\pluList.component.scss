@import "scss/colors";
@import "scss/inputs.scss";

.actions-button {
  background-color: #ffffff !important;
  //color: #000000 !important;
  border-color: $grey-lighter-hex !important;
}
.dropdown-toggle::after {
  border: none;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+);
  vertical-align: 0;
  background-repeat: no-repeat;
}
.list-item-container {
  border: 1px solid #f0f4f7;
  background: #f0f4f7;
  padding: 0.5rem;
  height: 65px;
  justify-content: center;
}
.list-collapsed {
  background-color: #f0f4f7;
}
// .list-expanded {
//   background-color: rgb(255, 255, 255);

//   -moz-box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
//   -webkit-box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
//   box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
// }
.bold-label {
  margin: 0px;
  font-weight: 700;
  font-size: 16px;
}
.text-label {
  margin: 0px;
  font-size: 16px;
}

.dropdown-menu {
  min-width: 7rem !important;
}
.dropdown-item {
  padding: 0.25rem 1rem;
}
.requestIdLink {
  color: $theme-primary !important;
}
.offers-wrap .offer-line:last-child {
  display: none;
}

.digital-value {
  color: $theme-primary !important;
}

.zero-digital-value {
  color: $grey-dark-hex !important;
}
.isDisabled {
  cursor: not-allowed;
  opacity: 0.4;
}
.actions-col{
  right: 26px;
}

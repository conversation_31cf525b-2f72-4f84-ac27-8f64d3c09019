import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PluListComponent } from './pluList.component';
import { Router, NavigationEnd } from '@angular/router';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { CommonService } from '@appServices/common/common.service';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('PluListComponent', () => {
    let component: PluListComponent;
    let fixture: ComponentFixture<PluListComponent>;
    let routerSpy: jasmine.SpyObj<Router>;
    let initialDataServiceSpy: jasmine.SpyObj<InitialDataService>;
    let commonServiceSpy: jasmine.SpyObj<CommonService>;

    beforeEach(async () => {
        routerSpy = jasmine.createSpyObj('Router', ['navigateByUrl']);
        initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getAppData']);
        commonServiceSpy = jasmine.createSpyObj('CommonService', ['getDepartmentNameFromCode']);
        await TestBed.configureTestingModule({
            declarations: [PluListComponent],
            providers: [
                { provide: Router, useValue: routerSpy },
                { provide: InitialDataService, useValue: initialDataServiceSpy },
                { provide: CommonService, useValue: commonServiceSpy }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(PluListComponent);
        component = fixture.componentInstance;
        component.pluItem = {
            id: '123',
            info: { id: { externalOfferId: 'ext123' } }
        };
        component.pluItemData = { department: 'dept1' };
        initialDataServiceSpy.getAppData.and.returnValue({ app: 'data' });
        commonServiceSpy.getDepartmentNameFromCode.and.returnValue('Department One');
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should set initialData and detailsUrl on ngOnInit', () => {
        expect(component.initialData).toEqual({ app: 'data' });
        const expectedUrl = `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Edit}/${component.pluItem.id}`;
        expect(component.detailsUrl).toEqual(expectedUrl);
    });

    it('should set departmentData on ngOnChanges', () => {
        const deptCode = 'dept1';
        component.pluItemData = { department: deptCode };
        commonServiceSpy.getDepartmentNameFromCode.and.returnValue('Department One');
    
        component.ngOnChanges();
    
        expect(component.departmentData).toEqual('Department One');
        expect(commonServiceSpy.getDepartmentNameFromCode).toHaveBeenCalledWith(deptCode);
    });

    it('should return the correct summary path from getSummaryPath', () => {
        const id = '456';
        const expectedSummaryPath = `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluDetails}/${ROUTES_CONST.REQUEST.Summary}/${id}`;
    
        expect(component.getSummaryPath(id)).toEqual(expectedSummaryPath);
    });
    
    it('should format a valid date using mmDdYySlash_DateFormat', () => {
        const testDate = new Date(Date.UTC(2025, 2, 4));
  
        const formatted = component.mmDdYySlash_DateFormat(testDate);
  
        expect(formatted).toEqual('03/04/25');
    });

    describe('mmDdYySlash_DateFormat', () => {
        it('should format a valid date to "mm/dd/yy" format', () => {
            const testDate = new Date(Date.UTC(2025, 2, 4));
            const formatted = component.mmDdYySlash_DateFormat(testDate);
            expect(formatted).toEqual('03/04/25');
        });

        it('should return "Invalid date" when passed an invalid date input', () => {
            const formatted = component.mmDdYySlash_DateFormat("invalid date");
            expect(formatted).toEqual("Invalid date");
        });

    });
    
    it('should navigate to the correct detail url on editAction', () => {
        component.pluItem = {
            info: { id: { externalOfferId: 'ext123' } }
        };
        const expectedUrl = '/offers/edit/ext123';
  
        component.editAction();
  
        expect(routerSpy.navigateByUrl).toHaveBeenCalledWith(expectedUrl);
    });

});

import { ROUTES_CONST } from "@appConstants/routes_constants";

import { Component, Input, OnChanges, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { InitialDataService } from "@appServices/common/initial.data.service";

import { CommonService } from "@appServices/common/common.service";
import { mmDdYySlash_DateFormat } from "@appUtilities/date.utility";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
  selector: "plu-list",
  templateUrl: "./pluList.component.html",
  styleUrls: ["./pluList.component.scss"],
})
export class PluListComponent extends UnsubscribeAdapter implements OnInit, OnChanges {
  detailsUrl;
  @Input("pluItem") pluItemData: any;
  configData;
  pluItem: any = {};
  initialData: any;
  departmentData: any;
  constructor(private _router: Router, private _initialDataService: InitialDataService, private commonService: CommonService) {
    super();
  }

  ngOnInit() {
    this.initialData = this._initialDataService.getAppData();
    this.initData();
    this.mmDdYySlash_DateFormat = mmDdYySlash_DateFormat;
  }
  
  ngOnChanges(){
    this.departmentData = this.commonService.getDepartmentNameFromCode(this.pluItemData.department);
  }

  initData() {
    this.detailsUrl = `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Edit}/${this.pluItem.id}`;
  }

  mmDdYySlash_DateFormat(date) {
    return "";
  }

  editAction() {
    const detailUrl = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Edit}/${this.pluItem.info.id.externalOfferId}`;
    this._router.navigateByUrl(detailUrl);
  }

  getSummaryPath(id) {
    return `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluDetails}/${ROUTES_CONST.REQUEST.Summary}/${id}`;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

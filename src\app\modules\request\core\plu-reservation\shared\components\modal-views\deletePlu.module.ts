import { NgModule } from '@angular/core';
import { DeletePluComponent } from '@appRequestPLU/shared/components/modal-views/deletePlu.component';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { NgxConfirmBoxService } from '@appShared/ngx-confirm-box';
import { BsModalRef, ModalModule } from 'ngx-bootstrap/modal';

@NgModule({
  declarations: [DeletePluComponent],
  imports: [
    ModalModule.forRoot(),
    ApiErrorsModule
  ],
  exports: [DeletePluComponent],
  providers: [{ provide: BsModalRef, useValue: undefined }, NgxConfirmBoxService],
})
export class DeletePluModule {}

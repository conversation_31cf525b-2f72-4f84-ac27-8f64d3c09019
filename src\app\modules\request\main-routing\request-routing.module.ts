import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { RequestComponent } from "@appRequest/shared/components/request/request.component";
import { CanDeactivateGuard } from "@appServices/common/can-deactivate-guard.service";
import { PermissionsGuard } from '@appShared/albertsons-angular-authorization';
import { AuthGuard } from "guard/auth.guard";

export const routes: Routes = [
  {
    path: ROUTES_CONST.REQUEST.Request,
    component: RequestComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: ROUTES_CONST.REQUEST.RequestForm,
        loadChildren: () => import("../core/offer-request/details/request-form.module").then((m) => m.RequestFormModule),
        canActivate: [AuthGuard, PermissionsGuard],
        canDeactivate: [CanDeactivateGuard],
        data: {
          permissions: {
            only: ["VIEW_OFFER_REQUESTS", "VIEW_GR_SPD_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
      },
      {
        path: ROUTES_CONST.REQUEST.PluManagement,
        loadChildren: () => import("../core/plu-reservation/management/module/pluManagement.module").then((m) => m.PluManagementModule),
        canActivate: [AuthGuard, PermissionsGuard],
        canDeactivate: [CanDeactivateGuard],
        data: {
         
          permissions: {
            only: ["VIEW_PLU_RESERVATION"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
      },
      {
        path: ROUTES_CONST.REQUEST.PluDetails,
        loadChildren: () => import("../core/plu-reservation/details/module/pluDetails.module").then((m) => m.PluDetailsModule),
        canActivate: [AuthGuard, PermissionsGuard],
        canDeactivate: [CanDeactivateGuard],
        data: {
         
          permissions: {
            only: ["VIEW_PLU_RESERVATION"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RequestRoutingModule {}

import { TestBed } from '@angular/core/testing';
import { ComponentInstanceService } from './component-instance-service.service';
import { NopaSectionComponent } from '@appRequest/core/offer-request/details/components/nopa-section/nopa-section.component';
import { OfferReqBuilder } from '@appRequest/core/offer-request/details/components/offer-builder/offer-builder.comp';
import { OfferRequestSectionComponent } from '@appRequest/core/offer-request/details/components/request-section/request-section.comp';

describe('ComponentInstanceService', () => {
  let service: ComponentInstanceService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ComponentInstanceService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should set and get component correctly', () => {
    service.setComponent('TestComponent', OfferReqBuilder);
    expect(service.getComponent('TestComponent')).toBe(OfferReqBuilder);
  });

  it('should return undefined for unknown components', () => {
    expect(service.getComponent('UnknownComponent')).toBeUndefined();
  });

  it('should have preloaded components', () => {
    expect(service.getComponent('OfferReqBuilder')).toBe(OfferReqBuilder);
    expect(service.getComponent('OfferRequestSectionComponent')).toBe(OfferRequestSectionComponent);
    expect(service.getComponent('NopaSectionComponent')).toBe(NopaSectionComponent);
  });
});

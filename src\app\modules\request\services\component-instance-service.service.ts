import { Injectable } from '@angular/core';
import { AdditonalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/additonal-description.comp';
import { BpdAdditonalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/bpd-additonal-description/bpd-additonal-description.component';
import { GrAdditionalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/gr-additional-description/gr-additional-description.component';
import { SpdAdditionalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/spd-additional-description/spd-additional-description.component';
import { GeneralComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/general.component';
import { BPDPodComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/bpd-pod/bpd-pod.component';
import { GrPodComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/gr-pod/gr-pod.component';
import { SPDPodComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/spd-pod/spd-pod.component';
import { JustificationComponent } from '@appModules/request/core/offer-request/details/components/justification/justification.component';
import { NopaSectionComponent } from '@appModules/request/core/offer-request/details/components/nopa-section/nopa-section.component';
import { OfferReqBuilder } from '@appModules/request/core/offer-request/details/components/offer-builder/offer-builder.comp';
import { OfferDetailsComponent } from '@appModules/request/core/offer-request/details/components/offer-details/offer-details.comp';
import { BpdRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/bpd-request-section/bpd-request-section.component';
import { GrRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/gr-request-section/gr-request-section.component';
import { OfferRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/request-section.comp';
import { SpdRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/spd-request-section/spd-request-section.component';
import { BpdOfferRequestManagementComponent } from '@appModules/request/core/offer-request/management/components/bpd-offer-request-management/bpd-offer-request-management.component';
import { GrOfferRequestManagementComponent } from '@appModules/request/core/offer-request/management/components/gr-offer-request-management/gr-offer-request-management.component';
import { HomeComponent } from '@appModules/request/core/offer-request/management/components/home/<USER>';
import { SpdOfferRequestManagementComponent } from '@appModules/request/core/offer-request/management/components/spd-offer-request-management/spd-offer-request-management.component';
import { AdditionalInformationComponent } from '@appModules/templates/core/offer-template/details/components/additional-information/additional-information.component';
import { PodDetailsTemplateComponent } from '@appModules/templates/core/offer-template/details/components/pod-details-template/pod-details-template.component';
import { TemplateSectionComponent } from '@appModules/templates/core/offer-template/details/components/template-section/template-section.component';
import { TemplateManagementComponent } from '@appModules/templates/core/offer-template/management/components/home/<USER>';


@Injectable({
  providedIn: 'root'
})
export class ComponentInstanceService {

  componentInstances: Map<string, any> = new Map();

  constructor() {
    this.setComponent('OfferReqBuilder', OfferReqBuilder);
    this.setComponent('OfferRequestSectionComponent', OfferRequestSectionComponent);
    this.setComponent('NopaSectionComponent', NopaSectionComponent);
    this.setComponent('GeneralComponent', GeneralComponent);
    this.setComponent('AdditonalDescriptionComponent', AdditonalDescriptionComponent);
    this.setComponent('JustificationComponent', JustificationComponent);
    this.setComponent('OfferDetailsComponent', OfferDetailsComponent);
    this.setComponent('GrRequestSectionComponent', GrRequestSectionComponent);
    this.setComponent('GrOfferRequestManagementComponent', GrOfferRequestManagementComponent); 
    this.setComponent('HomeComponent', HomeComponent);
    this.setComponent('GrPodComponent', GrPodComponent);
    this.setComponent('GrAdditionalDescriptionComponent', GrAdditionalDescriptionComponent);
    this.setComponent('SpdRequestSectionComponent', SpdRequestSectionComponent);
    this.setComponent('SpdOfferRequestManagementComponent', SpdOfferRequestManagementComponent);
    this.setComponent('SPDPodComponent',SPDPodComponent);
    this.setComponent('SpdAdditionalDescriptionComponent', SpdAdditionalDescriptionComponent);
    
    this.setComponent('BPDTemplateManagementComponent',TemplateManagementComponent);
    this.setComponent('BPDTemplateSectionComponent',TemplateSectionComponent);
    this.setComponent('BPDAdditionalInformationComponent', AdditionalInformationComponent);
    this.setComponent('BPDPodDetailsTemplateComponent', PodDetailsTemplateComponent);

    this.setComponent('BpdOfferRequestManagementComponent',BpdOfferRequestManagementComponent);
    this.setComponent('BpdRequestSectionComponent',BpdRequestSectionComponent);
    this.setComponent('BpdAdditonalDescriptionComponent', BpdAdditonalDescriptionComponent);
    this.setComponent('BPDPodComponent', BPDPodComponent);

  }
  setComponent(name: string, component: any): void {
    this.componentInstances.set(name, component);
  }

  getComponent(name: string) {
    return this.componentInstances.get(name);
  }
}
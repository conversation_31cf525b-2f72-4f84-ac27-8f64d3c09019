import { TestBed } from '@angular/core/testing';
import { Injector } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpHeaders } from '@angular/common/http';
import { AdminStoreGroupService } from '@appAdminServices/admin-store-group.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { OfferRequestBaseService } from './offer-request-base.service';
import { UntypedFormGroup, UntypedFormControl, UntypedFormArray, Validators } from '@angular/forms';
import { AuthService } from '@appServices/common/auth.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { MsalService } from '@azure/msal-angular';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { AppService } from "@appServices/common/app.service";
import { ActivatedRoute } from '@angular/router';
import { BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { CONSTANTS } from '@appConstants/constants';
import { of, throwError } from 'rxjs';
import { REQUEST_CONSTANTS } from "@appModules/request/constants/request_constants";

describe('OfferRequestBaseService', () => {
    let service: OfferRequestBaseService;
    let toastrService: jasmine.SpyObj<ToastrService>;
    let adminStoreGroupService: jasmine.SpyObj<AdminStoreGroupService>;
    const authServiceStub = () => ({ onUserDataAvailable: () => ({}), getTokenString: () => ({}), getUserId: () => 'draja08' });
    const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
    const permissionsServiceStub = () => ({
        loadPermissions: () => ({})
    });
    
  const initialDataServiceStub = () => ({
    getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
    getConfigUrls: () => ({}),
    getEventsApi: () => ({}),
    getAppDataName: () => ({ batchImportConfig: { templatePath: {} } }),
  });
  const appServiceStub = () => ({ getFeatureFlags: () => ({}) });

    beforeEach(() => {
        
        const bsModalServiceStub = () => ({
            show: (importDetailsOverlay, object) => ({}),
        });
        const toastrSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']);
        const adminStoreGroupSpy = jasmine.createSpyObj('AdminStoreGroupService', ['listCorporateStoreGroups']);

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                OfferRequestBaseService,
                { provide: ToastrService, useValue: toastrSpy },
                { provide: AdminStoreGroupService, useValue: adminStoreGroupSpy },
                { provide: AuthService, useFactory: authServiceStub },
                { provide: MsalService, useFactory: MsalServiceStub },
                { provide: PermissionsService, useFactory: permissionsServiceStub },
                { provide: InitialDataService, useFactory: initialDataServiceStub },
                { provide: AppService, useFactory: appServiceStub },
                { provide: ActivatedRoute, useValue: {} },
                { provide: BsModalService, useFactory: bsModalServiceStub },
            ]
        });

        AppInjector.setInjector(TestBed.inject(Injector));
        service = TestBed.inject(OfferRequestBaseService);
        toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
        adminStoreGroupService = TestBed.inject(AdminStoreGroupService) as jasmine.SpyObj<AdminStoreGroupService>;
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should initialize request form', () => {
        service.initializeRequestForm();
    });

    it('should get offer request ID', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ id: new UntypedFormControl('123') }));
        service.requestForm = formGroup;
        expect(service.offerRequestId).toBe('123');
    });

    it('should get selected region ID', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ regionId: new UntypedFormControl('456') }));
        service.requestForm = formGroup;
        expect(service.selectedRegionId).toBe('456');
    });

    it('should handle save action', () => {
        service.requestForm = new UntypedFormGroup({}); // Ensure requestForm is initialized
        spyOn(service, 'doSave');
        service.save();
        expect(service.doSave).toHaveBeenCalled();
    });

    // it('should handle update action', () => {
    //     spyOn(service, 'dispatchSucessHandler');
    //     service.dispatchSucessHandler();
    //     expect(service.dispatchSucessHandler).toHaveBeenCalled();
    // });

    it('should handle region ID change', () => {
        const region = { storeGroupName: 'StoreGroup1', storeGroupId: '1' };
        spyOn(service, 'regionIdChange');
        service.regionIdChange(region);
        expect(service.regionIdChange).toHaveBeenCalled();
    });

    it('should handle pod usage limit change', () => {
        spyOn(service, 'setPodUsage');
        service.setPodUsage();
        expect(service.setPodUsage).toHaveBeenCalled();
    });

    it('should handle custom fields setting', () => {
        spyOn(service, 'setCustomFields');
        service.setCustomFields();
        expect(service.setCustomFields).toHaveBeenCalled();
    });

    it('should handle display start date setting', () => {
        spyOn(service, 'setDisplayStartDate');
        service.setDisplayStartDate();
        expect(service.setDisplayStartDate).toHaveBeenCalled();
    });
    it('should get program type control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ programType: new UntypedFormControl('type1') }));
        service.requestForm = formGroup;
        expect(service.programTypeCntrl.value).toBe('type1');
    });

    it('should get usage limit value', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ usageLimitTypePerUser: new UntypedFormControl('limit1') }));
        service.requestForm = formGroup;
        expect(service.usageLimitValue).toBe('limit1');
    });

    it('should get sub program code control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ subProgramCode: new UntypedFormControl('subCode1') }));
        service.requestForm = formGroup;
        expect(service.subProgramCdCtrl.value).toBe('subCode1');
    });

    it('should get usage limit control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ usageLimitTypePerUser: new UntypedFormControl('limitCtrl1') }));
        service.requestForm = formGroup;
        expect(service.usageLimitCtrl.value).toBe('limitCtrl1');
    });

    it('should get pod usage limit value', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('podDetails', new UntypedFormGroup({ podUsageLimit: new UntypedFormControl('podLimit1') }));
        service.requestForm = formGroup;
    });

    it('should check if behavioral continuity is enabled and selected BAC', () => {
        spyOnProperty(service.featureFlagService, 'isBehavioralContinuityEnabled', 'get').and.returnValue(true);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ deliveryChannel: new UntypedFormControl(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE) }));
        service.requestForm = formGroup;
        expect(service.isBehavioralContinuityEnabledAndSelectedBAC).toBeTrue();
    });

    it('should get form control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ testControl: new UntypedFormControl('testValue') }));
        service.requestForm = formGroup;
        expect(service.getFormControl('info', 'testControl').value).toBe('testValue');
    });

    it('should clear validators', () => {
        const formGroup = new UntypedFormGroup({
            testControl1: new UntypedFormControl('', Validators.required),
            testControl2: new UntypedFormControl('', Validators.required)
        });
        service.clearValidators(formGroup, ['testControl1', 'testControl2']);
        expect(formGroup.get('testControl1').validator).toBeNull();
        expect(formGroup.get('testControl2').validator).toBeNull();
    });
    it('should get pod usage limit value', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('podDetails', new UntypedFormGroup({ podUsageLimit: new UntypedFormControl('podLimit1') }));
        service.requestForm = formGroup;
    });

    it('should set data in form', () => {
        const formGroup = new UntypedFormGroup({
            testControl: new UntypedFormControl('')
        });
        const data = { testControl: 'testValue' };
        service.setData(formGroup, data);
        expect(formGroup.get('testControl').value).toBe('testValue');
    });

    it('should dispatch error handler', () => {
        const mockService = { dispatchErrorHandler: jasmine.createSpy('dispatchErrorHandler') };
        service.dispatchErrorHandler(mockService);
        expect(mockService.dispatchErrorHandler).toHaveBeenCalled();
    });

    it('should dispatch success handler', () => {
        const mockContainerService = { dispatchSucessHandler: jasmine.createSpy('dispatchSucessHandler') };
        service.dispatchSucessHandler(mockContainerService);
        expect(mockContainerService.dispatchSucessHandler).toHaveBeenCalled();
    });

    it('should get period week', () => {
        const offerStartDate = '2023-01-01';
        spyOn(service.http$, 'post').and.returnValue(of({}));
        service.getPeriodWk(offerStartDate);
        expect(service.http$.post).toHaveBeenCalled();
    });
    
    it('should clear async validators for allocation', () => {
        spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(true);
        spyOn(service, 'clearAsyncValidatiors');
        spyOn(service, 'getControl').and.returnValue(new UntypedFormControl());

        service.clearAsyncValidatorsForAllocation();

        expect(service.checkFeatureFlagEnabled).toHaveBeenCalledWith('enableAllocationOffers');
        expect(service.clearAsyncValidatiors).toHaveBeenCalledTimes(2);
        expect(service.getControl).toHaveBeenCalledWith('allocationCode');
        expect(service.getControl).toHaveBeenCalledWith('allocationCodeName');
    });
    it('should handle save action with setDynamicOffer', () => {
        spyOn(service, 'setDynamicOffer');
        spyOn(service, 'clearAsyncValidatorsForAllocation');
        spyOn(service, 'doSave');
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        service.requestForm = new UntypedFormGroup({}); 

        service.save();

        expect(service.setDynamicOffer).toHaveBeenCalled();
        expect(service.clearAsyncValidatorsForAllocation).toHaveBeenCalled();
        expect(service.doSave).toHaveBeenCalled();
    });

    it('should handle save action without setDynamicOffer', () => {
        spyOn(service, 'setDynamicOffer');
        spyOn(service, 'clearAsyncValidatorsForAllocation');
        spyOn(service, 'doSave');
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.GR);
        service.requestForm = new UntypedFormGroup({}); 

        service.save();

        expect(service.setDynamicOffer).not.toHaveBeenCalled();
        expect(service.clearAsyncValidatorsForAllocation).not.toHaveBeenCalled();
        expect(service.doSave).toHaveBeenCalled();
    });

    it('should handle update action', () => {
        spyOn(service, 'dispatchSucessHandler');
        spyOn(service.http$, 'put').and.returnValue(throwError('Error occurred'));

        const apiUrl = 'testApiUrl';
        const payload = { key: 'value' };

        service.update(apiUrl, payload);

        expect(service.http$.put).toHaveBeenCalledWith(apiUrl, payload);
    });

    it('should handle regions multiple copy offer request', () => {
        spyOn(service.http$, 'post').and.returnValue(of({}));
        const payload = { key: 'value' };

        service.regionsMultipleCopyOfferRequest(payload);

        expect(service.http$.post).toHaveBeenCalledWith(service.regionsMultipleCopy_API, { ...payload, reqObj: { headers: service.getHeaders() } });
    });
    it('should check custom validate', () => {
        spyOn(service, 'checkCustomValidation');
        const podDetails = {};
        service.checkCustomValidate(podDetails);
        expect(service.checkCustomValidation).toHaveBeenCalledWith(podDetails, service.requestForm, 'request');
    });
    it('should handle update action with error', () => {
        spyOn(service, 'dispatchSucessHandler');
        spyOn(service.http$, 'put').and.returnValue(of({}));
        spyOn(console, 'error');

        const apiUrl = 'testApiUrl';
        const payload = { key: 'value' };

        service.update(apiUrl, payload);

        expect(service.http$.put).toHaveBeenCalledWith(apiUrl, payload);
        expect(service.dispatchSucessHandler).toHaveBeenCalled();
    });

    it('should notify validation on action', () => {
        spyOn(service, 'notifySaveOrSubmitAttempt');
        service.requestForm = new UntypedFormGroup({});
        service.notifyValidationOnAction('submit');
        expect(service.isReqSubmitAttempted).toBeTrue();
        expect(service.notifySaveOrSubmitAttempt).toHaveBeenCalledWith(true, service.requestForm, 'submit');
    });
    it('should set pod data on value changes', () => {
        spyOn(service, 'updatePodDataOnValueChanges');
        service.requestForm = new UntypedFormGroup({});
        service.setPodDataOnValueChanges();
        expect(service.updatePodDataOnValueChanges).toHaveBeenCalledWith(service.requestForm);
    });
    
    it('should set multi clip value and validators when showMultiClipLimit is true', () => {
        const prgmCdRules = {
            podDetails: {
                multiClipLimit: {
                    validators: []
                }
            }
        };
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);

        service.setMultiClipValueAndValidators(prgmCdRules);
    });

    it('should return false when getMultiClipFeatureFlagsByProgCode is false', () => {
        spyOnProperty(service, 'getMultiClipFeatureFlagsByProgCode', 'get').and.returnValue(false);

        const result = service.setMultiClipValueAndValidators(null);

        expect(result).toBeFalse();
    });

    it('should return true for showMultiClipLimit when conditions are met', () => {
        spyOnProperty(service, 'getMultiClipFeatureFlagsByProgCode', 'get').and.returnValue(true);
        spyOnProperty(service, 'usageLimitValue', 'get').and.returnValue('ONCE_PER_CLIP');
        spyOnProperty(service, 'podUsageLimitValue', 'get').and.returnValue('MULTI_CLIP');

        expect(service.showMultiClipLimit).toBeTrue();
    });

    it('should return false for showMultiClipLimit when conditions are not met', () => {
        spyOnProperty(service, 'getMultiClipFeatureFlagsByProgCode', 'get').and.returnValue(false);
        spyOnProperty(service, 'usageLimitValue', 'get').and.returnValue('ONCE_PER_CLIP');
        spyOnProperty(service, 'podUsageLimitValue', 'get').and.returnValue('MULTI_CLIP');

        expect(service.showMultiClipLimit).toBeFalse();
    });

    it('should return true for isMultiClipFlagEnabled when feature flag is enabled', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.returnValue(true);

        expect(service.isMultiClipFlagEnabled).toBeTrue();
    });

    it('should return false for isMultiClipFlagEnabled when feature flag is not enabled', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.returnValue(false);

        expect(service.isMultiClipFlagEnabled).toBeFalse();
    });

    it('should return true for isFAProgSubType when conditions are met', () => {
        spyOnProperty(service, 'isFlavorAdvFeatureEnabled', 'get').and.returnValue(true);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ programType: new UntypedFormControl('EPISODIC') }));
        service.requestForm = formGroup;

        expect(service.isFAProgSubType).toBeTrue();
    });

    it('should return false for isFAProgSubType when conditions are not met', () => {
        spyOnProperty(service, 'isFlavorAdvFeatureEnabled', 'get').and.returnValue(false);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ programType: new UntypedFormControl('EPISODIC') }));
        service.requestForm = formGroup;

        expect(service.isFAProgSubType).toBeFalse();
    });

    it('should return correct feature flag value for getMultiClipFeatureFlagsByProgCode constant as GR', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.callFake((flag) => {
            if (flag === 'isMulticlipEnabled') return true;
            if (flag === 'enableSpdMultiClip') return false;
        });

        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.GR);
        expect(service.getMultiClipFeatureFlagsByProgCode).toBeTrue();
    });

    it('should return correct feature flag value for getMultiClipFeatureFlagsByProgCode constant as SPD', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.callFake((flag) => {
            if (flag === 'isMulticlipEnabled') return true;
            if (flag === 'enableSpdMultiClip') return false;
        });

        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        expect(service.getMultiClipFeatureFlagsByProgCode).toBeFalse();
    });

    it('should return correct feature flag value for getMultiClipFeatureFlagsByProgCode constant as Other', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.callFake((flag) => {
            if (flag === 'isMulticlipEnabled') return true;
            if (flag === 'enableSpdMultiClip') return false;
        });

        spyOn(service, 'getProgramCode').and.returnValue('OTHER');
        expect(service.getMultiClipFeatureFlagsByProgCode).toBeFalse();
    });

    it('should set pod usage if not Rx offer', () => {
        spyOnProperty(service, 'isFAProgSubType', 'get').and.returnValue(false);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ usageLimitTypePerUser: new UntypedFormControl('ONCE_PER_CLIP') }));
        formGroup.addControl('info', new UntypedFormGroup({ programType: new UntypedFormControl('OTHER') }));
        service.requestForm = formGroup;
        spyOn(service, 'setPodUsage');
        spyOn(service.onUsageLimitChangeGR$, 'next');

        service.setPodUsageIfNotRxOffer(null);

        expect(service.usageLimitCtrl.value).toBe('ONCE_PER_OFFER');
        expect(service.setPodUsage).toHaveBeenCalled();
        expect(service.onUsageLimitChangeGR$.next).toHaveBeenCalledWith(true);
    });

    it('should return true for isFlavorAdvFeatureEnabled when feature flag is enabled', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.returnValue(true);
        expect(service.isFlavorAdvFeatureEnabled).toBeTrue();
    });

    it('should return false for isFlavorAdvFeatureEnabled when feature flag is not enabled', () => {
        spyOn(service.featureFlagService, 'isFeatureFlagEnabled').and.returnValue(false);
        expect(service.isFlavorAdvFeatureEnabled).toBeFalse();
    });

    it('should validate multiClipLimitValidator correctly', () => {
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('multiClipLimitCtrl', new UntypedFormControl(0));
        service.requestForm = formGroup;
        formGroup.get('multiClipLimitCtrl').setValue(1);
    });
    

    it('should trim programSubType value', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ programSubType: new UntypedFormControl('  subtype  ') }));
        service.requestForm = formGroup;
        service.setprogramSubType();
        expect(service.programSubType.value).toBe('subtype');
    });

    it('should get customer segment control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ customerSegment: new UntypedFormControl('segment1') }));
        service.requestForm = formGroup;
        expect(service.customerSegCrtl.value).toBe('segment1');
    });

    it('should get period control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ period_week: new UntypedFormControl('week1') }));
        service.requestForm = formGroup;
        expect(service.periodCtrl.value).toBe('week1');
    });

    it('should get period ID control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ periodId: new UntypedFormControl('period1') }));
        service.requestForm = formGroup;
        expect(service.periodIdCtrl.value).toBe('period1');
    });

    it('should get dynamic offer control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ isDynamicOffer: new UntypedFormControl(true) }));
        service.requestForm = formGroup;
        expect(service.dynamicOfferCtrl.value).toBeTrue();
    });

    it('should get promo week ID control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ promoWeekId: new UntypedFormControl('promo1') }));
        service.requestForm = formGroup;
        expect(service.promoWeekIdCtrl.value).toBe('promo1');
    });

    it('should get offer start date control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ startDate: new UntypedFormGroup({ offerEffectiveStartDate: new UntypedFormControl('2023-01-01') }) }));
        service.requestForm = formGroup;
        expect(service.offerStartDateCtrl.value).toBe('2023-01-01');
    });

    it('should get custom type control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ customType: new UntypedFormControl('type1') }));
        service.requestForm = formGroup;
        expect(service.customTypeCtrl.value).toBe('type1');
    });

    it('should get custom usage control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ customUsage: new UntypedFormControl('usage1') }));
        service.requestForm = formGroup;
        expect(service.customUsageCtrl.value).toBe('usage1');
    });

    it('should get program subtype control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ programSubType: new UntypedFormControl('subtype1') }));
        service.requestForm = formGroup;
        expect(service.programSubType.value).toBe('subtype1');
    });

    it('should get behavioral action control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ behavioralAction: new UntypedFormControl('action1') }));
        service.requestForm = formGroup;
        expect(service.behavioralActionCtrl.value).toBe('action1');
    });

    it('should get program type control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('info', new UntypedFormGroup({ programType: new UntypedFormControl('type1') }));
        service.requestForm = formGroup;
        expect(service.programType.value).toBe('type1');
    });

    it('should get custom period control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ customPeriod: new UntypedFormControl('period1') }));
        service.requestForm = formGroup;
        expect(service.customPeriodCtrl.value).toBe('period1');
    });

    it('should get custom limit control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('rules', new UntypedFormGroup({ usageLimitPerUser: new UntypedFormControl('limit1') }));
        service.requestForm = formGroup;
        expect(service.customLimitCtrl.value).toBe('limit1');
    });

    it('should get store group service', async () => {
        const mockStoreGroups = [{ id: 1, name: 'StoreGroup1' }];
        adminStoreGroupService.listCorporateStoreGroups.and.returnValue(of(mockStoreGroups));
        const result = await service.getStoreGroupService();
        expect(result).toEqual(mockStoreGroups);
    });

    it('should handle region ID change when region is an object and not updating value validity', () => {
        const region = { storeGroupName: 'StoreGroup1', storeGroupId: '1' };
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                regionId: new UntypedFormControl(region)
            })
        });
        service.requestForm = formGroup;
        service.isUpdatingValueValidity = false;
        spyOn(service.generalOfferTypeServic$, 'getDropDownList').and.callThrough();
        const oROArray = new UntypedFormArray([new UntypedFormGroup({
            storeGroupVersion: new UntypedFormGroup({
                storeGroup: new UntypedFormGroup({
                    digitalRedemptionStoreGroupNames: new UntypedFormControl(),
                    digitalRedemptionStoreGroupIds: new UntypedFormControl(),
                    podStoreGroupNames: new UntypedFormControl(),
                    podStoreGroupIds: new UntypedFormControl()
                })
            })
        })]);
        service.generalOfferTypeServic$.generalOfferTypeForm = new UntypedFormGroup({
            offerRequestOffers: oROArray
        });

        service.regionIdChange({});
    });

    it('should handle region ID change when region is a string and storeGroupRegion exists', () => {
        const region = 'Region1';
        const storeGroupRegion = { storeGroupName: 'StoreGroup1', storeGroupId: '1', code: '19' };
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                regionId: new UntypedFormControl(region)
            })
        });
        service.requestForm = formGroup;
        spyOn(service.initialDataService$, 'getAppData').and.returnValue({ storeGroupRegions: [storeGroupRegion] });
        spyOn(service.generalOfferTypeServic$, 'getDropDownList').and.returnValue(['1', '27']);
        const oROArray = new UntypedFormArray([new UntypedFormGroup({
            storeGroupVersion: new UntypedFormGroup({
                storeGroup: new UntypedFormGroup({
                    digitalRedemptionStoreGroupNames: new UntypedFormControl(),
                    digitalRedemptionStoreGroupIds: new UntypedFormControl(),
                    podStoreGroupNames: new UntypedFormControl(),
                    podStoreGroupIds: new UntypedFormControl()
                })
            })
        })]);
        service.generalOfferTypeServic$.generalOfferTypeForm = new UntypedFormGroup({
            offerRequestOffers: oROArray
        });

        service.regionIdChange({});
    });

    it('should handle region ID change when region is an object and updating value validity', () => {
        const region = { storeGroupName: 'StoreGroup1', storeGroupId: '1' };
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                regionId: new UntypedFormControl(region)
            })
        });
        service.requestForm = formGroup;
        service.isUpdatingValueValidity = true;

        const result = service.regionIdChange({});

        expect(result).toBeFalse();
    });

    it('should return true for isRewardFreq when usageLimitTypePerUser is CUSTOM', () => {
        const programRules = {
            offerRequest: {
                usageLimitPerUser: { validators: [] },
                customPeriod: { validators: [] }
            }
        };
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('CUSTOM')
            })
        });
        service.requestForm = formGroup;

        const result = service.isRewardFreq(programRules);

        expect(result).toBeTrue();
        expect(programRules.offerRequest.usageLimitPerUser.validators).toContain(Validators.required);
        expect(programRules.offerRequest.customPeriod.validators).toContain(Validators.required);
    });

    it('should return false for isRewardFreq when usageLimitTypePerUser is not CUSTOM', () => {
        const programRules = {
            offerRequest: {
                usageLimitPerUser: { validators: [Validators.required] },
                customPeriod: { validators: [Validators.required] }
            }
        };
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('ONCE_PER_OFFER')
            })
        });
        service.requestForm = formGroup;

        const result = service.isRewardFreq(programRules);

        expect(result).toBeFalse();
        expect(programRules.offerRequest.usageLimitPerUser.validators.length).toBe(0);
        expect(programRules.offerRequest.customPeriod.validators.length).toBe(0);
    });

    it('should set custom fields when offer usage limit is CUSTOM', () => {
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('CUSTOM'),
                customType: new UntypedFormControl(null),
                usageLimitPerUser: new UntypedFormControl(null),
                customPeriod: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;

        service.setCustomFields();

        expect(service.customTypeCtrl.value).toBe(REQUEST_CONSTANTS.START_OF_INCENTIVE);
        expect(service.customLimitCtrl.value).toBeNull();
        expect(service.customPeriodCtrl.value).toBeNull();
    });

    it('should clear custom fields when offer usage limit is not CUSTOM', () => {
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('ONCE_PER_OFFER'),
                customType: new UntypedFormControl('type1'),
                usageLimitPerUser: new UntypedFormControl('limit1'),
                customPeriod: new UntypedFormControl('period1')
            })
        });
        service.requestForm = formGroup;

        service.setCustomFields();

        expect(service.customTypeCtrl.value).toBeNull();
        expect(service.customLimitCtrl.value).toBeNull();
        expect(service.customPeriodCtrl.value).toBeNull();
    });

    it('should get sub type', () => {
        const programType = 'testProgramType';
        spyOn(service.http$, 'get').and.returnValue(of({}));
        service.getSubType(programType);
    });

    it('should set display start date', () => {
        spyOn(service, 'updateDisplayStartDate');
        service.requestForm = new UntypedFormGroup({});
        service.setDisplayStartDate();
        expect(service.updateDisplayStartDate).toHaveBeenCalledWith(service.requestForm);
    });

    it('should validate multiClipLimitValidator correctly when showMultiClipLimit is true and value is less than 1', () => {
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('multiClipLimitCtrl', new UntypedFormControl(0));
        service.requestForm = formGroup;

        const result = service.multiClipLimitValidator();
    });

    it('should validate multiClipLimitValidator correctly when showMultiClipLimit is true and value is 1 or more', () => {
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('multiClipLimitCtrl', new UntypedFormControl(1));
        service.requestForm = formGroup;

        const result = service.multiClipLimitValidator();

        expect(result).toBeNull();
    });

    it('should validate multiClipLimitValidator correctly when showMultiClipLimit is false', () => {
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(false);
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('multiClipLimitCtrl', new UntypedFormControl(0));
        service.requestForm = formGroup;

        const result = service.multiClipLimitValidator();

        expect(result).toBeNull();
    });

    it('should set dynamic offer when feature flag is enabled and program code is SPD with BEHAVIORAL_ACTION_CODE', () => {
        spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(true);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        spyOnProperty(service, 'deliveryChannel', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_ACTION_CODE);
    
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                isDynamicOffer: new UntypedFormControl(null),
                daysToRedeem: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;
    
        service.setDynamicOffer();
    
        expect(service.requestForm.get('info').get('isDynamicOffer')).toBeNull();
        expect(service.requestForm.get('info').get('daysToRedeem')).toBeNull();
    });

    it('should set dynamic offer when feature flag is enabled and program code is SPD with BEHAVIORAL_CONTINUTY_CODE', () => {
        spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(true);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        spyOnProperty(service, 'deliveryChannel', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);
    
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                isDynamicOffer: new UntypedFormControl(null),
                daysToRedeem: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;
    
        service.setDynamicOffer();
    
        expect(service.requestForm.get('info').get('isDynamicOffer')).toBeNull();
        expect(service.requestForm.get('info').get('daysToRedeem')).toBeNull();
    });

    it('should not set dynamic offer when feature flag is not enabled', () => {
        spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(false);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                isDynamicOffer: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;

        service.setDynamicOffer();

        expect(service.dynamicOfferCtrl.value).toBeNull();
    });

    it('should not set dynamic offer when program code is not SPD', () => {
        spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(true);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.GR);
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                isDynamicOffer: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;

        service.setDynamicOffer();

        expect(service.dynamicOfferCtrl.value).toBeNull();
    });

    it('should not set dynamic offer when dynamicOfferCtrl is touched or has value', () => {
        spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(true);
        spyOn(service, 'getProgramCode').and.returnValue(CONSTANTS.SPD);
        const formGroup = new UntypedFormGroup({
            info: new UntypedFormGroup({
                isDynamicOffer: new UntypedFormControl(true)
            })
        });
        service.requestForm = formGroup;
        service.dynamicOfferCtrl.markAsTouched();

        service.setDynamicOffer();

        expect(service.dynamicOfferCtrl.value).toBeTrue();
    });

    it('should get control from base', () => {
        spyOn(service, 'getControlFromBase').and.returnValue(new UntypedFormControl('testValue'));
        const result = service.getControl('testControl');
        expect(service.getControlFromBase).toHaveBeenCalledWith('testControl', service.requestForm);
        expect(result.value).toBe('testValue');
    });

    it('should get field errors', () => {
        const control = new UntypedFormControl();
        spyOn(service, 'getControl').and.returnValue(control);
        spyOn(service, 'getErrorsForField').and.returnValue({ required: true });

        const result = service.getFieldErrors('testControl');

        expect(service.getControl).toHaveBeenCalledWith('testControl');
        expect(service.getErrorsForField).toHaveBeenCalledWith('testControl', control);
        expect(result).toEqual({ required: true });
    });

    it('should set pod usage to ONCE_PER_OFFER when usage limit is ONCE_PER_OFFER', () => {
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('ONCE_PER_OFFER')
            }),
            podDetails: new UntypedFormGroup({
                podUsageLimit: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;

        service.setPodUsage();
    });

    it('should set pod usage to MULTI_CLIP when usage limit is ONCE_PER_CLIP', () => {
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('ONCE_PER_CLIP')
            }),
            podDetails: new UntypedFormGroup({
                podUsageLimit: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;

        service.setPodUsage();
    });

    it('should set pod usage to UNLIMITED when usage limit is not CUSTOM, ONCE_PER_OFFER, or ONCE_PER_CLIP', () => {
        const formGroup = new UntypedFormGroup({
            rules: new UntypedFormGroup({
                usageLimitTypePerUser: new UntypedFormControl('UNLIMITED')
            }),
            podDetails: new UntypedFormGroup({
                podUsageLimit: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;

        service.setPodUsage();
    });

    it('should set multi clip value and validators when program code rules are provided', () => {
        const prgmCdRules = {
            podDetails: {
                multiClipLimit: {
                    validators: []
                }
            }
        };
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);

        service.setMultiClipValueAndValidators(prgmCdRules);
    });

    it('should not set multi clip value and validators when program code rules are not provided', () => {
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(false);

        const result = service.setMultiClipValueAndValidators(null);

        expect(result).toBeFalse();
    });
    it('should get event control', () => {
        const formGroup = new UntypedFormGroup({});
        formGroup.addControl('podDetails', new UntypedFormGroup({ eventIds: new UntypedFormControl('event1') }));
        service.requestForm = formGroup;
    });
    it('should add validators to multiClipLimit when showMultiClipLimit is true and validators do not exist', () => {
        const prgmCdRules = {
            podDetails: {
                multiClipLimit: {
                    validators: []
                }
            }
        };
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);

        service.setMultiClipValueAndValidators(prgmCdRules);
    });

    it('should not add validators to multiClipLimit when showMultiClipLimit is true and validators already exist', () => {
        const prgmCdRules = {
            podDetails: {
                multiClipLimit: {
                    validators: [Validators.required]
                }
            }
        };
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(true);

        service.setMultiClipValueAndValidators(prgmCdRules);

        expect(prgmCdRules.podDetails.multiClipLimit.validators.length).toBe(1);
    });

    it('should remove validators from multiClipLimit when showMultiClipLimit is false and validators exist', () => {
        const prgmCdRules = {
            podDetails: {
                multiClipLimit: {
                    validators: [Validators.required, service.multiClipLimitValidator.bind(service)]
                }
            }
        };
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(false);

        service.setMultiClipValueAndValidators(prgmCdRules);
    });

    it('should set multiClipLimitCtrl value to null when showMultiClipLimit is false', () => {
        const prgmCdRules = {
            podDetails: {
                multiClipLimit: {
                    validators: [Validators.required, service.multiClipLimitValidator.bind(service)]
                }
            }
        };
        spyOnProperty(service, 'showMultiClipLimit', 'get').and.returnValue(false);
        const formGroup = new UntypedFormGroup({
            multiClipLimitCtrl: new UntypedFormControl('someValue')
        });
        service.requestForm = formGroup;

        service.setMultiClipValueAndValidators(prgmCdRules);
    });
    it('should set pod usage limit value correctly', () => {
        const formGroup = new UntypedFormGroup({
            podDetails: new UntypedFormGroup({
                podUsageLimit: new UntypedFormControl(null)
            })
        });
        service.requestForm = formGroup;
    });

    it('should return null for pod usage limit value when podDetails is not defined', () => {
        const formGroup = new UntypedFormGroup({});
        service.requestForm = formGroup;
    });

    it('should return null for pod usage limit value when podUsageLimit control is not defined', () => {
        const formGroup = new UntypedFormGroup({
            podDetails: new UntypedFormGroup({})
        });
        service.requestForm = formGroup;
    });

    it('should return null for event control when podDetails is not defined', () => {
        const formGroup = new UntypedFormGroup({});
        service.requestForm = formGroup;
    });

    it('should return null for event control when eventIds control is not defined', () => {
        const formGroup = new UntypedFormGroup({
            podDetails: new UntypedFormGroup({})
        });
        service.requestForm = formGroup;
    });

    it('should get pod usage limit values', () => {
        const formGroup = new UntypedFormGroup({
            podDetails: new UntypedFormGroup({
                podUsageLimit: new UntypedFormControl('podLimit1')
            })
        });
        service.requestForm = formGroup;
    });

});

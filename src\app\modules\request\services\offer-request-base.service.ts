import { HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { UntypedFormArray, UntypedFormGroup, Validators } from "@angular/forms";
import { AdminStoreGroupService } from "@appAdminServices/admin-store-group.service";
import { CONSTANTS } from "@appConstants/constants";
import { OFFER_CONSTANTS } from "@appModules/offers/constants/offer_constants";
import { REQUEST_CONSTANTS } from "@appModules/request/constants/request_constants";
import { OFFER_REQUEST_CREATE_RULES } from "@appModules/request/shared/rules/create.rules";
import { AppInjector } from "@appServices/common/app.injector.service";
import { TemplateRequestBaseService } from "@appTemplates/services/template-request-base/template-request-base.service";
import { dateInOriginalFormat } from "@appUtilities/date.utility";
import { updateTreeValidity } from "@appUtilities/updateValueValidatityForTree";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";

@Injectable({
    providedIn: "root",
})
export class OfferRequestBaseService extends TemplateRequestBaseService {
    public adminStoreGroupService: AdminStoreGroupService;
    public programCodeRule = OFFER_REQUEST_CREATE_RULES;
    requestForm: UntypedFormGroup;
    periodWkApi: string;
    regionsMultipleCopy_API: string;
    getProgramSubtypeAPI: string;
    onUsageLimitChangeGR$ = new Subject();
    onBACChannelSelected$ = new Subject();
    // once the events loaded based on feature flag, will set the evnts here to reuse
    eventsList: any;

    programTypeChangedValue$ = new Subject();
    grProgramTypeChangedValue$ = new Subject();
    toasterService$: ToastrService;
    isScene7InValidError: boolean;

    constructor() {
        super();
        const injector = AppInjector.getInjector();
        this.toasterService$ = injector.get(ToastrService);
        this.adminStoreGroupService = injector.get(AdminStoreGroupService);
        this.regionsMultipleCopy_API = this.initialDataService$.getConfigUrls(OFFER_CONSTANTS.OFFER_REQ_REGIONS_MULTIPLE_COPY);
        this.periodWkApi = this.initialDataService$.getConfigUrls(REQUEST_CONSTANTS.GET_PERIOD_wK_API);
        this.getProgramSubtypeAPI = this.initialDataService$.getConfigUrls(CONSTANTS.SPD_OFFER_REQUEST_GET_SUBPROGRAM);
        this.requestForm = this.requestFormService$.requestForm;
        this.requestFormService$.offerRequestBaseService = this;
    }
    initializeRequestForm() {
        this.requestForm = this.requestFormService$.requestForm;
    }
    get offerRequestId() {
        return this.requestForm?.get("info")?.get("id")?.value;
    }
    get selectedRegionId() {
        return this.requestForm?.get("info")?.get("regionId")?.value;
    }

    get programTypeCntrl() {
        return this.requestForm?.get("info")?.get("programType");
    }
    get usageLimitValue() {
        return this.requestForm?.get("rules")?.get("usageLimitTypePerUser")?.value;
    }
    get subProgramCdCtrl() {
        return this.requestForm?.get("info")?.get("subProgramCode");
    }
    get usageLimitCtrl() {
        return this.requestForm?.get("rules")?.get("usageLimitTypePerUser");
    }
    get podUsageLimitValue() {
        return this.podDetails?.get("podUsageLimit")?.value;
    }
    get deliveryChannel(){
        return this.requestForm?.get("info")?.get("deliveryChannel")?.value;
    }

    get isBehavioralContinuityEnabledAndSelectedBAC() {
        const selectedChannel = this.requestForm?.get('info')?.get('deliveryChannel')?.value;
        return this.featureFlagService.isBehavioralContinuityEnabled && selectedChannel === CONSTANTS.BEHAVIORAL_CONTINUTY_CODE;
      }
    getFormControl(form, formControlName) {
        return this.requestForm.get(form).get(formControlName);
    }
    clearValidators(form, validators) {
        validators.forEach((control) => {
            form.get(control).clearValidators();
        });
        updateTreeValidity(form as UntypedFormGroup);
    }
    setData(form: UntypedFormGroup, data) {
        form.patchValue(data);
    }
    dispatchErrorHandler(service) {
        service.dispatchErrorHandler();
    }
    dispatchSucessHandler(containerService) {
        containerService.dispatchSucessHandler();
    }
    getPeriodWk(offerStartDate) {
        const offerEffectiveStartDate = dateInOriginalFormat({
            date: offerStartDate,
            isStartDate: true,
        });
        let searchInput = { offerEffectiveStartDate, reqObj: { headers: this.getHeaders() } };
        return this.http$.post(this.periodWkApi, searchInput);
    }
    clearAsyncValidatorsForAllocation() {
        let isFeatureEnabled = this.checkFeatureFlagEnabled("enableAllocationOffers");
        isFeatureEnabled && ["allocationCode", "allocationCodeName"].forEach((ctrl) => this.clearAsyncValidatiors(this.getControl(ctrl)));
    }
    save(actionLabel = "save") {
        if (this.getProgramCode() === CONSTANTS.SPD || this.commonRouteService.isBpdReqPage) {
            this.setprogramSubType();  
            this.setDynamicOffer();          
            this.clearAsyncValidatorsForAllocation();
        }else if(this.getProgramCode() === CONSTANTS.GR || this.getProgramCode() === CONSTANTS.SC){
            this.programSubType?.setValue(null);
        }
        this.requestForm.removeControl("offerReqGroup");
        this.doSave(this.requestForm, actionLabel, "offerRequest");
    }
    
    setDynamicOffer() {
        if (!this.checkFeatureFlagEnabled("enableDynamicOffers") || this.getProgramCode() !== CONSTANTS.SPD) {
            return;
        }

        if ([CONSTANTS.BEHAVIORAL_CONTINUTY_CODE, CONSTANTS.BEHAVIORAL_ACTION_CODE].includes(this.deliveryChannel)) {
            const infoGroup = this.requestForm.get('info') as UntypedFormGroup;
            infoGroup?.removeControl('isDynamicOffer');
            infoGroup?.removeControl('daysToRedeem');
            return;
        }
    
    }

    
    
    update(apiUrl, payload) {
        this.http$.put(apiUrl, payload).subscribe(
            (response) => this.dispatchSucessHandler(response),
            (err) => {}
        );
    }
    regionsMultipleCopyOfferRequest(payload) {
        let searchInput = { ...payload, reqObj: { headers: this.getHeaders() } };
        return this.http$.post(this.regionsMultipleCopy_API, searchInput);
    }
    checkCustomValidate(podDetails) {
        this.checkCustomValidation(podDetails, this.requestForm, "request");
    }  
    // on change of offer usage limit need to set pod usage
    setPodUsage(prgmCdRules = null) {
        const offerUsageLimit = this.requestForm?.get("rules")?.get("usageLimitTypePerUser")?.value;
        if (offerUsageLimit) {
            if (offerUsageLimit == "ONCE_PER_OFFER") {
                this.podUsageControl?.setValue("ONCE_PER_OFFER");
            } else if (offerUsageLimit === "ONCE_PER_CLIP") {
                this.podUsageControl?.setValue("MULTI_CLIP");
            } else if (offerUsageLimit !== "CUSTOM") {
                this.podUsageControl?.setValue("UNLIMITED");
            } else if(offerUsageLimit === "CUSTOM") {
                // If from ONCE PER CLIP, User selects CUSTOM, POD USAGE LIMIT SHOULD DEFAULT TO OLD VALUE
                const isPodUsageMultiClip = this.podUsageControl.value;
                if(isPodUsageMultiClip === "MULTI_CLIP") {
                    this.podUsageControl.setValue("ONCE_PER_OFFER");
                }
            }
            this.setMultiClipValueAndValidators(prgmCdRules);
            this.requestFormService$?.selectedOfferLimitType$?.next(offerUsageLimit);
        }
    }
    notifyValidationOnAction(action) {
        this.isReqSubmitAttempted = true;
        this.notifySaveOrSubmitAttempt(true, this.requestForm, action);
    }

    getControl(ctrlName) {
        return this.getControlFromBase(ctrlName, this.requestForm);
    }
    getFieldErrors(ctrl) {
        let control = this.getControl(ctrl);
        return this.getErrorsForField(ctrl, control);
    }
    ngOnDestroy() {
        this.requestForm = null;
    }
    setPodDataOnValueChanges() {
        this.updatePodDataOnValueChanges(this.requestForm);
    }
    setMultiClipValueAndValidators(prgmCdRules) {
        if(!this.getMultiClipFeatureFlagsByProgCode) {
            return false;
        }
        const multiClipLimitValidators = prgmCdRules?.podDetails["multiClipLimit"]?.validators,
        isValidatorsExist = multiClipLimitValidators?.length;
        if(this.showMultiClipLimit) {
            !isValidatorsExist && multiClipLimitValidators?.push(Validators.required, this.multiClipLimitValidator.bind(this))
        } else {
            isValidatorsExist && multiClipLimitValidators?.splice(0,2);
            this.multiClipLimitCtrl?.setValue(null);
        }
    }
    get showMultiClipLimit() {
        return this.getMultiClipFeatureFlagsByProgCode && this.usageLimitValue === 'ONCE_PER_CLIP' && this.podUsageLimitValue === 'MULTI_CLIP';
    }
    get isMultiClipFlagEnabled() {
        return this.featureFlagService.isFeatureFlagEnabled('isMulticlipEnabled');
    }
    get isFAProgSubType() {
        const progTypeValue = this.programType?.value;
        return this.isFlavorAdvFeatureEnabled && [CONSTANTS.SPD].includes(this.getProgramCode())
         && progTypeValue === "EPISODIC";
    }
    get getMultiClipFeatureFlagsByProgCode() {
        //Return feature flag value based on the program code
        let featureFlag;
        switch (this.getProgramCode()) {
            case CONSTANTS.GR: {
                featureFlag = this.featureFlagService.isFeatureFlagEnabled('isMulticlipEnabled');
                break;
            }
            case CONSTANTS.SPD: {
                featureFlag = this.featureFlagService.isFeatureFlagEnabled('enableSpdMultiClip');
                break;
            }
            //If not SPD OR GR Program Code return false flag value
            default: featureFlag = false
        }
        return featureFlag
    }
    setPodUsageIfNotRxOffer(spd_rule) {
        const progrmType = this.programType?.value;
        if((!this.isFAProgSubType || progrmType !== "HEALTH") && this.usageLimitCtrl?.value === "ONCE_PER_CLIP") {
          this.usageLimitCtrl?.setValue("ONCE_PER_OFFER");
          this.usageLimitCtrl?.updateValueAndValidity();
          this.setPodUsage(spd_rule);
          this.onUsageLimitChangeGR$.next(true);
        }
    }
    get isFlavorAdvFeatureEnabled() {
        return this.featureFlagService.isFeatureFlagEnabled("isFlavorAdventureEnabled");
    }
    multiClipLimitValidator() {
        const val = this.multiClipLimitCtrl?.value;
        if(this.showMultiClipLimit && val && val < 1) {
           return {customError: true};
        } else {
            return null;
        }
    }
    setprogramSubType() {
        // let progSubType = this?.requestForm?.get("info")?.get("programSubType")?.value;
        this.programSubType?.setValue(this.programSubType?.value?.trim());      
    }
    get eventControl() {
        return this.podDetails?.get("eventIds");
    }

    get customerSegCrtl() {
        return this.requestForm?.get("rules")?.get("customerSegment");
    }

    get periodCtrl() {
        return this.requestForm?.get("info")?.get("period_week");
    }
    get periodIdCtrl() {
        return this.requestForm?.get("info")?.get("periodId");
    }
    get dynamicOfferCtrl() {
        return this.requestForm?.get("info")?.get("isDynamicOffer");
    }
    get promoWeekIdCtrl() {
        return this.requestForm?.get("info")?.get("promoWeekId");
    }
    get offerStartDateCtrl() {
        return this.requestForm?.get("rules")?.get("startDate")?.get("offerEffectiveStartDate");
    }

    get customTypeCtrl() {
        return this.requestForm?.get("rules")?.get("customType");
    }

    get customUsageCtrl() {
        return this.requestForm?.get("rules")?.get("customUsage");
    }

    get programSubType() {
        return this.requestForm?.get("info")?.get("programSubType");
    }
    get behavioralActionCtrl() {
        return this.requestForm?.get("info")?.get("behavioralAction");
    }
    get programType() {
        return this.requestForm?.get("info")?.get("programType");
    }

    get customPeriodCtrl() {
        return this.requestForm?.get("rules")?.get("customPeriod");
    }
    get customLimitCtrl() {
        return this.requestForm?.get("rules")?.get("usageLimitPerUser");
    }

    getStoreGroupService() {
        return this.adminStoreGroupService.listCorporateStoreGroups().toPromise();
    }

    regionIdChange(e) {
        let region = this.requestForm?.get("info")?.get("regionId")?.value;
        const appData = this.initialDataService$.getAppData();
        const { storeGroupRegions } = appData;
        if (!storeGroupRegions) {
            return false;
        }
        let oROArray = this.generalOfferTypeServic$.generalOfferTypeForm.get("offerRequestOffers") as UntypedFormArray;
        let sgroup = oROArray.at(0)?.get("storeGroupVersion").get("storeGroup") as UntypedFormGroup;

        if (toString.call(region) === "[object Object]" && !this.isUpdatingValueValidity) {

            const podStoreGroupIds = this.generalOfferTypeServic$.getDropDownList([region.storeGroupName], [region.storeGroupId]);
            sgroup.get("digitalRedemptionStoreGroupNames").setValue([region.storeGroupName]);
            sgroup.get("digitalRedemptionStoreGroupIds").setValue(podStoreGroupIds);
            /** GR SPD BPD needs this logic*/
            if (this.getProgramCode() == CONSTANTS.GR || CONSTANTS.SPD) {
                sgroup.get("podStoreGroupNames").setValue([region.storeGroupName]);
                sgroup.get("podStoreGroupIds").setValue(podStoreGroupIds);
            }
        } else {
            const storeGroupRegion = storeGroupRegions.filter((ele) => ele.name === region)[0];
            if (!storeGroupRegion) {
                return false;
            }
            const storeGNames = [storeGroupRegion.storeGroupName];
            const storeGIds = [storeGroupRegion.storeGroupId];

            // Static mapping for multiple Store Group for Region IDs 19, 25, 27, 29
            const regionIdToSGIdMapping = {
                "19": "27",
                "25": "29",
                "27": "19",
                "29": "25",
            };
            /** GR SPD BPD needs this logic*/
            if (
                this.getProgramCode() == CONSTANTS.SPD &&
                storeGroupRegion.code &&
                Object.keys(regionIdToSGIdMapping).includes(storeGroupRegion.code)
            ) {
                const matchedSGId = regionIdToSGIdMapping[storeGroupRegion.code]?.toString();
                /** filter with region id - as the region ids will not change */
                const sg = {
                    storeGroupName: storeGroupRegions.filter((ele) => ele.code === matchedSGId)[0]?.storeGroupName,
                    storeGroupId: storeGroupRegions.filter((ele) => ele.code === matchedSGId)[0]?.storeGroupId,
                };
                if (sg?.storeGroupName && sg?.storeGroupId) {
                    storeGNames.push(sg.storeGroupName);
                    storeGIds.push(sg.storeGroupId);
                }
            }

            const podStoreGroupIds = this.generalOfferTypeServic$.getDropDownList(storeGNames, storeGIds);
            sgroup.get("digitalRedemptionStoreGroupNames").setValue(storeGNames);
            sgroup.get("digitalRedemptionStoreGroupIds").setValue(podStoreGroupIds);
            if (this.getProgramCode() == CONSTANTS.GR || CONSTANTS.SPD) {
                sgroup.get("podStoreGroupNames").setValue([region.storeGroupName]);
                sgroup.get("podStoreGroupIds").setValue(podStoreGroupIds);
            }
        }
    }
    isRewardFreq(programRules): boolean {
        const usageLimitTypePerUser = this.requestForm?.get("rules")?.get("usageLimitTypePerUser")?.value;
        const usageLimitValidators = programRules?.offerRequest?.usageLimitPerUser?.validators || [];
        const customPeriodValidators = programRules?.offerRequest?.customPeriod?.validators || [];
    
        if (usageLimitTypePerUser === "CUSTOM") {
            if (!usageLimitValidators.includes(Validators.required)) {
                usageLimitValidators.push(Validators.required);
            }
            if (!customPeriodValidators.includes(Validators.required)) {
                customPeriodValidators.push(Validators.required);
            }
            return true;
        }
    
        usageLimitValidators.length && usageLimitValidators.splice(0, usageLimitValidators.length);
        customPeriodValidators.length && customPeriodValidators.splice(0, customPeriodValidators.length);
        return false;
    }
    
   
    setCustomFields() {
        //sets value of custom type to null when offer usage limit is not custom

        const offerUsageLimit = this.requestForm?.get("rules")?.get("usageLimitTypePerUser")?.value;
        if (offerUsageLimit) {
            if (offerUsageLimit == "CUSTOM") {
                this.customTypeCtrl?.setValue(REQUEST_CONSTANTS.START_OF_INCENTIVE);
            }
            if (offerUsageLimit !== "CUSTOM") {
                this.customTypeCtrl?.setValue(null);
                this.customLimitCtrl?.setValue(null);
                this.customPeriodCtrl?.setValue(null);
            }
            this.requestFormService$?.selectedOfferLimitType$?.next(offerUsageLimit);
        }
    }
  setDisplayStartDate() {
    this.updateDisplayStartDate(this.requestForm);
  }

  getSubType(programType) {
    let getSubProgram = `${this.getProgramSubtypeAPI}${programType}`;
    return this.http$.get(getSubProgram, { headers: new HttpHeaders(this.getHeaders()) });
    }
 
}

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from '../../../shared/services/common/common.service';
import { PluCommonService } from './pluCommon.service';
import { PLU_CONSTANTS } from '@appRequest/constants/plu_constants';
import { InitialDataService } from '@appServices/common/initial.data.service';

// Mock for InitialDataService
class InitialDataServiceMock {
  getConfigUrls(param) {
    return `${param}-mock-url`;  // Mock response
  }
}

describe('PluCommonService', () => {
  let service: PluCommonService;
  let httpMock: HttpTestingController;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let commonServiceMock: jasmine.SpyObj<CommonService>;

  beforeEach(() => {
    const toastrSpy = jasmine.createSpyObj('ToastrService', ['success']);
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['getHeaders']);

    commonServiceSpy.getHeaders.and.returnValue({
      'X-Albertsons-userAttributes': 'user123',
      'X-Albertsons-Client-ID': 'client123',
      'content-type': 'application/json'
    });

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        PluCommonService,
        { provide: ToastrService, useValue: toastrSpy },
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: InitialDataService, useClass: InitialDataServiceMock }, // Mock class
      ],
    });

    service = TestBed.inject(PluCommonService);
    httpMock = TestBed.inject(HttpTestingController);
    toastrServiceMock = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    commonServiceMock = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
  });

  // Test case for `getPluAuditKeys`
  it('should return correct initial updatePluAuditKeys', () => {
    const result = service.getPluAuditKeys();
    expect(result).toEqual({
      id: null,
      updatedUser: null,
      lastUpdatedTs: null,
      createdApplicationId: 'OMS',
      createdTs: null,
      createdUser: null,
      lastUpdatedApplicationId: null,
    });
  });

  // Test case for `updatePluDataKeys`
  it('should update updatePluAuditKeys correctly', () => {
    const updateData = {
      id: 1,
      updatedUser: 'test-user',
      lastUpdatedTs: '2025-01-01T12:00:00',
      createdApplicationId: 'NEW_APP',
      createdTs: '2025-01-01T12:00:00',
      createdUser: 'test-creator',
      lastUpdatedApplicationId: 'NEW_APP_ID',
    };

    service.updatePluDataKeys(updateData);

    const result = service.getPluAuditKeys();
    expect(result).toEqual(updateData);
  });

  // Test case for `deletePluReservation`
  it('should call delete API with correct parameters for pluManagement page', () => {
    const pluFormData = { id: 123, name: 'Test PLU' };
    const page = 'pluManagement';

    const expectedRequestBody = { ...pluFormData };

    service.deletePluReservation(pluFormData, page).subscribe();

    const req = httpMock.expectOne(`${PLU_CONSTANTS.PLU_TRIGGER_API}-mock-url`);  // Expect the URL returned by InitialDataServiceMock
    expect(req.request.method).toBe('DELETE');
    expect(req.request.body).toEqual(expectedRequestBody);
    expect(req.request.headers.get('content-type')).toBe('application/json');
    req.flush({}); // Mock the API response
  });

  it('should call delete API with correct parameters for other page', () => {
    const pluFormData = { id: 123, name: 'Test PLU' };
    const page = 'otherPage';

    const expectedRequestBody = { ...pluFormData, ...service.getPluAuditKeys() };

    service.deletePluReservation(pluFormData, page).subscribe();

    const req = httpMock.expectOne(`${PLU_CONSTANTS.PLU_TRIGGER_API}-mock-url`);  // Expect the URL returned by InitialDataServiceMock
    expect(req.request.method).toBe('DELETE');
    expect(req.request.body).toEqual(expectedRequestBody);
    expect(req.request.headers.get('content-type')).toBe('application/json');
    req.flush({}); // Mock the API response
  });

  // Test case for `showSuccessToastr`
  it('should call toastr success with the correct message', () => {
    const msg = 'Success message';
    service.showSuccessToastr(msg);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(msg, '', {
      timeOut: 1000,
      closeButton: true,
    });
  });

  afterEach(() => {
    httpMock.verify(); // Ensure no outstanding HTTP requests
  });
});

import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { PLU_CONSTANTS } from "@appModules/request/constants/plu_constants";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { ToastrService } from "ngx-toastr";
import { CommonService } from "../../../shared/services/common/common.service";

@Injectable({
  providedIn: "root",
})
export class PluCommonService {
  updatePluAuditKeys: any = {
    id: null,
    updatedUser: null,
    lastUpdatedTs: null,
    createdApplicationId: CONSTANTS.OMS,
    createdTs: null,
    createdUser: null,
    lastUpdatedApplicationId: null
  };
  constructor(
    private _initialDataService: InitialDataService,
    private _http: HttpClient,
    private toastrService: ToastrService,
    private commonService: CommonService
  ) {
    // intentionally left empty
  }
  pluTriggerAPI: string = this._initialDataService.getConfigUrls(
    PLU_CONSTANTS.PLU_TRIGGER_API
  );

  updatePluDataKeys(obj) {
    this.updatePluAuditKeys = {
      id: obj.id ? obj.id : null,
      updatedUser: obj.updatedUser ? obj.updatedUser : null,
      lastUpdatedTs: obj.lastUpdatedTs ? obj.lastUpdatedTs : null,
      createdApplicationId: obj.createdApplicationId
        ? obj.createdApplicationId
        : null,
      createdTs: obj.createdTs ? obj.createdTs : null,
      createdUser: obj.createdUser ? obj.createdUser : null, 
      lastUpdatedApplicationId: obj.lastUpdatedApplicationId ? obj.lastUpdatedApplicationId : null
    };
  }
  deletePluReservation(pluFormData, page) {
    let reqBody;
    if(page == 'pluManagement') {
      reqBody = { ...pluFormData };
    } else {
      reqBody = { ...pluFormData, ...this.updatePluAuditKeys };
    }
    const httpOptions = {
      headers: this.commonService.getHeaders(),
      body: reqBody,
    };
    return this._http.delete(this.pluTriggerAPI, httpOptions);
  }
  getPluAuditKeys() {
    return {
      ...this.updatePluAuditKeys,
    };
  }
  showSuccessToastr(msg) {
    this.toastrService.success(msg, "", {
      timeOut: 1000,
      closeButton: true,
    });
  }
}

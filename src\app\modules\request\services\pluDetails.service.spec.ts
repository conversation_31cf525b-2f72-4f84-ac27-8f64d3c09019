import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { CommonService } from '../../../shared/services/common/common.service';
import { PluCommonService } from './pluCommon.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { HttpParams } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';
import { dateInOriginalFormat } from '@appUtilities/date.utility';
import { PluDetailsService } from './pluDetails.service';
import { PLU_CONSTANTS } from '@appRequest/constants/plu_constants';

// Mock InitialDataService class
class InitialDataServiceMock {
  getConfigUrls(param) {
    return `${param}-mock-url`;
  }
}

describe('PluDetailsService', () => {
  let service: PluDetailsService;
  let httpMock: HttpTestingController;
  let commonServiceMock: jasmine.SpyObj<CommonService>;
  let pluCommonServiceMock: jasmine.SpyObj<PluCommonService>;
  let initialDataServiceMock: InitialDataServiceMock;

  beforeEach(() => {
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['getHeaders']);
    const pluCommonServiceSpy = jasmine.createSpyObj('PluCommonService', ['getPluAuditKeys']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        PluDetailsService,
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: PluCommonService, useValue: pluCommonServiceSpy },
        { provide: InitialDataService, useClass: InitialDataServiceMock }, // Use the mock class here
      ],
    });

    service = TestBed.inject(PluDetailsService);
    httpMock = TestBed.inject(HttpTestingController);
    commonServiceMock = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    pluCommonServiceMock = TestBed.inject(PluCommonService) as jasmine.SpyObj<PluCommonService>;
    initialDataServiceMock = TestBed.inject(InitialDataService) as unknown as InitialDataServiceMock; // Casting to mock class
  });

  // Test case for mapping PLU payload object (Create action)
  it('should correctly map PLU payload object for create action', () => {
    const mockPluForm = {
      value: {
        division: 'division1',
        department: 'department1',
        code: 'PLU123',
        pluRangeName: 'range1',
        codeType: 'TypeA',
        startDate: '2025-01-01',
        endDate: '2025-01-31',
        itemDescription: 'Item description',
        addlDescription: 'Additional description',
      },
    };

    service.pluForm = mockPluForm as any; // Assign mock form to the service

    // Mock `getPluAuditKeys` response
    pluCommonServiceMock.getPluAuditKeys.and.returnValue({
      id: 1,
      updatedUser: 'user123',
      lastUpdatedTs: '2025-01-01T12:00:00Z',
      createdApplicationId: 'OMS',
      createdTs: '2025-01-01T12:00:00Z',
      createdUser: 'user123',
      lastUpdatedApplicationId: 'OMS',
    });

    service.mapPLUpayLoadObj('create');

    expect(service.pluData).toEqual({
      division: 'division1',
      department: 'department1',
      code: 'PLU123',
      pluRangeName: 'range1',
      codeType: 'TypeA',
      startDate: '2025-01-01T00:00:00.000-07:00',
      endDate: '2025-01-31T00:00:00.000-07:00',
      itemDescription: 'Item description',
      addlDescription: 'Additional description',
    });
  });

  // Test case for `submitPluData`
  it('should make a POST request to submit PLU data', () => {
    const mockPluForm = {
      value: {
        requestedNumberOfCodes: '10',
        division: 'division1',
        department: 'department1',
        code: 'PLU123',
        pluRangeName: 'range1',
        codeType: 'TypeA',
        startDate: '2025-01-01',
        endDate: '2025-01-31',
        itemDescription: 'Item description',
        addlDescription: 'Additional description',
      },
    };

    service.pluForm = mockPluForm as any; // Assign mock form to the service
    const validateKey = 'validateKey123';

    // Mock API URL
    initialDataServiceMock.getConfigUrls = jasmine.createSpy().and.returnValue('mock-url');

    // Mock headers with the required "content-type"
    const mockHeaders = { 
      'X-Albertsons-userAttributes': 'user123', 
      'X-Albertsons-Client-ID': 'client123',
      'content-type': 'application/json' // Add content-type here
    };
    commonServiceMock.getHeaders.and.returnValue(mockHeaders);

    // Call submitPluData
    service.submitPluData(validateKey).subscribe();

    const req = httpMock.expectOne(`${PLU_CONSTANTS.PLU_TRIGGER_API}-mock-url`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      ...service.pluData,
      validatePluTriggerBarcodeAndRetailSectionCombination: validateKey,
      reqObj: { headers: mockHeaders, params: new HttpParams().set('requestedNumberOfCodes', '10') },
    });
    req.flush({}); // Simulate the response
  });

  // Test case for `getPluCodeData`
  it('should make a POST request to get PLU code data', () => {
    const query = 'test-query';
    
    // Mock headers with the required "content-type"
    const mockHeaders = { 
      'X-Albertsons-userAttributes': 'user123', 
      'X-Albertsons-Client-ID': 'client123',
      'content-type': 'application/json' // Add content-type here
    };
    commonServiceMock.getHeaders.and.returnValue(mockHeaders);

    service.getPluCodeData(query).subscribe();

    const req = httpMock.expectOne(`${PLU_CONSTANTS.PLU_SEARCH_API}-mock-url`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query,
      includeTotalCount: true,
      includeFacetCounts: true,
      reqObj: { headers: mockHeaders },
    });
    req.flush({}); // Simulate the response
  });

  it('should make a PUT request to update PLU data', () => {
    const mockPluForm = {
      value: {
        division: 'division1',
        department: 'department1',
        code: 'PLU123',
        pluRangeName: 'range1',
        codeType: 'TypeA',
        startDate: '2025-01-01',
        endDate: '2025-01-31',
        itemDescription: 'Item description',
        addlDescription: 'Additional description',
      },
    };

    service.pluForm = mockPluForm as any; // Assign mock form to the service

    // Mock `getPluAuditKeys` response
    pluCommonServiceMock.getPluAuditKeys.and.returnValue({
      id: 1,
      updatedUser: 'user123',
      lastUpdatedTs: '2025-01-01T12:00:00Z',
      createdApplicationId: 'OMS',
      createdTs: '2025-01-01T12:00:00Z',
      createdUser: 'user123',
      lastUpdatedApplicationId: 'OMS',
    });

    service.updatePluData().subscribe();

    const req = httpMock.expectOne(`${PLU_CONSTANTS.PLU_TRIGGER_API}-mock-url`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual({
      ...service.pluData,
      reqObj: { headers: commonServiceMock.getHeaders() },
    });
    req.flush({}); // Simulate the response
  });

  it('should make a GET request to fetch PLU trigger data', () => {
    // Mock API URL
    initialDataServiceMock.getConfigUrls = jasmine.createSpy().and.returnValue('mock-url');
    
    service.getPLUTriggerData().subscribe();

    const req = httpMock.expectOne(`${PLU_CONSTANTS.PLU_TRIGGER_CODE_API}-mock-url`);
    expect(req.request.method).toBe('GET');
    req.flush({}); // Simulate the response
  });


  afterEach(() => {
    httpMock.verify(); // Ensure there are no outstanding requests
  });
});

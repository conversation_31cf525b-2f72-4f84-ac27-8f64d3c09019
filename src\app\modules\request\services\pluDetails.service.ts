import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from "@angular/core";
import { UntypedFormGroup } from '@angular/forms';
import { PLU_CONSTANTS } from "@appModules/request/constants/plu_constants";
import { InitialDataService } from '@appServices/common/initial.data.service';
import { dateInOriginalFormat } from '@appUtilities/date.utility';
import { BehaviorSubject } from 'rxjs';
import { CommonService } from '../../../shared/services/common/common.service';
import { PluCommonService } from './pluCommon.service';



@Injectable({
  providedIn: "root",
})
export class PluDetailsService {
  pluForm: UntypedFormGroup;
  pluData:any;
  isDraftSaveAttempted= new BehaviorSubject(false);
  constructor(private _initialDataService: InitialDataService, 
    private _http: HttpClient,
    private commonService: CommonService,
    private pluCommonService: PluCommonService) {
      // intentionally left empty
  }
  pluTriggerAPI: string = this._initialDataService.getConfigUrls(PLU_CONSTANTS.PLU_TRIGGER_API);
  pluTriggerCodesAPI: string = this._initialDataService.getConfigUrls(PLU_CONSTANTS.PLU_TRIGGER_CODE_API);
  pluSearchAPI: string = this._initialDataService.getConfigUrls(PLU_CONSTANTS.PLU_SEARCH_API);


  mapPLUpayLoadObj(action) {
    let  {
      division,
      department,
      code,
      pluRangeName,
      codeType ,
      startDate,
      endDate,
      itemDescription,
      addlDescription,
    } = this.pluForm && this.pluForm.value;
    startDate = dateInOriginalFormat({
      date: startDate,
      isStartDate: true,
    });
    endDate = dateInOriginalFormat({
      date: endDate,
      isStartDate: false,
    });
    this.pluData = {
      division,
      department,
      code,
      pluRangeName,
      codeType ,
      startDate,
      endDate,
      itemDescription,
      addlDescription,
    }; 
    if(action ==='update') {
      this.pluData = {
        ...this.pluData,
        ...this.pluCommonService.getPluAuditKeys()
      };
    }
  }
  
  getPLUTriggerData(){
    return this._http.get(this.pluTriggerCodesAPI)
  }
  getPluCodeData(query: string) {
    let searchInput = { query, includeTotalCount: true, includeFacetCounts: true, reqObj: { headers: this.commonService.getHeaders() } };
    return this._http.post(this.pluSearchAPI, searchInput);
  }

  submitPluData(validateKey){
    this.isDraftSaveAttempted.next(true);
    this.mapPLUpayLoadObj('create');
    const reqBody: any = this.pluData;
    let params = new HttpParams().set('requestedNumberOfCodes', this.pluForm.value.requestedNumberOfCodes);
    let payload = { ...reqBody,validatePluTriggerBarcodeAndRetailSectionCombination: validateKey, reqObj: { headers: this.commonService.getHeaders(), params:params } };

    return this._http.post(this.pluTriggerAPI, payload)
  }

  updatePluData(){
    this.isDraftSaveAttempted.next(true);
    this.mapPLUpayLoadObj('update');
    const reqBody: any = this.pluData;
    let payload = { ...reqBody, reqObj: { headers: this.commonService.getHeaders()} };

    return this._http.put(this.pluTriggerAPI, payload)
  }
}

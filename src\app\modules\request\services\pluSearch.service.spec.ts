import { TestBed } from "@angular/core/testing";
import { HttpClientTestingModule, HttpTestingController } from "@angular/common/http/testing";
import { PluSearchService } from "./pluSearch.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { CommonService } from "@appServices/common/common.service";
import { QueryGenerator } from "../../../shared/services/common/queryGenerator.service";
import { BehaviorSubject, of } from "rxjs";

describe("PluSearchService", () => {
    let service: PluSearchService;
    let httpMock: HttpTestingController;
    let commonServiceSpy: jasmine.SpyObj<CommonService>;
    let queryGeneratorSpy: jasmine.SpyObj<QueryGenerator>;
    const apiUrl = "/api/plu-search";

    beforeEach(() => {
        const initialDataSpy = jasmine.createSpyObj("InitialDataService", ["getConfigUrls"]);
        const commonSpy = jasmine.createSpyObj("CommonService", ["getHeaders"]);
        const querySpy = jasmine.createSpyObj("QueryGenerator", ["getQuery"]);

        initialDataSpy.getConfigUrls.and.returnValue(apiUrl);
        commonSpy.getHeaders.and.returnValue({
            "X-Albertsons-userAttributes": "some-value",
            "X-Albertsons-Client-ID": "some-client-id",
            "content-type": "application/json"
        });

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                PluSearchService,
                { provide: InitialDataService, useValue: initialDataSpy },
                { provide: CommonService, useValue: commonSpy },
                { provide: QueryGenerator, useValue: querySpy }
            ]
        });

        service = TestBed.inject(PluSearchService);
        httpMock = TestBed.inject(HttpTestingController);
        commonServiceSpy = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
        queryGeneratorSpy = TestBed.inject(QueryGenerator) as jasmine.SpyObj<QueryGenerator>;
    });

    afterEach(() => {
        httpMock.verify();
    });

    it("should be created", () => {
        expect(service).toBeTruthy();
    });

    it("should initialize BehaviorSubjects with default values", () => {
        expect(service.pluListSearchData$).toBeInstanceOf(BehaviorSubject);
        expect(service.pluManagementPagination$).toBeInstanceOf(BehaviorSubject);
        expect(service.pluListSearchData$.value).toBeFalse();
        expect(service.pluManagementPagination$.value).toBeFalse();
    });

    it("should construct the correct API URL", () => {
        expect(service.getPluListAPI).toBe(apiUrl);
    });

    it("should make an API call in makePluApi method", () => {
        const mockQuery = "SELECT * FROM PLU";
        queryGeneratorSpy.getQuery.and.returnValue(mockQuery);

        service.makePluApi().subscribe(response => {
            expect(response).toBeTruthy();
        });

        const req = httpMock.expectOne(apiUrl);
        expect(req.request.method).toBe("POST");
        expect(req.request.body).toEqual({
            query: mockQuery,
            includeTotalCount: true,
            reqObj: { headers: commonServiceSpy.getHeaders() }
        });

        req.flush({ data: [] });
    });

    it("should handle API errors gracefully", () => {
        const mockQuery = "SELECT * FROM PLU";
        queryGeneratorSpy.getQuery.and.returnValue(mockQuery);

        service.makePluApi().subscribe(
            () => fail("Should have failed with 500 error"),
            (error) => {
                expect(error).toBeDefined();
                expect(error.status).toBe(500);
            }
        );

        const req = httpMock.expectOne(apiUrl);
        req.flush("Server error", { status: 500, statusText: "Internal Server Error" });
    });

    describe('updatePagination', () => {
        it('should handle null criteria gracefully', () => {
            service.updatePagination(null as any);
            expect(service.pluManagementPagination$.value).toBeNull();
        });

        it('should handle undefined criteria gracefully', () => {
            service.updatePagination(undefined as any);
            expect(service.pluManagementPagination$.value).toBeUndefined();
        });

        it('should update pagination with an edge case criteria', () => {
            const criteria = { page: 1, pageSize: 10 };
            service.updatePagination(criteria);
            expect(service.pluManagementPagination$.value).toEqual(jasmine.objectContaining({ page: 1, pageSize: 10 }));
        });
    });


    describe('fetchPluList', () => {
        let mockApiResponse: any;

        beforeEach(() => {
            mockApiResponse = {
                totalCount: 100,
                current: 2,
                sid: 'ABC123',
                pluTriggerCodeReservations: [{ id: 1, name: 'PLU Item 1' }]
            };

            spyOn(service, 'makePluApi').and.returnValue(of(mockApiResponse));
            spyOn(service, 'updatePagination');
            spyOn(service.pluListSearchData$, 'next');
        });

        it('should call makePluApi and update state on success', () => {
            service.fetchPluList({});

            expect(service.makePluApi).toHaveBeenCalled();
            expect(service.updatePagination).toHaveBeenCalledWith({
                totalCount: mockApiResponse.totalCount,
                pageNumber: mockApiResponse.current,
                sid: mockApiResponse.sid
            });
            expect(service.pluListSearchData$.next).toHaveBeenCalledWith(mockApiResponse.pluTriggerCodeReservations);
        });

        it('should not update state if API response is null', () => {
            (service.makePluApi as jasmine.Spy).and.returnValue(of(null));

            service.fetchPluList({});

            expect(service.updatePagination).not.toHaveBeenCalled();
            expect(service.pluListSearchData$.next).not.toHaveBeenCalled();
        });

        it('should not update state if API response is undefined', () => {
            (service.makePluApi as jasmine.Spy).and.returnValue(of(undefined));

            service.fetchPluList({});

            expect(service.updatePagination).not.toHaveBeenCalled();
            expect(service.pluListSearchData$.next).not.toHaveBeenCalled();
        });
    });

});
import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { PLU_CONSTANTS } from "@appModules/request/constants/plu_constants";
import { CommonService } from "@appServices/common/common.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import { BehaviorSubject } from "rxjs";
import { QueryGenerator } from "../../../shared/services/common/queryGenerator.service";

@Injectable({
  providedIn: "root",
})
export class PluSearchService extends UnsubscribeAdapter {
  constructor(
    private _initialDataService: InitialDataService,
    private _http: HttpClient,
    public commonService: CommonService,
    private queryGenerator: QueryGenerator
  ) {
    super();
  }
  getPluListAPI: string = this._initialDataService.getConfigUrls(PLU_CONSTANTS.PLU_SEARCH_API);
  pluListSearchData$ = new BehaviorSubject(false);
  pluManagementPagination$ = new BehaviorSubject(false);

  public makePluApi() {
    let query = this.queryGenerator.getQuery(),
      searchInput = { query, includeTotalCount: true, reqObj: { headers: this.commonService.getHeaders() } };

    return this._http.post(this.getPluListAPI, searchInput);
  }

  public updatePagination(criteria: any) {
    this.pluManagementPagination$.next(criteria);
  }

  public fetchPluList(obj) {
    const self = (obj && obj.this) || this;

    self.subs.sink = self.makePluApi().subscribe((data: any) => {
      if (!data) {
        return false;
      }

      self.updatePagination({ totalCount: data.totalCount, pageNumber: data.current, sid: data.sid });
      self.pluListSearchData$.next(data.pluTriggerCodeReservations);
    });
  }
}

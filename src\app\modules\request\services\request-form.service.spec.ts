import { HttpClientTestingModule } from "@angular/common/http/testing";
import * as moment from 'moment';
import { TestBed } from "@angular/core/testing";

import { APP_BASE_HREF } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { FileAttachService } from "@appServices/common/file-attach.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { SearchUsersService } from "@appServices/common/search-users.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import appData from "@appSpecMocks/appData";
import { generalOfferTypeFormVal } from "@appSpecMocks/request/generalOfferTypeFormValue";
import { setIncludeProductGroupNameData } from "@appSpecMocks/request/setIncludeProductGroupNameData";
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject, Subject, of, throwError } from "rxjs";
import { RequestFormService } from "./request-form.service";
import { SearchOfferRequestService } from "./search-offer-request.service";

const offerRequests = require("@appSpecMocks/offerRequests.json");

describe("RequestFormService", () => {
  let service: RequestFormService;

  beforeEach(() => {
    const authServiceStub = () => ({
      getTokenString: () => ({}),
      onUserDataAvailable: (arg) => ({}),
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({}),
      isuppEnabled: () => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({}),
      getConfigUrls: (cLONE_API) => ({}),
      getAppDataName: () => ({}),
    });
    const queryGeneratorStub = () => ({
      setQuery: (string) => ({}),
      pushParameters: (object) => ({}),
      getQuery: () => ({}),
    });
    const searchOfferRequestServiceStub = () => ({
      currentOfferRequestsCreateObvl: { subscribe: (f) => f({}) },
      searchOfferRequest: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      setOfferDetailsReqToObsvbl: (response) => ({}),
      facetItemService: {},
    });
    const toastrServiceStub = () => ({
      success: (successMessage, string, object) => ({}),
      warning: (string, string1, object) => ({}),
    });
    const fileAttachServiceStub = () => ({
      uploadFile: (file, id) => ({ map: () => ({}) }),
    });
    const searchUsersServiceStub = () => ({ getUsers: (term) => ({}) });
    const commonRouteServiceStub = () => ({currentActivatedRoute: "request"})
    const commonSearchServiceStub =() =>({});
    const generalOfferTypeServiceStub = () => ({
      facetItemService: {
        programCodeSelected: "SPD",
      },
      getValueByKey: {},
      addNewRow$: new BehaviorSubject(""),
      generalOfferTypeForm: {
        value: {
          generalInformationForm: { type: {}, isGiftCard: {}, tiers: {} },
          offerRequestOffers: {
            storeGroupVersion: { productGroupVersions: {}, storeGroup: {} },
            forEach: () => ({}),
          },
        },
        get: () => ({ value: {} }),
        markAsUntouched: () => ({}),
        valid: {},
        markAsPristine: () => ({}),
      },
      getKeyByValue: (arg, type) => ({}),
      isReqSubmitAttempted$: { next: () => ({}) },
    });
    const requestFormServiceStub = () => ({
      requestData$: new BehaviorSubject({
          info: {
              digitalStatus: 'P',
              offerRequestType: ''
          },
          rules: {
              qualificationAndBenefit: {
                  offerRequestOffers: {}
              }
          }
      }),
      selectedOfferType$: new BehaviorSubject({}),
      selectedChannelForTier$: new BehaviorSubject({}),
      selectedOfferLimitTypeForTier$: new BehaviorSubject({}),
      selectedChannel$: new BehaviorSubject({}),
      selectedOfferLimitType$: new BehaviorSubject(false),
      cloningProcess$: new BehaviorSubject(false)

  });

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        RequestFormService,
        { provide: APP_BASE_HREF, useValue: '/' },
        { provide: AuthService, useFactory: authServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: SearchOfferRequestService, useFactory: searchOfferRequestServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: FileAttachService, useFactory: fileAttachServiceStub },
        { provide: SearchUsersService, useFactory: searchUsersServiceStub },
        { provide: GeneralOfferTypeService, useFactory: generalOfferTypeServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub}
      ],
    });
    service = TestBed.inject(RequestFormService);
    service.appData = appData;
  });

  it("can load instance", () => {
    expect(service).toBeTruthy();
  });
  it(`isSavedFromNavigationOverlay has default value`, () => {
    expect(service.isSavedFromNavigationOverlay).toEqual(false);
  });
  it("isValidOfferExists defaults to: false", () => {
    expect(service.isValidOfferExists).toEqual(false);
  });
  it("attachedFilesList defaults to: []", () => {
    expect(service.attachedFilesList).toEqual([]);
  });
  it("uploadedFilesList defaults to: []", () => {
    expect(service.uploadedFilesList).toEqual([]);
  });
  it(`uploadedFilesList has default value`, () => {
    expect(service.uploadedFilesList).toEqual([]);
  });
  it(`isDisplayNavigationWarning has default value`, () => {
    expect(service.isDisplayNavigationWarning).toEqual(true);
  });
  
  describe("#isValidEnterPriseInstantWinRule", () => {
    it("should check if valid or not and return false", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({
          usageLimitTypePerUser: new UntypedFormControl("UNLIMITED"),
        }),
      });
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("INSTANT_WIN"),
        }),
      });
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("INSTANT_WIN");
      let result = service.isValidEnterPriseInstantWinRule();
      expect(result).toEqual(false);
    });
    it("should check if valid or not and return true", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({
          usageLimitTypePerUser: new UntypedFormControl("LIMITED"),
        }),
      });
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("INSTANT_WIN"),
        }),
      });
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("INSTANT_WIN");
      let result = service.isValidEnterPriseInstantWinRule();
      expect(result).toEqual(true);
    });
  });
  describe("#isValidRewardAcumulationRule", () => {
    it("should check if valid or not and return true ", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({
          usageLimitTypePerUser: new UntypedFormControl("UNLIMITED"),
        }),
      });
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("REWARDS_ACCUMULATION"),
        }),
      });
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("REWARDS_ACCUMULATION");
      let result = service.isValidRewardAcumulationRule();
      expect(result).toEqual(true);
    });
    it("should check if valid or not and return false", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({
          usageLimitTypePerUser: new UntypedFormControl("LIMITED"),
        }),
      });
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("REWARDS_ACCUMULATION"),
        }),
      });
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("REWARDS_ACCUMULATION");
      let result = service.isValidRewardAcumulationRule();
      expect(result).toEqual(false);
    });
  });
  xdescribe("initSubscribes", () => {
    it("makes expected calls", () => {
      spyOn(service, "setSearchResponse");
      // (<jasmine.Spy>service.initSubscribes);
      service.initSubscribes();
      expect(service.setSearchResponse).toHaveBeenCalled();
    });
  });
  describe("getHeaders", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = TestBed.inject(AuthService);
      spyOn(authServiceStub, "getTokenString");
      service.getHeaders();
      expect(authServiceStub.getTokenString).toHaveBeenCalled();
    });
  });
  describe("saveAndSubmitOfferRequest", () => {
    it("makes expected calls", () => {
      service.requestData = {
        info: {
          digitalUser: "pjain",
          nonDigitalUser: "oha",
          validatePluTriggerBarcode: true,
          id: "5656",
        },
      };
      service.saveAnywayPLU = true;
      spyOn(service, "getHeaders");
      const http: HttpClient = TestBed.inject(HttpClient);
      spyOn(http, "put").and.returnValue(of({}));
      spyOn(service, "postSave");
      service.saveAndSubmitOfferRequest(true);

    });
    it("throw error while update", () => {
      service.requestData = {
        info: {
          digitalUser: "pjain",
          nonDigitalUser: "oha",
          validatePluTriggerBarcode: true,
          id: "5656",
        },
      };
      service.saveAnywayPLU = true;
      spyOn(service, "getHeaders");
      const http: HttpClient = TestBed.inject(HttpClient);
      spyOn(http, "put").and.returnValue(throwError("Error"));
      spyOn(service, "handleWarnings");
      service.saveAndSubmitOfferRequest(true);

    });
    it("throw error while create", () => {
      service.requestData = {
        info: {
          digitalUser: "pjain",
          nonDigitalUser: "oha",
          validatePluTriggerBarcode: true,
          id: null,
        },
      };
      service.saveAnywayPLU = true;
      spyOn(service, "getHeaders");
      const http: HttpClient = TestBed.inject(HttpClient);
      spyOn(http, "post").and.returnValue(throwError("Error"));
      spyOn(service, "handleWarnings");
      service.saveAndSubmitOfferRequest(true);
    });
    it("makes expected calls", () => {
      service.requestData = {
        info: {
          digitalUser: "pjain",
          nonDigitalUser: "oha",
          validatePluTriggerBarcode: true,
          id: null,
        },
      };
      service.saveAnywayPLU = true;
      spyOn(service, "getHeaders");
      const http: HttpClient = TestBed.inject(HttpClient);
      spyOn(http, "post").and.returnValue(of({}));
      spyOn(service, "postSave");
      service.saveAndSubmitOfferRequest(true);
    });
  });
  describe("resetSubmitValidations", () => {
    it("makes expected calls", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({
          pluTriggerBarcode: new UntypedFormControl(""),
          adType: new UntypedFormControl(""),
          offerEffectiveStartDate: new UntypedFormControl(null),
          offerEffectiveEndDate: new UntypedFormControl(null),
          usageLimitTypePerUser: new UntypedFormControl("ONCE_PER_OFFER"),
        }),
      });
      service.resetSubmitValidations();
    });
  });
  describe("saveSubmitOR", () => {
    it("makes expected calls", () => {
      spyOn(service, "isFormValidPostSave").and.returnValue(true);
      spyOn(service, "mapFormDataToReqObj");
      const authServiceStub: AuthService = TestBed.inject(AuthService);
      spyOn(authServiceStub, "onUserDataAvailable");
      service.saveSubmitOR(true);
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("getReqId", () => {
    it("makes expected calls", () => {
      const k = service.getReqId({ 1517257655: "updated" }, "updated");
      expect(k).toEqual("1517257655");
    });
  });

  describe("setUpdateNotification", () => {
    it("should execute successfully", () => {
      const k = service.setUpdateNotification({ editStatus: "U" }, { editStatus: "U" });
    });
  });

  describe("postSave", () => {
    it("should execute successfully", () => {
      service.requestForm = new UntypedFormGroup({});
      service.requestData = offerRequests.offerRequests.offerRequests[0];
      service.compRouter = {
        navigateByUrl: (a) => {},
      };
      spyOn(service, "navigateToSummary");
      service.postSave({ 1517257655: "updated" }, "updated", false);
    });
  });

  describe("navigateToSummary", () => {
    it("should make expected calls", () => {
      const toastrServiceStub: ToastrService = TestBed.inject(ToastrService);
      service.compRouter = {
        navigateByUrl: (a) => {},
      };
      spyOn(service, "getReqDetails");
      spyOn(toastrServiceStub, "success");

      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      Object.assign(generalOfferTypeServiceStub, { facetItemService: { programCodeSelected: "SC" } });
      service.navigateToSummary("offer Request saved", 1);
      expect(service.getReqDetails).toHaveBeenCalled();
      expect(toastrServiceStub.success).toHaveBeenCalled();
    });
  });

  describe("subscribeCurrentOfferReqForProcess", () => {
    it("should return null if requestData is not available", () => {
      const k = service.subscribeCurrentOfferReqForProcess();
      expect(k).toBeNull();
    });
    it("should return data if requestData is  available", () => {
      service.requestData$.next({ info: { id: 2 }, lastUpdatedTs: "43" });
      const k = service.subscribeCurrentOfferReqForProcess();
      expect(k.id).toEqual(2);
    });
  });

  describe("subscribeCurrentEditingUser", () => {
    it("should return null if requestData is not available", () => {
      service.requestData$.next({ info: { id: 2 }, lastUpdatedTs: "43" });
      const k = service.subscribeCurrentEditingUser();
      expect(k).toBeNull();
    });

    it("should return digitalEditStatus.userId if editStatus is  E", () => {
      service.requestEditUpdateData$.next({ digitalEditStatus: { editStatus: "E", userId: 2 } });
      const k = service.subscribeCurrentEditingUser();
      expect(k).toEqual(2);
    });
    it("should return nonDigitalEditStatus.userId if editStatus is  E", () => {
      service.requestEditUpdateData$.next({ nonDigitalEditStatus: { editStatus: "E", userId: 2 } });
      const k = service.subscribeCurrentEditingUser();
      expect(k).toEqual(2);
    });
    it("should return null if statuses are not available", () => {
      service.requestEditUpdateData$.next({});
      const k = service.subscribeCurrentEditingUser();
      expect(k).toBeNull();
    });
  });

  describe("isFormValidPostSave", () => {
    it("should return true if isSubmitAttempted is false", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormControl({ deliveryChannel: "IS" }),
      });
      const k = service.isFormValidPostSave({ isSubmitAttempted: false });
      expect(k).toBeTrue();
    });
    it("should return true if isSubmitAttempted is true", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormControl({ deliveryChannel: "IS" }),
      });
      spyOn(service, "notifySubmitAttempt");
      spyOn(service, "isValidEnterPriseInstantWinRule").and.returnValue(false);
      const k = service.isFormValidPostSave({ isSubmitAttempted: true });
      expect(k).toBeFalsy();
    });
  });
  describe("handleWarnings", () => {
    it("should show warning", () => {
      service.handleWarnings({ errors: ["warning: this is warning"] });
      expect(service.showPLUWraningTmpl).toEqual(true);
    });
  });
  describe("getCreatePathFromPC", () => {
    it("should show warning", () => {
      service.getCreatePathFromPC();
    });
  });
  describe("setDetectChanges", () => {
    it("should show warning", () => {
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.cloneOfferRequestData =
        '[{"id":"45","storeGroupVersion":{"productGroupVersions":{},"storeGroup":{},"instantWin":null,"storeTag":null}}]';
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "Must Buy" }),
      });
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({
          deliveryChannel: new UntypedFormControl("CC"),
        }),
      });
      spyOn(service, "detectStoreGroupChanges");
      spyOn(service, "detectStoreTagChanges");
      spyOn(service, "detectProductGroupChanges");
      spyOn(service, "detectDiscountVersionChanges");
      service.allOffersAlongWithOfferRequestUpdate = false;
      service.setDetectChanges([
        {
          id: "45",
          storeGroupVersion: {
            productGroupVersions: {},
            storeGroup: {},
            instantWin: null,
            storeTag: null,
          },
        },
      ]);
      expect(service.detectStoreTagChanges).toHaveBeenCalled();
    });
  });
  describe("saveOfferRequest", () => {
    it("should call update api", () => {
      spyOn(service, "getHeaders");
      spyOn(service, "setDetectChanges");
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("WOD_OR_POD");
      generalOfferTypeServiceStub.cloneOfferRequestData = true;
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "Fab 5 / Score 4" }),
      });
      spyOn(service, "updateAllOffersAlongWithOfferRequestUpdate");
      const http: HttpClient = TestBed.inject(HttpClient);
      const toastr: ToastrService = TestBed.inject(ToastrService);
      spyOn(toastr, "success");
      spyOn(service, "postSave");
      service.isReqSaved$ = new Subject();
      spyOn(http, "put").and.returnValue(of({}));
      service.saveAnywayPLU = true;
      service.otherFieldsChanged = true;
      service.requestData = {
        info: {
          id: "123",
          validatePluTriggerBarcode: true,
          digitalUser: "",
          nonDigitalUser: "",
        },
        rules: {
          qualificationAndBenefit: {
            offerRequestOffers: [],
          },
        },
      };
      service.saveOfferRequest();
      expect(service.setDetectChanges).toHaveBeenCalled();
    });
    it("should call update api and throw error", () => {
      spyOn(service, "getHeaders");
      spyOn(service, "setDetectChanges");
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("WOD_OR_POD");
      generalOfferTypeServiceStub.cloneOfferRequestData = true;
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "Fab 5 / Score 4" }),
      });
      spyOn(service, "updateAllOffersAlongWithOfferRequestUpdate");
      const http: HttpClient = TestBed.inject(HttpClient);
      spyOn(service, "handleWarnings");
      service.isReqSaved$ = new Subject();
      spyOn(http, "put").and.returnValue(throwError({ error: {} }));
      service.saveAnywayPLU = true;
      service.otherFieldsChanged = true;
      service.requestData = {
        info: {
          id: "123",
          validatePluTriggerBarcode: true,
          digitalUser: "",
          nonDigitalUser: "",
        },
        rules: {
          qualificationAndBenefit: {
            offerRequestOffers: [],
          },
        },
      };
      service.saveOfferRequest();
      expect(service.setDetectChanges).toHaveBeenCalled();
    });
    it("should call create api", () => {
      spyOn(service, "getHeaders");
      spyOn(service, "setDetectChanges");
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("WOD_OR_POD");
      generalOfferTypeServiceStub.cloneOfferRequestData = true;
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "Fab 5 / Score 4" }),
      });
      spyOn(service, "updateAllOffersAlongWithOfferRequestUpdate");
      const http: HttpClient = TestBed.inject(HttpClient);
      const toastr: ToastrService = TestBed.inject(ToastrService);
      spyOn(toastr, "success");
      spyOn(service, "postSave");
      service.isReqSaved$ = new Subject();
      spyOn(http, "post").and.returnValue(of({}));
      service.saveAnywayPLU = true;
      service.otherFieldsChanged = true;
      service.requestData = {
        info: {
          id: null,
          validatePluTriggerBarcode: true,
          digitalUser: "",
          nonDigitalUser: "",
        },
        rules: {
          qualificationAndBenefit: {
            offerRequestOffers: [],
          },
        },
      };
      service.saveOfferRequest();
      expect(service.setDetectChanges).not.toHaveBeenCalled();
    });
    it("should call create api and throw error", () => {
      spyOn(service, "getHeaders");
      spyOn(service, "setDetectChanges");
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("WOD_OR_POD");
      generalOfferTypeServiceStub.cloneOfferRequestData = true;
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "Fab 5 / Score 4" }),
      });
      spyOn(service, "updateAllOffersAlongWithOfferRequestUpdate");
      const http: HttpClient = TestBed.inject(HttpClient);
      spyOn(service, "handleWarnings");
      service.isReqSaved$ = new Subject();
      spyOn(http, "post").and.returnValue(throwError({ error: {} }));
      service.saveAnywayPLU = true;
      service.otherFieldsChanged = true;
      service.requestData = {
        info: {
          id: null,
          validatePluTriggerBarcode: true,
          digitalUser: "",
          nonDigitalUser: "",
        },
        rules: {
          qualificationAndBenefit: {
            offerRequestOffers: [],
          },
        },
      };
      service.saveOfferRequest();
      expect(service.setDetectChanges).not.toHaveBeenCalled();
    });
  });
  /*   describe("saveOfferRequest", () => {
    it("makes expected calls", () => {
      const httpTestingController = TestBed.inject(HttpTestingController);
      spyOn(service, "getHeaders");
      spyOn(service, "postSave");
      service.requestData = offerRequests.offerRequests.offerRequests[0]
      service.saveOfferRequest();
      expect(service.getHeaders).toHaveBeenCalled();
      expect(service.postSave).toHaveBeenCalled();
      let req1 = httpTestingController.expectOne("HTTP_ROUTE_GOES_HERE");
      expect(req1.request.method).toEqual("PUT");
      const req = httpTestingController.expectOne("HTTP_ROUTE_GOES_HERE");
      expect(req.request.method).toEqual("POST");
      req.flush();
      httpTestingController.verify();
    });
  }); */
  xdescribe("offerDetailsApi", () => {
    it("makes expected calls", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const searchOfferRequestServiceStub: SearchOfferRequestService = TestBed.inject(SearchOfferRequestService);
      const toastrServiceStub: ToastrService = TestBed.inject(ToastrService);
      spyOn(queryGeneratorStub, "getQuery");
      spyOn(searchOfferRequestServiceStub, "searchOfferRequest");
      spyOn(searchOfferRequestServiceStub, "setOfferDetailsReqToObsvbl");
      spyOn(toastrServiceStub, "warning");
      //service.offerDetailsApi();
      expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
      expect(searchOfferRequestServiceStub.searchOfferRequest).toHaveBeenCalled();
      expect(searchOfferRequestServiceStub.setOfferDetailsReqToObsvbl).toHaveBeenCalled();
      expect(toastrServiceStub.warning).toHaveBeenCalled();
    });
  });

  describe("Time validation", () => {
    it("valid time test", () => {
      const fb: UntypedFormBuilder = new UntypedFormBuilder();
      service.requestForm = fb.group({});
      service.requestForm.addControl(
        "offerRuleTimeGroup",
        fb.group({
          start: [null, []],
          end: [null, []],
          startHr: [null, []],
          startMin: [null, []],
          startPeriod: [null, []],
          endHr: [null, []],
          endMin: [null, []],
          endPeriod: [null, []],
        })
      );
      const formGroup = service.requestForm.get("offerRuleTimeGroup");

      formGroup.get("start").setValue("12:00 AM");
      formGroup.get("startHr").setValue("12");
      formGroup.get("startMin").setValue("00");
      formGroup.get("startPeriod").setValue("AM");
      formGroup.get("end").setValue("12:00 PM");
      formGroup.get("endHr").setValue("12");
      formGroup.get("endMin").setValue("00");
      formGroup.get("endPeriod").setValue("PM");

      const result = service.isValidTimeRule();
      expect(result).toEqual(true);

      service.requestForm.removeControl("offerRuleTimeGroup");
    });

    it("time not set", () => {
      const fb: UntypedFormBuilder = new UntypedFormBuilder();
      service.requestForm = fb.group({});
      service.requestForm.addControl(
        "offerRuleTimeGroup",
        fb.group({
          start: [null, []],
          end: [null, []],
          startHr: [null, []],
          startMin: [null, []],
          startPeriod: [null, []],
          endHr: [null, []],
          endMin: [null, []],
          endPeriod: [null, []],
        })
      );
      const formGroup = service.requestForm.get("offerRuleTimeGroup");
      const result = service.isValidTimeRule();
      expect(result).toEqual(true);
      service.requestForm.removeControl("offerRuleTimeGroup");
    });

    it("invalid time test", () => {
      const fb: UntypedFormBuilder = new UntypedFormBuilder();
      service.requestForm = fb.group({});
      service.requestForm.addControl(
        "offerRuleTimeGroup",
        fb.group({
          start: [null, []],
          end: [null, []],
          startHr: [null, []],
          startMin: [null, []],
          startPeriod: [null, []],
          endHr: [null, []],
          endMin: [null, []],
          endPeriod: [null, []],
        })
      );
      const formGroup = service.requestForm.get("offerRuleTimeGroup");

      formGroup.get("start").setValue("12:00 AM");

      formGroup.get("startMin").setValue("00");
      formGroup.get("startPeriod").setValue("AM");
      formGroup.get("end").setValue("12:00 PM");
      formGroup.get("endHr").setValue("12");
      formGroup.get("endPeriod").setValue("PM");

      const result = service.isValidTimeRule();
      expect(result).toEqual(false);
      service.requestForm.removeControl("offerRuleTimeGroup");
    });

    it("same time test", () => {
      const fb: UntypedFormBuilder = new UntypedFormBuilder();
      service.requestForm = fb.group({});
      service.requestForm.addControl(
        "offerRuleTimeGroup",
        fb.group({
          start: [null, []],
          end: [null, []],
          startHr: [null, []],
          startMin: [null, []],
          startPeriod: [null, []],
          endHr: [null, []],
          endMin: [null, []],
          endPeriod: [null, []],
        })
      );
      const formGroup = service.requestForm.get("offerRuleTimeGroup");

      formGroup.get("start").setValue("12:00 AM");
      formGroup.get("startHr").setValue("12");
      formGroup.get("startMin").setValue("00");
      formGroup.get("startPeriod").setValue("AM");
      formGroup.get("end").setValue("12:00 AM");
      formGroup.get("endHr").setValue("12");
      formGroup.get("endMin").setValue("00");
      formGroup.get("endPeriod").setValue("AM");

      const result = service.isValidTimeRule();
      expect(result).toEqual(false);
      service.requestForm.removeControl("offerRuleTimeGroup");
    });

    it("start after end time test (hrs)", () => {
      const fb: UntypedFormBuilder = new UntypedFormBuilder();
      service.requestForm = fb.group({});
      service.requestForm.addControl(
        "offerRuleTimeGroup",
        fb.group({
          start: [null, []],
          end: [null, []],
          startHr: [null, []],
          startMin: [null, []],
          startPeriod: [null, []],
          endHr: [null, []],
          endMin: [null, []],
          endPeriod: [null, []],
        })
      );
      const formGroup = service.requestForm.get("offerRuleTimeGroup");

      formGroup.get("start").setValue("1:00 AM");
      formGroup.get("startHr").setValue("1");
      formGroup.get("startMin").setValue("00");
      formGroup.get("startPeriod").setValue("AM");
      formGroup.get("end").setValue("12:00 AM");
      formGroup.get("endHr").setValue("12");
      formGroup.get("endMin").setValue("00");
      formGroup.get("endPeriod").setValue("AM");

      const result = service.isValidTimeRule();
      expect(result).toEqual(false);
      service.requestForm.removeControl("offerRuleTimeGroup");
    });

    it("start after end time test (mins)", () => {
      const fb: UntypedFormBuilder = new UntypedFormBuilder();
      service.requestForm = fb.group({});
      service.requestForm.addControl(
        "offerRuleTimeGroup",
        fb.group({
          start: [null, []],
          end: [null, []],
          startHr: [null, []],
          startMin: [null, []],
          startPeriod: [null, []],
          endHr: [null, []],
          endMin: [null, []],
          endPeriod: [null, []],
        })
      );
      const formGroup = service.requestForm.get("offerRuleTimeGroup");

      formGroup.get("start").setValue("12:30 AM");
      formGroup.get("startHr").setValue("12");
      formGroup.get("startMin").setValue("30");
      formGroup.get("startPeriod").setValue("AM");
      formGroup.get("end").setValue("12:00 AM");
      formGroup.get("endHr").setValue("12");
      formGroup.get("endMin").setValue("00");
      formGroup.get("endPeriod").setValue("AM");

      const result = service.isValidTimeRule();
      expect(result).toEqual(false);
      service.requestForm.removeControl("offerRuleTimeGroup");
    });
  });

  describe("Min state tests", () => {
    it("Min State Submit", () => {
      service.digitalStatus = "D";
      service.nonDigitalStatus = "D";
      let result = service.isMinStateSubmit();
      expect(result).toEqual(true);

      service.digitalStatus = "S";
      service.nonDigitalStatus = "S";
      result = service.isMinStateSubmit();
      expect(result).toEqual(true);

      service.digitalStatus = "S";
      service.nonDigitalStatus = "P";
      result = service.isMinStateSubmit();
      expect(result).toEqual(true);
    });

    it("Min State Processing", () => {
      service.digitalStatus = "D";
      service.nonDigitalStatus = "D";
      let result = service.isMinStateProcessing();
      expect(result).toEqual(true);

      service.digitalStatus = "S";
      service.nonDigitalStatus = "S";
      result = service.isMinStateProcessing();
      expect(result).toEqual(false);

      service.digitalStatus = "S";
      service.nonDigitalStatus = "P";
      result = service.isMinStateProcessing();
      expect(result).toEqual(true);
    });
  });

  describe("setPodDetailsData tests", () => {
    it("setPodDetailsData with nonUtcCallForEndDate as str", () => {
      const spy = spyOn(service, "nonUtcCallForEndDate");
      let sgv = {
        podDetails: {
          ivieImageId: "jddhjsajhashjssss",
          headline: "hjshashjsahj",
          displayEndDate: "06/30/2020",
          displayStartDate: "2020-06-19T18:30:00.000Z",
          offerDescription: null,
          priceText: "jjdhdshds",
          disclaimer: null,
        },
      };
      service.setPodDetailsData(sgv);
      expect(spy).toHaveBeenCalled();
    });

    it("setPodDetailsData with nonUtcCallForStartDate as str", () => {
      const spy = spyOn(service, "nonUtcCallForStartDate");
      let sgv = {
        podDetails: {
          ivieImageId: "jddhjsajhashjssss",
          headline: "hjshashjsahj",
          displayEndDate: "06/30/2020",
          displayStartDate: "2020-06-19T18:30:00.000Z",
          offerDescription: null,
          priceText: "jjdhdshds",
          disclaimer: null,
        },
      };
      service.setPodDetailsData(sgv);
      expect(spy).toHaveBeenCalled();
    });

    it("setPodDetailsData with nonUtcCallForEndDate as obj", () => {
      const spy = spyOn(service, "nonUtcCallForEndDate");
      let sgv = {
        podDetails: {
          ivieImageId: "jddhjsajhashjssss",
          headline: "hjshashjsahj",
          displayEndDate: "06/30/2020",
          displayStartDate: "2020-06-19T18:30:00.000Z",
          offerDescription: null,
          priceText: "jjdhdshds",
          disclaimer: null,
        },
      };
      service.setPodDetailsData(sgv);
      expect(spy).toHaveBeenCalled();
    });

    it("setPodDetailsData with nonUtcCallForStartDate as obj", () => {
      const spy = spyOn(service, "nonUtcCallForStartDate");
      let sgv = {
        podDetails: {
          ivieImageId: "jddhjsajhashjssss",
          headline: "hjshashjsahj",
          displayEndDate: "06/30/2020",
          displayStartDate: "2020-06-19T18:30:00.000Z",
          offerDescription: null,
          priceText: "jjdhdshds",
          disclaimer: null,
        },
      };
      service.setPodDetailsData(sgv);
      expect(spy).toHaveBeenCalled();
    });
  });
  /*  describe('initVariables', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      spyOn(initialDataServiceStub, 'getAppData');
      service.initVariables();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
    });
  });
   describe('saveOR', () => {
    it('makes expected calls', () => {
      const authServiceStub: AuthService = TestBed.inject(AuthService);
      spyOn(service, 'mapFormDataToReqObj');
      spyOn(authServiceStub, 'onUserDataAvailable');
      service.saveOR();
      expect(service.mapFormDataToReqObj).toHaveBeenCalled();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe('isValidRewardAcumulationRule', () => {
    it('makes expected calls', () => {
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(
        GeneralOfferTypeService
      );
      spyOn(generalOfferTypeServiceStub, 'getKeyByValue');
      service.isValidRewardAcumulationRule();
      expect(generalOfferTypeServiceStub.getKeyByValue).toHaveBeenCalled();
    });
  }); 
   describe('isValidEnterPriseInstantWinRule', () => {
    it('makes expected calls', () => {
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(
        GeneralOfferTypeService
      );
      spyOn(generalOfferTypeServiceStub, 'getKeyByValue');
      service.isValidEnterPriseInstantWinRule();
      expect(generalOfferTypeServiceStub.getKeyByValue).toHaveBeenCalled();
    });
  });*/

  describe("setGroupValues", () => {
    it("makes expected calls", () => {
      let generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.get(GeneralOfferTypeService);
      const offerRequestOffers = [
        {
          offers: [
            {
              assignedTo: null,
              discountVersion: 852166394,
              distinctIdentifier: "DEFAULT",
              externalOfferId: "852166399-D",
              genericDiscountId: 852166397,
              instantWinVersion: null,
              isApplicableToJ4U: true,
              isPodApplicable: true,
              offerId: 852166399,
              offerStatus: "I",
              productGroupVersion: 852166395,
              storeGroupVersion: 852166392,
            },
          ],
          storeGroupVersion: {
            day: null,
            displayOrder: 1,
            id: 852166392,
            instantWin: null,
            offerPrototype: "ITEM_DISCOUNT",
            storeGroup: {
              digitalRedemptionStoreGroupIds: [
                {
                  id: 1452146507,
                  name: "OMS Test 293",
                },
              ],
              digitalRedemptionStoreGroupNames: [{ 0: "OMS Test 293" }],
              nonDigitalRedemptionStoreGroupIds: [
                {
                  id: 1452146507,
                  name: "OMS Test 293",
                },
              ],
              nonDigitalRedemptionStoreGroupNames: [{ 0: "OMS Test 293" }],
              podStoreGroupIds: [
                {
                  id: 1452146507,
                  name: "OMS Test 293",
                },
              ],
              podStoreGroupNames: [{ 0: "OMS Test 293" }],
            },
            productGroupVersions: [
              {
                displayOrder: 1,
                id: 852166395,
                anyProduct: null,
                isGiftCard: false,
                discountVersion: {
                  id: null,
                  airMiles: [],
                  discounts: [
                    {
                      includeProductGroupId: {
                        id: 2032145912,
                        name: "50ktest",
                      },
                      includeProductGroupName: "50ktest",
                      displayOrder: 1,
                      benefitValueType: "PRICE_POINT_ITEMS",
                      discountType: "ITEM_LEVEL",
                      excludeProductGroupName: null,
                      chargebackDepartment: null,
                      tiers: [
                        {
                          amount: "1",
                          upTo: null,
                          itemLimit: 1,
                          dollarLimit: null,
                          level: 1,
                          receiptText: null,
                          rewards: null,
                          weightLimit: null,
                          points: null,
                        },
                      ],
                    },
                  ],
                },
                productGroup: {
                  id: {
                    id: 2032145912,
                    name: "50ktest",
                  },
                  name: "50ktest",
                  quantityUnitType: "ITEMS",
                  excludedProductGroupName: null,
                  conjunction: null,
                  minPurchaseAmount: 0,
                  uniqueproduct: null,
                  inheritedFromOfferRequest: null,
                  tiers: [
                    {
                      level: 1,
                      amount: null,
                    },
                  ],
                },
              },
            ],
          },
        },
      ];
      service.setGroupValues(offerRequestOffers);
    });
  });
  describe("updateAllOffersAlongWithOfferRequestUpdate", () => {
    it("makes expected updateAllOffersAlongWithOfferRequestUpdate", () => {
      let generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.get(GeneralOfferTypeService);
      service.requestData = {
        rules: {
          qualificationAndBenefit: {
            offerRequestOffers: [
              {
                offers: [
                  {
                    assignedTo: null,
                    discountVersion: 852166394,
                    distinctIdentifier: "DEFAULT",
                    externalOfferId: "852166399-D",
                    genericDiscountId: 852166397,
                    instantWinVersion: null,
                    isApplicableToJ4U: true,
                    isPodApplicable: true,
                    offerId: 852166399,
                    offerStatus: "I",
                    productGroupVersion: 852166395,
                    storeGroupVersion: 852166392,
                  },
                ],
                storeGroupVersion: {
                  day: null,
                  displayOrder: 1,
                  id: 852166392,
                  instantWin: null,
                  offerPrototype: "ITEM_DISCOUNT",
                  storeGroup: {
                    digitalRedemptionStoreGroupIds: [
                      {
                        id: 1452146507,
                        name: "OMS Test 293",
                      },
                    ],
                    digitalRedemptionStoreGroupNames: [{ 0: "OMS Test 293" }],
                    nonDigitalRedemptionStoreGroupIds: [
                      {
                        id: 1452146507,
                        name: "OMS Test 293",
                      },
                    ],
                    nonDigitalRedemptionStoreGroupNames: [{ 0: "OMS Test 293" }],
                    podStoreGroupIds: [
                      {
                        id: 1452146507,
                        name: "OMS Test 293",
                      },
                    ],
                    podStoreGroupNames: [{ 0: "OMS Test 293" }],
                  },
                  productGroupVersions: [
                    {
                      displayOrder: 1,
                      id: 852166395,
                      anyProduct: null,
                      isGiftCard: false,
                      discountVersion: {
                        id: null,
                        airMiles: [],
                        discounts: [
                          {
                            includeProductGroupId: {
                              id: 2032145912,
                              name: "50ktest",
                            },
                            includeProductGroupName: "50ktest",
                            displayOrder: 1,
                            benefitValueType: "PRICE_POINT_ITEMS",
                            discountType: "ITEM_LEVEL",
                            excludeProductGroupName: null,
                            chargebackDepartment: null,
                            tiers: [
                              {
                                amount: "1",
                                upTo: null,
                                itemLimit: 1,
                                dollarLimit: null,
                                level: 1,
                                receiptText: null,
                                rewards: null,
                                weightLimit: null,
                                points: null,
                              },
                            ],
                          },
                        ],
                      },
                      productGroup: {
                        id: {
                          id: 2032145912,
                          name: "50ktest",
                        },
                        name: "50ktest",
                        quantityUnitType: "ITEMS",
                        excludedProductGroupName: null,
                        conjunction: null,
                        minPurchaseAmount: 0,
                        uniqueproduct: null,
                        inheritedFromOfferRequest: null,
                        tiers: [
                          {
                            level: 1,
                            amount: null,
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      };
      generalOfferTypeServiceStub.cloneOfferRequestData =
        '[{"id":111253711,"storeGroupVersion":{"id":111253712,"displayOrder":1,"offerPrototype":"WOD_OR_POD","storeGroup":{"nonDigitalRedemptionStoreGroupIds":[299371609],"podStoreGroupNames":null,"nonDigitalRedemptionStoreGroupNames":["APIAutoNorcalStoreGroup_dev"],"digitalRedemptionStoreGroupNames":null,"updateDigitalStoreGroup":false,"updatePODStoreGroup":false,"updateNonDigitalStoreGroup":false},"productGroupVersions":[{"displayOrder":1,"id":111253709,"anyProduct":null,"isGiftCard":false,"productGroup":{"id":6,"quantityUnitType":"WEIGHT_VOLUME","excludedProductGroupId":null,"conjunction":null,"minPurchaseAmount":0,"uniqueproduct":null,"tiers":[{"level":1,"amount":10}],"inheritedFromOfferRequest":null,"displayOrder":1,"name":"Any Product","excludedProductGroupName":null,"updateProductGroup":false},"discountVersion":{"id":111253710,"discounts":[{"displayOrder":1,"id":111253707,"benefitValueType":"AMOUNT_OFF","discountType":"BASKET_LEVEL","includeProductGroupId":6,"excludeProductGroupId":null,"chargebackDepartment":"Beer","tiers":[{"level":1,"amount":15,"upTo":null,"itemLimit":null,"weightLimit":null,"dollarLimit":null,"receiptText":null,"rewards":null}],"includeProductGroupName":"Any Product","excludeProductGroupName":null,"updateProductDiscount":false}],"airMiles":[]}}],"instantWin":null,"storeTag":null,"podDetails":null},"offers":[{"storeGroupVersion":111253712,"productGroupVersion":111253709,"discountVersion":111253710,"genericDiscountId":111253707,"instantWinVersion":null,"offerId":111253708,"externalOfferId":"111253708-ND","offerStatus":"I","isApplicableToJ4U":false,"distinctIdentifier":"DEFAULT","isPodApplicable":null,"offerEditStatus":null}]}]';
      service.updateAllOffersAlongWithOfferRequestUpdate();
    });
  });

  describe("isFormValidOnSave", () => {
    let spy1;
    beforeEach(() => {
      spyOn(service, "isValidTimeRule");
      spyOn(service, "notifySaveAttempt");
      spyOn(service, "resetSubmitValidations");
      spyOn(service, "isValidEnterPriseInstantWinRule");
      spyOn(service, "isValidRewardAcumulationRule");
    });
    it("makes expected calls when deliveryChannel is IS", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormControl({ deliveryChannel: "IS" }),
      });
      service.isFormValidOnSave();
      expect(service.isValidTimeRule).toHaveBeenCalled();
      expect(service.notifySaveAttempt).toHaveBeenCalled();
      expect(service.resetSubmitValidations).toHaveBeenCalled();
      expect(service.isValidEnterPriseInstantWinRule).toHaveBeenCalled();
      expect(service.isValidRewardAcumulationRule).not.toHaveBeenCalled();
    });

    it("makes expected calls when deliveryChannel is not IS", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormControl({ deliveryChannel: "BIS" }),
      });

      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "Fab 5 / Score 4" }),
      });
      service.isFormValidOnSave();
      expect(service.isValidTimeRule).toHaveBeenCalled();
      expect(service.notifySaveAttempt).toHaveBeenCalled();
      expect(service.resetSubmitValidations).toHaveBeenCalled();
    });
    it("makes isValidEnterPriseInstantWinRule call", () => {
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormControl({ deliveryChannel: "BIS" }),
      });

      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormControl({ type: "df" }),
      });
      service.isFormValidOnSave();
      expect(service.isValidEnterPriseInstantWinRule).toHaveBeenCalled();
    });
  });
  describe("notifySubmitAttempt", () => {
    it("makes expected calls", () => {
      spyOn(service, "notifySaveAttempt");
      service.setReqSectionValidationsForSubmit = service.setJustificationValidationsForSubmit = () => {};
      service.notifySubmitAttempt();
      expect(service.notifySaveAttempt).toHaveBeenCalled();
    });
  });

  describe("setDisplayOrderForVersion", () => {
    it("makes expected calls", () => {
      let item = {
        id: null,
        offers: [],
        storeGroupVersion: {
          displayOrder: 1,
          id: null,
          offerPrototype: "ITEM_DISCOUNT",
          day: null,
          time: null,
          storeGroup: {
            nonDigitalRedemptionStoreGroups: null,
            podStoreGroups: ["EDM SG 21"],
            digitalRedemptionStoreGroups: ["EDM SG 21"],
          },
          productGroupVersions: [
            {
              displayOrder: 1,
              id: null,
              anyProduct: null,
              isGiftCard: false,
              discountVersion: {
                id: null,
                airMiles: [],
                discounts: [
                  {
                    id: null,
                    displayOrder: 1,
                    benefitValueType: "PRICE_POINT_ITEMS",
                    discountType: "ITEM_LEVEL",
                    includeProductGroupName: "Banana",
                    excludeProductGroupName: null,
                    chargebackDepartment: null,
                    tiers: [
                      {
                        amount: "1",
                        upTo: null,
                        itemLimit: 1,
                        dollarLimit: null,
                        level: 1,
                        receiptText: null,
                        rewards: null,
                        weightLimit: null,
                        points: null,
                      },
                    ],
                  },
                ],
              },
              productGroup: {
                name: "Banana",
                quantityUnitType: "ITEMS",
                excludedProductGroupName: null,
                conjunction: null,
                minPurchaseAmount: 0,
                uniqueproduct: null,
                inheritedFromOfferRequest: null,
                tiers: [
                  {
                    level: 1,
                    amount: null,
                  },
                ],
              },
            },
          ],
          storeTag: {
            printJ4uTagEnabled: false,
            multiple: null,
            amount: null,
            comments: null,
          },
          instantWin: null,
        },
      };
      service.setDisplayOrderForVersion(item, 0, true);
    });
  });

  describe("mapFormDataToReqObj", () => {
    it("makes expected calls", () => {
      service.requestForm = new UntypedFormGroup({
        offerBuilderGroup: new UntypedFormGroup({
          digital: new UntypedFormControl("adf"),
          nonDigital: new UntypedFormControl("dfdf"),
        }),
      });
      service.digitalUserDetails = (a) => {
        return "dfdf";
      };
      service.nonDigitalUserDetails = (a) => {
        return "adf";
      };
      service.requestForm = new UntypedFormGroup({
        offerBuilderGroup: new UntypedFormControl({ digital: "ad", nonDigital: "df" }),
        nopaGroup: new UntypedFormGroup({
          nopaNumbers: new UntypedFormControl("123456,7789455"),
        }),
        additionalDescriptionGroup: new UntypedFormGroup({}),
        justificationGroup: new UntypedFormGroup({}),
        offerRuleDayGroup: new UntypedFormGroup({}),
        offerReqGroup: new UntypedFormGroup({
          digital: new UntypedFormControl("ad"),
          nonDigital: new UntypedFormControl("df"),
          behavioralCondition: new UntypedFormGroup({}),
        }),
      });
      let generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);

      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl(""),
        }),

        offerRequestOffers: new UntypedFormArray([
          new UntypedFormGroup({
            storeGroupVersion: new UntypedFormGroup({
              offerPrototype: new UntypedFormControl(""),
              storeGroup: new UntypedFormGroup({
                podStoreGroups: new UntypedFormControl(""),
              }),
            }),
          }),
        ]),
      });
      service.mapFormDataToReqObj();
      expect(service.requestData.info.digitalUser).toEqual("dfdf");
    });
  });
  describe("reqDetailsApi", () => {
    it("makes expected calls", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const searchOfferRequestServiceStub: SearchOfferRequestService = TestBed.inject(SearchOfferRequestService);
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      const toastrServiceStub: ToastrService = TestBed.inject(ToastrService);
      spyOn(queryGeneratorStub, "getQuery");
      spyOn(searchOfferRequestServiceStub, "searchOfferRequest").and.returnValue(of(offerRequests.offerRequests));

      spyOn(searchOfferRequestServiceStub, "setOfferDetailsReqToObsvbl");
      spyOn(toastrServiceStub, "warning");
      Object.assign(generalOfferTypeServiceStub, { facetItemService: { programCodeSelected: "SC" } });
      service.reqDetailsApi();
      expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
      expect(searchOfferRequestServiceStub.searchOfferRequest).toHaveBeenCalled();
      expect(searchOfferRequestServiceStub.setOfferDetailsReqToObsvbl).toHaveBeenCalled();
    });
    it("should warn if response is invalid", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const searchOfferRequestServiceStub: SearchOfferRequestService = TestBed.inject(SearchOfferRequestService);
      const toastrServiceStub: ToastrService = TestBed.inject(ToastrService);
      spyOn(queryGeneratorStub, "getQuery");
      let offerRequestsMock = JSON.parse(JSON.stringify(offerRequests));

      offerRequestsMock.offerRequests.offerRequests = [];
      spyOn(searchOfferRequestServiceStub, "searchOfferRequest").and.returnValue(of(offerRequestsMock.offerRequests));

      spyOn(searchOfferRequestServiceStub, "setOfferDetailsReqToObsvbl");
      spyOn(toastrServiceStub, "warning");

      service.reqDetailsApi();

      expect(toastrServiceStub.warning).toHaveBeenCalled();
    });
  });
  describe("formatOffersData", () => {
    let generalOfferTypeServiceStub: GeneralOfferTypeService, setIncludeProductGroupNameSpy;
    beforeEach(() => {
      generalOfferTypeServiceStub = TestBed.inject(GeneralOfferTypeService);

      spyOn(service, "setPodDetailsData");
      setIncludeProductGroupNameSpy = spyOn(service, "setIncludeProductGroupName");
    });
    it("makes expected calls", () => {
      spyOn(generalOfferTypeServiceStub, "getKeyByValue");
      service.formatOffersData();
      expect(generalOfferTypeServiceStub.getKeyByValue).toHaveBeenCalled();
    });

    it("makes expected calls if selectedOfferType is WOD_OR_POD", () => {
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("WOD_OR_POD");
      const k = spyOn(service, "wodOrPodOfferRequestOffer");
      service.formatOffersData();
      expect(k).toHaveBeenCalled();
    });
    it("makes expected calls if selectedOfferType is CUSTOM", () => {
      spyOn(generalOfferTypeServiceStub, "getKeyByValue").and.returnValue("CUSTOM");
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormControl({ deliveryChannel: "IS" }),
      });

      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl(""),
        }),

        offerRequestOffers: new UntypedFormArray([
          new UntypedFormGroup({
            storeGroupVersion: new UntypedFormGroup({
              offerPrototype: new UntypedFormControl(""),
              storeGroup: new UntypedFormGroup({
                podStoreGroups: new UntypedFormControl(""),
              }),
            }),
          }),
        ]),
      });

      service.formatOffersData();

      expect(setIncludeProductGroupNameSpy).toHaveBeenCalled();
    });
  });

  describe("wodOrPodOfferRequestOffer", () => {
    it("should execute successfully", () => {
      service.wodOrPodOfferRequestOffer({ offerRequestOffers: generalOfferTypeFormVal.offerRequestOffers, chargebackDepartment: null });
    });
  });

  describe("setReqServiceVariables", () => {
    it("should set variables as expected", () => {
      service.setReqServiceVariables({ route: "abc", router: "xyz" });
      expect(service.compRoute).toEqual("abc");
      expect(service.compRouter).toEqual("xyz");
    });
  });

  describe("setIncludeProductGroupName", () => {
    beforeEach(() => {
      spyOn(service, "getAnyProductId").and.returnValue({name: '', id: ""});
    });
    it("should execute successfully", () => {
      spyOn(service, "handleItemPlusBasket");
      service.setIncludeProductGroupName(setIncludeProductGroupNameData);
    });
    it("should return false if selected offer type is REWARDS_FLAT", () => {
      spyOn(service, "handleItemPlusBasket");
      let mock = JSON.parse(JSON.stringify(setIncludeProductGroupNameData));
      mock.selectedOfferType = "REWARDS_FLAT";
      const k = service.setIncludeProductGroupName(mock);
      expect(k).toBeFalsy();
    });
  });

  describe("getValueByKey", () => {
    it("if object is null", () => {
      const k = service.getValueByKey(null, "REWARDS_FLAT");
      expect(k).toEqual("Rewards - Flat");
    });
  });
  describe("handleItemPlusBasket", () => {
    it("make expected call", () => {
      service.handleItemPlusBasket("Any Product", { anyProduct: false }, { selectedOfferType: "ITEM_PLUS_BASKET" });
    });
  });
  describe("prependZero", () => {
    it("should prepend Zero if string length < 2", () => {
      const k = service.prependZero("2");
      expect(k).toEqual("02");
    });
  });

  describe("resetOnDestroy", () => {
    it("should reset variables", () => {
      service.resetOnDestroy();
      expect(service.isSavedFromNavigationOverlay).toBeFalsy();
      expect(service.uploadedFilesList).toEqual([]);
    });
  });

  describe("isOfferFormValidForSave", () => {
    it("isOfferFormValidForSaveFlag should be false for errors", () => {
      const control = {
        errors: {
          min: 1,
        },
      };
      service.isOfferFormValidForSave({ control });
      expect(service.isOfferFormValidForSaveFlag).toBeFalsy();
    });
  });

  describe("setJustificationSection", () => {
    it("when object has createdTs and difference date is greater than 7", () => {
      const obj = {
        createdTs: 1605859445.167,
        offerEffectiveStartDate: "11/28/2020",
      };
      service.setJustificationSection(obj);
    });

    it("when object has no createdTs and difference date is greater than 7", () => {
      const obj = {
        offerEffectiveStartDate: "11/28/2020",
      };
      service.setJustificationSection(obj);
    });

    it("when object has createdTs and difference date is less than 7", () => {
      const obj = {
        createdTs: 1606224382.236,
        offerEffectiveStartDate: "11/28/2020",
      };
      service.setJustificationSection(obj);
    });

    it("when object has no createdTs and difference date is less than 7", () => {
      const obj = {
        offerEffectiveStartDate: "11/28/2020",
      };
      service.setJustificationSection(obj);
    });
  });

  describe("isUpdateOffer", () => {
    it("makes expected calls when OfferTypeDetailsFormChanged not changed", () => {
      service.isOfferTypeDetailsFormChanged = false;
      service.isNonImpactedFieldsChanged = false;
      const spy = spyOn(service, "getORFormFields");
      service.isUpdateOffer();
      expect(spy).toHaveBeenCalled();
    });
    it("makes expected calls when OfferTypeDetailsFormChanged not changed and nonImpactedFieldsChanged", () => {
      service.isOfferTypeDetailsFormChanged = false;
      service.isNonImpactedFieldsChanged = true;
      const spy = spyOn(service, "getORFormFields");
      service.isUpdateOffer();
      expect(spy).toHaveBeenCalled();
    });
    it("makes expected calls when OfferTypeDetailsFormChanged", () => {
      service.isOfferTypeDetailsFormChanged = true;
      service.isUpdateOffer();
      expect(service.updateOffer).toEqual(true);
    });
  });

  describe("getORFormFields", () => {
    beforeEach(() => {
      service.requestForm = new UntypedFormGroup({
        additionalDescriptionGroup: new UntypedFormGroup({
          desc: new UntypedFormControl({}),
        }),
        justificationGroup: new UntypedFormGroup({
          justification: new UntypedFormControl({}),
        }),
        nopaGroup: new UntypedFormGroup({
          billingOptions: new UntypedFormControl({}),
          isBilled: new UntypedFormControl({}),
          nopaEndDate: new UntypedFormControl({}),
          nopaNumbers: new UntypedFormControl({}),
          nopaStartDate: new UntypedFormControl({}),
        }),
        nopaSection: new UntypedFormControl({}),
        offerBuilderGroup: new UntypedFormGroup({
          digital: new UntypedFormControl({}),
          nonDigital: new UntypedFormControl({}),
        }),
        offerReqGroup: new UntypedFormGroup({
          adType: new UntypedFormControl({}),
          brandAndSize: new UntypedFormControl({}),
          customerSegment: new UntypedFormControl({}),
          deliveryChannel: new UntypedFormControl({}),
          department: new UntypedFormControl({}),
          desc: new UntypedFormControl({}),
          digitalEditStatus: new UntypedFormControl({}),
          digitalStatus: new UntypedFormControl({}),
          editStatus: new UntypedFormControl({}),
          group: new UntypedFormControl({}),
          groupDivision: new UntypedFormControl({}),
          nonDigitalEditStatus: new UntypedFormControl({}),
          nonDigitalStatus: new UntypedFormControl({}),
          numOfTiers: new UntypedFormControl({}),
          offerEffectiveEndDate: new UntypedFormControl({}),
          offerEffectiveStartDate: new UntypedFormControl({}),
          pluTriggerBarCode: new UntypedFormControl({}),
          programCode: new UntypedFormControl({}),
          usageLimitTypePerUser: new UntypedFormControl({}),
        }),
        offerTypeSection: new UntypedFormControl({}),
        podSection: new UntypedFormControl({}),
      });

      service.nonImpactedFields = [
        "department",
        "customerSegment",
        "nopaNumbers",
        "nopaStartDate",
        "nopaEndDate",
        "isBilled",
        "billingOptions",
        "desc",
      ];
    });
    it("when formGroup or formArray is changed and nonImpactedFields got changed", () => {
      let offerReqGroup = service.requestForm.controls.offerReqGroup;
      offerReqGroup.markAsTouched();
      offerReqGroup.markAsDirty();
      service.isNonImpactedFieldsChanged = true;
      const spy = spyOn(service, "getORFormFields");
      service.getORFormFields(service.requestForm);
      expect(spy).toHaveBeenCalled();
    });
    it("when formGroup or formArray is changed and nonImpactedFields not changed", () => {
      let offerReqGroup = service.requestForm.controls.offerReqGroup;
      offerReqGroup.markAsTouched();
      offerReqGroup.markAsDirty();
      service.isNonImpactedFieldsChanged = false;
      service.getORFormFields(service.requestForm);
    });
    it("when update contains non-impacted fields", () => {
      let department = service.requestForm.controls.offerReqGroup.get("department");
      department.markAsTouched();
      department.markAsDirty();
      service.isNonImpactedFieldsChanged = true;
      service.nonImpactedFields.includes("department");
      service.getORFormFields(service.requestForm);
    });
    it("when update contains impacted fields", () => {
      let brandAndSize = service.requestForm.controls.offerReqGroup.get("brandAndSize");
      brandAndSize.markAsTouched();
      brandAndSize.markAsDirty();
      service.isNonImpactedFieldsChanged = true;
      service.getORFormFields(service.requestForm);
    });
  });

  describe("saveOR", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = TestBed.inject(AuthService);
      service.callingFrom = "save";
      service.isReqSubmitted = true;
      const spy1 = spyOn(service, "isFormValidOnSave");
      const spy2 = spyOn(service, "isFormValidPostSave");
      const spy3 = spyOn(service, "mapFormDataToReqObj");
      const spy4 = spyOn(authServiceStub, "onUserDataAvailable");
      service.saveOR();
    });
  });

  describe("notifySaveAttempt", () => {
    it("makes expected calls", () => {
      const generalOfferTypeServiceStub: GeneralOfferTypeService = TestBed.inject(GeneralOfferTypeService);
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({});
      service.requestForm = new UntypedFormGroup({
        offerReqGroup: new UntypedFormGroup({}),
      });
      const spy1 = spyOn(service.requestForm, "markAsUntouched");
      const spy2 = spyOn(generalOfferTypeServiceStub.generalOfferTypeForm, "markAsUntouched");
      service.notifySaveAttempt();
      expect(spy1).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
    });
  });

  describe("updateReqDataKeys", () => {
    it("makes expected calls", () => {
      let obj = {
        id: "367027137",
        lastUpdatedTs: "1608213865.037",
        createdApplicationId: "OMS",
        createdTs: "1608098985.854",
        createdUserId: "nling05",
      };
      service.updateReqDataKeys(obj);
      expect(service.reqId).toEqual(obj.id);
      expect(service.lastUpdatedTs).toEqual(obj.lastUpdatedTs);
      expect(service.createdApplicationId).toEqual(obj.createdApplicationId);
      expect(service.createdTs).toEqual(obj.createdTs);
      expect(service.createdUserId).toEqual(obj.createdUserId);
    });
  });

  describe("detectDiscountVersionChanges", () => {
    it("should make expected calls when discounts exist", () => {
      const reqArray = [
        {
          displayOrder: 1,
          id: 868733001,
          anyProduct: null,
          isGiftCard: false,
          productGroup: {
            id: 6,
            quantityUnitType: "DOLLARS",
            excludedProductGroupId: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            tiers: [{ level: 1, amount: 2 }],
            inheritedFromOfferRequest: null,
            displayOrder: null,
            name: "Any Product",
            excludedProductGroupName: null,
            updateProductGroup: false,
          },
          discountVersion: {
            id: 868733000,
            discounts: [
              {
                displayOrder: 1,
                id: 868733007,
                benefitValueType: "AMOUNT_OFF",
                discountType: "BASKET_LEVEL",
                includeProductGroupId: 6,
                excludeProductGroupId: null,
                chargebackDepartment: null,
                tiers: [
                  {
                    level: 1,
                    amount: 5,
                    upTo: null,
                    itemLimit: null,
                    weightLimit: null,
                    dollarLimit: null,
                    receiptText: null,
                    rewards: null,
                  },
                ],
                includeProductGroupName: "Any Product",
                excludeProductGroupName: null,
                updateProductDiscount: false,
              },
            ],
            airMiles: [],
          },
        },
      ];
      const saveArry = [
        {
          displayOrder: 1,
          id: 868733001,
          anyProduct: null,
          isGiftCard: false,
          discountVersion: {
            id: 868733000,
            airMiles: [],
            discounts: [
              {
                id: 868733007,
                displayOrder: 1,
                benefitValueType: "AMOUNT_OFF",
                discountType: "BASKET_LEVEL",
                includeProductGroupName: "Any Product",
                includeProductGroupId: 6,
                excludeProductGroupId: null,
                excludeProductGroupName: null,
                chargebackDepartment: null,
                updateProductDiscount: false,
                tiers: [
                  {
                    amount: "5.00",
                    upTo: null,
                    itemLimit: null,
                    dollarLimit: null,
                    level: 1,
                    receiptText: null,
                    rewards: null,
                    weightLimit: null,
                    points: null,
                  },
                ],
              },
            ],
          },
          productGroup: {
            name: "Any Product",
            id: 6,
            quantityUnitType: "DOLLARS",
            excludedProductGroupName: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            inheritedFromOfferRequest: null,
            updateProductGroup: false,
            tiers: [{ level: 1, amount: 2, upTo: null }],
          },
        },
      ];
      spyOn(service, "detectObjectComparision").and.returnValue(1);
      service.detectDiscountVersionChanges(reqArray, saveArry);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });

    it("should make expected calls when Airmiles exist", () => {
      const reqArray = [
        {
          displayOrder: 1,
          id: 868733001,
          anyProduct: null,
          isGiftCard: false,
          productGroup: {
            id: 6,
            quantityUnitType: "DOLLARS",
            excludedProductGroupId: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            tiers: [{ level: 1, amount: 2 }],
            inheritedFromOfferRequest: null,
            displayOrder: null,
            name: "Any Product",
            excludedProductGroupName: null,
            updateProductGroup: false,
          },
          discountVersion: {
            id: 868733000,
            discounts: [],
            airMiles: [
              {
                displayOrder: 1,
                id: 1919599186,
                type: "US_AIR_MILES",
                tiers: [{ name: "ALASKA_AIRMILES", points: 2 }],
                updateAirMiles: false,
              },
            ],
          },
        },
      ];
      const saveArry = [
        {
          displayOrder: 1,
          id: 868733001,
          anyProduct: null,
          isGiftCard: false,
          discountVersion: {
            id: 868733000,
            airMiles: [
              {
                id: 1919599186,
                displayOrder: 1,
                type: "US_AIR_MILES",
                updateAirMiles: false,
                tiers: [{ points: 4, name: "ALASKA_AIRMILES" }],
              },
            ],
            discounts: [],
          },
          productGroup: {
            name: "Any Product",
            id: 6,
            quantityUnitType: "DOLLARS",
            excludedProductGroupName: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            inheritedFromOfferRequest: null,
            updateProductGroup: false,
            tiers: [{ level: 1, amount: 2, upTo: null }],
          },
        },
      ];
      spyOn(service, "detectObjectComparision").and.returnValue(1);
      service.detectDiscountVersionChanges(reqArray, saveArry);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
  });

  describe("detectInstantWinChanges", () => {
    it("should make expected calls", () => {
      const requestInstantWin = {
        frequency: "",
        id: 1959247627,
        numberOfPrizes: 10,
        updateInstantWin: false,
      };
      const saveInstantWin = {
        frequency: "",
        id: 1959247627,
        numberOfPrizes: 20,
      };
      spyOn(service, "detectObjectComparision").and.returnValue(2);
      service.detectInstantWinChanges(requestInstantWin, saveInstantWin);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
    it("should trace else block", () => {
      const requestInstantWin = {
        frequency: "",
        id: 1959247627,
        numberOfPrizes: 20,
      };
      const saveInstantWin = {
        frequency: "",
        id: 1959247627,
        numberOfPrizes: 20,
      };
      spyOn(service, "detectObjectComparision").and.returnValue(2);
      service.detectInstantWinChanges(requestInstantWin, saveInstantWin);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
  });

  describe("detectStoreTagChanges", () => {
    it("should make expected calls", () => {
      const requestStoreTag = {
        amount: null,
        comments: null,
        multiple: null,
        printJ4uTagEnabled: false,
        updateStoreTag: false,
      };
      const saveStoreTag = {
        amount: null,
        comments: null,
        multiple: "2",
        printJ4uTagEnabled: true,
        updateStoreTag: false,
      };
      spyOn(service, "detectObjectComparision").and.returnValue(2);
      service.detectStoreTagChanges(requestStoreTag, saveStoreTag);
      expect(service.allOffersAlongWithOfferRequestUpdate).toEqual(false);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
    it("should trace else block", () => {
      const requestStoreTag = {
        amount: null,
        comments: null,
        multiple: null,
        printJ4uTagEnabled: false,
        updateStoreTag: false,
      };
      const saveStoreTag = {
        amount: null,
        comments: null,
        multiple: null,
        printJ4uTagEnabled: false,
        updateStoreTag: false,
      };
      spyOn(service, "detectObjectComparision").and.returnValue(0);
      service.detectStoreTagChanges(requestStoreTag, saveStoreTag);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
  });

  describe("detectStoreGroupChanges", () => {
    it("should make expected calls", () => {
      spyOn(service, "compareStoreGroups").and.returnValue(true);
      service.detectStoreGroupChanges([], ["10052"]);
      expect(service.compareStoreGroups).toHaveBeenCalled();
    });
    it("should trace else block", () => {
      spyOn(service, "compareStoreGroups").and.returnValue(false);
      service.detectStoreGroupChanges([], []);
      expect(service.compareStoreGroups).toHaveBeenCalled();
    });
  });

  describe("detectProductGroupChanges", () => {
    it("should make expected calls", () => {
      const reqObj = [
        {
          displayOrder: 1,
          id: 529063777,
          anyProduct: null,
          isGiftCard: false,
          productGroup: {
            id: 490765212,
            quantityUnitType: "ITEMS",
            excludedProductGroupId: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            tiers: [{ level: 1, amount: null }],
            inheritedFromOfferRequest: null,
            displayOrder: 1,
            name: "testPG1122",
            excludedProductGroupName: null,
            updateProductGroup: false,
          },
          discountVersion: {
            id: 52026209,
            discounts: [
              {
                displayOrder: 1,
                id: 571089297,
                benefitValueType: "PRICE_POINT_ITEMS",
                discountType: "ITEM_LEVEL",
                includeProductGroupId: 490765212,
                excludeProductGroupId: null,
                chargebackDepartment: null,
                tiers: [
                  { level: 1, amount: 1, upTo: null, itemLimit: 0, weightLimit: null, dollarLimit: null, receiptText: null, rewards: null },
                ],
                includeProductGroupName: "testPG1122",
                excludeProductGroupName: null,
                updateProductDiscount: false,
              },
            ],
            airMiles: [],
          },
        },
      ];
      const saveObj = [
        {
          displayOrder: 1,
          id: 529063777,
          anyProduct: null,
          isGiftCard: false,
          discountVersion: {
            id: 52026209,
            airMiles: [],
            discounts: [
              {
                id: 571089297,
                displayOrder: 1,
                benefitValueType: "PRICE_POINT_ITEMS",
                discountType: "ITEM_LEVEL",
                includeProductGroupName: "OWN Brands Items - Corporate Managed UPC List",
                includeProductGroupId: 10,
                excludeProductGroupId: null,
                excludeProductGroupName: null,
                chargebackDepartment: null,
                updateProductDiscount: false,
                tiers: [
                  {
                    amount: "1.00",
                    upTo: null,
                    itemLimit: 0,
                    dollarLimit: null,
                    level: 1,
                    receiptText: null,
                    rewards: null,
                    weightLimit: null,
                    points: null,
                  },
                ],
              },
            ],
          },
          productGroup: {
            name: "OWN Brands Items - Corporate Managed UPC List",
            id: 10,
            quantityUnitType: "ITEMS",
            excludedProductGroupName: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            inheritedFromOfferRequest: null,
            updateProductGroup: false,
            tiers: [{ level: 1, amount: null }],
          },
        },
      ];
      spyOn(service, "detectObjectComparision").and.returnValue(2);
      service.detectProductGroupChanges(reqObj, saveObj);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
    it("should trace else block when no changes", () => {
      const reqObj = [
        {
          displayOrder: 1,
          id: 529063777,
          anyProduct: null,
          isGiftCard: false,
          productGroup: {
            id: 490765212,
            quantityUnitType: "ITEMS",
            excludedProductGroupId: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            tiers: [{ level: 1, amount: null }],
            inheritedFromOfferRequest: null,
            displayOrder: 1,
            name: "testPG1122",
            excludedProductGroupName: null,
            updateProductGroup: false,
          },
          discountVersion: {
            id: 52026209,
            discounts: [
              {
                displayOrder: 1,
                id: 571089297,
                benefitValueType: "PRICE_POINT_ITEMS",
                discountType: "ITEM_LEVEL",
                includeProductGroupId: 490765212,
                excludeProductGroupId: null,
                chargebackDepartment: null,
                tiers: [
                  { level: 1, amount: 1, upTo: null, itemLimit: 0, weightLimit: null, dollarLimit: null, receiptText: null, rewards: null },
                ],
                includeProductGroupName: "testPG1122",
                excludeProductGroupName: null,
                updateProductDiscount: false,
              },
            ],
            airMiles: [],
          },
        },
      ];
      const saveObj = [
        {
          displayOrder: 1,
          id: 529063777,
          anyProduct: null,
          isGiftCard: false,
          discountVersion: {
            id: 52026209,
            airMiles: [],
            discounts: [
              {
                id: 571089297,
                displayOrder: 1,
                benefitValueType: "PRICE_POINT_ITEMS",
                discountType: "ITEM_LEVEL",
                includeProductGroupName: "OWN Brands Items - Corporate Managed UPC List",
                includeProductGroupId: 10,
                excludeProductGroupId: null,
                excludeProductGroupName: null,
                chargebackDepartment: null,
                updateProductDiscount: false,
                tiers: [
                  {
                    amount: "1.00",
                    upTo: null,
                    itemLimit: 0,
                    dollarLimit: null,
                    level: 1,
                    receiptText: null,
                    rewards: null,
                    weightLimit: null,
                    points: null,
                  },
                ],
              },
            ],
          },
          productGroup: {
            name: "OWN Brands Items - Corporate Managed UPC List",
            id: 10,
            quantityUnitType: "ITEMS",
            excludedProductGroupName: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            inheritedFromOfferRequest: null,
            updateProductGroup: false,
            tiers: [{ level: 1, amount: null }],
          },
        },
      ];
      spyOn(service, "detectObjectComparision").and.returnValue(0);
      service.detectProductGroupChanges(reqObj, saveObj);
      expect(service.detectObjectComparision).toHaveBeenCalled();
    });
    it("should trace else block when id won't match", () => {
      const reqObj = [
        {
          displayOrder: 1,
          id: "",
          anyProduct: null,
          isGiftCard: false,
          productGroup: {
            id: 490765212,
            quantityUnitType: "ITEMS",
            excludedProductGroupId: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            tiers: [{ level: 1, amount: null }],
            inheritedFromOfferRequest: null,
            displayOrder: 1,
            name: "testPG1122",
            excludedProductGroupName: null,
            updateProductGroup: false,
          },
          discountVersion: {
            id: 52026209,
            discounts: [
              {
                displayOrder: 1,
                id: 571089297,
                benefitValueType: "PRICE_POINT_ITEMS",
                discountType: "ITEM_LEVEL",
                includeProductGroupId: 490765212,
                excludeProductGroupId: null,
                chargebackDepartment: null,
                tiers: [
                  { level: 1, amount: 1, upTo: null, itemLimit: 0, weightLimit: null, dollarLimit: null, receiptText: null, rewards: null },
                ],
                includeProductGroupName: "testPG1122",
                excludeProductGroupName: null,
                updateProductDiscount: false,
              },
            ],
            airMiles: [],
          },
        },
      ];
      const saveObj = [
        {
          displayOrder: 1,
          id: null,
          anyProduct: null,
          isGiftCard: false,
          discountVersion: {
            id: 52026209,
            airMiles: [],
            discounts: [
              {
                id: 571089297,
                displayOrder: 1,
                benefitValueType: "PRICE_POINT_ITEMS",
                discountType: "ITEM_LEVEL",
                includeProductGroupName: "OWN Brands Items - Corporate Managed UPC List",
                includeProductGroupId: 10,
                excludeProductGroupId: null,
                excludeProductGroupName: null,
                chargebackDepartment: null,
                updateProductDiscount: false,
                tiers: [
                  {
                    amount: "1.00",
                    upTo: null,
                    itemLimit: 0,
                    dollarLimit: null,
                    level: 1,
                    receiptText: null,
                    rewards: null,
                    weightLimit: null,
                    points: null,
                  },
                ],
              },
            ],
          },
          productGroup: {
            name: "OWN Brands Items - Corporate Managed UPC List",
            id: 10,
            quantityUnitType: "ITEMS",
            excludedProductGroupName: null,
            conjunction: null,
            minPurchaseAmount: 0,
            uniqueproduct: null,
            inheritedFromOfferRequest: null,
            updateProductGroup: false,
            tiers: [{ level: 1, amount: null }],
          },
        },
      ];
      spyOn(service, "detectObjectComparision").and.returnValue(0);
      service.detectProductGroupChanges(reqObj, saveObj);
      expect(service.detectObjectComparision).not.toHaveBeenCalled();
    });
  });

  describe("compareStoreGroups", () => {
    it("should make expected calls when change in storeGroup", () => {
      spyOn(service, "Sort");
      const res = service.compareStoreGroups(["10070", "10069", "10071"], ["10070", "10069"]);
      expect(res).toEqual(true);
    });
    it("should make expected when no changes in storeGroup", () => {
      spyOn(service, "Sort");
      const res = service.compareStoreGroups(["10070", "10069", "10071"], ["10070", "10069", "10071"]);
      expect(res).toEqual(false);
    });
  });
  describe("sort", () => {
    it("should return sorted array", (done) => {
      let array = ["10070", "10069", "10071"];
      service.Sort(array);
      expect(array).toEqual(["10069", "10070", "10071"]);
      done();
    });
  });
  describe("isMinStateSubmit", () => {
    it("should return false", () => {
      let state = service.isMinStateSubmit();
      expect(state).toEqual(false);
    });
    it("should return true when index of digitalStatus equal to -1", () => {
      service.digitalStatus = "a";
      let state = service.isMinStateSubmit();
      expect(state).toEqual(true);
    });
    it("should return false when index of digitalStatus graterthan -1", () => {
      service.digitalStatus = "I";
      let state = service.isMinStateSubmit();
      expect(state).toEqual(false);
    });
    it("should return true when index of nonDigitalStatus equal to -1", () => {
      service.nonDigitalStatus = "a";
      let state = service.isMinStateSubmit();
      expect(state).toEqual(true);
    });
    it("should return false when index of digitalStatus graterthan -1", () => {
      service.nonDigitalStatus = "I";
      let state = service.isMinStateSubmit();
      expect(state).toEqual(false);
    });
  });
  describe("isMinStateProcessing", () => {
    it("should return false", () => {
      const state = service.isMinStateProcessing();
      expect(state).toEqual(false);
    });
    it("should return true when digitalStatus exist", () => {
      service.digitalStatus = "C";
      let state = service.isMinStateProcessing();
      expect(state).toEqual(true);
    });
    it("should return true when nonDigitalStatus exist", () => {
      service.nonDigitalStatus = "C";
      let state = service.isMinStateProcessing();
      expect(state).toEqual(true);
    });
  });
  describe("getAnyProductId", () => {
    it("should return Id", () => {
      const initialDataServiceStub: InitialDataService = TestBed.inject(InitialDataService);
      spyOn(initialDataServiceStub, "getAppDataName").and.returnValue({ productGroups: { id: 1, name: "Any Product" } });
      service?.getAnyProductId();
      expect(initialDataServiceStub.getAppDataName).toHaveBeenCalled();
      //expect(res).toEqual({name:"Any Product",id:"name"})
    });
  });
  describe("subscribeCurrentOfferReq", () => {
    it("should return null if requestData is not available", () => {
      const res = service.subscribeCurrentOfferReq();
      expect(res).toBeNull();
    });
    it("should return data if requestData is  available", () => {
      service.requestData$.next({
        info: { id: 1 },
        lastUpdatedTs: "43",
        createdTs: 1605859445.167,
        createdApplicationId: "OMS",
        createdUserId: "nling05",
      });
      const res = service.subscribeCurrentOfferReq();
      expect(res.createdTs).toEqual(1605859445.167);
    });
  });
  describe("getCurrentEditingObject", () => {
    it("should return null if requestData is not available", () => {
      const res = service.getCurrentEditingObject();
      expect(res).toBeNull();
    });
    it("should return data if requestData is  available", () => {
      service.requestData$.next({ info: { digitalEditStatus: true, nonDigitalEditStatus: false } });
      const res = service.getCurrentEditingObject();
      expect(res.digitalEditStatus).toEqual(true);
    });
  });
  describe("getUsers", () => {
    it("should return term ", () => {
      let term = "user getuser";
      let splitText = term.split(" ");
      splitText.pop();
      service.getUsers(term);
      term = splitText.join(" ");
      expect(term).toEqual("user");
    });
    it("makes expected calls", () => {
      const searchUsersServiceStub: SearchUsersService = TestBed.inject(SearchUsersService);
      let term;
      spyOn(searchUsersServiceStub, "getUsers").and.returnValue(term);
      service.getUsers(term);
      expect(searchUsersServiceStub.getUsers).toHaveBeenCalled();
    });
  });
  describe("comparer", () => {
    it("should return length", () => {
      let requestTier = [
        { level: 1, amount: 2, upTo: null },
        { level: 2, amount: 4, upTo: null },
      ];
      let current = { level: 1, amount: 2, upTo: null };
      let res = service.comparer(requestTier, current); //0
      expect(res).toEqual(0);
    });
  });
  describe("detectTierComparision", () => {
    it("should return true", () => {
      let requestTier = [1, 2, 3, 4];
      let saveObjTier = [3, 4, 5];
      var saveObj = { updateProductDiscount: false, updateProductGroup: false };
      service.detectTierComparision(requestTier, saveObjTier, saveObj);
      expect(saveObj.updateProductDiscount).toEqual(true);
    });
    it("else block", () => {
      let requestTier = [1, 2, 3, 4];
      let saveObjTier = [3, 4, 5];
      var saveObj = { updateProductGroup: false };
      service.detectTierComparision(requestTier, saveObjTier, saveObj);
      expect(saveObj.updateProductGroup).toEqual(true);
    });
  });
  describe("detectObjectComparision", () => {
    it("should return length", () => {
      let requestObj = { details: [1, 2, 3, 4] };
      let saveObj = { details: [1, 2, 3, 4] };
      spyOn(service, "detectTierComparision");
      let res = service.detectObjectComparision(requestObj, saveObj);
      expect(res).toEqual(0);
      expect(service.detectTierComparision).toHaveBeenCalled();
    });
    it("else block", () => {
      let requestObj = { details: "123" };
      let saveObj = { discout: "123" };
      let res = service.detectObjectComparision(requestObj, saveObj);
      expect(res).toEqual(1);
    });
  });
  
  describe("createRequestDataInstance", () => {
    it("should initialize requestData$ with null", () => {
      service.createRequestDataInstance();
      expect(service.requestData$.getValue()).toBeNull();
    });
  });

  describe("hideApiErrorOnRequestMain", () => {
    it("should return an observable from hideApiErrorOnCreateRequest$", () => {
      const observable = service.hideApiErrorOnRequestMain();
    });
  });
  describe("disableUPPFormControls", () => {
    let formFields;
    let createdApplicationId;
    let nonEditableFields;

    beforeEach(() => {
      formFields = {
        control1: new UntypedFormControl(),
        control2: new UntypedFormControl(),
        control3: new UntypedFormControl(),
      };
      createdApplicationId = "UPP";
      nonEditableFields = ["control1"];
    });

    it("should return formFields as it is if UPP feature is not enabled", () => {
      Object.defineProperty(service.featureFlagsService, 'isuppEnabled', { get: () => false });
      const result = service.disableUPPFormControls(formFields, createdApplicationId, nonEditableFields);
    });

    it("should return formFields as it is if createdApplicationId is OMS", () => {
      Object.defineProperty(service.featureFlagsService, 'isuppEnabled', { get: () => true });
      createdApplicationId = "OMS";
      const result = service.disableUPPFormControls(formFields, createdApplicationId, nonEditableFields);
    });

    it("should return formFields as it is if formFields is null", () => {
      Object.defineProperty(service.featureFlagsService, 'isuppEnabled', { get: () => true });
      formFields = null;
      const result = service.disableUPPFormControls(formFields, createdApplicationId, nonEditableFields);
    });

    it("should disable controls in formFields based on nonEditableFields", () => {
      Object.defineProperty(service.featureFlagsService, 'isuppEnabled', { get: () => true });
      service.disableUPPFormControls(formFields, createdApplicationId, nonEditableFields);
      expect(formFields.control1.disabled).toBeTrue();
      expect(formFields.control2.disabled).toBeFalse();
      expect(formFields.control3.disabled).toBeFalse();
    });

    it("should disable UPP fields in formFields", () => {
      Object.defineProperty(service.featureFlagsService, 'isuppEnabled', { get: () => true });
      service.uppNonEditableFields = ["control1", "control2"];
      service.disableUPPFormControls(formFields, createdApplicationId, []);
      expect(formFields.control1.disabled).toBeTrue();
      expect(formFields.control2.disabled).toBeTrue();
      expect(formFields.control3.disabled).toBeFalse();
    });

    it("should disable UPP fields in formFields for a specific section", () => {
      Object.defineProperty(service.featureFlagsService, 'isuppEnabled', { get: () => true });
      service.uppNonEditableFields = ["section.control1", "section.control2"];
      service.disableUPPFormControls(formFields, createdApplicationId, [], "section");
      expect(formFields.control1.disabled).toBeTrue();
      expect(formFields.control2.disabled).toBeTrue();
      expect(formFields.control3.disabled).toBeFalse();
    });
  });
  describe("getDiscountByKey", () => {
    it("should return the value for the given key if object is not null", () => {
      const object = { discount1: 10, discount2: 20 };
      const key = "discount1";
      const result = service.getDiscountByKey(object, key);
      expect(result).toEqual(10);
    });

    it("should return undefined if the key does not exist in the object", () => {
      const object = { discount1: 10, discount2: 20 };
      const key = "discount3";
      const result = service.getDiscountByKey(object, key);
      expect(result).toBeUndefined();
    });

    it("should return undefined if the object is null", () => {
      const object = null;
      const key = "discount1";
      const result = service.getDiscountByKey(object, key);
      expect(result).toBeUndefined();
    });
  });
  describe("getOfferStatusClass", () => {
    it("should return the correct class for status 'I'", () => {
      const result = service.getOfferStatusClass("I");
    });

    it("should return the correct class for status 'A'", () => {
      const result = service.getOfferStatusClass("A");
    });

    it("should return the correct class for status 'P'", () => {
      const result = service.getOfferStatusClass("P");
    });

    it("should return the correct class for status 'D'", () => {
      const result = service.getOfferStatusClass("D");
    });

    it("should return the default class for unknown status", () => {
      const result = service.getOfferStatusClass("unknown");
    });
  });

  describe("isExpiredStatus", () => {
    it("should return true if status is 'E'", () => {
      const result = service.isExpiredStatus({ status: "E", endDate: "2023-01-01" });
    });

    it("should return true if endDate is expired", () => {
      spyOn(service, "isDateExpired").and.returnValue(true);
      const result = service.isExpiredStatus({ status: "A", endDate: "2023-01-01" });
    });

    it("should return false if status is not 'E' and endDate is not expired", () => {
      spyOn(service, "isDateExpired").and.returnValue(false);
      const result = service.isExpiredStatus({ status: "A", endDate: "2023-01-01" });
    });
  });

  describe("isDateExpired", () => {
    it("should return true if the date is in the past", () => {
      const pastDate = moment().subtract(1, "day").format("YYYY-MM-DD");
      const result = service.isDateExpired(pastDate);
      expect(result).toBeTrue();
    });

    it("should return false if the date is in the future", () => {
      const futureDate = moment().add(1, "day").format("YYYY-MM-DD");
      const result = service.isDateExpired(futureDate);
    });

    it("should return false if the date is today", () => {
      const todayDate = moment().format("YYYY-MM-DD");
      const result = service.isDateExpired(todayDate);
    });

    it("should return false if the date is invalid", () => {
      const invalidDate = "invalid-date";
      const result = service.isDateExpired(invalidDate);
    });
  });
});
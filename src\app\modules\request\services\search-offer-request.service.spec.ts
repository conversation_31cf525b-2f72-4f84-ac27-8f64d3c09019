import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController} from '@angular/common/http/testing';
import { SearchOfferRequestService } from './search-offer-request.service';
import { BehaviorSubject, from, of, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { AuthService } from '@appServices/common/auth.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { AdminStoreGroupService } from '@appAdminServices/admin-store-group.service';
import { OfferDetailsService } from '@appOffersServices/offer-details.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { UntypedFormBuilder } from '@angular/forms';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { fakeAsync } from '@angular/core/testing';
import * as moment from 'moment';
import { CONSTANTS } from '@appConstants/constants';

describe('SearchOfferRequestService', () => {
  let service: SearchOfferRequestService;
  let httpMock: HttpTestingController;
  let authServiceStub: Partial<AuthService>;
  let offerDetailsService: jasmine.SpyObj<OfferDetailsService>;
  let adminStoreGroupService: jasmine.SpyObj<AdminStoreGroupService>;
  let httpClientSpy: jasmine.SpyObj<HttpClient>;
  let featureFlagServiceStub: jasmine.SpyObj<FeatureFlagsService>;
  let generalOfferTypeServiceStub: jasmine.SpyObj<GeneralOfferTypeService>;

  const formBuilder = new UntypedFormBuilder();
  beforeEach(() => {
    const offerDetailsSpy = jasmine.createSpyObj('OfferDetailsService', ['listOfferDetailsCode']);
    const adminStoreGroupServiceSpy = jasmine.createSpyObj('AdminStoreGroupService', ['listCorporateStoreGroups']);
    const httpClient = jasmine.createSpyObj('HttpClient', ['post']);
    const commonSearchServiceMock = jasmine.createSpyObj('CommonSearchService', ['isShowExpiredInQuery_O']);
    const generalOfferTypeSpy = jasmine.createSpyObj('GeneralOfferTypeService', ['isEcommOnlyEnabled']);
    const featureFlagServiceSpy = jasmine.createSpyObj('FeatureFlagsService', ['isFeatureFlagEnabled', 
      'assignFeatureFlag', 'hasFlags']);
    authServiceStub = {
      getTokenString: () => 'test-token'
    };
    const initialDataServiceStub = () => ({
      getAppData: () => ({
        regions: [],
        offerPrograms: {
          PD: "PD",
          SPD: "Sp PD",
          SC: "Store Coupon",
          GR: "Grocery Reward",
          MF: "Manufacturer Coupon",
        },
        offerRequestStatuses: {
          I: "Draft",
          S: "Submitted",
          A: "Assigned",
          P: "Processing",
          E: "Editing",
          U: "Updating",
          D: "Completed",
          C: "Cancelled",
          R: "Removing",
        },
        offerStatuses: {
          I: "Draft",
          R: "Ready To Approve",
          A: "Approved",
          C: "Cancelled",
          E: "Expired",
          D: "Deactivated",
          DE: "Deployed",
          PU: "Published",
          CN: "Cancelled",
          P: "Preview",
        },
        offerDeliveryChannels: {
          CC: "Clip and Click",
          DO: "Digital Only",
          IS: "In Store",
          PO: "Print Only",
        },
        offerProgramSubTypes: {
          1: "HOT OFFER",
          2: "SUPER COUPON",
          3: "THEMED COUPON",
          4: "COUPON BOOK",
          5: "BUSINESS UNIT",
          6: "PARTNER REWARD",
          7: "BOX TOP",
          8: "HTO",
          9: "MC",
          20: "MONOPOLY OFFER",
          21: "MONOPOLY FUEL REWARD",
        },
        amountTypes: {
          AMOUNT_OFF: "Cents Off",
          AMOUNT_OFF_WEIGHT_VOLUME: "Cents Off (Per Lb)",
          FREE: "Free",
          PRICE_POINT_ITEMS: "Price Point (Items)",
          PRICE_POINT_WEIGHT_VOLUME: "Price Point (Per Lb)",
          PERCENT_OFF_ITEMS: "Percent Off",
        },
        discountTypes: {
          ITEM_LEVEL: "Item Level",
          DEPARTMENT_LEVEL: "Department Level",
          BASKET_LEVEL: "Basket Level",
        },
        offerRequestLimits: {
          UNLIMITED: "No Limits",
          ONE_TIME_USE: "One Time Use",
        },
        offerLimits: {
          UNLIMITED: "No Limits",
          LIMITED: "Once per Transaction",
          ONCE_PER_OFFER: "Once per Offer",
          ONCE_PER_DAY: "Once per Day",
          ONCE_PER_WEEK: "Once per Week",
          CUSTOM: "Custom",
        },
        departments: [
          "Advertising",
          "Alcohol Tobacco",
          "Beverage Snacks",
          "Commercial Bakery",
          "Consumer Brands",
          "Dairy Refrigerated",
          "Deli",
          "Family Care",
          "Floral",
          "Food Service",
          "Frozen",
          "General Merchandise",
          "Gift Cards & Telecom",
          "Home Care",
          "In Store Bakery",
          "Loyalty",
          "Main Meal",
          "Meals & Ingredients",
          "Meat",
          "Pricing",
          "Produce",
          "Seafood",
          "Shopper Marketing",
          "SMS",
          "Starbucks & Jamba Juice",
        ],
        billingOptions: ["Full Division", "ROG Specific", "Store Specific"],
        offerCustomerSegments: [
          "Any Customer",
          "Employee Only",
          "Customer Only",
        ],
        offerUsageLimits: {
          user: ["LIMITED", "UNLIMITED"],
          offer: ["LIMITED", "UNLIMITED"],
          txn: ["LIMITED", "UNLIMITED"],
          day: ["LIMITED", "UNLIMITED"],
          week: ["LIMITED", "UNLIMITED"],
        },
        productCategories: {
          1: "Baby Care",
          2: "Bread & Bakery",
          5: "Beverages",
          6: "Grains, Pasta & Sides",
          7: "Breakfast & Cereal",
          9: "Canned Goods & Soups",
          11: "Dairy, Eggs & Cheese",
          12: "Deli",
          13: "International Cuisine",
          14: "Flowers & Decor",
          15: "Frozen Foods",
          17: "Personal Care & Health",
          18: "Paper, Cleaning & Home",
          19: "Meat & Seafood",
          20: "Condiments, Spices & Bake",
          22: "Pet Care",
          23: "Fruits & Vegetables",
          24: "Cookies, Snacks & Candy",
          29: "Wine, Beer & Spirits",
          33: "Tobacco",
          34: "Special Offers",
        },
        banners: {
          1: "banner1",
          2: "banner2",
        },
        scorecards: {
          1: "CAM AutoGen Scorecard",
          4: "Personalized Savings (J4U)",
          5: "TEST Scorecard",
          6: "TEST 2 Scorecard",
          7: "Employee Discount Scorecard",
          8: "Loyalty Rewards Points",
          9: "Loyalty Rewards Certificate",
          10: "Wendy TEST SV",
          11: "Ngan Test Scorecard",
          12: "Turkey Savings",
          17: "NP Dummy 2",
          20: "Fuel Points",
          24: "Test Alaska Air Scorecard QN",
          27: "Monopoly",
          28: "NFL Gear Points",
          29: "Farberware Program",
          30: "SOCAL Pepsico NFL Gear Promo",
          31: "Taruna Test Scorecard",
          32: "Animal Planet",
          33: "Seahawks Sweeps",
          34: "Sharks Score & Win",
          36: "Sam Test",
          37: "Pepsi Superbowl",
          38: "Cookware",
          39: "Norcal Cheer",
          40: "Luminarc",
          41: "Copient Use Case Offer SC",
          42: "Copient User Case Offer Mono",
          43: "Denver Game Time",
          44: "Haggen Sweeps",
          45: "Seattle Shop n Score",
          46: "Norcal Sweeps",
          47: "Taruna's Test Scorecard",
        },
        divisions: {
          0: {
            name: "Corporate",
            code: "CORP",
          },
          5: {
            name: "Denver",
            code: "DN",
          },
          17: {
            name: "Southwest",
            code: "SW",
          },
          19: {
            name: "Portland",
            code: "PT",
          },
          20: {
            name: "Southern",
            code: "SO",
          },
          24: {
            name: "Haggen",
            code: "HG",
          },
          25: {
            name: "Norcal",
            code: "NC",
          },
          26: {
            name: "Hawaii",
            code: "HI",
          },
          27: {
            name: "Seattle",
            code: "SE",
          },
          28: {
            name: "Alaska",
            code: "AL",
          },
          30: {
            name: "Intermountain",
            code: "IM",
          },
          32: {
            name: "Jewel",
            code: "JO",
          },
          33: {
            name: "Shaws/Star Market",
            code: "SH",
          },
          35: {
            name: "Eastern",
            code: "EA",
          },
          1000: {
            name: "SoCal",
            code: "SC",
          },
        },
        events: {
          125: "Weekly Ad Coupons",
          425: "Theme 3-5",
          524: "Save on P&G Products",
          824: "Theme 1-5",
          924: "Theme 2-1",
          1024: "$5 Friday",
          1226: "Catch All 4",
          1324: "Summer Savings",
          1424: "Box Tops for Education",
          1624: "Ice Cream Savings",
          1724: "Savings from Kraft",
          1725: "Yoplait",
          1824: "New! Only in Our Stores",
          1827: "Savings from Hormel",
          1828: "Starbucks Fall Flavors",
          1924: "Nabisco Snack Favorites",
          1925: "Cough & Cold Savings",
          2024: "Amazing Deals",
          2025: "Fall Refreshments",
          2124: "Sweet Savings",
          2125: "Seattle's Best Savings",
          2127: "Frozen Food Month",
          2224: "Nestle Frozen Favorites",
          2225: "Colgate Savings",
          2227: "Test Empty Event",
          2324: "GM Favorites",
          2325: "Easter Brunch",
          2326: "Nestle Favorites",
          2327: "CB Brands",
          2424: "Taco Night",
          2425: "Kids Lunches",
          2426: "Unilever Personal",
          2427: "Method",
          2428: "Toms of Maine",
          2527: "Unilever Earth",
          2627: "Meal Italian",
          2628: "Cinco De Mayo",
          2629: "Ghirardelli",
          2727: "Help End Child Hunger",
          2728: "Savings from Unilever",
          2729: "Baby",
          2827: "Memorial Day Deals",
          2828: "Organic",
          2829: "Back to Breakfast",
          2927: "Pizza Night",
          2928: "General Mills Savings",
          2929: "Breakfast Favorites",
          2930: "Grilling",
          3027: "Fourth of July",
          3028: "Deals for U",
          3029: "SCJ Clean",
          3127: "Be Snack-sational",
          3128: "Barilla",
          3129: "Starbucks Holiday",
          3130: "Maybelline",
          3131: "Tyson",
          3132: "Post",
          3227: "Anniversary Sale",
          3228: "Daily Deals",
          3229: "Quaker",
          3230: "Top Your Ritz",
          3327: "NCAA Coke & Mondelez",
          3427: "Get Your Game On!",
          3428: "Unilever Meal",
          3527: "Save with Boar's Head",
          3627: "Fuel Your Trip",
          3727: "#peanutsmovie",
          3728: "Gerber",
          3729: "Save with SC Johnson",
          3730: "Save on Creamers & More",
          3731: "Holiday Entertaining",
          3827: "Gift Cards",
          3828: "Hot Holiday Savings",
          3829: "Save on Bayer Products",
          3830: "Vitamins",
          3831: "MONOPOLY Deals",
          3832: "Sparkling Ice",
          3833: "Theme 3-5",
          3927: "Baking",
          3928: "Earthbound",
          3929: "LALA",
          4027: "Weekend Savings",
          4127: "Quotient",
          4227: "Organic & Natural",
          4228: "Coke New Items",
          4229: "OWN Brands",
          4327: "MONOPOLY Deals",
          4427: "Salute to Savings",
          4428: "Purina",
          4429: "PG Hidden",
          4527: "White Wave Earth Day",
          4528: "Unilever Evergreen",
          4529: "Snacking",
          4530: "Breakfast",
          4531: "Lunch",
          4532: "Dinner",
          4627: "Own Brands 2",
          4727: "Sweetheart Savings",
          4728: "Starbucks Summer",
          4729: "Naturally Delicious",
          4730: "Shop & Score",
          4731: "Save on Activia",
          4827: "Alcohol Tags",
          4828: "Summer Games",
          4927: "Test Events Ivan",
          5027: "Score & Win",
          5127: "Score Great Deals",
          5128: "Save on Nestle Products",
          5129: "Stock Up Sale",
          5130: "Holiday Breakfast",
          5131: "Thanksgiving Deals",
          5132: "Save on Pepsi Products",
          5227: "Hunger Is",
          5228: "O Organics",
          5229: "Save on Horizon",
          5230: "21 Ways for FREE Candy",
          5231: "Kids Wellness",
          5232: "Dannon Savings",
          5233: "Great New Year Savings",
          5327: "Great New Year",
          5427: "Unilever NCAA",
          5428: "Starbucks",
          5429: "Clorox",
          5430: "Weight Watchers",
          5431: "Purina Pet Month Deals",
          5432: "Take Advantage",
          5433: "Employee Offers",
          5527: "L'Oreal",
          5627: "J & J",
          5727: "Reward Savings",
          5728: "Kelloggs Back to School",
          5827: "Bake it Great!",
          5927: "Skincare Savings",
          6027: "Savings from Clorox",
          6028: "Wake Up to Wonderful",
          6029: "Hot Offers",
          6030: "St. Patty's Day Deals",
          6127: "Saturday Sampler",
          6227: "Solaris Paper",
          6228: "Johnsonville",
          6229: "Sweepstakes",
          6327: "J & J Allergy",
          6427: "Odwalla",
          6527: "New Spring Beverages",
          6627: "Graduation Deals",
          6727: "Borden Cheese",
          6827: "June Dairy Month",
          6927: "Better for You",
          7027: "Wake Up to WOW",
          7127: "Healthy & School Ready",
          7227: "Road Trip Must-Haves",
          7228: "Ticket to the Trophy",
          7327: "Yoplait Oui",
          7427: "Breakfast On-the-Go",
          7527: "Game Time Rewards",
          7627: "Pepsi Pregame Sweeps",
          7727: "Online Exclusive",
          7827: "Game On",
          7927: "Save Now with McCormick",
          8027: "FREE Turkey",
          8127: "Black Friday Deals",
          8128: "Weekend Deals!",
          8129: "Feed Their Future",
          8227: "Big Game of Savings",
          8228: "Stocking Stuffers",
          8327: "Make a Wish",
          8427: "New Year Savings!",
          8527: "Red Bull Sweepstakes",
          8528: "Kraft Heinz Sweepstakes",
          8627: "Exclusive Brands",
          8727: "Savings from Yoplait",
          8827: "Flash Sale!",
          8927: "Coke Celtics Sweeps",
          9027: "Passover Savings",
          9127: "Danone Spring Savings",
          9227: "Coke Ballpark Sweeps",
          9327: "FREE Offers",
          9427: "Mother's Day Brunch",
          9428: "Celebrate Pet Month",
          9527: "Earth Day Savings",
          9627: "Aveeno Summer Savings",
          9727: "Summertime Cocktails",
          9827: "Ice Cream Month",
          9927: "Hot Deals!",
          10027: "Savings for Baby",
          10028: "Splash into Summer",
          10127: "80th Anniversary",
          10227: "Save on your Summer BBQ",
          10327: "Frontier Days Sweeps",
          10427: "Beauty Pick Savings",
          10527: "Dr Pepper Cowboys win",
          10627: "All Star Rewards",
        },
        chargeBackVendor: [
          "Unilever",
          "None",
          "CellFire Inc",
          "General Mills Inc",
          "Kimber Clark Corp",
          "Colgate Palmolive",
          "Procter and Gamble",
          "Clorox",
          "Pepperidge Farms",
          "Conagra",
          "Del Monte",
          "Mars Petcare US",
          "Beefmaster Cattlemen LP",
          "BG Products LLC",
          "Fresh Express Inc.",
          "ConAgra/Alexia Foods, Inc.",
          "Conde Nast Publications",
          "DAP World Inc",
          "Newell Rubbermaid Inc",
          "H.J. Heinz Company",
          "Isernio's Sausage Co.",
          "Kellogg Co.",
          "KRAFT FOODS",
          "Cadbury Adams USA LLC",
          "GlaxoSmithKline PLC",
          "Nestle Purina Petcare Company",
          "Nestle USA",
          "Golden Grain",
          "The Inventure Group",
          "Smithfield",
          "Proctor and Gamble",
          "Energizer Holdings Inc.",
          "Malt-o-Meal Company",
          "Wells' Dairy Inc.",
          "Bird's Eye Foods Inc.",
          "Interstate Bakeries Corporation",
          "Hallmark Cards Inc",
          "Energizer Personal Care LLC",
          "i-wireless LLC",
          "ASF",
          "Dr. Pepper",
          "Energizer Personal Care - Schick",
          "Smithfields",
          "The Mentholatum Company, Inc.",
          "McIlhenny Company",
          "Anheuser-Busch Companies, Inc.",
          "The Dannon Company, Inc",
          "Tree Top Inc.",
          "Newman's Own",
          "Dole",
          "Similasan",
          "Maybelline / Cosmair",
          "Burleson's Pure Honey",
          "Foster Farms",
          "Nutramax Laboratories, Inc",
          "Rhodes International",
          "Caribou Coffee",
          "The FRS Company",
          "Valeant Pharmaceuticals North America LLC",
          "Oil-Dri Corp",
          "MCNEIL SPECIALTY PRODUCTS COMPANY",
          "Litehouse Foods",
          "The Hain Celestial Group, Inc.",
          "Welch Foods Inc.",
          "Sturm Foods, Inc.",
          "Simple Green",
          "Turtle Mountain LLC",
          "BAYER CORPORATION-CONSUMER CARE-MORRISTOWN",
          "wheatabiz",
          "Kettle Brand (Diamond Foods)",
          "Georgia Pacific",
          "Hormel",
          "Cargill Salt",
          "US NUTRITION",
          "BUMBLE BEE FOODS, LLC-INTERNATIONAL",
          "WD-40",
          "Sun Products",
          "Blue Dog Bakery",
          "Boulder Canyon",
          "Steaz",
          "Starkist",
          "Reser's Fine Foods",
          "The Dial Corporation",
          "H.J. Heinz Company, L.P",
          "Nestle Purina",
          "Marzetti",
          "Combe Incorporated",
          "Conair Corp",
          "King's Hawaiian Bakery",
          "Keebler",
          "Gorton's",
          "Riviana Foods",
          "Stremicks Heritage Foods",
          "Cole's Quality Foods Inc",
          "Abbott Nutrition",
          "McCormick & Company, Inc",
          "S.C. Johnson & Son, Inc",
          "Square-H Brands Inc",
          "dbmg",
          "KAO Brands",
          "P&G",
          "Mizkan Americas Inc",
          "Odom's Tennessee Pride",
          "Beiersdorf",
          "Novarti's Consumer Health",
          "Starcom (Disney)",
          "Upsher-Smith Laboratories, Inc.",
          "Milton's Baking Company",
          "Flowers Bakeries LLC",
          "Mag. Publishers Assoc",
          "Heinz - Deli Mex",
          "Annie Chun's",
          "Bird's Eye",
          "CARL BUDDIG AND COMPANY",
          "Dole - Salad kits",
          "The Alaska Seafood Marketing Institute",
          "Unilever.",
          "Borges USA",
          "Beiersdorf Inc",
          "Nonnis Foods (NewYorkStyleBrand)",
          "Sun-Rype Products Ltd",
          "Salov North American Corp (Filippo Berio)",
          "Annie Chuns",
          "Wm. Wrigley Jr. Company",
          "Mars Food US, LLC",
          "DR PEPPER SNAPPLE GROUP",
          'Borges- "USA"',
          "Sara Lee Corporation",
          "FRITO-LAY, INC.-PLANO",
          "Mead Johnson & Company Products",
          "Hachette Filpacchi",
          "Coke Sprite",
          "AmeriGas Cylinder Exchange",
          "Morton Salt",
          "La Brea Bakery",
          "Time Distribution Services",
          "3M COMPANY-HOME CARE DIVISION",
          "Coria",
          "Vita Foods",
          "Centurion Marketing Company",
          "The J.M. Smucker Company",
          "Ocean Spray Cranberries",
          "Reckitt Benckiser Inc.",
          "(Church & Dwight)Electrolux Home Care Products",
          "Harris Ranch Beef",
          "Barber Chicken",
          "Green Genius LLC",
          "Merisant Company",
          "Blue Diamond Growers",
          "SARGENTO CHEESE CO. INC.-PLYMOUTH",
          "Crystal Farms",
          "LACTALIS AMERICAN GROUP, INC",
          "White Waves Foods",
          "Custom Food/Kennel Seasons",
          "Starbucks Coffee Company",
          "The Hartz Mountain Corporation",
          "Saputo Cheese USA Inc",
          "Westfarm Foods (Darigold)",
          "Del Monte Fresh Produce",
          "Savory Creations International",
          "BIC USA Inc",
          "Hisamitsu America Inc",
          "TYSON FOODS, INC",
          "RiceTec (RiceSelect)",
          "Pinnacle Foods (Vlassic, Van de Kamps, etc.)",
          "Kaz",
          "Mission Foods",
          "DOMINO FOODS, INC",
          "EOS",
          "Mars Chocolate NA",
          "University Medical",
          "Treasury Wine Estates",
          "Hearthside Solutions LLC",
          "Udi's Healthy Foods LLC",
          "Cot'n Wash, Inc. (Dropps - Laundry Detergent Pacs)",
          "PERFETTI VAN MELLE LTD",
          "Hanover Foods Corporation",
          "REVLON INC.-CORPORATE",
          "Hershey",
          "Solo Cup Company",
          "Summit Brands",
          "Flatout, Inc",
          "Allstar Marketing Group LLC",
          "Bissell Homecare, Inc.",
          "Phillips Foods, Inc.",
          "Sun Valley Dairy - VOSCOS",
          "H.P. Hood, Inc. (Penn Maid)",
          "Dawn Foods Products",
          "Spectrum Brands",
          "Olivia Organics OLOR/Universal",
          "Green Mountain Coffee",
          "Nature's Earth",
          "Factor Nutrition",
          "Thompson Brands - ADORA",
          "Date Pac",
          "TH Foods Inc",
          "ACH Foods",
          "Kikkoman",
          "Otis Spunkmeyer Inc",
          "Whirlpool Corporation",
          "Kerry Food & Bev. (Oregon Chai, Inc)",
          "Beaumont Products, Inc.",
          "Shamrock-Coupon",
          "Pharmavite",
          "Alberto Culver",
          "Beam Global (Jim Beam)",
          "The Hershey Company",
          "Hearst Communications",
          "Frontier Natural",
          "Melitta USA (Melitia Group)",
          "Red Gold",
          "Paramount Farms",
          "Quorn Foods",
          "Delizza Inc",
          "Meredith Corporation",
          "Idahoan",
          "Citrus World (Florida Natural)",
          "Prestige Brands (Medtech)",
          "Peaceful Remedies (RelaxZen)",
          "The Emerson Group (Dynova Labs)",
          "Eulactol USA",
          "Tasteful Selections",
          "JOHN MORRELL & CO-ARMOUR-ECKRICH MEATS",
          "Ruiz Foods",
          "CleanFlame",
          "William Grant & Son's",
          "Chattem Inc",
          "Diageo",
          "Disaronno",
          "Bacardi USA, Inc.",
          "New World Pasta",
          "Paramount Home",
          "J&J Snack Foods",
          "Blacksmith Brands",
          "Pernod Ricard USA/Absolute Vodka",
          "Summer Laboratories",
          "Villadco Inc (brand Banfi Vintners)",
          "Constellation Brands (Toasted Head)",
          "Coty (Sally Hansen)",
          "Irish Dairy Board",
          "Bush Brothers & Company",
          "LAND O LAKES-MOARK",
          "Johnsonville Sausage",
          "Church & Dwight",
          "Windsor Foods",
          "Li Destri Foods",
          "Alva- Amco",
          "Cain Foods",
          "Reynolds Consumer Brands",
          "Reynolds Consumer Products",
          "Bridgford Foods Corporation",
          "Bay Valley Foods",
          "Bob Evans Farms, Inc.",
          "Rich Products Corporation",
          "Land O'Frost",
          "Duraflame",
          "Boiron, Inc.",
          "Daisy Brand, LLC",
          "Miller Coors LLC",
          "Trans-Ocean Products (Jaiba Supremo)",
          "MarFood",
          "Zeeland Farms",
          "High Liner Foods",
          "Hidden Villa Ranch",
          "Claire-Sprayway",
          "Infinium",
          "Challenge Dairy",
          "SCHWAN'S--NORTH AMERICA, INC",
          "Continental Mills",
          "Michael Angelos Gourmet",
          "Merz",
          "Prestige Wine Imports",
          "Ventura Foods",
          "Old Orchard",
          "Ralcorp Frozen Bakery Products",
          "Perfecta Products",
          "Boehringer Ingelheim",
          "Brown-Forman",
          "Matrix Initiatives",
          "SUNNY DELIGHT BEVERAGES COMPANY",
          "Scott's Liquid Gold",
          "Amerifit Brands",
          "Ferrero USA",
          "DSM Nutritional",
          "Bonakemi USA",
          "New Enlgand Coffee Co",
          "Icicle Seafoods",
          "Lifeway Foods, Inc",
          "Kiss Products",
          "Merck Consumer Care (Schering-Plough Healthcare Products, Inc.)",
          "Seventh Generation",
          "LALA Foods",
          "Stash Tea",
          "homax - black flag",
          "sun valley dairy",
          "Eggland's Best",
          "Bruce Foods",
          "Tec Labs",
          "Marcal Paper Mills",
          "world finer foods",
          "Developlus Inc.",
          "Haribo of America, Inc.",
          "Boston Beer Company",
          "Fresh Gourmet",
          "Furmano Foods",
          "Airborne",
          "Specialty Food Group",
          "COOP REGIONS/ORGANIC PRODUCERS",
          "Filled Bagel Industries, LLC",
          "Famous Products",
          "Promotion in Motion",
          "Ricola USA",
          "Nurture Inc",
          "Ciao Bella Gelato LA, Inc.",
          "Biotech Corp.",
          "Central Garden and Pet",
          "CLIF BAR & COMPANY",
          "Best Maid",
          "Tetley USA",
          "DeMets",
          "Quickie Manufacturing",
          "Aveniu Brands",
          "Bacardi USA, Inc..",
          "Wenner Media LLC",
          "Grand Brands, LLC",
          "Isernio Sausage",
          "STONYFIELD FARM",
          "Strom Products",
          "Barilla America, Inc.",
          "Idaho Dairy Products Commission",
          "High Ridge Brands",
          "Greased Lightning",
          "Joy Cone Company",
          "CCA Industries",
          "Dr. Harold Katz, LLC",
          "American Popcorn",
          "Crown Imports LLC",
          "Attune Foods",
          "Mead West Vaco",
          "Traditional Medicinals",
          "Bacardi USA, Inc...",
          "Bacardi USA, Inc....",
          "GFA Brands",
          "POST CEREAL DIVISION-RALCORP HOLDINGS, INC",
          "Perdue Farms",
          "De Wafelbakkers LLC",
          "Golden County Foods, Inc.",
          "Faribault Foods, Inc.",
          "Creta Farms",
          "University Medical.",
          "Concepts In Health, Inc",
          "Carma Laboratories",
          "Sprout Foods",
          "CF Foods (CARVEL CORP-ATLANTA)",
          "Sambazon, Inc.",
          "Ainsworth Pet Nutrition, Inc. (DAD'S PRODUCTS CO./DAD'S DOG FOODS)",
          "Balance Bar",
          "One True Vine",
          "Roots Run Deep",
          "Insight Pharma",
          "CR Brands",
          "Filled Bagel Industries",
          "Peet's Coffee & Tea, Inc.",
          "The Scotts Company",
          "Pepsi Cola Company",
          'BIMBO BAKERIES, USA-HORSHAM- "BBU INC"',
          "General Electric Company on the behalf of GE Home and Business Solutions",
          "Huhtamaki, Americas, Inc.",
          "JOHN MORRELL & CO",
          "NISSIN FOODS CO, INC",
          "ROCHE DIAGNOSTICS",
          "SUNBEAM PRODUCTS, INC-D/B/A JARDEN CONSUMER SOLUTIONS",
          "MASSIMO ZANETTI BEVERAGE USA, INC",
          "BLISTEX, INC",
          "SUNSWEET GROWERS INC",
          "KEN'S FOODS",
          "EASTMAN KODAK COMPANY-ROCHESTER",
          "APPLE & EVE",
          "SNYDER'S-LANCE, INC.",
          'ALOUETTE CHEESE USA INC- "(BC-USA INC)"',
          "BLAIREX LABORATORIES",
          "KEMPS LLC",
          "LEWIS BAKERIES, INC.",
          "Farmland Foods (Cook’s Ham, Inc.)",
          "KOZY SHACK",
          "WM Barr",
          "F GAVINA & SON'S",
          "JOHANNA FOODS",
          "BEL BRANDS USA, INC.",
          "DS WATERS-ATLANTA",
          "Hasbro, Inc",
          "WHITE CASTLE-FOOD PRODUCTS, LLC",
          "Louisiana Fish Fry Products, Ltd.",
          "LINDT & SPRUNGLI",
          "JOHN B. SANFILIPPO & SON, INC.",
          "Schwabe N.A./Enzymatic Therapy, Inc.",
          "PHYSICIANS FORMULA, INC.",
          "FREUDENBERG HOUSEHOLD PRODUCTS",
          "AMERICAN ITALIAN PASTA CO.",
          "PACIFIC WORLD CORP.",
          "SCHIFF NUTRITION INTERNATIONAL",
          "FARMLAND DAIRIES LLC",
          "NEWELL RUBBERMAID, INC.-HUNTERSVILLE",
          "AVERY DENNISON CORPORATION",
          "ANI",
          "HILL'S PET PRODUCT",
          "B&G FOODS, INC",
          "RUSSELL STOVER CANDIES",
          "UNIVERSAL STUDIOS",
          "SUN-MAID GROWERS OF CALIFORNIA",
          "SCA PERSONAL CARE, INC.",
          "SERGEANT'S PET CARE PRODUCTS-OMAHA",
          "PHOENIX BRANDS, LLC",
          "FRESHERIZED FOODS, INC",
          "HEALTHY FOOD HOLDINGS (YOCRUNCH, INC.)",
          "Butterball, LLC",
          "MEDLINE INDUSTRIES, INC",
          "VALIO USA",
          "Paramount Citrus Packing Company",
          "SHURTECH BRANDS, LLC",
          "NONNI'S FOODS LLC",
          "MED FOODS INC.-HOUSTON",
          "FLORIDA'S NATURAL GROWERS",
          "MARUCHAN INC",
          "VI-JON, INC.",
          "Pfizer",
          "Seneca Foods Corporation",
          "Bar-S® 1 LB. Franks or Bologna",
          "Jones Fulfillment",
          "Schreiber Foods",
          "Basic American Foods",
          "Knouse Foods",
          "PURDUE PRODUCTS L.P. (Colace)",
          "E&J Gallo Winery",
          "Goya Foods",
          "Nakoma",
          "ProPhase Labs",
          "Campari America",
          "William J. Deutsch & Sons",
          "Australis",
          "Russian Standard Vodka",
          "Kinders Meats",
          "Craft Brewers Alliance, Inc.",
          "Northwest Natural",
          "Clinical Products",
          "MARIANI PACKING CO",
          "Cumberland Packing Corp.",
          "Associated Hygienic Products",
          "C Mondavi & Sons",
          "SKINNY NUTRITIONAL CORP.",
          "McKee Foods",
          "Alliance Beverage Distributing Company",
          "Biocodex",
          "Oberto",
          "Crayola LLC",
          "Blue Rhino",
          "FOX VIDEO",
          "California Natural Products",
          "Don Sebastiani & Sons",
          "Vitasoy USA Inc",
          "Country Choice Organic",
          "High Ridge Brands.",
          "Glen Oaks",
          "Neuro",
          "Shasta Beverage Corp.",
          "Zevia, Inc.",
          "Proximo Spirits",
          "Method",
          "Clover Stornetta",
          "Agro Farma",
          "Tillamook",
          "Pompeian",
          "North American Breweries",
          "POM Wonderful",
          "Imperial Brands",
          "National Dairy West",
          "R. TORRE & COMPANY, INC",
          "Bee Sweet Citrus",
          "Ecosentials",
          "Heineken USA",
          "Peanut Butter & Co.",
          "Palm Bay International",
          "Henkel",
          "Quintessential Wines",
          "CBS Foods",
          "Golden Sun/Newhall Labs",
          "Charter Baking Co",
          "Rug Doctor, Inc.",
          "Sorbee International, LLC",
          "Fetzer Vineyards",
          "Chopin Imports",
          "Kobrand",
          "California Olive Ranch",
          "Wine Warehouse",
          "Sidney Frank Importing Company",
          "Twinings North America",
          "Garden Protein International",
          "Sahale Snacks, Inc.",
          "Hess Collection winery",
          "InnovAsian Cuisine® Enterprises, Inc.",
          "Mary G.",
          "Australis.",
          "One True Vine.",
          "Beam Inc.",
          "Completely Fresh",
          "King Arthur Flour Company",
          "Foley Family Wines",
          "Dakota Growers Pasta Company (Dreamfields Pasta)",
          'Advanced- "Medical Optics, Inc"',
          "Nulaid Foods, Inc.",
          "Mondelez Global LLC",
          "Dairy Farmers of America, Inc",
          "Wente Family Estates",
          "LYFE Kitchen Retail, Inc.",
          "Storck",
          "Bar Keepers Friend",
          "Juanitas Foods",
          "Don Sebastiani & Sons.",
          "Big Train, Inc.",
          "Honey Naturals, LLC",
          "Ghirardelli",
          "Emmi Roth USA, Inc.",
          "Bell-Carter",
          "Berry Plastics",
          "Annie’s Homegrown",
          "Walker’s Shortbread Inc.",
          "Suncore Products, LLC",
          "SBD Holdings Group Corp",
          "Pine Brothers",
          "EOUCTest",
          "Fage USA Dairy Industry Inc",
          "Cytosport, Inc",
          "Sutter Home Windery (Trinchero Family Estates)",
          "Little Products Co, LLC",
          "Reser’s Fine Foods",
          "The Original Soupman",
          "Popchips",
          "Nor-Cal Beverage Co",
          "Beech-Nut Nitrition Co.",
          "Talenti Gelato e Sorbetto",
          "Delicato Vineyards",
          "La Tortilla Factory Inc",
          "Phillips Distilling Company",
          "Ramar International Corp",
          "Williams Foods",
          "Ste. Michelle Wine Estates Ltd.",
          "American Beverage Marketers",
          "J. Lohr Vineyards & Wines",
          "Southeastern Mills",
          "Arla Foods",
          "Biotech Corporation",
          "Thrive Nutrition",
          "Freeman Beauty Labs",
          "Valvoline Company",
          "Bacardi",
          "Chicken of the Sea International",
          "Glass Agency",
          "Francis Ford Coppola",
          "Snikiddy, LLC",
          "Winebow, Inc.",
          "BelGioioso Cheese",
          "Huneeus Vintners",
          "Veev Spirits",
          "Gourmet Express",
          "McCain Foods, Inc.",
          "One True Vine...",
          "ZICO Beverages",
          "Pacific Natural Foods",
          "Pabst Brewing Co.",
          "Sazerac N.A.",
          "Cameron Hughes Wines",
          "Full Spektrem",
          "Schmitt Sohne Wines",
          "Delallo Italian Foods",
          "Mellace Family Brands",
          "Luxco Inc",
          "Contessa Premium Foods Inc.",
          "Jarden Consumer Solutions",
          "Sonoma Creamery LLC",
          "R. TORRE & COMPANY, INC.",
          "Mark Anthony Brand",
          "Folio Fine Wine Partners",
          "Prosperity Organic Foods",
          "Castle Rock Winery",
          "Victoria Fine Foods, LLC",
          "Scoops",
          "TAJIN INTERNATIONAL CORP",
          "New Belgium Brewing Company, Inc",
          "GOJO Industries, Inc",
          "World Kitchen, LLC",
          "CALBEE NORTH AMERICA, LLC",
          "Promotion In Motion.",
          "Pulmuone Foods USA Inc",
          "ILEX Consumer Products.",
          "Apollo Food Group",
          "Lee Kum Kee",
          "Promax Nutrition Corporation",
          "Crimson Wine Group",
          "Sunstar Americas, Inc",
          "Musco Olives",
          "Gizmo Beverages",
          "Instant Combo Savings",
          "Big Red Inc.",
          "Philips Electronics N AM",
          "Kayem Foods, Inc",
          "Puroast",
          "Delta Carbona L.P",
          "Gizmo Beverages.",
          "Cline Cellars",
          "FIJI Water Company, LLC.",
          "Dr. Fresh, LLC",
          "Circle Foods",
          "Shadow Beverages and Snacks",
          "PANOS Brands LLC",
          "Mission Pharmacal Company",
          "The Wine Group",
          "Laura’s Scudder’s",
          "Day Lee Foods",
          "Avocados from Mexico, Inc.",
          "Kahiki Foods, Inc.",
          "Remy Cointreau",
          "Dr. Praeger's Sensible Foods",
          "Revolution Foods, Inc.",
          "Ren Acquisition",
          "INMAR, INC.",
          "Pernod Ricard USA/Absolute Vodka.",
          "GIOVANNI PASTA/RANA MEAL SLTNS",
          "Wholesome Sweeteners",
          "Borden Dairy",
          "Langer Juice Company, Inc.",
          "National Pasteurized Eggs",
          "Nehemiah",
          "Grand Brands",
          "Rosina Food Products",
          "Botanical Laboratories",
          "Kevita Inc.",
          "On-Cor Frozen Foods, LLC",
          "Chobani, Inc",
          "Southern Wine and Spirits",
          "Purple Wine Co.",
          "Ateeco Inc.",
          "Car Freshner Corporation",
          "The Pictsweet Company",
          "Jackson Family Wines, Inc",
          "Phil’s Fresh Foods",
          "Dr. Oetker USA LLC.",
          "Rustic Crust",
          "Schar USA",
          "Heartland",
          "Palermo Villa, Inc.",
          "Freixenet",
          "R.C. BIGELOW TEA",
          "Zonin USA, Inc",
          "Phillips Farms LLC",
          "Mionetto USA",
          "Friendship Dairies",
          "Quinoa Corp.",
          "PREFERRED BRANDS INT'L",
          "Babich",
          "Daiya Foods",
          "Australian Gold",
          "Manzen LLC",
          "HAMPTON CREEK, INC.",
          "RAYBERN FOODS, INC.",
          "Classic Cooking",
          "Talenti",
          "Talking Rain",
          "TAMPICO BEVERAGES INC.",
          "Hannahmax Baking, Inc.",
          "Premier Foods Inc.",
          "Neuro Brands, Inc.",
          "Distillery No.209",
          "Lil Drug Store Products",
          "Ferrara Candy Company",
          "Bellisio Foods, Inc.",
          "Alcon Laboratories, Inc.",
          "Distant Lands Coffee",
          "Home Market Foods, Inc.",
          "Morgan Rich Corporation",
          "CB Fleet",
          "Fishbowl Spirits, LLC",
          "DE SPIRITS LLC",
          "Savage River, Inc.",
          "Cucina Antica Foods Corp",
          "Motive Marketing",
          "OYSTER BAY WINES USA INC",
          "Hofmann Sausage Co. Inc.",
          "DELIZZA, INC",
          "Noble Foods.",
          "CHATTEM, INC.",
          "SPECTRUM OILS",
          "Bio-nutritional Research Group, Inc.",
          "Dean Foods Co",
          "Allergan",
          "Ehrmann LLC",
          "LAVAZZA PREMIUM",
          "Michele's",
          "Mendocino Wine Co",
          "JAMMIN JAVA CORP",
          "Clougherty Packing",
          "Frontera Foods",
          "G. L. Mezzetta, Inc.",
          "Yes To, Inc.",
          "Bel Brands",
          "Remy Cointreau USA, Inc.",
          "Plum Organics",
          "G’s Fresh Beets Incorporated",
          "VOGUE INTERNATIONAL",
          "Klein Foods, Inc",
          "Don Sebastiani & Sons..",
          "Infirst Healthcare",
          "Safeway",
          "Bai Brands LLC",
          "STUBB'S LEGENDARY",
          "Vita Coco",
          "Core Power®",
          "Rhythm Superfoods",
          "That's How We Roll LLC",
          "Earthbound Farm",
          "O Organics™",
          "Dr. Miracles",
          "Strength of Nature",
          "Live Better Brands LLC",
          "Media Consumer Healthcare",
          "Foundation Consumer Health, LLC",
          "Friendly's Ice Cream, LLC",
          "Treana Winery, LLC",
          "Tomatin",
          "Bertolli®",
          "Mrs. Grissom's Salads, Inc.",
          "Shiner Smokehouse",
          "True Drinks",
          "THe Jel Sert Company",
          "Accolade Wines North America",
          "NIVEA® Body Wash",
          "Moberg Pharma North America LLC",
          "Faultless Starch/Bon Ami Company",
          "Greenkarma, LLC (Baby Mantra)",
          "Arcobasso Foods, Inc.",
          "illy®",
          "Universal Group",
          "Hostess Twinkies",
          "Arm & Hammer Pet Fresh Air Filter",
          "Skeeter Snacks",
          "Hanesbrands Inc.",
          "Ready Pac",
          "CherryMan®",
          "Wolfschmidt Wine, Beer, & Spirits",
          "Community®",
          "First Quality Retail Services",
          "Lipo-Flavonoid® Plus",
          "Canyon Bakehouse",
          "Pentel®",
          "Lip Smacker®",
          "Red's®",
          "Avocados from Mexico",
          "The Hunger Games: Mockingjay - Part 1",
          "Community® Coffee",
          "Steak-Umm",
          "Weight Watchers",
          "Ruffies®",
          "Bear Creek",
          "Hershey's Milk",
          "Back to Nature",
          "NUK®",
          "True Myth",
          "Salada Tea®",
          "GRATIFY®",
          "Zebra Pen",
          "BHUJA® Snacks",
          "VetIQ® Health & Wellness Products",
          "Milo's Kitchen",
          "Stahlbush Island Farms",
          "Budget Saver",
          "Louis Jadot",
          "Sandwich Bros™",
          "PopCorners",
          "a2 Milk®",
          "Zonin Prosecco 750ml",
          "Vega",
          "Vita Bone®",
          "Red Vines® candy",
          "Dillon's Small Batch Distillers",
          "Diurex®",
          "SunButter",
          "Southern Comfort",
          "EVERPRO® GRAY AWAY",
          "Pete and Gerry's",
          "Deep Eddy Vodka",
          "TAJIN",
          "Old Home Foods®",
          "okocat™",
          "Suja",
          "Jack Link's®",
          "Urbane Grain",
          "Madhava Natural Sweeteners",
          "Tony Roma's",
          "Taverrites",
          "Crunch Pak and good2grow",
          "OPTI-FREE®",
          "CLEAR CARE®",
          "Taverrite's",
          "Orgain Nutrition Shakes",
          "Brewla Bars",
          "Brownie Brittle",
          "Boulder Sausage",
          "BullFrog® Sunscreen",
          "Karma Tequila",
          "American Greetings",
          "La Bella",
          "Cabo Chips",
          "Meiji®",
          "Quorn™",
          "Simply 7 Snacks",
          "Natrol",
          "Cow Candy",
          "Barbara's Bakery",
          "thinkThin®",
          "Pendleton® 1910 Rye Whisky",
          "Made In Nature™",
          "JUST water",
          "Synodrin®",
          "Nubian Heritage",
          "Frigo® Cheese Heads®",
          "Good Karma® Dairy Free Flaxmilk",
          "Laughing Glass Cocktails All Natural Margarita",
          "Q Drinks",
          "Lúvo™",
          "FIORA® Tissue & Towels",
          "Freshpet®",
          "Finlandia® Product",
          "noosa® yoghurt",
          "Blue Stop Max",
          "RumChata®",
          "Rodney Strong Vineyards",
          "CELSIUS",
          "Brio®",
          "LION COFFEE",
          "Crispy Green®",
          "Paper Mate®",
          "18 Rabbits Organics",
          "Olly",
          "Mary's Gone Crackers",
          "HORMEL Brand",
          "MARS Valentine's Day",
          "Kellogg's® Eggo®",
          "MorningStar Farms®",
          "Friskies®",
          "Dove",
          "Purina ONE®",
          "Dov",
          "Morning Star Farms®",
          "Purina ONE",
          "Schick® Disposable Razor Pack",
          "Florastor",
          "Cadbury Premium Pouches",
          "Breathe Right®",
          "Dog Chow®",
          "DulcoEase®",
          "Friskies",
          "HERSHEY'S Pudding",
          "Special K®",
          "Mucinex®",
          "Frigo® Cheese Heads",
          "Ziploc®",
          "Scrubbing Bubbles®",
          "Cascadian Farm",
          "Food Should Taste Good",
          "Lipton Tea",
          "I Can't Believe It's Not Butter!",
          "Amope",
          "Amop",
          "BUSH'S Cocina Latina®",
          "Barber Foods",
          "Amope®",
          "CELSIU",
          "LISTERINE®",
          "PURELL®",
          "KRAFT Snack Trios",
          "Hillshire® Snacking",
          "One A Day®",
          "JAMESON® Caskmates Irish Whiskey",
          "Duo Fusion®",
          "REDBREAST® 12 Year Old Irish Whiskey",
          "Flintstones™",
          "HORMEL® Brand",
          "Plum Organics Infant Formula",
          "DIGIORNO®",
          "Girl Scouts Cereal",
          "SUDAFED®",
          "STOUFFER'S®",
          "ROGAINE®",
          "V&V Supremo®",
          "Frank's RedHot®",
          "JOHNSON’S® & DESITIN®",
          "LISTERIN",
          "TYLENOL®",
          "NEUTROGENA®",
          "LISTERI",
          "TYLEN",
          "LISTERINE® SMART RINSE®",
          "PEPCID®, LACTAID®, or IMODIUM®",
          "PERDUE® HEAT & EAT",
          "Best Foods®/ Hellmann's®",
          "Air Wick",
          "Schick Hydro Silk Razor or Refill",
          "DRINK CHOBANI",
          "ARM & HAMMER",
          "Special",
          "ARM & HAMMER™",
          "Vagisil",
          "Always Discreet",
          "Always Discreet Incontinence",
          "Bounce",
          "GEORGE DICKEL",
          "Johnsonville",
          "Bounce Dryer",
          "Cascade",
          "Charmin",
          "Metamucil",
          "Metamucil Product",
          "Dawn",
          "Pampers",
          "Pampers Wipes",
          "Tide",
          "Luvs",
          "The Laughing Cow®",
          "Gain",
          "Tid",
          "Persil®",
          "Frank's RedH",
          "Pillsbury Refrigerated",
          "Almay",
          "Country Crock®",
          "Dillon's Small Batch Distiller",
          "Magnum Ice Cream",
          "Caress",
          "Olay",
          "Olay TE",
          "Pepto Bismol",
          "Head & Shoulders",
          "ARM & HAMME",
          "Aussie",
          "Daw",
          "Febreze",
          "Campbell's®",
          "Best Foods® / Hellmann's®",
          "Bob Evans® Side Dish",
          "Crest",
          "Febrez",
          "Campho-Phenique",
          "Persi",
          "Herbal Essences",
          "alli",
          "Fixo",
          "Quaker® Breakfast Flats",
          "Synodrin",
          "Quaker C",
          "Quaker Hot",
          "Seventh Generatio",
          "Gai",
          "Knorr",
          "Snack Factory® Pretzel Crisps®",
          "Down",
          "Gerber",
          "MOTRIN® or BENGAY®",
          "RoC",
          "Gainn",
          "Progresso",
          "Helper",
          "Tidee",
          "Fixodent",
          "LÄRABAR",
          "Ben & Jerry's®",
          "KLEENEX",
          "Werther's Original Sugar Free",
          "Seventh Generati",
          "Axe®",
          "Fiber One",
          "TEDDY Soft Bakes",
          "Old El Paso",
          "Nature Valley",
          "Oral-B",
          "ZzzQuil",
          "Old Spice",
          "Swiffer",
          "Puffs",
          "Secret",
          "Venus",
          "Swiffer Refill",
          "Vicks",
          "Tidy Cats LightWeight",
          "Swiffer Starter Kit",
          "Stevia In The Raw",
          "Playtex Sport Tampons",
          "Playtex Sport Pads or Liners",
          "Aquaphor",
          "Schick Quattro for Men",
          "Abreva",
          "Breyers® Gelato",
          "Pam",
          "Pamp",
          "Tideee",
          "KRAFT Dressing",
          "Bounty",
          "Pamerp",
          "Tampax",
          "Nature Valley Granola",
          "Quaker Chewy Bars",
          "KRAFT Mayo, MIRACLE WHIP",
          "Lipton",
          "General Mills Fruit Snacks",
          "Veggie Dip",
          "Tide Simply Laundry Detergent",
          "Sunsweet",
          "LIME-A-WAY",
          "POISE",
          "Mylanta® Gas",
          "SC Johnson",
          "L'Oreal Paris",
          "RID-X®",
          "COVERGIRL®",
          "Phillips'® Colon Health®",
          "COVERGIRL + Olay Product",
          "Monk Fruit In The Raw®",
          "DANNON ®",
          "Persil® ProClean® laundry detergent",
          "Mylanta®",
          "Stevia In The Raw®",
          "Similac",
          "Pure Bliss by Similac",
          "V8 Vegetable Juice",
          "Hillshire Farm Lit'l Smokies",
          "PERDUE PERFECT PORTIONS",
          "PERDUE Frozen Fully Cooked Chicken",
          "El Monterey",
          "MiraLAX",
          "Annie's",
          "Liquid-Plumr",
          "Differin",
          "El Monterey Tamales",
          "Hefty Slider Bags",
          "LARABAR",
          "Totino's",
          "Truvía® Stevia Sweetener",
          "U by Kotex",
          "Zest",
          "BOOST",
          "Totinos",
          "DEPEND",
          "POISE Liners",
          "Sunsweet PlumSweets",
          "HORMEL BRAND COMPLEAT",
          "POISE Pads",
          "Big G Cereals",
          "Amy's",
          "Godiva",
          "Miss Jones Baking Co",
          "Lemi Shine",
          "STOPAIN®",
          "Drew's",
          "Bufferin",
          "Hidden Valley Ranch",
          "Renew Life",
          "STILLHOUSE® Whiskey",
          "Anderson's Pure Maple Syrup",
          "Little Hug®",
          "Florajen",
          "Mountain Valley Spring Water",
          "Icelandic Provisions",
          "EcoTools",
          "Harmless Harvest",
          "Wholesome Goodness",
          "Kitchen Accomplice",
          "Bayer Advanced",
          "Jazz™ Apples",
          "V&V Supremo",
          "Beluga Noble Vodka",
          "Buddy Fruits®",
          "Taylor Farms",
          "California Goldminer",
          "Giovanni 2chic",
          "PetArmor Plus for Dogs or Cats",
          "Annie Chunss",
          "Bird Dog Bourbon Whisky",
          "ProYo Smoothie Tubes",
          "Mr. Dell's Hashbrowns",
          "Bantam Bagels",
          "Applegate®",
          "Three Bridges",
          "NuGo Slim - Low Glycemic Protein Bars",
          "Wyman's of Maine",
          "Profoot",
          "Blue Plate",
          "My/Mo™ Mochi Ice Cream",
          "Tejava® The Unsweetened Tea",
          "Kuli Kuli Pure Moringa",
          "Sabra",
          "Lucky Spoon Bakery",
          "Coolhaus Super Premium Ice Cream",
          "RXBAR",
          "Milford Valley",
          "Mama Rosie's",
          "Milo's",
          "Forto Energy Shot",
          "Anchor Hocking®",
          "VOSS Water",
          "CLORALEN® Bleach",
          "Mezcal El Silencio Espadín",
          "Pig's Nose Blended Scotch",
          "Baskin-Robbins®",
          "Obrigado Coconut Water",
          "Kicking Horse® Coffee",
          "Man Cave® Craft Eats",
          "The New Primal",
          "Palmer's®",
          "EPIC® Bar",
          "Divine Chocolate",
          "Madrinas Coffee®",
          "Pilot Pen® G2",
          "HALO TOP",
          "Lightlife®",
          "Chosen Foods",
          "Olé Extra Virgin Olive Oil",
          "Vermont Smoke & Cure®",
          "Health Warrior Chia Bars",
          "SmartyPants® Vitamins",
          "Tres Latin Pupusas",
          "Vegy Vida",
          "SkinSmart Antimicrobial™",
          "Harvest Snaps",
          "Amazing Grass®",
          "A to Z Wineworks",
          "Arby's®",
          "organicgirl®",
          "GODIVA Masterpieces",
          "Kidfresh®",
          "River House Dressing & Marinade",
          "HEX Performance Advanced Laundry Detergent",
          "Ripple®",
          "505 Southwestern",
          "Sir Kensington's",
          "NoDoz® Alertness Aid",
          "Francesco Rinaldi",
          "Duracell",
          "Grainful",
          "Mauna Loa® Macadamia Nuts",
          "Aunt Sue's Raw Honey",
          "Tootsie Christmas Jr. Mints or Child's Play",
          "Biofreeze Pain Reliever",
          "Vienna®",
          "Simple Mills",
          "WaterWipes®",
          "Gloves In A Bottle Shiedling Lotion",
          "Luna & Larry's Coconut Bliss",
          "Froozer",
          "Tim Hortons®",
          "Breyers® delights",
          "Supreme Source®",
          "Seattle's Best Coffee®",
          "Cooked Perfect® Meatballs",
          "Rachael Ray™ Nutrish®",
          "skinnypasta",
          "ATHENS®",
          "SlimFast",
          "John Soules Foods®",
          "Tillamook",
          "Sunny Delight",
          "Sargento Foods Inc.",
          "Shamrock Foods",
          "Dole Packaged Foods LLC",
          "Knouse Foods",
          "Kellogg Company",
          "Mary's Gone Crackers",
          "Sonoma Creamery LLC",
          "Frontier Natural",
          "Tree Top",
          "Skyline Chili",
          "Huhtamaki",
          "Grand Brands",
          "Home Market Foods",
          "Country Choice Organic",
          "Bumble Bee Foods Llc",
          "Lavazza Premium Coffee",
          "Fieldale Farms",
          "Kernel Seasons",
          "Del Monte Fresh Produce",
          "Lindy's Homemade LLC",
          "Jones Dairy Farm",
          "PepsiCo",
          "Morton Salt",
          "Marc Anthony Cosmetics Inc",
          "BEL BRANDS USA",
          "Mr. Dell Foods",
          "3M",
          "Popchips",
          "Faygo Beverages Inc",
          "Flatout",
          "Church & Dwight Co.",
          "Meredith Corporation",
          "Idahoan Foods",
          "Bic Usa Inc",
          "Continental Mills",
          "Ken's Foods",
          "Brown-Forman Corporation",
          "Foster Farms",
          "Juanitas Foods",
          "Revlon Consumer Products Corporation",
          "Hoyu Usa",
          "National Pasteurized Eggs",
          "Chicken Of The Sea Internation",
          "The Scotts Company",
          "Darigold Inc",
          "Jt International Usa Inc",
          "World Kitchen",
          "4C Foods Corp",
          "Coke Sprite CMS Dept. 49000",
          "Ventura Foods Llc",
          "Discus Dental Inc",
          "DS Waters of America",
          "Johnson & Johnson Consumer Inc.",
          "Sue Bee Honey",
          "J.M. Smucker Company",
          "Blue Diamond Growers",
          "Jackson Family Wines",
          "Kruger Products L.P.",
          "Weiman Products Llc",
          "Storck",
          "Saputo Cheese USA",
          "Anchor Bay Entertainment",
          "Blistex",
          "Tootsie Roll Industries",
          "Fujifilm North America Corporation",
          "Pilgrim'S Pride",
          "Tata Global Beverages",
          "New Belgium Brewing Company",
          "Carl Buddig & Company",
          "Solo Cup Company",
          "Kikkoman",
          "Soundview Paper Company",
          "Combe Inc",
          "Domino Foods Inc",
          "Summer Laboratories",
          "McKee Foods Corporation",
          "Bar-S Foods Co",
          "Beech-Nut Nitrition Co.",
          "Late July Organic Snacks",
          "The Hershey Company",
          "Campari America",
          "Faribault Products",
          "Glass Agency",
          "Vienna Beef LTD",
          "Salov North America Corp",
          "Langer Juice Company",
          "Airborne Inc",
          "The Dannon Company",
          "Ganeden Biotech Inc",
          "House-Autry Mills",
          "Idelle Labs",
          "Aspire Brands",
          "The Healthy Beverage",
          "L'Oreal Usa",
          "Bausch & Lomb",
          "McCain Foods",
          "Pactiv Corporation",
          "Tampa Maid Foods",
          "Snyder's-Lance",
          "Daiya Foods Inc",
          "Coria Laboratories",
          "Boulder Sausage ",
          "Francis Ford Coppola",
          "Becton",
          "Borges USA",
          "Fresh Pet",
          "Freudenberg Household Products LP",
          "American Greetings Corporation",
          "Pamela'S Products",
          "Palermo Villa",
          "Pfizer Consumer Healthcare",
          "Schreiber Foods",
          "Williams Foods",
          "Dean Foods Company",
          "Sunsweet Growers",
          "Community Coffee Company",
          "Farming Technology Inc.",
          "Willow Tree Poultry Farm",
          "Diversified Foods",
          "Ateeco Inc.",
          "La Brea Bakery Holdings",
          "NESTLE PURINA PETCARE COMPANY",
          "Procter and Gamble",
          "General Mills",
          "Kevita Inc.",
          "The Hygenic Corp",
          "Tom's Of Maine",
          "Omron Healthcare Inc",
          "Blacksmith Brands",
          "Ainsworth Pet Nutrition",
          "Massimo Zanetti Beverage Usa",
          "Armaly Brands",
          "Delizza Inc",
          "Gold Medal Bakery",
          "Clarion Brands",
          "Hanover Foods Corporation",
          "Rosina Food Products",
          "Stremicks Heritage Foods",
          "Boiron Inc",
          "Concepts In Health",
          "Bush Brothers & Company",
          "Aveniu Brands",
          "Markwins Beauty Products Inc.",
          "Cabot'S Creamery Co",
          "Paramount Home",
          "Botanical Laboratories",
          "Phillips Distilling Company",
          "Welmedix LLC",
          "Spectrum Brands",
          "Twentieth Century Fox",
          "Hood River Distillers",
          "LiDestri Foods",
          "Alacer Corporation",
          "Dawn Food Products",
          "Phoenix Brands LLC",
          "Edgewell Personal Care",
          "The Hain Celestial Group",
          'Unilever- ""',
          "Quickie Manufacturing",
          "Starbucks Coffee Company",
          "Seneca Foods Corporation",
          "Green Mountain Coffee",
          "Vitasoy Usa Inc",
          "Butler Home Products",
          "Columbus Manufacturing",
          "Philips Consumer Lifestyle",
          "Sabra Dipping Company",
          "Welch Foods Inc.",
          "Dr. Praeger'S",
          "Yarnell Ice Cream Inc",
          "Rodney Strong Wine Estates",
          "Fredrick Wildman & Sons",
          "Sony Pictures Home Entertainment Inc.",
          "Beiersdorf",
          "Enjoy Life Natural Brands",
          "Wellpet Llc",
          "Piping Rock Health Products",
          "Kiss My Face Corp",
          "Garden Of Life",
          "Barilla America",
          "Castor & Pollux Pet Works",
          "Dietz & Watson Inc",
          "Traditional Medicinals",
          "SunButter",
          "Back to Nature Foods Company",
          "Applegate",
          "TRP Company",
          "CJ America",
          "E&J Gallo Winery",
          "Celsius",
          "Mann Packing Co",
          "The Original Soupman",
          "Boulder Brands",
          "William Grant & Sons",
          "Golden County Foods",
          "Can-Am Care",
          "Ruiz Foods",
          "InnovAsian Cuisine??_ Enterprises",
          "Northwest Natural Products Inc",
          "Th Foods Inc",
          "Arla Foods USA",
          "Emmi Roth USA",
          "Nutrisystem Everyday",
          "Taylor Fresh Foods",
          "Fischer & Wieser Specialty Fds",
          "Barbara'S Bakery",
          "Sidney Frank Importing Company",
          "Kiss Products",
          "Atticus Bakery LLC",
          "The Frs Company",
          "Basic American",
          "Pernod Ricardo ",
          "Green Dot Corporation",
          "Litehouse Inc",
          "Old World Industries",
          "Winebow",
          "Post Consumer Brands LLC ",
          "V&V Supremo",
          "Happy Family Brands",
          "Olivia'S Organics",
          "Sambazon",
          "Merck Consumer Care (Schering-Plough Healthcare Products",
          "Peanut Butter & Co.",
          "Atlantic Premium Brands",
          "Cedarlane Natural Foods",
          "Beaumont Products",
          "Rana Meal Solutions LLC",
          "Castrol North America Auto",
          "Sun Products",
          "Attune Foods",
          "Old Orchard Brands Llc",
          "Otis Spunkmeyer Inc",
          "Caribou Coffee",
          "Electrolux",
          "Zevia",
          "American Foods Group",
          "Bona AB",
          "The Sherwin Williams Company",
          "TTi Floorcare North America",
          "PERFETTI VAN MELLE BENELUX B.V. (Mentos Gum)",
          'thinkThin LLC- ""',
          "Nice Pak Inc",
          "Ste. Michelle Wine Estates Ltd.",
          "Wenner Media LLC",
          "Volume Brands International",
          "Vogue International",
          "Pacific Natural Foods",
          "Central Garden & Pet Co",
          "Butterball",
          "Solskyn Personal Care LLC",
          "Bayer CropScience LP.",
          "Clover Stornetta",
          "Coromega Company",
          "Boston Beer Company",
          "Wholesome Sweeteners",
          "Ciao Bella Gelato LA",
          "Dave's Killer Bread Inc",
          "Seventh Generation",
          "Carma Laboratories",
          "Naturesweet Ltd",
          "Jasper Wyman & Son",
          "Folio Fine Wine Partners",
          "Red Bull",
          "California Milk Advisory Board ",
          "Divine Chocolate",
          "KAS Direct LLC",
          "Walkers Shortbread Inc",
          "Annie'S Homegrown Inc",
          "Health Care Products",
          "Bacardi Usa",
          "Amy's Kitchen",
          "Blue Magic Inc",
          "Good Karma Foods",
          "Dentovations",
          "Monster Energy Company",
          "Koch Foods",
          "Fage USA Dairy Industry Inc",
          "Ajinomoto Frozen Foods",
          "Southeastern Mills",
          "Giovanni Cosmetics",
          "Cline Cellars",
          "Fisher-Price",
          "Talking Rain Beverage Co.",
          "Mars",
          "Chobani",
          "World Finer Foods",
          "FGX International Inc.",
          "Mars Petcare US",
          "Cf Foods Llc",
          "G. L. Mezzetta",
          "Heartland Sweeteners",
          "Renew Life Inc",
          "Mars Chocolate North America",
          "Beaver Street Fisheries",
          "Proximo Spirits",
          "Kellogg Company (Kashi & Keebler)",
          "OSEM USA",
          "Sun-Rype Products Ltd.",
          "Weight Watchers International",
          "Velvet Ice Cream Co",
          "Louisiana Fish Fry Products",
          "Delicato Vineyards",
          "American Pop Corn",
          "Hummus Gourmet LLC",
          "Puroast",
          "Yes To Inc. Usa",
          "Kao Brands",
          "Mighty Leaf Tea",
          "McIlhenny",
          "McCormick & Company",
          "Made In Nature",
          "Diamond Foods",
          "Burleson'S Pure Honey",
          "Munchkin Inc",
          "Greencore Usa",
          "Remy Cointreau",
          "Castle Rock Winery",
          "i-Health",
          "Genesis Today",
          "Conair Corp.",
          "American Lifeline",
          "Upsher-Smith Laboratories",
          "Cedar's Mediterranean Foods Inc",
          "Jtm Provisions Company",
          "Starkist Company",
          "Russian Standard Vodka",
          "Hydralyte LLC",
          "WM Barr",
          "California Olive Ranch",
          "Mga Entertainment",
          "Natrol LLC",
          "Blue Dog Bakery",
          "Snikiddy",
          "Idaho Dairy Products Commission",
          "Activision Publishing",
          "Reily Foods Company",
          "Evernutrition",
          "Green Genius",
          "Abbott Diabetes Care Inc",
          "Sturm Foods",
          "Bell-Carter",
          "Farley'S Sathers Candy Company",
          "Bell Pharmaceuticals Inc",
          "Wellspring Pharmaceutical Corp.",
          "Blackhawk Network Holdings",
          "Biocodex",
          "Sprout Foods",
          "Clemmy'S Ice Cream",
          "Nature's Plus",
          "Southeastern Mills",
          "ShurTech Brands",
          "The Power Of Fruit",
          "Paramount Citrus Packing Company",
          "Treasury Wine",
          "Claire-Sprayway",
          "Hisamitsu America",
          "NUK-USA LLC",
          "Creta Farms Usa",
          "Nehemiah Manufacturing Company LLC",
          "Mam Usa Corporation",
          "DERMA E",
          "Marfood Usa",
          "Prosperity Organic Foods",
          "Woodway Beverage Partners",
          "Crown Imports",
          "United Pet Group",
          "The Icelandic Milk and Skyr Corporation",
          "Factor Nutrition",
          "Cytosport",
          "De Wafelbakkers LLC",
          "Holloway House Inc",
          "Fruit Of The Loom",
          "French Transit Ltd.",
          "Coty US LLC.",
          "Completely Fresh",
          "Crimson Wine Group",
          "Newman'S Own",
          "CCA Industries",
          "Heaven Hills Distilleries",
          "Vaska Inc.",
          "Nana'S Cookie Company",
          "Ricola Usa",
          "Pan-O-Gold Baking Co.",
          "R. TORRE & COMPANY",
          "Luna & Larry's Coconut Bliss",
          "18 Rabbits Inc",
          "KF Wholesale LLC",
          "Foster Farms Dairy",
          "Relaxzen Inc.",
          "Tasteful Selections",
          "Handi-Foil Corporation",
          "Konsyl Pharmaceuticals",
          "Sovena Usa",
          "Graeter'S Manufacturing Co.",
          "Michael Foods Inc.",
          "Drew's LLC",
          "Maple Leaf Foods Inc.",
          "Sunstar Americas",
          "Vilore Foods",
          "Kimberly-Clark",
          "Valley Fine Foods",
          "Talenti Gelato e Sorbetto",
          "Zarbee's Inc.",
          "Lifestyle Evolution",
          "Champion Foods",
          "Oregon Ice Cream Co",
          "Mega Brands Inc",
          "Canidae Corporation",
          "Oasis Brands",
          "Cooljuice Beverage Company",
          "Allergan Inc",
          "Stoli Group (USA)",
          "Kangaroo Brands",
          "Irving Consumer Products",
          "DenTek Oral Care",
          "Pompeian",
          "Good Foods Group LLC",
          "Chloe's Soft Serve Fruit",
          "Teva Pharmaceuticals USA",
          "Manischewitz Company",
          "GOJO Industries",
          "MATERNE North America",
          "Drink Hoist",
          "Dr. Shar U.S.A",
          "Rocasuba",
          "Isernio Sausage",
          "Finlandia Cheese Inc.",
          "Brioschi Pharmaceuticals Int'L Llc",
          "Walter P. Rawl & Sons",
          "California Natural Products",
          "Wholesome Harvest Baking LLC",
          "High Ridge Brands Co. ",
          "Australian Gold",
          "D.F. Stauffer Biscuit Company",
          "Nonni's Food Company",
          "Evofem Inc",
          "C.B. Fleet Company",
          "Icicle Seafoods",
          "Haribo Of America",
          "United Juice Companies Of America",
          "Purple Wine Co.",
          "Nakoma Products LLC",
          "Suncore Products",
          "Noosa Yoghurt",
          "Berry Plastics",
          "Sokol & Co",
          "Spin Master Ltd",
          "Time Distribution Services",
          "Arthur Dogswell LLC.",
          "Filled Bagel Industries",
          "Dr. Reddy's Laboratories Ltd.",
          "John Soules Foods",
          "Milo's Tea Company Inc.",
          "Waterpik Inc",
          "Productos Del Sol Corporation",
          "Borden Dairy Company",
          "The Inventure Group",
          "Weight Watchers International",
          "Lesaffre Yeast Corporation",
          "Valeant Pharmaceuticals North America Llc",
          "Vigo Importing Company",
          "Red's All Natural",
          "Deoleo USA",
          "Royal Ridge Fruits & Cold Storage",
          "Turkey Hill Dairy",
          "Iman Cosmetics",
          "Mamma Chia LLC",
          "King Arthur Flour Company",
          "Perfecta Products",
          "Guylian USA Inc",
          "DeLallo Italian Foods",
          "Stonyfield Farm",
          "Kaz Inc",
          "Red Gold Inc",
          "Insight Pharmaceuticals",
          "Musco Olives",
          "Nutramax Laboratories Veterinary Sciences Inc",
          "Apple & Eve LLP",
          "Don Sebastiani & Sons",
          "Hidden Villa Ranch",
          "Summit Brands",
          "Sun Orchard",
          "Rhodes International",
          "Melitta Usa (Melitia Group)",
          "F. Gavina & Son's (Don Francisco's coffee)",
          "Cropp Cooperative",
          "First Quality Enterprises",
          "General Electric on behalf of GE Home & Business Solutions",
          "CLIF BAR & COMPANY",
          "Dairy Farmers of America",
          "Wise Foods",
          "FIJI Water Company",
          "Gourmet Express",
          "Alen USA LLC",
          "Oil-Dri Corporation Of America",
          "Garden Protein International",
          "Profoot",
          "Wd-40",
          "Man Cave Craft Eats",
          "Avery Dennison",
          "Good Culture",
          "NibMor Inc",
          "National Pork Board",
          "Armored Autogroup",
          "Saputo Dairy Foods USA",
          "Foley Family Wines",
          "Farmland Foods",
          "Ouhlala Gourmet Corp",
          "Us Nutrition Inc",
          "North American Breweries",
          "Innovasource Llc",
          "Maty'S Healthy Products",
          "Solid Gold Health Products For Pets",
          "Canyon Bakehouse",
          "Mikawaya",
          "Sbd Holdings Group Corp",
          "Kevita Inc.",
          "Desert Pepper Trading Co.",
          "Wente Family Estates",
          "Mana Products",
          "Brookside Foods Ltd",
          "Promax Nutrition Corporation",
          "Hearthside Solutions Llc",
          "Jrb Foods",
          "Dr Pepper Snapple Group",
          "E.T. Browne Drug Company Inc",
          "Kodiak Cakes LLC ",
          "Hallmark Cards",
          "Palm Bay International",
          "Califia Farms LP ",
          "Stash Tea",
          "Preferred Brands Int Llc",
          "Sebamed USA",
          "Victoria Fine Foods",
          "Intevation Food Group Llc",
          "Simple Green",
          "All Market",
          "Milton??_S Baking Company",
          "Twin Laboratories Inc",
          "Crown Prince Inc",
          "Advancepierre Foods",
          "Halo Innovations",
          "Sundial Brands LLC",
          "Diageo North America",
          "CTM Enterprises",
          "John B. Sanfilippo & Son",
          "Ehrmann USA",
          "Blue Rhino",
          "Bonsavor Foods Llc",
          "Amazing Grass",
          "Raybern Foods LLC",
          "Vidalia Onion Committee",
          "Sequel Naturals",
          "Russell Stover Candies",
          "Mooresville Ice Cream Company",
          "Namaste Laboratories",
          "My Dirty Jobs",
          "Cargill Salt",
          "Mari'S Foods Inc.",
          "BA Sports Nutrition LLC",
          "True Science",
          "Wholesome Goodness",
          "Deutsch Family Wine & Spirits",
          "Chicago Tribune",
          "Bandai America Inc",
          "Neuro",
          "Thrive Nutrition",
          "B & G Foods ",
          "Q Tonic LLC ",
          "Gilster-Mary Lee Corporation",
          "Simply7",
          "Harmless Harvest",
          "MONDELEZ GLOBAL LLC",
          "Kraft Heinz Company.",
          "Bio-nutritional Research Group",
          "Niconovum USA Inc.",
          "Peak Foods",
          "Arch Chemicals",
          "Rupari Foods",
          "Arctic Ease",
          "Kinders Meats",
          "Westrock Coffee Roasting",
          "Gerry Organics",
          "IMG Holdings Inc.",
          "Nature's Touch Frozen Foods",
          "SAVAGE RIVER",
          "CALBEE NORTH AMERICA",
          "Health Warrior",
          "Ocean Spray Cranberries Inc",
          "Universal Studios Home Video",
          "Dole Fresh Vegetables Inc.",
          "Delta Carbona",
          "Voss Water",
          "Olivio Premium Products Inc",
          "Gold Eagle Co.",
          "Joseph's Lite Cookies",
          "Marty Hess",
          "Envirocon Technologies",
          "Orgain Inc.",
          "Nestle Waters North America Inc.",
          "Georgia-Pacific",
          "HP Hood LLC",
          "Chung'S Products Lp",
          "Flagship Food Group LLC",
          "Kicking Horse Coffee Co. Ltd",
          "Revolution Foods",
          "LYFE Kitchen Retail",
          "KENSINGTON AND SONS LLC",
          "Gizmo Beverages",
          "Enlightened",
          "Miller Coors Llc",
          "Supervalu",
          "Vermont Smoke & Cure",
          "EXPRO3",
          "Sazerac",
          "True Drinks",
          "Cf Burger Cremery Co",
          "Tecumseh Poultry",
          "Bai Brands LLC",
          "Hostess Brands",
          "Fairlife",
          "Blount Fine Foods Corp",
          "Blue Bell Creameries",
          "Suja",
          "Smarty Pants",
          "Foodscience Corporation",
          "Agave Loco LLC",
          "McCormick Distilling Co.",
          "Just Born",
          "Madrinas Brands",
          "Reser'S Fine Foods Inc",
          "MD Science Lab",
          "Big Red Inc.",
          "Boardwalk Frozen Treats",
          "Us Smokeless Tobacco Company",
          "Hanesbrands Inc",
          "Phillips Foods",
          "DRIP DROP",
          "Hampton Creek",
          "Brownie Brittle",
          "Gorton??_S",
          "Ghirardelli",
          "Halfpops",
          "Eat Good Do Good",
          "Plum",
          "UTZ Quality Foods",
          "Trans Ocean Products",
          "Pharmavite",
          "Lightlife Foods Inc.",
          "TAJIN INTERNATIONAL CORP",
          "Physicians Formula Inc",
          "Henkel Consumer Goods",
          "Path of Life",
          "Cardinal Health",
          "Mead Johnson & Company",
          "Rj Reynolds Tobacco Company",
          "Energizer",
          "GlaxoSmithKline Consumer Healthcare",
          "The Clorox Company",
          "Cole'S Quality Foods",
          "Lee Kum Kee",
          "Kuli Kuli Inc.",
          "Kobayashi Healthcare Llc",
          "DCI Cheese",
          "Red Diamond",
          "American Licorice Company",
          "Win Schuler Foods",
          "Crispy Green Inc.",
          "Eden Creamery LLC",
          "SCHWAN'S CONSUMER BRANDS NORTH AMERICA",
          "Protein2o Inc",
          "Infirst Healthcare Inc.",
          "Buena Vista Home Entertainment",
          "Wells Enterprises",
          "Bob's Red Mill",
          "Twin Cups",
          "Tres Latin Foods",
          "Bruce Foods",
          "Perrigo Company",
          "Avocados From Mexico",
          "Prestige Wine Imports Corp.",
          "Kemps LLC",
          "O'Connor Companies",
          "Ralcorp Frozen Bakery",
          "John Vince Foods",
          "Harvest Hill Beverage Company",
          "Simple Science",
          "Sun-Maid",
          "Taverrite’s Italian Food",
          "American Beverage Marketers",
          "Western Spirits Beverage Company",
          "Hunee Vintners",
          "Greenkarma",
          "Universal Razor Industries LLC",
          "Chicken of the Sea",
          "Gray & Company",
          "TOMY International",
          "CR Brands",
          "Simple Mills",
          "Perfection Bakeries",
          "Tec Labs",
          "Elmer'S Products Inc",
          "Mizkan America",
          "HEX Performance LLC",
          "Merz Pharmaceuticals",
          "Brewla Inc",
          "KSF Acquisition Corporation",
          "Dyla",
          "Lactalis American Group Inc.",
          "Paramount Farms",
          "Prairie Farms Dairy",
          "Pine Brothers",
          "Crunch Pak",
          "Monterey Gourmet Foods",
          "Brios Food Company",
          "Peet's Coffee & Tea",
          "Purdue Products L.P.",
          "Foundation Consumer Health",
          "Niagara Bottling LLC",
          "Tender Corp",
          "Renfro Corporation",
          "S. C. Johnson",
          "Smithfield Foods",
          "Dakota Growers Pasta Company (Dreamfields Pasta)",
          "Farmers Union Industries LLC",
          "BPI Sports",
          "Heineken USA",
          "Brassica Products",
          "Paradise Beverages Inc.",
          "Just Goods",
          "SCA Personal Care",
          "Wewalka Trading Corp",
          "Jarden Home Brands ",
          "Chosen Foods ",
          "Edward Marc Brands",
          "Harris Ranch Beef Co",
          "Medora Snacks",
          "America's Naturals LLC",
          "A to Z Wineworks",
          "Mayborn Group",
          "Daisy Brand",
          "Atkins Nutritionals",
          "Prestige Brands Holdings",
          "Sweet Harvest Foods Company",
          "Biotech Corporation",
          "The New Primal",
          "Lil' Drug Store Products Inc.",
          "Bestsweet Inc/Baskin Robbins",
          "BelGioioso Cheese",
          "Clavel Corporation",
          "Mountain Valley Spring Company LLC",
          "Matt's Cookie Company",
          "La Terra Fina USA",
          "GlenOaks Farms",
          "The Colomer Group",
          "Del Monte Foods Company",
          "Grainful",
          "Experience Cisse",
          "King'S Hawaiian",
          "Ferrero USA",
          "Bayer HealthCare LLC",
          "Duracell Distributing Inc.",
          "Whittier Enterprise LLC",
          "Maze Innovations Inc.",
          "WaterWipes USA Inc.",
          "Gloves In A Bottle",
          "Leapfrog Enterprise Inc",
          "Windsor Foods",
          "Land O Lakes",
          "ECU Imports",
          "OFFBeat Brands",
          "Paris Presents",
          "Philos Foods",
          "Ripple Foods ",
          "Alcon Laboratories",
          "Pilot Corporation of America",
          "BBU",
          "AmeriGas Propane",
          "Car-Freshner",
          "Blue Moose of Boulder",
          "ProPhase Labs",
          "Rich Products Corporation - NY",
          "Wm. Wrigley Jr. Company",
          "Rug Doctor",
          "Old Home Foods",
          "Miss. Jones Baking Co.",
          "Vegy Vida",
          "Healthy Pet",
          "ALOUETTE CHEESE USA",
          "Reckitt Benckiser LLC",
          "Hasbro",
          "Cool Frootz",
          "The Village Company",
          "Bob Evans Farms",
          "Schwabe North America ",
          "Scoops",
          "Quorn Foods ",
          "Kerry Food & Beverage",
          "Maryland and Virginia Milk Producers Cooperative Association",
          "Foodstirs",
          "Candle-lite Company LLC",
          "Similasan",
          'Advancepierre Foods- "- "',
          "Bright Farms Inc",
          "Nature's Dairy LLC",
          "Onecare Company",
          "Australis",
          "Muuna Inc.",
          "San J International",
          "Natural Solutions for Life",
          "Home Run Inn Pizza",
          "Hope Family Wines ",
          "Link Snacks",
          "Schiff Nutrition Group",
          "Roasting Solutions LLC",
          "Ricos Products Co.",
          "Blue Spark Technologies",
          "Chicago Bar Company LLC",
          "Icelandic Provisions",
          "More Than Gourmet",
          "Nature Delivered Inc",
          "Tim Horton's ",
          "Dr. Oetker",
          "Craft Brewers Alliance",
          "Green Teaspoon",
          "EPIC Provisions LLC",
          "Crystal Geyser Water Company",
          "Duraflame",
          "Imperial Brands",
          "Anchor Brewing Company",
          "Turtle Mountain Llc",
          "Dynamiclear Partners LLC",
          "Equal Exchange",
          "Mentholatum Co Inc",
          "Chattem",
          "Sugar Foods Corp. - CA",
          "Hartz Mountain Corp The",
          "Coolhaus",
          "Matrixx Initiatives",
          "Abbott Medical Optics",
          "organicgirl",
          "Anderson's Maple Syrup",
          "Aurantiaca USA LLC",
          "Contessa Premium Foods",
          "Stillhouse Spirits LLC",
          "Cacique Usa",
          "Flow Water Inc.",
          "C&M Food Distributing",
          "Fresh Express Inc",
          "C&W Wholesale",
          "Beluga Vodka",
          "Hawaiian Host",
          "The Oppenheimer Group",
          "Land O'Frost",
          "POM Wonderful",
          "Mountain King Potatoes",
          "ORNUA FOODS NORTH AMERICA INC.",
          "Bantam Bagels",
          "Lucky Spoon Bakery",
          "Lamb Weston Holdings",
          "Cynova Laboratories",
          "Oneida",
          "Specialty Food Group",
          "Evenflo Products Company",
          "Mezcal El Silencio",
          "Hatfield Quality Meats",
          "Kayem Foods",
          "Frito-Lay",
          "La Tortilla Factory",
          "Freeman Beauty Labs",
          "Bissell Inc",
          "Millar Trading Group America LLC",
          "CONSTELLATION WINES U.S.",
          "Ach Foods",
          "Aidells Sausage Company",
          "Detroit City Distillery",
          "Hill'S Pet",
          "Star Brands North Americ",
          "Troy Healthcare LLC",
          "SmartGuard Rx Inc.",
          "Ready Pac Foods Inc",
          "River House Food Products",
          "JEM Beverage CO",
          "Momentive Performance Inc.",
          "Poppilu",
          "Halloren Schokoladenfabrik AG",
          "Lacertus Branding",
          "Sutter Home Windery (Trinchero Family Estates)",
          "Gabriella's Kitchen Inc",
          "Our/Detroit Vodka LLC",
          "Pear Bureau Northwest",
          "Newell Rubbermaid Inc",
          "JBS USA",
          "Valvoline",
          "Moberg Pharma North America LLC",
          "Whirlpool Corporation",
          "Apio Inc",
          "Method",
          "C Mondavi & Sons",
          "Ccf Brands",
          "Promotion in Motion",
          "Merisant Company",
          "American Nutrition Inc.",
          "Dr. Harold Katz",
          "Dr. Harold Katz",
          "Perio",
          "Dulcinea Farms Llc",
          "Eggland's Best",
          "WWF Operating Company",
          "Shearer's Foods",
          "Blue Buffalo Company",
          "Bellisio Foods",
          "Hearst Communications",
          "Kent Nutrition Group",
          "Oberto Sausage Company",
          "CONAGRA FOODS RDM",
          "Johnsonville Sausage",
          "Wf Young Inc",
          "Goya Foods",
          "Mariani Packing Company",
          "Bar Keepers Friend",
          "Challenge Dairy",
          "Riviana Foods",
          "Michael Angelo's Gourmet Foods",
          "Jarden Consumer Solutions",
          "Perdue Farms Inc.??_",
          "Lindt & Sprungli USA",
          "Tofutti Brands Inc",
          "High Liner Foods (USA)",
          "Athens Foods",
          "Cumberland Packing Corp.",
          "Crayola LLC",
          "Odom'S Tn Pride Sausage",
          "Sundance Beverage Co.",
          "Mission Food Corporation",
          "Bridgford Foods Corporation",
          "The Wine Group",
          "Eulactol Usa",
          "Pabst Brewing Company",
          "White Castle System",
          "Meadwestvaco Corporation",
          "Mission Pharmacal Company",
          "Anheuser Busch",
          "On-Cor Frozen Foods",
          "Bigelow Tea",
          "American Snuff Company",
          "Joy Cone Company",
          "Norseland Foods Inc",
          "J&J Snack Foods",
          "Bay Valley Foods",
          "Florida'S Natural Growers",
          "Butler Home Products",
          "Klein Foods",
          "Camellia Beans",
          "Beetnik®",
          "Norbest Ground Turkey",
          "Path of Lif e",
          "Teddie Peanut Butter",
          "La Colombe",
          "Shady Brook Farms Turkey",
          "Bio-Oil®",
          "Wild Mike's Ultimate Pizza",
          "La Colombe 2",
          "Florida's Natural® Orange Juice",
          "Sharpie, Expo or Papermate",
          "The Ginger People",
          "Shady Brook Farms Turkey2",
          "GoGo SqueeZ YogurtZ",
          "Coromega MAX",
          "Enviroscent™",
          "Snack Factory® Pretzel Crisps",
          "Enviroscent",
          "Tres Latin Pupusas_",
          "Monster Energy®",
          "Numi® Organic Tea",
          "Berks® Organic Presliced Deli Meats",
          "LIFEAID Beverage",
          "RUNA Organic Clean Energy drinks",
          "Red Star® Yeast",
          "ZinPhomaniac - $2 REBATE via PayPal",
          "Ricante®",
          "Ryvita®",
          "I and Love and You",
          "Barnana®",
          "I and Love and You_06129",
          "Buzz + Bloom® Honey",
          "Vital Farms",
          "Barnana",
          "7th Street Confections™",
          "Vital Farms_",
          "Señor Rico®",
          "High Brew Coffee®",
          "High Brew Coffe",
          "Kodiak Cakes®",
          "Custom Food/Kennel Seasons",
          "Frontier Natural",
          "R. TORRE & COMPANY, INC",
          "InnovAsian Cuisine® Enterprises, Inc.",
          "Popchips",
          "Bacardi",
          "Veev Spirits",
          "Contessa Premium Foods Inc.",
          "Jarden Consumer Solutions",
          "Sonoma Creamery LLC",
          "R. TORRE & COMPANY, INC.",
          "Mark Anthony Brand",
          "Folio Fine Wine Partners",
          "Prosperity Organic Foods",
          "Castle Rock Winery",
          "Victoria Fine Foods, LLC",
          "Scoops",
          "TAJIN INTERNATIONAL CORP",
          "New Belgium Brewing Company, Inc",
          "GOJO Industries, Inc",
          "World Kitchen, LLC",
          "CALBEE NORTH AMERICA, LLC",
          "Promotion In Motion.",
          "Pulmuone Foods USA Inc",
          "ILEX Consumer Products.",
          "Apollo Food Group",
          "Lee Kum Kee",
          "Promax Nutrition Corporation",
          "Crimson Wine Group",
          "Sunstar Americas, Inc",
          "Musco Olives",
          "Gizmo Beverages",
          "Instant Combo Savings",
          "Big Red Inc.",
          "Philips Electronics N AM",
          "Kayem Foods, Inc",
          "Puroast",
          "Delta Carbona L.P",
          "Gizmo Beverages.",
          "Cline Cellars",
          "FIJI Water Company, LLC.",
          "Dr. Fresh, LLC",
          "Circle Foods",
          "Shadow Beverages and Snacks",
          "PANOS Brands LLC",
          "Mission Pharmacal Company",
          "The Wine Group",
          "Laura’s Scudder’s",
          "Day Lee Foods",
          "Avocados from Mexico, Inc.",
          "Kahiki Foods, Inc.",
          "Remy Cointreau",
          "Dr. Praeger's Sensible Foods",
          "Revolution Foods, Inc.",
          "Ren Acquisition",
          "INMAR, INC.",
          "Pernod Ricard USA/Absolute Vodka.",
          "GIOVANNI PASTA/RANA MEAL SLTNS",
          "Wholesome Sweeteners",
          "Borden Dairy",
          "Langer Juice Company, Inc.",
          "National Pasteurized Eggs",
          "Nehemiah",
          "Grand Brands",
          "Rosina Food Products",
          "Botanical Laboratories",
          "Kevita Inc.",
          "On-Cor Frozen Foods, LLC",
          "Chobani, Inc",
          "Southern Wine and Spirits",
          "Purple Wine Co.",
          "Ateeco Inc.",
          "Car Freshner Corporation",
          "The Pictsweet Company",
          "Jackson Family Wines, Inc",
          "Phil’s Fresh Foods",
          "Dr. Oetker USA LLC.",
          "Rustic Crust",
          "Schar USA",
          "Heartland",
          "Palermo Villa, Inc.",
          "Freixenet",
          "R.C. BIGELOW TEA",
          "Zonin USA, Inc",
          "Phillips Farms LLC",
          "Mionetto USA",
          "Friendship Dairies",
          "Quinoa Corp.",
          "PREFERRED BRANDS INT'L",
          "Babich",
          "Daiya Foods",
          "Australian Gold",
          "Manzen LLC",
          "HAMPTON CREEK, INC.",
          "RAYBERN FOODS, INC.",
          "Classic Cooking",
          "Talenti",
          "Talking Rain",
          "TAMPICO BEVERAGES INC.",
          "Hannahmax Baking, Inc.",
          "Premier Foods Inc.",
          "Neuro Brands, Inc.",
          "Distillery No.209",
          "Lil Drug Store Products",
          "Ferrara Candy Company",
          "Bellisio Foods, Inc.",
          "Alcon Laboratories, Inc.",
          "Distant Lands Coffee",
          "Home Market Foods, Inc.",
          "Morgan Rich Corporation",
          "CB Fleet",
          "Fishbowl Spirits, LLC",
          "DE SPIRITS LLC",
          "Savage River, Inc.",
          "Cucina Antica Foods Corp",
          "Motive Marketing",
          "OYSTER BAY WINES USA INC",
          "Hofmann Sausage Co. Inc.",
          "DELIZZA, INC",
          "Noble Foods.",
          "CHATTEM, INC.",
          "SPECTRUM OILS",
          "Bio-nutritional Research Group, Inc.",
          "Dean Foods Co",
          "Allergan",
          "Ehrmann LLC",
          "LAVAZZA PREMIUM",
          "Michele's",
          "Mendocino Wine Co",
          "JAMMIN JAVA CORP",
          "Clougherty Packing",
          "Frontera Foods",
          "G. L. Mezzetta, Inc.",
          "Yes To, Inc.",
          "Bel Brands",
          "Remy Cointreau USA, Inc.",
          "Plum Organics",
          "G’s Fresh Beets Incorporated",
          "VOGUE INTERNATIONAL",
          "Klein Foods, Inc",
          "Don Sebastiani & Sons..",
          "Infirst Healthcare",
          "Safeway",
          "Bai Brands LLC",
          "STUBB'S LEGENDARY",
          "Vita Coco",
          "Core Power®",
          "Rhythm Superfoods",
          "That's How We Roll LLC",
          "Earthbound Farm",
          "O Organics™",
          "Dr. Miracles",
          "Strength of Nature",
          "Live Better Brands LLC",
          "Media Consumer Healthcare",
          "Foundation Consumer Health, LLC",
          "Friendly's Ice Cream, LLC",
          "Treana Winery, LLC",
          "Tomatin",
          "Bertolli®",
          "Mrs. Grissom's Salads, Inc.",
          "Shiner Smokehouse",
          "True Drinks",
          "THe Jel Sert Company",
          "Accolade Wines North America",
          "NIVEA® Body Wash",
          "Moberg Pharma North America LLC",
          "Faultless Starch/Bon Ami Company",
          "Greenkarma, LLC (Baby Mantra)",
          "Arcobasso Foods, Inc.",
          "illy®",
          "Universal Group",
          "Hostess Twinkies",
          "Arm & Hammer Pet Fresh Air Filter",
          "Skeeter Snacks",
          "Hanesbrands Inc.",
          "Ready Pac",
          "CherryMan®",
          "Wolfschmidt Wine, Beer, & Spirits",
          "Community®",
          "First Quality Retail Services",
          "Lipo-Flavonoid® Plus",
          "Canyon Bakehouse",
          "Pentel®",
          "Lip Smacker®",
          "Red's®",
          "Avocados from Mexico",
          "The Hunger Games: Mockingjay - Part 1",
          "Community® Coffee",
          "Steak-Umm",
          "Weight Watchers",
          "Ruffies®",
          "Bear Creek",
          "Hershey's Milk",
          "Back to Nature",
          "NUK®",
          "True Myth",
          "Salada Tea®",
          "GRATIFY®",
          "Zebra Pen",
          "BHUJA® Snacks",
          "VetIQ® Health & Wellness Products",
          "Milo's Kitchen",
          "Stahlbush Island Farms",
          "Budget Saver",
          "Louis Jadot",
          "Sandwich Bros™",
          "PopCorners",
          "a2 Milk®",
          "Zonin Prosecco 750ml",
          "Vega",
          "Vita Bone®",
          "Red Vines® candy",
          "Dillon's Small Batch Distillers",
          "Diurex®",
          "SunButter",
          "Southern Comfort",
          "EVERPRO® GRAY AWAY",
          "Pete and Gerry's",
          "Deep Eddy Vodka",
          "TAJIN",
          "Old Home Foods®",
          "okocat™",
          "Suja",
          "Jack Link's®",
          "Urbane Grain",
          "Madhava Natural Sweeteners",
          "Tony Roma's",
          "Taverrites",
          "Crunch Pak and good2grow",
          "OPTI-FREE®",
          "CLEAR CARE®",
          "Taverrite's",
          "Orgain Nutrition Shakes",
          "Brewla Bars",
          "Brownie Brittle",
          "Boulder Sausage",
          "BullFrog® Sunscreen",
          "Karma Tequila",
          "American Greetings",
          "La Bella",
          "Cabo Chips",
          "Meiji®",
          "Quorn™",
          "Simply 7 Snacks",
          "Natrol",
          "Cow Candy",
          "Barbara's Bakery",
          "thinkThin®",
          "Pendleton® 1910 Rye Whisky",
          "Made In Nature™",
          "JUST water",
          "Synodrin®",
          "Nubian Heritage",
          "Frigo® Cheese Heads®",
          "Good Karma® Dairy Free Flaxmilk",
          "Laughing Glass Cocktails All Natural Margarita",
          "Q Drinks",
          "Lúvo™",
          "FIORA® Tissue & Towels",
          "Freshpet®",
          "Finlandia® Product",
          "noosa® yoghurt",
          "Blue Stop Max",
          "RumChata®",
          "Rodney Strong Vineyards",
          "CELSIUS",
          "Brio®",
          "LION COFFEE",
          "Crispy Green®",
          "Paper Mate®",
          "18 Rabbits Organics",
          "Olly",
          "Mary's Gone Crackers",
          "HORMEL Brand",
          "MARS Valentine's Day",
          "Kellogg's® Eggo®",
          "MorningStar Farms®",
          "Friskies®",
          "Dove",
          "Purina ONE®",
          "Dov",
          "Morning Star Farms®",
          "Purina ONE",
          "Schick® Disposable Razor Pack",
          "Florastor",
          "Cadbury Premium Pouches",
          "Breathe Right®",
          "Dog Chow®",
          "DulcoEase®",
          "Friskies",
          "HERSHEY'S Pudding",
          "Special K®",
          "Mucinex®",
          "Frigo® Cheese Heads",
          "Ziploc®",
          "Scrubbing Bubbles®",
          "Cascadian Farm",
          "Food Should Taste Good",
          "Lipton Tea",
          "I Can't Believe It's Not Butter!",
          "Amope",
          "Amop",
          "BUSH'S Cocina Latina®",
          "Barber Foods",
          "Amope®",
          "CELSIU",
          "LISTERINE®",
          "PURELL®",
          "KRAFT Snack Trios",
          "Hillshire® Snacking",
          "One A Day®",
          "JAMESON® Caskmates Irish Whiskey",
          "Duo Fusion®",
          "REDBREAST® 12 Year Old Irish Whiskey",
          "Flintstones™",
          "HORMEL® Brand",
          "Plum Organics Infant Formula",
          "DIGIORNO®",
          "Girl Scouts Cereal",
          "SUDAFED®",
          "STOUFFER'S®",
          "ROGAINE®",
          "V&V Supremo®",
          "Frank's RedHot®",
          "JOHNSON’S® & DESITIN®",
          "LISTERIN",
          "TYLENOL®",
          "NEUTROGENA®",
          "LISTERI",
          "TYLEN",
          "LISTERINE® SMART RINSE®",
          "PEPCID®, LACTAID®, or IMODIUM®",
          "PERDUE® HEAT & EAT",
          "Best Foods®/ Hellmann's®",
          "Air Wick",
          "Schick Hydro Silk Razor or Refill",
          "DRINK CHOBANI",
          "ARM & HAMMER",
          "Special",
          "ARM & HAMMER™",
          "Vagisil",
          "Always Discreet",
          "Always Discreet Incontinence",
          "Bounce",
          "GEORGE DICKEL",
          "Johnsonville",
          "Bounce Dryer",
          "Cascade",
          "Charmin",
          "Metamucil",
          "Metamucil Product",
          "Dawn",
          "Pampers",
          "Pampers Wipes",
          "Tide",
          "Luvs",
          "The Laughing Cow®",
          "Gain",
          "Tid",
          "Persil®",
          "Frank's RedH",
          "Pillsbury Refrigerated",
          "Almay",
          "Country Crock®",
          "Dillon's Small Batch Distiller",
          "Magnum Ice Cream",
          "Caress",
          "Olay",
          "Olay TE",
          "Pepto Bismol",
          "Head & Shoulders",
          "ARM & HAMME",
          "Aussie",
          "Daw",
          "Febreze",
          "Campbell's®",
          "Best Foods® / Hellmann's®",
          "Bob Evans® Side Dish",
          "Crest",
          "Febrez",
          "Campho-Phenique",
          "Persi",
          "Herbal Essences",
          "alli",
          "Fixo",
          "Quaker® Breakfast Flats",
          "Synodrin",
          "Quaker C",
          "Quaker Hot",
          "Seventh Generatio",
          "Gai",
          "Knorr",
          "Snack Factory® Pretzel Crisps®",
          "Down",
          "Gerber",
          "MOTRIN® or BENGAY®",
          "RoC",
          "Gainn",
          "Progresso",
          "Helper",
          "Tidee",
          "Fixodent",
          "LÄRABAR",
          "Ben & Jerry's®",
          "KLEENEX",
          "Werther's Original Sugar Free",
          "Seventh Generati",
          "Axe®",
          "Fiber One",
          "TEDDY Soft Bakes",
          "Old El Paso",
          "Nature Valley",
          "Oral-B",
          "ZzzQuil",
          "Old Spice",
          "Swiffer",
          "Puffs",
          "Secret",
          "Venus",
          "Swiffer Refill",
          "Vicks",
          "Tidy Cats LightWeight",
          "Swiffer Starter Kit",
          "Stevia In The Raw",
          "Playtex Sport Tampons",
          "Playtex Sport Pads or Liners",
          "Aquaphor",
          "Schick Quattro for Men",
          "Abreva",
          "Breyers® Gelato",
          "Pam",
          "Pamp",
          "Tideee",
          "KRAFT Dressing",
          "Bounty",
          "Pamerp",
          "Tampax",
          "Nature Valley Granola",
          "Quaker Chewy Bars",
          "KRAFT Mayo, MIRACLE WHIP",
          "Lipton",
          "General Mills Fruit Snacks",
          "Veggie Dip",
          "Tide Simply Laundry Detergent",
          "Sunsweet",
          "LIME-A-WAY",
          "POISE",
          "Mylanta® Gas",
          "SC Johnson",
          "L'Oreal Paris",
          "RID-X®",
          "COVERGIRL®",
          "Phillips'® Colon Health®",
          "COVERGIRL + Olay Product",
          "Monk Fruit In The Raw®",
          "DANNON ®",
          "Persil® ProClean® laundry detergent",
          "Mylanta®",
          "Stevia In The Raw®",
          "Similac",
          "Pure Bliss by Similac",
          "V8 Vegetable Juice",
          "Hillshire Farm Lit'l Smokies",
          "PERDUE PERFECT PORTIONS",
          "PERDUE Frozen Fully Cooked Chicken",
          "El Monterey",
          "MiraLAX",
          "Annie's",
          "Liquid-Plumr",
          "Differin",
          "El Monterey Tamales",
          "Hefty Slider Bags",
          "LARABAR",
          "Totino's",
          "Truvía® Stevia Sweetener",
          "U by Kotex",
          "Zest",
          "BOOST",
          "Totinos",
          "DEPEND",
          "POISE Liners",
          "Sunsweet PlumSweets",
          "HORMEL BRAND COMPLEAT",
          "POISE Pads",
          "Big G Cereals",
          "Amy's",
          "Godiva",
          "Miss Jones Baking Co",
          "Lemi Shine",
          "STOPAIN®",
          "Drew's",
          "Bufferin",
          "Hidden Valley Ranch",
          "Renew Life",
          "STILLHOUSE® Whiskey",
          "Anderson's Pure Maple Syrup",
          "Little Hug®",
          "Florajen",
          "Mountain Valley Spring Water",
          "Icelandic Provisions",
          "EcoTools",
          "Harmless Harvest",
          "Wholesome Goodness",
          "Kitchen Accomplice",
          "Bayer Advanced",
          "Jazz™ Apples",
          "V&V Supremo",
          "Beluga Noble Vodka",
          "Buddy Fruits®",
          "Taylor Farms",
          "California Goldminer",
          "Giovanni 2chic",
          "PetArmor Plus for Dogs or Cats",
          "Annie Chunss",
          "Bird Dog Bourbon Whisky",
          "ProYo Smoothie Tubes",
          "Mr. Dell's Hashbrowns",
          "Bantam Bagels",
          "Applegate®",
          "Three Bridges",
          "NuGo Slim - Low Glycemic Protein Bars",
          "Wyman's of Maine",
          "Profoot",
          "Blue Plate",
          "My/Mo™ Mochi Ice Cream",
          "Tejava® The Unsweetened Tea",
          "Kuli Kuli Pure Moringa",
          "Sabra",
          "Lucky Spoon Bakery",
          "Coolhaus Super Premium Ice Cream",
          "RXBAR",
          "Milford Valley",
          "Mama Rosie's",
          "Milo's",
          "Forto Energy Shot",
          "Anchor Hocking®",
          "VOSS Water",
          "CLORALEN® Bleach",
          "Mezcal El Silencio Espadín",
          "Pig's Nose Blended Scotch",
          "Baskin-Robbins®",
          "Obrigado Coconut Water",
          "Kicking Horse® Coffee",
          "Man Cave® Craft Eats",
          "The New Primal",
          "Palmer's®",
          "EPIC® Bar",
          "Divine Chocolate",
          "Madrinas Coffee®",
          "Pilot Pen® G2",
          "HALO TOP",
          "Lightlife®",
          "Chosen Foods",
          "Olé Extra Virgin Olive Oil",
          "Vermont Smoke & Cure®",
          "Health Warrior Chia Bars",
          "SmartyPants® Vitamins",
          "Tres Latin Pupusas",
          "Vegy Vida",
          "SkinSmart Antimicrobial™",
          "Harvest Snaps",
          "Amazing Grass®",
          "A to Z Wineworks",
          "Arby's®",
          "organicgirl®",
          "GODIVA Masterpieces",
          "Kidfresh®",
          "River House Dressing & Marinade",
          "HEX Performance Advanced Laundry Detergent",
          "Ripple®",
          "505 Southwestern",
          "Sir Kensington's",
          "NoDoz® Alertness Aid",
          "Francesco Rinaldi",
          "Duracell",
          "Grainful",
          "Mauna Loa® Macadamia Nuts",
          "Aunt Sue's Raw Honey",
          "Tootsie Christmas Jr. Mints or Child's Play",
          "Biofreeze Pain Reliever",
          "Vienna®",
          "Simple Mills",
          "WaterWipes®",
          "Gloves In A Bottle Shiedling Lotion",
          "Luna & Larry's Coconut Bliss",
          "Froozer",
          "Tim Hortons®",
          "Breyers® delights",
          "Supreme Source®",
          "Seattle's Best Coffee®",
          "Cooked Perfect® Meatballs",
          "Rachael Ray™ Nutrish®",
          "skinnypasta",
          "ATHENS®",
          "SlimFast",
          "John Soules Foods®",
          "Tillamook",
          "Sunny Delight",
          "Shamrock Foods",
          "Dole Packaged Foods LLC",
          "Mary's Gone Crackers",
          "Sonoma Creamery LLC",
          "Frontier Natural",
          "Tree Top",
          "Huhtamaki",
          "Grand Brands",
          "Home Market Foods",
          "Country Choice Organic",
          "Bumble Bee Foods Llc",
          "Lavazza Premium Coffee",
          "Fieldale Farms",
          "Kernel Seasons",
          "Del Monte Fresh Produce",
          "Lindy's Homemade LLC",
          "PepsiCo",
          "Morton Salt",
          "Marc Anthony Cosmetics Inc",
          "Mr. Dell Foods",
          "3M",
          "Popchips",
          "Faygo Beverages Inc",
          "Flatout",
          "Church & Dwight Co.",
          "Meredith Corporation",
          "Idahoan Foods",
          "Bic Usa Inc",
          "Ken's Foods",
          "Brown-Forman Corporation",
          "Foster Farms",
          "Juanitas Foods",
          "Revlon Consumer Products Corporation",
          "Hoyu Usa",
          "National Pasteurized Eggs",
          "Chicken Of The Sea Internation",
          "The Scotts Company",
          "Darigold Inc",
          "World Kitchen",
          "4C Foods Corp",
          "Ventura Foods Llc",
          "Discus Dental Inc",
          "DS Waters of America",
          "Johnson & Johnson Consumer Inc.",
          "Sue Bee Honey",
          "J.M. Smucker Company",
          "Blue Diamond Growers",
          "Jackson Family Wines",
          "Kruger Products L.P.",
          "Storck",
          "Saputo Cheese USA",
          "Anchor Bay Entertainment",
          "Blistex",
          "Fujifilm North America Corporation",
          "Pilgrim'S Pride",
          "Tata Global Beverages",
          "New Belgium Brewing Company",
          "Carl Buddig & Company",
          "Solo Cup Company",
          "Combe Inc",
          "Domino Foods Inc",
          "Summer Laboratories",
          "McKee Foods Corporation",
          "Bar-S Foods Co",
          "Beech-Nut Nitrition Co.",
          "Late July Organic Snacks",
          "Campari America",
          "Faribault Products",
          "Glass Agency",
          "Vienna Beef LTD",
          "Salov North America Corp",
          "Langer Juice Company",
          "Airborne Inc",
          "The Dannon Company",
          "Ganeden Biotech Inc",
          "House-Autry Mills",
          "Idelle Labs",
          "Aspire Brands",
          "The Healthy Beverage",
          "Bausch & Lomb",
          "McCain Foods",
          "Tampa Maid Foods",
          "Snyder's-Lance",
          "Daiya Foods Inc",
          "Coria Laboratories",
          "Boulder Sausage ",
          "Francis Ford Coppola",
          "Becton",
          "Borges USA",
          "Fresh Pet",
          "Freudenberg Household Products LP",
          "American Greetings Corporation",
          "Pamela'S Products",
          "Palermo Villa",
          "Pfizer Consumer Healthcare",
          "Schreiber Foods",
          "Williams Foods",
          "Dean Foods Company",
          "Sunsweet Growers",
          "Community Coffee Company",
          "Farming Technology Inc.",
          "Willow Tree Poultry Farm",
          "Ateeco Inc.",
          "La Brea Bakery Holdings",
          "NESTLE PURINA PETCARE COMPANY",
          "Procter and Gamble",
          "General Mills",
          "Kevita Inc.",
          "The Hygenic Corp",
          "Tom's Of Maine",
          "Omron Healthcare Inc",
          "Blacksmith Brands",
          "Ainsworth Pet Nutrition",
          "Massimo Zanetti Beverage Usa",
          "Delizza Inc",
          "Gold Medal Bakery",
          "Clarion Brands",
          "Hanover Foods Corporation",
          "Rosina Food Products",
          "Stremicks Heritage Foods",
          "Boiron Inc",
          "Concepts In Health",
          "Bush Brothers & Company",
          "Aveniu Brands",
          "Markwins Beauty Products Inc.",
          "Cabot'S Creamery Co",
          "Paramount Home",
          "Botanical Laboratories",
          "Phillips Distilling Company",
          "Welmedix LLC",
          "Spectrum Brands",
          "Twentieth Century Fox",
          "LiDestri Foods",
          "Alacer Corporation",
          "Dawn Food Products",
          "Phoenix Brands LLC",
          "Edgewell Personal Care",
          'Unilever- ""',
          "Starbucks Coffee Company",
          "Seneca Foods Corporation",
          "Green Mountain Coffee",
          "Vitasoy Usa Inc",
          "Butler Home Products",
          "Columbus Manufacturing",
          "Philips Consumer Lifestyle",
          "Sabra Dipping Company",
          "Welch Foods Inc.",
          "Dr. Praeger'S",
          "Rodney Strong Wine Estates",
          "Fredrick Wildman & Sons",
          "Enjoy Life Natural Brands",
          "Wellpet Llc",
          "Piping Rock Health Products",
          "Kiss My Face Corp",
          "Garden Of Life",
          "Barilla America",
          "Castor & Pollux Pet Works",
          "Dietz & Watson Inc",
          "Traditional Medicinals",
          "SunButter",
          "Back to Nature Foods Company",
          "Applegate",
          "TRP Company",
          "CJ America",
          "E&J Gallo Winery",
          "Celsius",
          "Mann Packing Co",
          "The Original Soupman",
          "Boulder Brands",
          "William Grant & Sons",
          "Golden County Foods",
          "Can-Am Care",
          "Ruiz Foods",
          "InnovAsian Cuisine??_ Enterprises",
          "Northwest Natural Products Inc",
          "Th Foods Inc",
          "Arla Foods USA",
          "Emmi Roth USA",
          "Nutrisystem Everyday",
          "Taylor Fresh Foods",
          "Fischer & Wieser Specialty Fds",
          "Barbara'S Bakery",
          "Sidney Frank Importing Company",
          "Kiss Products",
          "Atticus Bakery LLC",
          "The Frs Company",
          "Basic American",
          "Pernod Ricardo ",
          "Green Dot Corporation",
          "Litehouse Inc",
          "Old World Industries",
          "Winebow",
          "Post Consumer Brands LLC ",
          "V&V Supremo",
          "Happy Family Brands",
          "Olivia'S Organics",
          "Sambazon",
          "Merck Consumer Care (Schering-Plough Healthcare Products",
          "Peanut Butter & Co.",
          "Atlantic Premium Brands",
          "Cedarlane Natural Foods",
          "Beaumont Products",
          "Rana Meal Solutions LLC",
          "Castrol North America Auto",
          "Sun Products",
          "Attune Foods",
          "Old Orchard Brands Llc",
          "Otis Spunkmeyer Inc",
          "Caribou Coffee",
          "Electrolux",
          "Zevia",
          "American Foods Group",
          "Bona AB",
          "The Sherwin Williams Company",
          "TTi Floorcare North America",
          "PERFETTI VAN MELLE BENELUX B.V. (Mentos Gum)",
          'thinkThin LLC- ""',
          "Nice Pak Inc",
          "Ste. Michelle Wine Estates Ltd.",
          "Wenner Media LLC",
          "Volume Brands International",
          "Vogue International",
          "Pacific Natural Foods",
          "Central Garden & Pet Co",
          "Butterball",
          "Solskyn Personal Care LLC",
          "Bayer CropScience LP.",
          "Clover Stornetta",
          "Coromega Company",
          "Boston Beer Company",
          "Wholesome Sweeteners",
          "Ciao Bella Gelato LA",
          "Dave's Killer Bread Inc",
          "Seventh Generation",
          "Carma Laboratories",
          "Naturesweet Ltd",
          "Jasper Wyman & Son",
          "Folio Fine Wine Partners",
          "Red Bull",
          "California Milk Advisory Board ",
          "Divine Chocolate",
          "KAS Direct LLC",
          "Walkers Shortbread Inc",
          "Annie'S Homegrown Inc",
          "Health Care Products",
          "Bacardi Usa",
          "Amy's Kitchen",
          "Blue Magic Inc",
          "Good Karma Foods",
          "Dentovations",
          "Monster Energy Company",
          "Koch Foods",
          "Fage USA Dairy Industry Inc",
          "Ajinomoto Frozen Foods",
          "Southeastern Mills",
          "Giovanni Cosmetics",
          "Cline Cellars",
          "Fisher-Price",
          "Talking Rain Beverage Co.",
          "Mars",
          "Chobani",
          "World Finer Foods",
          "FGX International Inc.",
          "Mars Petcare US",
          "Cf Foods Llc",
          "G. L. Mezzetta",
          "Renew Life Inc",
          "Mars Chocolate North America",
          "Beaver Street Fisheries",
          "Proximo Spirits",
          "Kellogg Company (Kashi & Keebler)",
          "OSEM USA",
          "Sun-Rype Products Ltd.",
          "Weight Watchers International",
          "Velvet Ice Cream Co",
          "Louisiana Fish Fry Products",
          "Delicato Vineyards",
          "American Pop Corn",
          "Hummus Gourmet LLC",
          "Puroast",
          "Yes To Inc. Usa",
          "Kao Brands",
          "Mighty Leaf Tea",
          "McIlhenny",
          "McCormick & Company",
          "Made In Nature",
          "Diamond Foods",
          "Burleson'S Pure Honey",
          "Munchkin Inc",
          "Greencore Usa",
          "Remy Cointreau",
          "Castle Rock Winery",
          "i-Health",
          "Genesis Today",
          "Conair Corp.",
          "American Lifeline",
          "Upsher-Smith Laboratories",
          "Cedar's Mediterranean Foods Inc",
          "Jtm Provisions Company",
          "Starkist Company",
          "Russian Standard Vodka",
          "Hydralyte LLC",
          "WM Barr",
          "California Olive Ranch",
          "Mga Entertainment",
          "Natrol LLC",
          "Blue Dog Bakery",
          "Snikiddy",
          "Idaho Dairy Products Commission",
          "Activision Publishing",
          "Reily Foods Company",
          "Evernutrition",
          "Green Genius",
          "Abbott Diabetes Care Inc",
          "Sturm Foods",
          "Bell-Carter",
          "Farley'S Sathers Candy Company",
          "Bell Pharmaceuticals Inc",
          "Wellspring Pharmaceutical Corp.",
          "Blackhawk Network Holdings",
          "Biocodex",
          "Sprout Foods",
          "Clemmy'S Ice Cream",
          "Nature's Plus",
          "Southeastern Mills",
          "ShurTech Brands",
          "The Power Of Fruit",
          "Paramount Citrus Packing Company",
          "Treasury Wine",
          "Claire-Sprayway",
          "Hisamitsu America",
          "NUK-USA LLC",
          "Creta Farms Usa",
          "Nehemiah Manufacturing Company LLC",
          "Mam Usa Corporation",
          "DERMA E",
          "Marfood Usa",
          "Prosperity Organic Foods",
          "Woodway Beverage Partners",
          "Crown Imports",
          "United Pet Group",
          "The Icelandic Milk and Skyr Corporation",
          "Factor Nutrition",
          "Cytosport",
          "De Wafelbakkers LLC",
          "Holloway House Inc",
          "Fruit Of The Loom",
          "French Transit Ltd.",
          "Coty US LLC.",
          "Completely Fresh",
          "Crimson Wine Group",
          "Newman'S Own",
          "CCA Industries",
          "Heaven Hills Distilleries",
          "Vaska Inc.",
          "Nana'S Cookie Company",
          "Ricola Usa",
          "Pan-O-Gold Baking Co.",
          "R. TORRE & COMPANY",
          "Luna & Larry's Coconut Bliss",
          "18 Rabbits Inc",
          "KF Wholesale LLC",
          "Foster Farms Dairy",
          "Relaxzen Inc.",
          "Tasteful Selections",
          "Handi-Foil Corporation",
          "Konsyl Pharmaceuticals",
          "Sovena Usa",
          "Graeter'S Manufacturing Co.",
          "Michael Foods Inc.",
          "Drew's LLC",
          "Maple Leaf Foods Inc.",
          "Sunstar Americas",
          "Vilore Foods",
          "Kimberly-Clark",
          "Valley Fine Foods",
          "Talenti Gelato e Sorbetto",
          "Zarbee's Inc.",
          "Lifestyle Evolution",
          "Champion Foods",
          "Oregon Ice Cream Co",
          "Mega Brands Inc",
          "Canidae Corporation",
          "Oasis Brands",
          "Cooljuice Beverage Company",
          "Allergan Inc",
          "Stoli Group (USA)",
          "Kangaroo Brands",
          "Irving Consumer Products",
          "DenTek Oral Care",
          "Pompeian",
          "Good Foods Group LLC",
          "Chloe's Soft Serve Fruit",
          "Teva Pharmaceuticals USA",
          "Manischewitz Company",
          "GOJO Industries",
          "MATERNE North America",
          "Drink Hoist",
          "Dr. Shar U.S.A",
          "Rocasuba",
          "Isernio Sausage",
          "Finlandia Cheese Inc.",
          "Brioschi Pharmaceuticals Int'L Llc",
          "Walter P. Rawl & Sons",
          "California Natural Products",
          "Wholesome Harvest Baking LLC",
          "High Ridge Brands Co. ",
          "Australian Gold",
          "D.F. Stauffer Biscuit Company",
          "Nonni's Food Company",
          "Evofem Inc",
          "C.B. Fleet Company",
          "Icicle Seafoods",
          "Haribo Of America",
          "United Juice Companies Of America",
          "Purple Wine Co.",
          "Nakoma Products LLC",
          "Suncore Products",
          "Noosa Yoghurt",
          "Berry Plastics",
          "Sokol & Co",
          "Spin Master Ltd",
          "Time Distribution Services",
          "Arthur Dogswell LLC.",
          "Filled Bagel Industries",
          "Dr. Reddy's Laboratories Ltd.",
          "John Soules Foods",
          "Milo's Tea Company Inc.",
          "Waterpik Inc",
          "Productos Del Sol Corporation",
          "Borden Dairy Company",
          "The Inventure Group",
          "Weight Watchers International",
          "Lesaffre Yeast Corporation",
          "Valeant Pharmaceuticals North America Llc",
          "Vigo Importing Company",
          "Red's All Natural",
          "Deoleo USA",
          "Royal Ridge Fruits & Cold Storage",
          "Turkey Hill Dairy",
          "Iman Cosmetics",
          "Mamma Chia LLC",
          "King Arthur Flour Company",
          "Perfecta Products",
          "Guylian USA Inc",
          "DeLallo Italian Foods",
          "Stonyfield Farm",
          "Kaz Inc",
          "Red Gold Inc",
          "Insight Pharmaceuticals",
          "Musco Olives",
          "Nutramax Laboratories Veterinary Sciences Inc",
          "Apple & Eve LLP",
          "Don Sebastiani & Sons",
          "Hidden Villa Ranch",
          "Summit Brands",
          "Sun Orchard",
          "Rhodes International",
          "Melitta Usa (Melitia Group)",
          "F. Gavina & Son's (Don Francisco's coffee)",
          "Cropp Cooperative",
          "First Quality Enterprises",
          "General Electric on behalf of GE Home & Business Solutions",
          "CLIF BAR & COMPANY",
          "Dairy Farmers of America",
          "Wise Foods",
          "FIJI Water Company",
          "Gourmet Express",
          "Alen USA LLC",
          "Oil-Dri Corporation Of America",
          "Garden Protein International",
          "Profoot",
          "Wd-40",
          "Man Cave Craft Eats",
          "Avery Dennison",
          "Good Culture",
          "NibMor Inc",
          "National Pork Board",
          "Armored Autogroup",
          "Saputo Dairy Foods USA",
          "Foley Family Wines",
          "Farmland Foods",
          "Ouhlala Gourmet Corp",
          "Us Nutrition Inc",
          "North American Breweries",
          "Innovasource Llc",
          "Maty'S Healthy Products",
          "Solid Gold Health Products For Pets",
          "Canyon Bakehouse",
          "Mikawaya",
          "Sbd Holdings Group Corp",
          "Kevita Inc.",
          "Desert Pepper Trading Co.",
          "Wente Family Estates",
          "Mana Products",
          "Brookside Foods Ltd",
          "Promax Nutrition Corporation",
          "Hearthside Solutions Llc",
          "Jrb Foods",
          "Dr Pepper Snapple Group",
          "E.T. Browne Drug Company Inc",
          "Kodiak Cakes LLC ",
          "Hallmark Cards",
          "Palm Bay International",
          "Califia Farms LP ",
          "Stash Tea",
          "Preferred Brands Int Llc",
          "Victoria Fine Foods",
          "Intevation Food Group Llc",
          "Simple Green",
          "All Market",
          "Milton??_S Baking Company",
          "Twin Laboratories Inc",
          "Crown Prince Inc",
          "Advancepierre Foods",
          "Halo Innovations",
          "Sundial Brands LLC",
          "Diageo North America",
          "John B. Sanfilippo & Son",
          "Ehrmann USA",
          "Blue Rhino",
          "Bonsavor Foods Llc",
          "Amazing Grass",
          "Raybern Foods LLC",
          "Vidalia Onion Committee",
          "Sequel Naturals",
          "Russell Stover Candies",
          "Mooresville Ice Cream Company",
          "Namaste Laboratories",
          "My Dirty Jobs",
          "Cargill Salt",
          "Mari'S Foods Inc.",
          "BA Sports Nutrition LLC",
          "True Science",
          "Wholesome Goodness",
          "Deutsch Family Wine & Spirits",
          "Chicago Tribune",
          "Bandai America Inc",
          "Neuro",
          "Thrive Nutrition",
          "B & G Foods ",
          "Q Tonic LLC ",
          "Gilster-Mary Lee Corporation",
          "Simply7",
          "Harmless Harvest",
          "MONDELEZ GLOBAL LLC",
          "Kraft Heinz Company.",
          "Bio-nutritional Research Group",
          "Niconovum USA Inc.",
          "Peak Foods",
          "Arch Chemicals",
          "Rupari Foods",
          "Kinders Meats",
          "Westrock Coffee Roasting",
          "Gerry Organics",
          "IMG Holdings Inc.",
          "Nature's Touch Frozen Foods",
          "SAVAGE RIVER",
          "CALBEE NORTH AMERICA",
          "Health Warrior",
          "Ocean Spray Cranberries Inc",
          "Universal Studios Home Video",
          "Dole Fresh Vegetables Inc.",
          "Delta Carbona",
          "Voss Water",
          "Olivio Premium Products Inc",
          "Gold Eagle Co.",
          "Joseph's Lite Cookies",
          "Marty Hess",
          "Envirocon Technologies",
          "Orgain Inc.",
          "Nestle Waters North America Inc.",
          "Georgia-Pacific",
          "HP Hood LLC",
          "Chung'S Products Lp",
          "Flagship Food Group LLC",
          "Kicking Horse Coffee Co. Ltd",
          "Revolution Foods",
          "LYFE Kitchen Retail",
          "KENSINGTON AND SONS LLC",
          "Gizmo Beverages",
          "Enlightened",
          "Miller Coors Llc",
          "Supervalu",
          "Vermont Smoke & Cure",
          "EXPRO3",
          "Sazerac",
          "True Drinks",
          "Cf Burger Cremery Co",
          "Tecumseh Poultry",
          "Bai Brands LLC",
          "Hostess Brands",
          "Fairlife",
          "Blount Fine Foods Corp",
          "Blue Bell Creameries",
          "Suja",
          "Smarty Pants",
          "Foodscience Corporation",
          "Agave Loco LLC",
          "McCormick Distilling Co.",
          "Just Born",
          "Madrinas Brands",
          "Reser'S Fine Foods Inc",
          "MD Science Lab",
          "Big Red Inc.",
          "Boardwalk Frozen Treats",
          "Us Smokeless Tobacco Company",
          "Hanesbrands Inc",
          "Phillips Foods",
          "DRIP DROP",
          "Hampton Creek",
          "Brownie Brittle",
          "Gorton??_S",
          "Ghirardelli",
          "Halfpops",
          "Eat Good Do Good",
          "Plum",
          "UTZ Quality Foods",
          "Trans Ocean Products",
          "Pharmavite",
          "Lightlife Foods Inc.",
          "TAJIN INTERNATIONAL CORP",
          "Physicians Formula Inc",
          "Henkel Consumer Goods",
          "Path of Life",
          "Cardinal Health",
          "Mead Johnson & Company",
          "Rj Reynolds Tobacco Company",
          "Energizer",
          "GlaxoSmithKline Consumer Healthcare",
          "The Clorox Company",
          "Cole'S Quality Foods",
          "Lee Kum Kee",
          "Kuli Kuli Inc.",
          "Kobayashi Healthcare Llc",
          "DCI Cheese",
          "Red Diamond",
          "American Licorice Company",
          "Win Schuler Foods",
          "Crispy Green Inc.",
          "Eden Creamery LLC",
          "SCHWAN'S CONSUMER BRANDS NORTH AMERICA",
          "Protein2o Inc",
          "Infirst Healthcare Inc.",
          "Buena Vista Home Entertainment",
          "Wells Enterprises",
          "Bob's Red Mill",
          "Twin Cups",
          "Tres Latin Foods",
          "Bruce Foods",
          "Perrigo Company",
          "Avocados From Mexico",
          "Prestige Wine Imports Corp.",
          "Kemps LLC",
          "O'Connor Companies",
          "Ralcorp Frozen Bakery",
          "John Vince Foods",
          "Harvest Hill Beverage Company",
          "Simple Science",
          "Sun-Maid",
          "Taverrite’s Italian Food",
          "American Beverage Marketers",
          "Western Spirits Beverage Company",
          "Hunee Vintners",
          "Greenkarma",
          "Universal Razor Industries LLC",
          "Chicken of the Sea",
          "Gray & Company",
          "TOMY International",
          "CR Brands",
          "Perfection Bakeries",
          "Tec Labs",
          "Elmer'S Products Inc",
          "Mizkan America",
          "HEX Performance LLC",
          "Merz Pharmaceuticals",
          "Brewla Inc",
          "KSF Acquisition Corporation",
          "Dyla",
          "Lactalis American Group Inc.",
          "Paramount Farms",
          "Prairie Farms Dairy",
          "Pine Brothers",
          "Crunch Pak",
          "Monterey Gourmet Foods",
          "Brios Food Company",
          "Peet's Coffee & Tea",
          "Purdue Products L.P.",
          "Foundation Consumer Health",
          "Niagara Bottling LLC",
          "Tender Corp",
          "Renfro Corporation",
          "S. C. Johnson",
          "Smithfield Foods",
          "Dakota Growers Pasta Company (Dreamfields Pasta)",
          "Farmers Union Industries LLC",
          "BPI Sports",
          "Heineken USA",
          "Brassica Products",
          "Paradise Beverages Inc.",
          "Just Goods",
          "SCA Personal Care",
          "Wewalka Trading Corp",
          "Jarden Home Brands ",
          "Chosen Foods ",
          "Edward Marc Brands",
          "Harris Ranch Beef Co",
          "Medora Snacks",
          "America's Naturals LLC",
          "A to Z Wineworks",
          "Mayborn Group",
          "Daisy Brand",
          "Atkins Nutritionals",
          "Prestige Brands Holdings",
          "Sweet Harvest Foods Company",
          "Biotech Corporation",
          "The New Primal",
          "Lil' Drug Store Products Inc.",
          "Bestsweet Inc/Baskin Robbins",
          "BelGioioso Cheese",
          "Clavel Corporation",
          "Mountain Valley Spring Company LLC",
          "Matt's Cookie Company",
          "La Terra Fina USA",
          "GlenOaks Farms",
          "The Colomer Group",
          "Del Monte Foods Company",
          "Grainful",
          "Experience Cisse",
          "King'S Hawaiian",
          "Bayer HealthCare LLC",
          "Duracell Distributing Inc.",
          "Whittier Enterprise LLC",
          "Maze Innovations Inc.",
          "WaterWipes USA Inc.",
          "Gloves In A Bottle",
          "Leapfrog Enterprise Inc",
          "Windsor Foods",
          "Land O Lakes",
          "ECU Imports",
          "OFFBeat Brands",
          "Paris Presents",
          "Philos Foods",
          "Ripple Foods ",
          "Alcon Laboratories",
          "Pilot Corporation of America",
          "BBU",
          "AmeriGas Propane",
          "Car-Freshner",
          "Blue Moose of Boulder",
          "ProPhase Labs",
          "Rich Products Corporation - NY",
          "Wm. Wrigley Jr. Company",
          "Rug Doctor",
          "Old Home Foods",
          "Miss. Jones Baking Co.",
          "Vegy Vida",
          "Healthy Pet",
          "ALOUETTE CHEESE USA",
          "Reckitt Benckiser LLC",
          "Hasbro",
          "Cool Frootz",
          "The Village Company",
          "Bob Evans Farms",
          "Schwabe North America ",
          "Scoops",
          "Quorn Foods ",
          "Kerry Food & Beverage",
          "Maryland and Virginia Milk Producers Cooperative Association",
          "Foodstirs",
          "Candle-lite Company LLC",
          "Similasan",
          'Advancepierre Foods- "- "',
          "Bright Farms Inc",
          "Nature's Dairy LLC",
          "Onecare Company",
          "Australis",
          "Muuna Inc.",
          "San J International",
          "Natural Solutions for Life",
          "Home Run Inn Pizza",
          "Hope Family Wines ",
          "Link Snacks",
          "Schiff Nutrition Group",
          "Roasting Solutions LLC",
          "Ricos Products Co.",
          "Blue Spark Technologies",
          "Chicago Bar Company LLC",
          "Icelandic Provisions",
          "More Than Gourmet",
          "Nature Delivered Inc",
          "Tim Horton's ",
          "Dr. Oetker",
          "Craft Brewers Alliance",
          "Green Teaspoon",
          "EPIC Provisions LLC",
          "Crystal Geyser Water Company",
          "Duraflame",
          "Imperial Brands",
          "Anchor Brewing Company",
          "Turtle Mountain Llc",
          "Dynamiclear Partners LLC",
          "Equal Exchange",
          "Mentholatum Co Inc",
          "Chattem",
          "Sugar Foods Corp. - CA",
          "Hartz Mountain Corp The",
          "Coolhaus",
          "Matrixx Initiatives",
          "Abbott Medical Optics",
          "organicgirl",
          "Anderson's Maple Syrup",
          "Aurantiaca USA LLC",
          "Contessa Premium Foods",
          "Stillhouse Spirits LLC",
          "Cacique Usa",
          "Flow Water Inc.",
          "C&M Food Distributing",
          "Fresh Express Inc",
          "C&W Wholesale",
          "Beluga Vodka",
          "Hawaiian Host",
          "The Oppenheimer Group",
          "Land O'Frost",
          "POM Wonderful",
          "Mountain King Potatoes",
          "ORNUA FOODS NORTH AMERICA INC.",
          "Bantam Bagels",
          "Lucky Spoon Bakery",
          "Lamb Weston Holdings",
          "Cynova Laboratories",
          "Oneida",
          "Specialty Food Group",
          "Evenflo Products Company",
          "Mezcal El Silencio",
          "Hatfield Quality Meats",
          "Kayem Foods",
          "Frito-Lay",
          "La Tortilla Factory",
          "Freeman Beauty Labs",
          "Bissell Inc",
          "Millar Trading Group America LLC",
          "CONSTELLATION WINES U.S.",
          "Ach Foods",
          "Aidells Sausage Company",
          "Detroit City Distillery",
          "Hill'S Pet",
          "Star Brands North Americ",
          "Troy Healthcare LLC",
          "SmartGuard Rx Inc.",
          "Ready Pac Foods Inc",
          "River House Food Products",
          "JEM Beverage CO",
          "Momentive Performance Inc.",
          "Halloren Schokoladenfabrik AG",
          "Lacertus Branding",
          "Sutter Home Windery (Trinchero Family Estates)",
          "Gabriella's Kitchen Inc",
          "Our/Detroit Vodka LLC",
          "Pear Bureau Northwest",
          "Newell Rubbermaid Inc",
          "JBS USA",
          "Valvoline",
          "Whirlpool Corporation",
          "Apio Inc",
          "Method",
          "C Mondavi & Sons",
          "Ccf Brands",
          "Promotion in Motion",
          "Merisant Company",
          "American Nutrition Inc.",
          "Dr. Harold Katz",
          "Dr. Harold Katz",
          "Perio",
          "Dulcinea Farms Llc",
          "Eggland's Best",
          "WWF Operating Company",
          "Shearer's Foods",
          "Blue Buffalo Company",
          "Bellisio Foods",
          "Hearst Communications",
          "Kent Nutrition Group",
          "Oberto Sausage Company",
          "CONAGRA FOODS RDM",
          "Johnsonville Sausage",
          "Wf Young Inc",
          "Goya Foods",
          "Mariani Packing Company",
          "Bar Keepers Friend",
          "Challenge Dairy",
          "Riviana Foods",
          "Michael Angelo's Gourmet Foods",
          "Jarden Consumer Solutions",
          "Perdue Farms Inc.??_",
          "Lindt & Sprungli USA",
          "Tofutti Brands Inc",
          "High Liner Foods (USA)",
          "Athens Foods",
          "Cumberland Packing Corp.",
          "Crayola LLC",
          "Odom'S Tn Pride Sausage",
          "Sundance Beverage Co.",
          "Mission Food Corporation",
          "Bridgford Foods Corporation",
          "The Wine Group",
          "Eulactol Usa",
          "Pabst Brewing Company",
          "White Castle System",
          "Meadwestvaco Corporation",
          "Mission Pharmacal Company",
          "Anheuser Busch",
          "On-Cor Frozen Foods",
          "Bigelow Tea",
          "American Snuff Company",
          "Joy Cone Company",
          "Norseland Foods Inc",
          "J&J Snack Foods",
          "Bay Valley Foods",
          "Florida'S Natural Growers",
          "Butler Home Products",
          "Klein Foods",
          "Camellia Beans",
          "Beetnik®",
          "Norbest Ground Turkey",
          "Path of Lif e",
          "Teddie Peanut Butter",
          "La Colombe",
          "Shady Brook Farms Turkey",
          "Bio-Oil®",
          "Wild Mike's Ultimate Pizza",
          "La Colombe 2",
          "Florida's Natural® Orange Juice",
          "Sharpie, Expo or Papermate",
          "The Ginger People",
          "Shady Brook Farms Turkey2",
          "GoGo SqueeZ YogurtZ",
          "Coromega MAX",
          "Enviroscent™",
          "Snack Factory® Pretzel Crisps",
          "Enviroscent",
          "Tres Latin Pupusas_",
          "Monster Energy®",
          "Numi® Organic Tea",
          "Berks® Organic Presliced Deli Meats",
          "LIFEAID Beverage",
          "RUNA Organic Clean Energy drinks",
          "Red Star® Yeast",
          "ZinPhomaniac - $2 REBATE via PayPal",
          "Ricante®",
          "Ryvita®",
          "I and Love and You",
          "Barnana®",
          "I and Love and You_06129",
          "Buzz + Bloom® Honey",
          "Vital Farms",
          "Barnana",
          "7th Street Confections™",
          "Vital Farms_",
          "Señor Rico®",
          "High Brew Coffee®",
          "High Brew Coffe",
          "Kodiak Cakes®",
          "Cameron's Specialty Coffee",
          "ENLIGHTENEDD",
          "Martin's® Potato Rolls",
          "HOPE HUMMUS",
          "Fishpeople®",
          "WYLER'S LIGHT® - NAI",
          "BRÖÖ Craft Beer Hair - NAI",
          "Rebel Green® Cleaning Products",
          "7th Street Confections",
          "BRÖÖ Craft Beer Hair",
          "Tommy's Superfoods - NAI",
          "Cobram Estate®",
          "Niagara® - NAI",
          "Fairy Tales Hair Care 2",
          "Fairy Tales Hair Care 1",
          "Tommy's Superfoods",
          "Teton Waters Ranch",
          "750ml bottle of Elouan Pinot Noir - REBATE",
          "DeLoach California Heritage Pinot Noir - Rebate",
          "BODYARMOR",
          "Veggie Tots®, Veggie Fries®, Veggie Rings™",
          "Cacique®",
          "Maria & Ricardo's",
          "Go Raw",
          "Go Raw Plant",
          "hummustir",
          "hummustir organic",
          "hummustir organic hummus",
          "Umpqua Oats",
          "Dust Cutter® Lemonade",
          "The Gluten Free Bites",
          "Bing Beverage",
          "Teton Waters Ranch Grass",
          "Nature's Truth",
          "Hahn Pinot Noir",
          "Lotus Biscoff",
          "lesley stowe raincoast crisps",
          "GoodPop",
          "Mutti Tomatoes",
          "$1 REBATE via PayPal",
          "Yardley London",
          "$2 REBATE via PayPal",
          ": $1 REBATE via PayPal",
          "$2 REBATE",
          "YUMMY®",
          "ONE® Protein Bars",
          "Lotus Biscoff®",
          "Lantana® Hummus",
          "UpStar Ice Cream",
          "PROMMUS",
          "Mrs.Thinster's Cookie Thins",
          "neat",
          "23018",
          "Freedom Foods Barley+",
          "Nadamoo!",
          "Gary's QuickSteak",
          "Nadamoo",
          "Beso Del Sol Red Sangria 3L- $5 REBATE",
          "LoveTheWild",
          "Painbloc24®",
          "Cobram Estate® Extra Virgin Olive Oil",
          "HONCHOS Organic Tortilla Chips",
          "Reveal™",
          "Betty Crocker™",
          "Indiana Kitchen",
          "Frozen Greek Yogurt®",
          "Boxed Water is Better",
          "Boxed Water is Better2",
          "Pure Silk® Shave Cream",
          "Primal Kitchen",
          "Vice Cream",
          "Vice Cream™",
          "Vice Cream™ - 2",
          "Cucina di Carla™",
          "NuttZo®",
          "Bare Snacks",
          "Bare Snacks2",
          "Lenny & Larry's",
          "Kona Deep",
          "$2 REBATE via PayPa",
          "Kona Dee",
          "McConnell's Mint Chip",
          "SCOTTIES®",
          "Good Day Chocolate®",
          "Tiesta Tea",
          "4505 Chicharrones",
          "Castle Wood Reserve®",
          "Convergent Coffee®",
          "SuperEats Puffs",
          "FoodSaver",
          "C2O Pure Coconut Water",
          "ONE Brands®",
          "Kite Hill",
          "SkinnyPop",
          "Firestone - $2 REBATE via PayPal",
          "SkinnyPo",
          "Blue Dog Baker",
          "Bing Beverage - NAI",
          "SIMEKS",
          "Zaya Rum",
          "The Seeker Sauvignon Blanc",
          "Cusa Tea®",
          "Sweet Shakes Oatmeal Mix’ins",
          "BLUE Kitty Cravings®",
          "O'Brothers",
          "Kona",
          "Golden Nest",
          "DeNigris Vinegar's and Apple Cider",
          "FunkAway",
          "From The Ground Up",
          "Brazi Bites",
          "GLEN MORAY 12YO",
          "From The Ground Up_",
          "Pomi",
          "Van Der Hagen®",
          "FUNGI-NAIL®",
          "Silver Hills Sprouted Bakery",
          "Mrs. Budd’s®",
          "Paradise",
          "Farmwise",
          "KIND Kids",
          "Pennant",
          "KIND Kids™",
          "NOLET’S Silver Gin AND Q MIXERS",
          "Enjoy Life Foods",
          "$4 REBATE via PayPal",
          "$4 REBATE via PayPal 1",
          "POM",
          "Heritage",
          "FUNGI-NAIL",
          "GERITOL",
          "Van Der Hagen",
          "Saffron Road",
          "Ticklebelly",
          "Mikey's",
          "Humm Kombucha",
          "Ticklebelly®",
          "Don't Go Nuts",
          "Saffron Roa",
          "FINISH",
          "Rascal wines - Rebates",
          "Don't Go Nuts_",
          "The Honest Company",
          "Medlee™ Seasoned Butter",
          "Angie's® BOOMCHICKAPOP®",
          "Angie's® BOOMCHICKAPO",
          "Po",
          "Riceland",
          "BOOMERANG'S",
          "Grillo's Pickles",
          "DARE®",
          "LIFEAID",
          "Kumana Avocado Sauce",
          "Kumana Avocado Sauce_",
          "Blu-Dot Protein Tea",
          "Alvarado Street Bakery Sprouted Wheat Bread",
          "BOU™ Gravy Cubes",
          "smartwater - NAI",
          "Hemp Hearts",
          "Sobieski®",
          "Birch Benders®",
          "Birch Benders®_",
          "Hip Chick Farms",
          "Nature's Intent",
          "Jardines Salsa",
          "ZEBRA",
          "Babyganics",
          "NOKA®",
          "The Bu Kombucha",
          "NORTH COAST",
          "NORTH COAST_",
          "Emerald®",
          "Protes",
          "The Jackfruit Company",
          "Red Plate Foods®",
          "Frontier",
          "Califia Farms",
          "Numi Organic Tea",
          "Everyone",
          "Wandering Bear Coffee",
          "Sonoma",
          "MALFY Con Limone Gin",
          "CedarLane Foods",
          "Numi Organic Tea_",
          "Brazi Bites_",
          "Fever-Tree",
          "True Story®",
          "DESCHUTES_Rebate",
          "Green Giant™ Fresh",
          "Moore's Marinade and Sauce",
          "Bahlsen Cookies",
          "Three Creeks Brewing",
          "Toby's",
          "Once Upon a Farm",
          "Canterbury Soup",
          "MadeGood®",
          "Natures Intent",
          "Brew Dr. Kombucha",
          "Founders Brewing",
          "Greenfield Bacon",
          "Firestone Walker",
          "ECOS®",
          "Red Bu",
          "Mr Crumb",
          "Bundaberg Brewed Drinks",
          "Bundaberg Brewed Drinks_",
          "NANDO'S PERi PERi",
          "Clio Greek Yogurt Bar",
          "Three Bakers™ Bread",
          "Clio Greek Yogurt B",
          "Row Eleven Wines",
          "DAVE'S GOURMET",
          "Underwood Wines",
          "Uncle Harry's Ice Cream Cakes",
          "Three Bakers™ Bre",
          "LIMITLESS®",
          "Owen's Craft Mixers",
          "Sofrito Foods'",
          "Clyde May's Whiskey",
          "DAVE'S GOURMET'",
          "Sofrito Foods",
          "Theo Chocolate®",
          "Theraworx® Relief",
          "Seattle's Best Coffee",
          "Theraworx® Relief_",
          "Rainbow Light®",
          "Rainbow Light®_",
          "Theo Chocol",
          "BENGAL ROACH SPRAY",
          "Pre®",
          "BENGAL ROACH SPRAY_",
          "FRANZ Family Bakery",
          "Turkey Hill",
          "Thinsters",
          "4505 Chicharrones_",
          "TGI Fridays®",
          "MadeG",
          "Sierra Nevada",
          "Mrs. Budd's",
          "Modenaceti Balsamic Vinegar Classic",
          "ICONIC Protein",
          "Big Watt Cold Press Coffee",
          "Wewalka",
          "Caesar’s Kitchen",
          "Caesar’s Kitchen_",
          "Uptime",
          "Uptime_",
          "DRY Zero Sugar Organic Sodas",
          "SunFed Ranch ™",
          "Zespri SunGold©",
          "franz",
          "The Extreme Bean",
          "Zespr",
          "Franz_",
          "_Franz",
          "JonnyPops",
          "THE BU KO",
          "Love Grown",
          "Simple Sensations",
          "Cabot Cracker Cuts",
          "NAI: Jarritos, Mineragua, Sangria Senorial, Sidral Mundetc",
          "Jarritos, Mineragua, Sangria Senorial, Sidral Mundet",
          "Bubbies Mochi Ice Cream",
          "AdapTable® Meals",
          "True Story Foods",
          "Bubbies Mochi Ice Cream 2",
          "Schöfferhofer",
          "Uncle Harry's Ice Cream Cakes_",
          "Waterloo Sparkling Water",
          "Love Gr",
          "Lotus Trolley Bag",
          "GoodLife Brewing",
          "Q Cups",
          "Crook & Marker®",
          "Crunchy Rollers™",
          "Crunchy Roll",
          "Koops'",
          "Blue Circle Foods",
          "SunButter®",
          "Flow Alkaline Spring Water",
          "NuNaturals",
          "Field & Farmer™",
          "Chocolove® - NAI",
          "Sweet Loren's",
          "Nature’s Earthly Choice",
          "Sweet Lorens",
          "Nature's Bakery",
          "Bogle or Phantom Wine",
          "Chocolove®",
          "Tasti-Lee® Tomatoes",
          "GT's Kombucha",
          "LIMITLE",
          "Defunkify",
          "Ancient Harvest",
          "Capatriti® The Honest Olive Oil",
          "Seal the Seasons",
          "Seal the Seasons_",
          "Forager Project®_NAI",
          "Forager Project",
          "Tandoor Chef -- now Deep Indian Kitchen!",
          "Butterfinger, Crunch, Baby Ruth, 100 Grand",
          "Tandoor Chef -- now Deep Indian Kitchen!_",
          "Defunk",
          "Farm & Oven",
          "Purity Vodka",
        ],
        sendOutboundData: [
          "Teradata CRM",
          "Outbound Data 1",
          "No External Interface",
        ],
        priority: ["Low", "Medium", "High"],
        copientCategory: [
          "Private Label",
          "Affinity/Club",
          "Phase A",
          "Testing",
          "Points",
          "Product",
          "Basket",
          "Department",
          "018-Fuel",
          "Persistent",
          "Drive Frequency",
          "Audit",
          "Build Basket",
          "Drive Frequency",
          "Build Basket",
          "Cross Sell",
          "Net vs Gross",
          "Cross Sell",
          "Test",
          "2000-Awareness",
          "Up-Sell",
          "3000-Frequency",
          "Dominicks",
          "Denver Selected Stores Only",
          "De, Do, Ea, Ge, Nc, Ph, Po",
          "Denver",
          "Mobiloyalty",
          "007-J4U Manufacturer Coupons",
          "CAM Conversion",
          "PromoClassID=44",
          "PromoClassID=45",
          "PromoClassID=46",
          "PromoClassID=47",
          "PromoClassID=48",
          "PromoClassID=49",
          "PromoClassID=50",
          "PromoClassID=51",
          "PromoClassID=52",
          "PromoClassID=53",
          "PromoClassID=54",
          "PromoClassID=55",
          "PromoClassID=56",
          "PromoClassID=57",
          "PromoClassID=58",
          "PromoClassID=59",
          "PromoClassID=60",
          "PromoClassID=61",
          "PromoClassID=62",
          "PromoClassID=63",
          "PromoClassID=64",
          "PromoClassID=65",
          "PromoClassID=66",
          "001-Club Card Savings",
          "002-Store Coupon (paper)",
          "003-J4U Store eCoupons (elec)",
          "004-J4U Personalized Savings",
          "005-Deal Match",
          "006-Employee Savings",
          "PromoClassID=73",
          "008-Multiplied Coupon (mfr eCpns)",
          "009-Department Savings",
          "010-Basket Savings",
          "011-Non Discount Offers",
          "012-Customer Service Credit",
          "014-Reward Points BONUS",
          "015 - Hot Offers",
          "020-J4U Savings",
          "CURE Cutover Testing",
          "017-Dummy Special Tag",
          "Liquor Discount",
          "Alaska Airmiles Reporting",
          "Alaska Non-Reporting",
          "021-Fuel (non-redemption)",
          "30 - Wine Discount",
          "Grocery Rewards",
        ],
        engine: ["CPE", "CPE Instant Win", "CPE US Airmiles"],
        chargebackDepartments: [
          "Item's Department",
          "Prorate",
          "Tender",
          "Grocery",
          "Kodak Film Department",
          "GM",
          "Meat",
          "Whole Order Discount",
          "Pharmacy",
          "Alcoholic Beverages",
          "Jamba Juice",
          "Fuel Station",
          "Bakery Pkgd Outside",
          "Seafood",
          "Produce",
          "Starbucks",
          "Frozen Grocery",
          "Bakery In-Store",
          "Floral",
          "Dairy",
          "Deli",
          "Tobacco",
          "Food Service",
          "Soft Drinks",
          "Groc. Meals/Ingredients",
          "Candy",
          "Groc. Family Care",
          "Groc. Home Care",
          "Groc Snacks",
          "Beer",
          "Wine",
          "Narcotics",
          "GM/HBC Family Care",
          "GM/HBC Home Care",
          "Refrigerated Foods",
          "Eggs",
          "Cheese",
          "Service Seafood",
          "Frozen Meat",
          "Production Meat",
          "Service Meat",
          "Car Wash",
          "PL Grocery Dept",
          "PL GM Family Care",
          "PL GM",
          "PL Groc Home Care",
          "PL Soft Drinks",
          "PL Refrigerated Foods",
          "PL Dairy",
          "PL Frozen Grocery",
          "PL Cheese",
          "In-store Bakery",
          "Telecom",
        ],
        supportedFileExtensionsForUpload: [
          "doc",
          "docx",
          "pdf",
          "txt",
          "csv",
          "xls",
          "xlsx",
          "gif",
          "jpg",
          "jpeg",
          "png",
        ],
        offerType: {
          ITEM_DISCOUNT: "Item Discount",
          BUYX_GETX: "Buy X Get X",
          BUYX_GETY: "Buy X Get Y",
          MEAL_DEAL: "Meal Deal",
          BUNDLE: "Bundle",
          MUST_BUY: "Must Buy",
          FAB_5_OR_SCORE_4: "Fab 5 / Score 4",
          WOD_OR_POD: "WOD / POD",
          STORE_CLOSURE: "Store Closure",
          REWARDS_ACCUMULATION: "Rewards - Accumulation",
          REWARDS_FLAT: "Rewards - Flat",
          CONTINUITY: "Continuity",
          INSTANT_WIN: "Enterprise Instant Win",
          ALASKA_AIRMILES: "Alaska Airmiles",
          CUSTOM: "Custom",
        },
        adType: ["In Ad", "Not In Ad"],
        offerRequestFilters: [
          {
            displayValue: "Program Code",
            configMapper: "offerPrograms",
            facetMapper: "programCd",
          },
          {
            displayValue: "Channel",
            configMapper: "offerDeliveryChannels",
            facetMapper: "deliveryChannel",
          },
          {
            displayValue: "Offer Type",
            configMapper: "offerType",
            facetMapper: "offerType",
          },
          {
            displayValue: "Discount",
            configMapper: "amountTypes",
            facetMapper: "discountType",
          },
          {
            displayValue: "Divisions",
            configMapper: "divisions",
            facetMapper: "divisions",
          },
          {
            displayValue: "Status",
            configMapper: "offerRequestStatuses",
            facetMapper: "status",
          },
        ],
        offerFilters: [
          {
            displayValue: "Program Code",
            configMapper: "offerPrograms",
            facetMapper: "offerProgramCd",
          },
          {
            displayValue: "Channel",
            configMapper: "offerDeliveryChannels",
            facetMapper: "deliveryChannel",
          },
          {
            displayValue: "Digital/Non-Digital",
            configMapper: "digital",
            facetMapper: "digital",
          },
          {
            displayValue: "Offer Type",
            configMapper: "offerType",
            facetMapper: "offerType",
          },
          {
            displayValue: "Discount",
            configMapper: "amountTypes",
            facetMapper: "discountType",
          },
          {
            displayValue: "Divisions",
            configMapper: "divisions",
            facetMapper: "divisions",
          },
          {
            displayValue: "Status",
            configMapper: "offerStatuses",
            facetMapper: "offerStatus",
          },
          {
            displayValue: "Category",
            configMapper: "productCategories",
            facetMapper: "categories",
          },
          {
            displayValue: "Events",
            configMapper: "events",
            facetMapper: "events",
          },
        ],
        offerRequestGroups: [
          {
            name: "Corporate",
            code: "CORP",
            groupDivisions: [
              {
                name: "Multi-Division",
                code: "MD",
              },
              {
                name: "ACME",
                code: "AC",
              },
              {
                name: "Denver",
                code: "DE",
              },
              {
                name: "Eastern",
                code: "ES",
              },
              {
                name: "Haggen",
                code: "HG",
              },
              {
                name: "Intermountain",
                code: "IM",
              },
              {
                name: "Jewel",
                code: "JW",
              },
              {
                name: "Norcal",
                code: "NC",
              },
              {
                name: "Portland",
                code: "PT",
              },
              {
                name: "Seattle",
                code: "SE",
              },
              {
                name: "Shaws/Star Market",
                code: "SH",
              },
              {
                name: "SoCal",
                code: "SC",
              },
              {
                name: "Southern",
                code: "SO",
              },
              {
                name: "Southwest",
                code: "SW",
              },
            ],
          },
          {
            name: "Acme",
            code: "AM",
          },
          {
            name: "Denver",
            code: "DE",
          },
          {
            name: "Eastern",
            code: "ES",
          },
          {
            name: "Haggen",
            code: "HG",
          },
          {
            name: "Intermountain",
            code: "IM",
          },
          {
            name: "Jewel",
            code: "JW",
          },
          {
            name: "Norcal",
            code: "NC",
            groupDivisions: [
              {
                name: "NorCal",
                code: "NC",
              },
              {
                name: "Hawaii",
                code: "HI",
              },
              {
                name: "Both",
                code: "NC HI",
              },
            ],
          },
          {
            name: "Portland",
            code: "PT",
          },
          {
            name: "Seattle",
            code: "SE",
            groupDivisions: [
              {
                name: "Seattle",
                code: "SE",
              },
              {
                name: "Alaska ",
                code: "AK",
              },
              {
                name: "Both",
                code: "SE AK",
              },
            ],
          },
          {
            name: "Shaws/Star Market",
            code: "SH",
          },
          {
            name: "SoCal",
            code: "SC",
          },
          {
            name: "Southern",
            code: "SO",
          },
          {
            name: "Southwest",
            code: "SW",
          },
        ],
      }),
      getConfigUrls: (cLONE_API) => ({}),
    });
    // const authServiceStub = () => ({ getTokenString: () => ({}) });
    const facetItemServiceStub = () => ({
      programCodeSelected: {},
      getSearchFacetFields: () => ({ includes: () => ({}) }),
      getFacetItems: () => ({ reduce: () => ({}) }),
      programCodeInitial: {},
      setOfferFilter: string => ({}),
      addOfferTypes: (offerType, offerType1) => ({}),
      sortProperties: (group, string, arg, arg1) => ({}),
      addDeliveryChannels: deliveryChannel => ({}),
      getOfferFilter: () => ({}),
      populateFacetSearch: arg => ({}),
      populateFacet: offerRequestFilters => ({})
    });

    // const featureFlagServiceStub = () => ({
    //   assignFeatureFlag: () => ({}),
    //   isFeatureFlagEnabled: (arg) => ({}),
    //   hasFlags: () => ({})
    // });


    const bulkUpdateServiceStub = () => ({
      requestIdsListSelected$: { subscribe: (f) => f({}) },
      bulkAssignedUsers: {
        digitalUser: { firstName: {}, lastName: {}, userId: {} },
        nonDigitalUser: { firstName: {}, lastName: {}, userId: {} },
      },
      userTypeArray: [],
      hideApiErrorOnRequestHome: () => ({}),
      isSelectionReset: new BehaviorSubject(true),
      bulkUnAssignUsers: (userType, query) => ({ subscribe: (f) => f({}) }),
      bulkAssignUsers: (usersList, query) => ({ subscribe: (f) => f({}) }),
    });

    const requestFormServiceStub = () => ({
      cloningProcess$: new BehaviorSubject(false),
    });

    const searchOfferServiceStub = () => ({
      savedSearchForRetrieve: (reqType, userType) => ({
        subscribe: (f) => f({}),
      }),
      searchAllOffers: (arg, arg2) => ({
        subscribe: (f) => f({}),
        bind: () => ({}),
      }),
      savedSearchforOffer: (searchQuery, savedSearchName, type) => ({
        subscribe: (f) => f({}),
      }),
      updateSavedSearch: (modifyItem, savedSearchName) => ({
        subscribe: (f) => f({}),
      }),
      deleteSavedSearchOffer: (name, type) => ({ subscribe: (f) => f({}) }),
    });

    const searchOfferRequestServiceStub = () => ({
      populateHomeFilterSearch: (object) => ({}),
      getOfferDetails: (response) => ({}),
      searchOfferRequest: (arg, arg2, arg3) => ({
        subscribe: (f) => f({}),
        bind: () => ({}),
      }),
      searchAllOfferRequest: (arg, arg2, arg3) => ({
        subscribe: (f) => f({}),
        bind: () => ({}),
      }),
      currentOfferRequests: { subscribe: (f) => f({}) },
      paginationCriteria: (object) => ({}),
    });

    // const routerStub = { navigate: array => ({}) };
    const queryGeneratorStub = () => ({
      setQueryWithFilter: () => ({}),
      setQuery: () => ({}),
      getQuery: () => ({}),
      getQueryWithFilter: () => ({
        filter: () => ({})
      }),
      removeParametersFromQueryFilter: () => ({}),
      removeParam: () => ({}),
      removeParameters: () => ({}),
      pushParameters: () => ({}),
      removeQueryWithFilter: () => ({}),
      getQueryFilter: () => ({})
    });
    const storeGroupServiceStub = {
      createInstance: () => ({}),
      createFacetInstance: () => ({}),
      setEnableForm: (arg) => ({}),
      searchStoreGroup: (groupName, arg) => ({ subscribe: () => ({}) }),
      setStoreQuery: (object) => ({}),
      getFeatureKeys: () => ({ length: {}, includes: () => ({}) }),
      setFeatureKeys: (arg) => ({}),
      populateStoreFacets: () => ({ subscribe: () => ({}) }),
      populateStoreFilterSearch: (object) => ({}),
      getStoreQuery: () => ({}),
      createStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
      updateStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
      call: (arg) => ({}),
      getStoreIds: (object) => ({ subscribe: () => ({}) }),
    };

    const adminStoreGroupServiceStub = () => ({
      listCorporateStoreGroups: () => ({ subscribe: f => f({}) }),
      editCorporateStoreGroups: eventList => ({ subscribe: f => f({}) })
    });

    const offerDetailsServiceStub = () => ({
      listOfferDetailsCode: obj => ({ subscribe: f => f({}) }),
      fetchOdcList: () => ({ subscribe: f => f({}) }),
      addOfferDetailsCode: array => ({
        subscribe: f => f({}),
        bind: () => ({})
      }),
      editOfferDetailsCode: { bind: () => ({ subscribe: f => f({}) }) }
    });
    const commonRouteServiceStub = () => ({});
    // const commonSearchServiceStub = () => ({});
    const httpClientStub = () => ({
      post: () => { subscribe: () => ({}) }
  })
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        SearchOfferRequestService,
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        // { provide: FeatureFlagsService, useFactory: featureFlagServiceStub },
        { provide: FeatureFlagsService, useValue: featureFlagServiceSpy },
        { provide: AdminStoreGroupService, useFactory: adminStoreGroupServiceStub },
        { provide: OfferDetailsService, useFactory: offerDetailsServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub },
        // { provide: GeneralOfferTypeService, useFactory: generalOfferTypeServiceStub },
         { provide: GeneralOfferTypeService, useValue: generalOfferTypeSpy },
        { provide: AuthService, useValue: authServiceStub },
        { provide: OfferDetailsService, useValue: offerDetailsSpy },
        { provide: AdminStoreGroupService, useValue: adminStoreGroupServiceSpy },
        { provide: HttpClient, useValue: httpClient },
        { provide: CommonSearchService, useValue: commonSearchServiceMock },
      ]
    });
    service = TestBed.inject(SearchOfferRequestService);
    httpMock = TestBed.inject(HttpTestingController);
    offerDetailsService = TestBed.inject(OfferDetailsService) as jasmine.SpyObj<OfferDetailsService>;
    adminStoreGroupService = TestBed.inject(AdminStoreGroupService) as jasmine.SpyObj<AdminStoreGroupService>;
    httpClientSpy = TestBed.inject(HttpClient) as jasmine.SpyObj<HttpClient>;
    commonSearchServiceMock.isShowExpiredInQuery_O.and.returnValue(true);
    featureFlagServiceStub = TestBed.inject(FeatureFlagsService) as jasmine.SpyObj<FeatureFlagsService>;
    generalOfferTypeServiceStub = TestBed.inject(GeneralOfferTypeService) as jasmine.SpyObj<GeneralOfferTypeService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  // private _http: HttpClient,
  
  
  // private generalOfferTypeService:GeneralOfferTypeService


  it('can load instance', () => {
    expect(service).toBeTruthy();
  });

  it(`orData has default value`, () => {
    expect(service.orData).toEqual([]);
  });

  it(`isNoResultsMsg has default value`, () => {
    expect(service.isNoResultsMsg).toEqual(false);
  });

  describe('getHeaders', () => {
    it('makes expected calls', () => {
      const authServiceStub: AuthService = TestBed.inject(AuthService);
      spyOn(authServiceStub, 'getTokenString');
      service.getHeaders();
      expect(authServiceStub.getTokenString).toHaveBeenCalled;
    });
  });

  describe('getSavedSearchChips', () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = TestBed.inject(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "getFacetItems").and.returnValue({
        categories: [
          {
            count: 2578,
            id: "1",
            selected: false,
            value: "Baby Care",
          },
        ],
        deliveryChannel: [
          {
            count: 8189,
            id: "CC",
            selected: true,
            value: "Clip and Click",
          },
        ],
        offerStatus: [],
        offerPrototype: [],
      });
      service.getSavedSearchChips();
    });
  });
  describe("getSearchFieldForSavedSearch", () => {
    it("make expected calls", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(
        QueryGenerator
      );
      const facetItemServiceStub: FacetItemService = TestBed.inject(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "getSearchFacetFields")
        .and.returnValue(["assignedTo", "limit=100"]);
      spyOn(queryGeneratorStub, "getQueryFilter");
      spyOn(queryGeneratorStub, "getQuery")
        .and.returnValue("limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;");
      service.getSearchFieldForSavedSearch({});
    });
  });
  describe("mapRegionName", () => {
    it("make expected calls mapRegionName", () => {
      let orData = [{
        "info": {
          "id": 2116449283,
          "pluTriggerBarcode": "",
          "pluTriggerBarcodeType": null,
          "validatePluTriggerBarcode": true,
          "pluReferenceId": null,
          "deliveryChannel": "IS",
          "shoppingChannel": null,
          "displayChannel": null,
          "brandAndSize": "test",
          "nopaNumbers": null,
          "nopaStartDate": null,
          "nopaEndDate": null,
          "billingOptions": "",
          "desc": "test",
          "programCode": "SC",
          "attachments": null,
          "adType": "NA",
          "offerRequestType": "STORE_CLOSURE",
          "numOfVersions": null,
          "numOfTiers": 1,
          "numOfProducts": null,
          "digitalUser": null,
          "nonDigitalUser": {
            "userId": "nkeer00",
            "firstName": "Narender",
            "lastName": "Keerthi"
          },
          "digitalStatus": "NA",
          "nonDigitalStatus": "D",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": {
            "editStatus": "U",
            "userId": "nkeer00",
            "firstName": "Narendra",
            "lastName": "Keerthi",
            "editTs": 1613662447.544
          },
          "digitalUiStatus": "NA",
          "nonDigitalUiStatus": "U",
          "isBilled": false,
          "justification": "test",
          "group": "CORP",
          "groupDivision": "MD",
          "autoAssignMob": null,
          "mobId": null,
          "mobName": null,
          "templateId": null,
          "regionId": null,
          "programType": null
        },
        "rules": {
          "startDate": {
            "offerEffectiveStartDate": "2021-02-19T07:00:00.000+00:00"
          },
          "endDate": {
            "offerEffectiveEndDate": "2021-02-27T07:00:00.000+00:00"
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 2116117315,
                "storeGroupVersion": {
                  "id": 2116117316,
                  "displayOrder": 1,
                  "offerPrototype": "STORE_CLOSURE",
                  "storeGroup": {
                    "nonDigitalRedemptionStoreGroupIds": [
                      1668300461
                    ],
                    "podStoreGroupNames": null,
                    "nonDigitalRedemptionStoreGroupNames": [
                      "updateStoreGroupAdmin_dev9209"
                    ],
                    "digitalRedemptionStoreGroupNames": null,
                    "updateDigitalStoreGroup": false,
                    "updatePODStoreGroup": false,
                    "updateNonDigitalStoreGroup": false
                  },
                  "productGroupVersions": [
                    {
                      "displayOrder": 23,
                      "id": 2116117140,
                      "anyProduct": null,
                      "isGiftCard": null,
                      "productGroup": {
                        "id": 1724543999,
                        "quantityUnitType": "ITEMS",
                        "excludedProductGroupId": null,
                        "conjunction": null,
                        "minPurchaseAmount": null,
                        "uniqueproduct": null,
                        "tiers": [
                          {
                            "level": 1,
                            "amount": null
                          }
                        ],
                        "inheritedFromOfferRequest": null,
                        "displayOrder": null,
                        "name": "DEPT 327 - Family Care Grocery",
                        "excludedProductGroupName": null,
                        "updateProductGroup": false
                      },
                      "discountVersion": {
                        "id": 2116117137,
                        "discounts": [
                          {
                            "displayOrder": 1,
                            "id": 2116117138,
                            "benefitValueType": "PERCENT_OFF_ITEMS",
                            "discountType": "DEPARTMENT_LEVEL",
                            "includeProductGroupId": 1724543999,
                            "excludeProductGroupId": null,
                            "chargebackDepartment": "Groc. Family Care",
                            "tiers": [
                              {
                                "level": 1,
                                "amount": null,
                                "upTo": null,
                                "itemLimit": null,
                                "weightLimit": null,
                                "dollarLimit": null,
                                "receiptText": "Dept 327 - Family Care Grocery",
                                "rewards": null
                              }
                            ],
                            "includeProductGroupName": "DEPT 327 - Family Care Grocery",
                            "excludeProductGroupName": null,
                            "updateProductDiscount": false
                          }
                        ],
                        "airMiles": []
                      }
                    }
                  ],
                  "instantWin": null,
                  "storeTag": null,
                  "podDetails": null
                },
                "offers": [
                  {
                    "storeGroupVersion": 2116117316,
                    "productGroupVersion": 2116117325,
                    "discountVersion": 2116117326,
                    "genericDiscountId": 2116117331,
                    "instantWinVersion": null,
                    "offerId": 2116117332,
                    "externalOfferId": "2116117332-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT",
                    "isPodApplicable": null,
                    "offerEditStatus": "R"
                  },
                ]
              }
            ],
            "grOfferRequestOffers": null,
            "day": null,
            "time": null
          },
          "department": "Advertising1",
          "customerSegment": "Any Customer",
          "usageLimitTypePerUser": "UNLIMITED",
          "customUsageLimitPerUser": null,
          "rewardsRequired": null,
          "rank": null
        },
        "lastUpdatedTs": 1613662447.627,
        "createdTs": 1613573259.642,
        "createdApplicationId": "OMS",
        "createdUserId": "nkeer00",
        "lastUpdatedApplicationId": "OMS",
        "lastUpdatedUserId": "nkeer00",
        "createdUser": {
          "userId": "nkeer00",
          "firstName": "Narendra",
          "lastName": "Keerthi"
        },
        "updatedUser": {
          "userId": "nkeer00",
          "firstName": "Narendra",
          "lastName": "Keerthi"
        },
        "updateOffers": true,
        "cachedDigitalOfferStatus": null,
        "cachedNonDigitalOfferStatus": null,
        "allOffersAlongWithOfferRequestUpdate": false,
        "updateOnlyPluTriggerBarcode": false
      }];
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      spyOn(initialDataServiceStub, 'getAppData').and.returnValue({ regions: [{ name: "Alaska", code: "28" }] });

      service.mapRegionName(orData)
    });
  });

  describe("trackByFnForReq", () => {
    it("should make expected calls trackByFnForReq", () => {
      let item = {
        info: {

        }
      }
      service.trackByFnForReq(0, item);
    });

  });

  describe("getExcel", () => {
    it("should make expected calls getExcel", () => {
      let item = {
        info: {

        }
      }
      service.getExcel(0);
    });

  });
  describe("exportExcelF", () => {
    it("should make expected calls exportExcelF", () => {
      let item = {
        info: {

        }
      }
      service.exportExcelF('', 'queryWithOrFilters');
    });

  });

  describe("FacetsCount", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = TestBed.inject(
        FacetItemService
      );
      facetItemServiceStub.programCodeSelected = "SC";
      
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      service.getFacetCountsData(
        initialDataServiceStub.getAppData().offerRequestFilters,
        {}
      );
    });
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = TestBed.inject(
        FacetItemService
      );
      facetItemServiceStub.programCodeSelected = "GR";
      
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      service.getFacetCountsData(
        initialDataServiceStub.getAppData().offerRequestFilters,
        {}
      );
    });
  });

  describe('fetchOfferRequests', () => {

    it("should make expected calls when offerRequests Exists", () => {

      const searchOfferRequestServiceStub: SearchOfferRequestService = TestBed.inject(
        SearchOfferRequestService
      );
      const facetItemServiceStub: FacetItemService = TestBed.inject(
        FacetItemService
      );
      facetItemServiceStub.programCodeSelected = "SC";
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      const queryGeneratorStub: QueryGenerator = TestBed.inject(
        QueryGenerator
      );
      const bulkUpdateServiceStub: BulkUpdateService = TestBed.inject(
        BulkUpdateService
      );
      // const featureFlagServiceStub: FeatureFlagsService = TestBed.inject(
      //   FeatureFlagsService
      // );
      // spyOn(featureFlagServiceStub, 'isFeatureFlagEnabled');
      spyOn(bulkUpdateServiceStub, 'hideApiErrorOnRequestHome').and.returnValue(of(true));
      const offerRequests = require("@appSpecMocks/offerRequests.json");
      spyOn(initialDataServiceStub, "getAppData")
        .and.returnValue({ offerRequestFilters: [{ offerType: "Test" }], offerPrograms: { BPD: '', GR: '', SPD: '' } });
      spyOn(facetItemServiceStub, "populateFacetSearch");
      spyOn(service, "getSearchFieldForSavedSearch");
      spyOn(service, "getSavedSearchChips").and.returnValue({
        'programCd': ''
      });
      spyOn(queryGeneratorStub, "getQuery");
      Object.assign(searchOfferRequestServiceStub, {
        currentOfferRequests: of(offerRequests.offerRequests),
      });
      spyOn(service, 'mapRegionName');
      spyOn(searchOfferRequestServiceStub, "populateHomeFilterSearch");
      spyOn(searchOfferRequestServiceStub, "paginationCriteria");
      spyOn(service, "getFacetCountsData");
      spyOn(facetItemServiceStub, 'setOfferFilter');
      service.fetchOfferRequests();
      expect(service.getFacetCountsData).toHaveBeenCalled;
    });
  });
  describe("sortOptions",()=>{
    it("should return sorted array",()=>{
      let options=[{name:"name"},{name:"ao"},{name:"fd"}]
      let res=service.sortOptions(options,"name")
      expect(res).toEqual([{name:"ao"},{name:"fd"},{name:"name"}])
    });
    it("should return expected value",()=>{
      let options=[{name:""},{name:""}]
      let res=service.sortOptions(options,"name")
      expect(res).toEqual([{name:""},{name:""}])
    });
  });
  describe("toFormGroup",()=>{
    it("should make expected calls",()=>{
     const formGroup=[{}] 
     let res=service.toFormGroup(formGroup)
     expect(res.controls.undefined.value).toEqual("")
    });
  });
  describe('searchAllOfferRequest', () => {
    it('should make expected HTTP POST call', () => {
      httpClientSpy.post.and.returnValue(of({}));

      service.searchAllOfferRequest('query', true, '[]');

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.offerReqSearch_API,
        {
          query: 'query',
          includeTotalCount: true,
          includeFacetCounts: true,
          reqObj: {
            headers: {
              'X-Albertsons-Client-ID': 'OMS',
              'content-type': 'application/vnd.safeway.v1+json',
              'X-Albertsons-userAttributes': 'test-token'
            }
          },
          queryWithOrFilters: '[]'
        }
      );
    });
  });

  describe('searchOfferRequest', () => {
    it('should make expected HTTP POST call', () => {
      httpClientSpy.post.and.returnValue(of({}));

      service.searchOfferRequest('query', true, '[]');

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.offerReqSearch_API,  // Ensure this is the correct API URL
        {
          query: 'query',
          includeTotalCount: true,
          includeFacetCounts: true,
          reqObj: {
            headers: {
              'X-Albertsons-Client-ID': 'OMS',
              'content-type': 'application/vnd.safeway.v1+json',
              'X-Albertsons-userAttributes': 'test-token'
            }
          },
          queryWithOrFilters: '[]'
        }
      );
    });
  });

  describe("searchOffer", () => {
    it("should return expected value", () => {
      const externalOfferId = [null, 1]
      service.searchOffer(externalOfferId)
      expect(externalOfferId).toEqual([1])
    });
  });
  describe("populateHomeFilterSearch", () => {
    it("should make expected calls", () => {
      service["homeFilterSearchSource"] = new BehaviorSubject(service.facetItem)
      service.populateHomeFilterSearch("")
      expect(service["homeFilterSearchSource"].value).toEqual("")
    });
  });
  describe("paginationCriteria", () => {
    it("should make expected calls", () => {
      service["homeFilterPaginationSource"] = new BehaviorSubject(service.facetItem)
      service.paginationCriteria('')
    });
  });

  describe('copyOffer', () => {
    let offer: any;
    let mockResponse: any;

    beforeEach(() => {
      expect(service).toBeTruthy();
      expect(service.copyOffer).toBeDefined();

      offer = { id: 1, name: 'Test Offer' };
      mockResponse = { success: true };

    });

    it('should call the copy offer API with correct headers', (done) => {
      httpClientSpy.post.and.returnValue(of(mockResponse));

      service.copyOffer(offer).subscribe(response => {
        expect(response).toEqual(mockResponse);
        done();
      });

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        `${service.offerCreateAPI}/copy`,
        offer,
        jasmine.objectContaining({
          headers: jasmine.objectContaining({
            'X-Albertsons-userAttributes': 'test-token'
          })
        })
      );
    });

  });

  describe('getProgramService', () => {

    it('should call listOfferDetailsCode with correct parameter and return expected result', async () => {
      const mockResponse = { data: 'mockData' };
      offerDetailsService.listOfferDetailsCode.and.returnValue(of(mockResponse));

      const result = await service.getProgramService('TEST_TYPE');

      expect(offerDetailsService.listOfferDetailsCode).toHaveBeenCalledWith({ programCode: 'TEST_TYPE' });
      expect(result).toEqual(mockResponse);
    });

    it('should handle error when listOfferDetailsCode fails', async () => {
      const mockError = new Error('API Error');
      offerDetailsService.listOfferDetailsCode.and.returnValue(throwError(mockError));

      try {
        await service.getProgramService('TEST_TYPE');
        fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).toBe(mockError);
      }
    });
  });

  describe('getStoreGroupService', () => {
    it('should call listCorporateStoreGroups with correct parameter and return expected result', async () => {
      const mockResponse = { data: 'mockData' };
      adminStoreGroupService.listCorporateStoreGroups.and.returnValue(of(mockResponse));

      const result = await service.getStoreGroupService();

      expect(adminStoreGroupService.listCorporateStoreGroups).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should handle error when listCorporateStoreGroups fails', async () => {
      const mockError = new Error('API Error');
      adminStoreGroupService.listCorporateStoreGroups.and.returnValue(throwError(mockError));

      try {
        await service.getStoreGroupService();
        fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).toBe(mockError);
      }
    });
  });

  describe('getBGGMIds', () => {
    it('should return expected result', async () => {
      const mockResponse = { data: 'mockData' };
      const testBGGM = 'someBGGMValue';

      // Mock HttpClient.post to return a Promise
      httpClientSpy.post.and.returnValue(from(Promise.resolve(mockResponse)));

      const result = await service.getBGGMIds(testBGGM);

      expect(result).toEqual(mockResponse);
      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.productGroupsConfigApi,
        { query: `bggmDesc=(${testBGGM})`, requiredFieldsToFetch: ['bggm'] }
      );
    });

    it('should handle HTTP error', async () => {
      const testBGGM = 'TestBGGM';
      const mockError = new Error('Internal Server Error');

      // Mock HttpClient.post to return a rejected Promise
      httpClientSpy.post.and.returnValue(from(Promise.reject(mockError)));

      await expectAsync(service.getBGGMIds(testBGGM)).toBeRejectedWith(mockError);
      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.productGroupsConfigApi,
        { query: `bggmDesc=(${testBGGM})`, requiredFieldsToFetch: ['bggm'] }
      );
    });
  });

  describe('getBUGMIds', () => {
    it('should return expected result', async () => {
      const mockResponse = { data: ['mockBUGMId'] };
      const testBUGM = 'TestBUGM';

      httpClientSpy.post.and.returnValue(from(Promise.resolve(mockResponse)));

      const result = await service.getBUGMIds(testBUGM);

      expect(result).toEqual(mockResponse);
      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.productGroupsConfigApi,
        {
          query: `bggmDesc=(${testBUGM})`,
          requiredFieldsToFetch: ['bugm']
        }
      );
    });

    it('should handle HTTP error', async () => {
      const testBUGM = 'TestBUGM';
      const mockError = new Error('Internal Server Error');

      httpClientSpy.post.and.returnValue(from(Promise.reject(mockError)));

      await expectAsync(service.getBUGMIds(testBUGM)).toBeRejectedWith(mockError);
      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.productGroupsConfigApi,
        {
          query: `bggmDesc=(${testBUGM})`,
          requiredFieldsToFetch: ['bugm']
        }
      );
    });
  });

  describe('getCategoryIds', () => {
    it('should return expected result', async () => {
      const mockResponse = { data: ['mockCategoryId'] };
      const testBGGM = 'TestBGGM';
      const testBUGM = 'TestBUGM';

      httpClientSpy.post.and.returnValue(from(Promise.resolve(mockResponse)));

      const result = await service.getCategoryIds(testBGGM, testBUGM);

      expect(result).toEqual(mockResponse);
      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.productGroupsConfigApi,
        {
          query: `bggmDesc=(${testBGGM});bugmDesc=(${testBUGM})`,
          requiredFieldsToFetch: ['smic']
        }
      );
    });

    it('should handle HTTP error', async () => {
      const testBGGM = 'TestBGGM';
      const testBUGM = 'TestBUGM';
      const mockError = new Error('Internal Server Error');

      httpClientSpy.post.and.returnValue(from(Promise.reject(mockError)));

      await expectAsync(service.getCategoryIds(testBGGM, testBUGM)).toBeRejectedWith(mockError);
      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.productGroupsConfigApi,
        {
          query: `bggmDesc=(${testBGGM});bugmDesc=(${testBUGM})`,
          requiredFieldsToFetch: ['smic']
        }
      );
    });
  });
  
  describe('setOfferDetailsReqToObsvbl', () => {
    it('should update offerRequestCreateSource with provided offer list', () => {
      const mockOfferList = [{ id: 1, name: 'Offer1' }];
      spyOn(service['offerRequestCreateSource'], 'next');

      service.setOfferDetailsReqToObsvbl(mockOfferList);

      expect(service['offerRequestCreateSource'].next).toHaveBeenCalledWith(mockOfferList);
    });
  });

  describe('getOfferDetails', () => {
    it('should update offerRequestsSource with provided offer list', () => {
      const mockOfferList = [{ id: 2, name: 'Offer2' }];
      spyOn(service['offerRequestsSource'], 'next');

      service.getOfferDetails(mockOfferList);

      expect(service['offerRequestsSource'].next).toHaveBeenCalledWith(mockOfferList);
    });
  });

  describe('offerRequestSearchOptions', () => {
    it('should update offerRequestSearchSource with provided search options', () => {
      const mockSearchOptions: any = { keyword: 'discount' };
      spyOn(service['offerRequestSearchSource'], 'next');

      service.offerRequestSearchOptions(mockSearchOptions);

      expect(service['offerRequestSearchSource'].next).toHaveBeenCalledWith(mockSearchOptions);
    });
  });
  
  describe('SearchByOfferId', () => {
    it('should call the API with correct query when offerId is a string', () => {
      const mockOfferId = '12345';
      const mockResponse = { data: 'mockData' };
    
      httpClientSpy.post.and.returnValue(of(mockResponse));

      service.searchByOfferId(mockOfferId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.offerSearchAPI,
        jasmine.objectContaining({
          query: `sortBy=createTimestampASC;offerId=(${mockOfferId})`,
          displayStoresDivisionCount: false,
          reqObj: jasmine.objectContaining({
            headers: service.getHeaders()
          })
        })
      );
    });

    it('should join offerId array correctly and call API', () => {
      const mockOfferId = ['123', '456', '789'];
      const expectedOfferId = '123 OR 456 OR 789';
      const mockResponse = { data: 'mockData' };

      httpClientSpy.post.and.returnValue(of(mockResponse));

      service.searchByOfferId(mockOfferId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.offerSearchAPI,
        jasmine.objectContaining({
          query: `sortBy=createTimestampASC;offerId=(${expectedOfferId})`,
          displayStoresDivisionCount: false,
          reqObj: jasmine.objectContaining({
            headers: service.getHeaders()
          })
        })
      );
    });
    
  });

  describe('searchByOfferId', () => {
    it('should remove null values from offerId array before joining', () => {
      const mockOfferId = ['123', null, '456', null, '789'];
      const expectedOfferId = '123 OR 456 OR 789';
      const mockResponse = { data: 'mockData' };

      httpClientSpy.post.and.returnValue(of(mockResponse));

      service.searchByOfferId(mockOfferId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.offerSearchAPI,
        jasmine.objectContaining({
          query: `sortBy=createTimestampASC;offerId=(${expectedOfferId})`,
          displayStoresDivisionCount: false,
          reqObj: jasmine.objectContaining({
            headers: service.getHeaders()
          })
        })
      );
    });

    it('should include showExpired when feature flag is enabled', () => {
      (service['commonSearchService'] as any).isShowExpiredInQuery_O = true;

      (service['featureFlagService'] as any).isOfferArchivalEnabled = true;

      const mockOfferId = '12345';
      const mockResponse = { data: 'mockData' };

      httpClientSpy.post.and.returnValue(of(mockResponse));

      service.searchByOfferId(mockOfferId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(httpClientSpy.post).toHaveBeenCalledWith(
        service.offerSearchAPI,
        jasmine.objectContaining({
          query: `sortBy=createTimestampASC;offerId=(${mockOfferId})`,
          displayStoresDivisionCount: false,
          reqObj: jasmine.objectContaining({
            headers: service.getHeaders()
          }),
          showExpired: true
        })
      );
    });
  });

  describe('sortObject', () => {
    beforeEach(() => {
      jasmine.DEFAULT_TIMEOUT_INTERVAL = 10000; // Increase timeout interval to 10 seconds
    });
    it('should sort the object by keys', () => {
      const input = { b: 2, a: 1, c: 3 };
      const expectedOutput = { a: 1, b: 2, c: 3 };

      const result = service.sortObject(input);

      expect(result).toEqual(expectedOutput);
    });

    it('should return an empty object if input is empty', fakeAsync(() => {
      const input = {};
      const expectedOutput = {};

      const result = service.sortObject(input);

      expect(result).toEqual(expectedOutput);
    }));

    it('should handle objects with numeric keys', fakeAsync(() => {
      const input = { 2: 'b', 1: 'a', 3: 'c' };
      const expectedOutput = { 1: 'a', 2: 'b', 3: 'c' };

      const result = service.sortObject(input);

      expect(result).toEqual(expectedOutput);
    }));
  });

  describe('fetchProgramDetails', () => {
    it('should fetch program details and set options', async () => {
      const mockResponse = {
        dynaOfferProgramDetails: [
          { offerDetailsCode: 'code1' },
          { offerDetailsCode: 'code2' }
        ]
      };

      spyOn(service, 'getProgramService').and.returnValue(Promise.resolve(mockResponse));

      const result = await service.fetchProgramDetails({}, 'type');

      expect(service.getProgramService).toHaveBeenCalledWith('type');
      expect(result).toEqual({ code1: 'code1', code2: 'code2' });
    });
  });

  describe('convertPrgrmTypeToList', () => {
    it('should convert program type object to list', () => {
      const progTypeObj = {
        type1: 'Type 1',
        type2: 'Type 2'
      };

      const expectedOutput = [
        { code: 'type1', name: 'Type 1' },
        { code: 'type2', name: 'Type 2' }
      ];

      const result = service.convertPrgrmTypeToList(progTypeObj);

      expect(result).toEqual(expectedOutput);
    });

    it('should return an empty array if progTypeObj is an empty array', () => {
      const result = service.convertPrgrmTypeToList([]);

      expect(result).toEqual([]);
    });

    it('should return an empty array if progTypeObj is an empty object', () => {
      const result = service.convertPrgrmTypeToList({});

      expect(result).toEqual([]);
    });
  });

  describe('fetchProgramTypes', () => {
    it('should fetch program types and set options', async () => {
      const mockResponse = {
        dynaOfferProgramDetails: [
          { offerDetails: 'detail1', offerDetailsCode: 'code1' },
          { offerDetails: 'detail2', offerDetailsCode: 'code2' }
        ]
      };

      spyOn(service, 'getProgramService').and.returnValue(Promise.resolve(mockResponse));
      const appData = { offerDetailsProgramTypes: [] };

      const result = await service.fetchProgramTypes(appData, 'GR_ODC');

      expect(service.getProgramService).toHaveBeenCalledWith('GR_ODC');
      expect(result).toEqual([
        { code: 'detail1', name: 'code1' },
        { code: 'detail2', name: 'code2' }
      ]);
      expect(appData.offerDetailsProgramTypes).toEqual(result);
    });

    it('should handle empty program details', async () => {
      const mockResponse = {
        dynaOfferProgramDetails: []
      };

      spyOn(service, 'getProgramService').and.returnValue(Promise.resolve(mockResponse));
      const appData = { offerDetailsProgramTypes: [] };

      const result = await service.fetchProgramTypes(appData, 'GR_ODC');

      expect(service.getProgramService).toHaveBeenCalledWith('GR_ODC');
      expect(result).toEqual([]);
      expect(appData.offerDetailsProgramTypes).toEqual(result);
    });
  });

  describe('fetchBGGMIds', () => {
    it('should fetch and process BGGM IDs correctly', async () => {
      const mockBGGMData = {
        bggm: {
          id1: 'value1',
          id2: 'value2'
        }
      };

      const sortedBGGMData = {
        id1: 'value1',
        id2: 'value2'
      };

      spyOn(service, 'getBGGMIds').and.returnValue(Promise.resolve(mockBGGMData));
      spyOn(service, 'sortObject').and.returnValue(sortedBGGMData);

      const appData = { bggmData: {} };

      const result = await service.fetchBGGMIds('*', appData);

      expect(service.getBGGMIds).toHaveBeenCalledWith('*');
      expect(service.sortObject).toHaveBeenCalledWith({
        value1: 'value1',
        value2: 'value2'
      });
      expect(appData.bggmData).toEqual(sortedBGGMData);
      expect(result).toEqual(sortedBGGMData);
    });

    it('should return an empty object if getBGGMIds returns an empty object', async () => {
      const mockBGGMData = { bggm: {} };

      spyOn(service, 'getBGGMIds').and.returnValue(Promise.resolve(mockBGGMData));
      spyOn(service, 'sortObject').and.returnValue({});

      const appData = { bggmData: {} };

      const result = await service.fetchBGGMIds('*', appData);

      expect(service.getBGGMIds).toHaveBeenCalledWith('*');
      expect(service.sortObject).toHaveBeenCalledWith({});
      expect(appData.bggmData).toEqual({});
      expect(result).toEqual({});
    });

  });

  describe('isDivisionalGamesEnabled', () => {
    it('should return true when the feature flag is enabled', () => {
      featureFlagServiceStub.isFeatureFlagEnabled.and.returnValue(true);

      expect(service.isDivisionalGamesEnabled).toBeTrue();
      expect(featureFlagServiceStub.isFeatureFlagEnabled).toHaveBeenCalledWith('isDivisionalGamesFeatureEnabled');
    });

    it('should return false when the feature flag is disabled', () => {
      featureFlagServiceStub.isFeatureFlagEnabled.and.returnValue(false);

      expect(service.isDivisionalGamesEnabled).toBeFalse();
      expect(featureFlagServiceStub.isFeatureFlagEnabled).toHaveBeenCalledWith('isDivisionalGamesFeatureEnabled');
    });
  });

  describe('applyFilterBasedOnFeatureFlag', () => {
    it('should return isEcommOnlyEnabled when configMapper is ecommPromoCodeTypeChannel', () => {
      const config = { configMapper: 'ecommPromoCodeTypeChannel' };
      Object.defineProperty(generalOfferTypeServiceStub, 'isEcommOnlyEnabled', { get: () => true });

      const result = service.applyFilterBasedOnFeatureFlag(config);

      expect(result).toBeTrue();
    });

    it('should return true when configMapper is not ecommPromoCodeTypeChannel', () => {
      const config = { configMapper: 'otherConfig' };

      const result = service.applyFilterBasedOnFeatureFlag(config);

      expect(result).toBeTrue();
    });
  });

  describe('canEditOfferStartDate', () => {
    it('should return true when startDate and endDate are not provided', () => {
      const digitalStatus = 'A';
      const nonDigitalStatus = 'A';
      const rules = { startDate: {}, endDate: {} };

      const result = service.canEditOfferStartDate(digitalStatus, nonDigitalStatus, rules);

      expect(result).toBeTrue();
    });

    it('should return false when offerStartDate is before today', () => {
      const digitalStatus = 'A';
      const nonDigitalStatus = 'A';
      const rules = {
        startDate: { offerEffectiveStartDate: moment().subtract(1, 'days').toISOString() },
        endDate: { offerEffectiveEndDate: moment().add(1, 'days').toISOString() }
      };

      const result = service.canEditOfferStartDate(digitalStatus, nonDigitalStatus, rules);

    });

    it('should return false when offerEndDate is before today', () => {
      const digitalStatus = 'A';
      const nonDigitalStatus = 'A';
      const rules = {
        startDate: { offerEffectiveStartDate: moment().subtract(2, 'days').toISOString() },
        endDate: { offerEffectiveEndDate: moment().subtract(1, 'days').toISOString() }
      };

      const result = service.canEditOfferStartDate(digitalStatus, nonDigitalStatus, rules);

    });

    it('should return false when digitalStatus or nonDigitalStatus is "D"', () => {
      const digitalStatus = 'D';
      const nonDigitalStatus = 'A';
      const rules = {
        startDate: { offerEffectiveStartDate: moment().add(1, 'days').toISOString() },
        endDate: { offerEffectiveEndDate: moment().add(2, 'days').toISOString() }
      };

      const result = service.canEditOfferStartDate(digitalStatus, nonDigitalStatus, rules);

    });

    it('should return true when offerStartDate is today and offerEndDate is after today', () => {
      const digitalStatus = 'A';
      const nonDigitalStatus = 'A';
      const rules = {
        startDate: { offerEffectiveStartDate: moment().toISOString() },
        endDate: { offerEffectiveEndDate: moment().add(1, 'days').toISOString() }
      };

      const result = service.canEditOfferStartDate(digitalStatus, nonDigitalStatus, rules);

      expect(result).toBeTrue();
    });
  });

  describe('fetchBUGMIds', () => {
    it('should fetch and process BUGM IDs correctly', async () => {
      const mockBUGMData = {
        bugm: {
          id1: 'value1',
          id2: 'value2'
        }
      };

      const sortedBUGMData = {
        id1: 'value1',
        id2: 'value2'
      };

      spyOn(service, 'getBUGMIds').and.returnValue(Promise.resolve(mockBUGMData));
      spyOn(service, 'sortObject').and.returnValue(sortedBUGMData);

      const appData = { bugmData: {} };

      const result = await service.fetchBUGMIds('*', appData);

      expect(service.getBUGMIds).toHaveBeenCalledWith('*');
      expect(service.sortObject).toHaveBeenCalledWith({
        value1: 'value1',
        value2: 'value2'
      });
      expect(appData.bugmData).toEqual(sortedBUGMData);
      expect(result).toEqual(sortedBUGMData);
    });

    it('should return an empty object if getBUGMIds returns an empty object', async () => {
      const mockBUGMData = { bugm: {} };

      spyOn(service, 'getBUGMIds').and.returnValue(Promise.resolve(mockBUGMData));
      spyOn(service, 'sortObject').and.returnValue({});

      const appData = { bugmData: {} };

      const result = await service.fetchBUGMIds('*', appData);

      expect(service.getBUGMIds).toHaveBeenCalledWith('*');
      expect(service.sortObject).toHaveBeenCalledWith({});
      expect(appData.bugmData).toEqual({});
      expect(result).toEqual({});
    });

    it('should handle errors when getBUGMIds fails', async () => {
      const mockError = new Error('API Error');

      spyOn(service, 'getBUGMIds').and.returnValue(Promise.reject(mockError));

      const appData = { bugmData: {} };

      await expectAsync(service.fetchBUGMIds('*', appData)).toBeRejectedWith(mockError);

      expect(service.getBUGMIds).toHaveBeenCalledWith('*');
    });
  });

  describe('fetchCategoriesIds', () => {
    it('should fetch and process category IDs correctly', async () => {
      const mockCategoryData = {
        smic: {
          id1: 'value1',
          id2: 'value2'
        }
      };

      const sortedCategoryData = {
        id1: 'value1',
        id2: 'value2'
      };

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.resolve(mockCategoryData));
      spyOn(service, 'sortObject').and.returnValue(sortedCategoryData);

      const appData = { categoryData: {} };

      const result = await service.fetchCategoriesIds('bggmValue', 'bugmValue', false, appData);
    });

    it('should return an empty object if getCategoryIds returns an empty object', async () => {
      const mockCategoryData = { smic: {} };

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.resolve(mockCategoryData));
      spyOn(service, 'sortObject').and.returnValue({});

      const appData = { categoryData: {} };

      const result = await service.fetchCategoriesIds('bggmValue', 'bugmValue', false, appData);
    });

    it('should handle errors when getCategoryIds fails', async () => {
      const mockError = new Error('API Error');

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.reject(mockError));

      const appData = { categoryData: {} };
    });
  });

  describe('getFacetCountsData', () => {
    it('should process group, regionId, or programType when programCodeSelected is CONSTANTS.GR', () => {
      const mockOfferRequestFilters = { group: { key1: { code: 'code1', name: 'name1' }, key2: { code: 'code2', name: 'name2' } } };
      const mockFacetCounts = {};
      service.programCodeSelected = CONSTANTS.GR;

      spyOn(Object, 'keys').and.callThrough();

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);

      expect(Object.keys).toHaveBeenCalledWith(mockOfferRequestFilters.group);
    });

    it('should process progSubType or programType when programCodeSelected is CONSTANTS.SPD', () => {
      const mockOfferRequestFilters = { progSubType: { key1: 'value1', key2: 'value2' } };
      const mockFacetCounts = {};
      service.programCodeSelected = CONSTANTS.SPD;

      spyOn(Object, 'keys').and.callThrough();

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);

      expect(Object.keys).toHaveBeenCalledWith(mockOfferRequestFilters.progSubType);
    });

    it('should process subProgramCode correctly', () => {
      const mockOfferRequestFilters = { subProgramCode: { key1: 'value1', key2: 'value2' } };
      const mockFacetCounts = {};

      spyOn(Object, 'keys').and.callThrough();

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);

      expect(Object.keys).toHaveBeenCalledWith(mockOfferRequestFilters.subProgramCode);
    });

    it('should not modify value if key does not match any condition', () => {
      const mockOfferRequestFilters = { otherKey: { key1: 'value1', key2: 'value2' } };
      const mockFacetCounts = {};

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);
    });
  });

  describe('fetchCategoriesIds', () => {
    it('should fetch and process category IDs correctly', async () => {
      const mockCategoryData = {
        smic: {
          id1: 'value1',
          id2: 'value2'
        }
      };

      const sortedCategoryData = {
        id1: 'value1',
        id2: 'value2'
      };

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.resolve(mockCategoryData));
      spyOn(service, 'sortObject').and.returnValue(sortedCategoryData);

      const appData = { categoryData: {} };

      const result = await service.fetchCategoriesIds('bggmValue', 'bugmValue', false, appData);
    });

    it('should return an empty object if getCategoryIds returns an empty object', async () => {
      const mockCategoryData = { smic: {} };

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.resolve(mockCategoryData));
      spyOn(service, 'sortObject').and.returnValue({});

      const appData = { categoryData: {} };

      const result = await service.fetchCategoriesIds('bggmValue', 'bugmValue', false, appData);
    });

    it('should handle errors when getCategoryIds fails', async () => {
      const mockError = new Error('API Error');

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.reject(mockError));

      const appData = { categoryData: {} };


    });

    it('should handle search flag correctly', async () => {
      const mockCategoryData = {
        smic: {
          id1: 'value1',
          id2: 'value2'
        }
      };

      const sortedCategoryData = {
        id1: 'value1',
        id2: 'value2'
      };

      spyOn(service, 'getCategoryIds').and.returnValue(Promise.resolve(mockCategoryData));
      spyOn(service, 'sortObject').and.returnValue(sortedCategoryData);

      const appData = { categoryData: {} };

      const result = await service.fetchCategoriesIds('bggmValue', 'bugmValue', true, appData);
    });
  });

  describe('fetchRegionsIds', () => {
    it('should fetch and process region IDs correctly', async () => {
      const mockStoreGroupData = {
        dynaStoreGroups: [
          { code: 'region1', name: 'Region 1' },
          { code: 'region2', name: 'Region 2' }
        ]
      };

      const sortedRegionData = Array.isArray(mockStoreGroupData.dynaStoreGroups)
        ? mockStoreGroupData.dynaStoreGroups.reduce((acc, region) => {
            acc.push({ code: region.code, name: region.name });
            return acc;
          }, [])
        : [];

      spyOn(service, 'getStoreGroupService').and.returnValue(Promise.resolve(mockStoreGroupData));
      spyOn(service, 'sortOptions').and.returnValue(sortedRegionData);

      const appData = { storeGroupRegions: [] };

      const result = await service.fetchRegionsIds(appData);

      expect(service.getStoreGroupService).toHaveBeenCalled();
      expect(appData.storeGroupRegions).toEqual(sortedRegionData);
      expect(result).toEqual(sortedRegionData);
    });

    it('should return an empty array if getStoreGroupService returns an empty object', async () => {
      const mockStoreGroupData = { dynaStoreGroups: [] };

      spyOn(service, 'getStoreGroupService').and.returnValue(Promise.resolve(mockStoreGroupData));
      spyOn(service, 'sortOptions').and.returnValue([]);

      const appData = { storeGroupRegions: [] };

      const result = await service.fetchRegionsIds(appData);

      expect(service.getStoreGroupService).toHaveBeenCalled();
      expect(service.sortOptions).toHaveBeenCalledWith(Array.isArray(mockStoreGroupData.dynaStoreGroups) ? mockStoreGroupData.dynaStoreGroups : [], 'code');
      expect(appData.storeGroupRegions).toEqual([]);
      expect(result).toEqual([]);
    });

    it('should handle errors when getStoreGroupService fails', async () => {
      const mockError = new Error('API Error');

      spyOn(service, 'getStoreGroupService').and.returnValue(Promise.reject(mockError));

      const appData = { storeGroupRegions: [] };

      await expectAsync(service.fetchRegionsIds(appData)).toBeRejectedWith(mockError);

      expect(service.getStoreGroupService).toHaveBeenCalled();
    });

  });

  describe('searchOfferRequest', () => {
    it('should set orData to null, isNoResultsMsg to true, and call paginationCriteria with an empty object when no results are found', () => {
      spyOn(service, 'paginationCriteria');

      const mockResponse = { totalCount: 0 };
      httpClientSpy.post.and.returnValue(of(mockResponse));
    });

    it('should not set orData to null or isNoResultsMsg to true when results are found', () => {
      spyOn(service, 'paginationCriteria');

      const mockResponse = { totalCount: 10, data: ['result1', 'result2'] };
      httpClientSpy.post.and.returnValue(of(mockResponse));

      service.searchOfferRequest('query', true, '[]').subscribe(() => {
        expect(service.orData).not.toBeNull();
        expect(service.isNoResultsMsg).toBeFalse();
        expect(service.paginationCriteria).not.toHaveBeenCalledWith({});
      });
    });
  });

  describe('fetchOfferRequests', () => {
    it('should return false when routerPage is "OFFER"', () => {
      Object.defineProperty(service['commonRouteService'], 'routerPage', {
        get: () => 'OFFER'
      });

      const result = service.fetchOfferRequests();
    });

    it('should proceed with fetching offer requests when routerPage is not "OFFER"', () => {
      Object.defineProperty(service['commonRouteService'], 'routerPage', {
        get: () => 'OFFER'
      });
      
      spyOn(service['offerRequestsSource'], 'next');

      service.fetchOfferRequests();
    });
  });

  describe('getConfigMapperData', () => {
    it('should update configMapper to "offerType" when pCode is CONSTANTS.GR and configMapper is "OfferTypeGR"', () => {
      const mockAppData = {};
      const mockElement = {
        configMapper: 'OfferTypeGR'
      };
      const mockFilters = [
        { configMapper: 'OfferTypeGR' },
        { configMapper: 'OtherType' }
      ];

      spyOn(service, 'applyFilterBasedOnFeatureFlag').and.returnValue(true);
      Object.defineProperty(service, 'programCodeSelected', {
        get: () => CONSTANTS.GR
      });

      const result = service.getConfigMapperData(mockAppData, { ...mockElement, filters: mockFilters });
      expect(mockFilters[1].configMapper).toBe('OtherType');
    });

    it('should not update configMapper when pCode is not CONSTANTS.GR', () => {
      const mockAppData = {};
      const mockElement = {
        configMapper: 'OfferTypeGR'
      };
      const mockFilters = [
        { configMapper: 'OfferTypeGR' },
        { configMapper: 'OtherType' }
      ];

      spyOn(service, 'applyFilterBasedOnFeatureFlag').and.returnValue(true);
      Object.defineProperty(service, 'programCodeSelected', {
        get: () => 'OTHER_CODE'
      });

      const result = service.getConfigMapperData(mockAppData, { ...mockElement, filters: mockFilters });

      expect(mockFilters[0].configMapper).toBe('OfferTypeGR');
      expect(mockFilters[1].configMapper).toBe('OtherType');
    });

    it('should not update configMapper when filters are not provided', () => {
      const mockAppData = {};
      const mockElement = {
        configMapper: 'OfferTypeGR'
      };

      spyOn(service, 'applyFilterBasedOnFeatureFlag').and.returnValue(true);
      Object.defineProperty(service, 'programCodeSelected', {
        get: () =>  CONSTANTS.GR
      });

      const result = service.getConfigMapperData(mockAppData, mockElement);
    });
  });

  describe('getFacetCountsData', () => {
    it('should process regionId when programCodeSelected is CONSTANTS.SPD and storeGroupRegions exist', () => {
      const mockOfferRequestFilters = {};
      const mockFacetCounts = {};
      const mockAppData = {
        storeGroupRegions: [{ code: 'region1' }, { code: 'region2' }]
      };
      service.programCodeSelected = CONSTANTS.SPD;

      spyOn(service['facetItemService'], 'sortProperties').and.returnValue(mockAppData.storeGroupRegions);

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);

    });

    it('should process programType when programCodeSelected is CONSTANTS.SPD and programTypeSPD exists', () => {
      const mockOfferRequestFilters = {};
      const mockFacetCounts = {};
      const mockAppData = {
        programTypeSPD: ['type1', 'type2']
      };
      service.programCodeSelected = CONSTANTS.SPD;

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);

    });

    it('should process progSubType when programCodeSelected is CONSTANTS.SPD and offerDetailsProgramSubTypes exist', () => {
      const mockOfferRequestFilters = {};
      const mockFacetCounts = {};
      const mockAppData = {
        offerDetailsProgramSubTypes: ['subType1', 'subType2']
      };
      service.programCodeSelected = CONSTANTS.SPD;

      service.getFacetCountsData(mockOfferRequestFilters, mockFacetCounts);

    });
  });
});
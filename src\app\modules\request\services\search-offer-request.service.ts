import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import {
  Pagination
} from "@appModels/offer-request.model";
import { PRODUCT_GROUP_CONSTANTS } from "@appModules/groups/constants/product_group_constants";
import { OFFER_CONSTANTS } from "@appModules/offers/constants/offer_constants";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import { BehaviorSubject, Subject } from "rxjs";
import { AuthService } from "../../../shared/services/common/auth.service";
import { CommonRouteService } from "../../../shared/services/common/common-route.service";
import { FeatureFlagsService } from '../../../shared/services/common/feature-flags.service';
import { QueryGenerator } from "../../../shared/services/common/queryGenerator.service";
import { BulkUpdateService } from "../../../shared/services/management/bulk-update.service";
import { AdminStoreGroupService } from "../../admin/services/admin-store-group.service";
import { OfferDetailsService } from "../../offers/services/offer-details.service";
import * as moment from "moment";


@Injectable({
  providedIn: "root"
})
export class SearchOfferRequestService {
  public offerRequests: any = {};
  public offerRequestsList: any  =  {};
  public facetItem: any = {};
  public pagination: Pagination;
  orData = [];
  onClearSubProgramCdChip$ = new Subject();
  resetProgramTypeGr$ = new Subject();

  isNoResultsMsg = false;
  pageNumber: any;
  totalCount: any;
  sid: any;

  programCodeSelected: string;
  options: any;

  constructor(
    private _http: HttpClient,
    private apiConfigService: InitialDataService,
    private queryGenerator: QueryGenerator,
    private _bulkupdateservice: BulkUpdateService,
    private facetItemService: FacetItemService,
    private authService: AuthService,
    private featureFlagService: FeatureFlagsService,
    private adminStoreGroupService: AdminStoreGroupService,
    private offerDetailsService: OfferDetailsService,
    private commonRouteService:CommonRouteService,
    private generalOfferTypeService:GeneralOfferTypeService,
    private commonSearchService: CommonSearchService
  ) { 
    // intentionally left empty
  }

  offerReqSearch_API: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.OFFER_REQ_SEARCH_API
  );
  exportExcelSearch_API: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.EXPORT_EXCEL_SEARCH
  );
  getExcel_API: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.GET_EXCEL
  );

  offerSearchALL: string = this.apiConfigService.getConfigUrls(
    OFFER_CONSTANTS.OFFER_REQ_SEARCH_ALL
  );

  //offers
  offerSearchAPI: string = this.apiConfigService.getConfigUrls(
    OFFER_CONSTANTS.OFFER_SEARCH_API
  );
  offerCreateAPI: string = this.apiConfigService.getConfigUrls(
    OFFER_CONSTANTS.OFFER_CREATE_API
  );
  savedSearchAPIforOffer: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.SAVED_SEARCH_OFFER_API
  );
  savedSearchAPIforDeleteOffer: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.SAVED_SEARCH_DELETE_OFFER_API
  );
  savedSearchAPIforRetrieve: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.SAVED_SEARCH_RETRIEVE
  );
  productGroupsConfigApi: string = this.apiConfigService.getConfigUrls(
    PRODUCT_GROUP_CONSTANTS.PRODUCT_GROUP_CONFIG
  );

  private offerRequestsSource = new BehaviorSubject(this.offerRequestsList);
  currentOfferRequests = this.offerRequestsSource.asObservable();

  private offerRequestSearchSource = new BehaviorSubject(false);
  offerRequestSearch = this.offerRequestSearchSource.asObservable();

  private offerRequestCreateSource = new BehaviorSubject(this.offerRequests);
  currentOfferRequestsCreateObvl = this.offerRequestCreateSource.asObservable();

  private homeFilterSearchSource = new BehaviorSubject(this.facetItem);
  homeFilterSearchSourceSearch = this.homeFilterSearchSource.asObservable();

  private podFilterSearchSource = new BehaviorSubject(this.facetItem);
  podFilterSearchSourceSearch = this.podFilterSearchSource.asObservable();

  private homeFilterPaginationSource = new BehaviorSubject(this.facetItem);
  homeFilterPaginationSourceSearch = this.homeFilterPaginationSource.asObservable();

  actionAndMore$ = new BehaviorSubject(false);
  myTasksObj = {
    isMyTasksClicked: false,
    myTasksText: "My Tasks"
  };

  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString()
    };
  }

  public copyOffer(offer) {
    const headers = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString()
    };    
    return this._http.post(this.offerCreateAPI + '/copy', offer,{headers:headers});
  }

  getExcel(id) {
    const reqObj = {
      responseType: "blob" as "json",
      headers: CONSTANTS.HTTP_HEADERS_CSV
    };
    return this._http.post(
      `${this.getExcel_API}${id}`,
      { reqObj }
    );
  }
  exportExcelF(query: string, queryWithOrFilters = '') {
    let searchInput = {
      query,
      includeTotalCount: false,
      includeFacetCounts: false,
      reqObj: { headers: this.getHeaders() }
    };
    if (queryWithOrFilters.length) {
      searchInput['queryWithOrFilters'] = queryWithOrFilters;
    }
    return this._http.post(this.exportExcelSearch_API, searchInput);
  }
  public searchAllOfferRequest(query: string, includeFacetCounts: boolean, queryWithOrFilters = '') {
    let searchInput = {
      query,
      includeTotalCount: true,
      includeFacetCounts,
      reqObj: { headers: this.getHeaders() }
    };
    if(this.featureFlagService.isOfferRequestArchivalEnabled)
    {
      if(queryWithOrFilters.length && queryWithOrFilters?.[0].indexOf("(EX)") > -1)
        searchInput['showExpired'] = true;
    }
    //else{
      if (queryWithOrFilters.length) {
        searchInput['queryWithOrFilters'] = queryWithOrFilters?.[0].indexOf("(EX)") > -1 ? [] : queryWithOrFilters;
      }
    //}

    return this._http.post(this.offerReqSearch_API, searchInput);
  }
  public searchOffer(externalOfferId, displayStoresDivisionCount = false) {
    if (typeof externalOfferId !== "string" && externalOfferId.length > 1) {
      for (let i in externalOfferId) {
        if (externalOfferId[i] === null) { externalOfferId.splice(i, 1) }
      }
      externalOfferId = externalOfferId.join(" OR ");
    }
    let searchInput = {
      query: "sortBy=createTimestampASC;externalOfferId=(" + externalOfferId + ")",
      displayStoresDivisionCount,
      reqObj: { headers: this.getHeaders() }
    };
    if(this.featureFlagService.isOfferArchivalEnabled && this.commonSearchService.isShowExpiredInQuery_O)
    {
      searchInput['showExpired'] = true;
    }
    return this._http.post(this.offerSearchAPI, searchInput);
  }

  public searchByOfferId(offerId, displayStoresDivisionCount = false) {
    if (typeof offerId !== "string" && offerId.length > 1) {
      for (let i in offerId) {
        if (offerId[i] === null) { offerId.splice(i, 1) }
      }
      offerId = offerId.join(" OR ");
    }
    let searchInput = {
      query: "sortBy=createTimestampASC;offerId=(" + offerId + ")",
      displayStoresDivisionCount,
      reqObj: { headers: this.getHeaders() }
    };
    if(this.featureFlagService.isOfferArchivalEnabled && this.commonSearchService.isShowExpiredInQuery_O)
    {
      searchInput['showExpired'] = true;
    }
    return this._http.post(this.offerSearchAPI, searchInput);
  }

  public populateHomeFilterSearch(facetFilterSearch: any) {
    this.homeFilterSearchSource.next(facetFilterSearch);
    this.podFilterSearchSource.next(facetFilterSearch);
  }
  public paginationCriteria(criteria: any) {
    this.homeFilterPaginationSource.next(criteria);
  }
  public setOfferDetailsReqToObsvbl(offerList: any) {
    this.offerRequestCreateSource.next(offerList);
  }
  public getOfferDetails(offerList: any) {
    this.offerRequestsSource.next(offerList);
  }
  public offerRequestSearchOptions(search: any) {
    this.offerRequestSearchSource.next(search);
  }
  public searchOfferRequest(query: string, includeFacetCounts: boolean, queryWithOrFilters = "") {
    const headers = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString()
    };
    //Remove show expired from query if any;
    let isExpiredExistsInQueryWithTrue = false;
    if(query.indexOf("showExpired") > -1)
    {
      isExpiredExistsInQueryWithTrue = (query.indexOf("showExpired=true;") > -1);
      this.queryGenerator.removeParam("showExpired");
    }
    let searchInput = {
      query,
      includeTotalCount: true,
      includeFacetCounts,
      reqObj: { headers }
    };
    if (queryWithOrFilters?.length) {
      searchInput['queryWithOrFilters'] = queryWithOrFilters;
    }
    if(this.featureFlagService.isOfferRequestArchivalEnabled)
    {
      if(this.commonSearchService.isShowExpiredInQuery || isExpiredExistsInQueryWithTrue){
        searchInput['query'] = query.replace('showExpired=true;',''); 
        searchInput['showExpired'] = true;
      }
    }
    return this._http.post(this.offerReqSearch_API, searchInput);
  }

  toFormGroup(formGroup) {
    let group: any = {};

    formGroup.forEach(items => {
      group[items.key] = new UntypedFormControl("", items.validate);
    });
    return new UntypedFormGroup(group);
  }

  getFacetCountsData(offerRequestFilters, facetCounts) {
    if (offerRequestFilters) {
      for (let [key, value] of Object.entries(offerRequestFilters)) {
        let facetParam = facetCounts[key];
        let programCodeSelected = this.facetItemService.programCodeSelected;
        if (key === 'group' || key === 'regionId' || (key === 'programType' && programCodeSelected === CONSTANTS.GR)) {
          const divObj = {};
          let keys = Object.keys(value);
          keys.forEach(ele => {
            const dObj = value[ele] || ele.trim();
            divObj[`${dObj['code']}::${dObj['name']}`] = 0;
          });
          value = divObj;
        }
        else if (programCodeSelected === CONSTANTS.SPD) {
          if (['progSubType', 'programType'].includes(key)) {
            const divObj = {};
            let keys = Object.keys(value);
            keys.forEach(ele => {
              let dObj = value[ele] || ele.trim();
              divObj[`${dObj}`] = 0;
            });
            value = divObj;
          }
        } else if(key === "subProgramCode") {
          const divObj = {};
          let keys = Object.keys(value);
          keys.forEach(ele => {
            let dObj = value[ele] || ele.trim();
            divObj[`${dObj}`] = 0;
          });
          value = divObj;
        }
        if (value) {
          let keys = Object.keys(value);
          keys.forEach(ele => {
            if (facetParam && facetParam[ele]) {
              value[ele] = facetParam[ele];
            } else {
              value[ele] = 0;
            }
          });
        }

        offerRequestFilters[key] = value;
      }
    }

  }

  getSearchFieldForSavedSearch(facetChip) {
    const searchKeys = this.facetItemService.getSearchFacetFields(),
      query = this.queryGenerator.getQuery(), split = query.split(';'),
      queryWithOrFilters = this.queryGenerator.getQueryFilter('User');
    split.forEach(ele => {
      const include = ele.slice(0, ele.indexOf('='));
      if (searchKeys.includes(include)) {
        facetChip[include] = ele.slice(ele.indexOf('=') + 1, ele.length);
      }
    });
    if (searchKeys.includes('assignedTo') && queryWithOrFilters) {
      facetChip['assignedTo'] = queryWithOrFilters;
    }
    // setting the flag to reset the checkboxes for bulk. change to somewhere else if appropriate
    this._bulkupdateservice.isSelectionReset.next(true)
  }

  getSavedSearchChips() {
    let items = this.facetItemService.getFacetItems(), keys = Object.keys(items), facetList = {};
    keys.forEach(key => {
      items[key].reduce((output, ele) => {
        if (ele.selected) {
          if (!output[key]) {
            output[key] = ele.value;
          } else {
            output[key] = key === 'status' ? [output[key], ele.value].join(';') : [output[key], ele.value].join(',');
          }
        }
        return output;
      }, facetList);
    });
    return facetList;
  }

  fetchOfferRequests() {
    this.currentOfferRequests.subscribe((offerRequests: any) => {
       if(this.commonRouteService.routerPage ==="OFFER"){
         return false;
       }
      const appData = this.apiConfigService.getAppData();
      let programCodeSelected = this.facetItemService.programCodeSelected,
        pCode = programCodeSelected === CONSTANTS.SC ? "" : programCodeSelected, filters = appData[`offerRequestFilters${pCode}`];
      if (pCode === CONSTANTS.GR) {
        filters?.forEach(ele => {
          if (ele.configMapper === "OfferTypeGR") {
            ele.configMapper = 'offerType';
          }
        })
      }
      if ([CONSTANTS.SC, CONSTANTS.GR, CONSTANTS.BPD, CONSTANTS.SPD].indexOf(programCodeSelected) > -1) {
        this._bulkupdateservice.userTypeArray = [];
        const offerRequestStatuses = {
          DI: 'Digital',
          NDI: 'Non-Digital'
        };

        if (this.facetItemService.programCodeInitial) {
          this.facetItemService.programCodeInitial = false;
          this.facetItemService.setOfferFilter('facetSearch');
        }

        const statusData = { ...offerRequestStatuses, ...appData.offerRequestStatuses };
        delete appData.offerRequestStatuses;
        appData.offerRequestStatuses = statusData;

        


        let offerType = null, facetChip, offerRequestFilters = filters?.reduce((output, ele) => {
          if (programCodeSelected && [CONSTANTS.GR, CONSTANTS.SPD].includes(programCodeSelected) && ele.facetMapper === 'status') {
            output['regionId'] = {};
          }
          const configMapper = this.getConfigMapperData(appData,ele);
          if(configMapper){
            output[ele.facetMapper] = { ...configMapper };
          }
          
          return output;
        }, {});
        // delete offerRequestFilters['regions'];
        if (offerRequests?.facetCounts && offerRequestFilters?.offerType) {
          offerType = this.facetItemService.addOfferTypes(offerRequests?.facetCounts?.offerType, offerRequestFilters.offerType);
        }


        if (offerRequests?.facetCounts) {
          offerRequests.facetCounts.offerType = offerType;
        }

        if (programCodeSelected === CONSTANTS.SC) {
          offerRequestFilters.group = this.facetItemService.sortProperties(offerRequestFilters.group, 'name', false, false);
          this.facetItemService.addDeliveryChannels(offerRequestFilters.deliveryChannel);

        }

        this.orData = offerRequests?.offerRequests;

        if (programCodeSelected === CONSTANTS.GR && appData?.storeGroupRegions) {
          offerRequestFilters.regionId = this.facetItemService.sortProperties(appData.storeGroupRegions, 'code', true, false);
        }
        if (programCodeSelected === CONSTANTS.GR) {
          if(this.isDivisionalGamesEnabled) {
            const isPrgrmTypeList = toString.call(appData?.offerDetailsProgramTypesRequestGR) === '[object Array]';
            if(isPrgrmTypeList && appData?.offerDetailsProgramTypesRequestGR?.length) {
              offerRequestFilters.programType = this.facetItemService.sortProperties(appData.offerDetailsProgramTypesRequestGR, 'name', false, false);
            } else if(toString.call(appData?.offerDetailsProgramTypesRequestGR) === '[object Object]' && Object.keys(appData?.offerDetailsProgramTypesRequestGR)?.length) {
              offerRequestFilters.programType = this.convertPrgrmTypeToList(appData?.offerDetailsProgramTypesRequestGR);
            } else {
              offerRequestFilters.programType = [];
            }

          } else {
            if(appData?.offerDetailsProgramTypes) {
              offerRequestFilters.programType = this.facetItemService.sortProperties(appData.offerDetailsProgramTypes, 'name', false, false);
            }
          }
        }
        if (programCodeSelected === CONSTANTS.SPD) {
          if (appData?.storeGroupRegions) {
            offerRequestFilters.regionId = this.facetItemService.sortProperties(appData.storeGroupRegions, 'code', true, false);
          }
          if (appData?.programTypeSPD) {
            offerRequestFilters.programType = appData.programTypeSPD;
          }
          if (appData?.offerDetailsProgramSubTypes && offerRequestFilters) {
            offerRequestFilters.progSubType = appData?.offerDetailsProgramSubTypes;
          }
          if(!this.featureFlagService.isFeatureFlagEnabled('enableACDeliveryChannel')){
            delete offerRequestFilters['deliveryChannel'];
          }
        }


        if (offerRequests?.offerRequests && offerRequests?.offerRequests.length) {
          if(offerRequests?.offerRequests){
            this.isNoResultsMsg = !(offerRequests?.offerRequests.length > 0);
          }
          this.totalCount = offerRequests?.totalCount;
          this.sid = offerRequests?.sid;
          this.pageNumber = offerRequests?.current;
          this.orData = offerRequests?.offerRequests;
          this.mapRegionName(this.orData);
          this.paginationCriteria({ totalCount: this.totalCount, pageNumber: this.pageNumber, sid: this.sid });

          if (!offerRequests?.pagination && ((this.facetItemService.getOfferFilter() === 'facetSearch') || (offerRequests.facetCounts && Object.keys(offerRequests.facetCounts).length))) {
            this.getFacetCountsData(offerRequestFilters, offerRequests?.facetCounts || {});
            this.facetItemService.populateFacet(offerRequestFilters);
            facetChip = this.facetItemService.populateFacetSearch(this.queryGenerator.getQuery());
            
           
              // When persistance enabled, need to get filter chip also on management page landing if any filter key exist in query
              const filterChip = this.getSavedSearchChips();
              if(filterChip) {
                const {programCd: programCode = null, ...rest} = {...filterChip};
                facetChip = {programCode, ...facetChip, ...rest};
              }
            
            this.populateHomeFilterSearch({ facetChip, facetFilter: this.facetItemService.getFacetItems() });
          }

        } else {
          //let programCodeSelected = this.facetItemService?.programCodeSelected;

          // if ([CONSTANTS.BPD].indexOf(programCodeSelected) < 0) {
          //   this.facetItemService.populateFacet(offerRequestFilters);
          //   this.populateHomeFilterSearch({ facetChip, facetFilter: this.facetItemService.getFacetItems() });
          // }
          
          this.orData = null;
          this.isNoResultsMsg = true;
          this.paginationCriteria({});
        }
        if (offerRequests?.savedSearches) {
          this.orData = offerRequests?.offerRequests;
          this.getFacetCountsData(offerRequestFilters, offerRequests?.facetCounts || {});
          this.facetItemService.populateFacet(offerRequestFilters);
          facetChip = this.getSavedSearchChips();
          this.getSearchFieldForSavedSearch(facetChip);
          if (facetChip['programCd']) {
            facetChip['programCode'] = facetChip['programCd'];
            delete facetChip['programCd'];
          }
          this.facetItemService.setOfferFilter(null);
          this.populateHomeFilterSearch({ facetChip, facetFilter: this.facetItemService.getFacetItems() });

        }
      }

    })

  }
  get isDivisionalGamesEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled('isDivisionalGamesFeatureEnabled')
  }
  getConfigMapperData(appData,element){
    let configMapperData;
    if(this.applyFilterBasedOnFeatureFlag(element)){
      configMapperData = {...appData[element.configMapper]};
    }
    
    if(["offerDeliveryChannels"].includes(element.configMapper) && !this.generalOfferTypeService.isEcommOnlyEnabled){
      delete configMapperData["EC"];
    }
    if(configMapperData && ["ecommPromoCodeTypeChannel"].includes(element.configMapper)){
      const ecommPromoCodeMapper=[{flag:CONSTANTS.ENABLE_ECOM_PROMO_CODE,channel:CONSTANTS.EPC},{flag:CONSTANTS.ENABLE_SCHEDULE_AND_SAVE,channel:CONSTANTS.ESS}];
      ecommPromoCodeMapper.forEach((item)=>{
        if(!this.featureFlagService.isFeatureFlagEnabled(item.flag)){
          delete configMapperData[item.channel];
        }
      })   
    }
    return configMapperData;
  }
  applyFilterBasedOnFeatureFlag(config){
    if(["ecommPromoCodeTypeChannel"].includes(config.configMapper)){
     return this.generalOfferTypeService.isEcommOnlyEnabled;
    }
     return true;
   }
  mapRegionName(orData) {
    const regionData = this.apiConfigService.getAppData().regions;
    orData?.forEach((val, index, arr) => {
      const rData = regionData.filter((item) => item.code === val.info.regionId);
      if (rData.length) {
        val.info.regionName = rData[0].name;
      }
    });
  }

  trackByFnForReq(index, item) {
    return (item.info);
  }
  async fetchProgramTypes(appData = this.apiConfigService.getAppData(), type = "GR_ODC") {
    let offerDetails: any = await this.getProgramService(type);
    this.options = offerDetails.dynaOfferProgramDetails.map((val) => {
      return {
        code: val.offerDetails,
        name: val.offerDetailsCode
      }
    });
    //const appData = this.apiConfigService.getAppData();
    appData.offerDetailsProgramTypes = this.options;
    return this.options;
  }
  convertPrgrmTypeToList(progTypeObj) {
    if(progTypeObj) {
      const progrmTypeList = Object.keys(progTypeObj)?.map((key) =>{
        return {
          code: key,
          name: progTypeObj[key]
        }
      });
      return progrmTypeList || [];
    }
  }
  async fetchProgramDetails(appData, type) {
    let offerDetails: any = await this.getProgramService(type);
    this.options = offerDetails?.dynaOfferProgramDetails?.reduce((output, val) => {
      output[val.offerDetailsCode] = val.offerDetailsCode
      return output;
    },{});
    return this.options;
  }

  getProgramService(type) {
    return this.offerDetailsService.listOfferDetailsCode({ programCode: type}).toPromise();
  }
  getStoreGroupService() {
    return this.adminStoreGroupService.listCorporateStoreGroups().toPromise();
  } 
  getBGGMIds(bggm) {
    return this._http.post(this.productGroupsConfigApi, { query : `bggmDesc=(${bggm})` ,requiredFieldsToFetch: [ "bggm" ]}).toPromise();
  }
  getBUGMIds(bugm) {
    return this._http.post(this.productGroupsConfigApi, { query : `bggmDesc=(${bugm})` ,requiredFieldsToFetch: [ "bugm" ]}).toPromise();
  }
  getCategoryIds(bggmDesc,bugmDesc) {
    return this._http.post(this.productGroupsConfigApi, { query : `bggmDesc=(${bggmDesc});bugmDesc=(${bugmDesc})` ,requiredFieldsToFetch: [ "smic" ]}).toPromise();
  }
   async fetchBGGMIds(bggm = "*",appData = this.apiConfigService.getAppData()){
    let bggmData: any = await this.getBGGMIds(bggm); 
    const object = bggmData["bggm"];
    bggmData = Object.keys(object).reduce((output,ele)=>{
      const value = object[ele];
      output[value] = value;
    return output;
    },{});
    const sortedbggmData = this.sortObject(bggmData);
    appData.bggmData = sortedbggmData;
    return sortedbggmData;
  }
 
  sortObject(obj) {
    return Object.keys(obj).sort().reduce(function (acc, key) {
      acc[key] = obj[key];
      return acc;
    }, {});
  }
  async fetchBUGMIds(bugm = "*",appData = this.apiConfigService.getAppData()){

    let bugmData: any = await this.getBUGMIds(bugm);
    const object = bugmData["bugm"];
    bugmData = Object.keys(object).reduce((output,ele)=>{
      const value = object[ele];
      output[value] = value;
    return output;
    },{});
    const sortedbugmData = this.sortObject(bugmData);
    appData.bugmData = sortedbugmData;
    return sortedbugmData;
  }
  async fetchCategoriesIds(bggm = "*",bugm = "*",search = false,appData = this.apiConfigService.getAppData()){
    if(search){
      let categoriesData: any = await this.getCategoryIds(bggm,bugm);
      const object = categoriesData["smic"];
      categoriesData = Object.keys(object).reduce((output,ele)=>{
        const value = object[ele];
        const catId = ele.slice(0,-2);
        output[catId] = `${ele} ${value}`;
      return output;
      },{});
     // const sortedCategoryData = this.sortObject(categoriesData);
      appData.categoriesData = categoriesData;
      return categoriesData;
    }
   
  
  }
  async fetchRegionsIds(appData = this.apiConfigService.getAppData()){

    let storeGroup: any =  await this.getStoreGroupService();
    const regionData = this.apiConfigService.getAppData().regions;
    const regionObject = regionData.reduce((output, ele) => {
      const code = ele.code;
      output[code] = ele;
      return output;
    },{})
      const options = this.sortOptions((storeGroup.dynaStoreGroups || storeGroup.storeGroups).map((val) => {
      
      const rData = regionObject[val.regionId];
      if (rData) {
        return {
          storeGroupId: val.storeGroupRid || val.id,
          name: `${val.regionId} ${rData.name}`,
          code: val.regionId,
          storeGroupName: val.storeGroupName
        }
      }
      
    }),"code").filter((ele)=>ele?.code);
    appData.storeGroupRegions = options;
    return options;
  }
 
  

  sortOptions(options, field) {
    return options.sort((a, b) => {  // a should come before b in the sorted order
      if (a[field] < b[field]) {
        return -1;
        // a should come after b in the sorted order
      } else if (a[field] > b[field]) {
        return 1;
        // and and b are the same
      } else {
        return 0;
      }
    })
  }

  canEditOfferStartDate(digitalStatus,nonDigitalStatus, rules) {
    let canEdit = true;

    const startDate = rules.startDate.offerEffectiveStartDate;
    const endDate = rules.endDate.offerEffectiveEndDate;

    if (!startDate || !endDate) {
        return canEdit;
    }

    const offerStartDate = moment(startDate);
    const offerEndDate = moment(endDate);
    const today = moment().utcOffset(0, true);
    const isSameOrBefore = offerStartDate.isSameOrBefore(today);
    const isSameOrAfter = offerEndDate.isSameOrAfter(today);
    const isCompletedOR = digitalStatus === "D" || nonDigitalStatus === "D";
    canEdit = !(isCompletedOR && isSameOrBefore && isSameOrAfter);
    return canEdit;
  }  
}

<div class="container-fluid">
  <spinner *ngIf="loading"></spinner>
    <ng-container *ngVar="errors as error">
      <div class="row col text-danger mb-4" *ngIf="error">
        <span *ngIf="error.errorTxt">{{ error.errorTxt }}</span>
      </div>
  </ng-container>
  <section class="row m-0 mb-4" *ngIf="!isSCPcSelected">
    <div class="pr-3">
      <input
        type="radio"
        name="copyType"
        value="byDates"
        [checked]="copyWithDates"
        (change)="onChangeCopyTypeValue('byDate')"
      />
      <label class="pl-2 input-label small">Enter Dates</label>
    </div>
    <div class="pl-3">
      <input
        type="radio"
        name="copyType"
        value="by4weeks"
        [checked]="!copyWithDates"
        (change)="onChangeCopyTypeValue('by+4Weeks')"
      />
      <label class="pl-2 input-label small">+4 Weeks</label>
    </div>
  </section>
  <form class="pod-form" [formGroup]="batchOfferRequestCopyForm">
    
    <div class="row" *ngIf="canShowDynamicCheckBox && copyWithDates">
      <div class="col-6">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label class="float-left font-weight-bold small" for="title"
              >Dynamic</label
            >
          </div>
          <div class="col">
            <input 
            type="checkbox" 
            id="isDynamicOffer"
            name="isDynamicOffer"
            [formCtrl]="getFormCtrl('isDynamicOffer')"
            formControlName="isDynamicOffer"          
            (change)="onDynamicCheckBoxClick($event.target)"
            (checked)="isDynamicOfferChecked"
          />
          </div>
        </div>
      </div>
      <div class="col-6" *ngIf="isDynamicOfferChecked">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label class="float-left font-weight-bold small" for="title"
              >Days To Redeem</label
            >
          </div>
          <div class="col">
            <input
            type="text"
            class="form-control"
            markAsTouchedOnFocus
            id="daysToRedeem"
            name="daysToRedeem"
            [formCtrl]="getFormCtrl('daysToRedeem')"
            formControlName="daysToRedeem"
            (input)="onInput($event.target.value, 'daysToRedeem')"
            appInputPattern="integerOrEmpty"
          />
          </div>
        </div>
      </div>
      
      
  </div>


    <ng-container *ngIf="copyWithDates">
      <div class="row">
        <div class="col-6">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label class="float-left font-weight-bold small" for="title"
                >Offer Start Date</label
              >
            </div>
            <div class="col">
              <input
                markAsTouchedOnFocus
                onkeydown="return false"
                type="text"
                [formCtrl]="getFormCtrl('offerStartDate')"
                class="form-control form-control-lg optional input-background"
                id="startDate"
                name="startDate"
                autocomplete="off"
                formControlName="offerStartDate"
                [minDate]="minOfferStartDate"
                (bsValueChange)="setMinOfferEndDate($event)"
                [bsConfig]="{
                  containerClass: colorTheme,
                  dateInputFormat: 'MM/DD/YYYY',
                  showWeekNumbers: false
                }"
                bsDatepicker
              />
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label class="float-left font-weight-bold small" for="title"
                >Offer End Date</label
              >
            </div>
            <div class="col">
              <input
                onkeydown="return false"
                markAsTouchedOnFocus
                [formCtrl]="getFormCtrl('offerEndDate')"
                type="text"
                class="form-control form-control-lg optional input-background"
                id="endDate"
                name="endDate"
                autocomplete="off"
                formControlName="offerEndDate"
                [minDate]="minOfferEndDate"
                [bsConfig]="{
                  containerClass: colorTheme,
                  dateInputFormat: 'MM/DD/YYYY',
                  showWeekNumbers: false
                }"
                bsDatepicker
              />
            </div>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="showDisplayEndDate">
        <div class="col-6">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label class="float-left font-weight-bold small" for="title"
                >Display End Date</label
              >
            </div>
            <div class="col" >
              <input
                onkeydown="return false"
                type="text"
                [formCtrl]="getFormCtrl('displayEndDate')"
                markAsTouchedOnFocus
                class="form-control form-control-lg optional input-background"
                id="displayEndDate"
                name="endDate"
                autocomplete="off"
                formControlName="displayEndDate"
                [minDate]="minOfferEndDate"
                [bsConfig]="{
                  containerClass: colorTheme,
                  dateInputFormat: 'MM/DD/YYYY',
                  showWeekNumbers: false
                }"
                bsDatepicker
              />
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <div class="row" *ngIf="isSCPcSelected">
      <div class="col">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label
              class="float-left font-weight-bold small"
              for="prodDescription"
              > Additional Details</label
            >
          </div>

          <div class="col col-12">
            <textarea
              type="text"
              class="form-control"
              id="additionalDetails"
              [formCtrl]="getFormCtrl('additionalDetails')"
              markAsTouchedOnFocus
              rows="4"
              name="additionalDetails"
              formControlName="additionalDetails"
              maxlength="1000"
            >
            </textarea>
          </div>
        </div>
      </div>
    </div>
    <ng-container *ngIf="!isSCPcSelected">
      <div class="row">
        <div class="col">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label class="float-left font-weight-bold small" for="title"
                >Price Text</label
              >
            </div>
            <div class="col-6">
              <input
                type="text"
                class="form-control"
                markAsTouchedOnFocus
                id="title"
                name="title"
                [formCtrl]="getFormCtrl('priceText')"
                formControlName="priceText"
                (input)="onInput($event.target.value, 'priceText')"
                maxlength="25"
              />
            </div>
            <small class="text-danger col-12" *ngIf="priceTextLength >= 16"
              >Exceeds recommended characters. May not display correctly. ({{
                25 - priceTextLength
              }}
              characters left)</small
            >
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label class="float-left font-weight-bold small" for="title"
                >Headline 1</label
              >
            </div>
            <div class="col col-12">
              <input
                type="text"
                class="form-control"
                id="headline1"
                [formCtrl]="getFormCtrl('headline1')"
                markAsTouchedOnFocus
                name="headline1"
                formControlName="headline1"
                (input)="onInput($event.target.value, 'headline')"
                maxlength="100"
              />
              <small class="text-danger" *ngIf="headlineLength >= 90"
                >Exceeds recommended characters. May not display correctly. ({{
                  100 - headlineLength
                }}
                characters left)</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label class="float-left font-weight-bold small" for="title"
                >Headline 2</label
              >
            </div>
            <div class="col col-12">
              <input
                type="text"
                class="form-control"
                id="headline2"
                name="headline2"
                markAsTouchedOnFocus
                [formCtrl]="getFormCtrl('headline2')"
                formControlName="headline2"
                (input)="onInput($event.target.value, 'headline2')"
                maxlength="100"
              />
              <small class="text-danger" *ngIf="headline2Length >= 90"
                >Exceeds recommended characters. May not display correctly. ({{
                  100 - headline2Length
                }}
                characters left)</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col">
          <div class="form-group row">
            <div class="clearfix col-12">
              <label
                class="float-left font-weight-bold small"
                for="prodDescription"
                >Offer Description</label
              >
            </div>
  
            <div class="col col-12">
              <textarea
                type="text"
                class="form-control"
                id="offerDescription"
                [formCtrl]="getFormCtrl('offerDescription')"
                markAsTouchedOnFocus
                rows="4"
                name="offerDescription"
                formControlName="offerDescription"
                (input)="onInput($event.target.value, 'offerDesc')"
                maxlength="100"
              >
              </textarea>
              <small class="text-danger" *ngIf="offerDescLength >= 90"
                >Exceeds recommended characters. May not display correctly. ({{
                  100 - offerDescLength
                }}
                characters left)
              </small>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </form>
  <div class="row">
    <div class="col d-flex mt-3 mb-3 justify-content-end">
      <label
        class="anchor-link-blue cursor-pointer mr-4 mb-0 align-self-center"
        (click)="modalRef.hide()"
      >
        <u>Cancel</u>
      </label>
      <button
        class="btn btn-primary font-weight-bolder submit-btn"
        (click)="onClickCopy()"
      >
        Copy
      </button>
    </div>
  </div>
</div>

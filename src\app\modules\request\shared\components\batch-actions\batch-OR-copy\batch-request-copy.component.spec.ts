import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService,ToastrModule } from 'ngx-toastr';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators, FormControl, FormGroup, FormBuilder } from '@angular/forms';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, of, throwError } from 'rxjs';
import { BatchOfferRequestCopy } from './batch-request-copy.component';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { FileSaverService } from 'ngx-filesaver';
import { NotificationService } from '@appServices/common/notification.service';
import { HttpClient } from '@angular/common/http';
import { dateInOriginalFormat } from "@appUtilities/date.utility";
import { CONSTANTS } from '@appConstants/constants';


describe('BatchRequestCopyComponent', () => {
  let component: BatchOfferRequestCopy;
  let fixture: ComponentFixture<BatchOfferRequestCopy>;
  let bulkUpdateService: jasmine.SpyObj<BulkUpdateService>;
  let facetItemService: jasmine.SpyObj<FacetItemService>;
  let toastrService: jasmine.SpyObj<ToastrService>;
  let modalRef: jasmine.SpyObj<BsModalRef>;
  let commonSearchService: jasmine.SpyObj<CommonSearchService>;
  let featureFlagsService: jasmine.SpyObj<FeatureFlagsService>;
  let queryGenerator: jasmine.SpyObj<QueryGenerator>;
  
  beforeEach(async () => {
    bulkUpdateService = jasmine.createSpyObj('BulkUpdateService', [
      'doBatchCopySC',
      'doBatchCopyOR',
      'doBatchCopyBPDOR',
      'checkIfActionEnabledForUniversalJob',
    ]);
    bulkUpdateService.isAllBatchSelected = new BehaviorSubject<string>('testValue');
    bulkUpdateService.requestIdArr = ['123', '456'];
    bulkUpdateService.requestIdsListSelected$ = new BehaviorSubject<any[]>([]);
    bulkUpdateService.createdAppIds$ = new BehaviorSubject<any[]>([]);
    bulkUpdateService.offerBulkSelection = new BehaviorSubject<any>(null);
    bulkUpdateService.isSelectionReset = new BehaviorSubject<boolean>(false);
    bulkUpdateService.deliveryChannelArr = [];
    
    facetItemService = jasmine.createSpyObj('FacetItemService', ['programCodeSelected']);
    toastrService = jasmine.createSpyObj('ToastrService', ['success']);
    modalRef = jasmine.createSpyObj('BsModalRef', ['hide']);
    commonSearchService = jasmine.createSpyObj('CommonSearchService', ['isAllBatchSelected', 'isShowExpiredInQuery', { isAllBatchSelected: of('selectAcrossAllPages') }]);
    featureFlagsService = jasmine.createSpyObj('FeatureFlagsService', ['isFeatureFlagEnabled', 'isArchivalEnabled']);
    queryGenerator = jasmine.createSpyObj('QueryGenerator', ['removeParameters', 'getQuery', 'getQueryWithFilter']);
    
    await TestBed.configureTestingModule({
      declarations: [BatchOfferRequestCopy],
      imports: [ToastrModule.forRoot()],
      providers: [
        BsModalRef,
        UntypedFormBuilder,
        { provide: CommonSearchService, useValue: commonSearchService },
        { provide: HttpClient, useValue: {} },
        { provide: BsModalService, useValue: {} },
        { provide: FacetItemService, useValue: facetItemService },
        { provide: ToastrService, useValue: toastrService },
        { provide: FeatureFlagsService, useValue: featureFlagsService },
        { provide: QueryGenerator, useValue: queryGenerator },
        { provide: FileSaverService, useValue: {} },
        { provide: NotificationService, useValue: {} },
        { provide: BulkUpdateService, useValue: bulkUpdateService },
        { provide: ActivatedRoute, useValue: {} },
        { provide: FileAttachService, useValue: {} },
        { provide: PermissionsService, useValue: { isAdmin: () => false } },
        { provide: SearchOfferRequestService, useValue: { getImportLogItems: () => of([]) } },
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BatchOfferRequestCopy);
    component = fixture.componentInstance;
    fixture.detectChanges();

    modalRef = jasmine.createSpyObj('BsModalRef', ['hide', 'setClass']);
    component.modalRef = modalRef;

    component.batchOfferRequestCopyForm = new FormGroup({
      offerStartDate: new FormControl('2025-02-14'),
      offerEndDate: new FormControl('2025-03-01'),
      displayEndDate: new FormControl('2025-04-01'),
    });

    component.action = {
      checkUniversalJobFeatureFlag: true,
      universalJobFlagValue: "someFlag",
    };

    bulkUpdateService.checkIfActionEnabledForUniversalJob.and.returnValue(true);
    facetItemService.programCodeSelected = 'someValue';

    queryGenerator.getQueryWithFilter = jasmine.createSpy().and.returnValue('mockQueryWithFilters');
    queryGenerator.getQuery = jasmine.createSpy().and.returnValue('mockQuery');

    // Spying on methods
    spyOn(component, 'getPayloadObj');
    spyOn(component, 'getPayloadObjForSC');

    // featureFlagsService.isArchivalEnabled = true;
    commonSearchService.isShowExpiredInQuery = true;

  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
    
  it('should initialize the form in ngOnInit', () => {
    spyOn(component, 'buildForm');
    spyOn(component, 'initVariables');

    component.ngOnInit();

    expect(component.buildForm).toHaveBeenCalled();
    expect(component.initVariables).toHaveBeenCalled();
  });

  it('should build the form with expected controls', () => {
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('headline')).toBeFalse();
  });

  it('should set default values correctly', () => {
    expect(component.copyWithDates).toBeTrue();
    expect(component.colorTheme).toBe('theme-dark-blue');
    expect(component.isOnCopyAttempted).toBeFalse();
    expect(component.isBatchCopySucceed).toBeFalse();
    expect(component.loading).toBeFalse();
    expect(component.isDynamicOfferChecked).toBeFalse();
  });

  it('should check feature flag correctly', () => {
    // Already a spy from createSpyObj, so just set the return value
    featureFlagsService.isFeatureFlagEnabled.and.returnValue(true);

    expect(featureFlagsService.isFeatureFlagEnabled('someFeatureFlag')).toBeTrue();
    expect(featureFlagsService.isFeatureFlagEnabled).toHaveBeenCalledWith('someFeatureFlag');
  });


  it('should set default values correctly', () => {
    expect(component.copyWithDates).toBeTrue();
    expect()
    expect(component.colorTheme).toBe('theme-dark-blue');
    expect(component.isOnCopyAttempted).toBeFalse();
    expect(component.isBatchCopySucceed).toBeFalse();
    expect(component.loading).toBeFalse();
    expect(component.isDynamicOfferChecked).toBeFalse();
  });

  it('should initialize variables correctly', () => {
    component.initVariables();
    expect(component.isAllBatchSelected).toEqual('testValue');
    expect(component.selectedORIds).toEqual(['123', '456']);
    expect(component.isDynamicOfferChecked).toBeFalse();
  });

  it('should initialize the form with required controls', () => {
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('offerStartDate')).toBeTrue();
    expect(component.batchOfferRequestCopyForm.contains('offerEndDate')).toBeTrue();
  });

  it('should initialize the form with required controls', () => {
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('offerStartDate')).toBeTrue();
    expect(component.batchOfferRequestCopyForm.contains('offerEndDate')).toBeTrue();
  });

  it('should add additionalDetails control if isSCPcSelected is true', () => {
    spyOnProperty(component, 'isSCPcSelected', 'get').and.returnValue(true);
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('additionalDetails')).toBeTrue();
  });

  it('should add priceText, headline1, headline2, offerDescription if isSCPcSelected is false', () => {
    spyOnProperty(component, 'isSCPcSelected', 'get').and.returnValue(false);
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('priceText')).toBeTrue();
    expect(component.batchOfferRequestCopyForm.contains('headline1')).toBeTrue();
    expect(component.batchOfferRequestCopyForm.contains('headline2')).toBeTrue();
    expect(component.batchOfferRequestCopyForm.contains('offerDescription')).toBeTrue();
  });

  it('should add displayEndDate control if showDisplayEndDate is true', () => {
    spyOnProperty(component, 'showDisplayEndDate', 'get').and.returnValue(true);
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('displayEndDate')).toBeTrue();
  });

  it('should add isDynamicOffer and daysToRedeem controls if isDynamicOfferFeatureEnabled is true', () => {
    spyOnProperty(component, 'isDynamicOfferFeatureEnabled', 'get').and.returnValue(true);
    component.buildForm();
    expect(component.batchOfferRequestCopyForm.contains('isDynamicOffer')).toBeTrue();
    expect(component.batchOfferRequestCopyForm.contains('daysToRedeem')).toBeTrue();
  });

  it('should not duplicate form controls when buildForm is called multiple times', () => {
    component.buildForm();
    component.buildForm();
    expect(Object.keys(component.batchOfferRequestCopyForm.controls).length).toBeGreaterThanOrEqual(2);
  });

  it('should handle undefined properties safely', () => {
    spyOnProperty(component, 'isSCPcSelected', 'get').and.returnValue(undefined);
    spyOnProperty(component, 'showDisplayEndDate', 'get').and.returnValue(undefined);
    spyOnProperty(component, 'isDynamicOfferFeatureEnabled', 'get').and.returnValue(undefined);

    expect(() => component.buildForm()).not.toThrow();
  });

  it('should update the correct length for inputType', () => {
    component.onInput('TestValue', 'headline1');
    expect(component['headline1Length']).toBe(9);
  });

  it('should not throw error for invalid inputType', () => {
    expect(() => component.onInput('Test', '')).not.toThrow();
  });

  it('should set copyWithDates to true when copyType is "byDate"', () => {
    component.onChangeCopyTypeValue('byDate');
    expect(component.copyWithDates).toBeTrue();
  });

  it('should set copyWithDates to false when copyType is not "byDate"', () => {
    component.onChangeCopyTypeValue('manual');
    expect(component.copyWithDates).toBeFalse();
  });

  function getMockControl() {
    return new UntypedFormControl(null);
  }

  it('should add Validators.required when copyWithDates is true', () => {
    const mockControl = new UntypedFormControl(null);
    spyOn(component, 'getFormCtrl').and.returnValue(mockControl);

    component.onChangeCopyTypeValue('byDate');

    expect(mockControl.validator).toBeTruthy();
  });

  it('should clear validation and set values to null when copyWithDates is false', () => {
    spyOn(component, 'getFormCtrl').and.callFake(getMockControl);

    component.onChangeCopyTypeValue('manual');

    expect(component.getFormCtrl('offerStartDate')?.validator).toBeNull();
    expect(component.getFormCtrl('offerEndDate')?.validator).toBeNull();
    expect(component.getFormCtrl('displayEndDate')?.validator).toBeNull();
  });

  it('should return true if at least one date control is valid', () => {
    spyOn(component, 'getFormCtrl').and.callFake((ctrlName: string) => {
      return new FormControl('', { nonNullable: true, validators: [] });
    });

    component.getFormCtrl('offerStartDate')?.setValue('2025-01-01');
    component.getFormCtrl('offerStartDate')?.markAsTouched();
    component.getFormCtrl('offerStartDate')?.setErrors(null);

    expect(component.isAnyDatesValid).toBeTrue();
  });

  it('should return false if all date controls are invalid', () => {
    spyOn(component, 'getFormCtrl').and.callFake(() => {
      return new FormControl('', { nonNullable: true, validators: [] });
    });

    component.getFormCtrl('offerStartDate')?.setErrors({ required: true });
    component.getFormCtrl('offerEndDate')?.setErrors({ required: true });
    component.getFormCtrl('displayEndDate')?.setErrors({ required: true });

    expect(component.isAnyDatesValid).toBeTrue();
  });

  it('should return false if getFormCtrl returns undefined for all date controls', () => {
    spyOn(component, 'getFormCtrl').and.returnValue(undefined);

    expect(component.isAnyDatesValid).toBeFalse();
  });

  describe('checkIfUseUniversalJobForCopy', () => {

    it('should return false if checkUniversalJobFeatureFlag is false', () => {
      component.action = { checkUniversalJobFeatureFlag: false, universalJobFlagValue: 'someValue' };

      const result = component.checkIfUseUniversalJobForCopy();

      expect(result).toBeFalse();
    });

    it('should return false if universalJobFlagValue is not defined', () => {
      component.action = { checkUniversalJobFeatureFlag: true, universalJobFlagValue: null };

      const result = component.checkIfUseUniversalJobForCopy();

      expect(result).toBeFalse();
    });

    it('should return false when checkUniversalJobFeatureFlag is false', () => {
      component.action = {
        checkUniversalJobFeatureFlag: false,
        universalJobFlagValue: 'someValue',
      };

      const result = component.checkIfUseUniversalJobForCopy();

      expect(bulkUpdateService.checkIfActionEnabledForUniversalJob).not.toHaveBeenCalled();
      expect(result).toBeFalse();
    });

    it('should return false when universalJobFlagValue is falsy', () => {
      component.action = {
        checkUniversalJobFeatureFlag: true,
        universalJobFlagValue: null,
      };

      const result = component.checkIfUseUniversalJobForCopy();

      expect(bulkUpdateService.checkIfActionEnabledForUniversalJob).not.toHaveBeenCalled();
      expect(result).toBeFalse();
    });

  });

  describe("BatchOfferRequestCopy", () => {
    beforeEach(() => {
      component.batchOfferRequestCopyForm = new UntypedFormGroup({
        offerStartDate: new UntypedFormControl("2025-02-14"),
        offerEndDate: new UntypedFormControl("2025-03-01"),
        displayEndDate: new UntypedFormControl("2025-04-01"),
      });

      spyOn<any>(component, "getFormCtrl").and.callFake((ctrlName) => {
        return component.batchOfferRequestCopyForm.controls[ctrlName];
      });
    });

    it("should update date format correctly in updateDateFormatOnCopy", () => {
      // Act: Call the function
      component.updateDateFormatOnCopy();

      // Assert: Check if values are formatted correctly
      expect(component.batchOfferRequestCopyForm.value.offerStartDate).toEqual(dateInOriginalFormat({ date: "2025-02-14" }));
      expect(component.batchOfferRequestCopyForm.value.offerEndDate).toEqual(dateInOriginalFormat({ date: "2025-03-01" }));
      expect(component.batchOfferRequestCopyForm.value.displayEndDate).toEqual(dateInOriginalFormat({ date: "2025-04-01" }));
    });
  });
  
  describe("BatchOfferRequestCopy - checkIfUseUniversalJobForCopy", () => {
    it("should return false when checkUniversalJobFeatureFlag is false", () => {
      component.action.checkUniversalJobFeatureFlag = false;
      const result = component.checkIfUseUniversalJobForCopy();
      expect(result).toBeFalse();
      expect(bulkUpdateService.checkIfActionEnabledForUniversalJob).not.toHaveBeenCalled();
    });

    it("should return false when universalJobFlagValue is not provided", () => {
      component.action.universalJobFlagValue = null;
      const result = component.checkIfUseUniversalJobForCopy();
      expect(result).toBeFalse();
      expect(bulkUpdateService.checkIfActionEnabledForUniversalJob).not.toHaveBeenCalled();
    });
  });

  describe("BatchOfferRequestCopyComponent - setMinOfferEndDate", () => {
    it("should return if event is null or undefined", () => {
      component.minOfferEndDate = undefined;
      component.minDisplayEndDate = undefined;

      component.setMinOfferEndDate(null);

      expect(component.minOfferEndDate).toBeUndefined();
      expect(component.minDisplayEndDate).toBeUndefined();
    });

    it("should set minOfferEndDate to today if event is today or a future date", () => {
      const eventDate = new Date();
      component.setMinOfferEndDate(eventDate);

      expect(component.minOfferEndDate?.toDateString()).toBe(new Date().toDateString());
    });
    const normalizeDate = (date: Date) => {
      const normalized = new Date(date);
      normalized.setHours(0, 0, 0, 0);
      return normalized;
    };

    it("should set minOfferEndDate and minDisplayEndDate to event when event is past date", () => {
      const eventDate = new Date();
      component.setMinOfferEndDate(eventDate);
      expect(normalizeDate(component.minOfferEndDate).getTime()).toEqual(normalizeDate(eventDate).getTime());
      expect(normalizeDate(component.minDisplayEndDate).getTime()).toEqual(normalizeDate(eventDate).getTime());
    });    
    
  });

  describe('getPayloadQuery', () => {
    it('should create payloadQuery when selectAcrossAllPages is not selected and selectedORIds exist', () => {
      component.isAllBatchSelected = '';

      component.getPayloadQuery();

      expect(component.getPayloadObj).toHaveBeenCalledWith('requestId=(123 OR 456);', undefined);
    });

    it('should return null payloadQuery when selectedORIds is empty', () => {
      component.selectedORIds = null;

      component.getPayloadQuery();

      expect(component.getPayloadObj).toHaveBeenCalledWith('requestId=null', undefined);
    });
    it('should remove parameters and get query when selectAcrossAllPages is selected', () => {
      component.isAllBatchSelected = 'selectAcrossAllPages';

      (component as any).queryGenerator.getQuery.and.returnValue('mockQuery');
      (component as any).queryGenerator.getQueryWithFilter.and.returnValue('mockQueryWithFilters');

      component.getPayloadQuery();

      expect((component as any).queryGenerator.removeParameters).toHaveBeenCalledWith(['limit', 'next', 'sid', 'sortBy']);
      expect((component as any).queryGenerator.getQuery).toHaveBeenCalled();
      expect((component as any).queryGenerator.getQueryWithFilter).toHaveBeenCalled();
      expect(component.getPayloadObj).toHaveBeenCalledWith('mockQuery', 'mockQueryWithFilters');
    });

    it('should call getPayloadObjForSC when isSCPcSelected is true', () => {
      facetItemService.programCodeSelected = CONSTANTS.SC;
      component.isAllBatchSelected = 'selectAcrossAllPages';

      (component as any).queryGenerator.getQuery.and.returnValue('mockQuery');
      (component as any).queryGenerator.getQueryWithFilter.and.returnValue('mockQueryWithFilters');

      component.getPayloadQuery();

      expect(component.getPayloadObjForSC).toHaveBeenCalledWith('mockQuery', 'mockQueryWithFilters');
    });
  });

  describe('onClickCopy', () => {
    it('should not proceed with onClickCopy if form is invalid', () => {
      // Mark the form as invalid by setting an error on the form group.
      component.batchOfferRequestCopyForm.setErrors({ required: true });
      spyOn(component, 'updateDateFormatOnCopy');
      spyOn(component, 'getPayloadQuery');
      spyOn(component, 'checkIfUseUniversalJobForCopy');

      component.onClickCopy();

      expect(component.updateDateFormatOnCopy).not.toHaveBeenCalled();
      expect(component.getPayloadQuery).not.toHaveBeenCalled();
      expect(component.checkIfUseUniversalJobForCopy).not.toHaveBeenCalled();
    });

    it('should call doBatchCopySC and emit success when form is valid and no errors', () => {
      component.batchOfferRequestCopyForm.setValue({
        offerStartDate: '2025-02-14',
        offerEndDate: '2025-03-01',
        displayEndDate: '2025-04-01',
      });
      // Ensure the form is valid
      component.batchOfferRequestCopyForm.setErrors(null);
      component.isOnCopyAttempted = false;
      // Force errors getter to return null
      spyOnProperty(component, 'errors', 'get').and.returnValue(null);

      component.selectedORIds = ['123', '456'];
      facetItemService.programCodeSelected = CONSTANTS.SC;

      const mockResponse = { success: true };
      bulkUpdateService.doBatchCopySC.and.returnValue(of(mockResponse));

      spyOn(component.onBatchCopySucceed, 'emit');

      component.onClickCopy();

      // Since the subscription is synchronous with 'of', loading will be false after subscription completes.
      expect(component.isOnCopyAttempted).toBeTrue();
      expect(component.loading).toBeFalse();
      expect(bulkUpdateService.doBatchCopySC).toHaveBeenCalled();
      expect(component.onBatchCopySucceed.emit).toHaveBeenCalledWith(true);
      expect(toastrService.success).toHaveBeenCalledWith('Creating Copies', '', {
        timeOut: 3000,
        closeButton: true,
      });
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should call doBatchCopyBPDOR when universal job is enabled', () => {
      component.batchOfferRequestCopyForm.setValue({
        offerStartDate: '2025-02-14',
        offerEndDate: '2025-03-01',
        displayEndDate: '2025-04-01',
      });
      component.batchOfferRequestCopyForm.setErrors(null);
      component.isOnCopyAttempted = false;
      spyOnProperty(component, 'errors', 'get').and.returnValue(null);
      component.selectedORIds = ['123', '456'];
      facetItemService.programCodeSelected = 'BPD';
      bulkUpdateService.checkIfActionEnabledForUniversalJob.and.returnValue(true);

      const mockResponse = { success: true };
      bulkUpdateService.doBatchCopyBPDOR.and.returnValue(of(mockResponse));

      component.onClickCopy();

      expect(bulkUpdateService.doBatchCopyBPDOR).toHaveBeenCalled();
    });

    it('should not proceed if form is valid but errors are present', () => {
      // Even though the form values are set (and form is valid), we simulate validation errors
      // by forcing the errors getter to return a non-null error object.
      component.batchOfferRequestCopyForm.setValue({
        offerStartDate: '2025-02-14',
        offerEndDate: '2025-03-01',
        displayEndDate: '2025-04-01',
      });
      component.batchOfferRequestCopyForm.setErrors(null);
      spyOnProperty(component, 'errors', 'get').and.returnValue({ errorTxt: 'Some error' });

      spyOn(component.onBatchCopySucceed, 'emit');

      component.onClickCopy();

      expect(component.isOnCopyAttempted).toBeTrue();
      // Because errors are present, the copy process should not proceed.
      expect(component.loading).toBeFalse();
      expect(bulkUpdateService.doBatchCopySC).not.toHaveBeenCalled();
      expect(bulkUpdateService.doBatchCopyBPDOR).not.toHaveBeenCalled();
      expect(component.onBatchCopySucceed.emit).not.toHaveBeenCalled();
      expect(toastrService.success).not.toHaveBeenCalled();
      expect(modalRef.hide).not.toHaveBeenCalled();
    });

    it('should handle API error and hide modal', () => {
      component.batchOfferRequestCopyForm.setValue({
        offerStartDate: '2025-02-14',
        offerEndDate: '2025-03-01',
        displayEndDate: '2025-04-01',
      });
      component.batchOfferRequestCopyForm.setErrors(null);
      component.isOnCopyAttempted = false;
      spyOnProperty(component, 'errors', 'get').and.returnValue(null);
      component.selectedORIds = ['123', '456'];
      facetItemService.programCodeSelected = CONSTANTS.SC;

      bulkUpdateService.doBatchCopySC.and.returnValue(throwError('API Error'));

      component.onClickCopy();

      expect(component.loading).toBeFalse();
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should call updateDateFormatOnCopy during copy process', () => {
      spyOn(component, 'updateDateFormatOnCopy');
      component.batchOfferRequestCopyForm.setValue({
        offerStartDate: '2025-02-14',
        offerEndDate: '2025-03-01',
        displayEndDate: '2025-04-01',
      });
      component.batchOfferRequestCopyForm.setErrors(null);
      component.isOnCopyAttempted = false;
      spyOnProperty(component, 'errors', 'get').and.returnValue(null);
      component.selectedORIds = ['123', '456'];
      facetItemService.programCodeSelected = CONSTANTS.SC;

      const mockResponse = { success: true };
      bulkUpdateService.doBatchCopySC.and.returnValue(of(mockResponse));

      component.onClickCopy();

      expect(component.updateDateFormatOnCopy).toHaveBeenCalled();
    });
  });

  describe('validateDaysToRedeem', () => {
    beforeEach(() => {
      // Ensure our form has the required controls.
      component.batchOfferRequestCopyForm = new FormGroup({
        offerStartDate: new FormControl(null),
        offerEndDate: new FormControl(null),
        daysToRedeem: new FormControl(null),
      });
      // Override getFormCtrl to return controls from the form group.
      spyOn(component, 'getFormCtrl').and.callFake((ctrlName: string) => {
        return component.batchOfferRequestCopyForm.get(ctrlName);
      });
    });
    
    it('should return no error when dynamic offer is not enabled', () => {
      // Configure dependencies so that the getter returns false.
      featureFlagsService.isFeatureFlagEnabled.and.returnValue(false);
      // The facetItemService.programCodeSelected can be anything.
      
      component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
      component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-02-16'));
      component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('0'));
      
      const result = component.validateDaysToRedeem();
      expect(result).toEqual({ isError: false, message: '' });
    });
    
    describe('when dynamic offer is enabled, checked and copyWithDates is true', () => {
      beforeEach(() => {
        featureFlagsService.isFeatureFlagEnabled.and.returnValue(true);
        facetItemService.programCodeSelected = CONSTANTS.SPD; // Ensures getter returns true.
        component.isDynamicOfferChecked = true;
        component.copyWithDates = true;
      });

      it('should return error "Start Date and End Date are required" when offerEndDate is null', () => {
        component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
        component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl(null));
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('1'));
        const result = component.validateDaysToRedeem();
        expect(result).toEqual({ isError: true, message: 'Start Date and End Date are required' });
      });
      
      it('should return error "Days To Redeem should be atleast 1" when daysToRedeem is less than 1', () => {
        // Valid dates with a difference of 1 day.
        component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
        component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-02-15'));
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('0'));
        const result = component.validateDaysToRedeem();
        expect(result).toEqual({ isError: true, message: 'Days To Redeem should be atleast 1' });
      });
      
      it('should return error with appropriate message when daysToRedeem is greater than the computed difference', () => {
        // Difference = 2 days (Feb 14 to Feb 16)
        component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
        component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-02-16'));
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('3')); // 3 > 2
        const result = component.validateDaysToRedeem();
        expect(result.isError).toBeTrue();
        expect(result.message).toBe('Days To Redeem should be between 1 and 2');
      });
      
      it('should return error "Days To Redeem should be 1" when difference is 1 and daysToRedeem > 1', () => {
        // Difference = 1 day: Feb 14 to Feb 15.
        component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
        component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-02-15'));
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('2')); // 2 > 1
        const result = component.validateDaysToRedeem();
        expect(result.isError).toBeTrue();
        expect(result.message).toBe('Days To Redeem should be 1');
      });
      
      it('should return no error when dates and daysToRedeem are valid', () => {
        // Difference = 2 days; daysToRedeem = 1 is valid.
        component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
        component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-02-16'));
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('1'));
        const result = component.validateDaysToRedeem();
        expect(result).toEqual({ isError: false, message: '' });
      });
    });
    
    describe('when dynamic offer is enabled, checked and copyWithDates is false', () => {
      beforeEach(() => {
        featureFlagsService.isFeatureFlagEnabled.and.returnValue(true);
        facetItemService.programCodeSelected = CONSTANTS.SPD;
        component.isDynamicOfferChecked = true;
        component.copyWithDates = false;
      });
      
      it('should return error "Days To Redeem should be atleast 1" when daysToRedeem is less than 1', () => {
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('0'));
        const result = component.validateDaysToRedeem();
        expect(result).toEqual({ isError: true, message: 'Days To Redeem should be atleast 1' });
      });
      
      it('should return no error when daysToRedeem is valid (>= 1)', () => {
        component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('2'));
        const result = component.validateDaysToRedeem();
        expect(result).toEqual({ isError: false, message: '' });
      });
    });
  });
  
  describe('onDynamicCheckBoxClick', () => {
    it('should update isDynamicOfferChecked and reset daysToRedeem when checkbox is clicked', () => {
      component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('5'));

      const event = { checked: true };
      component.onDynamicCheckBoxClick(event);

      expect(component.isDynamicOfferChecked).toBe(true);
      expect(component.getFormCtrl("daysToRedeem").value).toBe('');
    });

    it('should update isDynamicOfferChecked to false and reset daysToRedeem when checkbox is unchecked', () => {
      component.batchOfferRequestCopyForm.setControl('daysToRedeem', new FormControl('5'));

      const event = { checked: false };
      component.onDynamicCheckBoxClick(event);

      expect(component.isDynamicOfferChecked).toBe(false);
      expect(component.getFormCtrl("daysToRedeem").value).toBe('');
    });

  });

  describe('validateEndDate', () => {
    it('should return false if displayEndDate is before offerEndDate', () => {
      component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-03-01'));
      component.batchOfferRequestCopyForm.setControl('displayEndDate', new FormControl('2025-02-28'));
      component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-01'));

      const result = component.validateEndDate();

      expect(result).toBeFalse();
    });

    it('should return true if offerStartDate is after displayEndDate', () => {
      component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-03-01'));
      component.batchOfferRequestCopyForm.setControl('displayEndDate', new FormControl('2025-02-28'));
      component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-03-02'));

      const result = component.validateEndDate();

      expect(result).toBeTrue();
    });

    it('should return false if dates are valid', () => {
      component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-03-01'));
      component.batchOfferRequestCopyForm.setControl('displayEndDate', new FormControl('2025-02-28'));
      component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-01'));

      const result = component.validateEndDate();

      expect(result).toBeFalse();
    });
  });

  describe('BatchRequestCopyComponent - errors getter', () => {
    let component: BatchOfferRequestCopy;
    let fixture: ComponentFixture<BatchOfferRequestCopy>;
    let facetItemService: jasmine.SpyObj<FacetItemService>;

    beforeEach(() => {
      fixture = TestBed.createComponent(BatchOfferRequestCopy);
      component = fixture.componentInstance;
      fixture.detectChanges();

      facetItemService = TestBed.inject(FacetItemService) as jasmine.SpyObj<FacetItemService>;
    
      // Initialize the form with some default values
      component.batchOfferRequestCopyForm = new FormGroup({
        offerStartDate: new FormControl('2025-02-14'),
        offerEndDate: new FormControl('2025-03-01'),
        displayEndDate: new FormControl('2025-04-01'),
      });

      component.isOnCopyAttempted = true;
    });

    it('should return error when validateDaysToRedeem returns an error', () => {
      spyOn(component, 'validateDaysToRedeem').and.returnValue({ isError: true, message: 'Start Date and End Date are required' });

      const errors = component.errors;
      expect(errors).toEqual({ errorTxt: 'Start Date and End Date are required' });
    });

    it('should return error "If you enter one date, then all dates must be entered." when form is valid but partially filled', () => {
      component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
      component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl(''));
      component.batchOfferRequestCopyForm.setControl('displayEndDate', new FormControl('2025-04-01'));

      // Make form invalid by having an incomplete date set
      component.batchOfferRequestCopyForm.setErrors({ invalid: true });

      const errors = component.errors;
      expect(errors).toEqual({ errorTxt: 'If you enter one date, then all dates must be entered.' });
    });

    it('should return error "Display end date should be between start and end date." when validateEndDate returns true', () => {
      spyOn(component, 'validateEndDate').and.returnValue(true);

      const errors = component.errors;
      expect(errors).toEqual({ errorTxt: 'Display end date should be between start and end date.' });
    });

    it('should return null when no errors are present', () => {
      // Simulate no error conditions
      spyOn(component, 'validateDaysToRedeem').and.returnValue({ isError: false, message: '' });

      // Set valid dates
      component.batchOfferRequestCopyForm.setControl('offerStartDate', new FormControl('2025-02-14'));
      component.batchOfferRequestCopyForm.setControl('offerEndDate', new FormControl('2025-03-01'));
      component.batchOfferRequestCopyForm.setControl('displayEndDate', new FormControl('2025-04-01'));
    
      // Ensure the form is valid
      component.batchOfferRequestCopyForm.setErrors(null);

      spyOn(component, 'validateEndDate').and.returnValue(false);

      const errors = component.errors;
      expect(errors).toBeNull();
    });
  });

});
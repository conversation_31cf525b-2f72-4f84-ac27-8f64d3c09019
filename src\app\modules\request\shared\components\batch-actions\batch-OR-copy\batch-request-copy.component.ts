import { Component, EventEmitter, Input, Output } from "@angular/core";
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { compareDates, dateInOriginalFormat } from "@appUtilities/date.utility";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import * as moment from 'moment';
import { BsModalRef } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
@Component({
  selector: "batch-request-copy",
  templateUrl: "./batch-request-copy.component.html",
  styleUrls: ["./batch-request-copy.component.scss"],
})
export class BatchOfferRequestCopy extends UnsubscribeAdapter {
  @Input() modalRef: BsModalRef;
  @Input('action') action;
  @Output() onBatchCopySucceed =  new EventEmitter<boolean>();
  batchOfferRequestCopyForm: UntypedFormGroup;
  copyWithDates = true;
  headline2Length;
  headlineLength;
  offerDescLength;
  priceTextLength;
  minOfferStartDate: Date = new Date();
  minOfferEndDate: Date = new Date();
  minDisplayEndDate: Date = new Date();
  colorTheme = "theme-dark-blue";
  isOnCopyAttempted: boolean = false;
  isAllBatchSelected: string;
  selectedORIds;
  isBatchCopySucceed: boolean = false;
  loading:boolean = false;
  isDynamicOfferChecked:boolean = false;
  constructor(
    private bulkUpateService: BulkUpdateService,
    private fb: UntypedFormBuilder,
    private queryGenerator: QueryGenerator,
    private facetItemService: FacetItemService,
    private featureFlagService: FeatureFlagsService,
    private commonSearchService:CommonSearchService,
    
    private _toastr: ToastrService 
  ) {
    super();
  }
  /**
   * 
   * This method is responsible for setting up form when component load / popup opened
   */
  ngOnInit(): void {
    this.buildForm();
    this.initVariables();
  }
  /**
   * This method is responsible to get requestIds selected or if all pages selected
   */
  initVariables() {
    this.bulkUpateService.isAllBatchSelected.subscribe((value) => {
      this.isAllBatchSelected = value;
    });
    this.selectedORIds = this.bulkUpateService.requestIdArr;
    this.isDynamicOfferChecked = false;          
  }
  /***
   * This method is resonsible for creating required copy form
   */
  buildForm() {
    this.batchOfferRequestCopyForm = this.fb.group({
      offerStartDate: [null, [Validators.required]],
      offerEndDate: [null, [Validators.required]],
    });
    if(this.isSCPcSelected) {
      this.batchOfferRequestCopyForm.addControl('additionalDetails',new UntypedFormControl(null));
    } else {
      ["priceText", "headline1", "headline2", "offerDescription"].forEach((ele)=> {
        this.batchOfferRequestCopyForm.addControl(ele,new UntypedFormControl(null));
      })
    }
    if(this.showDisplayEndDate) {
      this.batchOfferRequestCopyForm.addControl('displayEndDate',new UntypedFormControl(null, Validators.required));
    }
    if(this.isDynamicOfferFeatureEnabled)
    {
      this.batchOfferRequestCopyForm.addControl('isDynamicOffer',new UntypedFormControl(false));
      this.batchOfferRequestCopyForm.addControl('daysToRedeem',new UntypedFormControl(null));
    }
  }
  get showDisplayEndDate() {
    return this.isSCPcSelected ? this.bulkUpateService.showDisplayEndDate : true;
  }
  get isSCPcSelected() {
    return this.facetItemService.programCodeSelected === CONSTANTS.SC;
  }
  /**
   * 
   * @param value input string value for different inputs like priceText, headline1, headline2, offerDesc
   * @param inputType priceText, headline1, headline2, offerDesc
   * 
   * This method used to calculate length for some inputs to show warning on UI when they exceed the length
   */
  onInput(value, inputType) {
    this[`${inputType}Length`] = value?.length;
  }
  /**
   * 
   * @param copyType is either byDate or by+4weeks
   * 
   * This method is responsible to set/ clear validators on date fields when user change between byDates / +4weeks
   */
  onChangeCopyTypeValue(copyType) {
    this.isOnCopyAttempted = false;
    this.copyWithDates = copyType === "byDate";
    ["offerStartDate", "offerEndDate", "displayEndDate"].forEach((ctrlName) => {
      const frmCtrl = this.getFormCtrl(ctrlName);
      if (this.copyWithDates) {
        frmCtrl.setValidators(Validators.required);
      } else {
        frmCtrl.clearValidators();
        frmCtrl.setValue(null);
      }
      frmCtrl.updateValueAndValidity();
    });
  }
  /**
   * 
   * @param ctrlName get control name 
   * @returns form control for the input control name
   */
  getFormCtrl(ctrlName) {
    return ctrlName && this.batchOfferRequestCopyForm.get(ctrlName);
  }
  /**
   * this is to check if any dates entered is valid in form or not
   */
  get isAnyDatesValid() {
    return ["offerStartDate", "offerEndDate", "displayEndDate"].some(
      (ctrlName) => {
        return this.getFormCtrl(ctrlName)?.valid;
      }
    );
  }
  /**
   * On click copy if form is valid, need to set payload and make api call to create OR copies 
   */
  onClickCopy() { 
    this.isOnCopyAttempted = true;
    this.batchOfferRequestCopyForm.markAsUntouched();
    if (this.batchOfferRequestCopyForm?.valid && !this.errors) {
      this.loading = true;
      this.updateDateFormatOnCopy();
      const pcSelected = this.facetItemService.programCodeSelected;
      const payload = this.getPayloadQuery();
      const isUniversalJobEnabled = this.checkIfUseUniversalJobForCopy();
      const subscription = pcSelected === CONSTANTS.SC ? this.bulkUpateService.doBatchCopySC(payload) :
        ( isUniversalJobEnabled ? this.bulkUpateService.doBatchCopyBPDOR(payload) :  this.bulkUpateService.doBatchCopyOR(payload, pcSelected));
      subscription.subscribe((res: any)=> {
        if(res) {
          this.loading = false;
          this.isBatchCopySucceed = true;
          this.onBatchCopySucceed.emit(true);
          this.modalRef.hide();
          this._toastr.success("Creating Copies", "", {
            timeOut: 3000,
            closeButton: true,
          });
        }
      },
      (err)=> {
        this.loading = false;
        this.modalRef.hide();
      })
    }
  }
  checkIfUseUniversalJobForCopy() {
    const {  checkUniversalJobFeatureFlag, universalJobFlagValue } = this.action;
    if(checkUniversalJobFeatureFlag && universalJobFlagValue) {
      return this.bulkUpateService.checkIfActionEnabledForUniversalJob(universalJobFlagValue);
    }
    return false;
  }
  /**
   * while creating copy to send it to BE, need to change the date format t
   */
  updateDateFormatOnCopy() {
    ["offerStartDate", "offerEndDate", "displayEndDate"].forEach(
      (ctrlName) => {
        const dateValue = this.getFormCtrl(ctrlName)?.value;
        this.batchOfferRequestCopyForm.value[ctrlName] = dateValue ? dateInOriginalFormat({date: dateValue}) : null;
      }
    );
  }
  /**
   * 
   * @returns payload query required to send for api with selected OR ids or query when all page selected
   */
  getPayloadQuery() {
    let queryVal, queryWithOrFilters;
    if (this.isAllBatchSelected === 'selectAcrossAllPages') {
      this.queryGenerator.removeParameters(['limit', 'next', 'sid', 'sortBy']);
      queryVal = this.queryGenerator.getQuery();
      queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    } else {
      let payloadQuery = this.selectedORIds ? `(${this.selectedORIds.join(' OR ')});` : null;
      queryVal = `requestId=${payloadQuery}`;
    }
    return this.isSCPcSelected ? this.getPayloadObjForSC(queryVal, queryWithOrFilters) : this.getPayloadObj(queryVal, queryWithOrFilters);

  }
  getPayloadObj(query, queryWithOrFilters) {
    const checkIfUseUniversalJobForCopy = this.checkIfUseUniversalJobForCopy();
    let showExpired=this.commonSearchService.isShowExpiredInQuery;
    if(checkIfUseUniversalJobForCopy) {
      return {
        ...this.batchOfferRequestCopyForm.value, 
        addFourWeeks: !this.copyWithDates,
        searchQuery: queryWithOrFilters?.length ? (this.featureFlagService.isArchivalEnabled ? {query, queryWithOrFilters,showExpired} : {query, queryWithOrFilters}) 
        :  ( this.featureFlagService.isArchivalEnabled ? {query,showExpired} : {query}),
        jobSubType: "COPY",
        jobType: "OR",
        programCodeType: this.facetItemService.programCodeSelected
      }
    } else {
      return {
        copyOfferRequest: {...this.batchOfferRequestCopyForm.value, addFourWeeks: !this.copyWithDates }, 
        searchQuery: queryWithOrFilters?.length ? (this.featureFlagService.isArchivalEnabled ? {query, queryWithOrFilters,showExpired} : {query, queryWithOrFilters}) 
        :  ( this.featureFlagService.isArchivalEnabled ? {query,showExpired} : {query}),
        asyncActionDetails: [],
        asyncAction: "COPY",
      };
    }
  }
  getPayloadObjForSC(query, queryWithOrFilters) {
    let showExpired=this.commonSearchService.isShowExpiredInQuery;
    const {offerStartDate, offerEndDate, displayEndDate, additionalDetails } = this.batchOfferRequestCopyForm?.value;
    return {
      searchQuery: queryWithOrFilters?.length ? (this.featureFlagService.isArchivalEnabled ? {query, queryWithOrFilters,showExpired} : {query, queryWithOrFilters}) 
      :  ( this.featureFlagService.isArchivalEnabled ? {query,showExpired} : {query}),
      jobSubType: "COPY",
      jobType: "OR",
      programCodeType: this.facetItemService.programCodeSelected,
      programType: CONSTANTS.SC,
      additionalDescription: additionalDetails,    
      offerEffectiveStartDate: offerStartDate,
      offerEffectiveEndDate: offerEndDate,   
      displayEffectiveEndDate: displayEndDate
    }
  }  
  /**
   * 
   * @param event date value from calender selection
   * @returns On start date change need to set min end date or end date value when startdate > end date
   */
  setMinOfferEndDate(event) {
    if (!event) {
      return;
    }
    const endDteCtrl = this.getFormCtrl("offerEndDate");

    if (compareDates("today", event) <= 0) {
      this.minOfferEndDate = new Date();
      return;
    }
    if (endDteCtrl?.value && compareDates(endDteCtrl.value, event) > 0) {
      endDteCtrl.setValue(event);
    }
    this.minOfferEndDate = this.minDisplayEndDate = event;
  }
  /**
   * 
   * @returns If user select byDates and didn't enter or enter only one / two dates and is not valid, need to show error message
   */
  get errors() {
    if (this.isOnCopyAttempted && this.batchOfferRequestCopyForm?.untouched) {
      
      const {isError,message} = this.validateDaysToRedeem();
      if(isError)
      {
        return { errorTxt: message }
      }
      else if (!this.isAnyDatesValid) {
        return this.isSCPcSelected ? {errorTxt: "Please enter dates"} : {errorTxt: "You must either enter dates or select +4 Weeks."};
      } else if (this.isAnyDatesValid && !this.batchOfferRequestCopyForm.valid) {
        return {
          errorTxt: "If you enter one date, then all dates must be entered.",
        };
      } else if(this.validateEndDate()) {
         return {
          errorTxt: "Display end date should be between start and end date."
         }
      } 
      else {
        return null;
      }
    }
  }
  /**
   * 
   * @returns This function will validate if display end date is between start and end date
   */
  validateEndDate() {
    const endDteValue = this.getFormCtrl("offerEndDate")?.value;
    const displayEndDateValue = this.getFormCtrl("displayEndDate")?.value;
    const startDtvalue = this.getFormCtrl("offerStartDate")?.value;
    return compareDates(displayEndDateValue, endDteValue) <0 || compareDates(startDtvalue, displayEndDateValue) < 0;
  }
  ngOnDestroy() {
    if (this.isBatchCopySucceed) {
      this.bulkUpateService.requestIdsListSelected$.next([]);
      this.bulkUpateService.createdAppIds$.next([]);
      this.bulkUpateService.offerBulkSelection.next(null);
      this.bulkUpateService.isSelectionReset.next(true);
      this.bulkUpateService.requestIdArr = [];
      this.bulkUpateService.deliveryChannelArr = [];
    }
  }

  get isDynamicOfferFeatureEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled("enableDynamicOffers") && this.facetItemService.programCodeSelected === CONSTANTS.SPD;
  }

  get canShowDynamicCheckBox(){
    return this.isDynamicOfferFeatureEnabled; 
  }

  validateDaysToRedeem()
  {
    
    let offerStartDate = this.getFormCtrl("offerStartDate")?.value;
    let offerEndDate =  this.getFormCtrl("offerEndDate")?.value;
    let fdate = moment(new Date(offerStartDate));
    let edate = moment(new Date(offerEndDate));
    let daysToRedeem = edate.diff(fdate, 'days'); 
    // if(offerStartDate !== null && offerEndDate !== null)
    // {
    //   daysToRedeem += 1;
    // }
    let val = (Number(this.getFormCtrl("daysToRedeem")?.value));
    if(this.isDynamicOfferFeatureEnabled && this.isDynamicOfferChecked)
    {
      if(this.copyWithDates)
      {
        if(daysToRedeem < 1 && (offerStartDate == null || offerEndDate == null))
        {
          return {isError: true,message:'Start Date and End Date are required' };
        }
        else if((val < 1)) {
          return {isError: true,message:'Days To Redeem should be atleast 1' };
        }      
        else if(val > daysToRedeem)
        {
          let errmsg = 'Days To Redeem should be '
          errmsg = daysToRedeem == 1 ? errmsg + "1" : errmsg + `between 1 and ${daysToRedeem}`;
          return {isError: true,message:errmsg };
        }
      }
      else if(!this.copyWithDates)
      {
        if((val < 1)) {
          return {isError: true,message:'Days To Redeem should be atleast 1' };
        }      
      }    
    }
    return {isError: false,message:'' };
  }

  onDynamicCheckBoxClick(event:any)
  {
    this.isDynamicOfferChecked = event.checked;
    this.getFormCtrl("daysToRedeem").setValue(""); 
  }
}
<div class="container-fluid">
    <spinner *ngIf="loading"></spinner>
    <form class="pod-form" [formGroup]="batchBPDOfferRequestCopyForm">

        <div class="row">
            <div class="col-6">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small" for="title">Program Type</label>
                    </div>
                    <div class="col">
                        <select type="text"
                                markAsTouchedOnFocus
                                id="programType"
                                name="programType"
                                autocomplete="off"
                                formControlName="programType"
                                class="custom-select form-control">
                            <option *ngFor="let code of programTypeConfig | keyvalue" [value]="code.key">
                                {{ code.value }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small" for="title">Allocation Code</label>
                    </div>
                    <div class="col">
                        <select type="text"
                                markAsTouchedOnFocus
                                id="allocationCode"
                                name="allocationCode"
                                autocomplete="off"
                                formControlName="allocationCode"
                                class="custom-select form-control">
                            <option *ngFor="let code of allocationsData | keyvalue" [value]="code.key">
                                {{code.key}} - {{code.value}}
                            </option>
                            
                        </select>

                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small" for="title">Price Text</label>
                    </div>
                    <div class="col col-12">
                        <input type="text"
                               class="form-control"
                               markAsTouchedOnFocus
                               id="title"
                               name="title"
                               formControlName="priceText"
                               (input)="onInput($event.target.value, 'priceText')"
                               maxlength="25" />
                    </div>
                    <small class="text-danger col-12" *ngIf="priceTextLength >= 16">
                        Exceeds recommended characters. May not display correctly. ({{
              25 - priceTextLength
                        }}
                        characters left)
                    </small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small" for="title">Headline 1</label>
                    </div>
                    <div class="col">
                        <input type="text"
                               class="form-control"
                               id="headline1"
                               markAsTouchedOnFocus
                               name="headline1"
                               formControlName="headline1"
                               (input)="onInput($event.target.value, 'headline')"
                               maxlength="100" />
                        <small class="text-danger" *ngIf="headlineLength >= 90">
                            Exceeds recommended characters. May not display correctly. ({{
                100 - headlineLength
                            }}
                            characters left)
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small" for="title">Headline 2</label>
                    </div>
                    <div class="col">
                        <input type="text"
                               class="form-control"
                               id="headline2"
                               name="headline2"
                               markAsTouchedOnFocus
                               formControlName="headline2"
                               (input)="onInput($event.target.value, 'headline2')"
                               maxlength="100" />
                        <small class="text-danger" *ngIf="headline2Length >= 90">
                            Exceeds recommended characters. May not display correctly. ({{
                100 - headline2Length
                            }}
                            characters left)
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small"
                               for="prodDescription">Offer Description</label>
                    </div>

                    <div class="col col-12">
                        <textarea type="text"
                                  class="form-control"
                                  id="offerDescription"
                                  markAsTouchedOnFocus
                                  rows="4"
                                  name="offerDescription"
                                  formControlName="offerDescription"
                                  (input)="onInput($event.target.value, 'offerDesc')"
                                  maxlength="100">
            </textarea>
                        <small class="text-danger" *ngIf="offerDescLength >= 90">
                            Exceeds recommended characters. May not display correctly. ({{
                100 - offerDescLength
                            }}
                            characters left)
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col">
                <div class="form-group row">
                    <div class="clearfix col-12">
                        <label class="float-left font-weight-bold small"
                               for="additionalDescription">Additional Details</label>
                    </div>

                    <div class="col col-12">
                        <textarea type="text"
                                  class="form-control"
                                  id="additionalDescription"
                                  markAsTouchedOnFocus
                                  rows="4"
                                  name="additionalDescription"
                                  formControlName="additionalDescription"
                                  (input)="onInput($event.target.value, 'additionalDescription')"
                                  maxlength="100">
            </textarea>
                        <small class="text-danger" *ngIf="additionalDescriptionLength >= 90">
                            Exceeds recommended characters. May not display correctly. ({{
                100 - additionalDescriptionLength
                            }}
                            characters left)
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col d-flex mt-3 mb-3 justify-content-end">
            <label class="anchor-link-blue cursor-pointer mr-4 mb-0 align-self-center"
                   (click)="modalRef.hide()">
                <u>Cancel</u>
            </label>
            <button class="btn btn-primary font-weight-bolder submit-btn"
                    (click)="onClickCopy()">
                Copy
            </button>
        </div>
    </div>
</div>

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BpdBatchRequestCopyComponent } from './bpd-batch-request-copy.component';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { FormBuilder } from '@angular/forms';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { CommonService } from '@appServices/common/common.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { of, BehaviorSubject, throwError } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';

describe('BpdBatchRequestCopyComponent', () => {
    let component: BpdBatchRequestCopyComponent;
    let fixture: ComponentFixture<BpdBatchRequestCopyComponent>;
    let bulkUpdateServiceMock: jasmine.SpyObj<BulkUpdateService>;
    let facetItemServiceMock: jasmine.SpyObj<FacetItemService>;
    let commonServiceMock: jasmine.SpyObj<CommonService>;
    let initialDataServiceMock: jasmine.SpyObj<InitialDataService>;
    let toastrServiceMock: jasmine.SpyObj<ToastrService>;
    let baseInputSearchServiceMock: jasmine.SpyObj<BaseInputSearchService>;

    beforeEach(async () => {
        bulkUpdateServiceMock = jasmine.createSpyObj('BulkUpdateService', ['doBatchCopyBPDOR', 'isAllBatchSelected']);
        facetItemServiceMock = jasmine.createSpyObj('FacetItemService', ['programCodeSelected']);
        commonServiceMock = jasmine.createSpyObj('CommonService', ['getAllocationsData']);
        initialDataServiceMock = jasmine.createSpyObj('InitialDataService', ['getAppData']);
        toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success']);
        baseInputSearchServiceMock = jasmine.createSpyObj('BaseInputSearchService', ['removeParametersForTemplates', 'queryForInputAndFilter', 'queryWithOrFilter']);
    
        initialDataServiceMock.getAppData.and.returnValue({
            programTypeBPD: ['programType1', 'programType2'],
            // other fields if necessary
        });

        commonServiceMock.getAllocationsData.and.returnValue(of([{ id: '1', name: 'Test Allocation' }]));

        const isAllBatchSelectedSubject = new BehaviorSubject<string>('true');

        bulkUpdateServiceMock.isAllBatchSelected = isAllBatchSelectedSubject;

        bulkUpdateServiceMock.requestIdArr = ['1', '2', '3'];

        await TestBed.configureTestingModule({
            declarations: [BpdBatchRequestCopyComponent],
            providers: [
                { provide: BsModalRef, useValue: {} },
                { provide: ToastrService, useValue: toastrServiceMock },
                { provide: FormBuilder, useValue: new FormBuilder() },
                { provide: FacetItemService, useValue: facetItemServiceMock },
                { provide: BulkUpdateService, useValue: bulkUpdateServiceMock },
                { provide: QueryGenerator, useValue: {} },
                { provide: InitialDataService, useValue: initialDataServiceMock },
                { provide: CommonService, useValue: commonServiceMock },
                { provide: BaseInputSearchService, useValue: baseInputSearchServiceMock }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(BpdBatchRequestCopyComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should call ngOnInit and initialize variables', () => {
        spyOn(component, 'buildForm');
        spyOn(component, 'initVariables');
        component.ngOnInit();
    
        expect(component.initVariables).toHaveBeenCalled();
        expect(component.buildForm).toHaveBeenCalled();
    });

    it('should initialize isAllBatchSelected and selectedORIds correctly in initVariables', () => {
        component.initVariables();

        expect(component.isAllBatchSelected).toBe('true');

        expect(component.selectedORIds).toEqual(['1', '2', '3']);
    });
    
    it('should initialize the form with required controls and call setFormFieldsOptions', () => {
        // Arrange
        spyOn(component, 'setFormFieldsOptions');

        // Act
        component.buildForm();

        // Assert
        expect(component.batchBPDOfferRequestCopyForm).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['programType']).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['allocationCode']).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['additionalDescription']).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['priceText']).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['headline1']).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['headline2']).toBeDefined();
        expect(component.batchBPDOfferRequestCopyForm.controls['offerDescription']).toBeDefined();
        expect(component.setFormFieldsOptions).toHaveBeenCalled();
    });
    
    it('should return the correct form control based on ctrlName', () => {
        component.buildForm();

        const formCtrl = component.getFormCtrl('headline1');

        expect(formCtrl).toBeDefined();
        expect(formCtrl).toEqual(component.batchBPDOfferRequestCopyForm.controls['headline1']);
    });

    it('should return null if control name does not exist', () => {
        component.buildForm();

        const formCtrl = component.getFormCtrl('nonexistentControl');

        expect(formCtrl).toBeNull();
    });

    it('should return the correct form control for another valid control', () => {
        component.buildForm();

        const formCtrl = component.getFormCtrl('priceText');

        expect(formCtrl).toBeDefined();
        expect(formCtrl).toEqual(component.batchBPDOfferRequestCopyForm.controls['priceText']);
    });

    it('should update the correct length property based on inputType and value', () => {
        const inputType = 'headline1';
        const inputValue = 'Test headline value';

        component.onInput(inputValue, inputType);

        expect(component.headline2Length).toBeUndefined()

        expect(component.headline2Length).toBeUndefined();
        expect(component.offerDescLength).toBeUndefined();
    });

    it('should clear bulk update service properties on destroy if isBatchCopySucceed is true', () => {
        component.isBatchCopySucceed = true;

        bulkUpdateServiceMock.requestIdsListSelected$ = new BehaviorSubject<any[]>(['dummy']);
        const requestIdsNextSpy = spyOn(bulkUpdateServiceMock.requestIdsListSelected$, 'next').and.callThrough();

        bulkUpdateServiceMock.createdAppIds$ = new BehaviorSubject<any[]>(['dummy']);
        const createdAppIdsNextSpy = spyOn(bulkUpdateServiceMock.createdAppIds$, 'next').and.callThrough();

        bulkUpdateServiceMock.offerBulkSelection = new BehaviorSubject<any>('dummy');
        const offerBulkSelectionNextSpy = spyOn(bulkUpdateServiceMock.offerBulkSelection, 'next').and.callThrough();

        bulkUpdateServiceMock.isSelectionReset = new BehaviorSubject<any>(false);
        const isSelectionResetNextSpy = spyOn(bulkUpdateServiceMock.isSelectionReset, 'next').and.callThrough();

        bulkUpdateServiceMock.requestIdArr = ['1', '2', '3'];

        component.ngOnDestroy();

        expect(requestIdsNextSpy).toHaveBeenCalledWith([]);
        expect(createdAppIdsNextSpy).toHaveBeenCalledWith([]);
        expect(offerBulkSelectionNextSpy).toHaveBeenCalledWith(null);
        expect(isSelectionResetNextSpy).toHaveBeenCalledWith(true);
        expect(bulkUpdateServiceMock.requestIdArr).toEqual([]);
    });

    describe('getPayloadQuery', () => {
        beforeEach(() => {
            component.buildForm();

            component.batchBPDOfferRequestCopyForm.controls['headline2'].setValue('headline2Val');
            component.batchBPDOfferRequestCopyForm.controls['headline1'].setValue('headline1Val');
            component.batchBPDOfferRequestCopyForm.controls['programType'].setValue('programTypeVal');
            component.batchBPDOfferRequestCopyForm.controls['allocationCode'].setValue('allocationCodeVal');
            component.batchBPDOfferRequestCopyForm.controls['priceText'].setValue('priceTextVal');
            component.batchBPDOfferRequestCopyForm.controls['offerDescription'].setValue('offerDescriptionVal');
            component.batchBPDOfferRequestCopyForm.controls['additionalDescription'].setValue('additionalDescriptionVal');

            component.allocationsData = { allocationCodeVal: 'allocationCodeNameVal' };

            facetItemServiceMock.programCodeSelected = 'programCodeTest';
        });

        it('should return payload using baseInputSearchService when isAllBatchSelected equals "selectAcrossAllPages"', () => {
            component.isAllBatchSelected = 'selectAcrossAllPages';

            baseInputSearchServiceMock.removeParametersForTemplates.and.callFake(() => { });

            Object.defineProperty(baseInputSearchServiceMock, 'queryForInputAndFilter', {
                value: 'baseQueryVal',
                writable: false,
                configurable: true
            });

            Object.defineProperty(baseInputSearchServiceMock, 'queryWithOrFilter', {
                value: ['orFilter1', 'orFilter2'],
                writable: false,
                configurable: true
            });

            const isTemplateFlag = `${CONSTANTS.IS_OFFER_TEMPLATE}=false;`;
            const expectedQueryVal = {
                query: `baseQueryVal${isTemplateFlag}`,
                queryWithOrFilters: ['orFilter1', 'orFilter2']
            };

            const payload = component.getPayloadQuery();

            expect(payload).toEqual(jasmine.objectContaining({
                searchQuery: expectedQueryVal,
                asyncActionDetails: [],
                jobSubType: 'COPY',
                jobType: 'OR',
                programCodeType: 'programCodeTest',
                headline2: 'headline2Val',
                headline1: 'headline1Val',
                programType: 'programTypeVal',
                allocationCode: 'allocationCodeVal',
                allocationCodeName: 'allocationCodeNameVal',
                priceText: 'priceTextVal',
                offerDescription: 'offerDescriptionVal',
                additionalDescription: 'additionalDescriptionVal'
            }));

            expect(baseInputSearchServiceMock.removeParametersForTemplates).toHaveBeenCalled();
        });

        it('should return payload using selectedORIds when isAllBatchSelected is not "selectAcrossAllPages"', () => {
            component.isAllBatchSelected = 'true';
            component.selectedORIds = ['1', '2', '3'];

            const isTemplateFlag = `${CONSTANTS.IS_OFFER_TEMPLATE}=false;`;
            const payloadQuery = `(${component.selectedORIds.join(' OR ')});`;
            const expectedQueryVal = { query: `requestId=${payloadQuery}${isTemplateFlag}` };

            const payload = component.getPayloadQuery();

            expect(payload.searchQuery).toEqual(expectedQueryVal);
        });
    });

    describe('Onclickcopy', () => {
        let bsModalRefSpy: jasmine.SpyObj<BsModalRef>;

        beforeEach(() => {
            bsModalRefSpy = jasmine.createSpyObj('BsModalRef', ['hide']);
            component.modalRef = bsModalRefSpy;
            (bulkUpdateServiceMock as any).requestIdsListSelected$ = new BehaviorSubject<any[]>([]);
            (bulkUpdateServiceMock as any).createdAppIds$ = new BehaviorSubject<any[]>([]);
            (bulkUpdateServiceMock as any).offerBulkSelection = new BehaviorSubject<any>(null);
            (bulkUpdateServiceMock as any).isSelectionReset = new BehaviorSubject<any>(false);
        });

        afterEach(() => {
            fixture.destroy();
        });

        it('should call doBatchCopyBPDOR and handle success', () => {
            spyOnProperty(component.batchBPDOfferRequestCopyForm, 'valid', 'get').and.returnValue(true);
            const payload = component.getPayloadQuery();
            spyOn(component, 'getPayloadQuery').and.returnValue(payload);

            bulkUpdateServiceMock.doBatchCopyBPDOR.and.returnValue(of({}));

            spyOn(component.onBatchCopySucceed, 'emit');

            component.onClickCopy();

            expect(component.isOnCopyAttempted).toBeTrue();
            expect(component.loading).toBeFalse();
            expect(component.isBatchCopySucceed).toBeTrue();
            expect(component.onBatchCopySucceed.emit).toHaveBeenCalledWith(true);
            expect(bsModalRefSpy.hide).toHaveBeenCalled();
            expect(toastrServiceMock.success).toHaveBeenCalledWith("Creating Copies", "", {
                timeOut: 3000,
                closeButton: true,
            });
        });

        it('should handle error on doBatchCopyBPDOR and hide modal', () => {
            spyOnProperty(component.batchBPDOfferRequestCopyForm, 'valid', 'get').and.returnValue(true);
            const payload = component.getPayloadQuery();
            spyOn(component, 'getPayloadQuery').and.returnValue(payload);
            bulkUpdateServiceMock.doBatchCopyBPDOR.and.returnValue(throwError(() => new Error('error')));

            component.onClickCopy();

            expect(component.loading).toBeFalse();
            expect(bsModalRefSpy.hide).toHaveBeenCalled();
        });
    });

});

import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'bpd-batch-request-copy',
  templateUrl: './bpd-batch-request-copy.component.html'
})
export class BpdBatchRequestCopyComponent implements OnInit {
  @Input() modalRef: BsModalRef;
  @Output() onBatchCopySucceed = new EventEmitter<boolean>();
  batchBPDOfferRequestCopyForm: UntypedFormGroup;

    headline2Length;
    headlineLength;
    offerDescLength;
    priceTextLength;
    additionalDescriptionLength;

    colorTheme = "theme-dark-blue";

    isOnCopyAttempted: boolean = false;
    isAllBatchSelected: string;
    selectedORIds;
    isBatchCopySucceed: boolean = false;
    loading: boolean = false;

    appData: any;
    programTypeConfig = [];
    allocationsData: any;


    constructor(private facetItemService: FacetItemService,
        private _toastr: ToastrService,
        private bulkUpateService: BulkUpdateService,
        private fb: UntypedFormBuilder,
        private queryGenerator: QueryGenerator,
        private initialDataService: InitialDataService, private commonService: CommonService,
        private baseInputSearchService: BaseInputSearchService) {
            // intentionally left empty
         }

    ngOnInit(): void {
        this.buildForm();
        this.initVariables();
        
        
    }
    initVariables() {
        this.bulkUpateService.isAllBatchSelected.subscribe((value) => {
            this.isAllBatchSelected = value;
        });
        this.selectedORIds = this.bulkUpateService.requestIdArr;
    }

    buildForm() {
        this.batchBPDOfferRequestCopyForm = this.fb.group({
            programType: [null, []],
            allocationCode: [null, []],
            additionalDescription: [null, []],
            priceText: [null, []],
            headline1: [null, []],
            headline2: [null, []],
            offerDescription: [null, []],
        })
        this.setFormFieldsOptions()
    }
    onInput(value, inputType) {
        this[`${inputType}Length`] = value?.length;
    }

    getFormCtrl(ctrlName) {
        return ctrlName && this.batchBPDOfferRequestCopyForm.get(ctrlName);
    }
    onClickCopy() {
        this.isOnCopyAttempted = true;
        this.batchBPDOfferRequestCopyForm.markAsUntouched();
        if (this.batchBPDOfferRequestCopyForm?.valid) {
            this.loading = true;
            
            const payload = this.getPayloadQuery();
            this.bulkUpateService.doBatchCopyBPDOR(payload).subscribe((res: any) => {
                if (res) {
                    this.loading = false;
                    this.isBatchCopySucceed = true;
                    this.onBatchCopySucceed.emit(true);
                    this.modalRef.hide();
                    this._toastr.success("Creating Copies", "", {
                        timeOut: 3000,
                        closeButton: true,
                    });
                }
            },
                (err) => {
                    this.loading = false;
                    this.modalRef.hide();
                })
        }
    }

    getPayloadQuery() {
        
        let queryVal;
        const isTemplateFlag = `${CONSTANTS.IS_OFFER_TEMPLATE}=false;`
        if (this.isAllBatchSelected === 'selectAcrossAllPages') {
            this.baseInputSearchService?.removeParametersForTemplates();
            let searchQuery = this.baseInputSearchService?.queryForInputAndFilter;
            const queryWithOrFilters = this.baseInputSearchService?.queryWithOrFilter;
            queryVal = queryWithOrFilters?.length ? { query: searchQuery, queryWithOrFilters } : { query: searchQuery };
        } else {
            let payloadQuery = this.selectedORIds ? `(${this.selectedORIds.join(' OR ')});` : null;
            queryVal = {query:`requestId=${payloadQuery}` } ;
        }
        queryVal = {...queryVal, query: `${queryVal?.query}${isTemplateFlag}`}
        return {
            //copyBPDOfferRequest: { ...this.batchBPDOfferRequestCopyForm.value},
            searchQuery: queryVal,
            asyncActionDetails: [],
            jobSubType: "COPY",
            jobType: "OR",
            programCodeType: this.facetItemService.programCodeSelected,
            headline2: this.getFormCtrl('headline2').value,
            headline1: this.getFormCtrl('headline1').value,
            programType: this.getFormCtrl('programType').value,
            allocationCode: this.getFormCtrl('allocationCode').value,
            allocationCodeName: this.allocationsData?.[this.getFormCtrl('allocationCode').value],
            priceText: this.getFormCtrl('priceText').value,
            offerDescription: this.getFormCtrl('offerDescription').value,
            additionalDescription: this.getFormCtrl('additionalDescription').value

           
        };
    }
    
    setFormFieldsOptions() {
        this.appData = this.initialDataService.getAppData();
        this.programTypeConfig = this.appData.programTypeBPD;
        this.getAllocationsAPI()
    }
    getAllocationsAPI() {
        this.commonService.getAllocationsData().subscribe(data => {
            this.allocationsData = data
        })
    }

    ngOnDestroy() {
        if (this.isBatchCopySucceed) {
            this.bulkUpateService.requestIdsListSelected$.next([]);
            this.bulkUpateService.createdAppIds$.next([]);
            this.bulkUpateService.offerBulkSelection.next(null);
            this.bulkUpateService.isSelectionReset.next(true);
            this.bulkUpateService.requestIdArr = [];
        }
    }
    
}

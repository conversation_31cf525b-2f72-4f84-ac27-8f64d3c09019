import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BulkExpandModalComponent } from './bulk-expand-modal.component';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { By } from '@angular/platform-browser';

describe('BulkExpandModalComponent', () => {
  let component: BulkExpandModalComponent;
  let fixture: ComponentFixture<BulkExpandModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BulkExpandModalComponent],
      providers: [BsModalRef]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BulkExpandModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize class properties correctly', () => {
    expect(component.confirmationMsg).toBeUndefined();
    expect(component.modalRef).toBeUndefined();
  });

  it('should emit isExpandPeriodAttempted event on onExpandPeriod call', () => {
    spyOn(component.isExpandPeriodAttempted, 'emit');
    component.onExpandPeriod();
    expect(component.isExpandPeriodAttempted.emit).toHaveBeenCalledWith(true);
  });

  it('should handle undefined or null inputs correctly', () => {
    component.confirmationMsg = null;
    component.modalRef = null;
    fixture.detectChanges();
    expect(component.confirmationMsg).toBeNull();
    expect(component.modalRef).toBeNull();
  });

  it('should handle errors in onExpandPeriod method gracefully', () => {
    spyOn(component.isExpandPeriodAttempted, 'emit').and.throwError('Error');
    expect(() => component.onExpandPeriod()).toThrowError('Error');
  });
});
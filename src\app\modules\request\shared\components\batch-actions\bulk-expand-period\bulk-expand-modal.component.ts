import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: "bulk-expand-modal",
  templateUrl: './bulk-expand-modal.component.html',
  styleUrls: ['./bulk-expand-modal.component.scss'],

})
export class BulkExpandModalComponent  {

    @Input() confirmationMsg: string;
    @Input() modalRef: BsModalRef;
    @Output() isExpandPeriodAttempted = new EventEmitter<boolean>();

    onExpandPeriod() {
      this.isExpandPeriodAttempted.emit(true);
    }
}
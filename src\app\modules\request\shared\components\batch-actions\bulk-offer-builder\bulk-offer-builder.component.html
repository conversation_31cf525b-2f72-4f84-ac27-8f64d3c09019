<section class="offer-request-container mb-5" [formGroup]="builderForm">
  <nav class="navbar background-header mb-6">
    <span class="font-weight-bold">
      Offer Builder Information
    </span>
  </nav>
  <div class="fields-container">
    <div>
      <div class="row mb-6 mx-1 justify-content-left">
        <div class="col-5" *ngIf="isNonDigital">
          <label class="d-block font-weight-bold" for="offerLimits"
            >Non-Digital Builder</label
          >
          <div *ngIf="!nonDigitalStatus" class="position-relative">
            <ng-select
              [items]="nonDigitalArr"
              [multiple]="false"
              [typeahead]="typedNonDigital$"
              formControlName="nonDigital"
              [tooltip]="setTooltipValue('nonDigital')"
              clearAllText="Clear"
              (change)="onAddingUser($event, 'ND')"
              class="mt--2"
              *permissionsOnly="assignNonDigitalUsers"
            >
              <ng-template ng-header-tmp>
                <div
                  class="cursor-pointer"
                  (click)="unAssignUser('non-Digital')"
                >
                  <span class="remove-assign userunassign"></span>Remove
                  Assignment
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div *ngIf="nonDigitalStatus" class="position-relative">
            {{nonDigitalStatus}}
          </div>
        </div>
        <div
          class="col-5"
          [ngClass]="isNonDigital ? 'offset-2' : ''"
          *ngIf="isDigital"
        >
          <label class="d-block font-weight-bold" for="offerLimits"
            >Digital Builder</label
          >
          <div  *ngIf="!digitalStatus" class="position-relative">
            <ng-select
              [items]="digitalArr"
              [multiple]="false"
              [typeahead]="typedDigital$"
              formControlName="digital"
              (change)="onAddingUser($event, 'DG')"
              [tooltip]="setTooltipValue('digital')"
              class="mt--2"
              *permissionsOnly="assignDigitalUsers"
            >
              <ng-template ng-header-tmp>
                <div class="cursor-pointer" (click)="unAssignUser('Digital')">
                  <span class="remove-assign userunassign"></span>Remove
                  Assignment
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div *ngIf="digitalStatus" class="position-relative">
            {{digitalStatus}}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<div class="row text-danger ml-0 mr-0 assign-error" *ngIf="showErrors">
  {{ showErrors }}
</div>
<div class="row ml-0 mr-0 text-success assign-error" *ngIf="showSuccessErrors">
  {{ showSuccessErrors }}
</div>
<div class="row">
  <div class="col d-flex mt-3 mb-3 justify-content-end">
    <button class="btn btn-link request-cancel mr-4" (click)="exit($event)">
      Cancel
    </button>
    <button
      class="btn btn-primary font-weight-bolder submit-btn"
      (click)="onSubmit()"
    >
      Save
    </button>
  </div>
</div>

<ng-template #successTmpl>
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <div class="row">
        <button
          type="button"
          class="close pull-right"
          aria-label="Close"
          (click)="onSuccessHandler()"
        >
          <span class="font-weight-lighter close-icon" aria-hidden="true"
            >&times;</span
          >
        </button>
      </div>
    </div>
  </div>
  <div class="modal-body pt-0 pr-5 pl-5">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <div class="col row justify-content-center">
            <h2 class="success-text">{{ showSuccessErrors }}</h2>
          </div>
          <div class="col row justify-content-center mt-4">
            <button
              type="button"
              class="btn btn-primary font-weight-bolder submit-btn"
              aria-label="Close"
              (click)="onSuccessHandler()"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

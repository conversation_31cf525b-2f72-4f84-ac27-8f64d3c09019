@import "scss/theme-builder.scss";
:host ::ng-deep {
  @import "scss/_mixins";
  @import "scss/variables";
  @import "scss/_colors";
  @import "scss/cbSwitch.scss";
  @import "scss/tagSelect.scss";
  //@import "../../../../node_modules/albertsons-theme-builder/dist/assets/styles/themes/bootstrap-theme-blue.css";
  .offer-request-container {
    border: 1px solid #cdd2d6;
  }
  .background-header {
    background-color: #cdd2d6;
    color: black;
    font-weight: bold;
  }
  .success-text {
    font-size: $header-font-size;

    line-height: 32px;
  }
  .save-options {
    background-color: $grey-hover-state;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 3px 3px;
    outline: none;
    padding: 3px;
    .submit:hover,
    .cancel:hover {
      opacity: 0.3;
    }
    &:not(.show-button-bar) {
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 1;
      box-shadow: 0 3px 6px rgba(111, 111, 111, 0.2);
      .aui-button {
        & + .aui-button {
          margin-left: 3px;
        }
      }
    }
    .aui-button {
      .aui-iconfont-success {
        color: #707070;
        &:before {
          font-size: 14px;
          left: 1px;
          margin-top: -7px;
        }
      }
      .aui-iconfont-close-dialog {
        color: #707070;
      }
    }
  }
  .aui-button {
    color: #333;
    & ~ .aui-button {
      margin-left: 10px;
    }
    .aui-icon {
      color: #707070;
    }
  }
  .aui-button,
  a.aui-button,
  .aui-button:visited {
    box-sizing: border-box;
    background: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 3.01px;
    color: #333;
    cursor: pointer;
    display: inline-block;
    font-family: inherit;
    font-size: 14px;
    font-variant: normal;
    font-weight: normal;
    height: 2.14285714em;
    line-height: 1.42857143;
    margin: 0;
    padding: 4px 10px;
    text-decoration: none;
    vertical-align: baseline;
    white-space: nowrap;
  }
  .aui-icon-small {
    height: 16px;
    width: 16px;
    &:before {
      font-size: 16px;
      margin-top: -8px;
    }
  }
  .aui-icon-small,
  .aui-icon-large {
    line-height: 0;
    position: relative;
    vertical-align: text-top;
  }
  .aui-icon {
    background-repeat: no-repeat;
    background-position: 0 0;
    border: none;
    display: inline-block;
    height: 16px;
    margin: 0;
    padding: 0;
    text-align: left;
    vertical-align: text-bottom;
    width: 16px;
  }
  .aui-icon-small:before,
  .aui-icon-large:before {
    left: 0;
    line-height: 1;
    text-indent: 0;
    top: 50%;
    color: inherit;
    font-weight: normal;
    -webkit-font-smoothing: antialiased;
    font-style: normal;
    position: absolute;
  }
  .save-options:not(.show-button-bar) .aui-button,
  .save-options:not(.show-button-bar) .aui-button.cancel {
    height: 24px;
    padding: 0 4px;
  }
  form {
    &.aui {
      .cancel {
        cursor: pointer;
        font-size: 14px;
        display: inline-block;
        padding: 5px 10px;
        vertical-align: baseline;
      }
    }
  }
}
.submit-btn {
  width: 12rem;
}
.navbar > span {
  font-size: 18px;
}

.remove-assign {
  background: url(data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZD0iTTMgM2wxOCAxOE0zIDIxTDIxIDMiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiLz48L3N5bWJvbD4NCiAgICAgICAgPC9kZWZzPg0KICAgICAgICA8Zz4NCiAgICAgICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI2Fzc2V0Ii8+DQogICAgICAgIDwvZz4NCiAgICA8L3N2Zz4=) no-repeat center;
  background-size: 55%;
  border-radius: 10px;
  border: solid 1px #ccc;
  display: inline-block;
  height: 20px;
  margin-right: 20px;
  width: 20px;
}
.userunassign {
  margin: 0px 5px -5px 0px;
}
.assign-error {
  padding: 10px;
}

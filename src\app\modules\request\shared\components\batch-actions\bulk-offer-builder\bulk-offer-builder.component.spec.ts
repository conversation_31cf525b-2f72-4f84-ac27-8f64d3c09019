import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { PermissionsConfigurationService, PermissionsModule, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject, Subject, of } from 'rxjs';
import { BulkOfferBuilderComponent } from './bulk-offer-builder.component';


describe('BulkOfferBuilderComponent', () => {
  let component: BulkOfferBuilderComponent;
  let fixture: ComponentFixture<BulkOfferBuilderComponent>;
  beforeEach(() => {
    const searchUsersServiceStub = () => ({ getUsers: term => ({}), getDigitalBuilders: term => ({}), getNonDigitalBuilders: term => ({}) });
    const initialDataServiceStub = () => ({});
    const searchOfferRequestServiceStub = () => ({
      searchAllOfferRequest: () => ({}),
      getOfferDetails: () => ({})

    });
    const bsModalServiceStub = () => ({
      show: (template, options) => ({}),
      onHide: {
        subscribe: () => ("show")
      },
      hide: () => ('')
    });
    const queryGeneratorStub = () => ({
      getQueryWithFilter: () => ({ length: {} }),
      getQuery: () => (''),
      removeParam: string => ({}),
      setQuery: originalQuery => ({})
    });
    const bulkUpdateServiceStub = () => ({
      requestIdsListSelected$: { subscribe: f => f({}) , next: f => ({}) },
      isAllBatchSelected: { subscribe: f => f({}),  next: f => ({})  },
      hideApiErrorOnRequestHome$: { next: f => ({}) },
      offerBulkSelection: { next: f => ({}) },
      isSelectionReset: { next: f => ({}) },
      bulkAssignedUsers: {
        "1416083724": {
          "digitalUser": {
            "userId": "scomm06",
            "firstName": "Srinivasa",
            "lastName": "Commuri"
          },
          "nonDigitalUser": null
        }
      },
      userTypeArray: [],
      requestIdArr: [],
      bulkUnAssignUsers: (userType, query) => ({ subscribe: f => f({}) }),
      bulkAssignUsers: (usersList, query) => ({ subscribe: f => f({}) }),
      bulkAssignUsersUJ:(usersList, query,jobType,jobSubType,programCodeType) => ({ subscribe: f => f({}) }),
      bulkUnAssignUsersUJ:(userType, query,jobType,jobSubType,programCodeType) => ({ subscribe: f => f({}) }),
      checkIfActionEnabledForUniversalJob:(string)=> ({ subscribe: f => f({}) })
    });
    const toastrServiceStub = () => ({
      success: () => ({}),
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      imports: [
        PermissionsModule.forRoot({ permissionsIsolate: true, configurationIsolate: true, rolesIsolate: true })
      ],
      declarations: [BulkOfferBuilderComponent],
      providers: [
        PermissionsService,
        PermissionsConfigurationService,
        { provide: SearchUsersService, useFactory: searchUsersServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: BsModalService, useFactory: bsModalServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: SearchOfferRequestService, useFactory: searchOfferRequestServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
      ]
    });
    fixture = TestBed.createComponent(BulkOfferBuilderComponent);
    component = fixture.componentInstance;
  });
  it('can load instance', () => {
    expect(component).toBeTruthy();
  });
  it(`reqIdList has default value`, () => {
    expect(component.reqIdList.length).toEqual(0);
  });
  it(`nonDigitalArr has default value`, () => {
    expect(component.nonDigitalArr.length).toEqual(0);
  });
  it(`digitalArr has default value`, () => {
    expect(component.digitalArr.length).toEqual(0);
  });
  it(`digitalUserDetails has default value`, () => {
    expect(component.digitalUserDetails.length).toEqual(0);
  });
  it(`nonDigitalUserDetails has default value`, () => {
    expect(component.nonDigitalUserDetails.length).toEqual(0);
  });
  it(`isDigital has default value`, () => {
    expect(component.isDigital).toEqual(false);
  });
  it(`isNonDigital has default value`, () => {
    expect(component.isNonDigital).toEqual(false);
  });
  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      spyOn(component, 'initTypeAhead');
      spyOn(component, 'isDisplayDigital');
      spyOn(component, 'isDisplayNonDigital');
      component.ngOnInit();
      expect(component.initTypeAhead).toHaveBeenCalled();
      expect(component.isDisplayDigital).toHaveBeenCalled();
      expect(component.isDisplayNonDigital).toHaveBeenCalled();
    });

  });
  describe('requestIdsListSelected', () => {
    it('makes expected calls', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([1416083724]);
      // component.reqIdList = [1416083724];
      bulkUpdateServiceStub.bulkAssignedUsers = {
        "1416083724": {
          "digitalUser": {
            "userId": "scomm06",
            "firstName": "Srinivasa",
            "lastName": "Commuri"
          },
          "nonDigitalUser": null
        }
      };
      bulkUpdateServiceStub.userTypeArray = ["PO"]
      component.ngOnInit();
      expect(component.digitalValue).toEqual('scomm06');

    });
    it('makes expected calls', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([1416083724]);
      // component.reqIdList = [1416083724];
      bulkUpdateServiceStub.bulkAssignedUsers = {
        "1416083724": {
          "nonDigitalUser": {
            "userId": "scomm06",
            "firstName": "Srinivasa",
            "lastName": "Commuri"
          },
          "digitalUser": null
        }
      };
      bulkUpdateServiceStub.userTypeArray = ["CC"];
      component.ngOnInit();
      expect(component.nonDigitalValue).toEqual('scomm06');
      bulkUpdateServiceStub.userTypeArray = ["IS"];
      component.ngOnInit();
      bulkUpdateServiceStub.userTypeArray = ["PO"]
      component.ngOnInit();
      bulkUpdateServiceStub.savedSearchesFilter = "Needs Digital Assignment";
      component.ngOnInit();
      bulkUpdateServiceStub.savedSearchesFilter = "Needs Non-Digital Assignment";
      component.ngOnInit();
      bulkUpdateServiceStub.savedSearchesFilter = "Needs Assignment";
      component.ngOnInit();

    });

    it('makes username assignment when selected status is digital', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get (
        BulkUpdateService
      );   
      bulkUpdateServiceStub.bulkAssignedUsers = {
        "1416083724": {
          digitalStatus: 'C',
          digitalUser: {
            firstName: "Narender",
            lastName: "Lingampally",
            userId: "nling05"
          },
          nonDigitalStatus: 'NA',
          nonDigitalUser: null
        }
      };
      const digitalUser = bulkUpdateServiceStub.bulkAssignedUsers['1416083724'].digitalUser;
      const digitalStatus = bulkUpdateServiceStub.bulkAssignedUsers['1416083724'].digitalStatus;
      component.ngOnInit();
    });

    it('makes username assignment when selected status is non-digital', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get (
        BulkUpdateService
      );   
      bulkUpdateServiceStub.bulkAssignedUsers = {
        "1416083724": {
          digitalStatus: 'NA',
          digitalUser: null,
          nonDigitalStatus: 'C',
          nonDigitalUser: {
            firstName: "Narender",
            lastName: "Lingampally",
            userId: "nling05"
          },
        }
      };
      
      const nonDigitalUser = bulkUpdateServiceStub.bulkAssignedUsers['1416083724'].nonDigitalUser;
      const nonDigitalStatus = bulkUpdateServiceStub.bulkAssignedUsers['1416083724'].nonDigitalStatus;
      component.ngOnInit();
    });
  });
  describe('openModal', () => {
    it('makes expected calls openModal', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(
        BsModalService
      );
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      spyOn(bsModalServiceStub, "show");
      component.datesUpdated = true;
      bulkUpdateServiceStub.requestIdArr = [];
      component.openModal({}, {});
      expect(bsModalServiceStub.show).toHaveBeenCalled();
    });
    xit('makes expected calls openModal when onHide emits reason and updateDates is true', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(
        BsModalService
      );
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      spyOn(bsModalServiceStub, "show");
      spyOn(component, 'searchAllOfferRequestPage');
      bsModalServiceStub.onHide = new EventEmitter(true);
      component.datesUpdated = true;
      bulkUpdateServiceStub.requestIdArr = [];
      bsModalServiceStub.onHide.emit(true);
      component.openModal({}, {});
      fixture.detectChanges();
      expect(bsModalServiceStub.show).toHaveBeenCalled();
      expect(component.datesUpdated).toEqual(false);
      expect(component.searchAllOfferRequestPage).toHaveBeenCalled();
    });

  });
  describe('onSubmit', () => {
    it('makes expected calls', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      component.onSubmit();
    });
  });
  describe('onSuccessHandler', () => {
    it('makes expected calls onSuccessHandler when datesUpdated', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      component.modalRef =  new BsModalRef();
      spyOn(component, "searchAllOfferRequestPage");
      component.datesUpdated = true;
      bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([]);
      component.onSuccessHandler();
      expect(component.searchAllOfferRequestPage).toHaveBeenCalled();
    });
    it('makes expected calls onSuccessHandler when dates not updated', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      component.modalRef =  new BsModalRef();
      spyOn(component, "searchAllOfferRequestPage");
      component.datesUpdated = false;
      bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([]);
      component.onSuccessHandler();
      expect(component.searchAllOfferRequestPage).not.toHaveBeenCalled();
    });
  });
  describe('getQuery', () => {
    it('makes expected calls', () => {
      const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(
        QueryGenerator
      );
      spyOn(queryGeneratorStub, 'getQueryWithFilter').and.returnValue([]);
      spyOn(queryGeneratorStub, 'getQuery').and.returnValue("userId=(pjain03)");
      spyOn(queryGeneratorStub, 'removeParam');
      spyOn(queryGeneratorStub, 'setQuery');
      component.isAllBatchSelected = '';
      component.getQuery();
      expect(queryGeneratorStub.getQueryWithFilter).toHaveBeenCalled();

      component.isAllBatchSelected = 'selectAcrossAllPages';
      component.getQuery();
      expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
      expect(queryGeneratorStub.removeParam).toHaveBeenCalled();
      expect(queryGeneratorStub.setQuery).toHaveBeenCalled();
    });
    it('makes expected calls isDisplayDigital', () => {
      const deliveryChannel = ['DO'];
      const exist = component.isDisplayDigital(deliveryChannel);
      expect(exist).toEqual(true);
    });
    it('makes expected calls isDisplayNonDigital', () => {
      const deliveryChannel = ['CC'];
      const exist = component.isDisplayNonDigital(deliveryChannel);
      expect(exist).toEqual(true);
    });
    it('makes expected calls getAssignmentUsers', () => {
      component.nonDigitalValue = "non";
      const exist = component.getAssignmentUsers();
      expect(exist.length).toEqual(1);
    });
    it('makes expected calls getAssignmentUsers', () => {
      component.digitalValue = "digital";
      const exist = component.getAssignmentUsers();
      expect(exist.length).toEqual(1);
    });
    it('makes expected calls getAssignmentUsers', () => {
      component.digitalValue = "digital";
      component.nonDigitalValue = "non";
      const exist = component.getAssignmentUsers();
      expect(exist.length).toEqual(2);
    });
    it('makes expected calls', () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(
        QueryGenerator
      );
      const spy = spyOn(searchOfferRequestServiceStub, "searchAllOfferRequest").and.returnValue(of({}));
      const spy1 = spyOn(searchOfferRequestServiceStub, "getOfferDetails");
      const spy2 = spyOn(queryGeneratorStub, "getQuery");
      component.searchAllOfferRequestPage();
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('setTooltipValue', () => {
    it('makes expected calls', () => {
      const builderForm = new UntypedFormGroup({
        digital: new UntypedFormControl(),
        nonDigital: new UntypedFormControl(),
      });
      const res = component.setTooltipValue('digital');
      const val = builderForm.get('digital').value;
      expect(res).toEqual(val);
    });
  });

  describe('exit', () => {
    it('make expected calls', () => {
      const spy = spyOn(component.cancelClick, 'emit');
      component.exit(0);
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('ngOnDestroy', () => {
    it('makes expected calls when datesUpdated', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(BulkUpdateService);
      bulkUpdateServiceStub.hideApiErrorOnRequestHome$ = new Subject();
      component.datesUpdated = true;
      bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([]);
      bulkUpdateServiceStub.offerBulkSelection = new BehaviorSubject(null);
      bulkUpdateServiceStub.isSelectionReset = new BehaviorSubject(true);
      bulkUpdateServiceStub.requestIdArr = [];
      bulkUpdateServiceStub.userTypeArray = [];
      component.ngOnDestroy();
    });
    it('makes expected calls when datesUpdated is false', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(BulkUpdateService);
      bulkUpdateServiceStub.hideApiErrorOnRequestHome$ = new Subject();
      component.datesUpdated = false;
      component.ngOnDestroy();
    });
  });
  describe("onAddingUser",()=>{
    it("if flag is ND",()=>{
      component.nonDigitalUserDetails=[{userName:"name",userId:1}]
      component.onAddingUser("name", "ND")
      expect(component.nonDigitalValue).toEqual(1)
    });
    it("if flag is DG",()=>{
      component.digitalUserDetails=[{userName:"name",userId:1}]
      component.onAddingUser("name", "DG")
      expect(component.digitalValue).toEqual(1)
    });
  });
   describe("unAssignUser",()=>{
     it("make expected calls",()=>{
       let spy=spyOn(component,"getQuery")
       component.unAssignUser("Digital")
       expect(spy).toHaveBeenCalled()
      });
      it("make expected calls",()=>{
        let spy=spyOn(component,"getQuery")
        component.unAssignUser("nonDigital")
        expect(spy).toHaveBeenCalled()
      });
   });

  describe('getUsers', () => {
    it('should call getDigitalBuilders when typeOfBuilders is digital', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      const spy = spyOn(searchUsersServiceStub, 'getDigitalBuilders').and.returnValue(of([]));
      const term = 'test';
      component.getUsers(term, 'digital');
      expect(spy).toHaveBeenCalledWith(term);
    });

    it('should call getNonDigitalBuilders when typeOfBuilders is nondigital', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      const spy = spyOn(searchUsersServiceStub, 'getNonDigitalBuilders').and.returnValue(of([]));
      const term = 'test';
      component.getUsers(term, 'nondigital');
      expect(spy).toHaveBeenCalledWith(term);
    });

    it('should return an empty observable when term is null', () => {
      const result = component.getUsers(null, 'digital');
      result.subscribe((res) => {
        expect(res).toEqual([]);
      });
    });

    it('should handle term with multiple words by removing the last word', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      const spy = spyOn(searchUsersServiceStub, 'getDigitalBuilders').and.returnValue(of([]));
      const term = 'test term';
      component.getUsers(term, 'digital');
      expect(spy).toHaveBeenCalledWith('test');
    });
  });

  describe('getUpdatedAssignments', () => {
    it('should return the correct message for submit action when there are updated assignments', () => {
      const response = {
        "Attempted Assignments": "10",
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1"
      };
      const result = component.getUpdatedAssignments(response, "submit");
      expect(result).toEqual("Assignments were not updated for draft offer requests.");
    });

    it('should return an empty string for submit action when there are no updated assignments', () => {
      const response = {
        "Attempted Assignments": "6",
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1"
      };
      const result = component.getUpdatedAssignments(response, "submit");
      expect(result).toEqual("");
    });

    it('should return the correct message for remove action when there are updated assignments', () => {
      const response = {
        "Attempted Un-Assignments": "10",
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1"
      };
      const result = component.getUpdatedAssignments(response, "remove");
      expect(result).toEqual("Assignments were not removed for processed and completed offer requests.");
    });

    it('should return an empty string for remove action when there are no updated assignments', () => {
      const response = {
        "Attempted Un-Assignments": "6",
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1"
      };
      const result = component.getUpdatedAssignments(response, "remove");
      expect(result).toEqual("");
    });
  });

  describe('openModal', () => {
    it('should call searchAllOfferRequestPage when onHide emits a reason and datesUpdated is true', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(
        BsModalService
      );
      spyOn(bsModalServiceStub, 'show');
      spyOn(component, 'searchAllOfferRequestPage');
      bsModalServiceStub.onHide = new EventEmitter<string>();
      component.datesUpdated = true;

      component.openModal({}, {});
      bsModalServiceStub.onHide.emit('some reason');

      expect(component.datesUpdated).toEqual(false);
      expect(component.searchAllOfferRequestPage).toHaveBeenCalled();
    });

    it('should not call searchAllOfferRequestPage when onHide emits a reason and datesUpdated is false', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(
        BsModalService
      );
      spyOn(bsModalServiceStub, 'show');
      spyOn(component, 'searchAllOfferRequestPage');
      bsModalServiceStub.onHide = new EventEmitter<string>();
      component.datesUpdated = false;

      component.openModal({}, {});
      bsModalServiceStub.onHide.emit('some reason');

      expect(component.datesUpdated).toEqual(false);
      expect(component.searchAllOfferRequestPage).not.toHaveBeenCalled();
    });
  });

  describe('initTypeAhead', () => {
    it('should set nonDigitalArr and nonDigitalUserDetails when typedNonDigital$ emits a value', () => {
      const mockUsers = [
        { userId: 'user1', firstName: 'John', lastName: 'Doe' },
        { userId: 'user2', firstName: 'Jane', lastName: 'Smith' },
      ];
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      spyOn(searchUsersServiceStub, 'getNonDigitalBuilders').and.returnValue(of(mockUsers));

      component.initTypeAhead();
      component.typedNonDigital$.next('test');

    });

    it('should set digitalArr and digitalUserDetails when typedDigital$ emits a value', () => {
      const mockUsers = [
        { userId: 'user1', firstName: 'Alice', lastName: 'Brown' },
        { userId: 'user2', firstName: 'Bob', lastName: 'White' },
      ];
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      spyOn(searchUsersServiceStub, 'getDigitalBuilders').and.returnValue(of(mockUsers));

      component.initTypeAhead();
      component.typedDigital$.next('test');

    });

    it('should handle errors for typedNonDigital$', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(BulkUpdateService);
      spyOn(searchUsersServiceStub, 'getNonDigitalBuilders').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.initTypeAhead();
      component.typedNonDigital$.next('test');
    });

    it('should handle errors for typedDigital$', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(BulkUpdateService);
      spyOn(searchUsersServiceStub, 'getDigitalBuilders').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.initTypeAhead();
      component.typedDigital$.next('test');
    });
  });

  describe('onSubmit', () => {
    it('should show success toastr when response contains a valid jobId', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsersUJ').and.returnValue(of({ jobId: '12345' }));
      spyOn(toastrServiceStub, 'success');

      component.onSubmit();

      expect(toastrServiceStub.success).toHaveBeenCalledWith(
        'Offer Request(s) are assigning',
        '',
        { timeOut: 3000, closeButton: true }
      );
    });

    it('should handle error and call hideApiErrorOnRequestHome$ when bulkAssignUsersUJ fails', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsersUJ').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.onSubmit();
    });

    it('should open modal and set success messages when bulkAssignUsers succeeds', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Assignments": "10",
        message: "SUCCESS"
      };
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsers').and.returnValue(of(response));
      spyOn(component, 'openModal');
      spyOn(component, 'getUpdatedAssignments').and.returnValue('');

      component.onSubmit();

    });

    it('should handle error and call hideApiErrorOnRequestHome$ when bulkAssignUsers fails', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsers').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.onSubmit();
    });
  });

  describe('initTypeAhead', () => {
    it('should populate nonDigitalArr and nonDigitalUserDetails when typedNonDigital$ emits a value', () => {
      const mockUsers = [
        { userId: 'user1', firstName: 'John', lastName: 'Doe' },
        { userId: 'user2', firstName: 'Jane', lastName: 'Smith' },
      ];
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      spyOn(searchUsersServiceStub, 'getNonDigitalBuilders').and.returnValue(of(mockUsers));

      component.initTypeAhead();
      component.typedNonDigital$.next('test');

    });

    it('should handle empty user list when typedNonDigital$ emits a value', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      spyOn(searchUsersServiceStub, 'getNonDigitalBuilders').and.returnValue(of([]));

      component.initTypeAhead();
      component.typedNonDigital$.next('test');

      expect(component.nonDigitalArr).toEqual([]);
      expect(component.nonDigitalUserDetails).toEqual([]);
    });

    it('should handle errors when typedNonDigital$ emits a value', () => {
      const searchUsersServiceStub: SearchUsersService = fixture.debugElement.injector.get(SearchUsersService);
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(BulkUpdateService);
      spyOn(searchUsersServiceStub, 'getNonDigitalBuilders').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.initTypeAhead();
      component.typedNonDigital$.next('test');
    });
  });

  describe('onSubmit', () => {
    it('should handle success response when bulkAssignUsers succeeds with UPDATE_FAILED message', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Assignments": "10",
        message: "UPDATE_FAILED"
      };
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsers').and.returnValue(of(response));
      spyOn(component, 'openModal');
      spyOn(component, 'getUpdatedAssignments').and.returnValue('');

      component.onSubmit();

    });

    it('should handle success response when bulkAssignUsers succeeds without UPDATE_FAILED message', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Assignments": "10",
        message: "SUCCESS"
      };
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsers').and.returnValue(of(response));
      spyOn(component, 'openModal');
      spyOn(component, 'getUpdatedAssignments').and.returnValue('');

      component.onSubmit();

    });

    it('should handle error response when bulkAssignUsers fails', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      spyOn(bulkUpdateServiceStub, 'bulkAssignUsers').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.onSubmit();
    });
  });

  describe('unAssignUser', () => {
    it('should handle success response when bulkUnAssignUsers succeeds with UPDATE_FAILED message', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Un-Assignments": "10",
        message: "UPDATE_FAILED"
      };
      spyOn(bulkUpdateServiceStub, 'bulkUnAssignUsers').and.returnValue(of(response));
      spyOn(component, 'openModal');
      spyOn(component, 'getUpdatedAssignments').and.returnValue('');

      component.unAssignUser('Digital');

      expect(component.datesUpdated).toBeTrue();
    });

    it('should handle success response when bulkUnAssignUsers succeeds without UPDATE_FAILED message', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Un-Assignments": "10",
        message: "SUCCESS"
      };
      spyOn(bulkUpdateServiceStub, 'bulkUnAssignUsers').and.returnValue(of(response));
      spyOn(component, 'openModal');
      spyOn(component, 'getUpdatedAssignments').and.returnValue('');

      component.unAssignUser('Digital');

      expect(component.datesUpdated).toBeTrue();
    });

    it('should handle error response when bulkUnAssignUsers fails', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      spyOn(bulkUpdateServiceStub, 'bulkUnAssignUsers').and.returnValue(of(() => { throw new Error('Error'); }));
      spyOn(bulkUpdateServiceStub.hideApiErrorOnRequestHome$, 'next');

      component.unAssignUser('Digital');

    });

    it('should reset form values for Digital type', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Un-Assignments": "10",
        message: "SUCCESS"
      };
      spyOn(bulkUpdateServiceStub, 'bulkUnAssignUsers').and.returnValue(of(response));

      component.unAssignUser('Digital');

      expect(component.builderForm.get('digital').value).toBeNull();
      expect(component.digitalValue).toEqual('');
    });

    it('should reset form values for Non-Digital type', () => {
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      const response = {
        "Digital & Non-Digital Track": "3",
        "Digital-Only Track": "2",
        "Non-Digital-Only Track": "1",
        "Attempted Un-Assignments": "10",
        message: "SUCCESS"
      };
      spyOn(bulkUpdateServiceStub, 'bulkUnAssignUsers').and.returnValue(of(response));

      component.unAssignUser('Non-Digital');

      expect(component.builderForm.get('nonDigital').value).toBeNull();
      expect(component.nonDigitalValue).toEqual('');
    });

    it('should set dropdown opacity to 0 when assignedDropdown exists', () => {
      const assignedDropdown = document.createElement('div');
      assignedDropdown.classList.add('ng-dropdown-panel');
      document.body.appendChild(assignedDropdown);

      component.unAssignUser('Digital');
      document.body.removeChild(assignedDropdown);
    });
  });
});
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import { UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { CONSTANTS } from '@appConstants/constants';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { SearchUsersService } from "@appServices/common/search-users.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { Subject, of } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
} from "rxjs/operators";
@Component({
  selector: "bulk-offer-builder",
  templateUrl: "./bulk-offer-builder.component.html",
  styleUrls: ["./bulk-offer-builder.component.scss"],
})
export class BulkOfferBuilderComponent extends UnsubscribeAdapter
  implements OnInit ,OnDestroy {
  reqIdList = [];
  builderForm = new UntypedFormGroup({
    digital: new UntypedFormControl(),
    nonDigital: new UntypedFormControl(),
  });
  showErrors = "";
  showSuccessErrors = "";
  modalRef: BsModalRef;
  datesUpdated: boolean = false; 
  @Output() offerRequestSearch: EventEmitter<any> = new EventEmitter();
  isAllBatchSelected: string;
  digitalStatus: string;
  nonDigitalStatus: string;
  assignDigitalUsers = CONSTANTS.Permissions.AssignDigitalUsers;
  assignNonDigitalUsers = CONSTANTS.Permissions.AssignNonDigitalUsers;
  constructor(
    private _searchUsersService: SearchUsersService,
    public _initialDataService: InitialDataService,
    private queryGenerator: QueryGenerator,
    private bulkService: BulkUpdateService,
    private _modalService: BsModalService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private _bulkupdateService: BulkUpdateService,
    private toastr: ToastrService
    ) {
    super();
  }
  public nonDigitalArr = [];
  public digitalArr = [];
  public digitalValue : any = "";
  public nonDigitalValue: any = "";
  public digitalUserDetails = [];
  public nonDigitalUserDetails = [];
  public nonDigitalUser = "";
  public digitalUser = "";
  public bulkAssignedUsers;

  isDigital: boolean = false;
  isNonDigital: boolean = false;

  public typedNonDigital$ = new Subject<string>();
  public typedDigital$ = new Subject<string>();
  @Input("selectPage") selectPage;
  @Output() cancelClick = new EventEmitter();
  @ViewChild("successTmpl")
  private _successTmpl: TemplateRef<any>;
  ngOnInit() {
    this.initTypeAhead();
    this.subs.sink = this.bulkService.requestIdsListSelected$.subscribe(
      (reqIdList) => {
        this.reqIdList = reqIdList;
        if (this.reqIdList.length === 1) {
          const {digitalUser,nonDigitalUser,digitalStatus,nonDigitalStatus}  = this.bulkService.bulkAssignedUsers[reqIdList[0]];
           if(['C'].includes(digitalStatus)){
             this.digitalStatus = digitalUser.firstName?`${digitalUser.firstName} ${digitalUser.lastName}`:"No Assignment";
           }
           if(['C'].includes(nonDigitalStatus)){
            this.nonDigitalStatus = nonDigitalUser.firstName?`${nonDigitalUser.firstName} ${nonDigitalUser.lastName}`:"No Assignment";
          }
          if (digitalUser && digitalUser.firstName !== null) {
            this.builderForm
              .get("digital")
              .setValue(
                this.bulkService.bulkAssignedUsers[reqIdList[0]].digitalUser
                  .firstName +
                " " +
                this.bulkService.bulkAssignedUsers[reqIdList[0]].digitalUser
                  .lastName
              );
            this.digitalValue = this.bulkService.bulkAssignedUsers[
              reqIdList[0]
            ].digitalUser.userId;
          }
          if (nonDigitalUser && nonDigitalUser.firstName !== null) {
            this.builderForm
              .get("nonDigital")
              .setValue(
                this.bulkService.bulkAssignedUsers[reqIdList[0]].nonDigitalUser
                  .firstName +
                " " +
                this.bulkService.bulkAssignedUsers[reqIdList[0]]
                  .nonDigitalUser.lastName
              );
            this.nonDigitalValue = this.bulkService.bulkAssignedUsers[
              reqIdList[0]
            ].nonDigitalUser.userId;
          }
        }
        this.showAndHide();
      }
    );
    this.bulkService.isAllBatchSelected.subscribe((value) => {
      this.isAllBatchSelected  = value;
    });
  }
  showAndHide(){
    this.isNonDigital = false;
    this.isDigital = false;
    if("Needs Digital Assignment" === this.bulkService.savedSearchesFilter){
      this.isDigital = true;
    }else if("Needs Non-Digital Assignment"=== this.bulkService.savedSearchesFilter){
      this.isNonDigital = true;
    }else if("Needs Assignment" === this.bulkService.savedSearchesFilter){
      this.isDigital = true;
      this.isNonDigital = true;
    }else {
      this.isDigital = this.isDisplayDigital(
        this.bulkService.userTypeArray
      );
      this.isNonDigital = this.isDisplayNonDigital(
        this.bulkService.userTypeArray
      );
    }
    
  }
  initTypeAhead() {
    this.typedNonDigital$
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        switchMap((term) => this.getUsers(term, 'nondigital'))
      )
      .subscribe((items) => {
        let usersArr = items as any[];
        this.nonDigitalArr = usersArr.map(
          (user) => user.firstName + " " + user.lastName
        );
        this.nonDigitalUserDetails = usersArr.map((user) => {
          return {
            userId: user.userId,
            userName: user.firstName + " " + user.lastName,
          };
        });
      },
      (err)=>{
        if(err) {
          this._bulkupdateService.hideApiErrorOnRequestHome$.next(true);
        }
      });

    this.typedDigital$
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        switchMap((term) => this.getUsers(term, 'digital'))
      )
      .subscribe((items) => {
        let usersArr = items as any[];
        this.digitalArr = usersArr.map(
          (user) => user.firstName + " " + user.lastName
        );

        this.digitalUserDetails = usersArr.map((user) => {
          return {
            userId: user.userId,
            userName: user.firstName + " " + user.lastName,
          };
        });
      },
      (err)=> {
        if(err) {
          this._bulkupdateService.hideApiErrorOnRequestHome$.next(true);
        }
      });
  }

  unAssignUser(type) {
    let query = this.getQuery();
    let userType = type === "Digital" ? ["DG"] : ["ND"];
    const assignedDropdown = document.querySelector(".ng-dropdown-panel");
    const isUniversalJobEnabledForAction = this.bulkService.checkIfActionEnabledForUniversalJob("UnAssign");
    if(isUniversalJobEnabledForAction)
    {
      this.subs.sink = this.bulkService
      .bulkUnAssignUsersUJ(userType, query,CONSTANTS.OR,"UNASSIGN",CONSTANTS.SC)
      .subscribe((response: any) => {
        this.exit(0);
        this.datesUpdated = true;
        if(response?.jobId !== null && response?.jobId?.length > 0)
          {  
            this.toastr.success(`Offer request(s) are unassigning`, '',                 {
              timeOut: 3000,
              closeButton: true
            })          
          }
      },
        (error: any) => {
          if(error) {
            this._bulkupdateService.hideApiErrorOnRequestHome$.next(true);
          }
        });
    }
    else{
    this.bulkService
      .bulkUnAssignUsers(userType, query)
      .subscribe((response: any) => {
        this.exit(0);
        this.datesUpdated = true;
        this.openModal(this._successTmpl, {
          keyboard: true,
          class: "modal-lg",
        });
        let successCount = parseInt(response["Digital & Non-Digital Track"]) + parseInt(response["Digital-Only Track"]) + parseInt(response["Non-Digital-Only Track"])
        let unassignedUsers = this.getUpdatedAssignments(response, "remove");
        if (response.message === "UPDATE_FAILED") {
          this.showErrors = null;
          //"Assignments were added to 5 of the 10 offer requests selected. The other offer requests did not meet the criteria for an assignment."
          this.showSuccessErrors = `Assignments were removed from ${successCount} of the ${
            response["Attempted Un-Assignments"]
              ? response["Attempted Un-Assignments"]
              : response["Attempted Assignments"]
            } offer requests selected. The other offer requests did not meet the criteria for un-assignment.`;
        } else {
          if (unassignedUsers !== null && unassignedUsers !== "") {
            (this.showErrors = null);
              (this.showSuccessErrors = `Assignments were removed from ${successCount} of the ${
                response["Attempted Un-Assignments"]
                  ? response["Attempted Un-Assignments"]
                  : response["Attempted Assignments"]
                } offer requests selected. The other offer requests did not meet the criteria for un-assignment.`);
          } else {
            (this.showErrors = null);
              (this.showSuccessErrors = `Assignments were removed from ${successCount} of the ${
                response["Attempted Un-Assignments"]
                  ? response["Attempted Un-Assignments"]
                  : response["Attempted Assignments"]
                } offer requests selected.`);
          }
          if (type === "Digital") {
            this.builderForm.get("digital").setValue(null);
            this.digitalValue = "";
          } else {
            this.builderForm.get("nonDigital").setValue(null);
            this.nonDigitalValue = "";
          }
        }
      },
        (error: any) => {
          if(error) {
            this._bulkupdateService.hideApiErrorOnRequestHome$.next(true);
          }
        });
      }
    if (assignedDropdown) {
      assignedDropdown.setAttribute("style", "opacity:0 !important;");
    }
  }

  isDisplayNonDigital(deliveryChannel) {
    const arr = ["CC", "IS", "PO","EC"];
    const array = deliveryChannel.filter((ele) => arr.indexOf(ele) > -1);
    return array.length > 0;
  }

  isDisplayDigital(deliveryChannel) {
    const arr = ["CC", "DO", "BA", "BAC"];
    const array = deliveryChannel.filter((ele) => arr.indexOf(ele) > -1);
    return array.length > 0;
  }

  getQuery() {
    const query: any = {};
    let generatedQuery: string = null;
    const queryWithFilter = this.queryGenerator.getQueryWithFilter();
    if (queryWithFilter.length) {
      query.queryWithOrFilters = queryWithFilter;
    }
    if (this.isAllBatchSelected === 'selectAcrossAllPages') {
      const originalQuery = this.queryGenerator.getQuery();
      this.queryGenerator.removeParam('limit');
      this.queryGenerator.removeParam('sid');
      this.queryGenerator.removeParam('next');
      generatedQuery = this.queryGenerator.getQuery();
      this.queryGenerator.setQuery(originalQuery);
      if (generatedQuery) {
        generatedQuery = generatedQuery.slice(0, generatedQuery.length - 1);
      }
    } else {
      const requestedList = this.reqIdList ? this.reqIdList.join(' OR ') : null;
      generatedQuery = 'requestId=(' + requestedList + ')';
    }
    query.query = generatedQuery;
    return query;
  }

  getUsers(term, typeOfBuilders) {
    if(term != null) {
      let splitText = term && term.split(" ");
      if (splitText && splitText.length > 1) {
        splitText.pop();
        term = splitText.join(" ");
      }
  
      if(typeOfBuilders === 'digital') {
        return this._searchUsersService.getDigitalBuilders(term);
      } else if(typeOfBuilders === 'nondigital') {
        return this._searchUsersService.getNonDigitalBuilders(term);
      }
    } else {
      return of([]);
    }
  }


  setTooltipValue(controlName) {
    return this.builderForm.get(controlName).value;
  }

  onAddingUser(event, flag) {
    if (flag === "ND") {
      this.nonDigitalValue = this.nonDigitalUserDetails.length && this.nonDigitalUserDetails.filter(
        (ele) => ele.userName === event
      )[0].userId;
    } else if (flag === "DG") {
      this.digitalValue = this.digitalUserDetails.length && this.digitalUserDetails.filter(
        (ele) => ele.userName === event
      )[0].userId;
    }
  }

  getAssignmentUsers() {
    let usersList = [];
    if (this.nonDigitalValue && this.digitalValue) {
      usersList.push(
        {
          userId: this.digitalValue,
          userType: "DG",
        },
        {
          userId: this.nonDigitalValue,
          userType: "ND",
        }
      );
    } else if (this.digitalValue) {
      usersList.push({
        userId: this.digitalValue,
        userType: "DG",
      });
    } else if (this.nonDigitalValue) {
      usersList.push({
        userId: this.nonDigitalValue,
        userType: "ND",
      });
    }
    return usersList;
  }
  //can be removed not being used.
  getUpdatedAssignments(response, action) {
    const updatedAssignments = (parseInt(response["Attempted Un-Assignments"]) ? 
    parseInt(response["Attempted Un-Assignments"]) :  parseInt(response["Attempted Assignments"])) - 
    (parseInt(response["Digital & Non-Digital Track"]) +
     parseInt(response["Digital-Only Track"]) + 
     parseInt(response["Non-Digital-Only Track"]));
     
    return action === "submit"
      ? updatedAssignments > 0
        ? `Assignments were not updated for draft offer requests.`
        : ""
      : updatedAssignments > 0
        ? `Assignments were not removed for processed and completed offer requests.`
        : "";
  }
  searchAllOfferRequestPage(){
    this._searchOfferRequestService
    .searchAllOfferRequest(this.queryGenerator.getQuery(), false, this.queryGenerator.getQueryWithFilter())
    .subscribe((result: any) => {
      result.pagination = true;
      this._searchOfferRequestService.getOfferDetails(result);
    });
  }
  openModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
    this._modalService.onHide.subscribe((reason: string) => {
      if(reason && this.datesUpdated){
        this.datesUpdated = false;
        this.searchAllOfferRequestPage();
     }
     
    })
  }
  onSubmit() {
    // TO DO: Use EventEmitter with form value
    let query = this.getQuery();
    this.showErrors = "";
    let usersList = this.getAssignmentUsers();
    const isUniversalJobEnabledForAction = this.bulkService.checkIfActionEnabledForUniversalJob("Assign");
    if(isUniversalJobEnabledForAction)
    {
      this.subs.sink = this.bulkService
      .bulkAssignUsersUJ(usersList, query,CONSTANTS.OR,"ASSIGN",CONSTANTS.SC)
      .subscribe((response: any) => {
        this.exit(0);
        this.datesUpdated = true;
        if(response?.jobId !== null && response?.jobId?.length > 0)
          {            
            this.toastr.success(`Offer Request(s) are assigning`, '',                 {
              timeOut: 3000,
              closeButton: true
            });
          }
      },
        (error: any) => {
          if(error) {
            this._bulkupdateService.hideApiErrorOnRequestHome$.next(true);
          }
        });
    }
    else{
    this.subs.sink = this.bulkService
      .bulkAssignUsers(usersList, query)
      .subscribe((response: any) => {
        let successCount = parseInt(response["Digital & Non-Digital Track"]) + parseInt(response["Digital-Only Track"]) + parseInt(response["Non-Digital-Only Track"])
        const updatedAssignments = this.getUpdatedAssignments(
          response,
          "submit"
        );
        this.exit(0);
        this.openModal(this._successTmpl, {
          keyboard: true,
          class: "modal-lg",
        });
        this.datesUpdated = true;
        if (response.message === "UPDATE_FAILED") {
          this.showErrors = null;
          this.showSuccessErrors = `Assignments were added to ${successCount} of the ${
            response["Attempted Un-Assignments"]
              ? response["Attempted Un-Assignments"]
              : response["Attempted Assignments"]
            } offer requests selected. The other offer requests did not meet the criteria for an assignment.`;
        } else {
          if (updatedAssignments !== null && updatedAssignments !== "") {
            this.showErrors = null;
            this.showSuccessErrors = `Assignments were added to ${successCount} of the ${
              response["Attempted Un-Assignments"]
                ? response["Attempted Un-Assignments"]
                : response["Attempted Assignments"]
              } offer requests selected. The other offer requests did not meet the criteria for an assignment.`;
          } else {
            this.showErrors = null;
            this.showSuccessErrors = `Assignments were added to ${successCount} of the ${
              response["Attempted Un-Assignments"]
                ? response["Attempted Un-Assignments"]
                : response["Attempted Assignments"]
              } offer requests selected.`;
          }
        }
      },
        (error: any) => {
          if(error) {
            this._bulkupdateService.hideApiErrorOnRequestHome$.next(true);
          }
        });
      }

  }
  onSuccessHandler(){
    this.modalRef.hide();
    if(this.datesUpdated){
       this.datesUpdated = false;
       this.searchAllOfferRequestPage();
     }
     this._bulkupdateService.requestIdArr = [];
     this._bulkupdateService.requestIdsListSelected$.next([]);
  }
  exit($event) {
    this.cancelClick.emit($event);
  }
  ngOnDestroy(): void {
    this._bulkupdateService?.hideApiErrorOnRequestHome$?.next(false);
    if(this.datesUpdated){
      this._bulkupdateService.requestIdsListSelected$?.next([]);
      this._bulkupdateService.offerBulkSelection?.next(null);
      this._bulkupdateService.isSelectionReset?.next(true);
      this._bulkupdateService.requestIdArr = [];
      this.bulkService.userTypeArray = [];
    }  }
}

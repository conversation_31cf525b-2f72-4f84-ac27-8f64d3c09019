<ng-container *ngIf="batchActions && batchActions.length">
  <batch-action-list [isPopupDisabled]="isPopupDisabled" [featureFlagCheck]="checkForFeatureFlag"
    [batchActions]="batchActions" (onClickBatchAction)="onClickActionElement($event)"></batch-action-list>
</ng-container>

<ng-template #assignTmpl>
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <ng-container *ngTemplateOutlet="modelCloseRef"></ng-container>
      <div class="row">
        <api-errors class="col-12"></api-errors>
      </div>
      <div class="row m-1 mb-3">
        <h2>Assign to</h2>
      </div>
    </div>
  </div>
  <div class="modal-body pt-0 pr-5 pl-5">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <bulk-offer-builder (cancelClick)="modalRef.hide()"></bulk-offer-builder>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #updateDateTmpl>
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <ng-container *ngTemplateOutlet="modelCloseRef"></ng-container>
      <div class="row">
        <api-errors class="col-12"></api-errors>
      </div>
      <div class="row m-1 mb-3">
        <h2>Update Offer Dates</h2>
      </div>
    </div>
  </div>
  <div class="modal-body pr-5 pl-5 pt-0">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <offer-date-picker (cancelClick)="modalRef.hide()">
          </offer-date-picker>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #modelCloseRef>
  <div class="row">
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span class="font-weight-lighter close-icon fs-44" aria-hidden="true">&times;</span>
    </button>
  </div>
</ng-template>

<ng-template #copyTmpl>
  <div class="modal-header pb-0">
    <div class="container-fluid">
      <ng-container *ngTemplateOutlet="modelCloseRef"></ng-container>
      <div class="row pl-6">
        <h2>Copy Offer Request</h2>
      </div>
      <div class="row pl-6">
        <span class="font-size-body">Current information will apply if field is left blank</span>
      </div>
    </div>
  </div>
  <div class="modal-body pr-5 pl-5">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <batch-request-copy [modalRef]="modalRef" [action] ="action" (onBatchCopySucceed)="$event && searchAllRequest()">
          </batch-request-copy>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #copyBPDTmpl>
    <div class="modal-header pb-0">
        <div class="container-fluid">
            <ng-container *ngTemplateOutlet="modelCloseRef"></ng-container>
            <div class="row pl-6">
                <h2>Copy Offer Request</h2>
            </div>
            <div class="row pl-6">
                <span class="font-size-body">Current information will apply if field is left blank</span>
            </div>
        </div>
    </div>
    <div class="modal-body pr-5 pl-5">
        <div class="container-fluid">
            <div class="row">
                <div class="container-fluid">
                    <bpd-batch-request-copy [modalRef]="modalRef" (onBatchCopySucceed)="$event && getAllBpdRequest()"></bpd-batch-request-copy>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #deleteTmpl>
  <bulk-delete-modal [confirmationMsg]="action.confirmationMsg" (isDeleteAttempted)="onDeleteBulkOR($event)"
    [modalRef]="modalRef"></bulk-delete-modal>
</ng-template>
<ng-template #expandPeriodTmpl>
  <bulk-expand-modal [confirmationMsg]="action.confirmationMsg" (isExpandPeriodAttempted)="onExpandPeriodBulkOR($event)"
    [modalRef]="modalRef"></bulk-expand-modal>
</ng-template>
<ng-template #commonMsgTmpl>
  <app-modal-success (yesClickHandler)="onYesClick($event)" [preCheckResultObj]="preCheckResultObj"
      [modalRef]="modalRef">
  </app-modal-success>
</ng-template>
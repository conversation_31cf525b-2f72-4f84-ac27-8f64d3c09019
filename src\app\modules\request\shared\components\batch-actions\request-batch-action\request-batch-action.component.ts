import { Component, Input, TemplateRef, ViewChild } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { REQUEST_BATCH_RULES } from '@appModules/request/shared/rules/OR-batch-rules';
import { PopoverDirective } from 'ngx-bootstrap/popover';
import { BaseBatchActionComponent } from '../../../../../../shared/components/management/batch-actions/base-batch-action/base-batch-action.component';

@Component({
  selector: "request-batch-action",
  templateUrl: './request-batch-action.component.html',
  styleUrls: ['./request-batch-action.component.scss'],
})
export class RequestBatchActionComponent extends BaseBatchActionComponent {

  /*************** Inputs *************************/
  @Input() isPopupDisabled;
  @Input() popupRef: PopoverDirective;
  @Input() batchType;

  @ViewChild("assignTmpl")
  public assignTmpl: TemplateRef<any>;

  @ViewChild("updateDateTmpl")
  public updateDateTmpl: TemplateRef<any>;

  @ViewChild("copyTmpl")
  public copyTmpl: TemplateRef<any>;

  @ViewChild("copyBPDTmpl")
  public copyBPDTmpl: TemplateRef<any>;

  @ViewChild("deleteTmpl")
  public deleteTmpl: TemplateRef<any>;

  @ViewChild("expandPeriodTmpl")
  public expandPeriodTmpl: TemplateRef<any>;

  /*********************Variables******************/
  pcSelected;
  batchDeleteConfirmationMsg: string = "";
  payloadquery = "";
  isUPPSelected:boolean = false;
  constructor() {
    super();
  }
  /**
   * On load - get the batch actions from rules and set the rule based on program code
   */
  ngOnInit(): void {
    this.pcSelected = this.facetItemService.programCodeSelected;
    this.subs.sink = this.bulkUpdateService.createdAppIds$.subscribe((data)=>{
      if (this.featureFlagService.isuppEnabled && data && (data.length > 1 || data.includes("UPP"))) {
        this.isUPPSelected = true;
      }
    
      this.getBatchActionsFromRules(this.pcSelected, REQUEST_BATCH_RULES,"request",this.isUPPSelected);
    });
    
  }
  /**
   * 
   * @param action 
   * On click batch action either we do - direct batch call or do precheck or do open modal directly based on rules
   */
  
  onClickActionElement(action) {

    
    this.popupRef?.hide();
    this.action = action;
    this.onClickBaseAction(action,this.payloadQuery,'Request',this.pcSelected)
  }
  
  onExpandPeriodBulkOR(event){
    if (event) {
      this.modalRef.hide();
      this.doBulkActionForExpandPeriod(this.payloadQuery, this.action, "Request", this.pcSelected);
    }
  }
  /**
   * Set payload query based on selected Ids
   */
   get payloadQuery() {
    return this.getQueryForPreCheck(
        {
            payload: this.bulkUpdateService.requestIdArr,
            batchType: this.batchType, 
            key: "requestId",
            progrmCd: this.pcSelected,
            progrmCdKey: "programCode",
            facetPage: CONSTANTS.REQUEST
        }
    );
}
  /**
   * 
   * @param event 
   * Handling special case for delete OR - open popup and then do async call
   */
  onDeleteBulkOR(event) {
    if (event) {
      this.modalRef.hide();
      this.doBulkActionCallBasedOnPage(this.payloadQuery, this.action, "Request", this.pcSelected);
    }
  }
  
}
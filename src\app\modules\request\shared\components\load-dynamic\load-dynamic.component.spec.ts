import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LoadDynamicComponent } from './load-dynamic.component';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { Router, NavigationEnd } from '@angular/router';
import { ComponentInstanceService } from '@appRequestServices/component-instance-service.service';
import { ComponentFactoryResolver, ComponentFactory, ViewContainerRef } from '@angular/core';
import { of } from 'rxjs';
import { filter } from 'rxjs/operators';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';


describe('LoadDynamicComponent', () => {
  let component: LoadDynamicComponent;
  let fixture: ComponentFixture<LoadDynamicComponent>;
  let requestFormServiceSpy: jasmine.SpyObj<RequestFormService>;
  let facetItemServiceSpy: jasmine.SpyObj<FacetItemService>;
  let routerSpy: any;
  let componentInstanceSpy: jasmine.SpyObj<ComponentInstanceService>;
  let resolverSpy: jasmine.SpyObj<ComponentFactoryResolver>;
  let fakeViewContainerRefSpy: jasmine.SpyObj<ViewContainerRef>;

  beforeEach(async () => {
    requestFormServiceSpy = jasmine.createSpyObj('RequestFormService', ['']);
    facetItemServiceSpy = jasmine.createSpyObj('FacetItemService', [], {
      programCodeSelected: 'BPD',
      programCodeChecked: { BPD: true },
      templateProgramCodeSelected: 'BPD'
    });
    routerSpy = {
      url: '/request/requestForm/Create',
      events: of(new NavigationEnd(1, '/request/requestForm/Create', '/request/requestForm/Create'))
        .pipe(filter((e: any) => e instanceof NavigationEnd))
    };
    componentInstanceSpy = jasmine.createSpyObj('ComponentInstanceService', ['getComponent']);
    resolverSpy = jasmine.createSpyObj('ComponentFactoryResolver', ['resolveComponentFactory']);
    fakeViewContainerRefSpy = jasmine.createSpyObj('ViewContainerRef', ['clear', 'createComponent']);
    fakeViewContainerRefSpy.createComponent.and.callFake(() => ({ instance: {} } as any));

    await TestBed.configureTestingModule({
      declarations: [LoadDynamicComponent],
      providers: [
        { provide: RequestFormService, useValue: requestFormServiceSpy },
        { provide: FacetItemService, useValue: facetItemServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ComponentInstanceService, useValue: componentInstanceSpy },
        { provide: ComponentFactoryResolver, useValue: resolverSpy }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LoadDynamicComponent);
    component = fixture.componentInstance;
    component.mainContent = fakeViewContainerRefSpy;
    fixture.detectChanges();
  });

  describe('Constructor and Initialization', () => {
    it('should set facetItemService.programCodeSelected to CONSTANTS.GR if router.url starts with GRSummary', () => {
      Object.defineProperty(facetItemServiceSpy, 'programCodeSelected', {
        value: undefined, writable: true, configurable: true
      });
      (ROUTES_CONST.REQUEST as any).Request = 'request';
      (ROUTES_CONST.REQUEST as any).RequestForm = 'requestForm';
      (ROUTES_CONST.REQUEST as any).GRSummary = 'GRSummary';
      (ROUTES_CONST.REQUEST as any).SPDSummary = 'SPDSummary';
      (ROUTES_CONST.REQUEST as any).BPDSummary = 'BPDSummary';
      routerSpy.url = '/request/requestForm/GRSummary';
  
      const fixture2: ComponentFixture<LoadDynamicComponent> = TestBed.createComponent(LoadDynamicComponent);
      const comp2: LoadDynamicComponent = fixture2.componentInstance;
      spyOn(comp2, 'loadComponent');
      fixture2.detectChanges();
  
      expect(facetItemServiceSpy.programCodeSelected).toBe(CONSTANTS.GR);
    });

    it('should clear mainContent and load component on ngOnInit', () => {
      spyOn(component, 'loadComponent');
      Object.defineProperty(fakeViewContainerRefSpy, 'length', { get: () => 0, configurable: true });
      fakeViewContainerRefSpy.clear.calls.reset();
      component.ngOnInit();
      expect(fakeViewContainerRefSpy.clear).toHaveBeenCalled();
      expect(component.loadComponent).toHaveBeenCalled();
    });
  });

  describe('Input Setter and Getter for componentData', () => {
    it('should call loadComponent when componentData is set and mainContent is empty', () => {
      Object.defineProperty(fakeViewContainerRefSpy, 'length', { get: () => 0, configurable: true });
      spyOn(component, 'loadComponent');
      component.componentData = { component: 'dummy', inputs: { test: 'value' } };
      expect(component.showNavBar).toBe('dummy');
      expect(component.loadComponent).toHaveBeenCalled();
    });

    it('getter should return currentComponent', () => {
      const dummyValue = { component: 'dummyComponent', inputs: { some: 'value' } };
      component.currentComponent = dummyValue;
      expect(component.componentData).toEqual(dummyValue);
    });
  });

  describe('loadComponent', () => {
    it('should create components based on showNavBar "management"', () => {
      component.showNavBar = "management";
      const dummyRules = { offerRequestManagement: ['MComp'] };
      spyOn(component, 'getORTRulesData').and.returnValue(dummyRules);
      spyOn(component, 'createComponent');
      component.loadComponent();
      expect(component.createComponent).toHaveBeenCalledWith('MComp');
    });

    it('should create components based on showNavBar "request" when isPod is false', () => {
      component.showNavBar = "request";
      component.isPod = false;
      const dummyRules = { components: ['CompR'] };
      spyOn(component, 'getORTRulesData').and.returnValue(dummyRules);
      spyOn(component, 'createComponent');
      component.loadComponent();
      expect(component.createComponent).toHaveBeenCalledWith('CompR');
    });

    it('should create components based on showNavBar "request" when isPod is true', () => {
      component.showNavBar = "request";
      component.isPod = true;
      const dummyRules = { podComponent: ['PodR'] };
      spyOn(component, 'getORTRulesData').and.returnValue(dummyRules);
      spyOn(component, 'createComponent');
      component.loadComponent();
      expect(component.createComponent).toHaveBeenCalledWith('PodR');
    });

    it('should create components based on showNavBar "summary" when isPod is false', () => {
      component.showNavBar = "summary";
      component.isPod = false;
      const dummyRules = { summaryComponents: ['SumComp'] };
      spyOn(component, 'getORTRulesData').and.returnValue(dummyRules);
      spyOn(component, 'createComponent');
      component.loadComponent();
      expect(component.createComponent).toHaveBeenCalledWith('SumComp');
    });

    it('should create components based on showNavBar "summary" when isPod is true', () => {
      component.showNavBar = "summary";
      component.isPod = true;
      const dummyRules = { podComponent: ['PodSum'] };
      spyOn(component, 'getORTRulesData').and.returnValue(dummyRules);
      spyOn(component, 'createComponent');
      component.loadComponent();
      expect(component.createComponent).toHaveBeenCalledWith('PodSum');
    });
  });

  describe('createComponent', () => {
    it('should create a component without rendering summary/request inputs if showNavBar is not "request" or "summary"', () => {
      component.showNavBar = "management";
      component.isPod = false;
      class DummyComponent { }
      componentInstanceSpy.getComponent.and.returnValue(DummyComponent);
      const fakeComponentFactory = {} as ComponentFactory<any>;
      resolverSpy.resolveComponentFactory.and.returnValue(fakeComponentFactory);
      const fakeComponentRef: any = { instance: {} };
      fakeViewContainerRefSpy.createComponent.and.returnValue(fakeComponentRef);
      spyOn(component, 'renderSummaryOrRequest');
      spyOn(component, 'setPodComponentsInput');
      const result = component.createComponent("TestComp");
      expect(component.renderSummaryOrRequest).not.toHaveBeenCalled();
      expect(component.setPodComponentsInput).not.toHaveBeenCalled();
      expect(result).toBe(fakeComponentRef);
    });
  });

  describe('setIsSummaryValue and renderSummaryOrRequest', () => {
    it('should set isSummary to readOnly if componentName is "OfferReqBuilder" in setIsSummaryValue', () => {
      const comp: any = { instance: {} };
      component.setIsSummaryValue("OfferReqBuilder", comp, true);
      expect(comp.instance.isSummary).toBe(true);
    });
    it('should set isSummary to !readOnly if componentName is not "OfferReqBuilder" in setIsSummaryValue', () => {
      const comp: any = { instance: {} };
      component.setIsSummaryValue("SomeOtherComponent", comp, true);
      expect(comp.instance.isSummary).toBe(false);
    });
    it('should call setIsSummaryValue with appropriate readOnly in renderSummaryOrRequest for summary', () => {
      component.showNavBar = "summary";
      const comp: any = { instance: {} };
      spyOn(component, 'setIsSummaryValue');
      component.renderSummaryOrRequest(comp, "AnyComponent");
      expect(component.setIsSummaryValue).toHaveBeenCalledWith("AnyComponent", comp, false);
    });
    it('should call setIsSummaryValue with appropriate readOnly in renderSummaryOrRequest for request', () => {
      component.showNavBar = "request";
      const comp: any = { instance: {} };
      spyOn(component, 'setIsSummaryValue');
      component.renderSummaryOrRequest(comp, "AnyComponent");
      expect(component.setIsSummaryValue).toHaveBeenCalledWith("AnyComponent", comp, true);
    });
  });

  describe('setPodComponentsInput', () => {
    it('should set pod components inputs on component instance if podDetailsObj is provided', () => {
      component.podDetailsObj = { key1: 'value1', key2: 'value2' };
      const comp: any = { instance: {} };
      component.setPodComponentsInput(comp);
      expect(comp.instance.key1).toBe('value1');
      expect(comp.instance.key2).toBe('value2');
    });
    it('should do nothing if podDetailsObj is not provided', () => {
      component.podDetailsObj = null;
      const comp: any = { instance: {} };
      expect(() => component.setPodComponentsInput(comp)).not.toThrow();
      expect(comp.instance).toEqual({});
    });
  });

  describe('componentData Input Setter', () => {
    it('should set showNavBar and call loadComponent when componentData is set and mainContent is empty', () => {
      Object.defineProperty(component.mainContent, 'length', { get: () => 0, configurable: true });
      spyOn(component, 'loadComponent');
    
      component.componentData = { component: 'dummy', inputs: { test: 'value' } };
    
      expect(component.showNavBar).toBe('dummy');
      expect(component.loadComponent).toHaveBeenCalled();
    });

    it('should set showNavBar but not call loadComponent when componentData is set and mainContent is not empty', () => {
      Object.defineProperty(component.mainContent, 'length', { get: () => 1, configurable: true });
      spyOn(component, 'loadComponent');
    
      component.componentData = { component: 'dummy', inputs: { test: 'value' } };
    
      expect(component.showNavBar).toBe('dummy');
      expect(component.loadComponent).not.toHaveBeenCalled();
    });

    it('should do nothing if componentData is set to null', () => {
      component.showNavBar = 'initial';
      spyOn(component, 'loadComponent');
    
      component.componentData = null;
    
      expect(component.showNavBar).toBe('initial');
      expect(component.loadComponent).not.toHaveBeenCalled();
    });
  });

  describe('Constructor Conditional Logic for programCodeSelected', () => {
    beforeEach(() => {
      Object.defineProperty(facetItemServiceSpy, 'programCodeSelected', {
        value: undefined,
        writable: true,
        configurable: true
      });
    });

    it('should set facetItemService.programCodeSelected to CONSTANTS.SPD if router.url starts with SPDSummary', () => {
      (ROUTES_CONST.REQUEST as any).Request = 'request';
      (ROUTES_CONST.REQUEST as any).RequestForm = 'requestForm';
      (ROUTES_CONST.REQUEST as any).SPDSummary = 'SPDSummary';
  
      routerSpy.url = '/request/requestForm/SPDSummary';
  
      const fixture2: ComponentFixture<LoadDynamicComponent> = TestBed.createComponent(LoadDynamicComponent);
      const comp2: LoadDynamicComponent = fixture2.componentInstance;
      spyOn(comp2, 'loadComponent');
      fixture2.detectChanges();
  
      expect(facetItemServiceSpy.programCodeSelected).toBe(CONSTANTS.SPD);
    });

    it('should set facetItemService.programCodeSelected to CONSTANTS.BPD if router.url starts with BPDSummary', () => {
      (ROUTES_CONST.REQUEST as any).Request = 'request';
      (ROUTES_CONST.REQUEST as any).RequestForm = 'requestForm';
      (ROUTES_CONST.REQUEST as any).BPDSummary = 'BPDSummary';
  
      routerSpy.url = '/request/requestForm/BPDSummary';
  
      const fixture2: ComponentFixture<LoadDynamicComponent> = TestBed.createComponent(LoadDynamicComponent);
      const comp2: LoadDynamicComponent = fixture2.componentInstance;
      spyOn(comp2, 'loadComponent');
      fixture2.detectChanges();
  
      expect(facetItemServiceSpy.programCodeSelected).toBe(CONSTANTS.BPD);
    });

    it('should set facetItemService.programCodeSelected to CONSTANTS.SC if router.url does not match any summary route', () => {
      routerSpy.url = '/request/requestForm/Create';
  
      const fixture2: ComponentFixture<LoadDynamicComponent> = TestBed.createComponent(LoadDynamicComponent);
      const comp2: LoadDynamicComponent = fixture2.componentInstance;
      spyOn(comp2, 'loadComponent');
      fixture2.detectChanges();
  
      expect(facetItemServiceSpy.programCodeSelected).toBe(CONSTANTS.SC);
    });
  });

  describe('createComponent conditional rendering', () => {
    it('should call renderSummaryOrRequest and not call setPodComponentsInput when showNavBar is "summary" (or "request") and isPod is false', () => {
      component.showNavBar = "summary";
      component.isPod = false;
    
      spyOn(component, 'renderSummaryOrRequest');
      spyOn(component, 'setPodComponentsInput');
    
      const dummyComponent = {};
      componentInstanceSpy.getComponent.and.returnValue(dummyComponent);
      const fakeComponentRef: any = { instance: {} };
      fakeViewContainerRefSpy.createComponent.and.returnValue(fakeComponentRef);
    
      const result = component.createComponent('TestComp');
    
      expect(component.renderSummaryOrRequest).toHaveBeenCalledWith(fakeComponentRef, 'TestComp');
      expect(component.setPodComponentsInput).not.toHaveBeenCalled();
      expect(result).toBe(fakeComponentRef);
    });
  
    it('should call both renderSummaryOrRequest and setPodComponentsInput when showNavBar is "request" (or "summary") and isPod is true', () => {
      component.showNavBar = "request";
      component.isPod = true;
    
      spyOn(component, 'renderSummaryOrRequest');
      spyOn(component, 'setPodComponentsInput');
    
      const dummyComponent = {};
      componentInstanceSpy.getComponent.and.returnValue(dummyComponent);
      const fakeComponentRef: any = { instance: {} };
      fakeViewContainerRefSpy.createComponent.and.returnValue(fakeComponentRef);
    
      const result = component.createComponent('TestComp');
    
      expect(component.renderSummaryOrRequest).toHaveBeenCalledWith(fakeComponentRef, 'TestComp');
      expect(component.setPodComponentsInput).toHaveBeenCalledWith(fakeComponentRef);
      expect(result).toBe(fakeComponentRef);
    });
  });

});

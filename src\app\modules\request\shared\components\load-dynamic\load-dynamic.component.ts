import { Component, ComponentFactoryResolver, EnvironmentInjector, inject, Input, OnInit, ViewChild, ViewContainerRef } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { TEMPLATE_CREATE_RULES } from "@appModules/templates/core/offer-template/details/shared/rules/rules";
import { ComponentInstanceService } from "@appRequestServices/component-instance-service.service";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { filter } from "rxjs/operators";
import { OFFER_REQUEST_CREATE_RULES } from "../../rules/create.rules";

@Component({
  selector: "app-load-dynamic",
  templateUrl: "./load-dynamic.component.html",
  styleUrls: [],
})
export class LoadDynamicComponent implements OnInit {
  @Input("isPod") isPod;
  @Input("podDetailsObj") podDetailsObj;
  @Input('reqId') reqId;
  programCode:string;
  componentName: string;
  currentActiveRoute = "";
  showNavBar = "";
  currentComponent = null;
  createAndEditRouteUrl = [
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.Create}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.Edit}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GRCreate}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GREdit}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.SPDCreate}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.SPDEdit}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.BPDCreate}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.BPDEdit}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDCreate}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDEdit}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.SCCreate}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.SCEdit}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.GRCreate}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.GREdit}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.SPDCreate}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.SPDEdit}`,
  ];

  summaryRoutesUrl = [
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.Summary}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GRSummary}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.SPDSummary}`,
    `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.BPDSummary}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.SPDSummary}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.SCSummary}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.GRSummary}`,
    `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDSummary}`,
  ];

  managementRouteUrl = [`/${ROUTES_CONST.REQUEST.Request}`, `/${ROUTES_CONST.TEMPLATES.Template}`];

  @ViewChild("mainContent", { read: ViewContainerRef, static: true }) mainContent: ViewContainerRef;
  constructor(
    private facetItemService: FacetItemService,
    private router: Router,
    private componentInstance: ComponentInstanceService,
    private environmentInjector: EnvironmentInjector
  ) {
    // TO DO work on the rewrite for below conditional logic , Implement by adding it in Local Storage
    if (!this.facetItemService.programCodeSelected) {
      if (
        this.router.url.startsWith(`/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GRSummary}`)
      ) {
        this.facetItemService.programCodeSelected = CONSTANTS.GR;
      } else if (
        this.router.url.startsWith(
          `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.SPDSummary}`
        )
      ) {
        this.facetItemService.programCodeSelected = CONSTANTS.SPD;
      }else if (
        this.router.url.startsWith(
          `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.BPDSummary}`
        )
      ) {
        this.facetItemService.programCodeSelected = CONSTANTS.BPD;
      } else {
        this.facetItemService.programCodeSelected = CONSTANTS.SC;
      }
    }
    router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      this.currentActiveRoute = event.url;
      const createEditFilter = this.createAndEditRouteUrl.filter((elem) => this.currentActiveRoute.startsWith(elem));
      const summaryFilter = this.summaryRoutesUrl.filter((elem) => this.router.url.startsWith(elem));
      const managementFilter = this.managementRouteUrl.filter((elem) => this.currentActiveRoute.startsWith(elem));
      if (createEditFilter?.length) {
        this.showNavBar = "request";
      } else if (summaryFilter?.length) {
        this.showNavBar = "summary";
      } else if (managementFilter?.length) {
        this.showNavBar = "management";
      }
    });
  }
  ngOnInit(): void {
    const createEditFilter = this.createAndEditRouteUrl.filter((elem) => this.router.url.startsWith(elem));
    const summaryFilter = this.summaryRoutesUrl.filter((elem) => this.router.url.startsWith(elem));
    const managementFilter = this.managementRouteUrl.filter((elem) => this.router.url.startsWith(elem));
    if (createEditFilter?.length) {
      this.showNavBar = "request";
    } else if (summaryFilter?.length) {
      this.showNavBar = "summary";
    } else if (managementFilter?.length) {
      this.showNavBar = "management";
    }

    this.mainContent.clear();
    this.loadComponent();
  }
  // component: Class for the component you want to create
  // inputs: An object with key/value pairs mapped to input name/input value
  @Input() set componentData(data: { component: any; inputs: any }) {
    if (!data) {
      return;
    }
    this.showNavBar = data.component;
    if (!this.mainContent.length) {
      this.loadComponent();
    }
  }

  get componentData() {
    return this.currentComponent;
  }
  getORTRulesData() {
    const programCode = this.facetItemService.programCodeSelected;
    this.programCode = programCode;
    return this.router.url.includes("request") ? OFFER_REQUEST_CREATE_RULES[programCode] : TEMPLATE_CREATE_RULES.BPD;
  }
  loadComponent() {
    const rules = this.getORTRulesData();
    let components;
    if (this.showNavBar === "management") {
      components = rules?.offerRequestManagement;
    } else if (this.showNavBar === "request") {
      components = this.isPod ? rules.podComponent : rules.components;
    } else if (this.showNavBar === "summary") {
      components = this.isPod ? rules.podComponent : rules.summaryComponents;
    }
    components.forEach((name) => {
      this.createComponent(name);
    });
  }
  setIsSummaryValue(componentName, component, readOnly) {
    if (componentName === "OfferReqBuilder") {
      component.instance.isSummary = readOnly;
    } else {
      component.instance.isSummary = !readOnly;
    }
  }
  renderSummaryOrRequest(component, name) {
    if (this.showNavBar === "summary") {
      this.setIsSummaryValue(name, component, false);
    } else if (this.showNavBar === "request") {
      this.setIsSummaryValue(name, component, true);
    }
  }
  setPodComponentsInput(component) {
    this.podDetailsObj && Object.keys(this.podDetailsObj).forEach((key) => (component.instance[key] = this.podDetailsObj[key]));
  }
  createComponent(name) {
    let component = this.componentInstance.getComponent(name);
    const componentRef = this.mainContent.createComponent(component, { environmentInjector: this.environmentInjector });
    if (["summary", "request"].includes(this.showNavBar)) {
      this.renderSummaryOrRequest(componentRef, name);
      if (this.isPod) {
        this.setPodComponentsInput(componentRef);
      }
    }
    this.currentComponent = this.mainContent;
    return componentRef;
  }
}

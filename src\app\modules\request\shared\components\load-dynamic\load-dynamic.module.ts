// Angular Imports
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DigitDecimaNumberDirectiveModule } from '@appDirectives/digit-decimal/digit.decimal.module';
import { FeatureFlagDirectiveModule } from '@appDirectives/feature-flags/feature-flag-module';
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { OnlyNumberDirectiveModule } from '@appDirectives/only-number/only.number.module';
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { AppCommonModule } from '@appModules/common/app.common.module';
import { OfferRequestListComponent } from '@appModules/request/shared/components/offer-request-list/offer-request-list.component';
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { FacetsModule } from '@appShared/components/common/facets/facets.module';
import { PreviewCardModule } from '@appShared/components/details/preview-card/preview-card.module';
import { PreviewCommentsHistoryModule } from '@appShared/components/details/preview-comments-history/preview-comments-history.module';
import { SidebarModule } from '@appShared/ng-sidebar';
import { NgxConfirmBoxModule } from '@appShared/ngx-confirm-box';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { ModalModule } from 'ngx-bootstrap/modal';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { InputDateComponentComponent } from '../../../../../shared/components/details/input-fields/input-date-component/input-date-component.component';
import { InputDisplayComponentComponent } from '../../../../../shared/components/details/input-fields/input-display-component/input-display-component.component';
import { InputSelectComponentComponent } from '../../../../../shared/components/details/input-fields/input-select-component/input-select-component.component';
import { InputTextAreaComponentComponent } from '../../../../../shared/components/details/input-fields/input-text-area-component/input-text-area-component.component';
import { InputTextComponentComponent } from '../../../../../shared/components/details/input-fields/input-text-component/input-text-component.component';
import { NgSelectComponent } from '../../../../../shared/components/details/input-fields/ng-select-component/ng-select.component';
import { ShowFieldErrorComponent } from '../../../../../shared/components/details/input-fields/show-field-error/show-field-error.component';
import { RequestListComponent } from '../request-list/request-list.component';
import { LoadDynamicComponent } from './load-dynamic.component';

import { LetDirectiveModule } from '@appDirectives/let/let.module';
import { BpdAdditonalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/bpd-additonal-description/bpd-additonal-description.component';
import { GrAdditionalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/gr-additional-description/gr-additional-description.component';
import { SpdAdditionalDescriptionComponent } from '@appModules/request/core/offer-request/details/components/additional-description/spd-additional-description/spd-additional-description.component';
import { FileAttachmentComponent } from '@appModules/request/core/offer-request/details/components/file-attachment/file-attachment.component';
import { BPDPodComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/bpd-pod/bpd-pod.component';
import { GrPodComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/gr-pod/gr-pod.component';
import { PodDetailsComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/pod-details.component';
import { SPDPodComponent } from '@appModules/request/core/offer-request/details/components/general-offer-type/pod-details/spd-pod/spd-pod.component';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { BpdRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/bpd-request-section/bpd-request-section.component';
import { CustomRewardComponent } from '@appModules/request/core/offer-request/details/components/request-section/custom-reward/custom-reward.component';
import { GrRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/gr-request-section/gr-request-section.component';
import { SpdRequestSectionComponent } from '@appModules/request/core/offer-request/details/components/request-section/spd-request-section/spd-request-section.component';
import { FullfillmentChannelComp } from '@appModules/request/core/offer-request/details/components/shopping-channel-flags/shopping-channel-flags.comp';
import { TimeComponent } from '@appModules/request/core/offer-request/details/components/time/time.component';
import { BpdOfferRequestManagementComponent } from '@appModules/request/core/offer-request/management/components/bpd-offer-request-management/bpd-offer-request-management.component';
import { GrOfferRequestManagementComponent } from '@appModules/request/core/offer-request/management/components/gr-offer-request-management/gr-offer-request-management.component';
import { SpdOfferRequestManagementComponent } from '@appModules/request/core/offer-request/management/components/spd-offer-request-management/spd-offer-request-management.component';
import { AdditionalInformationComponent } from '@appModules/templates/core/offer-template/details/components/additional-information/additional-information.component';
import { DetailActionsComponent } from '@appModules/templates/core/offer-template/details/components/detail-actions/detail-actions.component';
import { PodDetailsTemplateComponent } from '@appModules/templates/core/offer-template/details/components/pod-details-template/pod-details-template.component';
import { TemplateSectionComponent } from '@appModules/templates/core/offer-template/details/components/template-section/template-section.component';
import { ManagementActionsComponent } from '@appModules/templates/core/offer-template/management/components/actions/management-actions.component';
import { TemplateManagementComponent } from '@appModules/templates/core/offer-template/management/components/home/<USER>';
import { TemplateListComponent } from '@appModules/templates/core/offer-template/management/components/template-list/template-list.component';
import { AddAllocationModule } from '@appShared/components/details/add-allocation/add-allocation.module';
import { InputAutocompleteComponent } from '@appShared/components/details/input-fields/input-autocomplete-component/input-autocomplete-component.component';
import { InputCheckboxComponent } from '@appShared/components/details/input-fields/input-checkbox-component/input-checkbox-component';
import { UpcListTableModule } from '@appShared/components/details/upc-list-table/upc-list-table.module';




@NgModule({
    imports: [
        FormsModule,
        PreviewCardModule,
        LetDirectiveModule,
        NgSelectModule,
        AddAllocationModule,
        PreviewCommentsHistoryModule,
        ReactiveFormsModule,
        RouterModule,
        BsDatepickerModule.forRoot(),
        FacetsModule,
        CommonModule,
        MarkAsTouchedOnFocusDirectiveModule,
        VarDirectiveModule,
        AppCommonModule,
        TabsModule.forRoot(),
        ModalModule.forRoot(),
        SidebarModule.forRoot(),
        TypeaheadModule.forRoot(),
        ApiErrorsModule,
        DigitDecimaNumberDirectiveModule,
        TooltipModule.forRoot(),
        NgxConfirmBoxModule,
        NgOptionHighlightModule,
        PermissionsModule.forChild(),
        FeatureFlagDirectiveModule,
        PopoverModule.forRoot(),
        OnlyNumberDirectiveModule,
        UpcListTableModule
    ],
    declarations: [
        FileAttachmentComponent,
        PodDetailsComponent,
        RequestListComponent,
        GrRequestSectionComponent,
        ShowFieldErrorComponent,
        InputTextComponentComponent,
        InputDateComponentComponent,
        NgSelectComponent,
        TimeComponent,
        InputSelectComponentComponent,
        InputDisplayComponentComponent,
        InputCheckboxComponent,
        BaseFieldComponentComponent,
        OfferRequestListComponent,
        LoadDynamicComponent,
        GrOfferRequestManagementComponent,
        GrPodComponent,
        GrAdditionalDescriptionComponent,
        InputTextAreaComponentComponent,
        SpdOfferRequestManagementComponent,
        SpdRequestSectionComponent,
        SPDPodComponent,
        BpdOfferRequestManagementComponent,
        BpdRequestSectionComponent,
        BPDPodComponent,
        BpdAdditonalDescriptionComponent,
        InputAutocompleteComponent,
        TemplateSectionComponent,
        SpdAdditionalDescriptionComponent,
        TemplateListComponent,
        TemplateManagementComponent,
        AdditionalInformationComponent,
        ManagementActionsComponent,
        DetailActionsComponent,
        PodDetailsTemplateComponent,
        CustomRewardComponent,
        FullfillmentChannelComp
    ],
    exports: [
        FileAttachmentComponent,
        PodDetailsComponent,
        RequestListComponent,
        InputCheckboxComponent,
        GrRequestSectionComponent,
        ShowFieldErrorComponent,
        InputTextComponentComponent,
        InputDateComponentComponent,
        InputSelectComponentComponent,
        InputDisplayComponentComponent,
        BaseFieldComponentComponent,
        OfferRequestListComponent,
        LoadDynamicComponent,
        GrOfferRequestManagementComponent,
        GrPodComponent,
        TimeComponent,
        GrAdditionalDescriptionComponent,
        InputTextAreaComponentComponent,
        CustomRewardComponent,
        SpdOfferRequestManagementComponent,
        SpdRequestSectionComponent,
        SPDPodComponent,
        SpdAdditionalDescriptionComponent,

        BpdOfferRequestManagementComponent,
        BpdRequestSectionComponent,
        BPDPodComponent,
        BpdAdditonalDescriptionComponent,
        InputAutocompleteComponent,
        TemplateSectionComponent,
        TemplateListComponent,
        TemplateManagementComponent,
        AdditionalInformationComponent,
        PodDetailsTemplateComponent,
        FullfillmentChannelComp
    ]
})
export class LoadDynamicModule {

}
<div class="modal-header">
  <h4 class="modal-title pull-left">Disclaimer</h4>
  <button type="button" class="close pull-right" aria-label="Close" (click)="closeDisclaimerModalView()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="row">
    <div class="col d-flex justify-content-center">
      <div class="table-container">
        <table class="table table-striped table-hover disclaimer" aria-describedby="Disclaimer Table">
          <thead>
          <tr>
            <th scope="col">Name</th>
            <th scope="col">Description</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let x of data; let i = index" class="cursor-pointer" [class.active]="i == selectedRow"
              (click)="setClickedRow(i, x.collapsed , x.name)">
            <td style="width: 150px;">{{x.name}}</td>
            <td [ngClass]="{'ellipsis': x.collapsed}"><span>{{x.description}}</span></td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

@import "scss/_mixins";
@import "scss/variables";
@import "scss/_colors";

.disclaimer{
  font-size: 14px;
}

.ellipsis {
  position: relative;
}
.ellipsis:before {
  content: '&nbsp;';
  visibility: hidden;
}
.ellipsis span {
  position: absolute;
  left: 0;
  right: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table tr.active td {
  background-color:$blue-primary-hex !important;
  color: $theme-white;
}
.table-container {
  height: 20em;
}
table {
  display: flex;
  flex-flow: column;
  height: 100%;
  width: 100%;
}
table thead {
  flex: 0 0 auto;
  width: calc(100% - 0.9em);
}
table tbody {
  flex: 1 1 auto;
  display: block;
  overflow-y: scroll;
}
table tbody tr {
  width: 100%;
}
table thead, table tbody tr {
  display: table;
  table-layout: fixed;
}

textarea {
  min-height: 150px;
  resize: none;
}





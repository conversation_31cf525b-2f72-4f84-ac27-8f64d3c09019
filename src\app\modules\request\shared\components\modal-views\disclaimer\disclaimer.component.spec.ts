import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DisclaimerComponent } from './disclaimer.component';
import { PodDetailsService } from '@appServices/details/pod-details.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { By } from '@angular/platform-browser';

describe('DisclaimerComponent', () => {
    let component: DisclaimerComponent;
    let fixture: ComponentFixture<DisclaimerComponent>;
    let podDetailsService: PodDetailsService;
    let modalRef: BsModalRef;

    beforeEach(async () => {
        const podDetailsServiceStub = {
            setDisclaimer: jasmine.createSpy('setDisclaimer')
        };

        const modalRefStub = {
            hide: jasmine.createSpy('hide')
        };

        await TestBed.configureTestingModule({
            declarations: [DisclaimerComponent],
            providers: [
                { provide: PodDetailsService, useValue: podDetailsServiceStub },
                { provide: BsModalRef, useValue: modalRefStub }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DisclaimerComponent);
        component = fixture.componentInstance;
        podDetailsService = TestBed.inject(PodDetailsService);
        modalRef = TestBed.inject(BsModalRef);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should close the modal when closeDisclaimerModalView is called', () => {
        component.closeDisclaimerModalView();
        expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should set the selected row and toggle collapsed state when setClickedRow is called', () => {
        const index = 1;
        const collapsed = true;
        const name = 'Southwest';

        component.setClickedRow(index, collapsed, name);

        expect(component.selectedRow).toBe(index);
        expect(component.data[component.objIndex].collapsed).toBeFalse();
        expect(podDetailsService.setDisclaimer).toHaveBeenCalledWith(component.data[component.objIndex].description);
    });

    it('should initialize with default values', () => {
        expect(component.selectedRow).toBeUndefined();
        expect(component.objIndex).toBeUndefined();
    });

    it('should have data array with correct length', () => {
        expect(component.data.length).toBe(6);
    });

    it('should render the correct number of rows', () => {
        const rows = fixture.debugElement.queryAll(By.css('.row-selector'));
    });
});
import { Component, OnInit } from '@angular/core';
import { PodDetailsService } from '@appServices/details/pod-details.service';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-disclaimer',
  templateUrl: './disclaimer.component.html',
  styleUrls: ['./disclaimer.component.scss']
})
export class DisclaimerComponent implements OnInit {
  selectedRow: number;
  objIndex: any;

  constructor(public podDetailsService: PodDetailsService,
    public modalRef: BsModalRef) {
    // intentionally left empty
  }

  ngOnInit() {
    // intentionally left empty
  }

  data = [{ name: 'Denver', collapsed: false, description: 'Save $A when you spend $X, or Save $B when you spend $Y, or Save $C when you spend $Z or more in qualifying purchases in a single transaction using your just for U® account ($X or $Y or $Z minimum purchase calculated after deduction of promotional and loyalty card savings, coupons and all other discounts, offers and savings). Offer is valid [INSERT START DATE] - [INSERT END DATE] only at participating Safeway and Albertsons stores located in Colorado, Nebraska, South Dakota, Eastern Wyoming, Farmington, NM and Aztec, NM. Qualifying purchases exclude: alcoholic beverages, tobacco products, fluid dairy, fuel, prescription items and co-payments, fishing/hunting licenses and tags, postage stamps, money orders/transfers, bus/commuter passes, amusement park/ski/event/lottery tickets, phone cards, gift cards/certificates and applicable taxes, bottle/container deposits and bag fees.' },
  { name: 'Southwest', collapsed: true, description: 'Save $A when you spend $X, or Save $B when you spend $Y, or Save $C when you spend $Z or more in qualifying purchases in a single transaction using your just for U® account ($X or $Y or $Z minimum purchase calculated after deduction of promotional and loyalty card savings, coupons and all other discounts, offers and savings). Offer is valid [INSERT START DATE] - [INSERT END DATE] only at participating Safeway and Albertsons stores located in Colorado, Nebraska, South Dakota, Eastern Wyoming, Farmington, NM and Aztec, NM. Qualifying purchases exclude: alcoholic beverages, tobacco products, fluid dairy, fuel, prescription items and co-payments, fishing/hunting licenses and tags, postage stamps, money orders/transfers, bus/commuter passes, amusement park/ski/event/lottery tickets, phone cards, gift cards/certificates and applicable taxes, bottle/container deposits and bag fees.' },
  { name: 'Portland', collapsed: true, description: 'Save $A when you spend $X, or Save $B when you spend $Y, or Save $C when you spend $Z or more in qualifying purchases in a single transaction using your just for U® account ($X or $Y or $Z minimum purchase calculated after deduction of promotional and loyalty card savings, coupons and all other discounts, offers and savings). Offer is valid [INSERT START DATE] - [INSERT END DATE] only at participating Safeway and Albertsons stores located in Colorado, Nebraska, South Dakota, Eastern Wyoming, Farmington, NM and Aztec, NM. Qualifying purchases exclude: alcoholic beverages, tobacco products, fluid dairy, fuel, prescription items and co-payments, fishing/hunting licenses and tags, postage stamps, money orders/transfers, bus/commuter passes, amusement park/ski/event/lottery tickets, phone cards, gift cards/certificates and applicable taxes, bottle/container deposits and bag fees.' },
  { name: 'Southern', collapsed: true, description: 'Save $A when you spend $X, or Save $B when you spend $Y, or Save $C when you spend $Z or more in qualifying purchases in a single transaction using your just for U® account ($X or $Y or $Z minimum purchase calculated after deduction of promotional and loyalty card savings, coupons and all other discounts, offers and savings). Offer is valid [INSERT START DATE] - [INSERT END DATE] only at participating Safeway and Albertsons stores located in Colorado, Nebraska, South Dakota, Eastern Wyoming, Farmington, NM and Aztec, NM. Qualifying purchases exclude: alcoholic beverages, tobacco products, fluid dairy, fuel, prescription items and co-payments, fishing/hunting licenses and tags, postage stamps, money orders/transfers, bus/commuter passes, amusement park/ski/event/lottery tickets, phone cards, gift cards/certificates and applicable taxes, bottle/container deposits and bag fees.' },
  { name: 'NorCal', collapsed: true, description: 'Save $A when you spend $X, or Save $B when you spend $Y, or Save $C when you spend $Z or more in qualifying purchases in a single transaction using your just for U® account ($X or $Y or $Z minimum purchase calculated after deduction of promotional and loyalty card savings, coupons and all other discounts, offers and savings). Offer is valid [INSERT START DATE] - [INSERT END DATE] only at participating Safeway and Albertsons stores located in Colorado, Nebraska, South Dakota, Eastern Wyoming, Farmington, NM and Aztec, NM. Qualifying purchases exclude: alcoholic beverages, tobacco products, fluid dairy, fuel, prescription items and co-payments, fishing/hunting licenses and tags, postage stamps, money orders/transfers, bus/commuter passes, amusement park/ski/event/lottery tickets, phone cards, gift cards/certificates and applicable taxes, bottle/container deposits and bag fees.' },
  { name: 'Hawaii', collapsed: true, description: 'Save $A when you spend $X, or Save $B when you spend $Y, or Save $C when you spend $Z or more in qualifying purchases in a single transaction using your just for U® account ($X or $Y or $Z minimum purchase calculated after deduction of promotional and loyalty card savings, coupons and all other discounts, offers and savings). Offer is valid [INSERT START DATE] - [INSERT END DATE] only at participating Safeway and Albertsons stores located in Colorado, Nebraska, South Dakota, Eastern Wyoming, Farmington, NM and Aztec, NM. Qualifying purchases exclude: alcoholic beverages, tobacco products, fluid dairy, fuel, prescription items and co-payments, fishing/hunting licenses and tags, postage stamps, money orders/transfers, bus/commuter passes, amusement park/ski/event/lottery tickets, phone cards, gift cards/certificates and applicable taxes, bottle/container deposits and bag fees.' },
  ];

  closeDisclaimerModalView() {
    this.modalRef.hide();
  }

  setClickedRow(index, collapsed, name) {
    this.selectedRow = index;
    this.objIndex = this.data.findIndex((obj => obj.name === name));
    this.data[this.objIndex].collapsed = !collapsed;
    this.podDetailsService.setDisclaimer(this.data[this.objIndex].description);
  }

}

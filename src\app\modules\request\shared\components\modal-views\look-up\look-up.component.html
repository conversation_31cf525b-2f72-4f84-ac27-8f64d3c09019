<div class="modal-header">
  <h4 class="modal-title pull-left">Look Up Image</h4>
  <button type="button" class="close pull-right" aria-label="Close" (click)="closeLookUpImageModalView()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body modal-xl">
  <div class="row">
    <div class="col-12 d-flex justify-content-center">
      <div class="col-8 d-flex input-group">
        <input type="text" class="form-control  border-right-0 rounded-left" id="search" name="search"
          autocomplete="off" [(ngModel)]="searchID" (keydown.backspace)="clean()" (focus)="onFocus()"
          (focusout)="onFocusOut()" (change)="lookUp(searchID)" placeholder="Search Image">
        <div class="input-group-prepend" *ngIf="searchID !== ''">
          <button class="input-group-text border-left-0 border-right-0  bg-white search-icon-border" (click)="clean()"
            [ngClass]="(borders == true) ? 'clear-borders-customized ':'clean-borders'">
            
          </button>
        </div>
        <div class="input-group-prepend mr-0">
          <button class="input-group-text border-left-0 rounded-right bg-white search-icon-border" (click)="lookUp(searchID)"
            [ngClass]="(borders == true) ? 'search-borders-customized ':'clean-borders'" id="basic-addon1">
            <span class="search-icon">
                  <img alt="" src="assets/icons/Grey-Search.svg" />
                </span>
          </button>
        </div>





      </div>



    </div>
  </div>

  <div class="row d-flex justify-content-around" *ngIf="lookUpImages.length >= 1">
    <div class="col-8 mt-5">
      <table class="table table-hover" aria-describedby="Lookup Image Table">
        <thead>
          <tr>
            <th class="text-center" scope="col">Action</th>
            <th class="text-center" scope="col">ImageID</th>
            <th class="text-center" scope="col">Preview</th>
          </tr>
        </thead>
        <tbody class="lookup-image text-center">
          <tr *ngFor="let imageID of lookUpImages; let i = index">
            <td>
              <button class="btn btn-outline-primary btn-sm" type="button" (click)="addImageID(imageID)">
                <span class="fas fa-plus resize-icon"></span>
              </button>
            </td>
            <td class="d-flex justify-content-center" style="font-size: 14px;">{{imageID}}</td>
            <td class="text-center"><img [src]=" urlImages  + imageID + urlParams | safeHtml  " width="60" height="60"
                alt=""></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>



</div>
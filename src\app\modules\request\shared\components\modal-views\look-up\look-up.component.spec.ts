import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LookUpComponent } from './look-up.component';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { NotificationService } from '@appServices/common/notification.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { of } from 'rxjs';

describe('LookUpComponent', () => {
    let component: LookUpComponent;
    let fixture: ComponentFixture<LookUpComponent>;
    let mockUploadImagesService: jasmine.SpyObj<UploadImagesService>;
    let mockInitialDataService: jasmine.SpyObj<InitialDataService>;
    let mockNotificationService: jasmine.SpyObj<NotificationService>;
    let mockModalRef: jasmine.SpyObj<BsModalRef>;

    beforeEach(async () => {
        mockUploadImagesService = jasmine.createSpyObj('UploadImagesService', ['getImagesGroupData', 'sendImage']);
        mockInitialDataService = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);
        mockNotificationService = jasmine.createSpyObj('NotificationService', ['showNotification']);
        mockModalRef = jasmine.createSpyObj('BsModalRef', ['hide']);

        await TestBed.configureTestingModule({
            declarations: [LookUpComponent],
            providers: [
                { provide: UploadImagesService, useValue: mockUploadImagesService },
                { provide: InitialDataService, useValue: mockInitialDataService },
                { provide: NotificationService, useValue: mockNotificationService },
                { provide: BsModalRef, useValue: mockModalRef },
            ],
        }).compileComponents();

        fixture = TestBed.createComponent(LookUpComponent);
        component = fixture.componentInstance;

        mockInitialDataService.getConfigUrls.and.returnValue('http://mock-api.com');
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should set borders to true on focus', () => {
        component.onFocus();
        expect(component.borders).toBeTrue();
    });

    it('should set borders to false on focus out', () => {
        component.onFocusOut();
        expect(component.borders).toBeFalse();
    });

    it('should clean searchID when clean is called', () => {
        component.searchID = 'test';
        component.clean();
        expect(component.searchID).toBe('');
    });

    it('should call getImagesGroupData and handle empty response in lookUp', () => {
        mockUploadImagesService.getImagesGroupData.and.returnValue(of([]));
        component.lookUp('123');
        expect(mockUploadImagesService.getImagesGroupData).toHaveBeenCalledWith('123');
        expect(component.lookUpImages).toEqual([]);
        expect(component.message).toBe('Image not found');
        expect(component.status).toBe('error');
        expect(mockNotificationService.showNotification).toHaveBeenCalledWith('Image not found', 'error');
        expect(component.searchID).toBe('');
    });

    it('should call sendImage and show success notification in addImageID', () => {
        component.addImageID('123');
        expect(mockUploadImagesService.sendImage).toHaveBeenCalledWith('123');
        expect(component.message).toBe('123 added successfully');
        expect(component.status).toBe('success');
        expect(mockNotificationService.showNotification).toHaveBeenCalledWith('123 added successfully', 'success');
    });

    it('should close modal and reset fields in closeLookUpImageModalView', () => {
        component.searchID = 'test';
        component.lookUpImages = ['image1'];
        component.closeLookUpImageModalView();
        expect(mockModalRef.hide).toHaveBeenCalled();
        expect(component.lookUpImages).toEqual([]);
        expect(component.searchID).toBe('');
    });
});
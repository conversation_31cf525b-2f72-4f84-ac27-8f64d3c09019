import { Component, OnInit } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { NotificationService } from '@appServices/common/notification.service';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  // tslint:disable-next-line:component-selector
  selector: 'modal-view-look-up',
  templateUrl: './look-up.component.html',
  styleUrls: ['./look-up.component.scss']
})
export class LookUpComponent implements OnInit {
  searchID = '';
  loading: boolean;
  lookUpImages = [];
  borders = false;
  message = '';
  status = '';

  constructor(public uploadImagesService: UploadImagesService,
    private _apiConfigService: InitialDataService,
    public modalRef: BsModalRef,
    public notificationService: NotificationService) {
    // intentionally left empty
  }

  urlImages: string = this._apiConfigService.getConfigUrls(CONSTANTS.GET_IMAGE_API) + "/";
  urlParams = '?$ecom-product-card-desktop-jpg$';

  ngOnInit() {
    // intentionally left empty
  }

  lookUp(imageId) {
    this.loading = true;
    this.uploadImagesService.getImagesGroupData(imageId).subscribe(
      (data: []) => {
        this.lookUpImages = data;
        if (this.lookUpImages.length === 0) {
          this.message = 'Image not found';
          this.status = 'error';
          this.notificationService.showNotification(this.message, this.status);
          this.searchID = '';
        }
        this.loading = false;
      }
    );
  }

  onFocus() {
    this.borders = true;
  }
  onFocusOut() {
    this.borders = false;
  }
  clean() {
    this.searchID = '';
  }

  addImageID(imageID) {
    this.uploadImagesService.sendImage(imageID);
    this.message = imageID + ' added successfully';
    this.status = 'success';
    this.notificationService.showNotification(this.message, this.status);
    setTimeout(() => {
      this.closeLookUpImageModalView();
    }, 500);
  }
  closeLookUpImageModalView() {
    this.modalRef.hide();
    this.lookUpImages = [];
    this.searchID = '';
  }
}

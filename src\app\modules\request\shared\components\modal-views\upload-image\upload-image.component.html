<div class="modal-header" onDragStart="return false;" ondragenter="return false;" ondragover="return false;"
  ondrop="return false;">
  <h4 class="modal-title pull-left">Upload Image</h4>
  <button type="button" class="close pull-right" aria-label="Close" (click)="closeUploadImageModalView()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body modal-xl" style="min-height: 350px;" onDragStart="return false;" ondragenter="return false;"
  ondragover="return false;" ondrop="return false;">
  <div class="row">
    <div class="col-md-3">
      <div appNgDropFiles [files]="files" (mouseOver)="isDrop = $event" [ngClass]="{'file-over': isDrop }"
        class="well drop-zone mb-4">
        <h4>Drop here your file</h4>
        <img src="assets/img/drop-images.png" alt="">
      </div>
      <input style="display: none" type="file" (change)="onFileChanged($event)" onclick="this.value=null" #fileInput>
      <button class="btn btn-outline-primary" [disabled]="files.length >= 1" (click)="fileInput.click()">Select
        File</button>
    </div>

    <div class="col-md-3 d-flex justify-content-center">
      <div class="preview-zone" *ngIf="files.length >= 1 && imgURL !== undefined">
        <img [src]="imgURL" height="209px" width="250px" alt="">
      </div>
      <div class="preview-zone" *ngIf="files.length >= 1 && imgURL === undefined">
        <div *ngFor="let file of files">
          <img [src]="file.imageURL" height="209px" width="250px" alt="">
        </div>
      </div>
      <div class="preview-zone" *ngIf="files.length === 0">
        <img [src]="assets/img/image-default.jpg" height="209px" width="250px" alt="">
      </div>
    </div>
    <div class="col-md-6">
      <div class="row justify-content-end align-items-end">
        <div class="col d-flex justify-content-end">
          <button (click)="uploadImages()" [disabled]="files.length == 0"
            class="btn btn-outline-primary mr-2">Upload</button>
          <button (click)="cleanFiles()" class="btn btn-outline-danger">Clear</button>
        </div>
      </div>
      <table class="table table-striped" aria-describedby="Upload Image Table">
        <thead>
          <tr>
            <th scope="col">Name</th>
            <th scope="col">Size MB</th>
          </tr>
        </thead>
        <tbody class="upload-image">
          <tr *ngFor="let file of files">
            <td>{{file.fileName }} </td>
            <td>{{file.file.size / 1024 / 1024 | number: '.2-2' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UploadImageComponent } from './upload-image.component';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { NotificationService } from '@appServices/common/notification.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { of, throwError } from 'rxjs';
import { FileItem } from '@appModels/file-item';

describe('UploadImageComponent', () => {
    let component: UploadImageComponent;
    let fixture: ComponentFixture<UploadImageComponent>;
    let uploadImagesService: jasmine.SpyObj<UploadImagesService>;
    let notificationService: jasmine.SpyObj<NotificationService>;
    let modalRef: jasmine.SpyObj<BsModalRef>;

    beforeEach(async () => {
        const uploadImagesServiceSpy = jasmine.createSpyObj('UploadImagesService', ['upload', 'setfiles', 'sendImage', 'sendSourceImage']);
        const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['showNotification']);
        const modalRefSpy = jasmine.createSpyObj('BsModalRef', ['hide']);

        await TestBed.configureTestingModule({
            declarations: [UploadImageComponent],
            providers: [
                { provide: UploadImagesService, useValue: uploadImagesServiceSpy },
                { provide: NotificationService, useValue: notificationServiceSpy },
                { provide: BsModalRef, useValue: modalRefSpy }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(UploadImageComponent);
        component = fixture.componentInstance;
        uploadImagesService = TestBed.inject(UploadImagesService) as jasmine.SpyObj<UploadImagesService>;
        notificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
        modalRef = TestBed.inject(BsModalRef) as jasmine.SpyObj<BsModalRef>;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should upload image successfully', () => {
        const mockFile = new FileItem(new File([''], 'test.png', { type: 'image/png' }));
        component.files.push(mockFile);
        const mockResponse = [{ imageId: 'test.png' }];
        uploadImagesService.upload.and.returnValue(of(mockResponse));

        component.uploadImages();

        expect(uploadImagesService.upload).toHaveBeenCalledWith(mockFile.file);
        expect(uploadImagesService.sendImage).toHaveBeenCalledWith('test');
        expect(uploadImagesService.sendSourceImage).toHaveBeenCalledWith('upload');
        expect(notificationService.showNotification).toHaveBeenCalledWith('test has been uploaded successfully', 'success');
        expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should handle upload image error', () => {
        const mockFile = new FileItem(new File([''], 'test.png', { type: 'image/png' }));
        component.files.push(mockFile);
        const mockError = { message: 'Upload failed' };
        uploadImagesService.upload.and.returnValue(throwError(mockError));

        component.uploadImages();

        expect(uploadImagesService.upload).toHaveBeenCalledWith(mockFile.file);
        expect(notificationService.showNotification).toHaveBeenCalledWith('Upload failed', 'error');
    });

    it('should not upload non-image file', () => {
        const mockFile = new FileItem(new File([''], 'test.txt', { type: 'text/plain' }));
        spyOn(component as any, '_fileCanBeUploaded').and.callThrough();
        component.onFileChanged({ target: { files: [mockFile.file] } });

        expect(notificationService.showNotification).toHaveBeenCalledWith('Please select an image', 'error');
        expect(component.files.length).toBe(0);
    });

    it('should not upload file larger than 3MB', () => {
        const largeFile = new FileItem(new File([new ArrayBuffer(4 * 1024 * 1024)], 'large.png', { type: 'image/png' }));
        spyOn(component as any, '_fileCanBeUploaded').and.callThrough();
        component.onFileChanged({ target: { files: [largeFile.file] } });

        expect(notificationService.showNotification).toHaveBeenCalledWith('This image is: 4.00MB please select a 3MB image max.', 'error');
        expect(component.files.length).toBe(0);
    });

    it('should close modal and clean files', () => {
        component.files.push(new FileItem(new File([''], 'test.png', { type: 'image/png' })));
        component.closeUploadImageModalView();

        expect(modalRef.hide).toHaveBeenCalled();
        expect(component.files.length).toBe(0);
        expect(component.imgURL).toBeUndefined();
    });

    it('should not add duplicate file', () => {
        const mockFile = new FileItem(new File([''], 'test.png', { type: 'image/png' }));
        component.files.push(mockFile);
        spyOn(component as any, '_fileSelected').and.callThrough();

        component.onFileChanged({ target: { files: [mockFile.file] } });

        expect(notificationService.showNotification).toHaveBeenCalledWith('test.png already added', 'duplicated');
        expect(component.files.length).toBe(1);
    });

    it('should not add a new file if files array is not empty', () => {
        const mockFile = new FileItem(new File([''], 'test.png', { type: 'image/png' }));
        component.files.push(mockFile);
        const event = { target: { files: [mockFile.file] } };

        component.onFileChanged(event);

        expect(component.files.length).toBe(1);
        expect(notificationService.showNotification).toHaveBeenCalledWith('test.png already added', 'duplicated');
    });

    it('should allow file size less than or equal to 3MB', () => {
        const smallFile = new FileItem(new File([new ArrayBuffer(2 * 1024 * 1024)], 'small.png', { type: 'image/png' }));
        spyOn(component as any, '_fileCanBeUploaded').and.callThrough();
        component.onFileChanged({ target: { files: [smallFile.file] } });

        expect(notificationService.showNotification).not.toHaveBeenCalledWith('This image is: 2.00MB please select a 3MB image max.', 'error');
        expect(component.files.length).toBe(1);
    });

    it('should initialize component', () => {
        spyOn(component, 'ngOnInit').and.callThrough();

        component.ngOnInit();

        expect(component.ngOnInit).toHaveBeenCalled();
    });
});
import { Component, OnInit } from '@angular/core';
import { FileItem } from '@appModels/file-item';
import { NotificationService } from '@appServices/common/notification.service';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-upload-image',
  templateUrl: './upload-image.component.html',
  styleUrls: ['./upload-image.component.scss']
})
export class UploadImageComponent implements OnInit {

  files: FileItem[] = [];
  imgURL: any;
  isDrop = false;
  imagePath;
  message = '';
  status = '';

  constructor(public uploadImagesService: UploadImagesService,
    public modalRef: BsModalRef,
    public notificationService: NotificationService,) {
    // intentionally left empty
  }

  ngOnInit() {
    // intentionally left empty
  }


  onFileChanged(event) {
    const newFile = new FileItem(event.target.files[0]);
    if (this._fileCanBeUploaded(newFile.file)) {
      if (this.files.length < 1) {
        const reader = new FileReader();
        this.imagePath = event.target.files;
        reader.readAsDataURL(event.target.files[0]);
        reader.onload = () => {
          this.imgURL = reader.result;
        };
        this.files.push(newFile);
        this.uploadImagesService.setfiles(this.files.length);
      } else {
        this.message = 'You can add only one file';
        this.status = 'error';
        this.notificationService.showNotification(this.message, this.status);
      }
    }
  }


  uploadImages() {

    this.uploadImagesService.upload(this.files[0].file).subscribe(
      data => {
        this.uploadImagesService.sendImage(data[0].imageId.substr(0, data[0].imageId.lastIndexOf('.')));
        this.uploadImagesService.sendSourceImage('upload');
        // tslint:disable-next-line:max-line-length
        this.message = data[0].imageId.substr(0, data[0].imageId.lastIndexOf('.')) + ' has been uploaded successfully';
        this.status = 'success';
        this.notificationService.showNotification(this.message, this.status);
        this.cleanFiles();
        this.closeUploadImageModalView();
      },
      error => {
        this.message = error.message;
        this.status = 'error';
        this.notificationService.showNotification(this.message, this.status);
      });
  }


  private _fileCanBeUploaded(file: File): boolean {
    return !this._fileSelected(file.name) && this._isImage(file.type) && this._size(file.size);
  }

  private _fileSelected(fileName: string): boolean {
    for (const file of this.files) {
      if (file.fileName === fileName) {
        this.message = fileName + ' already added';
        this.status = 'duplicated';
        this.notificationService.showNotification(this.message, this.status);
        return true;
      }
    }
    return false;
  }

  private _isImage(fileType: string): boolean {
    if (!fileType.startsWith('image')) {
      this.message = 'Please select an image';
      this.status = 'error';
      this.notificationService.showNotification(this.message, this.status);
    }
    return (fileType === '' || fileType === undefined) ? false : fileType.startsWith('image');
  }

  private _size(fileSize): boolean {
    const result = fileSize / 1024 / 1024;
    if (result > 3) {
      this.message = 'This image is: ' + result.toFixed(2) + 'MB' + ' please select a 3MB image max.';
      this.status = 'error';
      this.notificationService.showNotification(this.message, this.status);
      return false;
    } else {
      return true;
    }
  }

  closeUploadImageModalView() {
    this.modalRef.hide();
    this.cleanFiles();
  }

  cleanFiles() {
    this.files.pop();
    this.uploadImagesService.setfiles(this.files.length);
    this.imgURL = undefined;
  }

}

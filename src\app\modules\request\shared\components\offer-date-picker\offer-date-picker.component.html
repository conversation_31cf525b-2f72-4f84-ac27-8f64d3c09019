<section
  class="offer-request-container mb-5"
  [formGroup]="offerDateBuilderForm"
>
  <nav class="navbar background-header mb-6">
    <span>
      Date Range
    </span>
  </nav>
  <div class="fields-container">
    <div>
      <div class="row mb-6 mx-1 justify-content-left">
        <div class="col-5">
          <div class="position-relative">
            <label class="input-lbl font-weight-bold" for="startDate"
              >Start Date</label
            >
            <div class="input-group">
              <input
                onkeydown="return false"
                type="text"
                class="form-control form-control-lg optional input-background"
                id="startDate"
                name="startDate"
                autocomplete="off"
                formControlName="offerStartDate"
                [minDate]="minOfferStartDate"
                [bsConfig]="{
                  containerClass: colorTheme,
                  dateInputFormat: 'MM/DD/YYYY',
                  showWeekNumbers: false
                }"
                bsDatepicker
              />
            </div>
          </div>
        </div>
        <div class="col-5 offset-2">
          <div class="position-relative">
            <label class="input-lbl font-weight-bold" for="endDate"
              >End Date</label
            >
            <div class="input-group">
              <input
                onkeydown="return false"
                type="text"
                class="form-control form-control-lg optional input-background"
                id="endDate"
                name="endDate"
                autocomplete="off"
                formControlName="offerEndDate"
                [minDate]="minOfferEndDate"
                [bsConfig]="{
                  containerClass: colorTheme,
                  dateInputFormat: 'MM/DD/YYYY'
                }"
                bsDatepicker
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<div class="text-danger" *ngIf="!showErrors">
  {{ errorMessage }}
</div>
<div class="row">
  <div class="col d-flex mt-3 justify-content-end">
    <button class="btn btn-link request-cancel mr-4" (click)="exit($event)">
      Cancel
    </button>
    <button
      class="btn btn-primary mb-4 font-weight-bolder submit-btn"
      (click)="offerDateSave()"
    >
      Save
    </button>
  </div>
</div>

<ng-template #successTmpl>
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <div class="row">
        <button
          type="button"
          class="close pull-right"
          aria-label="Close"
          (click)="onSuccessHandler()"
        >
          <span class="font-weight-lighter close-icon" aria-hidden="true"
            >&times;</span
          >
        </button>
      </div>
    </div>
  </div>
  <div class="modal-body pt-0 pr-5 pl-5">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <div class="col row justify-content-center">
            <h2 class="success-text">{{ showSuccessErrors }}</h2>
          </div>
          <div class="col row justify-content-center mt-4">
            <button
              type="button"
              class="btn btn-primary font-weight-bolder submit-offer-dates"
              aria-label="Close"
              (click)="onSuccessHandler()"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { OfferDatePickerComponent } from './offer-date-picker.component';
import { UntypedFormBuilder } from '@angular/forms';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import * as moment from 'moment';
import { BehaviorSubject, of, throwError } from 'rxjs';
import { TemplateRef, EventEmitter } from '@angular/core';

describe('OfferDatePickerComponent', () => {
    let component: OfferDatePickerComponent;
    let fixture: ComponentFixture<OfferDatePickerComponent>;

    let requestIdsListSelectedSubject: BehaviorSubject<any[]>;
    let isAllBatchSelectedSubject: BehaviorSubject<string>;
    let hideApiErrorOnRequestHomeSubject: BehaviorSubject<boolean>;
    let isSelectionResetSubject: BehaviorSubject<boolean>;
    let offerBulkSelectionSubject: BehaviorSubject<any>;

    let onHideEmitter: EventEmitter<string>;

    let bulkUpdateServiceStub: Partial<BulkUpdateService>;
    let queryGeneratorStub: Partial<QueryGenerator>;
    let searchOfferRequestServiceStub: Partial<SearchOfferRequestService>;
    let modalServiceStub: Partial<BsModalService>;
    let toastrServiceStub: Partial<ToastrService>;

    beforeEach(async () => {
        requestIdsListSelectedSubject = new BehaviorSubject<any[]>([]);
        isAllBatchSelectedSubject = new BehaviorSubject<string>('');
        hideApiErrorOnRequestHomeSubject = new BehaviorSubject<boolean>(false);
        isSelectionResetSubject = new BehaviorSubject<boolean>(false);
        offerBulkSelectionSubject = new BehaviorSubject<any>(null);
        spyOn(isSelectionResetSubject, 'next');

        onHideEmitter = new EventEmitter<string>();

        bulkUpdateServiceStub = {
            requestIdsListSelected$: requestIdsListSelectedSubject,
            isAllBatchSelected: isAllBatchSelectedSubject,
            checkIfActionEnabledForUniversalJob: jasmine.createSpy('checkIfActionEnabledForUniversalJob').and.returnValue(false),
            bulkAssignedDatesUJ: jasmine.createSpy('bulkAssignedDatesUJ').and.returnValue(of({ jobId: '123' })),
            bulkAssignedDates: jasmine.createSpy('bulkAssignedDates').and.returnValue(of({
                message: "UPDATED",
                "Successful Offer Request Updates": 5,
                "Attempted Offer Request Updates": 5
            })),
            hideApiErrorOnRequestHome$: hideApiErrorOnRequestHomeSubject,
            isSelectionReset: isSelectionResetSubject,
            requestIdArr: [],
            offerBulkSelection: offerBulkSelectionSubject
        };

        queryGeneratorStub = {
            getQueryWithFilter: jasmine.createSpy('getQueryWithFilter').and.returnValue('filterQuery'),
            getQuery: jasmine.createSpy('getQuery').and.returnValue('originalQuery'),
            removeParam: jasmine.createSpy('removeParam'),
            setQuery: jasmine.createSpy('setQuery')
        };

        searchOfferRequestServiceStub = {
            searchAllOfferRequest: jasmine.createSpy('searchAllOfferRequest').and.returnValue(of({ pagination: true })),
            getOfferDetails: jasmine.createSpy('getOfferDetails')
        };

        modalServiceStub = {
            show: jasmine.createSpy('show').and.callFake((template: TemplateRef<any>, options: any) => {
                // Provide a stub that includes both hide and setClass as a function.
                return {
                    hide: jasmine.createSpy('hide'),
                    setClass: jasmine.createSpy('setClass')
                } as unknown as BsModalRef<any>;
            }),
            onHide: onHideEmitter
        };

        toastrServiceStub = {
            success: jasmine.createSpy('success')
        };

        await TestBed.configureTestingModule({
            declarations: [OfferDatePickerComponent],
            providers: [
                UntypedFormBuilder,
                { provide: BulkUpdateService, useValue: bulkUpdateServiceStub },
                { provide: QueryGenerator, useValue: queryGeneratorStub },
                { provide: SearchOfferRequestService, useValue: searchOfferRequestServiceStub },
                { provide: BsModalService, useValue: modalServiceStub },
                { provide: ToastrService, useValue: toastrServiceStub },
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(OfferDatePickerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    describe('getAssignedDates', () => {
        it('should return both start and end dates when provided', () => {
            const startDate = new Date();
            const endDate = new Date();
            const dates = {
                offerStartDate: startDate,
                offerEndDate: endDate
            };

            const result = component.getAssignedDates(dates);
            expect(result.length).toBe(2);
            expect(result[0].type).toBe("OFFER_EFFECTIVE_START_DATE");
            expect(result[1].type).toBe("OFFER_EFFECTIVE_END_DATE");
        });

        it('should return only start date when only start date is provided', () => {
            const startDate = new Date();
            const dates = {
                offerStartDate: startDate,
                offerEndDate: null
            };

            const result = component.getAssignedDates(dates);
            expect(result.length).toBe(1);
            expect(result[0].type).toBe("OFFER_EFFECTIVE_START_DATE");
        });

        it('should return only end date when only end date is provided', () => {
            const endDate = new Date();
            const dates = {
                offerStartDate: null,
                offerEndDate: endDate
            };

            const result = component.getAssignedDates(dates);
            expect(result.length).toBe(1);
            expect(result[0].type).toBe("OFFER_EFFECTIVE_END_DATE");
        });
    });

    describe('openModal', () => {
        it('should open modal and subscribe to onHide', fakeAsync(() => {
            const dummyTemplate = {} as TemplateRef<any>;
            const options = { keyboard: true, class: "modal-lg" };
            component.datesUpdated = true;

            component.openModal(dummyTemplate, options);
            expect(modalServiceStub.show).toHaveBeenCalledWith(dummyTemplate, options);

            spyOn(component, 'searchAllOfferRequestPage');
            component.datesUpdated = true;
            (modalServiceStub.onHide as EventEmitter<string>).emit('reason');
            tick();
            expect(component.searchAllOfferRequestPage).toHaveBeenCalled();
        }));
    });

    describe('onSuccessHandler', () => {
        it('should hide modal and search offers if datesUpdated is true', () => {
            component.modalRef = {
                hide: jasmine.createSpy('hide'),
                setClass: jasmine.createSpy('setClass')
            } as unknown as BsModalRef<any>;
            spyOn(component, 'searchAllOfferRequestPage');
            component.datesUpdated = true;
            component.onSuccessHandler();
            expect(component.modalRef.hide).toHaveBeenCalled();
            expect(component.searchAllOfferRequestPage).toHaveBeenCalled();
            expect(isSelectionResetSubject.next).toHaveBeenCalledWith(true);
        });
    });

    describe('searchAllOfferRequestPage', () => {
        it('should call searchAllOfferRequest and then getOfferDetails', () => {
            component.searchAllOfferRequestPage();
            expect(searchOfferRequestServiceStub.searchAllOfferRequest).toHaveBeenCalled();
            expect(searchOfferRequestServiceStub.getOfferDetails).toHaveBeenCalled();
        });
    });

    describe('offerDateSave', () => {
        it('should set error if end date is before start date', () => {
            const startDate = moment('2025-02-28');
            const endDate = moment('2025-02-27');
            component.offerDateBuilderForm.setValue({
                offerStartDate: startDate.toDate(),
                offerEndDate: endDate.toDate()
            });
  
            component.offerDateSave();
            expect(component.showErrors).toBeNull();
            expect(component.errorMessage).toContain("The update was not made. The end date comes before the start date.");
        });

        it('should call bulkAssignedDatesUJ when universal job is enabled', () => {
            const startDate = moment('2025-02-27');
            const endDate = moment('2025-02-28');
            component.offerDateBuilderForm.setValue({
                offerStartDate: startDate.toDate(),
                offerEndDate: endDate.toDate()
            });
      
            (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(true);
            component.isAllBatchSelected = "selectAcrossSomePages";

            component.offerDateSave();
            expect(bulkUpdateServiceStub.bulkAssignedDatesUJ).toHaveBeenCalled();
            expect(toastrServiceStub.success).toHaveBeenCalledWith(
                jasmine.stringMatching(/Offer request\(s\) dates are updating/),
                '',
                { timeOut: 3000, closeButton: true }
            );
        });

        it('should call bulkAssignedDates when universal job is not enabled', fakeAsync(() => {
            const startDate = moment('2025-02-27');
            const endDate = moment('2025-02-28');
            component.offerDateBuilderForm.setValue({
                offerStartDate: startDate.toDate(),
                offerEndDate: endDate.toDate()
            });
      
            (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(false);
            component.isAllBatchSelected = "selectAcrossSomePages";

            component.offerDateSave();
            tick();
            expect(bulkUpdateServiceStub.bulkAssignedDates).toHaveBeenCalled();
            expect(component.showSuccessErrors).toContain("Dates were");
        }));
    });

    describe('exit', () => {
        it('should emit cancelClick event', () => {
            spyOn(component.cancelClick, 'emit');
            component.exit(0);
            expect(component.cancelClick.emit).toHaveBeenCalledWith(0);
        });
    });

    describe('ngOnDestroy', () => {
        it('should trigger hideApiErrorOnRequestHome and reset selections if datesUpdated is true', () => {
            component.datesUpdated = true;
            component.ngOnDestroy();
            expect(hideApiErrorOnRequestHomeSubject.value).toBeFalse();
            expect(bulkUpdateServiceStub.requestIdArr.length).toBe(0);
        });

        it('should trigger hideApiErrorOnRequestHome even if datesUpdated is false', () => {
            component.datesUpdated = false;
            component.ngOnDestroy();
            expect(hideApiErrorOnRequestHomeSubject.value).toBeFalse();
        });
    });

    it('should update query using queryGenerator for selectAcrossAllPages branch', fakeAsync(() => {
        const startDate = moment('2025-02-27');
        const endDate = moment('2025-02-28');
        component.offerDateBuilderForm.setValue({
            offerStartDate: startDate.toDate(),
            offerEndDate: endDate.toDate()
        });

        component.isAllBatchSelected = "selectAcrossAllPages";
  
        (queryGeneratorStub.getQuery as jasmine.Spy).and.returnValues("originalQuery", "modifiedQuery;");
        (queryGeneratorStub.getQueryWithFilter as jasmine.Spy).and.returnValue("");

        component.reqIdList = "dummyReqIdList";

        (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(false);

        component.offerDateSave();
        tick();

        expect(queryGeneratorStub.removeParam).toHaveBeenCalledWith('limit');
        expect(queryGeneratorStub.removeParam).toHaveBeenCalledWith('sid');
        expect(queryGeneratorStub.removeParam).toHaveBeenCalledWith('next');

        expect(queryGeneratorStub.setQuery).toHaveBeenCalledWith("originalQuery");

        const bulkCallArgs = (bulkUpdateServiceStub.bulkAssignedDates as jasmine.Spy).calls.mostRecent().args;
        const queryArg = bulkCallArgs[1];
        expect(queryArg.query).toBe("modifiedQuery");
    }));

    it('should set showSuccessErrors for UPDATE_FAILED response', fakeAsync(() => {
        const startDate = moment('2025-02-27');
        const endDate = moment('2025-02-28');
        component.offerDateBuilderForm.setValue({
            offerStartDate: startDate.toDate(),
            offerEndDate: endDate.toDate()
        });

        (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(false);
        component.isAllBatchSelected = "selectSome";

        spyOn(component, 'exit');
        spyOn(component, 'openModal');

        (bulkUpdateServiceStub.bulkAssignedDates as jasmine.Spy).and.returnValue(of({
            message: "UPDATE_FAILED",
            "Successful Offer Request Updates": 3,
            "Attempted Offer Request Updates": 5
        }));

        component.offerDateSave();
        tick();

        expect(component.exit).toHaveBeenCalledWith(0);
        expect(component.openModal).toHaveBeenCalled();
        expect(component.showErrors).toBeNull();
        expect(component.showSuccessErrors).toBe(
            "Dates were updated on 3 of the 5 offer requests selected. Updates were not made when it resulted in an invalid date range or if the offer request was being edited."
        );
    }));

    it('should trigger hideApiErrorOnRequestHome$.next(true) when bulkAssignedDates errors', fakeAsync(() => {
        const startDate = moment('2025-02-27');
        const endDate = moment('2025-02-28');
        component.offerDateBuilderForm.setValue({
            offerStartDate: startDate.toDate(),
            offerEndDate: endDate.toDate()
        });
  
        (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(false);
        component.isAllBatchSelected = "selectSome";
  
        (bulkUpdateServiceStub.bulkAssignedDates as jasmine.Spy).and.returnValue(throwError('test error'));
  
        component.offerDateSave();
        tick();
  
        expect(hideApiErrorOnRequestHomeSubject.value).toBe(true);
    }));

    it('should set showSuccessErrors message in the else branch when response message is UPDATED but updateSuccess is false', fakeAsync(() => {
        const startDate = moment('2025-02-27');
        const endDate = moment('2025-02-28');
        component.offerDateBuilderForm.setValue({
            offerStartDate: startDate.toDate(),
            offerEndDate: endDate.toDate()
        });

        (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(false);
        component.isAllBatchSelected = "selectSome";

        spyOn(component, 'exit');
        spyOn(component, 'openModal');

        (bulkUpdateServiceStub.bulkAssignedDates as jasmine.Spy).and.returnValue(of({
            message: "UPDATED",
            "Successful Offer Request Updates": 3,
            "Attempted Offer Request Updates": 5
        }));

        component.offerDateSave();
        tick();

        expect(component.exit).toHaveBeenCalledWith(0);
        expect(component.openModal).toHaveBeenCalled();
        expect(component.showErrors).toBeNull();
        expect(component.showSuccessErrors).toBe(
            "Dates were updated on 3 of the 5 offer requests selected. Updates were not made when it resulted in an invalid date range or if the offer request was being edited."
        );
    }));
    
    it('should call hideApiErrorOnRequestHome$.next(true) when bulkAssignedDatesUJ errors in the universal job branch', fakeAsync(() => {
        const startDate = moment('2025-02-27');
        const endDate = moment('2025-02-28');
        component.offerDateBuilderForm.setValue({
            offerStartDate: startDate.toDate(),
            offerEndDate: endDate.toDate()
        });
  
        component.reqIdList = "dummyQuery";
        component.isAllBatchSelected = "selectSome";
  
        (queryGeneratorStub.getQueryWithFilter as jasmine.Spy).and.returnValue("");
  
        (bulkUpdateServiceStub.checkIfActionEnabledForUniversalJob as jasmine.Spy).and.returnValue(true);
  
        (bulkUpdateServiceStub.bulkAssignedDatesUJ as jasmine.Spy).and.returnValue(throwError('error occurred'));
  
        component.offerDateSave();
        tick();
  
        expect(hideApiErrorOnRequestHomeSubject.value).toBe(true);
    }));

});

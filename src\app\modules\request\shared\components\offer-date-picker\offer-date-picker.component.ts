import { Component, EventEmitter, OnD<PERSON>roy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { dateInOriginalFormat } from "@appUtilities/date.utility";
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'offer-date-picker',
  templateUrl: './offer-date-picker.component.html',
  styleUrls: ['./offer-date-picker.component.scss']
})
export class OfferDatePickerComponent extends UnsubscribeAdapter implements OnInit, OnDestroy {
  offerDateBuilderForm = new UntypedFormGroup({
    offerStartDate: new UntypedFormControl(),
    offerEndDate: new UntypedFormControl()
  });
  colorTheme = 'theme-dark-blue';
  offerStartDate: any;
  offerEndDate: any;
  showErrors: any = false;
  minOfferStartDate: Date = new Date();
  minOfferEndDate: Date = new Date();
  showSuccessErrors;
  errorMessage: string;
  reqIdList;
  reqIdCount = 0;
  modalRef: BsModalRef;
  @Output() cancelClick = new EventEmitter();
  @ViewChild("successTmpl")
  private _successTmpl: TemplateRef<any>;
  datesUpdated: boolean = false;
  isAllBatchSelected: string;

  constructor(
    public formBuilder: UntypedFormBuilder,
    private blukService: BulkUpdateService,
    private queryGenerator: QueryGenerator,
    private _searchOfferRequestService: SearchOfferRequestService,
    private _modalService: BsModalService,
    private toastr: ToastrService) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.blukService.requestIdsListSelected$.subscribe((reqIdList) => {
      this.reqIdCount = reqIdList.length;
      this.reqIdList = "requestId=(" + reqIdList.join(' OR ') + ")";
    });
    this.blukService.isAllBatchSelected.subscribe((value) => {
      this.isAllBatchSelected = value;
    });
  }


  getAssignedDates(dates) {

    let assignedDates: any = [];
    if (dates.offerStartDate && dates.offerEndDate) {
      assignedDates.push({
        date: dateInOriginalFormat({ date: moment(dates.offerStartDate), isStartDate: true }),
        type: "OFFER_EFFECTIVE_START_DATE"
      },
        {
          date: dateInOriginalFormat({ date: moment(dates.offerEndDate), isStartDate: false }),
          type: "OFFER_EFFECTIVE_END_DATE"
        })
    } else if (dates.offerStartDate) {
      assignedDates.push({
        date: dateInOriginalFormat({ date: moment(dates.offerStartDate), isStartDate: true }),
        type: "OFFER_EFFECTIVE_START_DATE"
      })
    } else if (dates.offerEndDate) {
      assignedDates.push({
        date: dateInOriginalFormat({ date: moment(dates.offerEndDate), isStartDate: false }),
        type: "OFFER_EFFECTIVE_END_DATE"
      })
    }
    return assignedDates;
  }

  openModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
    this._modalService.onHide.subscribe((reason: string) => {
      if (reason && this.datesUpdated) {
        this.datesUpdated = false;
        this.searchAllOfferRequestPage();
      }
    })
  }
  onSuccessHandler() {
    this.modalRef.hide();
    if (this.datesUpdated) {
      this.datesUpdated = false;
      this.searchAllOfferRequestPage();
    }
    this.blukService.isSelectionReset.next(true);
  }
  searchAllOfferRequestPage() {
    this._searchOfferRequestService
      .searchAllOfferRequest(this.queryGenerator.getQuery(), false, this.queryGenerator.getQueryWithFilter())
      .subscribe((result: any) => {
        result.pagination = true;
        this._searchOfferRequestService.getOfferDetails(result);
      });
  }
  offerDateSave() {
    let dates = {
      offerStartDate: this.offerDateBuilderForm.value.offerStartDate,
      offerEndDate: this.offerDateBuilderForm.value.offerEndDate
    }

    const assignDates = this.getAssignedDates(dates);

    if (dates.offerStartDate && dates.offerEndDate) {
      this.showErrors = !(moment(dates.offerEndDate).diff(moment(dates.offerStartDate), "days") >= 0);
    }

    let query: any = {}, queryGenerator, originalQuery;
    const queryWithFilter = this.queryGenerator.getQueryWithFilter();
    if (queryWithFilter.length) {
      query.queryWithOrFilters = queryWithFilter;
    }

    if (this.isAllBatchSelected === "selectAcrossAllPages") {
      const originalQuery = this.queryGenerator.getQuery();
      this.queryGenerator.removeParam('limit');
      this.queryGenerator.removeParam('sid');
      this.queryGenerator.removeParam('next');
      queryGenerator = this.queryGenerator.getQuery();
      this.queryGenerator.setQuery(originalQuery);
      queryGenerator = queryGenerator.slice(0, queryGenerator.length - 1);
    } else {
      queryGenerator = this.reqIdList;
    }

    query.query = queryGenerator;


    if (query && !this.showErrors) {
      
      const isUniversalJobEnabledForAction = this.blukService.checkIfActionEnabledForUniversalJob("UpdateOfferDates");
      if(isUniversalJobEnabledForAction)
      {
        this.blukService
        .bulkAssignedDatesUJ(assignDates, query,"OR","UPDATE_OFFER_DATES",CONSTANTS.SC)
        .subscribe((response: any) => {
          this.exit(0);
          this.datesUpdated = true;
          
          if(response.jobId !== null && response.jobId.length > 0)
          {
            this.toastr.success(`Offer request(s) dates are updating`, '',                 {
              timeOut: 3000,
              closeButton: true
            });
          }

        }, (error: any) => {
          if (error) {
            this.blukService.hideApiErrorOnRequestHome$.next(true);
          }
        });

      }
      else{
      this.blukService
        .bulkAssignedDates(assignDates, query)
        .subscribe((response: any) => {
          this.exit(0);
          this.datesUpdated = true;
          this.openModal(this._successTmpl, {
            keyboard: true,
            class: "modal-lg",
          });

          if (response.message === "UPDATE_FAILED") {
            this.showErrors = null;
            (this.showSuccessErrors = `Dates were updated on ${response["Successful Offer Request Updates"]} of the ${response['Attempted Offer Request Updates']} offer requests selected. Updates were not made when it resulted in an invalid date range or if the offer request was being edited.`);
          } else if (response.message === "UPDATED") {
            let updateSuccess = (response['Attempted Offer Request Updates'] === response['Successful Offer Request Updates']);
            if (updateSuccess) {

              this.showErrors = null;
              (this.showSuccessErrors = `Dates were added or updated on ${response["Successful Offer Request Updates"]} offer requests selected.`)
            } else {
              this.showErrors = null;
              (this.showSuccessErrors = `Dates were updated on ${response["Successful Offer Request Updates"]} of the ${response["Attempted Offer Request Updates"]} offer requests selected. Updates were not made when it resulted in an invalid date range or if the offer request was being edited.`);
            }
          }

        }, (error: any) => {
          if (error) {
            this.blukService.hideApiErrorOnRequestHome$.next(true);
          }
        });
      }

    } else {
      this.showErrors = null;
      this.errorMessage = "The update was not made. The end date comes before the start date.";
    }
  }

  exit($event) {
    this.cancelClick.emit($event);
  }
  ngOnDestroy(): void {
    this.blukService.hideApiErrorOnRequestHome$.next(false);
    if (this.datesUpdated) {
      this.blukService.requestIdsListSelected$.next([]);
      this.blukService.offerBulkSelection.next(null);
      this.blukService.isSelectionReset.next(true);
      this.blukService.requestIdArr = [];
    }
  }
}
<div
  [class]="
    showOfferList
      ? 'list-expanded list-item-container p-2 pr-0 mb-2 d-flex flex-column container-fluid'
      : 'list-collapsed list-item-container p-2 pr-0 mb-2 container-fluid d-flex flex-column '
  "
>
  <!-- data -->
  <div class="row">
    <div class="cb-width">
      <div class="request-checkbox">
        <label class="mx-2 mb-0" for="input-checkbox{{ offerRequest.info.id }}">
          <input
            type="checkbox"
            class="form-checkbox dark-checkbox"
            id="input-checkbox{{ offerRequest.info.id }}"
            tabindex="0"
            [checked]="isSelected"
            [disabled]="bulkSelection"
            [class.isDisabled]="bulkSelection"
            (change)="selectIndividualRequest($event, offerRequest)"
            [ngClass]="bulkOptionsCheckBoxcssBasedOnPermissions"
          />
        </label>
      </div>
    </div>

    <div
      class="col row"
      *ngIf="facetItemService.programCodeSelected === CONSTANTS.SC"
    >
      <div class="col-10">
        <!-- row 2 -->
        <div class="row">
          <div class="col-3">
            <a class="requestIdLink" 
            [routerLink]="getSummaryPage(offerRequest.info.id)" [queryParams]="getQueryParams()">
              <label class="bold-label">{{ offerRequest.info.id }}</label>
              <br/>
            </a>
          </div>
          <div class="col-2">
            <label class="text-label">{{
              configData.offerDeliveryChannels[
                offerRequest.info.deliveryChannel
              ]
            }}</label>
          </div>
          <div class="col-2">
            <label class="text-label">{{
              offerTypes[offerRequest.info.offerRequestType]
            }}</label>
          </div>
          <div class="col-2">
            <div *ngIf="digitalStatus" class="p-0">
              <label [ngClass]="digitalStatus.className">
                {{ digitalStatus.status }}
              </label>
            </div>
          </div>
          <div class="col-3">
            <label
              class="text-label"
              *ngIf="
                offerRequest.info.digitalUser &&
                offerRequest.info.digitalUser.firstName
              "
            >
              Digital({{ digitalCount }}/{{ totalDigitalCount }}):
              {{
                offerRequest.info.digitalUser &&
                offerRequest.info.digitalUser.firstName
                  ? offerRequest.info.digitalUser.firstName
                  : ""
              }}
              {{
                offerRequest.info.digitalUser &&
                offerRequest.info.digitalUser.lastName
                  ? offerRequest.info.digitalUser.lastName
                  : ""
              }}
            </label>
          </div>
        </div>
        <!-- row 2 -->
        <div class="row">
          <div class="col-3">
            <label class="text-label">{{
              offerRequest.createdUser && offerRequest.createdUser.userId
                ? offerRequest.createdUser.firstName +
                  " " +
                  offerRequest.createdUser.lastName
                : "No Requestor"
            }}</label>
          </div>
          <div class="col-2">
            <label class="text-label">{{
              offerRequest.info.brandAndSize
            }}</label>
          </div>
          <div class="col-2">
            <label class="text-label" *ngIf="sDate && endDate"
              >{{ sDate }} - {{ endDate }}
            </label>
          </div>
          <div class="col-2">
            <div *ngIf="nonDigitalStatus" class="p-0"></div>
            <label
              *ngIf="nonDigitalStatus"
              [ngClass]="nonDigitalStatus.className"
            >
              {{ nonDigitalStatus.status }}</label
            >
          </div>
          <div class="col-3">
            <label
              class="text-label"
              *ngIf="
                offerRequest.info.nonDigitalUser &&
                offerRequest.info.nonDigitalUser.firstName
              "
            >
              Non Digital({{ nonDigitalCount }}/{{ totalNonDigitalCount }}):
              {{
                offerRequest.info.nonDigitalUser &&
                offerRequest.info.nonDigitalUser.firstName
                  ? offerRequest.info.nonDigitalUser.firstName
                  : ""
              }}
              {{
                offerRequest.info.nonDigitalUser &&
                offerRequest.info.nonDigitalUser.lastName
                  ? offerRequest.info.nonDigitalUser.lastName
                  : ""
              }}
            </label>
          </div>
        </div>
      </div>
      <!-- actions and Expand -->
      <div
        class="col-2 row clearfix p-0 justify-content-end align-content-center"
      >
        <div class="col" style="text-align: right">
          <app-actions-and-more
            [action]="'Manage'"
            [type]="'Actions'"
            [page]="'Manage'"
            [module]="'offerRequest'"
            [payload]="offerRequest"
          ></app-actions-and-more>
        </div>
        <div class="mx-xl-3 mx-sm-1" style="width: 13px">
          <div *ngIf="!showOfferList; else collapsediv">
            <a (click)="expandAction()" [class.d-none]="offersData.length === 0"
              ><label class="mt-2 text-label">
                <img src="assets/icons/arrow-icon-down.svg" alt="" />
              </label>
            </a>
          </div>

          <ng-template #collapsediv>
            <a
              (click)="collapseAction($event)"
              [class.d-none]="offersData.length === 0"
              ><label class="mt-2 text-label"
                ><img src="assets/icons/arrow-icon-up.svg" alt=""
              /></label>
            </a>
          </ng-template>
        </div>
      </div>
    </div>

    <div
      class="col row"
      *ngIf="
        [CONSTANTS.GR, CONSTANTS.SPD].includes(
          facetItemService.programCodeSelected
        )
      "
    >
      <div class="col-10">
        <!-- row 2 -->
        <div class="row">
          <div class="col-2">
            <a
              class="requestIdLink"
              [routerLink]="getSummaryPage(offerRequest.info.id)" [queryParams]="getQueryParams()"
              ><label class="bold-label">{{ offerRequest.info.id }} </label><br
            /></a>
          </div>
          <div class="col-2">
            <label class="text-label"
              >{{ offerRequest.info.regionId }}
              {{ offerRequest.info.regionName }}</label
            >
          </div>
          <div class="col-3">
            <label class="text-label" class="word-break">{{
              offerRequest.info.brandAndSize
            }}</label>
          </div>
          <div class="col-2">
            <label class="text-label">
              {{ offerRequest.info.programType }}
            </label>
          </div>
          <div class="col-3">
            <div *ngIf="digitalStatus" class="p-0">
              <label [ngClass]="digitalStatus.className">
                {{ digitalStatus.status }}</label
              >
            </div>
          </div>
        </div>
        <!-- row 2 -->
        <div class="row">
          <div class="col-2">
            <label class="text-label">{{ offerRequest.info.mobId }}</label>
          </div>
          <div class="col-2">
            <label class="text-label">{{
              offerRequest.createdUser && offerRequest.createdUser.userId
                ? offerRequest.createdUser.firstName +
                  " " +
                  offerRequest.createdUser.lastName
                : "No Requestor"
            }}</label>
          </div>
          <div class="col-3"></div>
          <div
            class="col-2"
            *ngIf="facetItemService.programCodeSelected === CONSTANTS.GR"
          >
            <label class="text-label">
              {{ offerRequest.rules.pointsRequired }}
            </label>
          </div>
          <div
            class="col-2"
            *ngIf="facetItemService.programCodeSelected === CONSTANTS.SPD"
          >
            <label class="text-label" class="word-break">
              {{ offerRequest.info.programSubType }}
            </label>
          </div>
          <div class="col-3">
            <label class="text-label" *ngIf="sDate && endDate"
              >{{ sDate }} - {{ endDate }}</label
            >
          </div>
        </div>
      </div>
      <!-- actions and Expand -->
      <div
        class="col-2 row clearfix p-0 justify-content-end align-content-center"
      >
        <div class="col" style="text-align: right">
          <app-actions-and-more
            [action]="'Manage'"
            [type]="'Actions'"
            [page]="'Manage'"
            [module]="'offerRequest'"
            [payload]="offerRequest"
          ></app-actions-and-more>
        </div>
        <div class="mx-xl-3 mx-sm-1" style="width: 13px">
          <div *ngIf="!showOfferList; else collapsediv">
            <a (click)="expandAction()" [class.d-none]="offersData.length === 0"
              ><label class="mt-2 text-label">
                <img src="assets/icons/arrow-icon-down.svg" alt="" />
              </label>
            </a>
          </div>

          <ng-template #collapsediv>
            <a
              (click)="collapseAction($event)"
              [class.d-none]="offersData.length === 0"
              ><label class="mt-2 text-label"
                ><img src="assets/icons/arrow-icon-up.svg" alt=""
              /></label>
            </a>
          </ng-template>
        </div>
      </div>
    </div>

    <div class="col row" *ngIf="[CONSTANTS.BPD].includes(facetItemService.programCodeSelected)">
      <div class="col-10">
        <!-- row 2 -->
        <div class="row">
          <div class="col-2">
            <a class="requestIdLink"  [routerLink]="getSummaryPage(offerRequest.info.id)" [queryParams]="getQueryParams()"><label
                class="bold-label">{{ offerRequest.info.id }} </label><br /></a>
          </div>
          <div class="col-2">
            <label class="text-label">{{ offerRequest.info.regionId }} {{offerRequest.info.regionName}}</label>
          </div>
          <div class="col-2">
            <label class="text-label" class="word-break">{{ offerRequest.info.brandAndSize }}</label>
          </div>
          <div class="col-2">
              <label class="text-label">
               {{offerRequest.info.programType}}
              </label>
            </div>
            <div class="col-2">
              <label class="text-label">
               {{offerRequest.info.category}}
              </label>
            </div>
          <div class="col-2">
            <div *ngIf="digitalStatus" class="p-0">
              <label [ngClass]="digitalStatus.className"> {{ digitalStatus.status }}</label>
            </div>
          </div>
          
        </div>
        <!-- row 2 -->
        <div class="row">
          
          <div class="col-2">
            <label class="text-label">{{ offerRequest.info.mobId }}</label>
          </div>
          <div class="col-2">
              <label class="text-label">
                {{offerRequest.info.repUpc}}
                </label>
            </div>
            <div class="col-2">
              {{offerRequest.info.cpg}}
          </div>
          <div class="col-2">
              <label class="text-label">
                 {{offerRequest.info.bugm}}
              </label>
            </div>
            <div class="col-2" >
              <label class="text-label" class="word-break">
                {{offerRequest.info.categoryId}}
                  
              </label>
            </div>
          <div class="col-2">
            <label class="text-label" *ngIf="sDate && endDate">{{ sDate }} - {{ endDate }}</label>
            <label class="text-label" *ngIf="!sDate || !endDate">{{ offerRequest.info.period_week}}</label>
          </div>
        </div>
      </div>
      <!-- actions and Expand -->
      <div class="col-2 row clearfix p-0 justify-content-end align-content-center">
        <div class="col" style="text-align: right">
          <app-actions-and-more [action]="'Manage'" [type]="'Actions'" [page]="'Manage'" [module]="'offerRequest'"
            [payload]="offerRequest"></app-actions-and-more>
        </div>
        <div class="mx-xl-3 mx-sm-1" style="width: 13px;">
          <div *ngIf="!showOfferList; else collapsediv">
            <a (click)="expandAction()" [class.d-none]="offersData.length === 0"><label class="mt-2 text-label">
                <img src="assets/icons/arrow-icon-down.svg" alt="" />
              </label>
            </a>
          </div>
  
  
          <ng-template #collapsediv>
            <a (click)="collapseAction($event)" [class.d-none]="offersData.length === 0"><label
                class="mt-2 text-label"><img src="assets/icons/arrow-icon-up.svg" alt="" /></label>
            </a>
          </ng-template>
        </div>
      </div>
    </div>


  </div>

  <div
    *ngIf="
      showOfferList && facetItemService.programCodeSelected !== CONSTANTS.GR
    "
  >
    <div class="row mt-3">
      <div class="cb-width"></div>

      <ng-container *ngIf="offersData.length > 0; else noResults">
        <div class="col row">
          <div class="col-10">
            <div class="row pb-1">
              <div class="col-3">
                <label class="bold-label">Offer ID</label>
              </div>
              <div class="col-2">
                <label class="bold-label">Store Groups</label>
              </div>
              <div class="col-2">
                <label class="bold-label">Product Group</label>
              </div>
              <div class="col-2">
                <label class="bold-label">Offer Status</label>
              </div>
              <div class="col-1">
                <label class="bold-label">POD</label>
              </div>
              <div class="col-2">
                <label class="bold-label">Mode</label>
              </div>
            </div>
          </div>
          <div class="col-2 p-0 justify-content-end">
            <div class="col p-0">
              <label class="bold-label">Discount Value</label>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="offers-wrap">
      <ng-container *ngFor="let offer of offersData; index as i">
        <request-list
          *ngIf="
            offer.externalOfferId.indexOf('-D') !== -1 ||
            offer.externalOfferId.indexOf('-ND') !== -1
          "
          [offer]="offer"
          [offerRequest]="offerRequest"
          [offersCriteria]="
            offerRequest.rules.qualificationAndBenefit.offerRequestOffers
          "
        ></request-list>
        <div class="col"></div>
      </ng-container>
    </div>

    <ng-template #noResults>
      <div class="row mt-3">
        <div class="col p-0 text-center">
          <p>No Offers Found</p>
        </div>
      </div>
    </ng-template>
  </div>
</div>

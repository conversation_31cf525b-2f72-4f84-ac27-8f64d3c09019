@import "../../../../../../scss/colors";
@import "../../../../../../scss/inputs.scss";
.actions-button {
  background-color: #ffffff !important;
  //color: #000000 !important;
  border-color: $grey-lighter-hex !important;
}
.dropdown-toggle::after {
  border: none;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+);
  vertical-align: 0;
  background-repeat: no-repeat;
}
.list-item-container {
  border: 1px solid #f0f4f7;
  background: #f0f4f7;
}
.list-collapsed {
  background-color: #f0f4f7;
}
.list-expanded {
  background-color: rgb(255, 255, 255);

  -moz-box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
  -webkit-box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
  box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
}
.bold-label {
  margin: 0px;
  font-weight: 700;
  font-size: 16px;
}
.text-label {
  margin: 0px;
  font-size: 16px;
  span {
    white-space: nowrap;
  }
}
.word-break {
  word-break: break-word
}
.offers-link {
  color: $theme-primary !important;
}
.offers-link-deactivated {
  color: $grey-light-rgb !important;
}

.digital-status-icon-red {
  color: $red-primary-rgb;
  font-size: 10px;
  padding-left: 34px;
}

.non-digital-status-icon-red {
  color: $red-primary-rgb;
  font-size: 10px;
  padding-left: 7px;
}

.digital-status-icon-green {
  color: $green-primary-hex;
  font-size: 10px;
  padding-left: 34px;
}

.non-digital-status-icon-green {
  color: $green-primary-hex;
  font-size: 10px;
  padding-left: 7px;
}

.green-status {
  width: 80px;
  text-align: center;
  border: 1px solid $green-primary-hex;
  color: $green-primary-hex;
  font-weight: 800;
}
.yellow-status {
  width: 80px;
  text-align: center;
  border: 1px solid #e79023;
  color: #e79023;
  font-weight: 800;
}
.purple-status {
  width: 80px;
  text-align: center;
  border: 1px solid #841fa9;
  color: #841fa9;
  font-weight: 800;
}
.blue-status {
  width: 80px;
  text-align: center;
  border: 1px solid #59b1e3;
  color: #59b1e3;
  font-weight: 800;
}
.red-status {
  width: 80px;
  text-align: center;
  border: 1px solid $red-primary-rgb;
  color: $red-primary-rgb;
  font-weight: 800;
}
.dropdown-menu {
  min-width: 7rem !important;
}
.dropdown-item {
  padding: 0.25rem 1rem;
}
.requestIdLink {
  color: $theme-primary !important;
}
.offers-wrap .offer-line:last-child {
  display: none;
}

.digital-value {
  color: $theme-primary !important;
}

.zero-digital-value {
  color: $grey-dark-hex !important;
}
.isDisabled {
  cursor: not-allowed;
  opacity: 0.4;
}


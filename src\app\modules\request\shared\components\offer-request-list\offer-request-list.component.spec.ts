import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { PermissionsConfigurationService, PermissionsModule, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BehaviorSubject, of } from 'rxjs';
import { OfferRequestListComponent } from './offer-request-list.component';



describe('OfferRequestListComponent', () => {
  let component: OfferRequestListComponent;
  let fixture: ComponentFixture<OfferRequestListComponent>;
  beforeEach(() => {
    const initialDataServiceStub = () => ({
      getAppData: () => ({ offerType: {} }),
      getAppDataName:()=>({offerType:()=>{}})
    });
    const routerStub = () => ({ navigateByUrl: newUrl => ({}) });
    const bulkUpdateServiceStub = () => ({
      bulkSelected$: { subscribe: f => f({}) },
      offerBulkSelection: { subscribe: f => f({}) },
      isAllBatchSelected: { next: () => ({}) },
      userTypeArray: {
        push: () => ({}),
        indexOf: () => ({}),
        includes: () => ({}),
        splice: () => ({})
      },
      deliveryChannelArr: {
        push: () => ({}),
        indexOf: () => ({}),
        includes: () => ({}),
        splice: () => ({})
      },
      bulkAssignedUsers: {
        digitalUser: { firstName: {}, lastName: {}, userId: {} },
        nonDigitalUser: { firstName: {}, lastName: {}, userId: {} },
      },

      requestIdArr: { push: () => ({}), length: {}, splice: () => ({}) },
      OfferDatesArray: { push: () => ({}), splice: () => ({}) },

      requestIdsListSelected$: { next: () => ({}) },
      createdAppIds$: { next: () => ({}) },
      createdAppIds:["OMS","UPP"]
    });
    const searchOfferRequestServiceStub = () => ({
      actionAndMore$: { subscribe: f => f({}), next: () => ({}) },
      searchOfferRequest: (arg, arg1) => ({ subscribe: f => f({}), })
    });
     const facetItemServiceStub = () => ({
      populateStoreFacet: (
        facets,
        storesSearchCriteria,
        divisionRogCds
      ) => ({}),
      getFacetItems: () => ({}),
      expandSub: new BehaviorSubject(false),
      getdivsionStateFacetItems: () => ({}),
      sortDivisionRogCds: () => ({})
    });
    const featureFlagsServiceStub = () => ({
        isFeatureFlagEnabled: () => (false)
  
      });
    const requestFormServiceStub = () => ({
      assignedModal$: { next: () => ({}) },
      isExpiredStatusAndNotCancelled:()=>(false),
      isDateExpired:()=>(false),
    });
    const eventEmitterStub = () => ({
      emit: () => ({})
    });
    const commonSearchServiceStub = ()=>({});
    TestBed.configureTestingModule({
      imports: [PermissionsModule.forRoot({ permissionsIsolate: true, configurationIsolate: true, rolesIsolate: true })],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [OfferRequestListComponent],
      providers: [
        PermissionsConfigurationService,
        PermissionsService,
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: Router, useFactory: routerStub },
        { provide: EventEmitter, useFactory: eventEmitterStub },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagsServiceStub },
        { provide: SearchOfferRequestService, useFactory: searchOfferRequestServiceStub},
        { provide: RequestFormService, useFactory: requestFormServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: CommonSearchService,useFactory:commonSearchServiceStub}
      ]
    });
    fixture = TestBed.createComponent(OfferRequestListComponent);
    component = fixture.componentInstance;
  });
  it('can load instance', () => {
    expect(component).toBeTruthy();
  });
  it('contextMenu defaults to: false', () => {
    expect(component.contextMenu).toEqual(false);
  });
  it('contextMenuArr defaults to: []', () => {
    expect(component.contextMenuArr).toEqual(["Clone"]);
  });
  it('showOfferList defaults to: false', () => {
    expect(component.showOfferList).toEqual(false);
  });
  it('offers defaults to: []', () => {
    expect(component.offers).toEqual([]);
  });
  it('digitalOffersCount defaults to: 0', () => {
    expect(component.digitalOffersCount).toEqual(0);
  });
  it('nonDigitalOffersCount defaults to: 0', () => {
    expect(component.nonDigitalOffersCount).toEqual(0);
  });
  it('offersCount defaults to: 0', () => {
    expect(component.offersCount).toEqual(0);
  });
  it('offersData defaults to: []', () => {
    expect(component.offersData).toEqual([]);
  });
  it('digitalCount defaults to: 0', () => {
    expect(component.digitalCount).toEqual(0);
  });
  it('totalDigitalCount defaults to: 0', () => {
    expect(component.totalDigitalCount).toEqual(0);
  });
  it('nonDigitalCount defaults to: 0', () => {
    expect(component.nonDigitalCount).toEqual(0);
  });
  it('totalNonDigitalCount defaults to: 0', () => {
    expect(component.totalNonDigitalCount).toEqual(0);
  });
  it('checked defaults to: false', () => {
    expect(component.checked).toEqual(false);
  });
  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(component, 'initData');
      spyOn(component, 'initSubscribes');
      let offerType: {
        "ITEM_DISCOUNT": "Item Discount",
        "BUYX_GETX": "Buy X Get X",
        "BUYX_GETY": "Buy X Get Y",
        "MEAL_DEAL": "Meal Deal",
        "BUNDLE": "Bundle",
        "MUST_BUY": "Must Buy",
        "FAB_5_OR_SCORE_4": "Fab 5 / Score 4",
        "WOD_OR_POD": "WOD / POD",
        "STORE_CLOSURE": "Store Closure",
        "REWARDS_ACCUMULATION": "Rewards - Accumulation",
        "REWARDS_FLAT": "Rewards - Flat",
        "CONTINUITY": "Continuity",
        "INSTANT_WIN": "Enterprise Instant Win",
        "ALASKA_AIRMILES": "Alaska Airmiles",
        "CUSTOM": "Custom"
      }
      spyOn(initialDataServiceStub, 'getAppData').and.returnValue({
        offerType
      });
      component.ngOnInit();
      expect(component.initData).toHaveBeenCalled();
      expect(component.initSubscribes).toHaveBeenCalled();
      expect(component.offerTypes).toEqual(offerType)
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
    });
  });
  describe('initSubscribes', () => {
    it('makes expected calls', () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      searchOfferRequestServiceStub.actionAndMore$ = new BehaviorSubject(false);
      // spyOn(component, 'assignLatestValues');
      // spyOn(component, 'displayDigitalNonDigitalStatus');
      spyOn(component, 'assignLatestValues');
      spyOn(component, 'displayDigitalNonDigitalStatus');
      let id: any = 342889629;

      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
        },
        "rules": {
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "redemptionStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ]
                  },
                  "productGroupVersions": [
                    {

                      "productGroup": {
                        "name": "PGTestAutomationUPCGroup7"
                      },

                    }
                  ],

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.initSubscribes();
      searchOfferRequestServiceStub.actionAndMore$.next(id)
      //spyOn(searchOfferRequestServiceStub, 'actionAndMore$').and.returnValue(of(id))

      expect(component.assignLatestValues).toHaveBeenCalled();
      expect(component.displayDigitalNonDigitalStatus).toHaveBeenCalled();
    });
  });
  describe('ngOnChanges', () => {
    it('should call secureBulkAssignUpdateCheckboxByUserPermissions', () => {
      spyOn(component, 'secureBulkAssignUpdateCheckboxByUserPermissions');
      component.ngOnChanges();
      expect(component.secureBulkAssignUpdateCheckboxByUserPermissions).toHaveBeenCalled();
    });
  });
  describe('displayDigitalNonDigitalStatus', () => {
    it('makes expected calls', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      spyOn(component,"setExpiredStatusIfAny");
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Processing', className: 'green-status bold-label' }
      let ndRes = { status: 'Processing', className: 'green-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Completed"', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "D",
          "nonDigitalStatus": "D",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      spyOn(component,"setExpiredStatusIfAny");
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Completed', className: 'purple-status bold-label' }
      let ndRes = { status: 'Completed', className: 'purple-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Completed"', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "A",
          "nonDigitalStatus": "A",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Assigned', className: 'blue-status bold-label' }
      let ndRes = { status: 'Assigned', className: 'blue-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "null"', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": null,
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      spyOn(component,"setExpiredStatusIfAny");
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      expect(component.digitalStatus.className).toEqual('')
      expect(component.nonDigitalStatus.className).toEqual('')
    });
    it('if Both Digital and nondigital are "Editing', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",

          digitalEditStatus: {
            editStatus: 'E'
          },
          nonDigitalEditStatus: {
            editStatus: 'E'
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Editing', className: 'red-status bold-label' }
      let ndRes = { status: 'Editing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Updating', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "D",
          "nonDigitalStatus": "D",

          digitalEditStatus: {
            editStatus: 'U'
          },
          nonDigitalEditStatus: {
            editStatus: 'U'
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Updating', className: 'yellow-status bold-label' }
      let ndRes = { status: 'Updating', className: 'yellow-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Removing', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",

          digitalEditStatus: {
            editStatus: 'R'
          },
          nonDigitalEditStatus: {
            editStatus: 'R'
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Removing', className: 'red-status bold-label' }
      let ndRes = { status: 'Removing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Digital is "Removing', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": null,

          digitalEditStatus: {
            editStatus: 'R'
          },
          nonDigitalEditStatus: {
            editStatus: null
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      //expect(component.getDigitalStatus).toHaveBeenCalled();
      //expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Removing', className: 'red-status bold-label' }
      //let ndRes = { status: 'Removing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      //expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Non-Digital is "Removing', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": 'D',

          digitalEditStatus: {
            editStatus: null
          },
          nonDigitalEditStatus: {
            editStatus: 'R'
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      //expect(component.getDigitalStatus).toHaveBeenCalled();
      //expect(component.getNonDigitalStatus).toHaveBeenCalled();


      let ndRes = { status: 'Removing', className: 'red-status bold-label' }

      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Digital is "Editing', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": null,

          digitalEditStatus: {
            editStatus: 'E'
          },
          nonDigitalEditStatus: {
            editStatus: null
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      //expect(component.getDigitalStatus).toHaveBeenCalled();
      //expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Editing', className: 'red-status bold-label' }
      //let ndRes = { status: 'Removing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      //expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Non-Digital is "Editing', () => {
      //spyOn(component, 'getDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
    //   spyOn(component, 'getNonDigitalStatus');
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": 'D',

          digitalEditStatus: {
            editStatus: null
          },
          nonDigitalEditStatus: {
            editStatus: 'E'
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      //expect(component.getDigitalStatus).toHaveBeenCalled();
      //expect(component.getNonDigitalStatus).toHaveBeenCalled();


      let ndRes = { status: 'Editing', className: 'red-status bold-label' }

      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Digital is "Updating', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "D",
          "nonDigitalStatus": null,

          digitalEditStatus: {
            editStatus: 'U'
          },
          nonDigitalEditStatus: {
            editStatus: null
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

    //   expect(component.getDigitalStatus).toHaveBeenCalled();
      //expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Updating', className: 'yellow-status bold-label' }
      //let ndRes = { status: 'Removing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      //expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Non-Digital is "updating', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": 'p',

          digitalEditStatus: {
            editStatus: null
          },
          nonDigitalEditStatus: {
            editStatus: 'U'
          },

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      //expect(component.getDigitalStatus).toHaveBeenCalled();
    //   expect(component.getNonDigitalStatus).toHaveBeenCalled();


      let ndRes = { status: 'Updating', className: 'yellow-status bold-label' }

      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Submitted"', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "S",
          "nonDigitalStatus": "S",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      let digitalREs = { status: 'Submitted', className: 'yellow-status bold-label' }
      let ndRes = { status: 'Submitted', className: 'yellow-status bold-label' }

      expect(component.statusClassName).toEqual('yellow-status bold-label')
    });
    it('if Both Digital and nondigital are "Draft"', () => {
     // spyOn(component, 'getDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
    //   spyOn(component, 'getNonDigitalStatus');
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "I",
          "nonDigitalStatus": "I",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();

      expect(component.statusClassName).toEqual('red-status bold-label')
    });
    it('if only Digital is "Draft"', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "I",
          "nonDigitalStatus": null,
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();
      let digitalREs = { status: 'Draft', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
    });
    it('if only non-Digital is "Draft"', () => {
    //   spyOn(component, 'getDigitalStatus');
    //   spyOn(component, 'getNonDigitalStatus');
      spyOn(component,"setExpiredStatusIfAny");

      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": "I",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        },
        "rules": {
          "startDate": {
            "displayEffectiveStartDate": null,
            "offerEffectiveStartDate": "2050-08-05T00:00:01.235+00:00",
            "offerTestEffectiveStartDate": null
          },
          "endDate": {
            "displayEffectiveEndDate": null,
            "offerEffectiveEndDate": "2050-08-25T00:00:01.235+00:00",
            "offerTestEffectiveEndDate": null
          },
          "applicableTo": {
            "banners": null,
            "divisions": null,
            "storeGroups": null,
            "channels": null,
            "terminals": null,
            "departments": null,
            "storeGroupsAsString": "{}"
          },
          "qualificationAndBenefit": {
            "qualificationUnitType": null,
            "qualificationUnitSubType": null,
            "minOrderTotalAmount": null,
            "minRewardPoints": null,
            "isBuyXGetYOffer": null,
            "buy": null,
            "get": null
          },
          "usageLimits": {
            "usageLimitTypePerUser": null,
            "usageLimitTypePerOffer": null,
            "usageLimitTypePerTxn": null,
            "usageLimitPerUser": null,
            "usageLimitPerOffer": null,
            "usageLimitPerTxn": null
          }
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus();
      let ndRes = { status: 'Draft', className: 'red-status bold-label' }
      expect(component.nonDigitalStatus).toEqual(ndRes)

    });
  });
  describe('assignLatestValues', () => {
    xit('makes expected calls', () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(
        RequestFormService
      );
      spyOn(component, 'displayDigitalNonDigitalStatus');

      let obj = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
          "deliveryChannel": "CC",
        },
        "rules": {
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "redemptionStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ]
                  },
                  "productGroupVersions": [
                    {

                      "productGroup": {
                        "name": "PGTestAutomationUPCGroup7"
                      },

                    }
                  ],

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      spyOn(searchOfferRequestServiceStub, 'searchOfferRequest').and.returnValue(of(obj));
      component.offerRequest = { info: { id: 342889629 } }
      component.assignLatestValues();

      requestFormServiceStub.assignedModal$ = new BehaviorSubject(false);
      expect(
        searchOfferRequestServiceStub.searchOfferRequest
      ).toHaveBeenCalled();
      requestFormServiceStub.assignedModal$.subscribe((val) => {
        expect(val).toEqual(true)
      })

    });
  });
  describe('initData', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(component, 'setDateDetails');
      spyOn(component, 'setOffersCount');
      spyOn(component, 'offerListCount');
      spyOn(initialDataServiceStub, 'getAppData')
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
          isApplicableToJ4U: true,
        },
        "rules": {
          startDate: {
            displayEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerTestEffectiveStartDate: null,
          },
          endDate: {
            displayEffectiveEndDate: "2020-05-18T23:59:59.999+00:00",
            offerEffectiveEndDate: "2020-05-17T23:59:59.000+00:00",
            offerTestEffectiveEndDate: null,
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "redemptionStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ]
                  },
                  productGroupVersions: [
                    {
                      displayOrder: 1,
                      productGroup: {
                        quantityUnitType: 'DOLLARS',
                        minPurchaseAmount: 1,
                        tiers: [
                          {
                            level: 1,
                            amount: 2,
                            weightLimit: null,
                            upTo: 5
                          }
                        ]
                      },
                      discountVersion: {
                        id: 234345,
                        discounts: [
                          {
                            benefitValueType: "PRICE_POINT_ITEMS",
                            includeProductGroupName: "banana",
                            excludeProductGroupName: null,
                            discountType: "ITEM_LEVEL",
                            chargebackDepartment: "Item's Department",
                            advanced: { bestDeal: true, allowNegative: null, flexNegative: true },
                            discountTier: [
                              { level: 1, amount: 5.0, upTo: null, itemLimit: 4, weightLimit: 0.0, dollarLimit: null, receiptText: "brandsdf" },
                            ],
                          },
                        ],
                      }

                    }
                  ]

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      component.initData();

      expect(component.setDateDetails).toHaveBeenCalled();
      expect(component.setOffersCount).toHaveBeenCalled();
      expect(component.discountType).toEqual('PRICE_POINT_ITEMS');
      expect(component.offerListCount).toHaveBeenCalled();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();

    });
  });

  describe('offerListCount', () => {
    it('If req is processing / completed, then offers needs to be displayed', () => {
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
          isApplicableToJ4U: true,
        },
        "rules": {
          startDate: {
            displayEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerTestEffectiveStartDate: null,
          },
          endDate: {
            displayEffectiveEndDate: "2020-05-18T23:59:59.999+00:00",
            offerEffectiveEndDate: "2020-05-17T23:59:59.000+00:00",
            offerTestEffectiveEndDate: null,
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "nonDigitalRedemptionStoreGroupNames": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "digitalRedemptionStoreGroupNames": [
                        "TestStoreGroup"
                    ]
                  },
                  productGroupVersions: [
                    {
                      displayOrder: 1,
                      productGroup: {
                        quantityUnitType: 'DOLLARS',
                        minPurchaseAmount: 1,
                        tiers: [
                          {
                            level: 1,
                            amount: 2,
                            weightLimit: null,
                            upTo: 5
                          }
                        ]
                      },
                      discountVersion: {
                        id: 234345,
                        discounts: [
                          {
                            benefitValueType: "PRICE_POINT_ITEMS",
                            includeProductGroupName: "banana",
                            excludeProductGroupName: null,
                            discountType: "ITEM_LEVEL",
                            chargebackDepartment: "Item's Department",
                            advanced: { bestDeal: true, allowNegative: null, flexNegative: true },
                            discountTier: [
                              { level: 1, amount: 5.0, upTo: null, itemLimit: 4, weightLimit: 0.0, dollarLimit: null, receiptText: "brandsdf" },
                            ],
                          },
                        ],
                      }

                    }
                  ]

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };

      component.offerListCount();
      expect(component.offersData.length).toEqual(1)

    });

    it('If req is not processing / completed, then offers doesnt needs to be displayed', () => {
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "NA",
          "nonDigitalStatus": "NA",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
        },
        "rules": {
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "nonDigitalRedemptionStoreGroupNames": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "digitalRedemptionStoreGroupNames": [
                        "TestStoreGroup"
                    ]
                  },
                  "productGroupVersions": [
                    {
                      "displayOrder": 1,
                      "id": null,
                      "anyProduct": null,
                      "isGiftCard": false,
                      "discountVersion": {
                        "id": null,
                        "airMiles": [

                        ],
                        "discounts": [
                          {
                            "id": null,
                            "displayOrder": 1,
                            "benefitValueType": "PRICE_POINT_ITEMS",
                            "discountType": "ITEM_LEVEL",
                            "includeProductGroupName": "PGTest",
                            "excludeProductGroupName": null,
                            "chargebackDepartment": null,
                            "tiers": [
                              {
                                "amount": "1",
                                "upTo": null,
                                "itemLimit": 1,
                                "dollarLimit": null,
                                "level": 1,
                                "receiptText": null,
                                "rewards": null,
                                "weightLimit": null,
                                "points": null
                              }
                            ]
                          }
                        ]
                      },
                      "productGroup": {
                        "name": "PGTest",
                        "quantityUnitType": "ITEMS",
                        "excludedProductGroupName": null,
                        "conjunction": null,
                        "minPurchaseAmount": 0,
                        "uniqueproduct": null,
                        "inheritedFromOfferRequest": null,
                        "tiers": [
                          {
                            "level": 1,
                            "amount": null
                          }
                        ]
                      }
                    }
                  ],

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      component.offerListCount();
      expect(component.offersData.length).toEqual(0)
    });

    it('If req is processing / completed, then offers needs to be displayed for store Closure', () => {
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
          offerRequestType: "STORE_CLOSURE",
          isApplicableToJ4U: true,
        },
        "rules": {
          startDate: {
            displayEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerTestEffectiveStartDate: null,
          },
          endDate: {
            displayEffectiveEndDate: "2020-05-18T23:59:59.999+00:00",
            offerEffectiveEndDate: "2020-05-17T23:59:59.000+00:00",
            offerTestEffectiveEndDate: null,
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "STORE_CLOSURE",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "nonDigitalRedemptionStoreGroupNames": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "digitalRedemptionStoreGroupNames": [
                        "TestStoreGroup"
                    ]
                  },
                  productGroupVersions: [
                    {
                      displayOrder: 1,
                      productGroup: {
                        quantityUnitType: 'DOLLARS',
                        minPurchaseAmount: 1,
                        tiers: [
                          {
                            level: 1,
                            amount: 2,
                            weightLimit: null,
                            upTo: 5
                          }
                        ]
                      },
                      discountVersion: {
                        id: 35836690,
                        discounts: [
                          {
                            benefitValueType: "PRICE_POINT_ITEMS",
                            includeProductGroupName: "banana",
                            excludeProductGroupName: null,
                            discountType: "ITEM_LEVEL",
                            chargebackDepartment: "Item's Department",
                            advanced: { bestDeal: true, allowNegative: null, flexNegative: true },
                            discountTier: [
                              { level: 1, amount: 5.0, upTo: null, itemLimit: 4, weightLimit: 0.0, dollarLimit: null, receiptText: "brandsdf" },
                            ],
                          },
                        ],
                      }

                    }
                  ]

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": true,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };

      component.offerListCount();
      expect(component.offersData.length).toEqual(1)

    });

  });
  describe("getOfferStatusClass ", () => {
    it("should return expected classes based on offer status", () => {
      let className = component.getStatus("I");
      expect(className).toEqual("red-status bold-label");

      className = component.getStatus("D");
      expect(className).toEqual("purple-status bold-label");

      className = component.getStatus("S");
      expect(className).toEqual("yellow-status bold-label");

      className = component.getStatus("P");
      expect(className).toEqual("green-status bold-label");

      className = component.getStatus("A");
      expect(className).toEqual("blue-status bold-label");

      className = component.getStatus("E");
      expect(className).toEqual("red-status bold-label");

      className = component.getStatus("U");
      expect(className).toEqual("yellow-status bold-label");

      className = component.getStatus("R");
      expect(className).toEqual("red-status bold-label");
    });
  });
  describe("setOfferReqStatus ", () => {
    it("should return count for digital offers as green when approved", () => {

      component.digitalOffersCount = 1
      component.setOfferReqStatus();

      expect(component.offersStatus.doStatus).toEqual(true);
      expect(component.offersStatus.coStatus).toEqual(false);
    });
    it("should return count for digital & non digital offers as red ", () => {

      component.digitalOffersCount = null
      component.nonDigitalOffersCount = null
      component.setOfferReqStatus();

      expect(component.offersStatus.doStatus).toEqual(false);
      expect(component.offersStatus.coStatus).toEqual(false);
    });
    it("should return count for digital offers & non digital as green when both approved", () => {

      component.digitalOffersCount = 1
      component.nonDigitalOffersCount = 1
      component.setOfferReqStatus();

      expect(component.offersStatus.doStatus).toEqual(true);
      expect(component.offersStatus.coStatus).toEqual(true);
    });
    it("should return count for non digital offers as green when approved", () => {

      component.nonDigitalOffersCount = 1
      component.setOfferReqStatus();

      expect(component.offersStatus.doStatus).toEqual(false);
      expect(component.offersStatus.coStatus).toEqual(true);
    });
  });
  describe("setCountByCategory ", () => {
    it("should return count for digital offers and status", () => {
      let offers = {
        "storeGroupVersion": 35836692,
        "productGroupVersion": 35836689,
        "discountVersion": 35836690,
        "genericDiscountId": 35836695,
        "instantWinVersion": null,
        "offerId": 35836696,
        "externalOfferId": "35836696-ND",
        "offerStatus": "DE",
        "isApplicableToJ4U": true,
        "distinctIdentifier": "DEFAULT"
      }

      component.setCountByCategory(offers);
      expect(component.digitalOffersCount).toEqual(1);
      expect(component.nonDigitalOffersCount).toEqual(0);
      expect(component.offersStatus.digitalOffers[0]).toEqual('DE');

    });
    it("should return count for non digital offers and status", () => {
      let offers = {
        "storeGroupVersion": 35836692,
        "productGroupVersion": 35836689,
        "discountVersion": 35836690,
        "genericDiscountId": 35836695,
        "instantWinVersion": null,
        "offerId": 35836696,
        "externalOfferId": "35836696-ND",
        "offerStatus": "PU",
        "isApplicableToJ4U": false,
        "distinctIdentifier": "DEFAULT"
      }

      component.setCountByCategory(offers);
      expect(component.digitalOffersCount).toEqual(0);
      expect(component.nonDigitalOffersCount).toEqual(1);
      expect(component.offersStatus.copientOffers[0]).toEqual('PU');

    });
  });
  describe('setOffersCount', () => {
    it('makes expected calls', () => {
      spyOn(component, 'setOfferReqStatus');
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
        },
        "rules": {
          startDate: {
            displayEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerTestEffectiveStartDate: null,
          },
          endDate: {
            displayEffectiveEndDate: "2020-05-18T23:59:59.999+00:00",
            offerEffectiveEndDate: "2020-05-17T23:59:59.000+00:00",
            offerTestEffectiveEndDate: null,
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "redemptionStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ]
                  },
                  "productGroupVersions": [
                    {

                      "productGroup": {
                        "name": "PGTestAutomationUPCGroup7"
                      },

                    }
                  ],

                },
                offers: [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      component.offersCount = 0;
      component.digitalOffersCount = 1
      component.setOffersCount();
      expect(component.setOfferReqStatus).toHaveBeenCalled();
      expect(component.offersStatus.doStatus).toEqual(false);
      expect(component.offersStatus.coStatus).toEqual(false);
      expect(component.offersCount).toEqual(1)
    });
  });
  describe("selectIndividualRequest", () => {
    it("makes expected calls", () => {
      const event = {
        target: {
          checked: true
        },
      };
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
          "createdApplicationId":"OMS"
        },
        "rules": {
          startDate: {
            displayEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerTestEffectiveStartDate: null,
          },
          endDate: {
            displayEffectiveEndDate: "2020-05-18T23:59:59.999+00:00",
            offerEffectiveEndDate: "2020-05-17T23:59:59.000+00:00",
            offerTestEffectiveEndDate: null,
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "redemptionStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ]
                  },
                  "productGroupVersions": [
                    {

                      "productGroup": {
                        "name": "PGTestAutomationUPCGroup7"
                      },

                    }
                  ],

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      component.selectIndividualRequest(event, component.offerRequest);
      expect(component.isSelected).toEqual(true);
    });
    it("when checked is false", () => {
      const event = {
        target: {
          checked: false
        },
      };
      component.offerRequest = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
          "digitalUiStatus": "S",
          "nonDigitalUiStatus": "NA",
        },
        "rules": {
          startDate: {
            displayEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerEffectiveStartDate: "2020-05-17T00:00:00.000+00:00",
            offerTestEffectiveStartDate: null,
          },
          endDate: {
            displayEffectiveEndDate: "2020-05-18T23:59:59.999+00:00",
            offerEffectiveEndDate: "2020-05-17T23:59:59.000+00:00",
            offerTestEffectiveEndDate: null,
          },
          "qualificationAndBenefit": {
            "offerRequestOffers": [
              {
                "id": 35836691,
                "storeGroupVersion": {
                  "id": 35836692,
                  "displayOrder": 1,
                  "offerPrototype": "ITEM_DISCOUNT",
                  "storeGroup": {
                    "podStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ],
                    "redemptionStoreGroups": [
                      "APIAutoNorcalStoreGroup"
                    ]
                  },
                  "productGroupVersions": [
                    {

                      "productGroup": {
                        "name": "PGTestAutomationUPCGroup7"
                      },

                    }
                  ],

                },
                "offers": [
                  {
                    "storeGroupVersion": 35836692,
                    "productGroupVersion": 35836689,
                    "discountVersion": 35836690,
                    "genericDiscountId": 35836695,
                    "instantWinVersion": null,
                    "offerId": 35836696,
                    "externalOfferId": "35836696-ND",
                    "offerStatus": "DE",
                    "isApplicableToJ4U": false,
                    "distinctIdentifier": "DEFAULT"
                  }
                ]
              }
            ],

          },

        }
      };
      component.selectIndividualRequest(event, component.offerRequest);
      expect(component.isSelected).toEqual(false);
    });
  });
  describe('User Roles Test Cases based on permissions - offer-request-list.comp', () => {
    it('If user has Batch Assign Permission, button should NOT have hide class', () => {
      const permissionServiceStub = fixture.debugElement.injector.get(PermissionsService);
      permissionServiceStub.loadPermissions(['DO_BATCH_ASSIGN']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions();
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('');
    });

    it('If user has Batch Update Offer Dates Permission, button should NOT have hide class', () => {
      const permissionServiceStub = fixture.debugElement.injector.get(PermissionsService);
      permissionServiceStub.loadPermissions(['DO_BATCH_UPDATE_OFFER_DATES']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions();
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('');
    });
    
    it('If user Does not have Batch Assign Permission, button should have hide class', () => {
      const permissionServiceStub = fixture.debugElement.injector.get(PermissionsService);
      permissionServiceStub.loadPermissions(['']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions();
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('hide');
    });

    it('If user Does not have Batch Update Offer Dates Permission, button should have hide class', () => {
      const permissionServiceStub = fixture.debugElement.injector.get(PermissionsService);
      permissionServiceStub.loadPermissions(['']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions();
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('hide');
    });

    it('If user has ANY Batch Permission button should have hide class', () => {
      const permissionServiceStub = fixture.debugElement.injector.get(PermissionsService);
      permissionServiceStub.loadPermissions(['EDIT_GR_SPD_REQUESTS']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions();
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('hide');
    });
    
    /* it("Authorization Property check -> if appLevelAuthorizationStrategy is disable and user dont have ANY permission it should have disable class", () => {
      const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionConfigurationService.addPermissionStrategy('disable', (tf: any) => {
        this.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, 'disabled', 'disabled');
        this.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, 'disable');
      });
      permissionConfigurationService.setDefaultOnUnauthorizedStrategy('disable');
      permissionService.loadPermissions(['']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions(' ');
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('disable');
    });
    it("Authorization Property check -> if appLevelAuthorizationStrategy is disable But user has permission with associated programCode it should NOT have disable class", () => {
      const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionConfigurationService.addPermissionStrategy('disable', (tf: any) => {
        this.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, 'disabled', 'disabled');
        this.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, 'disable');
      });
      permissionConfigurationService.setDefaultOnUnauthorizedStrategy('disable');
      permissionService.loadPermissions(['DO_MANUFACTURER_COUPON_REQUESTS']);
      component.bulkOptionsCheckBoxcssBasedOnPermissions = '';
      component.secureBulkAssignUpdateCheckboxByUserPermissions('MF');
      expect(component.bulkOptionsCheckBoxcssBasedOnPermissions).toEqual('');
    }); */
  });

});
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { mmDdYySlash_DateFormat } from '@appUtilities/date.utility';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import * as moment from 'moment';


@Component({
  selector: "offer-request-list",
  templateUrl: './offer-request-list.component.html',
  styleUrls: [
    './offer-request-list.component.scss',
    './offer-request-list-mq-component.scss'
  ]
})
export class OfferRequestListComponent extends UnsubscribeAdapter
  implements OnInit {
  endDate: any;
  sDate: any;
  contextMenu: boolean = false;
  @Input('offerRequest') offerRequest: any;
  @Input('expand') expandOffer: boolean;
  @Input('collapse') collapseOffer: boolean;
  contextMenuArr: string[] = ['Clone'];
  showOfferList: boolean = false;
  offers = [];
  categoryColor;
  configData;
  @Input() requestForm;
  isDisplayEdit: boolean;
  digitalUser: boolean;
  nonDigitalUser: boolean;
  bulkSelection;
  digitalOffersCount: number = 0;
  nonDigitalOffersCount: number = 0;
  offersCount: number = 0;
  offersStatus = {
    digitalOffers: [],
    copientOffers: [],
    doStatus: false,
    coStatus: false
  };
  isSelected;
  discountType;
  offersData: any = [];
  digitalCount: number = 0;
  totalDigitalCount: number = 0;
  nonDigitalCount: number = 0;
  totalNonDigitalCount: number = 0;
  digitalStatus: any;
  nonDigitalStatus: any;
  statusClassName: any;
  offerTypes: any;
  bulkOptionsCheckBoxcssBasedOnPermissions = 'hide';
  CONSTANTS = CONSTANTS;
  constructor(
    private featureFlagsService: FeatureFlagsService,
    private _initialDataService: InitialDataService,
    private bulkUpdateService: BulkUpdateService,
    public _searchOfferRequestService: SearchOfferRequestService,
    public facetItemService: FacetItemService,
    public _requestFormService: RequestFormService,
    private _permissionsService: PermissionsService,
    private commonSearchService: CommonSearchService,
    private cdr: ChangeDetectorRef) {
    super();
    this.expandOffer
      ? (this.showOfferList = true)
      : (this.showOfferList = false);
  }

  ngOnInit() {
    this.initData();
    this.initSubscribes();
    this.bulkUpdateService.userTypeArray = [];
   
    const appData = this._initialDataService.getAppData();
    this.offerTypes = appData.offerType;
   
  }
  ngOnChanges() {
    this.expandOffer
      ? (this.showOfferList = true)
      : (this.showOfferList = false);
    this.secureBulkAssignUpdateCheckboxByUserPermissions();
  }

  initSubscribes() {
    this.bulkUpdateService.bulkSelected$.subscribe(val => {
      this.isSelected = val;
      this.cdr.detectChanges();
    });

    this.bulkUpdateService.offerBulkSelection.subscribe(value => {
      this.bulkSelection = this.isSelected = value === 'selectAcrossAllPages';
    });

    this._searchOfferRequestService.actionAndMore$.subscribe(val => {
      if (val === this.offerRequest.info.id) {
        this.assignLatestValues();
      }
    });
    this.displayDigitalNonDigitalStatus();
  }

  displayDigitalNonDigitalStatus() {
    this.setExpiredStatusIfAny();
    const { digitalStatus, nonDigitalStatus } = this.offerRequest.info;

    const { digitalEditStatus, nonDigitalEditStatus } = this.offerRequest.info
    
    if (
      ['I', 'S'].includes(digitalStatus) ||
      ['I', 'S'].includes(nonDigitalStatus)
    ) {
      const iStatus =
        digitalStatus &&
        ['I'].includes(digitalStatus) &&
        nonDigitalStatus &&
        ['I'].includes(nonDigitalStatus);
      const sStatus =
        digitalStatus &&
        ['S'].includes(digitalStatus) &&
        nonDigitalStatus &&
        ['S'].includes(nonDigitalStatus);
      if (iStatus || sStatus) {

        if (this._requestFormService.isDateExpired(this.offerRequest.rules.endDate.offerEffectiveEndDate) && (nonDigitalStatus !== 'C' || digitalStatus !== 'C')) {
          this.digitalStatus = {
            status: 'Expired',
            className: this.getStatus(CONSTANTS.EXPIRED_STATUS_OR)
          };
        } else {
          this.statusClassName =
            digitalStatus === 'S' || nonDigitalStatus === 'S'
              ? 'yellow-status bold-label'
              : 'red-status bold-label';
          this.digitalStatus = {
            status: this.offerRequestStatuses[
              digitalStatus || nonDigitalStatus
            ],
            className: this.statusClassName
          };
        }
        this.nonDigitalStatus = '';
      } else {
        this.digitalStatus = this.getDigitalStatus(digitalStatus);
        this.nonDigitalStatus = this.getNonDigitalStatus(nonDigitalStatus);
      }
    } else {
      this.digitalStatus = this.getDigitalStatus(digitalStatus, digitalEditStatus);
      this.nonDigitalStatus = this.getNonDigitalStatus(nonDigitalStatus);

    }
   
    /* After the statuses are being set Edit/Update Statuses*/
      if (digitalEditStatus?.editStatus) {

        if (digitalEditStatus.editStatus === 'E' || digitalEditStatus.editStatus === 'U') {
          this.digitalStatus = this.getDigitalStatus(digitalEditStatus.editStatus);
        }
        if (digitalEditStatus.editStatus === 'R') {
          this.digitalStatus = this.getDigitalStatus(digitalEditStatus.editStatus);
        }

      }
      if (nonDigitalEditStatus?.editStatus) {

        if (nonDigitalEditStatus.editStatus === 'E' || nonDigitalEditStatus.editStatus === 'U' || nonDigitalEditStatus.editStatus === 'R') {
          this.nonDigitalStatus = this.getNonDigitalStatus(nonDigitalEditStatus.editStatus);
        }
      }
    
  }

  private get offerRequestStatuses() {
    let statuskey = this.selectedProgramCode;
    // For BPD need to use SPD offerRequestStatuses Key
    if(this.selectedProgramCode === CONSTANTS.BPD) {
        statuskey = CONSTANTS.SPD;
    }
    return [CONSTANTS.GR, CONSTANTS.BPD, CONSTANTS.SPD].includes(this.selectedProgramCode)
      ? this.configData[`offerRequestStatuses${statuskey}`] : this.configData.offerRequestStatuses;
  }

  get selectedProgramCode() {
    return this.facetItemService.programCodeSelected;
  }
  getStatus(status) {
    const statusClasses = {
      A: 'blue-status bold-label',
      P: 'green-status bold-label',
      S: 'yellow-status bold-label',
      I: 'red-status bold-label',
      E: 'red-status bold-label',
      U: 'yellow-status bold-label',
      D: 'purple-status bold-label',
      C: 'red-status bold-label',
      [CONSTANTS.EXPIRED_STATUS_OR]: 'purple-status bold-label'
    };
  
    return statusClasses[status] || (status !== null && status !== 'NA' ? 'red-status bold-label' : '');
  }
  
  getDigitalStatus(nonDigitalStatus, digitalEditStatus?) {
    let status = this.offerRequestStatuses[nonDigitalStatus];
    let className = this.getStatus(nonDigitalStatus);
    const selectedProgramCode = this.facetItemService.programCodeSelected;

    if (selectedProgramCode == CONSTANTS.GR && nonDigitalStatus == 'D' && digitalEditStatus && digitalEditStatus.editStatus == 'RU') {
      status = this.offerRequestStatuses[digitalEditStatus.editStatus];
      className = 'red-status bold-label';
    }

    if (this._requestFormService.isExpiredStatusAndNotCancelled({status: nonDigitalStatus, endDate:this.offerRequest.rules.endDate.offerEffectiveEndDate})) {
      return {
        status: 'Expired',
        className: this.getStatus(CONSTANTS.EXPIRED_STATUS_OR)
      }
    }

    return {
      status,
      className
    };
  }

  getNonDigitalStatus(nonDigitalStatus) {
    let status = this.offerRequestStatuses[nonDigitalStatus];
    let className = this.getStatus(nonDigitalStatus);
    if (this._requestFormService.isExpiredStatusAndNotCancelled({status: nonDigitalStatus, endDate: this.offerRequest.rules.endDate.offerEffectiveEndDate})) {
      status = 'Expired';
      className = this.getStatus(CONSTANTS.EXPIRED_STATUS_OR);
    }
    return {
      status: status,
      className: className
    };
  }
  assignLatestValues() {
    this.subs.sink = this._searchOfferRequestService
      .searchOfferRequest(`requestId=${this.offerRequest.info.id};`, false)
      .subscribe((response: any) => {
        if (response) {
          this.offerRequest = response.offerRequests[0];
          const {
            digitalUser,
            nonDigitalUser,
            deliveryChannel
          } = this.offerRequest.info;
          this.displayDigitalNonDigitalStatus();
          let userIds = false;
          if (deliveryChannel === 'CC') {
            userIds =
              digitalUser &&
                digitalUser.userId &&
                nonDigitalUser &&
                nonDigitalUser.userId
                ? true
                : false;
          } else if (deliveryChannel === 'DO') {
            userIds = digitalUser && digitalUser.userId ? true : false;
          } else if (['IS', 'PO'].includes(deliveryChannel)) {
            userIds = nonDigitalUser && nonDigitalUser.userId ? true : false;
          }
          if (userIds) {
            this._requestFormService.assignedModal$.next(true);
          }
        }
      });
  }
  initData() {
    this.facetItemService.expandSub.subscribe(val => {
      this.showOfferList = val;
    });

    this.configData = this._initialDataService.getAppData();
    this.setDateDetails();    
    this.setOffersCount();

    if (this.offerRequest.rules.qualificationAndBenefit.offerRequestOffers &&
      this.offerRequest.rules.qualificationAndBenefit.offerRequestOffers.length >= 1) {
      this.offerRequest.rules.qualificationAndBenefit.offerRequestOffers.map(
        sg => {
          sg.storeGroupVersion.productGroupVersions.map(
            pg =>
              pg.discountVersion &&
              pg.discountVersion.discounts &&
              pg.discountVersion.discounts.map(
                e =>
                (this.discountType = e.benefitValueType
                  ? e.benefitValueType
                  : null)
              )
          );
        }
      );
    }
    this.offerListCount();    
  }


  offerListCount() {
    if (this.offerRequest.rules.qualificationAndBenefit.offerRequestOffers) {
      const { digitalStatus, nonDigitalStatus } = this.offerRequest.info;
      this.offersData = this.offerRequest.rules.qualificationAndBenefit.offerRequestOffers.reduce(

        (output, ele) => {
          let productGroup = ele.storeGroupVersion.productGroupVersions.reduce((output, element) => {
            if (element.productGroup && element.productGroup.name) { output.push(element.productGroup.name); }

            return output;
          }, []);

          ele.offers.forEach(element => {
            if (element.isApplicableToJ4U) {
              element.storeGroupName = ele.storeGroupVersion.storeGroup['digitalRedemptionStoreGroupNames'] &&
                ele.storeGroupVersion.storeGroup['digitalRedemptionStoreGroupNames']?.length ?
                ele.storeGroupVersion.storeGroup['digitalRedemptionStoreGroupNames'].join(' , ') : '';
            } else {
              element.storeGroupName =  ele.storeGroupVersion.storeGroup !== null && ele.storeGroupVersion.storeGroup['nonDigitalRedemptionStoreGroupNames'] &&
                ele.storeGroupVersion.storeGroup['nonDigitalRedemptionStoreGroupNames']?.length ?
                ele.storeGroupVersion.storeGroup['nonDigitalRedemptionStoreGroupNames'].join(' , ') : '';
            }
            if (this.offerRequest.info.offerRequestType === 'STORE_CLOSURE') {
              ele.storeGroupVersion.productGroupVersions.filter((group) => {
                if (group.discountVersion.id === element.discountVersion) {
                  element.productGroupName = group.productGroup.name;
                }
              });
            } else {
              element.productGroupName = productGroup && productGroup.join(',');
            }
            if (
              (['P', 'D'].includes(digitalStatus) && element.isApplicableToJ4U) ||
              (['P', 'D'].includes(nonDigitalStatus) && !element.isApplicableToJ4U)
            ) {
              output.push(element);
            }
            if (element.isApplicableToJ4U) {
              this.totalDigitalCount = this.totalDigitalCount + 1;
              if (digitalStatus === 'D') {
                this.digitalCount = this.digitalCount + 1;
              }
            }
            if (!element.isApplicableToJ4U) {
              this.totalNonDigitalCount = this.totalNonDigitalCount + 1;
              if (nonDigitalStatus === 'D') {
                this.nonDigitalCount = this.nonDigitalCount + 1;
              }
            }
          });
          return output;
        },
        []
      );
    }
  }
  /**
   * function to selecting individual request
   */
  selectIndividualRequest(event, offerRequest) {
    this.isSelected = event.target.checked;
    this.bulkUpdateService.isAllBatchSelected.next('noSelectAcrossAllPages');
    if (this.isSelected) {
      this.bulkUpdateService.userTypeArray.push(offerRequest.info.deliveryChannel);
    } else {
      let typeArrayIndex = this.bulkUpdateService.userTypeArray.indexOf(offerRequest.info.deliveryChannel);
      this.bulkUpdateService.userTypeArray.splice(typeArrayIndex, 1);
    }
    // }
    if (event.target.checked) {
      this.bulkUpdateService.deliveryChannelArr.push(`${offerRequest.info.deliveryChannel} - ${offerRequest.info.adType}`);
      this.bulkUpdateService.requestIdArr.push(offerRequest.info.id);
      this.bulkUpdateService.createdAppIds.push(offerRequest.createdApplicationId);
      this.bulkUpdateService.OfferDatesArray.push({
        startDate: offerRequest.rules.startDate.offerEffectiveStartDate,
        endDate: offerRequest.rules.endDate.offerEffectiveEndDate
      });
      const oRid = offerRequest.info.id;
      this.bulkUpdateService.bulkAssignedUsers[`${oRid}`] = {
        digitalUser: offerRequest.info.digitalUser,
        nonDigitalUser: offerRequest.info.nonDigitalUser,
        digitalStatus: offerRequest.info.digitalStatus,
        nonDigitalStatus: offerRequest.info.nonDigitalStatus,
      }
    } else {
      for (let i = 0; i < this.bulkUpdateService.requestIdArr.length; i++) {
        if (this.bulkUpdateService.requestIdArr[i] === offerRequest.info.id) {
          this.bulkUpdateService.requestIdArr.splice(i, 1);
          this.bulkUpdateService.OfferDatesArray.splice(i, 1);
          this.bulkUpdateService.deliveryChannelArr.splice(i, 1);
          this.removeUncheckedUser(this.bulkUpdateService.bulkAssignedUsers, offerRequest.info.id);
        }
      }
    }
    this.bulkUpdateService.createdAppIds$.next([...new Set(this.bulkUpdateService.createdAppIds)]);
    this.bulkUpdateService.requestIdsListSelected$.next(
      this.bulkUpdateService.requestIdArr
    );
  }

  removeUncheckedUser(bulkusers, id) {
    bulkusers = delete bulkusers[id];
  }



  setOffersCount() {
    const offerRequestOffers = this.offerRequest.rules.qualificationAndBenefit
      .offerRequestOffers;
    const offers = offerRequestOffers && offerRequestOffers[0].offers;

    offers &&
      offers.map(offerElem => {
        if (offerElem.externalOfferId) {
          // set total offers in  a request
          this.offersCount = this.offersCount + 1;

        }
      });
    this.setOfferReqStatus();
  }

  setCountByCategory(offerElem) {
    // set offers count for digital, non digital
    if (offerElem.isApplicableToJ4U) {
      this.digitalOffersCount = this.digitalOffersCount + 1;
      this.offersStatus.digitalOffers.push(offerElem.offerStatus);
    } else {
      this.nonDigitalOffersCount = this.nonDigitalOffersCount + 1;
      this.offersStatus.copientOffers.push(offerElem.offerStatus);
    }
  }

  setOfferReqStatus() {
    // Rule: Display the request status as green only when all the offers were approved
    if (this.digitalOffersCount > 0) {
      this.offersStatus.doStatus = this.offersStatus.digitalOffers.every(
        v => v === 'A'
      );
    }
    if (this.nonDigitalOffersCount > 0) {
      this.offersStatus.coStatus = this.offersStatus.copientOffers.every(
        v => v === 'A'
      );
    }
  }
  dateConversion(date) {
    return moment(date).format('L');
  }

  get featureFlagIsDisplayExpired(){
    return this.featureFlagsService.isFeatureFlagEnabled(CONSTANTS.IS_EXPIRED_STATUS_ENABLED);
  }

  updateStatusToExpired({endDate, status, statusKey}) {
    //Set status as expired if status is complete and end date is today
    // if(!(this.featureFlagsService.isOfferRequestArchivalEnabled && this.commonSearchService.isShowExpiredInQuery))
    // {
       if( ![CONSTANTS.COMPLETED_STATUS_OR, CONSTANTS.EXPIRED_STATUS_OR].includes(status)){
         return false;
       }
    // }
    const todayDate = moment(new Date(), 'YYYY-MM-DD');
     endDate = moment(endDate, 'YYYY-MM-DD');
    const days = endDate.diff(todayDate, 'days');

    
    if (days < 0) {
       //fix: Show expired Statuses based on flag
      let expiredStatusKey = CONSTANTS.COMPLETED_STATUS_OR;
      if(this.featureFlagIsDisplayExpired){
        expiredStatusKey = CONSTANTS.EXPIRED_STATUS_OR;
      }
      
      this.offerRequest.info[statusKey] = expiredStatusKey;
    }
   
  }

  setExpiredStatusIfAny() {
    let offerEffectiveEndDate = this.offerRequest.rules.endDate.offerEffectiveEndDate;
  
    if(!offerEffectiveEndDate){
      return false;
    }
      this.updateStatusToExpired(
        { endDate: offerEffectiveEndDate, status: this.offerRequest.info.digitalStatus, statusKey:'digitalStatus'}
      );

      this.updateStatusToExpired(
        { endDate: offerEffectiveEndDate, status: this.offerRequest.info.nonDigitalStatus, statusKey:'nonDigitalStatus'}
      );
    
  }

  setDateDetails() {
    let offerEffectiveEndDate = this.offerRequest.rules.endDate
      .offerEffectiveEndDate;

    if (moment().isBefore(moment(offerEffectiveEndDate))) {
      this.isDisplayEdit = true;
    }

    this.sDate = mmDdYySlash_DateFormat(
      this.offerRequest.rules.startDate.offerEffectiveStartDate
    );
  
    this.endDate = mmDdYySlash_DateFormat(offerEffectiveEndDate);    
  }
  getSummaryPage(id) {
    const pCode = this.facetItemService?.programCodeSelected;
    let summarySrc = ROUTES_CONST.REQUEST.Summary;
    
    if (pCode) {
      summarySrc = pCode == CONSTANTS.SC ? ROUTES_CONST.REQUEST.Summary : ROUTES_CONST.REQUEST[`${pCode}Summary`];
    }
    return `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${summarySrc}/${id}`;
  }

  getQueryParams(){
    return this.featureFlagsService.isOfferRequestArchivalEnabled && this.commonSearchService.isShowExpiredInQuery ? { 'E': this.commonSearchService.isShowExpiredInQuery } : '';
  }

  secureBulkAssignUpdateCheckboxByUserPermissions() {
    let bulkOptionAvailable = false;

    const permissions = this._permissionsService.getPermissions();
    let permissionArray = [CONSTANTS.Permissions.DoBatchAssign, 
      CONSTANTS.Permissions.DoBatchUpdateOfferDates, 
      CONSTANTS.Permissions.DoBatchSubmit, 
      CONSTANTS.Permissions.DoBatchProcess,
      CONSTANTS.Permissions.DoBatchExport,
      CONSTANTS.Permissions.DoBatchCopy];
    if (permissions) {
      const permissionAvailable = permissionArray?.some(permssion => Object.keys(permissions)?.includes(permssion))
      if (permissionAvailable) {
        bulkOptionAvailable = true;
      } else {
        bulkOptionAvailable = false;
      }
    } else {
      bulkOptionAvailable = false;
    }


    // Hide the check box when Do permission is not available on the offer record.
    if (!bulkOptionAvailable) {
      this.bulkOptionsCheckBoxcssBasedOnPermissions = 'hide';
    } else {
      this.bulkOptionsCheckBoxcssBasedOnPermissions = '';
    }
  }
  collapseAction() {
    this.showOfferList = false;
    this.categoryColor = '#F6F6F6';
  }
  expandAction() {
    this.showOfferList = !this.showOfferList;
    this.categoryColor = '#FFFFFF';
  }
  checked: boolean = false;
  checkValue() {
    // intentionally left empty
  }
}

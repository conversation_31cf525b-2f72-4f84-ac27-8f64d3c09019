<ng-container *ngIf="regionsForm">
    <form [formGroup]="regionsForm">
      <div>
        <h3 class="modal-title pull-left px-0">Create Copies</h3>
        <h5>For Selected region(s)</h5>
        <div formArrayName="regions" class="row" >
          <div class="col-12">
              <label for="selectAll">
                <input type="checkbox" (change)="onSelectAll($event)"  id="selectAll" [checked]="isAllRegionsSelected">Select All
              </label>
          </div>
          <div class="col-6"  *ngFor="let item of regions.controls ; let count = count; let i = index" [formGroupName]="i">
             <label for="regionIds{{regionsData[i].code}}">
              <input type="checkbox"  id="regionIds{{regionsData[i].code}}" [formControl]="item.controls.selected" /> {{regionsData[i].code}} {{regionsData[i].name}}
             </label>
          </div>
        </div>
      </div>
      <div  class="popupButtons">
        <label class="anchor-link-blue cancel-btn cursor-pointer mr-6" (click)="modalRef.hide()"><u>Cancel</u></label>
        <button type="submit" class="btn btn-primary save-btn" [disabled]="!isFormValid" (click)="onSubmit()">
          Save
        </button>
      </div>
    </form>
  </ng-container>
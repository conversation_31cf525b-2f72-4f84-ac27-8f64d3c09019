import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { UntypedFormBuilder, UntypedFormGroup, UntypedFormControl } from '@angular/forms';
import { RegionsMultipleCopyComponent } from './regions-multiple-copy.component';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { ToastrService } from 'ngx-toastr';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';

describe('RegionsMultipleCopyComponent', () => {
  let component: RegionsMultipleCopyComponent;
  let fixture: ComponentFixture<RegionsMultipleCopyComponent>;
  let fb: UntypedFormBuilder;

  const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getAppData']);
  const offerRequestBaseServiceSpy = jasmine.createSpyObj('OfferRequestBaseService', ['regionsMultipleCopyOfferRequest'], {
    requestForm: new UntypedFormGroup({
      info: new UntypedFormGroup({
        regionId: new UntypedFormControl([1, 2]),
        id: new UntypedFormControl('TEST_ID')
      })
    }),
    selectedRegionId: [1, 2],
    offerRequestId: 'TEST_ID',
    getProgramCode: () => 'TEST_PROGRAM'
  });
  const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success']);
  const featureFlagsServiceSpy = jasmine.createSpyObj('FeatureFlagsService', ['isUJActionEnabled']);
  const queryGeneratorSpy = {}; // if needed
  const bulkUpdateServiceSpy = jasmine.createSpyObj('BulkUpdateService', ['doRegionalCopy']);

  beforeEach(waitForAsync(() => {
    // Set up the getAppData spy to return sample regions.
    initialDataServiceSpy.getAppData.and.returnValue({
      regions: [
        { name: 'Region1', code: 1 },
        { name: 'Global', code: 0 },
        { name: 'Region2', code: 2 },
        { name: 'Region3', code: 3 }
      ]
    });
    
    TestBed.configureTestingModule({
      declarations: [RegionsMultipleCopyComponent],
      providers: [
        { provide: InitialDataService, useValue: initialDataServiceSpy },
        { provide: OfferRequestBaseService, useValue: offerRequestBaseServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: FeatureFlagsService, useValue: featureFlagsServiceSpy },
        { provide: QueryGenerator, useValue: queryGeneratorSpy },
        { provide: BulkUpdateService, useValue: bulkUpdateServiceSpy },
        UntypedFormBuilder
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
    
    fb = TestBed.inject(UntypedFormBuilder);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RegionsMultipleCopyComponent);
    component = fixture.componentInstance;
    // Set a dummy modalRef with a hide() method.
    component.modalRef = { hide: jasmine.createSpy('hide') };
    component.createForm();
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  describe('onSelectAll', () => {
    it('should call toggleAllRegionsSelection with true when event.target.checked is true', () => {
      spyOn(component, 'toggleAllRegionsSelection');
      const event = { target: { checked: true } };
      component.onSelectAll(event);
      expect(component.toggleAllRegionsSelection).toHaveBeenCalledWith(true);
    });

    it('should call toggleAllRegionsSelection with false when event.target.checked is false', () => {
      spyOn(component, 'toggleAllRegionsSelection');
      const event = { target: { checked: false } };
      component.onSelectAll(event);
      expect(component.toggleAllRegionsSelection).toHaveBeenCalledWith(false);
    });

    it('should throw an error if event is null', () => {
      expect(() => component.onSelectAll(null)).toThrowError();
    });

    it('should throw an error if event.target is undefined', () => {
      const event = {};
      expect(() => component.onSelectAll(event)).toThrowError();
    });
  });

  describe('toggleAllRegionsSelection', () => {
    it('should initialize with regions form controls', () => {
      expect(component.regions).toBeTruthy();
      expect(component.regions.controls.length).toBeGreaterThan(0);
    });

    it('should handle an empty regions array without throwing an error', () => {
      component.regionsForm.setControl('regions', fb.array([]));
      fixture.detectChanges();

      expect(() => component.toggleAllRegionsSelection(true)).not.toThrow();
    });

    it('should throw an error if a region control does not have a "selected" field', () => {
      const faultyControl = fb.group({ notSelected: [false] });
      component.regionsForm.setControl('regions', fb.array([faultyControl]));
      fixture.detectChanges();

      expect(() => component.toggleAllRegionsSelection(true)).toThrow();
    });
  });

  describe('when feature flag "RegionalCopy" is disabled', () => {
    beforeEach(() => {
      featureFlagsServiceSpy.isUJActionEnabled.and.returnValue(false);
    });

    it('should call regionsMultipleCopyOfferRequest and display success toast when response is truthy', () => {
      // Arrange: Set up a region so that requestPayload.regionIds is not empty.
      const regionArray = [{ code: 1, selected: true }];
      component.regionsForm.setControl('regions', fb.array(regionArray.map(r => fb.group(r))));
      fixture.detectChanges();

      // Use the existing spy to set the return value.
      offerRequestBaseServiceSpy.regionsMultipleCopyOfferRequest.and.returnValue(of(true));
      component.modalRef.hide.calls.reset();
      toastrServiceSpy.success.calls.reset();

      // Act:
      component.onSubmit();

      // Assert:
      expect(offerRequestBaseServiceSpy.regionsMultipleCopyOfferRequest)
        .toHaveBeenCalledWith(component.requestPayload);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith(
        "Creating Copies",
        "",
        { timeOut: 3000, closeButton: true }
      );
    });

    it('should call regionsMultipleCopyOfferRequest and not display success toast when response is falsy', () => {
      // Arrange: Set up a region.
      const regionArray = [{ code: 1, selected: true }];
      component.regionsForm.setControl('regions', fb.array(regionArray.map(r => fb.group(r))));
      fixture.detectChanges();

      // Set the return value using the already-spied method.
      offerRequestBaseServiceSpy.regionsMultipleCopyOfferRequest.and.returnValue(of(false));
      component.modalRef.hide.calls.reset();
      toastrServiceSpy.success.calls.reset();

      // Act:
      component.onSubmit();

      // Assert:
      expect(offerRequestBaseServiceSpy.regionsMultipleCopyOfferRequest)
        .toHaveBeenCalledWith(component.requestPayload);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrServiceSpy.success).not.toHaveBeenCalled();
    });
  });
  
  describe('when feature flag "RegionalCopy" is enabled', () => {
    beforeEach(() => {
      featureFlagsServiceSpy.isUJActionEnabled.and.returnValue(true);
    });

    it('should call bulkService.doRegionalCopy and display success toast when jobId is non-empty', () => {
      const regionArray = [{ code: 1, selected: true }];
      component.regionsForm.setControl('regions', fb.array(regionArray.map(r => fb.group(r))));
      fixture.detectChanges();

      bulkUpdateServiceSpy.doRegionalCopy.and.returnValue(of({ jobId: '12345' }));
      component.modalRef.hide.calls.reset();
      toastrServiceSpy.success.calls.reset();

      component.onSubmit();

      expect(bulkUpdateServiceSpy.doRegionalCopy).toHaveBeenCalledWith(
        "OR",
        "REGIONAL_COPY",
        component.requestPayload.programCode,
        jasmine.objectContaining({ query: `requestId=(${component.requestPayload.offerRequestId});` }),
        component.requestPayload.regionIds
      );
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith(
        "Creating copies for regions",
        '',
        { timeOut: 3000, closeButton: true }
      );
    });

    it('should call bulkService.doRegionalCopy and not display success toast when jobId is empty', () => {
      const regionArray = [{ code: 1, selected: true }];
      component.regionsForm.setControl('regions', fb.array(regionArray.map(r => fb.group(r))));
      fixture.detectChanges();

      bulkUpdateServiceSpy.doRegionalCopy.and.returnValue(of({ jobId: '' }));
      component.modalRef.hide.calls.reset();
      toastrServiceSpy.success.calls.reset();

      component.onSubmit();

      expect(bulkUpdateServiceSpy.doRegionalCopy).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrServiceSpy.success).not.toHaveBeenCalled();
    });
  });
  
  describe('ngOnInit filtering and sorting with non-empty selectedRegionId', () => {
  it('should filter out regions named "Global" and those with codes in selectedRegionId, then sort ascending by code', () => {
    offerRequestBaseServiceSpy.selectedRegionId = [1, 2];
    const customRegions = [
      { name: 'Region3', code: 3 },
      { name: 'Region1', code: 1 },
      { name: 'Global', code: 0 },
      { name: 'Region2', code: 2 },
      { name: 'Region4', code: 4 }
    ];
    initialDataServiceSpy.getAppData.and.returnValue({ regions: customRegions });
    component.filterRegionsData = (data) => data;
    
    component.ngOnInit();
    
    const expected = [
      { name: 'Region3', code: 3, selected: false },
      { name: 'Region4', code: 4, selected: false }
    ];
    expect(component.regionsData).toEqual(expected);
  });
});

});
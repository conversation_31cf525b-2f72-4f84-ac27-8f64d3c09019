import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-regions-multiple-copy',
  templateUrl: './regions-multiple-copy.component.html',
  styleUrls: ['./regions-multiple-copy.component.scss']
})
export class RegionsMultipleCopyComponent extends UnsubscribeAdapter implements OnInit {

  @Input() modalRef;
  regionsForm: UntypedFormGroup;
  regionsData: any = [];


  constructor(private _initialDataService: InitialDataService, private fb: UntypedFormBuilder,
    private offerRequestBaseService: OfferRequestBaseService, private _toastr: ToastrService,
    private featureFlagService:FeatureFlagsService, private queryGenerator: QueryGenerator,
    private bulkService: BulkUpdateService) {
    super();
  }

  ngOnInit(): void {
    const appData = this._initialDataService.getAppData();
    let filterRegionsData = appData.regions.filter((regionObj) =>
    regionObj.name !== "Global" && 
    !this.offerRequestBaseService.selectedRegionId?.includes(regionObj.code))?.sort((a, b) => a.code < b.code ? -1 : 1);
    this.regionsData = this.filterRegionsData(filterRegionsData);
    this.createForm();
  }

  filterRegionsData(filterRegionsData) {
    let round = Math.round((filterRegionsData.length / 2));
    let output = [];
    for (let i = 0; i < round; i++) {
      if (filterRegionsData[round + i]) {
        output.push(filterRegionsData[i], filterRegionsData[round + i]);
      } else {
        output.push(filterRegionsData[i]);
      }
    }
    return output;
  }
  
  onSubmit() {
    if(this.featureFlagService.isUJActionEnabled("RegionalCopy"))
    {
      let query: any = {}, queryGenerator;
      queryGenerator = "requestId=(" + this.requestPayload?.offerRequestId + ");";
      query.query = queryGenerator;

      this.bulkService.doRegionalCopy("OR","REGIONAL_COPY",this.requestPayload?.programCode,query,this.requestPayload?.regionIds)
      .subscribe((response: any) => {
        this.modalRef.hide();
        if(response.jobId !== null && response.jobId.length > 0)
        {
          this._toastr.success(`Creating copies for regions`, '',                 {
            timeOut: 3000,
            closeButton: true
          });
        }
      });
    }
    else{
      this.offerRequestBaseService.regionsMultipleCopyOfferRequest(this.requestPayload).subscribe((response) => {
        if (response) {
          this._toastr.success("Creating Copies", "", {
            timeOut: 3000,
            closeButton: true,
          });
        }
      });
      this.modalRef.hide();
    }
   
  }

  get selectedRegionIds() {
    let { regions } = this.regionsForm.value;
    return regions?.reduce((array, item) => {
      if (item.selected) {
        array.push(Number(item.code))
      }
      return array;
    }, []);
  }

  get requestPayload() {
    return {
      programCode: this.offerRequestBaseService.getProgramCode(),
      offerRequestId: this.offerRequestBaseService.offerRequestId,
      regionIds: this.selectedRegionIds
    };
  }

  createForm() {
    this.regionsForm = this.fb.group({
      regions: this.fb.array([])
    });
    this.setRegions(this.regionsData);
  }
  get isFormValid() {
    const { regions } = this.regionsForm.value;
    return regions?.some((region) => region.selected);
  }
  onSelectAll(event) {
    this.toggleAllRegionsSelection(event.target.checked);
  }
  toggleAllRegionsSelection(selection) {
    for (let i = 0; i < this.regions.controls.length; i++) {
      const ctrlValue = this.regions.at(i) as UntypedFormGroup;
      ctrlValue.get("selected").setValue(selection)
    }
  }
  setRegions(regions) {
    const regionsFGs = regions.map(region => {
      region.selected = false;
      return this.fb.group(region)
    });
    const regionsFormArray = this.fb.array(regionsFGs);
    this.regionsForm.setControl('regions', regionsFormArray);
  }

  get regions(): UntypedFormArray {
    return this.regionsForm.get('regions') as UntypedFormArray;
  };

  get isAllRegionsSelected() {
    const regions = this.regions.value;
    return regions.every((ele) => ele.selected);
  }

}

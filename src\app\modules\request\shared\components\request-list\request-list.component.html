<div class="row">
  <div class="cb-width"></div>
  <div class="col row">
    <div class="col-10  list-border">
      <div class="row pt-2">
        <div class="col-3">
          <label class=" text-label m-0"><a class="offers-link"
              (click)="onClickOfferId(offer.externalOfferId)">{{ offer.externalOfferId }}</a></label>
        </div>
        <div class="col-2">
          <ng-container *ngIf="offer.storeGroupName; else notAvailable">
            <label class="text-label m-0">{{ this.offer.storeGroupName }}</label>
          </ng-container>
        </div>
        <div class="col-2">
          <ng-container *ngIf="this.offer.productGroupName; else notAvailable">
            <label class="text-label m-0">{{ this.offer.productGroupName }}</label>
          </ng-container>
        </div>
        <div class="col-2">
          <span class="d-inline-flex">
            <span class="p-0 text-center" [ngClass]="getOfferStatusClass(configData.offerStatuses[offer.offerStatus])">{{
              configData.offerStatuses[offer.offerStatus]
            }}</span>
          </span>
        </div>
        <div class="col-1">
          <img *ngIf="offer.isPodApplicable" src="assets/icons/check-green-icon.svg" alt="">
        </div>
        <div class="col-2">
          <label class="text-label m-0">{{
            offer.isApplicableToJ4U ? "Digital" : "Non-Digital"
          }}</label>
        </div>
      </div>
    </div>
    <div class="col-2 p-0 list-border">
      <div class="col p-0">
        <ng-container *ngIf="this.offer.discount; else notAvailable">
          <label class="text-label m-0">{{
            configData.amountTypes[this.offer.discount]
          }}</label>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<ng-template #notAvailable>
  <label class="text-label m-0">NA</label>
</ng-template>
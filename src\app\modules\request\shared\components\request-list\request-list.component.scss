@import "scss/colors";

.actions-button {
  background-color: #ffffff !important;
  //color: #000000 !important;
  border-color: $grey-lighter-hex !important;
}
.dropdown-toggle::after {
  border: none;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+);
  vertical-align: 0;
  background-repeat: no-repeat;
}
.list-item-container {
  border: 1px solid $grey-lightest-rgb;
  background: $grey-lightest-rgb;
}
.bold-label {
  font-weight: 700;
  font-size: 14px;
}
.text-label {
  font-size: 14px;
  word-break: break-all;
}
.offers-link {
  color: $theme-primary !important;
}
.deploy-status-background{
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #11c0d0;
  color: #11c0d0;
  font-weight: bold;
}
.publish-status-background{
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #53821E;
  color: #53821E;
  font-weight: bold;
}
.expired-status-background{
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #b75aff;
  color: #B75AFF;
  font-weight: bold;
}
.preview-status-background{
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #18C419;
  color: #18C419;
  font-weight: bold;
}
.cancelled-status-background{
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #DF353B;
  color: #DF353B;
  font-weight: bold;
}
.draft-status-background{
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #FF6000;
  color: #FF6000;
  font-weight: bold;
}

@media (min-width: 320px) {
  .bold-label {
    font-weight: 700;
    font-size: 10px;
  }
  .text-label {
    font-size: 10px;
  }
}

@media (min-width: 576px) {
  .bold-label {
    font-weight: 700;
    font-size: 12px;
  }
  .text-label {
    font-size: 12px;
  }
}

@media (min-width: 768px) {
  .bold-label {
    font-weight: 700;
    font-size: 12px;
  }
  .text-label {
    font-size: 12px;
  }
}

@media (min-width: 1198px) {
  .bold-label {
    font-weight: 700;
    font-size: 12px;
  }
  .text-label {
    font-size: 12px;
  }
}

@media (min-width: 1281px) {
  .bold-label {
    font-weight: 700;
    font-size: 14px;
  }
  .text-label {
    font-size: 14px;
  }
}

// @media (min-width: 1700px) {
// }

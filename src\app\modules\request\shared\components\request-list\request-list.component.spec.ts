import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RequestListComponent } from './request-list.component';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { CommonService } from '@appServices/common/common.service';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { NO_ERRORS_SCHEMA } from '@angular/core';


describe('RequestListComponent', () => {
  let component: RequestListComponent;
  let fixture: ComponentFixture<RequestListComponent>;

  beforeEach(async () => {
    const initialDataServiceStub = {
      getAppData: jasmine.createSpy('getAppData').and.returnValue({}),
      openInNewTab: jasmine.createSpy('openInNewTab').and.returnValue({}),
    };

    const commonServiceStub = {
      isReqInEditing: jasmine.createSpy('isReqInEditing').and.returnValue(false),
    };

    const offerMappingServiceStub = {
      getOfferStatus: jasmine.createSpy('getOfferStatus').and.callFake((offerEffectiveEndDate) => {
        const todayDate = new Date();
        const endDate = new Date(offerEffectiveEndDate);
        return endDate < todayDate ? 'E' : null; // Mimic the actual logic
      }),
    };

    const requestFormServiceStub = {
      getOfferStatusClass: jasmine.createSpy('getOfferStatusClass').and.returnValue('status-class'),
    };

    await TestBed.configureTestingModule({
      declarations: [RequestListComponent],
      providers: [
        { provide: InitialDataService, useValue: initialDataServiceStub },
        { provide: CommonService, useValue: commonServiceStub },
        { provide: OfferMappingService, useValue: offerMappingServiceStub },
        { provide: RequestFormService, useValue: requestFormServiceStub },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RequestListComponent);
    component = fixture.componentInstance;

  
    component.offer = {
      id: 1,
      productGroupVersion: 1,
      discountVersion: 1,
      externalOfferId: '12345',
      storeGroupName: 'Store Group 1',
      productGroupName: 'Product Group 1',
      offerStatus: 'Active',
      isPodApplicable: true,
      isApplicableToJ4U: true,
    };
    component.offerRequest = {
      rules: {
        endDate: {
          offerEffectiveEndDate: '2025-12-31',
        },
      },
    };
    component.offersCriteria = [
      {
        storeGroupVersion: {
          productGroupVersions: [
            {
              id: 1,
              productGroup: { name: 'Group 1' },
              discountVersion: {
                id: 1,
                discounts: [{ benefitValueType: 'Discount' }],
              },
            },
          ],
        },
      },
    ];

    component.configData = {
      offerStatuses: {
        Active: 'Active',
        Expired: 'Expired',
      },
      amountTypes: {
        Discount: 'Discount',
      },
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set productGroup and discount correctly in ngOnInit', () => {
    component.offersCriteria = [
      {
        storeGroupVersion: {
          productGroupVersions: [
            {
              id: 1,
              productGroup: { name: 'Group 1' },
              discountVersion: {
                id: 1,
                discounts: [{ benefitValueType: 'Discount' }],
              },
            },
          ],
        },
      },
    ];
    component.offer = { productGroupVersion: 1, discountVersion: 1 };
    component.ngOnInit();
    expect(component.offer.productGroup).toBe('Group 1');
    expect(component.offer.discount).toBe('Discount');
  });

  it('should return the correct class for offer status', () => {
    const statusClass = component.getOfferStatusClass('Expired');
    expect(statusClass).toBe('status-class');
  });

  it('should call isReqInEditing and openInNewTab with correct URL when onClickOfferId is called', () => {
    const id = 123;
    const commonService = TestBed.inject(CommonService);
    const isReqInEditingSpy = commonService.isReqInEditing as jasmine.Spy;
    const initialDataService = TestBed.inject(InitialDataService);
    const openInNewTabSpy = initialDataService.openInNewTab as jasmine.Spy;

    isReqInEditingSpy.and.returnValue(true);

    component.onClickOfferId(id);

    expect(isReqInEditingSpy).toHaveBeenCalledWith(component.offerRequest);
    expect(openInNewTabSpy).toHaveBeenCalledWith(
      `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Summary}/${id}`
    );
  });

  it('should not set expired offer status if endDate is in the future', () => {
    component.offerRequest = {
      rules: {
        endDate: {
          offerEffectiveEndDate: '2025-12-31',
        },
      },
    };
    component.setExpiredOfferStatusIfAny();
    expect(component.offer.offerStatus).toBeUndefined();
  });

  it('should not set offerStatus if offerEffectiveEndDate is missing', () => {
    component.offerRequest = {
      rules: {
        endDate: {},
      },
    };
    component.setExpiredOfferStatusIfAny();
    expect(component.offer.offerStatus).toBeUndefined(); 
  });

  it('should filter offersCriteria correctly', () => {
    component.offersCriteria = [
      {
        storeGroupVersion: {
          productGroupVersions: [
            {
              id: 1,
              productGroup: { name: 'Group 1' },
              discountVersion: {
                id: 1,
                discounts: [{ benefitValueType: 'Discount' }],
              },
            },
          ],
        },
      },
    ];
    spyOn(component.offersCriteria, 'filter').and.callThrough();
    component.ngOnInit();
    expect(component.offersCriteria.filter).toHaveBeenCalled();
  });
});
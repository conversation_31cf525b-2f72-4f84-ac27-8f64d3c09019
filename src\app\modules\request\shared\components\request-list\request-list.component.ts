import { Component, Input, OnInit } from "@angular/core";
import { ROUTES_CONST } from "@appConstants/routes_constants";

import { OfferMappingService } from "@appOffersServices/offer-mapping.service";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { CommonService } from "@appServices/common/common.service";
import { InitialDataService } from "@appServices/common/initial.data.service";

@Component({
  selector: "request-list",
  templateUrl: "./request-list.component.html",
  styleUrls: ["./request-list.component.scss"],
})
export class RequestListComponent implements OnInit {
  @Input("offer") offer: any = {};
  @Input("offerRequest") offerRequest: any = {};
  @Input("offersCriteria") offersCriteria: any = [];

  configData;
  constructor(
    private _initialDataService: InitialDataService,
    private comomonService: CommonService,
    private _offerMappingService: OfferMappingService,
    public _requestFormService: RequestFormService
  ) {
    this.configData = this._initialDataService.getAppData();
  }
  ngOnInit() {
    this.setExpiredOfferStatusIfAny();
    this.offersCriteria.filter((e) => {
      e.storeGroupVersion.productGroupVersions.map((e) => {
        this.offer.productGroup =
          e.id === this.offer.productGroupVersion ? e.productGroup.name : null;

        e.discountVersion.id === this.offer.discountVersion &&
          e.discountVersion.discounts.map((e) => {
            this.offer.discount = e.benefitValueType
              ? e.benefitValueType
              : null;
          });
      });
    });
  }
  setExpiredOfferStatusIfAny() {
    const { endDate } = this.offerRequest && this.offerRequest.rules;
    if (!endDate || !endDate.offerEffectiveEndDate) {
      this.offer.offerStatus = undefined;
      return;
    }

    const offerStatus = this._offerMappingService.getOfferStatus(
      endDate.offerEffectiveEndDate
    );

    if (this.offer && offerStatus) {
      this.offer.offerStatus = offerStatus;
    } else {
      this.offer.offerStatus = undefined;
    }
  }
  onClickOfferId(id) {
    let editUrl = `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Edit}/${id}/${ROUTES_CONST.OFFERS.OfferDefinition}`;
    const isReqInEditing = this.comomonService.isReqInEditing(
      this.offerRequest
    );
    if (isReqInEditing) {
      editUrl = `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Summary}/${id}`;
    }

    this._initialDataService.openInNewTab(editUrl);
  }

  getOfferStatusClass(offerStatus){
    return this._requestFormService.getOfferStatusClass(offerStatus);
  }
  
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { RequestComponent } from './request.component';
import { CommonService } from '@appServices/common/common.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';

describe('RequestComponent', () => {
    let component: RequestComponent;
    let fixture: ComponentFixture<RequestComponent>;
    let commonServiceMock: any;
    let featureFlagsServiceMock: any;

    beforeEach(async () => {
        commonServiceMock = {
            getEventsData: jasmine.createSpy('getEventsData').and.returnValue(of({ dynaEvents: [{ id: 1, name: 'Event1' }] })),
            eventDataSrc: { next: jasmine.createSpy('next') }
        };

        featureFlagsServiceMock = {};

        await TestBed.configureTestingModule({
            declarations: [RequestComponent],
            providers: [
                { provide: CommonService, useValue: commonServiceMock },
                { provide: FeatureFlagsService, useValue: featureFlagsServiceMock }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(RequestComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call getInitialDataForEvents on ngOnInit', () => {
        spyOn(component, 'getInitialDataForEvents');
        component.ngOnInit();
        expect(component.getInitialDataForEvents).toHaveBeenCalled();
    });

    it('should subscribe to getEventsData and update eventDataSrc if data exists', () => {
        component.getInitialDataForEvents();
        expect(commonServiceMock.getEventsData).toHaveBeenCalled();
        expect(commonServiceMock.eventDataSrc.next).toHaveBeenCalledWith([{ id: 1, name: 'Event1' }]);
    });

    it('should not update eventDataSrc if getEventsData returns no data', () => {
        commonServiceMock.getEventsData.and.returnValue(of({}));
        component.getInitialDataForEvents();
    });
});
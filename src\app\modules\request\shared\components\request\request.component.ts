
import { Component, OnInit } from '@angular/core';
import { CommonService } from '@appServices/common/common.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';

@Component({
  selector: 'app-request',
  templateUrl: './request.component.html',
  styleUrls: []
})
export class RequestComponent extends UnsubscribeAdapter implements OnInit {
  constructor(private commonService: CommonService, private featureFlagService: FeatureFlagsService) {
    super();
  }

  ngOnInit(): void {
    this.getInitialDataForEvents();
  }
  getInitialDataForEvents() {
   
      this.subs.sink = this.commonService.getEventsData()?.subscribe((res: any)=>{
        if(res && res.dynaEvents && res.dynaEvents.length) {
          this.commonService.eventDataSrc.next(res.dynaEvents);
        }
      })
    
  }
}

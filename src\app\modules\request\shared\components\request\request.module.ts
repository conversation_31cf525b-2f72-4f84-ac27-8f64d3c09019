import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { BsModalRef, ModalModule } from 'ngx-bootstrap/modal';
import { ProgressbarModule } from 'ngx-bootstrap/progressbar';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { CommonModule } from '@angular/common';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { FacetsModule } from '@appShared/components/common/facets/facets.module';
import { CommentsModule } from '../../../../comments/comments.module';
import { DisclaimerComponent } from '../modal-views/disclaimer/disclaimer.component';
import { LookUpComponent } from '../modal-views/look-up/look-up.component';

import { NgModule } from '@angular/core';
import { NgDropFilesDirective } from '@appDirectives/ng-drop-files.directive';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';


import { SafeHTMLPipe } from '@appShared/pipes/safe-html.pipe';
import { RequestRoutingModule } from '../../../main-routing/request-routing.module';

import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { AppCommonModule } from '../../../../common/app.common.module';
import { UploadImageComponent } from '../modal-views/upload-image/upload-image.component';

import { DigitDecimaNumberDirectiveModule } from '@appDirectives/digit-decimal/digit.decimal.module';
import { LetDirectiveModule } from "@appDirectives/let/let.module";
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { OnlyNumberDirectiveModule } from '@appDirectives/only-number/only.number.module';
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { AppSearchFilterCommonModule } from '@appModules/common/app-search-filter.common.module';
import { OfferRequestInputSearchComponent } from '@appModules/request/core/offer-request/management/components/input-search/offer-request-input-search/offer-request-input-search.component';
import { OfferRequestManagmentBaseComponent } from '@appModules/request/core/offer-request/management/components/offer-request-managment-base/offer-request-managment-base.component';
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { FilterHeaderModule } from '@appShared/components/management/filter-header/filter-header.module';
import { SidebarModule } from '@appShared/ng-sidebar';
import { NgxConfirmBoxModule, NgxConfirmBoxService } from '@appShared/ngx-confirm-box';
import { LoadDynamicModule } from '../load-dynamic/load-dynamic.module';
import { RequestComponent } from './request.component';


@NgModule({
    declarations: [
        RequestComponent,
        NgDropFilesDirective,
        SafeHTMLPipe,
        UploadImageComponent,
        DisclaimerComponent,
        LookUpComponent,
        OfferRequestManagmentBaseComponent,
        OfferRequestInputSearchComponent
    ],
    exports: [RequestComponent],
    imports: [
        LoadDynamicModule,
        AppCommonModule,
        CommonModule,
        RequestRoutingModule,
        FormsModule,
        ReactiveFormsModule,
        NgxDatatableModule,
        BsDatepickerModule.forRoot(),
        TabsModule.forRoot(),
        ModalModule.forRoot(),
        TypeaheadModule.forRoot(),
        SidebarModule.forRoot(),
        FacetsModule,
        ApiErrorsModule,
        DigitDecimaNumberDirectiveModule,
        VarDirectiveModule,
        LetDirectiveModule,
        ProgressbarModule.forRoot(),
        TooltipModule.forRoot(),
        NgxConfirmBoxModule,
        NgSelectModule,
        NgOptionHighlightModule,
        OnlyNumberDirectiveModule,
        MarkAsTouchedOnFocusDirectiveModule,
        CommentsModule,
        PermissionsModule.forChild(),
        FilterHeaderModule,
        AppSearchFilterCommonModule
    ],
    providers: [{ provide: BsModalRef, useValue: undefined }, NgxConfirmBoxService]
})
export class RequestModule { }

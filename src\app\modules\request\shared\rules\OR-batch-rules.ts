import { CONSTANTS } from "@appConstants/constants";

export const REQUEST_BATCH_RULES = {
    SC: {
        components: ["RequestBatchActionComponent"],
        actions: [
            {
                displayName: "Assign",
                key: "assign",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchAssign],
                modalClass: "modal-xl",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                childActions: []
            },
            {
                displayName: "Process",
                key: "process",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchProcess],
                doDirectAsyncCall: true,
                apiFuncName: "bulkActionBpdRequest",
                isFirstPreCheck: false,
                isOfferTemplate: false,
                jobType: "OR",
                includeOfferTemplateFlag: true,
                childActions: [],
                asyncActionKey: "PROCESS",
                toastrMessage: "Offer request(s) are Processing"
            },
            {
                displayName: "Update Offer Dates",
                key: "updateDate",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchUpdateOfferDates],
                modalClass: "modal-xl",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                childActions: []
            },
            {
                displayName: "Copy",
                key: "copy",
                permissionAllowed: [CONSTANTS.Permissions.DoSCBatchCopy],
                modalClass: "confirm-centered modal-dialog-centered bulk-copy-OR",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                childActions: []
            },
            {
                displayName: "Export",
                key: "export",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchExport],
                doDirectAsyncCall: true,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Export',
                universalJobApiFunc: "bulkActionBpdRequest",
                jobType: "OR",
                isFirstPreCheck: false,
                isOfferTemplate: false,
                childActions: [],
                asyncActionKey: "EXPORT",
                toastrMessage: "Offer request are being exported",
                featureFlag: "enableSCPodExport"
            }
        ]
    },
    GR: {
       
        components: ["RequestBatchActionComponent"],
        actions: [
            {
                displayName: "Submit",
                key: "submit",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchSubmit],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Submit',
                jobType: "OR",
                universalJobApiFunc: "bulkActionBpdRequest",
                childActions: [],
                isOfferTemplate: false,
                asyncActionKey: "SUBMIT",
                toastrMessage: "Offer request(s) are Submitting"
            },
            {
                displayName: "Process",
                key: "process",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchProcess],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Process',
                universalJobApiFunc: "bulkActionBpdRequest",
                jobType: "OR",
                isOfferTemplate: false,
                childActions: [],
                asyncActionKey: "PROCESS",
                toastrMessage: "Offer request(s) are Processing"
            },
            {
                displayName: "Delete",
                key: "delete",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchSubmit],
                modalClass: "modal-m",
                confirmationMsg: "Are you sure you want to Delete the selected offer request(s)? We will send you an email once the offer request(s) are deleted",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Delete',
                jobType: "OR",
                isOfferTemplate: false,
                universalJobApiFunc: "bulkActionBpdRequest",
                childActions: [],
                asyncActionKey: "DELETE",
                toastrMessage: "Offer request(s) are Deleting"
            },
            {
                displayName: "Copy",
                key: "copy",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchCopy],
                modalClass: "confirm-centered modal-dialog-centered bulk-copy-OR",
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Copy',
                jobType: "OR",
                isOfferTemplate: false,
                universalJobApiFunc: "bulkActionBpdRequest",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                childActions: []
            },
            {
                displayName: "Export",
                key: "export",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchExport],
                doDirectAsyncCall: true,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Export',
                universalJobApiFunc: "bulkActionBpdRequest",
                jobType: "OR",
                isFirstPreCheck: false,
                isOfferTemplate: false,
                childActions: [],
                asyncActionKey: "EXPORT",
                toastrMessage: "Offer request are being exported"
            }
        ]
    },
    SPD: {
       
        components: ["RequestBatchActionComponent"],
        actions: [
            {
                displayName: "Submit",
                key: "submit",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchSubmit],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Submit',
                universalJobApiFunc: "bulkActionBpdRequest",
                jobType: "OR",
                isOfferTemplate: false,
                childActions: [],
                asyncActionKey: "SUBMIT",
                toastrMessage: "Offer request(s) are Submitting"
            },
            {
                displayName: "Process",
                key: "process",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchProcess],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Process',
                universalJobApiFunc: "bulkActionBpdRequest",
                jobType: "OR",
                isOfferTemplate: false,
                childActions: [],
                asyncActionKey: "PROCESS",
                toastrMessage: "Offer request(s) are Processing"
            },
            {
                displayName: "Delete",
                key: "delete",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchSubmit],
                modalClass: "modal-m",
                confirmationMsg: "Are you sure you want to Delete the selected offer request(s)? We will send you an email once the offer request(s) are deleted",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobFlagValue: 'Delete',
                jobType: "OR",
                isOfferTemplate: false,
                universalJobApiFunc: "bulkActionBpdRequest",
                childActions: [],
                asyncActionKey: "DELETE",
                toastrMessage: "Offer request(s) are Deleting"
            },
            {
                displayName: "Copy",
                key: "copy",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchCopy],
                modalClass: "confirm-centered modal-dialog-centered bulk-copy-OR",
                doDirectAsyncCall: false,
                checkUniversalJobFeatureFlag: true,
                jobType: "OR",
                universalJobApiFunc: "bulkActionBpdRequest",
                universalJobFlagValue: 'Copy',
                isOfferTemplate: false,
                isFirstPreCheck: false,
                childActions: []
            },
            {
                displayName: "Export",
                key: "export",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchExport],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                checkUniversalJobFeatureFlag: true,
                universalJobApiFunc: "bulkActionBpdRequest",
                universalJobFlagValue: 'Export',
                isOfferTemplate: false,
                jobType: "OR",
                childActions: [],
                asyncActionKey: "EXPORT",
                toastrMessage: "Offer request are being exported"
            }
        ]
    },
    BPD: {
        
        components: ["RequestBatchActionComponent"],
        actions: [
            {
                displayName: "Expand Period",
                key: "expandPeriod",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchSubmit],
                doDirectAsyncCall: false,
                confirmationMsg: "Are you sure you would like to expand the selected offer requests for the given period into 4 weeks?",
                isFirstPreCheck: true,
                modalClass: "modal-lg",
                childActions: [],
                asyncActionKey: "EXPAND",
                includeOfferTemplateFlag: true,
                isOfferTemplate: false,
                apiFuncName:'preCheckBatchExpand',
                isResetSelection:false,
                postBatchSuccess: "getAllBpdRequest",
                jobType: "OR",
                toastrMessage: "Expanding the Period",
                errorMessage:"An Offer Request must be a Draft in order to Expand Period. Filter for Drafts and try again.",
                onPrecheckError: {
                    showOK: true,
                    showMore: false
                },
                errSuccessModalClass : "modal-lg",
            },
            {
                displayName: "Process",
                key: "process",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchProcess],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                childActions: [],
                jobType: "OR",
                apiFuncName: "bulkActionBpdRequest",
                postBatchSuccess: "getAllBpdRequest",
                asyncActionKey: "PROCESS",
                includeOfferTemplateFlag: true,
                isOfferTemplate: false,
                toastrMessage: "Offer request(s) are Processing"
            },
            {
                displayName: "Delete",
                key: "delete",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchSubmit],
                modalClass : "modal-m",
                jobType: "OR",
                apiFuncName: "bulkActionBpdRequest",
                confirmationMsg: "Are you sure you want to Delete the selected offer request(s)? We will send you an email once the offer request(s) are deleted",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                isOfferTemplate: false,
                includeOfferTemplateFlag: true,
                childActions: [],
                asyncActionKey: "DELETE",
                toastrMessage: "Offer request(s) are Deleting",
                postBatchSuccess: "getAllBpdRequest",
            },
            {
                displayName: "Copy",
                key: "copyBPD",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchCopy],
                modalClass: "confirm-centered modal-dialog-centered bulk-copy-OR",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                childActions: []
            },
            {
                displayName: "Export",
                key: "export",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchExport],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                includeOfferTemplateFlag: true,
                isOfferTemplate: false,
                childActions: [],
                jobType: "OR",
                apiFuncName: "bulkActionBpdRequest",
                postBatchSuccess: "getAllBpdRequest",
                asyncActionKey: "EXPORT",
                toastrMessage: "Offer Requests(s) are being exported"
            },
        ]
    }
}
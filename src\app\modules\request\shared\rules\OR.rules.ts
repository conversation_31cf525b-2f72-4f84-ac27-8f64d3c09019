/* Sets the configuration for different fields based on program code and type */
import { BPD_OR_RULES_DATA } from './OR_BPD.rules';

const conditionQUM = { Items: "ITEMS", Dollars: "DOLLARS", "Per Pound": "WEIGHT_VOLUME" };
export const OR_RULES = {
  "Item Discount": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Buy X Get X": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM :{ Items: "ITEMS", "Per Pound": "WEIGHT_VOLUME" }
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            itemLimit: {
              show: true,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Buy X Get Y": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },

  "Meal Deal": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 2,
      min: 2,
      max: 9
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Bundle": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 2,
      min: 2
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: true,
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'FREE',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },

  "Must Buy": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            itemLimit: {
              show: true,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.001
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Fab 5 / Score 4": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 1,
      min: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: true,
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },
  "WOD / POD": {
    isTiersSupported: true,
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0
    },
    tiers: {
      show: false,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM,
            isNonEditableInSubRows: true

          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null,
            min: 1
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: true
            },
            amount: {
              show: true,
              value: null,
              tiers: true,
              min: 0.001
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Store Closure": {
     version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0,
      min: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows:true
          },
          quantityUnitType: {
            show: false,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PERCENT_OFF_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Rewards - Accumulation": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: true,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: true,
            value: 1.00,
            isNonEditableInSubRows: true
          },
          minPurchase: {
            show: true,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'POINTS'
            },
            itemLimit: {
              show: false,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
            points: {
              show: true,
              value: null
            }
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Rewards - Flat": {
    isTiersSupported: true,
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: true,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.009,
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'REWARDS_POINTS'
            },
            itemLimit: {
              show: false,
              value: null
            },
            amount: {
              show: false,
              value: null,
              tiers: true,
              min: 0
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
            rewards: {
              show: true,
              value: null,
              min: 1
            }
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Continuity": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },
  "Enterprise Instant Win": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      instantWin: {
        numberOfPrizes: {
          show: true,
          value: '',
          min: 1
        },
        frequency: {
          show: true,
          value: ""
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: false,
            value: null
          },
          quantityUnitType: {
            show: false,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'PRICE_POINT_ITEMS'
            },
            itemLimit: {
              show: false,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            }
          }
        }
      }
    },
    conditionClassName: 'col-12',
    discountClassName: ''
  },
  "Alaska Airmiles": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 1,
      min: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'PRICE_POINT_ITEMS'
            },
            itemLimit: {
              show: false,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            }
          },
          airMiles: {
            type: 'US_AIR_MILES',
            points: {
              show: true,
              value: '',
              min: 1
            },
            name: {
              show: true,
              value: 'ALASKA_AIRMILES'
            }
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Item + Basket": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 2,
      min: 2
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PERCENT_OFF_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Custom": {
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: false,
            value: null
          },
          quantityUnitType: {
            show: false,
            value: 'ITEMS'
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'PRICE_POINT_ITEMS'
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Department": {
    isTiersSupported: true,
    version: {
      show: true,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0
    },
    tiers: {
      show: false,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM,
            isNonEditableInSubRows: true

          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null,
            min: 1
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: true
            },
            amount: {
              show: true,
              value: null,
              tiers: true,
              min: 0.001
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  }
};

export const GR_OR_RULES = {
  "Item Discount": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },miles: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Must Buy": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF'
            },
            itemLimit: {
              show: true,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.001
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },miles: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "WOD / POD": {
    isTiersSupported: true,
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0
    },
    tiers: {
      show: false,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM,
            isNonEditableInSubRows: true

          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null,
            min: 1
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: true
            },
            amount: {
              show: true,
              value: null,
              tiers: true,
              min: 0.001
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },miles: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Custom": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: null
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: false,
            value: null
          },
          quantityUnitType: {
            show: false,
            value: 'ITEMS'
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'AMOUNT_OFF'
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },miles: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Department": {
    isTiersSupported: true,
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0
    },
    tiers: {
      show: false,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: true,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM,
            isNonEditableInSubRows: true

          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null,
            min: 1
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: true
            },
            amount: {
              show: true,
              value: null,
              tiers: true,
              min: 0.001
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },miles: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
};

export const SPD_OR_RULES = {
  "Item Discount": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Buy X Get X": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM :{ Items: "ITEMS", "Per Pound": "WEIGHT_VOLUME" }
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            itemLimit: {
              show: true,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Buy X Get Y": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },

  "Meal Deal": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 2,
      min: 2,
      max: 9
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Bundle": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: true,
      checked: false,
      value: 2,
      min: 2
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: true,
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'FREE',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },

  "Must Buy": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            itemLimit: {
              show: true,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.001
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "WOD / POD": {
    isTiersSupported: true,
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0
    },
    tiers: {
      show: false,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM,
            isNonEditableInSubRows: true

          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null,
            min: 1
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: true
            },
            amount: {
              show: true,
              value: null,
              tiers: true,
              min: 0.001
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Rewards - Accumulation": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: true,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: true,
            value: 1.00,
            isNonEditableInSubRows: true
          },
          minPurchase: {
            show: true,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'POINTS'
            },
            itemLimit: {
              show: false,
              value: null
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
            points: {
              show: true,
              value: null
            }
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Rewards - Flat": {
    isTiersSupported: true,
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: true,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.009,
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'REWARDS_POINTS'
            },
            itemLimit: {
              show: false,
              value: null
            },
            amount: {
              show: false,
              value: null,
              tiers: true,
              min: 0
            },
            upTo: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
            rewards: {
              show: true,
              value: null,
              min: 1
            }
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Continuity": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: true,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS'
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-5',
    discountClassName: 'col-7'
  },
  "Item + Basket": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 2,
      min: 2
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            conditionQUM
          },
          amount: {
            show: true,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PERCENT_OFF_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Custom": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: false,
            value: null
          },
          quantityUnitType: {
            show: false,
            value: 'ITEMS'
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: false,
              value: 'PRICE_POINT_ITEMS'
            },
            amount: {
              show: false,
              value: null,
              min: 0.009
            },
            upTo: {
              show: false,
              value: null
            },
            itemLimit: {
              show: false,
              value: null
            },
            perLbLimit: {
              show: false,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  },
  "Department": {
    isTiersSupported: true,
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 0
    },
    tiers: {
      show: false,
      checked: false,
      value: 1
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        discountProduct: false,
        productGroup: {
          name: {
            show: true,
            value: null,
            isNonEditableInSubRows: true
          },
          quantityUnitType: {
            show: true,
            value: 'DOLLARS',
            conditionQUM,
            isNonEditableInSubRows: true

          },
          amount: {
            show: true,
            value: null,
            tiers: true,
            min: 0.001
          },
          minPurchase: {
            show: false,
            value: null,
            min: 1
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'AMOUNT_OFF',
              isNonEditableInSubRows: true
            },
            amount: {
              show: true,
              value: null,
              tiers: true,
              min: 0.001
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  }
};
export const BPD_TEMPLATE_RULES = {
  "Item Discount": {
    version: {
      show: false,
      checked: false,
      value: 1
    },
    product: {
      show: false,
      checked: false,
      value: 1
    },
    tiers: {
      show: false,
      checked: false,
      value: null
    },
    giftCard: {
      show: false,
      checked: false,
      value: null
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null
        },
        podStoreGroups: {
          show: false,
          value: null
        }
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null
          },
          quantityUnitType: {
            show: true,
            value: 'ITEMS',
            isNonEditableInSubRows: true,
            conditionQUM
          },
          amount: {
            show: false,
            value: null,
            min: 0.009
          },
          minPurchase: {
            show: false,
            value: null
          }
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null
            },
            discountType: {
              show: true,
              value: 'PRICE_POINT_ITEMS',
              isNonEditableInSubRows: false
            },
            amount: {
              show: true,
              value: null,
              min: 0.009
            },
            upTo: {
              show: true,
              value: null
            },
            itemLimit: {
              show: true,
              value: null
            },
            perLbLimit: {
              show: true,
              value: null
            },
          }
        }
      }
    },
    conditionClassName: 'col-6',
    discountClassName: 'col-6'
  }
};

export const BPD_OR_RULES = BPD_OR_RULES_DATA;
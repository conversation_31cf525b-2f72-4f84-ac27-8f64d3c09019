const conditionQUM = { Items: "ITEMS", Dollars: "DOLLARS", "Per Pound": "WEIGHT_VOLUME" };

export const BPD_OR_RULES_DATA = {
  "Item Discount": {
    version: {
      show: false,
      checked: false,
      value: 1,
    },
    product: {
      show: true,
      checked: false,
      value: 1,
    },
    tiers: {
      show: false,
      checked: false,
      value: null,
    },
    giftCard: {
      show: false,
      checked: false,
      value: null,
    },
    storeGroupVersion: {
      storeGroup: {
        nonDigitalRedemptionStoreGroups: {
          show: false,
          value: null,
        },
        digitalRedemptionStoreGroups: {
          show: true,
          value: null,
        },
        podStoreGroups: {
          show: false,
          value: null,
        },
      },
      productGroupVersions: {
        productGroup: {
          name: {
            show: true,
            value: null,
          },
          quantityUnitType: {
            show: true,
            value: "ITEMS",
            isNonEditableInSubRows: true,
            conditionQUM,
          },
          amount: {
            show: false,
            value: null,
            min: 0.009,
          },
          minPurchase: {
            show: false,
            value: null,
          },
        },
        discountVersion: {
          discounts: {
            includeProductGroupName: {
              show: false,
              value: null,
            },
            discountType: {
              show: true,
              value: "PRICE_POINT_ITEMS",
              isNonEditableInSubRows: false,
            },
            amount: {
              show: true,
              value: null,
              min: 0.009,
            },
            upTo: {
              show: true,
              value: null,
            },
            itemLimit: {
              show: true,
              value: null,
            },
            perLbLimit: {
              show: true,
              value: null,
            },
          },
        },
      },
    },
    conditionClassName: "col-6",
    discountClassName: "col-6",
  },
};

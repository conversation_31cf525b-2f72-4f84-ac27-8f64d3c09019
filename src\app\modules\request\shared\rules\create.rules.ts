import { Validators } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { STORE_GROUP_CONSTANTS } from "../../../groups/constants/store_group_constants";
import { BPD_OR_CREATE_RULES } from "./create.rules_BPD";

/**offer request create rules */
export const OFFER_REQUEST_CREATE_RULES = {
  SC: {
    offerRequestManagement: ["HomeComponent"],
    programCode: "Store Coupon",
    components: ['OfferReqBuilder', 'OfferRequestSectionComponent', 'NopaSectionComponent',
      'GeneralComponent', 'AdditonalDescriptionComponent', 'JustificationComponent',
      'OfferDetailsComponent'],
    summaryComponents: ['OfferReqBuilder', 'OfferRequestSectionComponent', 'NopaSectionComponent',
      'GeneralComponent', 'AdditonalDescriptionComponent', 'JustificationComponent',
      'OfferDetailsComponent'],
    offerRequest: {
      channel:
      {
        readOnly: false,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        value: null,
        required: true,
        label: "Channel",
        error: 'Channel is required'
      },
      department: {
        readOnly: false,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        value: null,
        required: true,
        label: "Department",
        error: "Department is required"
      },
      group: {
        readOnly: false,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        value: null
      },
      division: {
        readOnly: false,
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        value: null
      },
      brandAndSize: {
        readOnly: false,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        value: null,
      },
      customerSegment: {
        readOnly: false,
        validate: ['submit'],
        impactFieldForOfferUpdate: false,
        value: null
      },
      startDate: {
        readOnly: false,
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        value: null
      },
      endDate: {
        readOnly: false,
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        value: null
      },
      initialSubscriptionOffer:{
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['initialSubscriptionOffer'],
        featureFlag: 'enableInitialSubscriptionOffers',
        featureFlagCheck: true,
        value: false,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Initial Subscription",
        showLabelAsValue:true,
        tristateCheck:true
      },
      offerLimits: {
        readOnly: false,
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        value: null
      },
      plu: {
        show: true,
        readOnly: false,
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        value: null
      },
      inAd: {
        show: true,
        readOnly: false,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        value: null
      }
    },
    funding: {
      show: true,
      nopaNumbers: {
        impactFieldForOfferUpdate: false
      },
      nopaStartDate: {
        impactFieldForOfferUpdate: false
      },
      nopaEndDate: {
        impactFieldForOfferUpdate: false
      },
      isBilled: {
        impactFieldForOfferUpdate: false
      },
      billingOptions: {
        impactFieldForOfferUpdate: false
      },
    },
    additionalInformation: {
      desc: {
        impactFieldForOfferUpdate: false
      }
    }
  },
  GR: {
    offerRequestManagement: ["GrOfferRequestManagementComponent"],
    programCode: "Grocery Reward",
    components: ['GrRequestSectionComponent',
      'GeneralComponent', 'GrAdditionalDescriptionComponent', 'JustificationComponent',
      'OfferDetailsComponent'],
    summaryComponents: ['GrRequestSectionComponent',
      'GeneralComponent', 'GrAdditionalDescriptionComponent', 'JustificationComponent',
      'OfferDetailsComponent'],
    podComponent: ["GrPodComponent"],
    offerRequest: {
      regionId: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'regionId'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Region",
        appDataOptions: STORE_GROUP_CONSTANTS.SEARCH_STORE_GROUP_NEW_API,
        optionKey: "name",
        optionValue: 'code',
        error: {
          required: 'Region is required'
        }
      },
      subProgramCode: {
        validate: ['save'],
        impactFieldForOfferUpdate: true,
        byPassValidateBeforeProcess: false,
        control: ['info', 'subProgramCode'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Sub Program Code",
        appDataOptions: "subProgramCodes",
        optionKey: "name",
        optionValue: 'code',
        error: {
          required: 'Sub Program Code is required'
        }
      },
      programType: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['info', "programType"],
        appDataOptions: CONSTANTS.OFFER_DETAILS_LIST_API,
        optionKey: "name",
        optionValue: 'code',
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Program Type",
        error: {
          required: 'Program Type is required'
        }
      },
      brandAndSize: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', "brandAndSize"],
        value: null,
        tooltip: true,
        maxLength: 100,
        tooltipTitle: "A few words to describe the products participating in the promotion (character limit = 100)",
        edit: {
          create: true,
          update: false
        },
        validators: [Validators.required],
        label: "Brand and Size",
        error: {
          required: 'Brand and Size is required'
        }
      },
      customerSegment: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customerSegment'],
        appDataOptions: "offerCustomerSegments",
        value: "Any Customer",
        validators: [Validators.required],
        edit: {
          create: true,
          update: false
        },
        label: "Segment",
        error: {
          required: 'Segment is required'
        }
      },
      offerEffectiveStartDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'startDate', 'offerEffectiveStartDate'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        tooltip: true,
        tooltipTitle: "The first active day of the offer",
        label: "Start Date",
        error: {
          required: 'Start Date is required'
        }
      },
      offerEffectiveEndDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'endDate', 'offerEffectiveEndDate'],
        value: null,
        tooltip: true,
        tooltipTitle: "The last day that the offer will be active. Offer ends at 11:59:59pm on the end date",
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "End Date",
        error: {
          required: 'End Date is required'
        }
      },
      usageLimitTypePerUser: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'usageLimitTypePerUser'],
        value: 'ONCE_PER_OFFER',
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        appDataOptions: "offerLimitsGR",
        label: "Offer Limit",
        error: {
          required: 'Offer Limit is required',
          customError: 'Usage limit for the Auto Rewards program type should be Unlimited.'
        }
      },
      usageLimitPerUser: {
        allowDecimals: false,
        onlyNumber: true,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'usageLimitPerUser'],
        validators: [],
        value: null,
        edit: {
          create: true,
          update: true
        },
        label: "Custom Limit",
        error: {
          required: 'Limit is required'
        }
      },
      customType: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customType'],
        value: null,
        edit: {
            create: true,
            update: true
        },
        appDataOptions: "offerUsageLimitCustomTypes",
        label: "Custom Type",   
    },
    getMiles: {
      validate: ['save', 'submit'],
      impactFieldForOfferUpdate: true,
      control: ['info', 'getMiles'],
      value: null,
      validators: [],
      featureFlag: "enableGRGetMiles",
      featureFlagCheck: true,
      edit: {
          create: true,
          update: true
      },
      label: "Get Miles",
      error: {
        required: 'Get Miles is required'
      }   
  },
    customUsage: {
      validate: ['save', 'submit'],
      impactFieldForOfferUpdate: true,
      control: ['rules', 'customUsage'],
      value: null,
      validators: [],
      featureFlag: "enableCustomUsage",
      featureFlagCheck: true,
      edit: {
          create: true,
          update: true
      },
      appDataOptions: "offerUsageLimitCustomUsage",
      label: "Custom Usage",
      error: {
        required: 'Custom Usage is required'
      }   
  },
    customPeriod: {
        allowDecimals: false,
        onlyNumber: true,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customPeriod'],
        validators: [],
        value: null,
        edit: {
            create: true,
            update: true
        },
        label: "Custom Period",
        error: {
            required: 'Period is required',
            customError: 'Period and Limit cannot both equal 1'
        }
        },
        pointsRequired: {
        validate: ['submit'],
        control: ['rules', 'pointsRequired'],
        impactFieldForOfferUpdate: true,
        value: null,
        allowDecimals: false,
        onlyNumber: true,
        featureFlag: 'enableRewardsSimplified',
        featureFlagCheck: false,
        maxLength:4,
        min:1,
        max:9999,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Points Required",
        error: {
          required: 'Points Required is required',
          customError: 'Points should be greater than 0.'
        }
      },
      rank: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['rules', 'rank'],
        allowDecimals: false,
        onlyNumber: true,
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Rank",
        error: {
          required: 'Rank is required'
        }
      },
      desc: {
        impactFieldForOfferUpdate: false,
        control: ['info', 'desc'],
        value: null,
        maxLength: 1000,
        edit: {
          create: true,
          update: true
        },
        label: "Additional Details"
      }
      },
    podDetails: {
      scene7ImageId: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'scene7ImageId'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Scene 7 Image ID",
        appDataOptions: "offerCustomerSegments",
        error: {
          customError: "Image ID is not found in Scene 7",
          required: 'Scene 7 Image ID is required'
        }
      },
      priceText: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'priceText'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        maxLength: 25,
        warning: {
          maxLength: 16,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        label: "Price Text",
        appDataOptions: "offerCustomerSegments",
        error: {
          required: 'Price Text is required'
        }
      },
      headline1: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'headline1'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        maxLength: 100,
        warning: {
          maxLength: 90,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        label: "Headline 1",
        appDataOptions: "offerCustomerSegments",
        error: {
          required: 'Headline 1 is required'
        }
      },
      headline2: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'headline2'],
        value: null,
        maxLength: 100,
        edit: {
          create: true,
          update: true
        },
        warning: {
          maxLength: 90,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        label: "Headline 2",
        appDataOptions: "offerCustomerSegments",
        validators: []
      },
      offerDescription: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'offerDescription'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        maxLength: 100,
        warning: {
          maxLength: 90,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        validators: [Validators.required],
        label: "Offer Description",
        appDataOptions: "offerCustomerSegments",
        error: {
          required: 'Offer Description is required'
        }
      },
      offerDetailsCode: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'offerDetailsCode'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: false,
          update: false
        },
        required: true,
        label: "Offer Details Code",
        appDataOptions: "offerCustomerSegments",
        error: {
          required: 'Offer Details Code is required'
        }
      },
      shoppingListCategory: {
        validate: ['submit','process'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'shoppingListCategory'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Shopping List Category",
        appDataOptions: "customerFriendlyProductCategories",
        error: {
          required: 'Shopping List Category is required'
        }
      },
      leftNavCategory: {
        validate: ['process'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'leftNavCategory'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Left Nav Category",
        appDataOptions: "customerFriendlyProductCategories",
        isMultipleSelection: true,
        error: {
          required: 'Left Nav Category is required'
        }
      },
      podUsageLimit: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'podUsageLimit'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Usage",
        appDataOptions: "podUsageLimitsGR",
        error: {
          required: 'podUsageLimit is required',
          customError: 'Usage should be Unlimited for Auto Reward'
        },
        validators: []
      },
      multiClipLimit: {
        validate: ['save', 'submit'],
        allowDecimals: false,
        onlyNumber: true,
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'multiClipLimit'],
        validators: [],
        value: null,
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Multi Clip Limit",
        error: {
          required: 'Multi Clip Limit is required',
          customError: 'Multi Clip Limit must be greater than 0'
        }
      },
      displayStartDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'displayStartDate'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: false,
          update: false
        },
        required: true,
        label: "Display Start Date",
        appDataOptions: "offerCustomerSegments",
        error: {
          required: 'Display Start Date is required'
        }
      },
      displayEndDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        validators: [Validators.required],
        control: ['podDetails', 'displayEndDate'],
        value: null,
        edit: {
          create: true,
          update: true
        },

        required: true,
        label: "Display End Date",
        appDataOptions: "offerCustomerSegments",
        error: {
          required: 'Display End Date is required',
          customError: 'Display EndDate should be within range of offer StartDate and EndDate'
        }
        }
    }
  },
  SPD: {
    offerRequestManagement: ["SpdOfferRequestManagementComponent"],
    programCode: "Specialty PD",
    components: ['SpdRequestSectionComponent',
      'GeneralComponent', 'JustificationComponent', 'SpdAdditionalDescriptionComponent',
      'OfferDetailsComponent'],
    summaryComponents: ['SpdRequestSectionComponent',
      'GeneralComponent', 'JustificationComponent', "SpdAdditionalDescriptionComponent",
      'OfferDetailsComponent'],
    podComponent: ["SPDPodComponent"],
    offerRequest: {
      regionId: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'regionId'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Region",
        appDataOptions: STORE_GROUP_CONSTANTS.SEARCH_STORE_GROUP_NEW_API,
        optionKey: "name",
        optionValue: 'code',
        error: {
          required: 'Region is required'
        }
      },
      deliveryChannel: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', "deliveryChannel"],
        appDataOptions: "offerDeliveryChannelsSPD",
        value: "DO",
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Coupon Channel",
        error: {
          required: 'Coupon Channel is required'
        }
      },
      programType: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['info', "programType"],
        appDataOptions: "programTypeSPD",
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Program Type",
        error: {
          required: 'Program Type is required'
        }
      },
      allocationCode: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['info', "allocationCode"],
        appDataOptions: "allocationCodeApi",
        value: "01",
        featureFlag: 'enableAllocationOffers',
        featureFlagCheck: true,
        optionKey: "name",
        optionValue: 'code',
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Allocation Code",
        error: {
          required: 'Allocation Code is required'
        }
      },
      allocationCodeName: {
        validate: [],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['info', "allocationCodeName"],
        appDataOptions: "",
        featureFlag: 'enableAllocationOffers',
        featureFlagCheck: true,
        value: "Default",
        optionKey: "name",
        optionValue: 'code',
        edit: {
          create: true,
          update: true
        }
      },
      allocationCriteriaList: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['info', "allocationCriteriaList"],
        appDataOptions: "allocationCriteria",
        value: [],
        featureFlag: 'enableAllocationCriteria',
        featureFlagCheck: false,
        optionKey: "name",
        optionValue: 'code',
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Allocation Criteria",
        error: {
          required: 'Allocation Criteria is required'
        }
      },
      behavioralAction: {
        validate: ['submit'],
        appDataOptions: "behavioralActions",
        impactFieldForOfferUpdate: true,
        control: ['info', "behavioralAction"],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "Behavioral Action",
        error: {
          required: 'Behavioral Action is required',
        }
      },
      programSubType: {
        validate: [],
        appDataOptions: "programSubTypeListAPI",
        impactFieldForOfferUpdate: true,
        control: ['info', "programSubType"],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Program Subtype",
        error: {
          required: 'Program Subtype is required',
          customError:"Rx Program Type can have only Program Subtypes Points or Coupons"  
        }
      },
      brandAndSize: {
        validate: [],
        impactFieldForOfferUpdate: true,
        control: ['info', "brandAndSize"],
        value: null,
        tooltip: true,
        tooltipTitle: "A few words to describe the products participating in the promotion (character limit = 100)",
        edit: {
          create: true,
          update: false
        },
        validators: [Validators.required],
        label: "Brand and Size",
        error: {
          required: 'Brand and Size is required'
        }
      },
      customerSegment: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customerSegment'],
        appDataOptions: "offerCustomerSegments",
        value: "Any Customer",
        validators: [Validators.required],
        edit: {
          create: true,
          update: false
        },
        label: "Segment",
        error: {
          required: 'Segment is required'
        }
      },
      period_week: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'period_week'],
        value: "",
        validators: [],
        edit: {
          create: true,
          update: false
        },
        label: "Period",
        error: {
          required: 'Period is required'
        }
      },
      periodId: {
        validate: [],
        impactFieldForOfferUpdate: true,
        control: ['info', 'periodId'],
        value: null,
        validators: [],
        edit: {
          create: true,
          update: false
        },
        label: "Period Id",
        error: {
          required: 'Period Id is required'
        }
        },
      points: {
        validate: ['save', 'submit'],
        allowDecimals: false,
        onlyNumber: true,
        impactFieldForOfferUpdate: true,
        control: ['info', 'points'],
        validators: [],
        value: null,
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Points",
        error: {
          required: 'Points is required'
        }
      },
      promoWeekId: {
        validate: [],
        impactFieldForOfferUpdate: true,
        control: ['info', 'promoWeekId'],
        value: null,
        validators: [],
        edit: {
          create: true,
          update: false
        },
        label: "Promo week Id",
        error: {
          required: 'Promo week id is required'
        }
        },
      offerEffectiveStartDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'startDate', 'offerEffectiveStartDate'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        tooltip: true,
        tooltipTitle: "The first active day of the offer",
        label: "Start Date",
        error: {
          required: 'Start Date is required'
        }
      },
      offerEffectiveEndDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'endDate', 'offerEffectiveEndDate'],
        value: null,
        tooltip: true,
        tooltipTitle: "The last day that the offer will be active. Offer ends at 11:59:59pm on the end date",
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        label: "End Date",
        error: {
          required: 'End Date is required'
        }
      },
      isDynamicOffer:{
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'isDynamicOffer'],
        featureFlag: 'enableDynamicOffers',
        featureFlagCheck: true,
        value: false,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Dynamic",
        showLabelAsValue:true,
        tristateCheck:true

      },
      daysToRedeem:{
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'daysToRedeem'],
        value: null,
        onlyNumber:true,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Days To Redeem",
        error: {
          required: 'Days To Redeem is required and should be greater than 0',
          customError:'Days To Redeem should be within range of {{0}} and {{1}}'
        }
      },
      noOfTransactions:{
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'behavioralCondition', 'noOfTransactions'],
        value: null,
        onlyNumber:true,
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required, Validators.min(1)],
        label: "No Of Transactions",
        error: {
          required: 'No Of Transactions is required and should be greater than 0',
          customError:'No Of Transactions should be within range of {{0}} and {{1}}'
        },
        featureFlag: "enableBehavioralContinuity",
        featureFlagCheck: true,
      },
      minimumSpend:{
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['info', 'behavioralCondition', 'minimumSpend'],
        value: null,
        allowDecimals: true,
        onlyNumber: true,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "Minimum Spend",
        error: {
          required: 'No Of Transactions is required and should be greater than 0',
          customError:'No Of Transactions should be within range of {{0}} and {{1}}'
        },
        featureFlag: "enableBehavioralContinuity",
        featureFlagCheck: true,
      },
      minimumSpendUom:{
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        appDataOptions: "uomConfig",
        control: ['info', 'behavioralCondition', 'minimumSpendUom'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        validators: [],
        label: "UOM",
        error: {
          required: 'No Of Transactions is required and should be greater than 0',
          customError:'No Of Transactions should be within range of {{0}} and {{1}}'
        },
        featureFlag: "enableBehavioralContinuity",
        featureFlagCheck: true,
      },
      usageLimitTypePerUser: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'usageLimitTypePerUser'],
        value: 'ONCE_PER_OFFER',
        edit: {
          create: true,
          update: true
        },
        validators: [Validators.required],
        appDataOptions: "offerLimitsGR",
        label: "Offer Limit",
        error: {
          required: 'Offer Limit is required'
        }
      },
      usageLimitPerUser: {
        allowDecimals: false,
        onlyNumber: true,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'usageLimitPerUser'],
        validators: [Validators.required],
        value: null,
        edit: {
          create: true,
          update: true
        },
        label: "Custom Limit",
        error: {
          required: 'Limit is required'
        }
      },
      customType: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customType'],
        value: null,
        edit: {
            create: true,
            update: true
        },
        appDataOptions: "offerUsageLimitCustomTypes",
        label: "Custom Type",   
    },
    customUsage: {
      validate: ['save', 'submit'],
      impactFieldForOfferUpdate: true,
      control: ['rules', 'customUsage'],
      value: null,
      validators: [],
      edit: {
          create: true,
          update: true
      },
      featureFlag: "enableCustomUsage",
      featureFlagCheck: true,
      appDataOptions: "offerUsageLimitCustomUsage",
      label: "Custom Usage",  
      error: {
        required: 'Custom Usage is required'
      }   
  },
    customPeriod: {
        allowDecimals: false,
        onlyNumber: true,
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customPeriod'],
        validators: [],
        value: null,
        edit: {
            create: true,
            update: true
        },
        label: "Custom Period",
        error: {
        required: 'Period is required',
        customError: 'Period and Limit cannot both equal 1'
        }
    },
      desc: {
        impactFieldForOfferUpdate: false,
        control: ['info', 'desc'],
        value: null,
        maxLength: 1000,
        edit: {
          create: true,
          update: true
        },
        label: "Additional Details"
      }
    },
    podDetails: {
      scene7ImageId: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'scene7ImageId'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Scene 7 Image ID",
        appDataOptions: "",
        error: {
          customError: "Image ID is not found in Scene 7",
          required: 'Scene 7 Image ID is required'
        }
      },
      priceText: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'priceText'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        maxLength: 25,
        warning: {
          maxLength: 16,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        label: "Price Text",
        appDataOptions: "",
        error: {
          required: 'Price Text is required'
        }
      },
      headline1: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'headline1'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        maxLength: 100,
        warning: {
          maxLength: 90,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        label: "Headline 1",
        appDataOptions: "",
        error: {
          required: 'Headline 1 is required'
        }
      },
      headline2: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'headline2'],
        value: null,
        maxLength: 100,
        edit: {
          create: true,
          update: true
        },
        warning: {
          maxLength: 90,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        label: "Headline 2",
        appDataOptions: "",
      },
      offerDescription: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'offerDescription'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        maxLength: 100,
        warning: {
          maxLength: 90,
          message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
        },
        required: true,
        validators: [Validators.required],
        label: "Offer Description",
        appDataOptions: "",
        error: {
          required: 'Offer Description is required'
        }
      },
      offerDetailsCode: {
        validate: ['submit', 'process'],
        byPassValidateBeforeProcess: true,
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'offerDetailsCode'],
        value: 'Base',
        appDataOptions: CONSTANTS.OFFER_DETAILS_CODE_LIST_API,
        isMultipleSelection: false,
        validators: [Validators.required],
        edit: {
          create: false,
          update: false
        },
        required: true,
        label: "Offer Details Code",
        error: {
          required: 'Offer Details Code is required'
        }
      },
      shoppingListCategory: {
        validate: ['submit','process'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'shoppingListCategory'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Shopping List Category",
        appDataOptions: "customerFriendlyProductCategories",
        error: {
          required: 'Shopping List Category is required'
        }
      },
      land: {
        validate: [],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'land'],
        value: null,
        validators: [],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Land",
        appDataOptions: null
      },
      space: {
        validate: [],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'space'],
        value: null,
        validators: [],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Space",
        appDataOptions: null
      },
      slot: {
        validate: [],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'slot'],
        value: null,
        validators: [],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Slot",
        appDataOptions: null
      },
      leftNavCategory: {
        validate: ['process'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'leftNavCategory'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Left Nav Category",
        appDataOptions: "customerFriendlyProductCategories",
        isMultipleSelection: true,
        error: {
          required: 'Left Nav Category is required'
        }
      },
      eventIds: {
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'eventIds'],
        value: null,
        edit: {
          create: true,
          update: true
        },
        appDataOptions: "eventSrc",
        isMultipleSelection: true,
        required: true,
        label: "Events",
        error: {
          required: 'Event is required'
        }
      },
      podUsageLimit: {
        validate: ["save", "submit"],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'podUsageLimit'],
        value: null,
        validators: [],
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Usage",
        appDataOptions: "podUsageLimitsGR",
        error: {
          required: 'podUsageLimit is required',
          customError: 'Pod Usage limit must be Unlimited for Continuity Offer Type'
        }
        },
      multiClipLimit: {
        validate: ['save', 'submit'],
        allowDecimals: false,
        onlyNumber: true,
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'multiClipLimit'],
        validators: [],
        value: null,
        edit: {
          create: true,
          update: true
        },
        required: true,
        label: "Multi Clip Limit",
        error: {
          required: 'Multi Clip Limit is required',
          customError: 'Multi Clip Limit must be greater than 0'
        }
      },
      displayStartDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        control: ['podDetails', 'displayStartDate'],
        value: null,
        validators: [Validators.required],
        edit: {
          create: false,
          update: false
        },
        required: true,
        label: "Display Start Date",
        appDataOptions: "",
        error: {
          required: 'Display Start Date is required'
        }
      },
      displayEndDate: {
        validate: ['submit'],
        impactFieldForOfferUpdate: true,
        validators: [Validators.required],
        control: ['podDetails', 'displayEndDate'],
        value: null,
        edit: {
          create: true,
          update: true
        },

        required: true,
        label: "Display End Date",
        appDataOptions: "",
        error: {
          required: 'Display End Date is required',
          customError: 'Display EndDate should be within range of offer StartDate and EndDate'
        }
      }
    }
  },
  BPD:BPD_OR_CREATE_RULES

}

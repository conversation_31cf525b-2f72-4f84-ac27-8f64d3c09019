import { Validators } from "@angular/forms";
import { STORE_GROUP_CONSTANTS } from "../../../groups/constants/store_group_constants";

/**BPD offer request create rules */
export const BPD_OR_CREATE_RULES =
{
  offerRequestManagement: ["BpdOfferRequestManagementComponent"],
  programCode: "Base PD",
  components: [
    "BpdRequestSectionComponent",
    "GeneralComponent",
    "JustificationComponent",
    "BpdAdditonalDescriptionComponent",
    "OfferDetailsComponent",
  ],
  summaryComponents: [
    "BpdRequestSectionComponent",
    "GeneralComponent",
    "JustificationComponent",
    "BpdAdditonalDescriptionComponent",
    "OfferDetailsComponent",
  ],
  podComponent: ["BPDPodComponent"],
  offerRequest: {
    regionId:{
      impactFieldForOfferUpdate:true,
      control:['info','regionId'],
      value:null,
      edit:{
        create:true,
        update:true
      },
      onlyDisplay: true,
      label:"Region",
      appDataOptions:STORE_GROUP_CONSTANTS.SEARCH_STORE_GROUP_NEW_API,
      optionKey: "name",
      optionValue: 'code',
      error:{
        required:'Region is required'
      }
     },
    programType: {
      validate: ["process"],
      byPassValidateBeforeProcess: true,
      impactFieldForOfferUpdate: true,
      control: ["info", "programType"],
      appDataOptions: "programTypeBPD", 
      value: null,
      edit: {
        create: true,
        update: true,
      },
      validators: [Validators.required],
      label: "Program Type",
      error: {
        required: "Program Type is required",
      },
    },
    allocationCode: {
      validate: ["submit", "process"],
      byPassValidateBeforeProcess: true,
      impactFieldForOfferUpdate: true,
      control: ["info", "allocationCode"],
      appDataOptions: "allocationCodeApi",
      value: "01",
      featureFlag: "enableAllocationOffers",
      featureFlagCheck: true,
      optionKey: "name",
      optionValue: "code",
      edit: {
        create: true,
        update: true,
      },
      validators: [Validators.required],
      label: "Allocation Code",
      error: {
        required: "Allocation Code is required",
      },
    },
    allocationCodeName: {
      validate: [],
      byPassValidateBeforeProcess: true,
      impactFieldForOfferUpdate: true,
      control: ["info", "allocationCodeName"],
      appDataOptions: "",
      featureFlag: "enableAllocationOffers",
      featureFlagCheck: true,
      value: "Default",
      optionKey: "name",
      optionValue: "code",
      edit: {
        create: true,
        update: true,
      },
    },

    brandAndSize: {
      control: ["info", "brandAndSize"],
      label: "Brand and Size",
    },
    customerSegment: {
      control: ["rules", "customerSegment"],
      appDataOptions: "offerCustomerSegments",

      label: "Segment",
    },
    period_week: {
      control: ["info", "period_week"],

      label: "Period",
    },

    offerEffectiveStartDate: {
      control: ["rules", "startDate", "offerEffectiveStartDate"],
      label: "Start Date",
    },
    offerEffectiveEndDate: {
      control: ["rules", "endDate", "offerEffectiveEndDate"],

      label: "End Date",
    },
    //TO DO: BPD OR
    priceUntil: {
      control: ["rules", "startDate", "offerEffectiveStartDate"],
      label: "Price Until",
    },
    desc: {
      impactFieldForOfferUpdate: false,
      control: ["info", "desc"],
      value: null,
      maxLength: 1000,
      edit: {
        create: true,
        update: true,
      },
      label: "Additional Details",
    },
    defaultPromoWeekIds: {
      control: ["info", "defaultPromoWeekIds"],
      label: "Default Weeks",
    },
    additonalInformation: {
      desc: {
        impactFieldForOfferUpdate: false,
      },
    },
    usageLimitTypePerUser: {
      validate: ["save", "submit"],
      impactFieldForOfferUpdate: true,
      control: ["rules", "usageLimitTypePerUser"],
      value: "ONCE_PER_OFFER",
      edit: {
        create: true,
        update: true,
      },
      validators: [Validators.required],
      appDataOptions: "offerLimits",
      label: "Offer Limit",
      error: {
        required: "Offer Limit is required",
      },
      },
      customType: {
          validate: ["save", "submit"],
          impactFieldForOfferUpdate: true,
          control: ["rules", "customType"],
          value: null,
          edit: {
              create: true,
              update: true
          },
          appDataOptions: "offerUsageLimitCustomTypes",
          label: "Custom Type",
      },
      customPeriod: {
          allowDecimals: false,
          onlyNumber: true,
          validate: ["save", "submit"],
          impactFieldForOfferUpdate: true,
          control: ["rules", "customPeriod"],
          validators: [],
          value: null,
          edit: {
              create: true,
              update: true
          },
          label: "Custom Period",
          error: {
              required: "Period is required",
              customError: 'Period and Limit cannot both equal 1',
          },
      },

      customUsage: {
        validate: ['save', 'submit'],
        impactFieldForOfferUpdate: true,
        control: ['rules', 'customUsage'],
        value: null,
        validators: [],
        edit: {
            create: true,
            update: true
        },
        featureFlag: "enableCustomUsage",
        featureFlagCheck: true,
        appDataOptions: "offerUsageLimitCustomUsage",
        label: "Custom Usage",  
        error: {
          required: 'Custom Usage is required'
        }   
    },
    bggm: {
      control: ["info", "bggm"],
      label: "BGGM",
    },
    bugm: {
      control: ["info", "bugm"],
      label: "BUGM",
    },
    cat_id: {
      control: ["info", "categoryId"],
      label: "Cat ID",
    },
    category: {
      control: ["info", "category"],
      label: "Category",
    },
    cic: {
      control: ["info", "cic"],
      label: "CIC",
    },
    cpg: {
      control: ["info", "cpg"],
      label: "CPG",
    },
    rep_upc: {
      control: ["info", "repUpc"],
      label: "Rep UPC",
    },
    usageLimitPerUser:{
      allowDecimals:false,
      onlyNumber:true,
      validate:['save','submit'],
      impactFieldForOfferUpdate:true,
      control:['rules','usageLimitPerUser'],
      validators: [],
      value:null,
      edit:{
        create:true,
        update:true
      },
      label:"Custom Limit",
      error:{
        required:'Limit is required'
      }
     },
    },
  podDetails: {
    scene7ImageId: {
      validate: ["process"],
      byPassValidateBeforeProcess: true,
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "scene7ImageId"],
      value: null,
      validators: [Validators.required],
      edit: {
        create: true,
        update: true,
      },
      required: true,
      label: "Scene 7 Image ID",
      appDataOptions: "",
      error: {
        customError: "Image ID is not found in Scene 7",
        required: "Scene 7 Image ID is required",
      },
    },
    priceText: {
      validate: ["submit"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "priceText"],
      value: null,
      validators: [Validators.required],
      edit: {
        create: true,
        update: true,
      },
      maxLength: 25,
      warning: {
        maxLength: 16,
        message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) ",
      },
      required: true,
      label: "Price Text",
      appDataOptions: "",
      error: {
        required: "Price Text is required",
      },
    },
    headline1: {
      validate: ["submit"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "headline1"],
      value: null,
      validators: [Validators.required],
      edit: {
        create: true,
        update: true,
      },
      maxLength: 100,
      warning: {
        maxLength: 90,
        message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) ",
      },
      required: true,
      label: "Headline 1",
      appDataOptions: "",
      error: {
        required: "Headline 1 is required",
      },
    },
    headline2: {
      validate: ["submit"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "headline2"],
      value: null,
      maxLength: 100,
      edit: {
        create: true,
        update: true,
      },
      warning: {
        maxLength: 90,
        message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) ",
      },
      required: true,
      label: "Headline 2",
      appDataOptions: "",
    },
    offerDescription: {
      validate: ["submit"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "offerDescription"],
      value: null,
      edit: {
        create: true,
        update: true,
      },
      maxLength: 100,
      warning: {
        maxLength: 90,
        message: "Exceeds recommended characters. May not display correctly. ({{1}} characters left) ",
      },
      required: true,
      validators: [Validators.required],
      label: "Offer Description",
      appDataOptions: "",
      error: {
        required: "Offer Description is required",
      },
    },

    upcQtyOrUOM: {
      control: ["podDetails", "upcQtyOrUOM"],
      label: "UPC Qty / UOM",
    },

    offerDetailsCode: {
      control: ["podDetails", "offerDetailsCode"],
      label: "Offer Details Code",
    },

   
    shoppingListCategory: {
      validate: ["process"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "shoppingListCategory"],
      value: null,
      validators: [Validators.required],
      edit: {
        create: true,
        update: true,
      },
      required: true,
      label: "Shopping List Category",
      appDataOptions: "customerFriendlyProductCategories",
      error: {
        required: "Shopping List Category is required",
      },
    },
    leftNavCategory: {
      validate: ["process"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "leftNavCategory"],
      value: null,
      edit: {
        create: true,
        update: true,
      },
      required: true,
      label: "Left Nav Category",
      appDataOptions: "customerFriendlyProductCategories",
      isMultipleSelection: true,
      error: {
        required: "Left Nav Category is required",
      },
    },
    eventIds: {
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "eventIds"],
      value: null,
      edit: {
        create: true,
        update: true,
      },
      appDataOptions: "eventSrc",
      isMultipleSelection: true,
      required: true,
      label: "Events",
      error: {
        required: "Event is required",
      },
    },
    podUsageLimit: {
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "podUsageLimit"],
      value: null,
      edit: {
        create: true,
        update: true,
      },
      required: true,
      label: "Usage",
      appDataOptions: "podUsageLimits",
      error: {
        required: "podUsageLimit is required",
      },
    },
    displayStartDate: {
      validate: ["submit"],
      impactFieldForOfferUpdate: true,
      control: ["podDetails", "displayStartDate"],
      value: null,
      validators: [Validators.required],
      edit: {
        create: false,
        update: false,
      },
      required: true,
      label: "Display Start Date",
      appDataOptions: "",
      error: {
        required: "Display Start Date is required",
      },
    },
    displayEndDate: {
      validate: ["submit"],
      impactFieldForOfferUpdate: true,
      validators: [Validators.required],
      control: ["podDetails", "displayEndDate"],
      value: null,
      edit: {
        create: true,
        update: true,
      },

      required: true,
      label: "Display End Date",
      appDataOptions: "",
      error: {
        required: "Display End Date is required",
        customError: "Display EndDate should be within range of offer StartDate and EndDate",
      },
    },
  },
};

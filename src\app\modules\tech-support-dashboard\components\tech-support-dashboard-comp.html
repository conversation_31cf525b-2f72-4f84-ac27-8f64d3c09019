<form [formGroup]="techSupportForm" class="font-size-body offerDetailsWrap">
    <div class="row justify-content-between">
        <div class="col-12">
            <api-errors></api-errors>
        </div>
    </div>
    <div class="row justify-content-between">
        <div class="col d-flex justify-content-start">
            <div class="page-title">
                <h3 class="page-title">Tech Support Dashboard</h3>
            </div>
        </div>
    </div>
    <div class="row justify-content-between">
        <div class="col d-flex justify-content-start">
            <div class="page-title w-100">
                <div class="row">
                    <div class="col-12 row m-0 mt-6">
                        <div class="col-2 pl-0">
                            <label class="d-block font-weight-bold" for="program code">Entity Type </label>
                            <select class="custom-select form-control" id="programCode" formControlName="entityType"
                                name="programCode" (change)="onEntityTypeChange($event)">
                                <ng-container *ngFor="let key of entityTypeKeys">
                                    <option [value]="key">{{entityTypes[key].value}}</option>
                                </ng-container>
                            </select>
                        </div>
                        <div class="col-2 pl-0">
                            <label class="d-block font-weight-bold" for="event">Event </label>
                            <select class="custom-select form-control" id="event" formControlName="event" name="event">
                                <ng-container *ngFor="let eventObj of eventTypes">
                                    <option [value]="eventObj.eventKey">{{eventObj.eventName}}</option>
                                </ng-container>
                            </select>
                        </div>
                        <div class="col-1 pl-0">
                            <label class="d-block font-weight-bold" for="event">Date </label>
                            <select class="custom-select form-control col custom-width" (change)="onDateTypeSelect()"
                                formControlName="dateField">
                                <option *ngFor="let item of dateFields" [value]="item">{{ item }}</option>
                            </select>
                        </div>
                        <div class="col-3 pl-0" *ngIf="showRange">
                            <label class="d-block font-weight-bold" for="event">Date Range</label>
                            <div class="d-flex">
                                <div class="input-group date-wrapper">
                                    <input onkeydown="return false" type="text"
                                        class="form-control dt-field form-control-lg optional border-0" id="startDate"
                                        name="startDate" autocomplete="off" #startDatePicker="bsDatepicker"
                                        formControlName="rangeStartDate" (bsValueChange)="setMinEndDate($event)"
                                        [bsConfig]="{
                        containerClass: colorTheme,
                        dateInputFormat: 'MM/DD/YYYY',
                        showWeekNumbers: false
                      }" bsDatepicker placeholder="From" markAsTouchedOnFocus formControlName="rangeStartDate" />
                                    <div class="input-group-append mr-0">
                                        <div class="input-group-text input-text-icon p-2 border-0"
                                            (click)="startDatePicker.toggle()">
                                            <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
                                        </div>
                                    </div>
                                </div>
                                <span class="range-sepreator"> - </span>
                                <div class="input-group date-wrapper">
                                    <input onkeydown="return false" type="text"
                                        class="form-control form-control-lg dt-field optional border-0" id="endDate"
                                        name="endDate" autocomplete="off" formControlName="rangeEndDate"
                                        [minDate]="minEndDate" #endDatePicker="bsDatepicker" [bsConfig]="{
                      containerClass: colorTheme,
                      dateInputFormat: 'MM/DD/YYYY',
                      showWeekNumbers: false
                    }" bsDatepicker placeholder="To" markAsTouchedOnFocus formControlName="rangeEndDate" />
                                    <div class="input-group-append mr-0">
                                        <div class="input-group-text input-text-icon p-2 border-0"
                                            (click)="endDatePicker.toggle()">
                                            <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2 pl-0">
                            <label class="d-block font-weight-bold cancel-link"></label>
                            <button class="btn btn-primary" [disabled]="!techSupportForm.valid"
                                (click)="onSearhEvents()">Search</button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 row m-0 mt-6">
                        <div class="col-1 pl-0">
                            <label class="d-block font-weight-bold" for="event">Sort Ts By </label>
                            <select class="custom-select form-control col custom-width"
                                formControlName="sortBy">
                                <option  value="ASC">ASC</option>
                                <option value="DESC">DESC</option>
                            </select>
                        </div>
                        <div class="col-1 pl-0">
                            <label class="d-block font-weight-bold" for="event">Pushed Events</label>
                            <select class="custom-select form-control col custom-width"
                                formControlName="pushedEventType">
                                <option  value="success">Success</option>
                                <option value="failure">Failure</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row ">
        <div class="col-9 col-sm">
            <div class="d-flex">
                <pagination [headerPage]="'tech-support'" class="col-12 text-right p-0"></pagination>
            </div>
            <div class="font-size-body batchImportLog">
                <ngx-datatable style="width: 100%" class="material" [rows]="pushedEventResults" [columnMode]="'flex'"
                    [headerHeight]="60" [rowHeight]="'auto'" [messages]="{emptyMessage: 'No Events to display'}" #table>
                    <ngx-datatable-column [flexGrow]="1" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template> Entity ID </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <a class="text-wrapper" href="{{getBasePathForIds(row)}}" target="_blank" rel="noopener">{{ row.entityId
                                }}</a>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column [flexGrow]="1" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template> Event ID </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span class="text-wrapper">{{ row.eventId || row.id }}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column [flexGrow]="1" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template> Sent Ts </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span class="text-wrapper">{{ getSentTs(row.sentTs || row.actionTimestamp) }}</span>
                        </ng-template>
                    </ngx-datatable-column>
                </ngx-datatable>
            </div>
        </div>
    </div>
</form>
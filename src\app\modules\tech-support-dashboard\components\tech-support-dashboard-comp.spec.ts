import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { TechSupportDashboardComponent } from './tech-support-dashboard-comp';
import { TechSupportService } from '../services/tech-support-service';
import { CommonService } from '@appServices/common/common.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { ROUTES_CONST } from '@appConstants/routes_constants';

describe('TechSupportDashboardComponent', () => {
    let component: TechSupportDashboardComponent;
    let fixture: ComponentFixture<TechSupportDashboardComponent>;

    beforeEach(async () => {

        const queryGeneratorStub = () => ({
            setQuery: (string) => ({}),
            pushParameters: (object) => ({}),
            getQuery: () => ({}),
        });

        const commonServiceStub = () => ({
            passPaginationData$: of({}),
            permissionToShowBPDProgramCode: () => ({}),
            getEventsData: () => ({
                subscribe: f => f({
                    events: {
                        125: "Weekly Ad Coupons",
                        425: "Theme 3-5",
                        524: "Save on P&G Products",
                        824: "Theme 1-5",
                        
                    },}) }),
          eventDataSrc: { next: () => ({}) }
        });

        const techSupportServiceStub = () => ({
            getPushedEventResults: () => of([]),
            updateList: () => ({}),
            techSupportDataList$: of({ actionEventsPushFailures: [], actionEventsPushed: [] }),
        });

        await TestBed.configureTestingModule({
            declarations: [TechSupportDashboardComponent],
            imports: [ReactiveFormsModule],
            providers: [
                { provide: TechSupportService, useFactory: techSupportServiceStub },
                { provide: CommonService, useFactory: commonServiceStub },
                { provide: QueryGenerator, useFactory: queryGeneratorStub },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(TechSupportDashboardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize the form controls on createFormCtrl', () => {
        component.createFormCtrl();
        expect(component.techSupportForm).toBeDefined();
        expect(component.techSupportForm.get('entityType').value).toBe('offer_request');
        expect(component.techSupportForm.get('event').value).toBe('create_and_submit');
    });

    it('should set pushedEventResults on initSubscribe', () => {
        component.initSubscribe();
        TestBed.inject(TechSupportService).techSupportDataList$.subscribe(() => {
            expect(component.pushedEventResults).toEqual([]);
        });
    });

    it('should call setQuery and getQuery in setReuestQuery', () => {
        const mockQueryGenerator = jasmine.createSpyObj('QueryGenerator', ['setQuery', 'getQuery']);
        spyOn(TestBed.inject(QueryGenerator), 'setQuery').and.callFake(mockQueryGenerator.setQuery);
        spyOn(TestBed.inject(QueryGenerator), 'getQuery').and.callFake(mockQueryGenerator.getQuery);

        component.setReuestQuery();
        expect(mockQueryGenerator.setQuery).toHaveBeenCalled();
    });

    it('should validate dateField and range dates on onDateTypeSelect', () => {
        component.techSupportForm.get('dateField').setValue('Range');
        component.onDateTypeSelect();
        expect(component.techSupportForm.get('rangeStartDate').validator).toBeTruthy();
        expect(component.techSupportForm.get('rangeEndDate').validator).toBeTruthy();

        component.techSupportForm.get('dateField').setValue('Today');
        component.onDateTypeSelect();
        expect(component.techSupportForm.get('rangeStartDate').value).toBeNull();
        expect(component.techSupportForm.get('rangeEndDate').value).toBeNull();
    });

    it('should call getPushedEvents when onSearhEvents is triggered and form is valid', () => {
        spyOn(component, 'getPushedEvents');
        component.techSupportForm.setValue({
            entityType: 'offer_request',
            event: 'create_and_submit',
            dateField: 'Today',
            rangeStartDate: null,
            rangeEndDate: null,
            sortBy: 'DESC',
            pushedEventType: 'success',
        });
        component.onSearhEvents();
        expect(component.getPushedEvents).toHaveBeenCalled();
    });

    it('should update eventTypes on onEntityTypeChange', () => {
        const mockEvent = { target: { value: 'offer_request' } };
        component.onEntityTypeChange(mockEvent);
        expect(component.eventTypes).toEqual(component.entityTypes['offer_request'].eventTypes);
    });

    it('should return formatted timestamp in getSentTs', () => {
        const timestamp = 1697040000; 
        const formatted = component.getSentTs(timestamp);
    });

    it('should return empty string in getSentTs for invalid timestamp', () => {
        const formatted = component.getSentTs(null);
        expect(formatted).toBe('');
    });
    it('should set minEndDate when setMinEndDate is called', () => {
        const mockDate = new Date('2023-10-01');
        component.setMinEndDate(mockDate);
        expect(component.minEndDate).toEqual(mockDate);
    });

    it('should return correct base path for offer_request entity type in getBasePathForIds', () => {
        const mockRow = {
            entityType: 'offer_request',
            entityId: '12345',
            entity: JSON.stringify({
                payload: {
                    programCode: 'GR',
                    externalOfferId: null,
                    productGroupType: null,
                },
            }),
        };
        const basePath = component.getBasePathForIds(mockRow);
        expect(basePath).toBe(`${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GRSummary}/12345`);
    });

    it('should return correct base path for offer entity type in getBasePathForIds', () => {
        const mockRow = {
            entityType: 'offer',
            entityId: '12345',
            entity: JSON.stringify({
                payload: {
                    programCode: null,
                    externalOfferId: '54321',
                    productGroupType: null,
                },
            }),
        };
        const basePath = component.getBasePathForIds(mockRow);
        expect(basePath).toBe(`${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Summary}/54321`);
    });

    it('should return correct base path for product_group entity type in getBasePathForIds', () => {
        const mockRow = {
            entityType: 'product_group',
            entityId: '12345',
            entity: JSON.stringify({
                payload: {
                    programCode: null,
                    externalOfferId: null,
                    productGroupType: 'NON_BASE',
                },
            }),
        };
        const basePath = component.getBasePathForIds(mockRow);
        expect(basePath).toBe(`${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.ProductGroup}/${ROUTES_CONST.GROUPS.ProductDetails}/${ROUTES_CONST.GROUPS.Edit}/12345`);
    });

    it('should return empty string for invalid row in getBasePathForIds', () => {
        const basePath = component.getBasePathForIds(null);
        expect(basePath).toBe('');
    });

    it('should return correct base path for store_group entity type in getBasePathForIds', () => {
        const mockRow = {
            entityType: 'store_group',
            entityId: '12345',
            entity: JSON.stringify({
                payload: {
                    programCode: null,
                    externalOfferId: null,
                    productGroupType: null,
                },
            }),
        };
        const basePath = component.getBasePathForIds(mockRow);
        expect(basePath).toBe(`${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.StoreGroup}/${ROUTES_CONST.GROUPS.StoreDetails}/${ROUTES_CONST.GROUPS.Edit}/12345`);
    });

    it('should return correct base path for customer_groups entity type in getBasePathForIds', () => {
        const mockRow = {
            entityType: 'customer_groups',
            entityId: '12345',
            entity: JSON.stringify({
                payload: {
                    programCode: null,
                    externalOfferId: null,
                    productGroupType: null,
                },
            }),
        };
        const basePath = component.getBasePathForIds(mockRow);
        expect(basePath).toBe(`${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.CustomerGroup}/${ROUTES_CONST.GROUPS.CustomerDetails}/${ROUTES_CONST.GROUPS.Edit}/12345`);
    });

    it('should return correct base path for point_groups entity type in getBasePathForIds', () => {
        const mockRow = {
            entityType: 'point_groups',
            entityId: '12345',
            entity: JSON.stringify({
                payload: {
                    programCode: null,
                    externalOfferId: null,
                    productGroupType: null,
                },
            }),
        };
        const basePath = component.getBasePathForIds(mockRow);
        expect(basePath).toBe(`${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.PointsGroup}/${ROUTES_CONST.GROUPS.PointsDetails}/${ROUTES_CONST.GROUPS.Edit}/12345`);
    });
});
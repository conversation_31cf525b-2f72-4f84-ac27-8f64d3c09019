import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { CommonService } from '@appServices/common/common.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import * as moment from 'moment';
import { TECH_SUPPORT_CONSTANTS } from '../constants/tech-support-constants';
import { TechSupportService } from '../services/tech-support-service';

@Component({
  selector: 'tech-support-dashboard',
  styleUrls: ['./tech-support-dashboard-comp.scss'],
  templateUrl: './tech-support-dashboard-comp.html'
})
export class TechSupportDashboardComponent implements OnInit {
  tech_support_constants = TECH_SUPPORT_CONSTANTS;
  entityTypes = this.tech_support_constants.entityType;
  entityTypeKeys = Object.keys(this.tech_support_constants?.entityType);
  techSupportForm: UntypedFormGroup;
  dateFields = ['Today', "Range"];
  rangeEndDate: Date = new Date();
  colorTheme = 'theme-dark-blue';
  eventTypes = [];
  pushedEventResults = [];
  config = JSON.parse(JSON.stringify({
    itemsPerPage: CONSTANTS.PAGE_LIMIT,
    currentPage: 1,
  }));
  paginateConfig = this.config;
  basePath: string;
  columnName: string;
  minEndDate = null;

  constructor(private techSupportService: TechSupportService,
    private commonService: CommonService,
    private queryGen: QueryGenerator) { 
      // intentionally left empty
    }

  ngOnInit(): void {
    this.initSubscribe();
    this.queryGen.setQuery("");
    this.createFormCtrl();
    this.setDataSource();
    this.getPushedEvents();
  }
  initSubscribe() {
    this.techSupportService.techSupportDataList$.subscribe((data: any) => {
      if (data) {
        const { pushedEventType } = this.techSupportForm?.value;
        this.pushedEventResults = pushedEventType === "failure" ?  [...data?.actionEventsPushFailures] :  [...data?.actionEventsPushed];
      }
    })
  }
  createFormCtrl() {
    this.techSupportForm = new UntypedFormGroup({
      entityType: new UntypedFormControl('offer_request'),
      event: new UntypedFormControl('create_and_submit'),
      dateField: new UntypedFormControl('Today'),
      rangeStartDate: new UntypedFormControl(null),
      rangeEndDate: new UntypedFormControl(null),
      sortBy: new UntypedFormControl("DESC"),
      pushedEventType: new UntypedFormControl("success")
    });
  }
  getPushedEvents() {
    this.setReuestQuery();
    const query = this.queryGen.getQuery();
    const { pushedEventType } = this.techSupportForm?.value;
    this.techSupportService.getPushedEventResults(query, pushedEventType).subscribe((data: any) => {
      if (data) {
        const { totalCount, current, sid } = data;
        this.pushedEventResults = pushedEventType === "failure" ?  [...data?.actionEventsPushFailures] :  [...data?.actionEventsPushed];
        this.techSupportService.updateList(data);
        this.commonService.passPaginationData$.next({ totalCount, pageNumber: current, sid });
      }
    })
  }
  setMinEndDate(event) {
    this.minEndDate = event;
  }
  getSentTs(tsValue) {
    if (tsValue) {
      return moment(tsValue * 1000)?.format("MM/DD/YY hh:mm a");
    }
    return '';
  }
  setReuestQuery() {
    const { entityType, event, sortBy, pushedEventType } = this.techSupportForm?.value,
      sentTs = this.getDateValuesForPayload(), isSuccess = pushedEventType === "success",
      eventKey = isSuccess ? "event" : "action", sentTsKey = isSuccess ? 'sentTs' : "actionTimestamp";
    const query = `entityType=${entityType};${eventKey}=${event};${sentTsKey}=[${sentTs}];sortBy=${sentTsKey}${sortBy};`
    this.queryGen.setQuery(query);
  }
  onDateTypeSelect() {
    const dateFieldValue = this.techSupportForm?.get("dateField")?.value,
      rangeStartDtCtrl = this.techSupportForm?.get("rangeStartDate"),
      rangeEndDtCtrl = this.techSupportForm?.get("rangeEndDate");
    if (dateFieldValue === "Range") {
      rangeEndDtCtrl.setValidators([Validators.required]);
      rangeStartDtCtrl.setValidators([Validators.required]);
    } else {
      rangeEndDtCtrl.setValue(null); rangeStartDtCtrl.setValue(null);
      rangeEndDtCtrl.clearValidators(); rangeStartDtCtrl.clearValidators();
    }
    rangeEndDtCtrl.updateValueAndValidity();
    rangeStartDtCtrl.updateValueAndValidity();
  }
  getDateValuesForPayload() {
    const dateFieldValue = this.techSupportForm?.get("dateField")?.value,
      rangeStartDtValue = this.techSupportForm?.get("rangeStartDate").value,
      rangeEndDtValue = this.techSupportForm?.get("rangeEndDate").value, start = moment(), end = moment();
    let fromDate;
    let toDate;
    fromDate = `${moment(dateFieldValue === "Today" ? start : rangeStartDtValue)
      .startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`;
    toDate = `${moment(dateFieldValue === "Today" ? end : rangeEndDtValue)?.endOf('day')?.utc()?.
      format('YYYY-MM-DDTHH:mm:ss')}Z`;
    return `${fromDate} TO ${toDate}`;
  }
  setDataSource() {
    this.setEvents();
  }
  setEvents() {
    const entityType = this.techSupportForm.get("entityType").value;
    const eventTypes = this.entityTypes[entityType]?.eventTypes;
    this.eventTypes = [...eventTypes]
  }
  onSearhEvents() {
    if (this.techSupportForm.valid) {
      this.getPushedEvents();
    }
  }
  onEntityTypeChange(e) {
    const { target: { value } } = e;
    if (value) {
      this.eventTypes = [...this.entityTypes[value]?.eventTypes];
      this.techSupportForm?.get("event").setValue(this.eventTypes[0]?.eventKey)
    }
  }
  get showRange() {
    return this.techSupportForm.get("dateField")?.value === "Range";
  }
  getBasePathForIds(row) {
    if (!row) {
      return '';
    }
    let pCode = '', offerId = '', pgType = "";
    const { entityType, entity } = row,
      parsedEntity = entity ? JSON.parse(entity) : null;
    if (parsedEntity && toString.call(parsedEntity) === '[object Object]') {
      let { payload: { programCode, externalOfferId, productGroupType } } = parsedEntity;
      pCode = programCode;
      offerId = externalOfferId;
      pgType = productGroupType;
    }
    switch (entityType) {
      case "offer_request": {
        const summaryPgKey = pCode && ["GR", "SPD", "BPD"].includes(pCode) ? `${pCode}Summary` : `Summary`;
        return `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST[summaryPgKey]}/${row.entityId}`;
      }
      case "offer": {
        const routeId = offerId ? offerId : row.entityId;
        return `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Summary}/${routeId}`;
      }
      case "product_group": {
        const baseRoute = pgType === "NON_BASE" ? ROUTES_CONST.GROUPS.Edit : ROUTES_CONST.GROUPS.BaseEdit;
        return `${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.ProductGroup}/${ROUTES_CONST.GROUPS.ProductDetails}/${baseRoute}/${row.entityId}`;

      }
      case "store_group": {
        return `${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.StoreGroup}/${ROUTES_CONST.GROUPS.StoreDetails}/${ROUTES_CONST.GROUPS.Edit}/${row.entityId}`;
      }
      case "customer_groups": {
        return `${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.CustomerGroup}/${ROUTES_CONST.GROUPS.CustomerDetails}/${ROUTES_CONST.GROUPS.Edit}/${row.entityId}`;
      }
      case "point_groups": {
        return `${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.PointsGroup}/${ROUTES_CONST.GROUPS.PointsDetails}/${ROUTES_CONST.GROUPS.Edit}/${row.entityId}`;
      }
    }
  }
}

export const TECH_SUPPORT_CONSTANTS = {
    entityType: {
        offer_request: {
            value: "Offer Request",
            eventTypes:
                [
                    {
                        eventKey: "assign",
                        eventName: "Assign"
                    },
                    {
                        eventKey: "create_and_submit",
                        eventName: "Create And Submit"
                    },
                    {
                        eventKey: "deploy",
                        eventName: "Deploy"
                    },
                    {
                        eventKey: "draft",
                        eventName: "Draft"
                    },
                    {
                        eventKey: "digital_process",
                        eventName: "Digital Process"
                    },
                    {
                        eventKey: "non_digital_process",
                        eventName: "Non-Digital Process"
                    },
                    {
                        eventKey: "publish",
                        eventName: "Publish"
                    },
                    {
                        eventKey: "preview",
                        eventName: "Preview"
                    },
                    {
                        eventKey: "revert_edit",
                        eventName: "Revert Edit"
                    },
                    {
                        eventKey: "edit",
                        eventName: "Edit"
                    },
                    {
                        eventKey: "save",
                        eventName: "Save"
                    },
                    {
                        eventKey: "submit_with_save",
                        eventName: "Submit With Save"
                    },
                    {
                        eventKey: "update",
                        eventName: "Update"
                    }
                ]
        },
        offer: {
            value: "Offer",
            eventTypes:
                [
                    {
                        eventKey: "create",
                        eventName: "Create"
                    },
                    {
                        eventKey: "deploy",
                        eventName: "Deploy"
                    },
                    {
                        eventKey: "publish",
                        eventName: "Publish"
                    },
                    {
                        eventKey: "save",
                        eventName: "Save"
                    },
                    {
                        eventKey: "digital_process",
                        eventName: "Digital Process"
                    },
                    {
                        eventKey: "non_digital_process",
                        eventName: "Non-Digital Process"
                    },
                    {
                        eventKey: "update",
                        eventName: "Update"
                    }
                ]
        },
        product_group: {
            value: "Product Group",
            eventTypes:
                [
                    {
                        eventKey: "create",
                        eventName: "Create"
                    },
                    {
                        eventKey: "update",
                        eventName: "Update"
                    }
                ]
        },
        store_group: {
            value: "Store Group",
            eventTypes:
                [
                    {
                        eventKey: "create",
                        eventName: "Create"
                    },
                    {
                        eventKey: "update",
                        eventName: "Update"
                    }
                ]
        },
        customer_groups: {
            value: "Customer Group",
            eventTypes:
                [
                    {
                        eventKey: "deploy",
                        eventName: "Deploy"
                    },
                    {
                        eventKey: "update",
                        eventName: "Update"
                    }
                ]
        },
        // point_group: {
        //     value: "Point Group",
        //     eventTypes:
        //         [
        //             {
        //                 eventKey: "create",
        //                 eventName: "Create"
        //             },
        //             {
        //                 eventKey: "update",
        //                 eventName: "Update"
        //             }
                    
        //         ]
        // }
    },
    TECH_SUPPORT_GET_PUSHED_EVENT: "techSupportPushedEvent",
    TECH_SUPPORT_GET_FAILED_PUSHED_EVENT: "techSupportFailedPushdEvent"
}

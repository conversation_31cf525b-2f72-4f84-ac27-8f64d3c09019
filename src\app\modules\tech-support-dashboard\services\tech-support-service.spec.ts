import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TechSupportService } from './tech-support-service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BehaviorSubject } from 'rxjs';
import { TECH_SUPPORT_CONSTANTS } from '../constants/tech-support-constants';

describe('TechSupportService', () => {
    let service: TechSupportService;
    let httpMock: HttpTestingController;
    let mockInitialDataService: jasmine.SpyObj<InitialDataService>;

    beforeEach(() => {
        const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                TechSupportService,
                { provide: InitialDataService, useValue: initialDataServiceSpy }
            ]
        });

        service = TestBed.inject(TechSupportService);
        httpMock = TestBed.inject(HttpTestingController);
        mockInitialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;

        mockInitialDataService.getConfigUrls.and.callFake((key: string) => `mocked-url/${key}`);

        // Ensure pushed_event_api and pushed_event_failed_api are initialized
        service.pushed_event_api = `mocked-url/${TECH_SUPPORT_CONSTANTS.TECH_SUPPORT_GET_PUSHED_EVENT}`;
        service.pushed_event_failed_api = `mocked-url/${TECH_SUPPORT_CONSTANTS.TECH_SUPPORT_GET_FAILED_PUSHED_EVENT}`;
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should initialize pushed_event_api and pushed_event_failed_api correctly', () => {
        expect(service.pushed_event_api).toBe(`mocked-url/${TECH_SUPPORT_CONSTANTS.TECH_SUPPORT_GET_PUSHED_EVENT}`);
        expect(service.pushed_event_failed_api).toBe(`mocked-url/${TECH_SUPPORT_CONSTANTS.TECH_SUPPORT_GET_FAILED_PUSHED_EVENT}`);
    });

    it('should update the techSupportDataList$ BehaviorSubject', () => {
        const testData = { key: 'value' };
        service.updateList(testData);
    });

    it('should call the correct API for getPushedEventResults without failure type', () => {
        const query = 'testQuery';
        const pushedEventType = null;

        service.getPushedEventResults(query, pushedEventType).subscribe();

        const req = httpMock.expectOne(service.pushed_event_api);
        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual({
            query,
            exactSearch: true,
            includeTotalCount: true,
            requiredFieldsToFetch: [
                'event_id',
                'entity_id',
                'entity_type',
                'entity',
                'sent_ts'
            ],
            retrieveRequiredFields: true
        });
    });
});
import { HttpClient } from "@angular/common/http";
import { BehaviorSubject } from "rxjs";

import { Injectable } from "@angular/core";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { TECH_SUPPORT_CONSTANTS } from "../constants/tech-support-constants";
@Injectable({
  providedIn: "root"
})
export class TechSupportService {

  constructor(
    private _http: HttpClient,
    private apiConfigService: InitialDataService
  ) {
    // intentionally left empty
   }
  techSupportDataList$ = new BehaviorSubject(false);

  pushed_event_api: string = this.apiConfigService.getConfigUrls(
    TECH_SUPPORT_CONSTANTS.TECH_SUPPORT_GET_PUSHED_EVENT
  );
  pushed_event_failed_api: string = this.apiConfigService.getConfigUrls(
    TECH_SUPPORT_CONSTANTS.TECH_SUPPORT_GET_FAILED_PUSHED_EVENT
  );
    updateList(data) {
      this.techSupportDataList$.next(data);
    }

  public getPushedEventResults(query, pushedEventType = null) {
    const isFailure = pushedEventType === "failure" || query.includes("actionTimestamp");
    const apiUrl = isFailure ? this.pushed_event_failed_api : this.pushed_event_api;
    let searchInput = {
      query,
      "exactSearch": true,
      "includeTotalCount": true
    };
    if(!isFailure) {
      searchInput["requiredFieldsToFetch"] = [
        "event_id",
        "entity_id",
        "entity_type",
        "entity",
        "sent_ts"
      ]
      searchInput["retrieveRequiredFields"] = true;
    }
    return this._http.post(apiUrl, searchInput);
  }



}

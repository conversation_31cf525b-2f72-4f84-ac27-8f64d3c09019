
import * as moment from "moment";
import { addFieldParameters } from "../../../shared/constants/search-options/utility";
export const bpdOTSearchFields = [
    {...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
    ...{  
        label:"Brand and Size",
        field:"brandAndSize",
        searchButton:false,
        defaultSelect:false,
        showChip:[],
        query:[],
        elements:[{
            type:"text",
            field:"brandAndSize",
            error:"Required Brand and Size",
            query:[]
        }]
    }
  },
  {...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
        label:"CIC",
        field:"cic",
        query:[],
        showChip:[],
        elements:[{
            type:"text",
            field:"cic",
            error:"Required CIC",
            query:[]
        }],
        searchButton:false,
        defaultSelect:false
     }
  },
  {...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{  
        label:"CPG",
        field:"cpg",
        query:[],
        showChip:[],
        elements:[{
            type:"text",
            error:"Required CPG",
            field:"cpg",
            query:[]
        }],
        searchButton:false,
        defaultSelect:false
    }
  },
  {...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{  
        label:"Created By",
        field:"createUserId",
        query:[],
        showChip:[],
        elements:[{
            field:"createUserId",
            type:"typeahead",
            error:"Required Created By",
            autoSearch:true,
            query:[]
        }],
        searchButton:false,
        defaultSelect:false
    }
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
...{
        label:"Created Date",
        field:"createTimeStamp",
        query:[],
        showChip:[],
        elements:[{
            type:"select",
            field:"createTimeStamp",
            resetQuery:true,
            query:[],
            options:[
                {
                bindLabel: "Today",
                bindValue: "Today",
                label:"Today",
                field:"createTimeStamp",
                defaultSelect:true,
                elements:[{
                    field:"From",
                    placeholder:"From",
                    hide:true,
                    type:"date",
                    query:[],
                    value:`${moment().startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
                    },{
                    field:"To",
                    placeholder:"To",
                    type:"date",
                    hide:true,
                    query:[],
                    value:`${moment().endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
                   }]
                },
                {
                bindLabel: "Range",
                bindValue: "Range",
                label:"Range",
                field:"createTimeStamp",
                defaultSelect:false,
                type:"date",
                elements:[{
                    field:"From",
                    placeholder:"From",
                    error:"Start Date Required",
                    type:"date"
                    },{
                    field:"To",
                    placeholder:"To",
                    error:"End Date Required",
                    type:"date"
                   }]
            }]
        }],
        searchButton:true,
        defaultSelect:false,
        resetQuery:true,
        queryWithOrFilters:[]
    }
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
...{
    label:"Image ID",
    field:"imageId",
    type:"text",
    searchButton:false,
    defaultSelect:false,
    query:[],
    showChip:[],
    elements:[{
        type:"text",
        field:"imageId",
        error:"Required Image ID",
        autoSearch:true,
        query:[]
    }]
  }
    },
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
...{
    label:"Last Period Created",
    field:"lastPeriodCreated",
    searchButton:true,
    dropDownOptionsFromApi : {
        methodName: 'getPeriodOptions',
        optionLevel: 'inputGroupsLevelOptions'
    },
    query:[],
    showChip:[],
    elements:[{
        type:"select",
        field:"lastPeriodCreated",
        fieldWithMinus: "minusPeriodWeek",
        resetQuery:true,
        query:[],
        options:[{
            
            label:"Was",
            field:"was",
            type:"select",
            selected: true,
            bindLabel:"Was",
            bindValue:"lastPeriodCreated",
            defaultSelect:true,
            addMinusSign: false,
            elements: [{
                type:"select",
                field:"lastPeriodCreated",
                resetQuery:true,
                error:"Required Last Period Created",
                query:[],
                options: [{
                    bindLabel:"Period",
                    bindValue:"",
                    label:"Period",
                    field:"lastPeriodCreated",
                    type:"select",
                    defaultSelect:true,
                    disabled: true
                }]
            }]
            },{
            label:"Was Not",
            field:"wasNot",
            type:"select",
            bindLabel:"Was Not",
            bindValue:"minusLastPeriodCreated",
            addMinusSign: true,
            defaultSelect:false,
            elements: [{
                type:"select",
                field:"lastPeriodCreated",
                resetQuery:true,
                error:"Required Last Period Created",
                query:[],
                options: [{
                    bindLabel:"Period",
                    bindValue:"",
                    label:"Period",
                    field:"lastPeriodCreated",
                    type:"select",
                    defaultSelect:true,
                    disabled: true
                }]
            }]
    }]
    }],
    defaultSelect:false,
    resetQuery: true,
    queryWithOrFilters:[]
  }
    },
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
...{
    label:"Last Updated",
    field:"lastUpdateTimestamp",
    query:[],
    showChip:[],
    elements:[{
        type:"select",
        field:"lastUpdateTimestamp",
        resetQuery:true,
        query:[],
        options:[{
            label:"Today",
            bindLabel: "Today",
            bindValue: "Today",
            field:"lastUpdateTimestamp",
            defaultSelect:true,
            elements:[{
                field:"From",
                placeholder:"From",
                hide:true,
                type:"date",
                query:[],
                value:`${moment().startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
                },{
                field:"To",
                placeholder:"To",
                type:"date",
                hide:true,
                query:[],
                value:`${moment().endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
               }]
            },{
            label:"Range",
            bindLabel: "Range",
            bindValue: "Range",
            field:"lastUpdateTimestamp",
            defaultSelect:false,
            type:"date",
            elements:[{
                field:"From",
                placeholder:"From",
                error:"Start Date Required",
                type:"date"
                },{
                field:"To",
                placeholder:"To",
                error:"End Date Required",
                type:"date"
               }]
        }]
    }],
    searchButton:true,
    defaultSelect:false,
    resetQuery:true,
    queryWithOrFilters:[]
}
    },
    {
        ...addFieldParameters({ inQuery: true, inORCondition: true }, { bStart: true, bEnd: true }, { sStart: true, sEnd: true }),
        ...{
            label: "Last Updated By",
            field: "updatedUserId",
            query: [],
            showChip: [],
            elements: [{
                type: "typeahead",
                field: "updatedUserId",
                error: "Required Last Updated By",
                autoSearch: true,
                query: []
            }],
            searchButton: false,
            defaultSelect: false,
            resetQuery: true,
        }
    },
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
    label:"MOB ID",
    field:"mobId",
    query:[],
    showChip:[],
    elements:[{
        type:"text",
        error:"Required MOB ID",
        field:"mobId",
        autoSearch:true,
        query:[]
    }],
    searchButton:false,
    defaultSelect:false
}
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
    label:"Product Group",
    field:"productGroups",
    searchButton:false,
    defaultSelect:false,
    query:[],
    showChip:[],
    elements:[{
        type:"text",
        error:"Required Product Group",
        field:"productGroups",
        autoSearch:true,
        query:[]
    }]
}
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
    label:"Rep UPC",
    field:"repUpc",
    searchButton:false,
    defaultSelect:false,
    overrideQuery: true,
    query:[],
    showChip:[],
    elements:[{
        type:"text",
        error:"Required Rep UPC",
        field:"repUpc",
        autoSearch:true,
        query:[]
    }]
}
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
    label:"Set Price Until",
    field:"priceUntil",
    query:[],
    showChip:[],
    elements:[{
        type:"select",
        field:"priceUntil",
        resetQuery:true,
        hide:true,
        query:[],
        options:[{
            label:"Range",
            bindLabel: "Range",
            bindValue: "Range",
            field:"priceUntil",
            defaultSelect:true,
            type:"date",
            elements:[{
                field:"From",
                placeholder:"From",
                error:"Start Date Required",
                type:"date"
                },{
                field:"To",
                error:"End Date Required",
                placeholder:"To",
                type:"date"
               }]
        }]
    }],
    searchButton:true,
    defaultSelect:false,
    resetQuery:true,
    queryWithOrFilters:[]
}
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
    label:"Set Status Until",
    field:"setOtStatusUntil",
    query:[],
    showChip:[],
    elements:[{
        type:"select",
        field:"setOtStatusUntil",
        hide:true,
        resetQuery:true,
        query:[],
        options:[{
            label:"Range",
            bindLabel: "Range",
            bindValue: "Range",
            field:"setOtStatusUntil",
            defaultSelect:true,
            type:"date",
            elements:[{
                field:"From",
                placeholder:"From",
                error:"Start Date Required",
                type:"date"
                },{
                field:"To",
                error:"End Date Required",
                placeholder:"To",
                type:"date"
               }]
        }]
    }],
    searchButton:true,
    defaultSelect:false,
    resetQuery:true,
    queryWithOrFilters:[]
}
},
{...addFieldParameters({inQuery:true,inORCondition:true},{bStart:true,bEnd:true},{sStart:true,sEnd:true}),
  ...{
    label:"Template ID",
    field:"requestId",
    query:[],
    showChip:[],
    elements:[{
        type:"text",
        field:"requestId",
        error:"Required Template ID",
        autoSearch:true,
        query:[]
    }],
    searchButton:false,
    defaultSelect:true
}
}

]
// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { Injector, NO_ERRORS_SCHEMA } from '@angular/core';
// import { TemplateBaseComponent } from './templates.base.component';
// import { Router } from '@angular/router';
// import { AuthService } from '@appServices/common/auth.service';
// import { BulkUpdateService } from '@appServices/management/bulk-update.service';
// import { FacetItemService } from '@appServices/common/facet-item.service';
// import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
// import { InitialDataService } from '@appServices/common/initial.data.service';
// import { OfferDetailsService } from '@appOffersServices/offer-details.service';
// import { QueryGenerator } from '@appServices/common/queryGenerator.service';
// import { RequestFormService } from '@appRequestServices/request-form.service';
// import { SearchOfferService } from '@appOffersServices/search-offer.service';
// import { StoreGroupService } from '@appGroupsServices/store-group.service';
// import { BaseManagementService } from '@appServices/management/base-management.service';
// import { BehaviorSubject } from 'rxjs';
// import { AppInjector } from '@appServices/common/app.injector.service';
// const chai = require("chai"),
//   spies = require("chai-spies"),
//   expect = chai.expect;
// chai.use(spies);
// const spyOn = chai.spy.on;

// const sinon = require("sinon");
// const sinonChai = require("sinon-chai");

// chai.use(sinonChai);
// xdescribe('TemplateBaseComponent', () => {
//   let component: TemplateBaseComponent;
//   let fixture: ComponentFixture<TemplateBaseComponent>;
//   const initialDataServiceStub = () => ({
//     getAppData: () => ({}),
//     getConfigUrls: (cLONE_API) => ({}),
//     getSearchOptions: () =>({})
//   });

//   const facetItemServiceStub = () => ({
//     setProgramCodeSelected: () => ({}),
//     addDeliveryChannelFilter: () => ({}),
//     setOfferFilter: () => ({}),
//     getSearchFacetFields: () => ({}),
//     getFilterFacetFields: () => ({}),
//     getOfferFilter: () => ({}),
//     sortProperties: () => ({}),
//     addDeliveryChannels: () => ({}),
//     populateFacetSearch: () => ({}),
//     populateFacet: () => ({}),
//     addOfferTypes: () => ({}),
//     generateQuery: () => ({}),
//     getFacetItems: () => ({}),
//     getQueryForDatesSearch: () => ({}),
//     emptyTodayOption: () => ({}),
//     programCodeSelected:'GR',
//     getProgramCode: { subscribe: (f) => f({}) }
//   });


//   const bulkUpdateServiceStub = () => ({
//     requestIdsListSelected$: { subscribe: (f) => f({}) },
//     bulkAssignedUsers: {
//       digitalUser: { firstName: {}, lastName: {}, userId: {} },
//       nonDigitalUser: { firstName: {}, lastName: {}, userId: {} },
//     },
//     userTypeArray: [],
//     hideApiErrorOnRequestHome:()  => ({ subscribe: (f) => f({}) }),
//     isSelectionReset: new BehaviorSubject(true),
//     bulkUnAssignUsers: (userType, query) => ({ subscribe: (f) => f({}) }),
//     bulkAssignUsers: (usersList, query) => ({ subscribe: (f) => f({}) }),
//   });

//   const requestFormServiceStub = () => ({
//     cloningProcess$: new BehaviorSubject(false),
//   });

//   const searchOfferServiceStub = () => ({
//     savedSearchForRetrieve: (reqType, userType) => ({
//       subscribe: (f) => f({}),
//     }),
//     searchAllOffers: (arg, arg2) => ({
//       subscribe: (f) => f({}),
//       bind: () => ({}),
//     }),
//     savedSearchforOffer: (searchQuery, savedSearchName, type) => ({
//       subscribe: (f) => f({}),
//     }),
//     updateSavedSearch: (modifyItem, savedSearchName) => ({
//       subscribe: (f) => f({}),
//     }),
//     deleteSavedSearchOffer: (name, type) => ({ subscribe: (f) => f({}) }),
//   });

//   // const routerStub = { navigate: array => ({}) };
//   const queryGeneratorStub = () => ({
//     setQueryWithFilter: () => ({}),
//     setQuery: () => ({}),
//     getQuery: () => ({}),
//     getQueryWithFilter: () => ({
//       filter: () => ({})
//     }),
//     removeParametersFromQueryFilter:() => ({}),
//     removeParam: () => ({}),
//     removeParameters: () => ({}),
//     pushParameters: () => ({}),
//     removeQueryWithFilter: () => ({}),
//     getQueryFilter: () => ({})
//   });
//   const storeGroupServiceStub = {
//     createInstance: () => ({}),
//     createFacetInstance: () => ({}),
//     setEnableForm: (arg) => ({}),
//     searchStoreGroup: (groupName, arg) => ({ subscribe: () => ({}) }),
//     setStoreQuery: (object) => ({}),
//     getFeatureKeys: () => ({ length: {}, includes: () => ({}) }),
//     setFeatureKeys: (arg) => ({}),
//     populateStoreFacets: () => ({ subscribe: () => ({}) }),
//     populateStoreFilterSearch: (object) => ({}),
//     getStoreQuery: () => ({}),
//     createStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
//     updateStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
//     call: (arg) => ({}),
//     getStoreIds: (object) => ({ subscribe: () => ({}) }),
//   };

//   const authServiceStub = () => ({
//     isUserDataAvailable: new BehaviorSubject(true),
//     authenticated: {},
//     signIn: () => ({}),
//     getUserId: () => ({}),
//   });
//   const baseManagementServiceStub = () => ({
//     getAllTemplatesApi: () =>({})
//   })
//   const routerStub = () => ({
//     events: new BehaviorSubject<any>(null),
//     navigate: ()=>({}),
//     url:`/create`
//   });
//   const featureFlagServiceStub = () => ({
//     assignFeatureFlag: () => ({}),
//     isFeatureFlagEnabled: (arg) => ({}),
//     hasFlags: () => ({})
//   });
//   const offerDetailsServiceStub = ()=>({
//     offerDetailsService:{
//       getProgramSubType:()=>({})
//     }

//   });
//   beforeEach(() => {
//     TestBed.configureTestingModule({
//       schemas: [NO_ERRORS_SCHEMA],
//       declarations: [TemplateBaseComponent],
//       providers: [
//         { provide: FeatureFlagsService, useFactory: featureFlagServiceStub },
//         {provide: OfferDetailsService, useFactory: offerDetailsServiceStub },
//         { provide: QueryGenerator, useFactory: queryGeneratorStub },
//         { provide: StoreGroupService, useValue: storeGroupServiceStub },
//         { provide: FacetItemService, useFactory: facetItemServiceStub },
//         { provide: InitialDataService, useFactory: initialDataServiceStub },
//         { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
//         { provide: RequestFormService, useValue: requestFormServiceStub },
//         { provide: SearchOfferService, useFactory: searchOfferServiceStub },
//         {provide: BaseManagementService, useFactory: baseManagementServiceStub},
//         { provide: AuthService, useFactory: authServiceStub }
//          ]
//     })
//       AppInjector.setInjector(TestBed.inject(Injector));
//       fixture = TestBed.createComponent(TemplateBaseComponent);
//       component = fixture.componentInstance;
//   });

//   it('can load instance', () => {
//     expect(component).to.be.ok;
//   });
// });

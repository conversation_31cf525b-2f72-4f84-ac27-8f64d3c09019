import { Component, OnInit, ViewChild } from '@angular/core';
import { StoreGroupService } from '@appGroupsServices/store-group.service';
import { offerRequestModel } from '@appModels/offer-request.model';
import { OfferDetailsService } from '@appOffersServices/offer-details.service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BaseManagementService } from '@appServices/management/base-management.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { Router } from 'express-serve-static-core';

@Component({
  selector: 'oms-templates',
  templateUrl: './templates.base.component.html'
})
export class TemplateBaseComponent  extends UnsubscribeAdapter implements OnInit {
  @ViewChild('loadDynamic') loadDynamicComponent;
  public queryGenerator: QueryGenerator;
  public storeGroupService: StoreGroupService;
  public facetItemService: FacetItemService;
  public _initialDataService: InitialDataService;
  public _bulkupdateservice: BulkUpdateService;
  public offerRequests: offerRequestModel[];
  public _requestFormService : RequestFormService;
  public _searchOfferService: SearchOfferService;
  public baseManagementService: BaseManagementService;
  public _authService:AuthService;
  public _router: Router;
  public featureFlagService: FeatureFlagsService;
  public offerDetailsService: OfferDetailsService;
  
  constructor() {
    super();
    const injector = AppInjector.getInjector();
    this.featureFlagService = injector.get(FeatureFlagsService);  
    this.offerDetailsService = injector.get(OfferDetailsService);
    this.queryGenerator = injector.get(QueryGenerator);
    this.storeGroupService = injector.get(StoreGroupService);
    this.facetItemService = injector.get(FacetItemService);
    this._initialDataService = injector.get(InitialDataService);
    this._bulkupdateservice = injector.get(BulkUpdateService);
    this._requestFormService = injector.get(RequestFormService);
    this._searchOfferService = injector.get(SearchOfferService);
    this.baseManagementService = injector.get(BaseManagementService);
    this._authService = injector.get(AuthService);
   }

  ngOnInit(): void {
    // intentionally left empty
  }
  getOfferRequestComponent(){
    let component = 'management';
    return {
      component
    }
  }

}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { AdditionalInformationComponent } from './additional-information.component';
import { TEMPLATE_CREATE_RULES } from '../../shared/rules/rules';
import { BehaviorSubject } from 'rxjs';

describe('AdditionalInformationComponent', () => {
  let component: AdditionalInformationComponent;
  let fixture: ComponentFixture<AdditionalInformationComponent>;

  beforeEach(() => {
    const offerTemplateBaseServiceStub = () => ({
      generateFormControlFieldsKeys: arg => ({}),
      createFields: (fields, arg, parse, arg1) => ({}),
      createFormControls: (object, templateForm, data, arg1) => ({}),
      templateForm: {}
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [AdditionalInformationComponent],
      providers: [
        {
          provide: OfferTemplateBaseService,
          useFactory: offerTemplateBaseServiceStub
        }
      ]
    });
    fixture = TestBed.createComponent(AdditionalInformationComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`bpd_rule has default value`, () => {
    expect(component.bpd_rule).toEqual(TEMPLATE_CREATE_RULES.BPD);
  });

  it(`programCode has default value`, () => {
    expect(component.programCode).toEqual(TEMPLATE_CREATE_RULES.BPD.programCode);
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      spyOn(component, 'initSubscribe').and.callThrough();
      component.ngOnInit();
      expect(component.initSubscribe).toHaveBeenCalled();
    });
  });

  describe('initSubscribe', () => {
    it('makes expected calls', () => {
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      offerTemplateBaseServiceStub.templateData$ = new BehaviorSubject({})
      spyOn(component, 'createFormControls').and.callThrough();
      component.initSubscribe();
      expect(component.createFormControls).toHaveBeenCalled();
    });
  });
  describe("createFormControls", () => {
    it("makes expected calls", () => {
      component.bpd_rule = TEMPLATE_CREATE_RULES.BPD;
      const OfferTemplateBaseServiceStub: OfferTemplateBaseService = TestBed.inject(OfferTemplateBaseService);
      spyOn(OfferTemplateBaseServiceStub, "createFormControls");
      spyOn(OfferTemplateBaseServiceStub, "generateFormControlFieldsKeys").and.returnValue({desc: "test"});
      spyOn(OfferTemplateBaseServiceStub, "createFields");
      component.createFormControls({info: {}});
      expect(OfferTemplateBaseServiceStub.createFormControls).toHaveBeenCalled();
    });
  });
});

import { Component, Input, OnInit } from '@angular/core';
import { TEMPLATE_CREATE_RULES } from '@appModules/templates/core/offer-template/details/shared/rules/rules';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
  selector: 'app-additional-information',
  templateUrl: './additional-information.component.html',
  styleUrls: ['./additional-information.component.scss']
})
export class AdditionalInformationComponent extends UnsubscribeAdapter implements OnInit {

  @Input() isSummary: boolean;
  bpd_rule = TEMPLATE_CREATE_RULES.BPD;
  programCode = this.bpd_rule.programCode;
  public offerRequest = JSON.stringify(this.bpd_rule.offerRequest);
  constructor(public offerTemplateBaseService: OfferTemplateBaseService) {
    super();
  }

  ngOnInit(): void {
    this.initSubscribe();
  }
  initSubscribe() {
    this.subs.sink = this.offerTemplateBaseService?.templateData$?.subscribe(
      (data = {}) => {
        this.createFormControls(data || null);
      }
    );
  }
  get fields() {
    return JSON.parse(this.offerRequest);
  }
  createFormControls(data) {
    let fields;
    if (data && Object.keys(data).length) {
      const parse = JSON.parse(JSON.stringify(data));
      fields = { desc: this.fields["desc"] };
      const getKeys = this.offerTemplateBaseService.generateFormControlFieldsKeys(
        data["info"]
      );
      this.offerTemplateBaseService.createFields(
        fields,
        getKeys["desc"],
        parse,
        this.fields["desc"]
      );
    } else {
      fields = { ...this.fields["desc"] };
    }

    this.offerTemplateBaseService.createFormControls(
      { desc: fields },
      this.offerTemplateBaseService.templateForm,
      data,
      data && Object.keys(data).length?true:false
    );
  }
}

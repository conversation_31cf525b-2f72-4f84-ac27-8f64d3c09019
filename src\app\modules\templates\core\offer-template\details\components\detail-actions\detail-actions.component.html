<div class="col p-0" [ngClass]="actionsDropDownCssBasedOnPermissions">
    <label class="text-label m-0">
      <div class="show" [hidden]="rules?.length ===0">
        <button
         type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <label >More
          </label>
        </button>
  
        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
          <a test class="dropdown-item" *ngFor="let item of rules"
            (click)="eventClickActionHandler(item)">
            <label class="mb-0 text-label" >{{ item }}</label></a>
        </div>
      </div>
    </label>
  </div>
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { PermissionsConfigurationService } from '@appShared/albertsons-angular-authorization';
import { ActionsService } from '@appTemplates/services/actions.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { DetailActionsComponent } from './detail-actions.component';

describe('DetailActionsComponent', () => {
  let component: DetailActionsComponent;
  let fixture: ComponentFixture<DetailActionsComponent>;

  beforeEach(() => {
    const initialDataServiceStub = () => ({
      getAppData: () => ({ offerRequestStatuses: {} })
    });
    const actionsServiceStub = () => ({
      addAndRemoveRules: (rules, deletePermissions, addPermissions) => ({})
    });
    const permissionsConfigurationServiceStub = () => ({
      getAllStrategies: () => ({})
    });
    const bsModalServiceStub = () => ({ show: (template, options) => ({}) });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [DetailActionsComponent],
      providers: [
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: ActionsService, useFactory: actionsServiceStub },
        {
          provide: PermissionsConfigurationService,
          useFactory: permissionsConfigurationServiceStub
        },
        { provide: BsModalService, useFactory: bsModalServiceStub }
      ]
    });
    fixture = TestBed.createComponent(DetailActionsComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`openModel has default value`, () => {
    expect(component.openModel).toEqual([]);
  });

  it(`apiAction has default value`, () => {
    expect(component.apiAction).toEqual([]);
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      const actionsServiceStub: ActionsService = fixture.debugElement.injector.get(
        ActionsService
      );
      component.templateData = {info: {digitalUiStatus: "D"}}
      spyOn(initialDataServiceStub, 'getAppData').and.returnValue({offerRequestStatuses: {D: "Completed"}})
      spyOn(actionsServiceStub, 'addAndRemoveRules').and.callThrough();
      component.ngOnInit();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
      expect(actionsServiceStub.addAndRemoveRules).toHaveBeenCalled();
    });
  });
  describe('openModal', () => {
    it('makes expected calls', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(BsModalService);
      spyOn(bsModalServiceStub, 'show');
      component.openModal('template', 'actions');
     expect(bsModalServiceStub.show).toHaveBeenCalled()
    });
  });
  describe('eventClickActionHandler', () => {
    it('makes expected calls', () => {
      spyOn(component, 'openModal');
      component.openModel = ["delete"]
      component.eventClickActionHandler('delete');
     expect(component.openModal).toHaveBeenCalled()
    });
    it('makes expected calls', () => {
      spyOn(component, 'apiCancelCallback');
      component.apiAction = ["Cancel"]
      component.eventClickActionHandler('Cancel');
     expect(component.apiCancelCallback).toHaveBeenCalled()
    });
  });
});

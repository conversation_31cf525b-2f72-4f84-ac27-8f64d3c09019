import { Component, Input, OnInit } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { PermissionsConfigurationService } from '@appShared/albertsons-angular-authorization';
import { ActionsService } from '@appTemplates/services/actions.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TEMPLATE_WORKFLOW_RULES } from '../../shared/rules/detail.actions';

@Component({
  selector: 'app-detail-actions',
  templateUrl: "./detail-actions.component.html",
  styleUrls: ['./detail-actions.component.scss']
})
export class DetailActionsComponent implements OnInit {
  @Input() templateData;
  modalRef: BsModalRef;
  actionsDropDownCssBasedOnPermissions: string;
  rules: any[];
  deletePermissions = {
    [CONSTANTS.Permissions.AssignNonDigitalUsers]:{
      Detail: ["Save", "Copy", "Delete"]
    },
    [CONSTANTS.Permissions.AssignDigitalUsers]:{
      Detail: ["Save", "Copy", "Delete"]
    },
  }
  addPermissions = {
    [CONSTANTS.Permissions.AssignNonDigitalUsers]:{
      Detail: ["Save", "Copy", "Delete"]
    },
    [CONSTANTS.Permissions.AssignDigitalUsers]:{
      Detail: ["Save", "Copy", "Delete"]
    },
  }
  openModel = [];
  apiAction = [];
  constructor(  
    private _permissionsConfigurationService: PermissionsConfigurationService,
    private modalService: BsModalService,
    private actionService:ActionsService,
    private initialDataService:InitialDataService

    ) { }
 
  ngOnInit(): void {
    const appData = this.initialDataService.getAppData(),
          statusData = appData.offerRequestStatuses,
          {info:{digitalUiStatus}} = this.templateData,
          ruleStatus = statusData[digitalUiStatus];
   const rules = TEMPLATE_WORKFLOW_RULES?.[ruleStatus]?.["Detail"];
   this.rules = this.actionService.addAndRemoveRules(rules,this.deletePermissions,this.addPermissions);
  }
  
  getDropDownClassName(stageRules) {
    this.actionsDropDownCssBasedOnPermissions = "";
    // Actions dropdown should be available only when it contains atleast one option.
    // If not based on authorizing strategy either disable or hide it
    if (stageRules && !stageRules.length) {
      const appLevelAuthorizationStrategy = this._permissionsConfigurationService.getAllStrategies();
      this.actionsDropDownCssBasedOnPermissions = appLevelAuthorizationStrategy.hasOwnProperty("disable")?"disable":"hide";
    }
  }
  eventClickActionHandler(type){
    if(this.openModel.includes(type)){
      this.openModal(this?.[`open${type}Model`], { keyboard: true, class: "modal-m" });
    }else if(this.apiAction.includes(type)){
      this?.[`api${type}Callback`]();
    }
  }
  openModal(template, options) {
    this.modalRef = this.modalService.show(template, options);
  }
  apiCancelCallback(){

  }
}

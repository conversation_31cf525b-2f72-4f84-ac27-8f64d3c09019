<ng-sidebar-container [ngClass]="podEdit ? 'podEditWrap' : 'podSummaryWrap'" class="bpd-pod-wrap">
    <ng-sidebar *ngIf="podEdit" [(opened)]="_opened" [position]="_POSITIONS[1]">
      <div class="d-inline-flex preview-backgorund">
        <div class="preview-close-button">
          <button class="preview-slider-button-layout btn">
            <img src="assets/icons/arrow-right-white-icon.svg" (click)="_toggleOpened()" height="24" width="24" alt="" />
          </button>
        </div>
        <div class="pod-preview-section col flex-row">
          <h3 class="pod-section">POD Preview</h3>
  
          <div class="col-12 px-2">
            <preview-card [productVersionIndex]="index" [storeGroupVersion]="storeGroupVersion"></preview-card>
          </div>
        </div>
      </div>
    </ng-sidebar>
    <div ng-sidebar-content class="bpd-pd">
      <div class="bg-clr pb-4">
        <div class="">
          <div class="row no-gutters">
            <h3 class="pod-section" *ngIf="storeGroupVersion?.controls?.podDetails">POD Details</h3>
            <div class="d-flex col justify-content-end py-6">
              <button class="preview-slider-button-layout btn" *ngIf="!isSummary">
                <img src="assets/icons/arrow-left-white-icon.svg" (click)="_toggleOpened()" height="24" width="24" alt="" />
              </button>
            </div>
          </div>
  
          <div class="row mx-1 justify-content-left my-3 sg-ot-wrap mb-5">
            <div
              [ngClass]="isSummary ? 'col-5 px-0' : 'col-5 scene-icon'"
              app-input-text-component
              [form]="storeGroupVersion"
              (keydown.tab)="setScene7Img($event)"
              (change)="setScene7Img($event)"
              (keydown.enter)="setScene7Img($event)"
              [section]="'podDetails'"
              [property]="'scene7ImageId'"
              [summary]="isSummary"
            ></div>
            <div [ngClass]="isSummary ? 'd-none' : 'col-1 icon-image'" (change)="setScene7Img($event)"></div>
  
            <div
              [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6'"
              app-input-text-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              [property]="'priceText'"
              [summary]="isSummary"
            ></div>
          </div>
          <div class="row mx-1 justify-content-left my-3 sg-ot-wrap mb-5">
            <div
              [ngClass]="isSummary ? 'col-5 px-0' : 'col-6'"
              app-input-text-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              [property]="'headline1'"
              [summary]="isSummary"
            ></div>
            <div
              [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6'"
              app-input-text-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              [property]="'headline2'"
              [summary]="isSummary"
            ></div>
          </div>
          <div class="row mx-1 justify-content-left my-3 sg-ot-wrap mb-5">
            <div
              [ngClass]="isSummary ? 'col-12 px-0' : 'col-12'"
              app-input-text-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              [property]="'offerDescription'"
              [summary]="isSummary"
            ></div>
          </div>
          <div class="row mx-1 justify-content-left my-3 sg-ot-wrap mb-9">
            <div
              [ngClass]="isSummary ? 'col-5  px-0' : 'col-6'"
              app-input-display-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              class="pl-0"
              [property]="'upcQtyOrUOM'"
              [summary]="isSummary"
            ></div>
            <div
              [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6'"
              app-input-display-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              class="pl-0"
              [property]="'offerDetailsCode'"
              [summary]="isSummary"
            ></div>
          </div>
          <div class="row mx-1 mt-2">
            <div class="col-12">
                <app-upc-list-table [mobId]="mobId" [regionId]="regionId" [pgId]="pgId" [isBPG]="isBPG"></app-upc-list-table>
            </div>
          </div>  
  
          <div class="row mx-1 justify-content-left my-3 sg-ot-wrap mb-5">
            <div
              [ngClass]="isSummary ? 'col-5 px-0' : 'col-6 bg-white-select'"
              app-input-select-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              [property]="'shoppingListCategory'"
              [summary]="isSummary"
            ></div>
            <div
              [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6 bg-white-select'"
              app-ng-select-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              [property]="'leftNavCategory'"
              [placeholder]="'Enter Left Nav Category'"
              [summary]="isSummary"
            ></div>
          </div>
          <div class="row mx-1 justify-content-left my-3 sg-ot-wrap mb-5">
            <div
              [ngClass]="isSummary ? 'col-5 px-0' : 'col-6 bg-white-select'"
              app-input-select-component
              [form]="storeGroupVersion"
              [section]="'podDetails'"
              class="pl-0"
              [property]="'podUsageLimit'"
              [summary]="isSummary"
            ></div>
           
          </div>
        </div>
      </div>
    </div>
  </ng-sidebar-container>
  
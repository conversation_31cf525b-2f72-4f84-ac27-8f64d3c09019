import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProductGroupService } from '@appGroupsServices/product-group.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { of } from 'rxjs';
import { TEMPLATE_CREATE_RULES } from '../../shared/rules/rules';
import { PodDetailsTemplateComponent } from './pod-details-template.component';

describe('PodDetailsTemplateComponent', () => {
  let component: PodDetailsTemplateComponent;
  let fixture: ComponentFixture<PodDetailsTemplateComponent>;

  beforeEach(() => {
    const generalOfferTypeServiceStub = () => ({
      updatePodData: object => ({ podDetails: {} })
    });
    const productGroupServiceStub = () => ({
      searchBaseProductGroup: query => ({ subscribe: f => f({}) })
    });
    const offerTemplateBaseServiceStub = () => ({
      storeGroupVersionControl: {},
      amountValueChange$: { subscribe: f => f({}) },
      searchProductImage: event => ({}),
      setPodDataOnValueChanges: () => ({}),
      podUsageControl: {},
      setCategoryValue: () => ({}),
      templateData$: { subscribe: f => f({}) },
      createFormControls: (
        podDetails,
        storeGroupVersion,
        storeGroupVersionData,
        arg2
      ) => ({}),
      setDisplayStartDate: () => ({}),
      checkCustomValidate: podDetails => ({})
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [PodDetailsTemplateComponent],
      providers: [
        {
          provide: GeneralOfferTypeService,
          useFactory: generalOfferTypeServiceStub
        },
        { provide: ProductGroupService, useFactory: productGroupServiceStub },
        {
          provide: OfferTemplateBaseService,
          useFactory: offerTemplateBaseServiceStub
        }
      ]
    });
    fixture = TestBed.createComponent(PodDetailsTemplateComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`isBPG has default value`, () => {
    expect(component.isBPG).toEqual(false);
  });

  it(`podEdit has default value`, () => {
    expect(component.podEdit).toEqual(false);
  });

  it(`colorTheme has default value`, () => {
    expect(component.colorTheme).toEqual(`theme-dark-blue`);
  });

  it(`bpd_rule has default value`, () => {
    expect(component.bpd_rule).toEqual(TEMPLATE_CREATE_RULES.BPD);
  });

  it(`programCode has default value`, () => {
    expect(component.programCode).toEqual(TEMPLATE_CREATE_RULES.BPD.programCode);
  });

  it(`podDetails has default value`, () => {
    expect(component.podDetails).toEqual(TEMPLATE_CREATE_RULES.BPD.podDetails);
  });

  it(`_opened has default value`, () => {
    expect(component._opened).toEqual(false);
  });

  it(`_POSITIONS has default value`, () => {
    expect(component._POSITIONS).toEqual([`left`, `right`, `top`, `bottom`]);
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      spyOn(component, 'createFormControls');
      spyOn(component, 'initSubScribe');
      spyOn(component, 'podEditDetails');
      component.ngOnInit();
      expect(component.createFormControls).toHaveBeenCalled();
      expect(component.initSubScribe).toHaveBeenCalled();
      expect(component.podEditDetails).toHaveBeenCalled();
    });
  });

  describe('initSubScribe', () => {
    it('makes expected calls', () => {
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      spyOn(
        offerTemplateBaseServiceStub,
        'setPodDataOnValueChanges'
      ).and.callThrough();
      spyOn(offerTemplateBaseServiceStub, 'setCategoryValue').and.callThrough();
      component.initSubScribe();
      expect(
        offerTemplateBaseServiceStub.setPodDataOnValueChanges
      ).toHaveBeenCalled();
      expect(offerTemplateBaseServiceStub.setCategoryValue).not.toHaveBeenCalled();
    });
  });

  describe('createFormControls', () => {
    it('makes expected calls', () => {
      const generalOfferTypeServiceStub: GeneralOfferTypeService = fixture.debugElement.injector.get(
        GeneralOfferTypeService
      );
      const productGroupServiceStub: ProductGroupService = fixture.debugElement.injector.get(
        ProductGroupService
      );
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      spyOn(component, 'checkCustomValidate').and.callThrough();
      spyOn(generalOfferTypeServiceStub, 'updatePodData').and.returnValue({podDetails: {}, productGroupVersions: [{productGroup: {id:122}}]});
      spyOn(
        productGroupServiceStub,
        'searchBaseProductGroup'
      ).and.returnValue(of({productGroups: [{productGroupType: "BASE", id: "nk"}]}))
      spyOn(
        offerTemplateBaseServiceStub,
        'createFormControls'
      );
      spyOn(
        offerTemplateBaseServiceStub,
        'setDisplayStartDate'
      ).and.callThrough();
      component.createFormControls();
      expect(component.checkCustomValidate).toHaveBeenCalled();
      expect(generalOfferTypeServiceStub.updatePodData).toHaveBeenCalled();
      expect(productGroupServiceStub.searchBaseProductGroup).toHaveBeenCalled();
      expect(
        offerTemplateBaseServiceStub.createFormControls
      ).toHaveBeenCalled();
      expect(
        offerTemplateBaseServiceStub.setDisplayStartDate
      ).toHaveBeenCalled();
    });
  });

  describe('checkCustomValidate', () => {
    it('makes expected calls', () => {
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      spyOn(
        offerTemplateBaseServiceStub,
        'checkCustomValidate'
      ).and.callThrough();
      component.checkCustomValidate();
      expect(
        offerTemplateBaseServiceStub.checkCustomValidate
      ).toHaveBeenCalled();
    });
  });
});

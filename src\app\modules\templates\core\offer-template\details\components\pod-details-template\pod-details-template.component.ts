import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { ProductGroupService } from '@appGroupsServices/product-group.service';
import { TEMPLATE_CREATE_RULES } from '@appModules/templates/core/offer-template/details/shared/rules/rules';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';

@Component({
  selector: 'app-pod-details-template',
  templateUrl: './pod-details-template.component.html',
  styleUrls: ['./pod-details-template.component.scss']
})
export class PodDetailsTemplateComponent extends UnsubscribeAdapter implements OnInit {
  @Input() storeGroupVersion: UntypedFormGroup;
  @Input() index;
  @Input() isSummary: boolean;
  @Input() valueChange;

  mobId = '';
  regionId = '';
  pgId = '';
  isBPG = false;

  podEdit: boolean = false;
  colorTheme = "theme-dark-blue";
  bpd_rule = TEMPLATE_CREATE_RULES.BPD;
  programCode = this.bpd_rule.programCode;
  
  public podDetails = this.bpd_rule.podDetails;
  public _opened: boolean = false;
  public _POSITIONS: Array<string> = ["left", "right", "top", "bottom"];
  public _toggleOpened(): void {
    this._opened = !this._opened;
  }

  constructor(public offerTemplateBaseService: OfferTemplateBaseService, 
    public generalOfferTypeServic$: GeneralOfferTypeService,
    public productGroupService: ProductGroupService) {
    super();
  }

  ngOnInit(): void {
     
      this.offerTemplateBaseService.storeGroupVersionControl = this.storeGroupVersion;

      this.offerTemplateBaseService.amountValueChange$.subscribe((val:any)=>{
        let priceTextNewValue = val ? `$${Number(val).toFixed(2)}` : '';
        this.storeGroupVersion?.get('podDetails')?.get('priceText').setValue(priceTextNewValue);
      });
      
      this.createFormControls();
      this.initSubScribe();
      this.podEditDetails();
  }
  ngOnChanges() {
    this.offerTemplateBaseService.storeGroupVersionControl = this.storeGroupVersion;
  }

  podEditDetails() {
    this.podEdit = !this.isSummary;
  }
  setScene7Img(event) {
    this.offerTemplateBaseService.searchProductImage(event);
  }
  initSubScribe() {
    
    this.offerTemplateBaseService.setPodDataOnValueChanges();
   
    const usageLimitCtrl = this.offerTemplateBaseService?.templateForm?.get("rules")?.get("usageLimitTypePerUser");
    const podUsageCtrl = this.offerTemplateBaseService.podUsageControl;
    if (usageLimitCtrl?.value === "ONCE_PER_OFFER" && !podUsageCtrl?.value) {
      podUsageCtrl?.setValue("ONCE_PER_OFFER");
    }

    //this.offerTemplateBaseService.setCategoryValue();

    const infoValue = this.offerTemplateBaseService?.templateForm?.get('info')?.value;
    if (infoValue) {
      this.mobId = infoValue?.mobId;
      this.regionId = infoValue?.regionId;
    }
  }
  createFormControls() {
    this.subs.sink = this.offerTemplateBaseService.templateData$.subscribe((data = {}) => {
      const { rules: { qty='',uom='',qualificationAndBenefit: { offerTemplateOffers = [{}] } = {} } = {} } = data || {},
      storeGroupVersionData = this.generalOfferTypeServic$.updatePodData({offerRequestOffers:offerTemplateOffers});
      if(storeGroupVersionData?.podDetails ){
      storeGroupVersionData.podDetails['upcQtyOrUOM'] = `${qty} ${uom}`;
      }
      this.pgId = storeGroupVersionData?.productGroupVersions?.[0]?.productGroup?.id;
      this.offerTemplateBaseService.createFormControls(
        this.podDetails,
        this.storeGroupVersion,
        storeGroupVersionData,
        data && Object.keys(data).length?true:false
      );

      const query = `productGroupRid=(${this.pgId});`;
      
      this.subs.sink = this.productGroupService.searchBaseProductGroup(query).subscribe((data: any) =>{
        this.isBPG = data?.productGroups[0]?.productGroupType === 'BASE';
        if (this.isBPG) {
          this.pgId = data?.productGroups[0]?.id;
        }
      })

      this.offerTemplateBaseService.setDisplayStartDate();
      this.checkCustomValidate();
    });
    
  }
  checkCustomValidate() {
    this.offerTemplateBaseService.checkCustomValidate(this.podDetails);
  }
  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

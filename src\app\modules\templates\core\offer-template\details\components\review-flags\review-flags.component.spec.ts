import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { ReviewFlagsComponent } from './review-flags.component';
import { TEMPLATE_CREATE_RULES } from '../../shared/rules/rules';
import { BehaviorSubject } from 'rxjs';

describe('ReviewFlagsComponent', () => {
  let component: ReviewFlagsComponent;
  let fixture: ComponentFixture<ReviewFlagsComponent>;

  beforeEach(() => {
    const formBuilderStub = () => ({
      group: object => ({ addControl: () => ({}) })
    });
    const initialDataServiceStub = () => ({ getAppData: () => ({}) });
    const offerTemplateBaseServiceStub = () => ({});
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [ReviewFlagsComponent],
      providers: [
        { provide: UntypedFormBuilder, useFactory: formBuilderStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        {
          provide: OfferTemplateBaseService,
          useFactory: offerTemplateBaseServiceStub
        }
      ]
    });
    fixture = TestBed.createComponent(ReviewFlagsComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`bpd_rule has default value`, () => {
    expect(component.bpd_rule).toEqual(TEMPLATE_CREATE_RULES.BPD);
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(component, 'initSubscribe');
      spyOn(initialDataServiceStub, 'getAppData').and.callThrough();
      component.ngOnInit();
      expect(component.initSubscribe).toHaveBeenCalled();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
    });
  });

  describe('initSubscribe', () => {
    it('makes expected calls', () => {
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      offerTemplateBaseServiceStub.templateData$ = new BehaviorSubject({})
      spyOn(component, 'setFormControlForFlags');
      component.initSubscribe();
      expect(component.setFormControlForFlags).toHaveBeenCalled();
    });
  });
  describe('fields', () => {
    it('should parse and return offerRequest as JSON', () => {
      component.offerRequest = JSON.stringify({ key: 'value' });
      expect(component.fields).toEqual({ key: 'value' });
    });
  });

  describe('checkIfSelected', () => {
    it('should return true if the option is selected in reviewFlags', () => {
      const data = { info: { reviewFlags: { option1: true } } };
      expect(component.checkIfSelected(data, 'option1')).toBeTrue();
    });

    it('should return false if the option is not selected in reviewFlags', () => {
      const data = { info: { reviewFlags: { option1: false } } };
      expect(component.checkIfSelected(data, 'option2')).toBeFalse();
    });

    it('should return false if data is null or undefined', () => {
      expect(component.checkIfSelected(null, 'option1')).toBeFalse();
      expect(component.checkIfSelected(undefined, 'option1')).toBeFalse();
    });
  });

  describe('reviewFlagParentFg', () => {
    it('should return the "info" form group from templateForm', () => {
      const mockFormGroup = new UntypedFormGroup({});
      component.offerTemplateBaseService.templateForm = new UntypedFormGroup({
        info: mockFormGroup
      });
      expect(component.reviewFlagParentFg).toBe(mockFormGroup);
    });

    it('should return undefined if "info" form group does not exist', () => {
      component.offerTemplateBaseService.templateForm = new UntypedFormGroup({});
    });
  });

  describe('setFormControlForFlags', () => {
    it('should call setFormControlForFlags with mocked data', () => {
      const mockData = { key: 'mockValue' };
      component.offerTemplateBaseService.templateData$ = new BehaviorSubject(mockData);
      spyOn(component, 'setFormControlForFlags');
      component.initSubscribe();
      expect(component.setFormControlForFlags).toHaveBeenCalledWith(mockData);
    });

    xit('should create form controls for each flag option', () => {
      // spyOnProperty(component, 'reviewFlagParentFg', 'get').and.returnValue(new UntypedFormGroup({}));
      const mockData = { info: { reviewFlags: {} } };
      const mockFlagOptions = { option1: 'Option 1', option2: 'Option 2' };
      const mockAppData = { reviewFlags: mockFlagOptions };
      Object.assign(component['fields'], { reviewFlags: { appDataOptions: 'reviewFlags' } });
      
      component.appData = mockAppData;
      spyOn(component, 'checkIfSelected').and.callFake((data, option) => {
        return data.info.reviewFlags[option] || false;
      });
      const fb = TestBed.inject(UntypedFormBuilder);
      const formGroup = fb.group({});
      spyOn(formGroup, 'addControl').and.callThrough();
      spyOnProperty(component, 'reviewFlagParentFg', 'get').and.returnValue(formGroup);
      component.offerTemplateBaseService.templateForm = new UntypedFormGroup({
        info: component.reviewFlagParentFg
      });

      component.setFormControlForFlags(mockData);

    });
  });
});

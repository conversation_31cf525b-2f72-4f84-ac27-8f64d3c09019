import { Component, Input, OnInit } from "@angular/core";
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { TEMPLATE_CREATE_RULES } from "@appModules/templates/core/offer-template/details/shared/rules/rules";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { OfferTemplateBaseService } from "@appTemplates/services/offer-template-base.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
    selector: "app-review-flags",
    templateUrl: "./review-flags.component.html"
})
export class ReviewFlagsComponent extends UnsubscribeAdapter implements OnInit {
    @Input() isSummary: boolean;
    bpd_rule = TEMPLATE_CREATE_RULES.BPD;
    appData;
    public offerRequest = JSON.stringify(this.bpd_rule.offerRequest);
    constructor(public offerTemplateBaseService: OfferTemplateBaseService,
        public fb: UntypedFormBuilder, public initialDataService: InitialDataService) {
        super();
    }

    ngOnInit(): void {
        this.appData = this.initialDataService.getAppData();
        this.initSubscribe();
    }
    initSubscribe() {
        this.subs.sink = this.offerTemplateBaseService?.templateData$.subscribe(
            (data = {}) => {
                this.setFormControlForFlags(data || null);
            }
        );
    }
    get fields() {
        return JSON.parse(this.offerRequest);
    }
    checkIfSelected(data, option) {
        if (data) {
            const { info: { reviewFlags } } = data;
            if (reviewFlags) {
                return reviewFlags[option] || false;
            }
        }
        return false
    }
    get reviewFlagParentFg() {
        return this.offerTemplateBaseService.templateForm.get('info') as UntypedFormGroup;
    }
    setFormControlForFlags(data) {
        const { appDataOptions } = this.fields["reviewFlags"];
        const flagOptions = this.appData[appDataOptions];
        const reviewFg = this.fb.group({});
        Object.keys(flagOptions).forEach((option: any) => {
            reviewFg.addControl(option, new UntypedFormControl(this.checkIfSelected(data, option)));
        });
        this.reviewFlagParentFg?.setControl("reviewFlags", reviewFg);

    }
}

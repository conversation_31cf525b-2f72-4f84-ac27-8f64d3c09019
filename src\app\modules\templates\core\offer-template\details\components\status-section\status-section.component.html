<div class="section-width">
    <div [ngClass]="isSummary ? 'col-12 pl-0' : 'col-12 segment-style'" app-input-select-component
        [section]="'offerRequest'" [property]="'otStatus'" [summary]="isSummary"></div>
    <div>
        <div class="mt-4" *ngIf="isParkedOrRemoved || isRemoved">
            <div [ngClass]="isSummary ? 'col-12 pl-0' : 'col-12 segment-style'" app-input-select-component
                [section]="'offerRequest'" [property]="'otStatusReason'" [summary]="isSummary"></div>
        </div>
        <div class="mt-4" *ngIf="isParkedOrRemoved || isNeedReview || isRemoved">
            <div [ngClass]="isSummary ? 'col-12 pl-0' : 'col-12 segment-style'" app-input-text-area-component
                [section]="'offerRequest'" [property]="'otStatusReasonComment'" [summary]="isSummary"
                [hideContentLength]="true"></div>
        </div>
        <div class="mt-4" *ngIf="isParkedOrRemoved || isRemoved">
            <div [ngClass]="isSummary ? 'col-12 pl-0' : 'col-12 segment-style'" app-input-date-component
                [section]="'offerRequest'" [property]="'otStatusSetUntil'" [summary]="isSummary"></div>
        </div>
    </div>
</div>
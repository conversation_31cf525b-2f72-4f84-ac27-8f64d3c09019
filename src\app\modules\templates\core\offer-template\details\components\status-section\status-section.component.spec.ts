
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { StatusSectionComponent } from './status-section.component';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { BehaviorSubject } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';
import { TEMPLATE_CREATE_RULES } from '@appModules/templates/core/offer-template/details/shared/rules/rules';
import { UntypedFormGroup, UntypedFormControl } from '@angular/forms';

describe('StatusSectionComponent', () => {
  let component: StatusSectionComponent;
  let fixture: ComponentFixture<StatusSectionComponent>;
  let mockOfferTemplateBaseService: jasmine.SpyObj<OfferTemplateBaseService>;

  beforeEach(() => {
    // Creating a mock service
    mockOfferTemplateBaseService = jasmine.createSpyObj('OfferTemplateBaseService', ['setFormControls', 'templateData$', 'templateForm']);
    
    // Mock templateData$ as a BehaviorSubject
    mockOfferTemplateBaseService.templateData$ = new BehaviorSubject({}); // mock initial value as empty object
    mockOfferTemplateBaseService.templateForm = new UntypedFormGroup({
      info: new UntypedFormGroup({
        otStatus: new UntypedFormControl('ACTIVE'),
        otStatusReasonComment: new UntypedFormControl(null),
        otStatusReason: new UntypedFormControl(null),
        otStatusSetUntil: new UntypedFormControl(null),
        reviewFlags: new UntypedFormControl(null),
      }),
    });

    TestBed.configureTestingModule({
      declarations: [StatusSectionComponent],
      providers: [
        { provide: OfferTemplateBaseService, useValue: mockOfferTemplateBaseService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(StatusSectionComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set form controls when templateData$ emits', () => {
    spyOn(component, 'createFormControls'); // Spy on createFormControls method

    component.ngOnInit();
    fixture.detectChanges();

    // Simulate the subscription to templateData$
    mockOfferTemplateBaseService.templateData$.next({}); // Trigger a value change to test the behavior
    fixture.detectChanges();

    expect(component.createFormControls).toHaveBeenCalled();
  });

  it('should handle otStatus changes correctly', () => {
    component.ngOnInit();
    fixture.detectChanges();

    // Simulate a status change to REVIEW
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('REVIEW');
    fixture.detectChanges();
    
    expect(component.isNeedReview).toBeTrue();
    expect(component.isParkedOrRemoved).toBeFalse();
    expect(component.isRemoved).toBeFalse();

    // Simulate a status change to ACTIVE
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('ACTIVE');
    fixture.detectChanges();

    expect(component.isNeedReview).toBeFalse();
    expect(component.isParkedOrRemoved).toBeFalse();
    expect(component.isRemoved).toBeFalse();
  });

  it('should set unclear flag controls to null when status is ACTIVE', () => {
    const infoGroup = mockOfferTemplateBaseService.templateForm.get('info') as UntypedFormGroup;
    spyOn(infoGroup, 'setControl');
  
    component.ngOnInit(); // set up valueChanges subscription
  
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue(CONSTANTS.ACTIVE);
  
    expect(infoGroup.setControl).toHaveBeenCalledWith(
      'reviewFlags',
      jasmine.any(UntypedFormControl)
    );
  });
  

  it('should reset status reason fields when status is not PARKED or REMOVED', () => {
    const commentControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReasonComment');
    const reasonControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReason');
    const untilControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusSetUntil');
  
    spyOn(commentControl, 'setValue');
    spyOn(reasonControl, 'setValue');
    spyOn(untilControl, 'setValue');
  
    component.ngOnInit(); // trigger subscriptions
  
    // emit value after ngOnInit
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('ACTIVE');
  
    expect(commentControl.setValue).toHaveBeenCalledWith(null);
    expect(reasonControl.setValue).toHaveBeenCalledWith(null);
    expect(untilControl.setValue).toHaveBeenCalledWith(null);
  });
  

  it('should call unclearFlagControlsIfNotReview when status is changed from REVIEW', () => {
    spyOn(component, 'unclearFlagControlsIfNotReview');
    
    component.ngOnInit();
    fixture.detectChanges();
    
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('ACTIVE');
    fixture.detectChanges();
    
    expect(component.unclearFlagControlsIfNotReview).toHaveBeenCalledWith('ACTIVE');
  });

  it('should parse the offerRequest and return correct fields', () => {
    const fields = component.fields;
    const parsedOfferRequest = JSON.parse(component.offerRequest);

    expect(fields).toEqual(parsedOfferRequest);
  });

  it('should reset status reason fields when status is neither PARKED nor REMOVED', () => {
    component.ngOnInit();
    fixture.detectChanges();
  
    const commentControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReasonComment');
    const reasonControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReason');
    const untilControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusSetUntil');
  
    spyOn(commentControl, 'setValue');
    spyOn(reasonControl, 'setValue');
    spyOn(untilControl, 'setValue');
  
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('ACTIVE');
    fixture.detectChanges();
  
    expect(commentControl.setValue).toHaveBeenCalledWith(null);
    expect(reasonControl.setValue).toHaveBeenCalledWith(null);
    expect(untilControl.setValue).toHaveBeenCalledWith(null);
  });
  
  // it('should not reset status reason fields when status is PARKED', () => {
  //   const commentControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReasonComment');
  //   const reasonControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReason');
  //   const untilControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusSetUntil');
  
  //   spyOn(commentControl, 'setValue');
  //   spyOn(reasonControl, 'setValue');
  //   spyOn(untilControl, 'setValue');
  
  //   component.ngOnInit(); // trigger subscriptions
  
  //   mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('PARKED');
  
  //   expect(commentControl.setValue).not.toHaveBeenCalled();
  //   expect(reasonControl.setValue).not.toHaveBeenCalled();
  //   expect(untilControl.setValue).not.toHaveBeenCalled();
  // });
  
  
  it('should call unclearFlagControlsIfNotReview and set reviewFlags to null for ACTIVE', () => {
    component.ngOnInit();
    fixture.detectChanges();
  
    const reviewFlagGroup = mockOfferTemplateBaseService.templateForm.get('info') as UntypedFormGroup;
    spyOn(reviewFlagGroup, 'setControl');
  
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue(CONSTANTS.ACTIVE);
    fixture.detectChanges();
  
    expect(reviewFlagGroup.setControl).toHaveBeenCalledWith('reviewFlags', jasmine.any(UntypedFormControl));
  });
  
  it('should call unclearFlagControlsIfNotReview and set reviewFlags to null for NEW', () => {
    component.ngOnInit();
    fixture.detectChanges();
  
    const reviewFlagGroup = mockOfferTemplateBaseService.templateForm.get('info') as UntypedFormGroup;
    spyOn(reviewFlagGroup, 'setControl');
  
    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue(CONSTANTS.NEW);
    fixture.detectChanges();
  
    expect(reviewFlagGroup.setControl).toHaveBeenCalledWith('reviewFlags', jasmine.any(UntypedFormControl));
  });
  
  it('should not reset status reason fields when status is REMOVED', () => {
    component.ngOnInit();
    fixture.detectChanges();

    const commentControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReasonComment');
    const reasonControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusReason');
    const untilControl = mockOfferTemplateBaseService.templateForm.get('info.otStatusSetUntil');

    spyOn(commentControl, 'setValue');
    spyOn(reasonControl, 'setValue');
    spyOn(untilControl, 'setValue');

    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('REMOVED');
    fixture.detectChanges();
  });

  it('should correctly handle when isRemoved is false', () => {
    component.ngOnInit();
    fixture.detectChanges();

    mockOfferTemplateBaseService.templateForm.get('info.otStatus')?.setValue('ACTIVE');
    fixture.detectChanges();

    expect(component.isRemoved).toBeFalse();
    expect(component.isParkedOrRemoved).toBeFalse();
    expect(component.isNeedReview).toBeFalse();
  });
});

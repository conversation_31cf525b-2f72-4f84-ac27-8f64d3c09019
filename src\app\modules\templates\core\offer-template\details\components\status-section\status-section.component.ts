import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { TEMPLATE_CREATE_RULES } from '@appModules/templates/core/offer-template/details/shared/rules/rules';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';

@Component({
  selector: 'app-status-section',
  templateUrl: './status-section.component.html',
  styleUrls: ['./status-section.component.scss']
})
export class StatusSectionComponent extends UnsubscribeAdapter implements OnInit {

  @Input() isSummary = false;
  bpd_rule = TEMPLATE_CREATE_RULES.BPD;
  programCode = this.bpd_rule.programCode;
  public offerRequest = JSON.stringify(this.bpd_rule.offerRequest);

  isParkedOrRemoved = false;
  isNeedReview = false;
  isRemoved = false;

  constructor(public offerTemplateBaseService: OfferTemplateBaseService) {
        super();
  }

  get fields() {
    return JSON.parse(this.offerRequest);
  }

  ngOnInit(): void {
    this.initSubscribe();
  }
  initSubscribe() {
    this.subs.sink = this.offerTemplateBaseService?.templateData$?.subscribe(
      (data = {}) => {
        this.createFormControls(data || null);
      }
    );

    this.subs.sink = this.offerTemplateBaseService.templateForm.get('info.otStatus')?.valueChanges?.subscribe(
      (statusVal) => {
        this.isParkedOrRemoved = ['PARKED'].includes(statusVal);
        this.isRemoved = ['REMOVED'].includes(statusVal);
        this.isNeedReview = statusVal === 'REVIEW';
        this.unclearFlagControlsIfNotReview(statusVal);
        if (!this.isParkedOrRemoved || !this.isRemoved) {
          this.offerTemplateBaseService.templateForm.get('info.otStatusReasonComment').setValue(null);
          this.offerTemplateBaseService.templateForm.get('info.otStatusReason').setValue(null);
          this.offerTemplateBaseService.templateForm.get('info.otStatusSetUntil').setValue(null);
        }
      }
    );
  }
  /**
   * 
   * @param status status value like - REVIEW, ACTIVE
   * 
   * When user changed the status from REVIEW to any other status, then the reviewFlags control value should be null
   */
  unclearFlagControlsIfNotReview(status) {
    if([CONSTANTS.ACTIVE, CONSTANTS.NEW].includes(status)) {
      const reviewFlagParent = this.offerTemplateBaseService.templateForm.get("info") as UntypedFormGroup;
      reviewFlagParent.setControl("reviewFlags", new UntypedFormControl(null));
    }
  }
  createFormControls(data) {
    this.offerTemplateBaseService.setFormControls(data, this.offerRequest, this.offerTemplateBaseService.templateForm); 
  }

}

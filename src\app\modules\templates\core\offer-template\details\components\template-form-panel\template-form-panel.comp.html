<ng-container *ngIf="!isSummary else summaryTmpl">
    <ng-sidebar-container style="max-height: 90vh;">
        <ng-sidebar [(opened)]="_opened" [position]="_POSITIONS[1]">
            <div
                [class]="this.toggleNotification || this.toggleEditNotification || this.toggleUpdateNotification ? 'top-113 close-button button-alignment':'top-63 close-button button-alignment'">
                <button class="slider-button-layout btn" (click)="_toggleOpened()">
                    <img src="assets/icons/arrow-right-white-icon.svg" height="24" width="24" alt="" />
                </button>
            </div>
            <app-preview-comments-history [offerProgramCode]="selectedProgramCode" [templateId]="templateId"></app-preview-comments-history>

        </ng-sidebar>
        <div ng-sidebar-content style="overflow: hidden;">
            <ng-container *ngTemplateOutlet="summaryTmpl"></ng-container>
        </div>
    </ng-sidebar-container>
</ng-container>
<ng-template #summaryTmpl>
    <div class="container-fluid request-component p-0">
        <div class="col">
            <div class="row">
                <div class="col error-block">
                    <api-errors class="row no-gutters" *ngIf="!hideApiError"></api-errors>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <div class="row">
                        <div class="side-bar col-12 p-0 col-sm-auto">
                            <div class="request-heading-new" style="padding-bottom: 12px;padding-top: 2px;">
                                <span class="request-heading-new">Template</span>
                            </div>
                            <nav class="menu d-flex d-sm-block justify-content-center flex-wrap">
                                <a *ngIf="templateId" class="nav-item nav-link menu-item-left-nav-menu"
                                    [ngClass]="isSummary ? 'active' : ''" (click)="openSummary(templateId)">
                                    <span class="acronym">Summary</span><span class="title">Summary</span>
                                </a>
                                <a class="nav-item nav-link menu-item-left-nav-menu"
                                    [ngClass]="isSummary ? 'disabled' : 'active'" (click)="toggleTabs('or')">
                                    <span class="acronym">OR</span><span class="title">Offer Template</span>
                                </a>
                            </nav>
                            <request-statuses>
                            </request-statuses>
                            <div class="mt-3 p-2">
                                <app-status-section [isSummary]="isSummary">
                                </app-status-section>
                            </div>
                            <div *ngIf="showReviewFlags" class="p-2">
                                <app-review-flags [isSummary]="isSummary"></app-review-flags>
                            </div>
                        </div>

                        <div class="main col pb-10">
                            <div class="row">
                                <!-- Side Bar -->

                                <div [ngClass]="!isSummary ? 'col-xl-11 col-12' : 'col-lg-8 col-md-auto'">
                                    <div class="row no-gutters">
                                        <div class="col my-3 align-buttons">
                                            <ng-container>
                                                <span class="pl-xl-0 pl-md-3 pl-sm-0">
                                                    <span>MOB:</span>
                                                    <span *ngIf="mobId;else other" class="font-weight-bold px-1">
                                                        {{mobId}}
                                                    </span>
                                                    <ng-template #other> <img
                                                            src="assets/icons/dash-icon.svg"
                                                            class="ml-2 mr-2" alt="close"></ng-template>
                                                </span>
                                            </ng-container>
                                            <span class="pl-xl-0 pl-md-3 pl-sm-0" style="display: inline-flex;">
                                                <span>Template:</span>
                                                <span class="font-weight-bold pl-2">{{ tempId }}</span>
                                            </span>

                                            <span class="pl-xl-5 pl-md-3 pl-sm-0" style="display: inline-flex;">
                                                <span>Last Period Created:</span>
                                                <span class="font-weight-bold pl-2">{{ lastPeriodCreated }}</span>
                                            </span>
                                        </div>

                                        <div class="row no-gutters justify-content-end mb-3">
                                            <ng-container *ngIf="templateData">
                                                <app-detail-actions [templateData]="templateData"></app-detail-actions>
                                            </ng-container>



                                            <div class="col ml-3">
                                                <button (click)="onActionClick()"
                                                    [disabled] = "actionLabel == 'Save' ? isTemplateFormPristine : false"
                                                    class="edit-button-layout btn btn-primary"
                                                    [ngClass]="securedCssByUserPermissions">
                                                    {{this.actionLabel}}
                                                </button>
                                            </div>

                                        </div>

                                    </div>
                                    <div class="row">
                                        <div class="col">
                                            <form [ngClass]="securedCssByUserPermissions">
                                                <div *ngIf="toggleBoolean">

                                                    <app-load-dynamic #loadDynamic [isPod] ="false"></app-load-dynamic>

                                                </div>
                                            </form>

                                            <div class="row">
                                                <div class="col d-flex mt-3 mb-3 justify-content-end">
                                                    <div class="row no-gutters">
                                                        <ng-container *ngIf="templateData">
                                                            <app-detail-actions [templateData]="templateData">
                                                            </app-detail-actions>
                                                        </ng-container>
                                                        <div class="col ml-3">
                                                            <button (click)="onActionClick()"
                                                                [disabled] = "actionLabel == 'Save' ? isTemplateFormPristine : false"
                                                                class="edit-button-layout btn btn-primary"
                                                                [ngClass]="securedCssByUserPermissions">
                                                                {{this.actionLabel}}
                                                            </button>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="isSummary" class="col-lg-4 pr-30">
                                    <app-preview-comments-history [templateId]="templateId" [offerProgramCode]="selectedProgramCode">
                                    </app-preview-comments-history>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="!isSummary && !_opened" class="col-1 col-sm-auto">
                            <button class="slider-button-layout btn" (click)="_toggleOpened()">
                                <img src="assets/icons/arrow-left-white-icon.svg" height="24" width="24"
                                    alt="" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">

                <main class="main col">

                </main>
                <div class="col-1 col-sm-auto">
                    <div class="col" style="width: 44px;"></div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #showRevertEdit>
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="warning-text">Do you want to revert your changes?</div>
        <div class="warning-text">
            Your changes will be lost if you don't save them.
        </div>
    </div>

    <div class="modal-footer">

        <button type="button" class="btn btn-primary save-btn" (click)="revertConfirm()">
            Revert Edit
        </button>
    </div>
</ng-template>

<ng-template #selectionTemplate>
    <div class="actions-popover">
        <app-mob [mobPopup]="mobPopup" [mobID]="mobId" (showMobID)="getMobId($event)"></app-mob>
    </div>
</ng-template>

<ng-template #showSaveConfirm>
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="warning-text">Do you want to save your changes?</div>
        <div class="warning-text">
            Your changes will be lost if you don't save them.
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-default dont-save-btn" (click)="dontSave()">
            Don't Save
        </button>
        <button type="button" class="btn btn-primary save-btn" (click)="saveConfirm()">
            Save
        </button>
    </div>
</ng-template>
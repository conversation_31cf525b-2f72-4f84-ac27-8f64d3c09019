@import "scss/theme-builder.scss";
:host ::ng-deep {
    @import "scss/_mixins";
    @import "scss/variables";
    @import "scss/_colors";
    @import "scss/cbSwitch.scss";
    @import "scss/tagSelect.scss";
    //@import "../../../../../node_modules/albertsons-theme-builder/dist/assets/styles/themes/bootstrap-theme-blue.css";
  
    aside {
      border: solid 1px #cdd2d6;
      box-shadow: 2px 2px 11px 4px #cdd2d6;
      background-color: #fff;
      padding: 1em 1em;
      width: 440px !important;
      z-index: 13 !important;
    }
    .text-underline{
      text-decoration: underline !important;
    }
    .top-113 {
      top: 137px;
    }
    .top-63 {
      top: 87px;
    }
    .close-button {
      right: 450px;
      position: fixed;
      z-index: 2;
    }
    .fw-b{
      font-weight: bold;
    }
    .request-component {
      font-size: $base-font-size;
    }
    .sideBarWrap{
      max-width: 11%;
    }
    .request-heading-new {
     
      font-size: $header-font-size;
      
    }
    .offer-request-container {
      border: 1px solid #cdd2d6;
      box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
    }
    .border-danger.input-group {
      border: 1px solid #cf202f !important;
      input,
      .input-group-append {
        border: 0 !important;
      }
    }
  
    
  
    .fields-container {
      margin-bottom: 20px;
    }
  
    .background-header {
      background-color: #cdd2d6;
      color: black;
      font-weight: bold;
    }
  
    /* Side Bar */
    .side-bar {
      background: $theme-white;
      min-width: 10%;
      min-height: 100vh;
  
    }
  
  
  
    /* type ahead*/
    .typeahead-no-results {
      color: #dc3545;
    }
    /** cancel link */
    .request-cancel {
      text-decoration: underline !important;
      margin-right: 50px !important;
    }
  
  
  
  
  
    .exit-button {
      font-size: 30px;
      color: $black-primary-hex;
      z-index: 1000;
    }
  
    .side-bar .menu a .title {
      margin-right: 0;
    }
  
    .side-bar .menu a .acronym {
      display: none;
    }
  
    .user-dropdown {
      min-width: 240px;
      height: 26px;
    }
  
    .select-text {
      font-size: 12px;
      margin-top: -5px;
    }
  
    .search-input {
      font-size: 13px;
    }
  
    label.search-label {
      font-size: 13px;
      margin-bottom: -5px;
    }
  
    .cancel-btn {
      cursor: pointer;
      font-size: 13px;
    }
    .request-heading {
      font-size: 25px;
    }
  
    .green-status {
      width: 80px;
      text-align: center;
      border: 1px solid $green-primary-hex;
      color: $green-primary-hex;
      font-weight: 800;
    }
    .yellow-status {
      width: 80px;
      text-align: center;
      border: 1px solid #e79023;
      color: #e79023;
      font-weight: 800;
    }
    .purple-status {
      width: 80px;
      text-align: center;
      border: 1px solid #841fa9;
      color: #841fa9;
      font-weight: 800;
    }
    .blue-status {
      width: 80px;
      text-align: center;
      border: 1px solid #59b1e3;
      color: #59b1e3;
      font-weight: 800;
    }
    .red-status {
      width: 80px;
      text-align: center;
      border: 1px solid $red-primary-rgb;
      color: $red-primary-rgb;
      font-weight: 800;
    }
    
    /* Media queries */
  
    @media screen and (max-width: 1400px) and (min-width: 1200px) {
      .error-block {
        max-width: 76%;margin: 0px 0px 0px 142px;
      }
    }
    @media screen and (max-width: 1200px) and (min-width: 991px) {
      .error-block {
        max-width: 77%;margin: 0px 0px 0px 142px;
      }
    }
    @media screen and (max-width: 990px) {
      .request-heading {
        font-size: 20px;
      }
      .error-block {
        max-width: 77%;margin: 0px 0px 0px 142px;
      }
    }
  
    @media screen and (max-width: 738px) {
      .side-bar {
        min-height: auto;
        position: relative;
        width: 100%;
      }
      .menu {
        display: flex !important;
      }
      .error-block {
        max-width: 82%;
        margin: 0px 0px 0px 111px;
      }
    }
    @media screen and (max-width: 546px) {
      .side-bar {
        min-height: auto;
        position: relative;
        width: 100%;
      }
      .menu {
        display: flex !important;
      }
      .error-block {
        max-width: 78%;margin: 0px 0px 0px 82px;
      }
    }
  
    /*drop down*/
    .dropdown-item:hover {
      background: $grey-lighter-hex;
      color: $grey-darkest-hex !important;
    }
  
    .dropdown-menu-left:before {
      position: absolute;
      top: -6px;
      right: 9px;
      display: inline-block;
      border-right: 7px solid transparent;
      border-bottom: 7px solid #ccc;
      border-left: 7px solid transparent;
      border-bottom-color: rgba(0, 0, 0, 0.2);
      content: "";
    }
  
    .dropdown-menu-left:after {
      position: absolute;
      top: -5px;
      right: 10px;
      display: inline-block;
      border-right: 6px solid transparent;
      border-bottom: 6px solid #ffffff;
      border-left: 6px solid transparent;
      content: "";
    }
  
    .dropdown-menu-right:before {
      position: absolute;
      top: -6px;
      left: 9px;
      display: inline-block;
      border-right: 7px solid transparent;
      border-bottom: 7px solid #ccc;
      border-left: 7px solid transparent;
      border-bottom-color: rgba(0, 0, 0, 0.2);
      content: "";
    }
  
    .dropdown-menu-right:after {
      position: absolute;
      top: -5px;
      left: 10px;
      display: inline-block;
      border-right: 6px solid transparent;
      border-bottom: 6px solid #ffffff;
      border-left: 6px solid transparent;
      content: "";
    }
    .align-info {
      padding: 55px 0px 0px 0px !important;
    }
    .created-by-info {
      padding: 0px 0px 0px 18px;
    }
    .align-action-buttons {
      padding: 10px 190px 0px 0px;
    }
    .slider-button-layout {
      width: 44px;
      padding-left: 5px;
      padding-right: 5px;
      text-align: center;
      background-color: $theme-primary;
      border-color: $theme-primary;
      // background: url("../../../../assets/icons/caret-blue-icon-right.svg")
      //   no-repeat;
  
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
    }
    .border-right-radius {
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
    }
  
    .edit-button-layout {
    
      font-size: $button-font-size;
      font-weight: 800;
      letter-spacing: 1.2px;
      line-height: 22px;
      text-align: center;
      min-width: 120px;
    }
    .edit-action-button {
      padding: 0px 0px 0px 40px;
    }
    .pb-10 {
      padding-bottom: 10px;
    }
    .mobContainer{
      left: 155px !important;
      width: 435px !important;
      max-width: 440px !important;
      .popover-arrow{
          left: 50px !important;
      }
      .popover-body{
        padding: 25px;
      }
  }
  }
  @import "scss/_mixins";
  @import "scss/variables";
  @import "scss/_colors";
  .button-save{
    height: 1%;
  }
  .dont-save-btn{
    color: $theme-primary;
    font-weight: bold;
    border: 2px solid $theme-primary;
    width: 150px;
  }
  .save-btn{
    width: 150px;
    font-weight: bold;
  }
  .warning-text{
    text-align: center;
    font-weight: 600;
  }
  .side-bar .menu a.active {
    background: $blue-primary-hex;
    color: $grey-lightest-hex;
    text-decoration: none;
    font-weight: 700;
    border: 0;
    &.is-tab-invalid {
      border: 1px solid $theme-primary;
      color: #ffffff;
      background: $theme-primary;
    }
  }
  .align-buttons {
    padding: 6px 0px 0px 0px;
  }
  .button-alignment {
    margin: 0px 2px 0px 0px;
  }
  .ng-sidebar--right {
    margin: 0px !important;
  }
  @import "scss/sidebar";
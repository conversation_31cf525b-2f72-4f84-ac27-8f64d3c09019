import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { TemplateRequestBaseService } from '@appTemplates/services/template-request-base/template-request-base.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { BehaviorSubject, of } from 'rxjs';
import { TemplateFormPanel } from './template-form.panel.comp';

describe('TemplateFormPanel', () => {
  let component: TemplateFormPanel;
  let fixture: ComponentFixture<TemplateFormPanel>;

  beforeEach(() => {
    const bsModalServiceStub = () => ({ show: (template, options) => ({}) });
    const activatedRouteStub = () => ({ snapshot: { params: {templateId: "1233"} } });
    const routerStub = () => ({ navigateByUrl: newUrl => ({}) });
    const requestFormServiceStub = () => ({
      requestForm: new UntypedFormGroup({}),
      isReqSubmitAttempted$: {},
      isDraftSaveAttempted: {}
    });
    const queryGeneratorStub = () => ({
      setQuery: string => ({}),
      pushParameters: object => ({}),
      getQuery: () => ({})
    });
    const authServiceStub = () => ({ onUserDataAvailable: arg => ({}) });
    const generalOfferTypeServiceStub = () => ({
      isReqSubmitAttempted$: {},
      isDraftSaveAttempted: {},
      initOfferSubmitSubscriber: () => ({}),
      cloneOfferRequestData: {}
    });
    const searchOfferRequestServiceStub = () => ({
      searchOfferRequest: (arg, arg2) => ({ subscribe: f => f({}) })
    });
    const offerTemplateBaseServiceStub = () => ({
      isRedirectRoute$: { subscribe: f => f({}), next: () => ({}) },
      onRouteChange$: { next: () => ({}), subscribe: f => f({}) },
      isDisplayNavigationWarning: {},
      initializeRequestForm: () => ({}),
      requestFormService$: {
        hideApiErrorOnRequestMain: () => ({ subscribe: f => f({}) })
      },
      navigateToTemplateSummary: templateId => ({}),
      templateData$: { next: () => ({}) },
      templateForm: new UntypedFormGroup({}),
      isTemplateSavedFromModal$: { pipe: () => ({ subscribe: f => f({}) }) },
      saveOT: actionLabel => ({})
    });
    const facetItemServiceStub = () => ({ templateProgramCodeSelected: {} });
    const permissionsServiceStub = () => ({ getPermissions: () => ({}) });
    const templateRequestBaseServiceStub = () => ({
      isSavedFromNavigationOverlay_OT: {}
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [TemplateFormPanel],
      providers: [
        { provide: BsModalService, useFactory: bsModalServiceStub },
        { provide: ActivatedRoute, useFactory: activatedRouteStub },
        { provide: Router, useFactory: routerStub },
        { provide: RequestFormService, useFactory: requestFormServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: AuthService, useFactory: authServiceStub },
        {
          provide: GeneralOfferTypeService,
          useFactory: generalOfferTypeServiceStub
        },
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub
        },
        {
          provide: OfferTemplateBaseService,
          useFactory: offerTemplateBaseServiceStub
        },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: PermissionsService, useFactory: permissionsServiceStub },
        {
          provide: TemplateRequestBaseService,
          useFactory: templateRequestBaseServiceStub
        }
      ]
    });
    fixture = TestBed.createComponent(TemplateFormPanel);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  // it(`routeUrl has default value`, () => {
  //   expect(component.routeUrl).toEqual(router.url);
  // });

  it(`toggleBoolean has default value`, () => {
    expect(component.toggleBoolean).toEqual(true);
  });

  it(`hideApiError has default value`, () => {
    expect(component.hideApiError).toEqual(false);
  });

  it(`isCreateFlow has default value`, () => {
    expect(component.isCreateFlow).toEqual(true);
  });

  it(`CONSTANTS has default value`, () => {
    expect(component.CONSTANTS).toEqual(CONSTANTS);
  });

  it(`navigatingAway has default value`, () => {
    expect(component.navigatingAway).toEqual(false);
  });

  it(`redirectingAway has default value`, () => {
    expect(component.redirectingAway).toEqual(false);
  });

  it(`_opened has default value`, () => {
    expect(component._opened).toEqual(false);
  });

  it(`_POSITIONS has default value`, () => {
    expect(component._POSITIONS).toEqual([`left`, `right`, `top`, `bottom`]);
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      const generalOfferTypeServiceStub: GeneralOfferTypeService = fixture.debugElement.injector.get(
        GeneralOfferTypeService
      );
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      spyOn(component, 'getTemplateDataIfEdit').and.callThrough();
      spyOn(component, 'initSubsribes').and.callThrough();
      spyOn(component, 'secureButtonsByUserPermissions').and.callThrough();
      spyOn(
        generalOfferTypeServiceStub,
        'initOfferSubmitSubscriber'
      ).and.callThrough();
      spyOn(
        offerTemplateBaseServiceStub,
        'initializeRequestForm'
      ).and.callThrough();
      component.ngOnInit();
      expect(component.getTemplateDataIfEdit).toHaveBeenCalled();
      expect(component.initSubsribes).toHaveBeenCalled();
      expect(component.secureButtonsByUserPermissions).toHaveBeenCalled();
      expect(
        generalOfferTypeServiceStub.initOfferSubmitSubscriber
      ).toHaveBeenCalled();
      expect(
        offerTemplateBaseServiceStub.initializeRequestForm
      ).toHaveBeenCalled();
    });
  });

  describe('initSubsribes', () => {
    it('makes expected calls', () => {
      spyOn(component, 'onNavigate').and.callThrough();
      component.initSubsribes();
      expect(component.onNavigate).toHaveBeenCalled();
    });
  });

  describe('getTemplateDataIfEdit', () => {
    it('makes expected calls', () => {
      const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(
        QueryGenerator
      );
      const authServiceStub: AuthService = fixture.debugElement.injector.get(
        AuthService
      );
      spyOn(queryGeneratorStub, 'setQuery').and.callThrough();
      spyOn(queryGeneratorStub, 'pushParameters').and.callThrough();
      spyOn(authServiceStub, 'onUserDataAvailable').and.callThrough();
      spyOn(component, "fetchTemplateData");
      component.getTemplateDataIfEdit();
      expect(queryGeneratorStub.setQuery).toHaveBeenCalled();
      expect(queryGeneratorStub.pushParameters).toHaveBeenCalled();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });

  describe('fetchTemplateData', () => {
    it('makes expected calls', () => {
      const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(
        QueryGenerator
      );
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      spyOn(queryGeneratorStub, 'getQuery').and.callThrough();
      spyOn( searchOfferRequestServiceStub, 'searchOfferRequest').and.returnValue(of({offerRequests: [{info: {
        mobId: "12",lastPeriodCreated: "", id: "cwj"

      }, rules: {
        qualificationAndBenefit: {OfferTemplateOffers: []}
      },
      createdUser: {
        firstName: "",
        lastName: ""
      }
    }]}))
      component.fetchTemplateData();
      expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
      expect(
        searchOfferRequestServiceStub.searchOfferRequest
      ).toHaveBeenCalled();
    });
  });

  describe('onActionClick', () => {
    it('makes expected calls', () => {
      component.actionLabel = "test";
      spyOn(component, 'handleSaveORSubmit').and.callThrough();
      component.onActionClick();
      expect(component.handleSaveORSubmit).toHaveBeenCalled();
    });
    it('makes expected calls if action label edit', () => {
      component.actionLabel = "Edit";
      spyOn(component, 'handleSummaryScenario').and.callThrough();
      component.onActionClick();
      expect(component.handleSummaryScenario).toHaveBeenCalled();
    });
  });

  describe('onNavigate', () => {
    it('makes expected calls', () => {
      spyOn(component, 'saveModal').and.callThrough();
      component.onNavigate();
      expect(component.saveModal).toHaveBeenCalled();
    });
  });

  describe('isFormDirty', () => {
    it('makes expected calls', () => {
      spyOn(component, 'getTemplateForm').and.callThrough();
      component.isFormDirty();
      expect(component.getTemplateForm).toHaveBeenCalled();
    });
  });

  describe('postTemplateSaved', () => {
    it('makes expected calls', () => {
      spyOn(component, 'getTemplateForm').and.callThrough();
      component.postTemplateSaved();
      expect(component.getTemplateForm).toHaveBeenCalled();
    });
  });

  describe('saveConfirm', () => {
    it('makes expected calls', () => {
      spyOn(component, 'closeModal').and.callThrough();
      spyOn(component, 'handleSaveORSubmit').and.callThrough();
      spyOn(component, 'postTemplateSaved').and.callThrough();
      component.saveConfirm();
      expect(component.closeModal).toHaveBeenCalled();
      expect(component.handleSaveORSubmit).toHaveBeenCalled();
      expect(component.postTemplateSaved).toHaveBeenCalled();
    });
  });

  describe('postFormResetOnDontSave', () => {
    it('makes expected calls', () => {
      spyOn(component, 'getTemplateForm').and.callThrough();
      component.postFormResetOnDontSave();
      expect(component.getTemplateForm).toHaveBeenCalled();
    });
  });
  describe('ngOnDestroy', () => {
    it('makes expected calls', () => {
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      const reqFormServiceStub: RequestFormService = fixture.debugElement.injector.get(
        RequestFormService
      );
      reqFormServiceStub.requestForm = new UntypedFormGroup({})
      offerTemplateBaseServiceStub.templateData$ = new BehaviorSubject("");
      component.ngOnDestroy();
    });
  });

  describe('dontSave', () => {
    it('makes expected calls', () => {
      spyOn(component, 'closeModal').and.callThrough();
      spyOn(component, 'postFormResetOnDontSave').and.callThrough();
      component.dontSave();
      expect(component.closeModal).toHaveBeenCalled();
      expect(component.postFormResetOnDontSave).toHaveBeenCalled();
    });
  });

  describe('handleSummaryScenario', () => {
    it('makes expected calls', () => {
      component.templateId = "1232"
      const routerStub: Router = fixture.debugElement.injector.get(Router);
      spyOn(routerStub, 'navigateByUrl').and.callThrough();
      component.handleSummaryScenario();
      expect(routerStub.navigateByUrl).toHaveBeenCalled();
    });
  });

  describe('handleSaveORSubmit', () => {
    it('makes expected calls', () => {
      const offerTemplateBaseServiceStub: OfferTemplateBaseService = fixture.debugElement.injector.get(
        OfferTemplateBaseService
      );
      spyOn(offerTemplateBaseServiceStub, 'saveOT').and.callThrough();
      component.handleSaveORSubmit();
      expect(offerTemplateBaseServiceStub.saveOT).toHaveBeenCalled();
    });
  });
});

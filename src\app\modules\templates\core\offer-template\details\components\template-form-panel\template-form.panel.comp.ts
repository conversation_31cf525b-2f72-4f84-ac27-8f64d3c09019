
import { Component, HostListener, Input, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { TemplateRequestBaseService } from '@appTemplates/services/template-request-base/template-request-base.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { PopoverDirective } from 'ngx-bootstrap/popover';
import { first } from 'rxjs/operators';

@Component({
  selector: "template-form-panel",
  templateUrl: './template-form-panel.comp.html',
  styleUrls: ['./template-form-panel.comp.scss'],
})
export class TemplateFormPanel extends UnsubscribeAdapter {
  routeUrl = this.router.url;
  toggleBoolean = true;
  hideApiError: boolean = false;
  isCreateFlow = true;
  isSummary: boolean = false;
  lastPeriodCreated: any;
  createdUserId: any;
  createdUser: any;
  templateData: any;
  actionLabel;
  // showSavemodalRef: BsModalRef
  securedCssByUserPermissions;
  @ViewChild('showSaveConfirm')
  private _saveConfirmationModalRef: TemplateRef<any>;

  @ViewChild("mobPopup")
  private mobPopup: PopoverDirective;
  

  @Input() mobID;
  @ViewChild('loadDynamic') loadDynamicComponent;
  objEdit: any = {};
  tempId: string;
  modalRef: BsModalRef;
  isEditRevert: boolean;
  CONSTANTS = CONSTANTS;
  mobId: any;
  selectedProgramCode: any;
  templateId: string;
  navigatingAway = false;
  redirectingAway = false;

  async canDeactivate() {
    let isActivate;
    isActivate = await new Promise((resolve) => {
      this.subs.sink = this.offerTemplateBaseService.isRedirectRoute$.subscribe((data) => {
        resolve(data);
      });
      this.offerTemplateBaseService.onRouteChange$.next(true);
    });
    this.offerTemplateBaseService.isDisplayNavigationWarning = true;
    return isActivate;
  }
  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any) {
    if (!this.canDeactivate()) {
      $event.returnValue = true;
    }
  }

  constructor(
      private router: Router,
      private requestFormService$ : RequestFormService,
      private activatedRoute: ActivatedRoute,
      private _queryGenerator: QueryGenerator,
      private _authService: AuthService,
      private generalOfferTypeService: GeneralOfferTypeService,
      private _searchOfferRequestService: SearchOfferRequestService,
      private offerTemplateBaseService: OfferTemplateBaseService,
      private facetItemService: FacetItemService,
      private _modalService: BsModalService,
      private permissionService: PermissionsService,
      private templateRequestBaseService: TemplateRequestBaseService
  ) {
    super();
    this.isSummary = this.routeUrl?.includes("summary");
    this.actionLabel  = this.isSummary ? "Edit" : "Save";  
  }
  public _opened = false;
  public _POSITIONS: Array<string> = ['left', 'right', 'top', 'bottom'];
  public _toggleOpened(): void {
    this._opened = !this._opened;
  }
  ngOnInit(): void {
    this.requestFormService$.requestForm = new UntypedFormGroup({});
    this.offerTemplateBaseService.initializeRequestForm();
    this.generalOfferTypeService.isReqSubmitAttempted$ = this.requestFormService$.isReqSubmitAttempted$;
    this.generalOfferTypeService.isDraftSaveAttempted = this.requestFormService$.isDraftSaveAttempted;
    this.generalOfferTypeService.initOfferSubmitSubscriber();
    this.getTemplateDataIfEdit();
    this.initSubsribes();
    this.secureButtonsByUserPermissions(this.facetItemService.templateProgramCodeSelected);
    this.selectedProgramCode = this.facetItemService.templateProgramCodeSelected;
  }
  initSubsribes() {
    this.subs.sink = this.offerTemplateBaseService.requestFormService$.hideApiErrorOnRequestMain()
    .subscribe((value: boolean)=> {
        this.hideApiError = value;
    })
    this.subs.sink = this.offerTemplateBaseService.onRouteChange$.subscribe((value) => {
      this.onNavigate();
    });
  }
  getTemplateDataIfEdit() {
    // In the case of Edit scenario, get the offer ID from the route and make API call to fetch data

    const activatedRoute = this.activatedRoute;
    let templateId = activatedRoute && activatedRoute.snapshot.params['templateId'];
    this.templateId = templateId;
    if (templateId) {
      this.isCreateFlow = false;
      let paramsList = [
        {
          remove: false,
          parameter: 'requestId',
          value: templateId
        }
      ];
      this._queryGenerator.setQuery('');
      this._queryGenerator.pushParameters({ paramsList });
      this._authService.onUserDataAvailable(this.fetchTemplateData.bind(this));
    }
  }
  openSummary(templateId) {
  this.offerTemplateBaseService.navigateToTemplateSummary(templateId);
  }
  fetchTemplateData() {
    // In edit case, get the Offer request details
    this.subs.sink = this._searchOfferRequestService
      .searchOfferRequest(this._queryGenerator.getQuery(), false)
      .subscribe((response: any) => {
        // If there are no offers
        if (!response.offerRequests[0]) {
          let message = 'The Request Id might be invalid';
          let status = 'error';
        }
        let data = response.offerRequests[0];
        this.templateData = data;
        let {
          rules: {
            qualificationAndBenefit,
          }
        } = data;

        const offerRequestOffers = qualificationAndBenefit[`OfferTemplateOffers`];
        this.mobId = data.info?.['mobId'];
        this.lastPeriodCreated = data.info?.['lastPeriodCreated'];
        this.tempId = data.info?.['id'];
        this.generalOfferTypeService.cloneOfferRequestData = JSON.stringify(offerRequestOffers);
        this.offerTemplateBaseService.templateData$.next(data);
        this.createdUser = data.createdUser?.firstName + ' ' + data.createdUser?.lastName;
      });
  }
  /**
   * If status selected is REVIEW, then need to show Review flags checkboxes
   */
  get showReviewFlags() {
    const otStatusValue = this.offerTemplateBaseService.getControl('otStatus')?.value;
    return [CONSTANTS.REVIEW, CONSTANTS.PARKED, CONSTANTS.NO_UPCS, CONSTANTS.REMOVED].includes(otStatusValue);
  }
  onActionClick() {
    if(this.actionLabel == "Edit") {
      this.handleSummaryScenario();
    } else {
      this.handleSaveORSubmit();
    }
  }
  getTemplateForm(){
   return this.offerTemplateBaseService.templateForm;
 }
  get isTemplateFormPristine() {
   return  this.getTemplateForm()?.pristine && this.generalOfferTypeService.generalOfferTypeForm && this.generalOfferTypeService.generalOfferTypeForm.pristine;
 }
  toggleTabs(tab) {
   if (tab === 'or') {
     this.toggleBoolean = true;
   } else if (tab === 'pod') {
     this.toggleBoolean = false;
   }
 }

 onNavigate() {
  if (!this.isTemplateFormPristine) {
    if (!this.modalRef) {
      // add in validation to check the form -SJC
      this.saveModal(this._saveConfirmationModalRef, {
        keyboard: true,
        class: 'confirm-centered modal-dialog-centered',
      });
    }
  } else {
    this.offerTemplateBaseService.isRedirectRoute$.next(true);
  }
}

isFormDirty() {
  return this.getTemplateForm().dirty  || this.generalOfferTypeService?.generalInformationForm?.dirty;
}

postTemplateSaved() {
// Once the form is reset with the earlier data, redirect to new route
this.offerTemplateBaseService.isTemplateSavedFromModal$.pipe(first()).subscribe((isSaved) => {
  if (isSaved) {
      this.templateRequestBaseService.isSavedFromNavigationOverlay_OT = true;
      this.getTemplateForm().markAsPristine();
      this.getTemplateForm().reset('');
      this.offerTemplateBaseService.isRedirectRoute$.next(true);
  } else {
      this.offerTemplateBaseService.isRedirectRoute$.next(false);
  }
});
}

saveConfirm() {
  this.closeModal();
  this.handleSaveORSubmit();
  this.postTemplateSaved();
}

postFormResetOnDontSave() {
  this.getTemplateForm().markAsPristine();
  this.offerTemplateBaseService.isRedirectRoute$.next(true);
}

dontSave() {
  this.closeModal();
  // const externalOfferId = this.activatedRoute && this.activatedRoute.firstChild.snapshot.params['externalOfferId'];
  // this.callOfferSearchApi({ externalOfferId });
  this.postFormResetOnDontSave();
}
saveModal(template, options) {
  this.modalRef = this._modalService.show(template, options);
}
closeModal() {
  if (this.modalRef) {
    this.modalRef.hide();
  }
}

  handleSummaryScenario() {
    if(!this.templateId) {
      return false;
    }
    // Defaulting to BPD for now
    let editSrc = ROUTES_CONST.TEMPLATES.BPDEdit;
    const pCode = this.facetItemService.templateProgramCodeSelected;
    if(pCode) {
      editSrc = ROUTES_CONST.TEMPLATES[`${pCode}Edit`]
    }
    const newUrl = `${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${editSrc}/${this.templateId}`;
    this.router.navigateByUrl(newUrl);
  }
  handleSaveORSubmit() {
    this.offerTemplateBaseService.saveOT(this.actionLabel);
  }
  ngOnDestroy() {
     this.offerTemplateBaseService.templateData$.next(null);
     this.offerTemplateBaseService?.templateForm?.reset();
     this.subs?.unsubscribe();
  }
  secureButtonsByUserPermissions(programCode) {
    let buttonAvailableByPermission = false;
    if (programCode) {
      const permissions = this.permissionService.getPermissions();
      if (permissions) {
        if (programCode === CONSTANTS.BPD && permissions[CONSTANTS.Permissions.Admin]) {
          buttonAvailableByPermission = true;
        }
      }
    }
    this.securedCssByUserPermissions = !buttonAvailableByPermission ? "disable" : "";
  }
}
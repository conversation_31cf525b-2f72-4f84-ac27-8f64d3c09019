<section>
    <div>
      <div class="offer-request-container mb-5">
        <div>
          <section>
            <nav class="navbar background-header mb-6" aria-label="Offer Request">
              <span class="fs-18">
                {{ programCode }}
              </span>
            </nav>
            <div class="fields-container">
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-5 pl-0' : 'col-6'"
                  app-input-select-component
                  [section]="'offerRequest'"
                  [property]="'regionId'"
                  [summary]="isSummary"
                  [data]="data"
                ></div>
                <div
                  [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6'"
                  app-input-select-component
                  [section]="'offerRequest'"
                  [property]="'programType'"
                  [summary]="isSummary"
                ></div>
              </div>
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-6 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'brandAndSize'"
                  [columnSize]="isSummary ? 'col-12' : 'col-12'"
                ></div>
                <div
                  [ngClass]="isSummary ? 'col-5 offset-1 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'customerSegment'"
                  [summary]="isSummary"
                ></div>
              </div>
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-5 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [summary]="isSummary"
                  [property]="'usageLimitTypePerUser'"
                  [columnSize]="isSummary ? 'col-12' : 'col-6'"
                ></div>
              </div>
              
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-5 pl-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'bggm'"
                  [summary]="isSummary"
                ></div>
                <div
                  [ngClass]="isSummary ? 'col-5 offset-2 pl-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'bugm'"
                  [summary]="isSummary"
                ></div>
              </div>
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-5 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'categoryId'"
                  [summary]="isSummary"
                ></div>
                <div
                  [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'category'"
                  [summary]="isSummary"
                ></div>
              </div>
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-5 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'cic'"
                  [summary]="isSummary"
                ></div>
                <div
                  [ngClass]="isSummary ? 'col-5 offset-2 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'cpg'"
                  [summary]="isSummary"
                ></div>
              </div>
              <div class="row mx-1 justify-content-left my-3 mb-5">
                <div
                  [ngClass]="isSummary ? 'col-5 px-0' : 'col-6'"
                  app-input-display-component
                  [section]="'offerRequest'"
                  [property]="'repUpc'"
                  [summary]="isSummary"
                ></div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </section>
  
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TemplateSectionComponent } from './template-section.component';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { BehaviorSubject, of } from 'rxjs';
import { FormsModule } from '@angular/forms';

describe('TemplateSectionComponent', () => {
    let component: TemplateSectionComponent;
    let fixture: ComponentFixture<TemplateSectionComponent>;
    let mockOfferTemplateBaseService: jasmine.SpyObj<OfferTemplateBaseService>;

    beforeEach(async () => {
        mockOfferTemplateBaseService = jasmine.createSpyObj('OfferTemplateBaseService', ['setFormControls'], {
            templateData$: of({}),
            templateForm: {}
        });

        await TestBed.configureTestingModule({
            declarations: [TemplateSectionComponent],
            imports: [FormsModule],
            providers: [
                { provide: OfferTemplateBaseService, useValue: mockOfferTemplateBaseService },
                { provide: BsModalRef, useValue: {} }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(TemplateSectionComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize templateForm on ngOnInit', () => {
        component.ngOnInit();
        expect(component.templateForm).toBe(mockOfferTemplateBaseService.templateForm);
    });

    it('should call setFormControls with data on initSubscribe', () => {
        const mockData = { key: 'value' };
        mockOfferTemplateBaseService.templateData$ = new BehaviorSubject(mockData);
        component.initSubscribe();
    });

    it('should unsubscribe on ngOnDestroy', () => {
        spyOn(component.subs, 'unsubscribe');
        component.ngOnDestroy();
        expect(component.subs.unsubscribe).toHaveBeenCalled();
    });

    it('should render template elements', () => {
        component.isSummary = true;
        fixture.detectChanges();
        const compiled = fixture.nativeElement as HTMLElement;
    });
});
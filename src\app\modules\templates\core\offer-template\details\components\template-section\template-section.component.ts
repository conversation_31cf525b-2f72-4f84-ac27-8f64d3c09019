import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { TEMPLATE_CREATE_RULES } from '@appModules/templates/core/offer-template/details/shared/rules/rules';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({​
  selector: 'app-template-section',
  templateUrl: './template-section.component.html'
}​)
export class TemplateSectionComponent extends UnsubscribeAdapter implements OnInit ,OnDestroy{​

  
  modalRef: BsModalRef;
  bpd_rule =  TEMPLATE_CREATE_RULES.BPD;
  programCode =  this.bpd_rule.programCode;
  public offerRequest = JSON.stringify(this.bpd_rule.offerRequest);
  requestForm;
  data: any;
  @Input() isSummary;
  showUsage: boolean;
  edited: boolean;
  created: boolean;
  templateForm: any;
  
  constructor( public offerTemplateBaseService: OfferTemplateBaseService
    ) {​
      super();
     }​
 
  
  ngOnInit(): void {​
   this.templateForm = this.offerTemplateBaseService?.templateForm;
    this.initSubscribe();
  }​
 
 
  initSubscribe(){​
    this.subs.sink = this.offerTemplateBaseService.templateData$?.subscribe((data={​}​) => {​
       if(data){​
         if(!this.edited){​
           this.data = data;
           this.createFormControls(data);
           this.edited = true;
          }​
       }​ else if(!this.created){​
         this.created = true;
         this.createFormControls(null);
       }​
    }​);
  }​
   createFormControls(data){​
    this.offerTemplateBaseService.setFormControls(data, this.offerRequest, this.templateForm);  
   }​
  
   ngOnDestroy(): void {​
    this.subs.unsubscribe();
    this.edited = false;
    this.created = false;
  }​
 }​
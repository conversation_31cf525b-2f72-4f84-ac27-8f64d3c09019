import { NgModule } from "@angular/core";
import { ActivatedRouteSnapshot, RouterModule, Routes } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { CanDeactivateGuard } from "@appServices/common/can-deactivate-guard.service";
import { PermissionsGuard } from '@appShared/albertsons-angular-authorization';
import { TemplateFormContainer } from "@appTemplates/core/offer-template/details/components/template-form-container/template-form-container.component";
import { TemplateFormPanel } from "@appTemplates/core/offer-template/details/components/template-form-panel/template-form.panel.comp";
import { AuthGuard } from "guard/auth.guard";

export function redirectFunc(rejectedPermissionName: string, activateRouteSnapshot: ActivatedRouteSnapshot) {
  // if program code is there and permission is not there for route, then route to summary based on pcode
  if (activateRouteSnapshot.params.templateId) {
    const pCode = activateRouteSnapshot?.data?.selectedProgramCode;
    let summarySrc = ROUTES_CONST.TEMPLATES.BPDSummary;
    if (pCode) {
      summarySrc = pCode == CONSTANTS.BPD ? ROUTES_CONST.REQUEST.Summary : ROUTES_CONST.REQUEST[`${pCode}Summary`];
    }
    return `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${summarySrc}/${activateRouteSnapshot.params.templateId}`;
  } else {
    return `${ROUTES_CONST.REQUEST.Request}`;
  }
}
export const routes: Routes = [
  {
    path: "",
    component: TemplateFormContainer,
    canActivate: [AuthGuard],
    children: [
      {
        path: "",
        pathMatch: "full",
        redirectTo: `/${ROUTES_CONST.TEMPLATES.Template}`,
      },
      {
        path: ROUTES_CONST.TEMPLATES.GRCreate,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["VIEW_GR_SPD_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
          selectedProgramCode: CONSTANTS.GR,
        },
      },
      {
        path: ROUTES_CONST.TEMPLATES.SPDCreate,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          
          permissions: {
            only: ["VIEW_GR_SPD_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
          selectedProgramCode: CONSTANTS.SPD,
        },
      },
      {
        path: ROUTES_CONST.TEMPLATES.SCCreate,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["VIEW_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
          selectedProgramCode: CONSTANTS.SC,
        },
      },
      {
        path: ROUTES_CONST.TEMPLATES.BPDCreate,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["VIEW_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
          selectedProgramCode: CONSTANTS.BPD,
        },
      },

      {
        path: `${ROUTES_CONST.TEMPLATES.GREdit}/:templateId`,
        component: TemplateFormPanel,
        canDeactivate: [CanDeactivateGuard],
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["EDIT_GR_SPD_REQUESTS"],
            redirectTo: redirectFunc,
          },
          selectedProgramCode: CONSTANTS.GR,
        },
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.BPDEdit}/:templateId`,
        component: TemplateFormPanel,
        canDeactivate: [CanDeactivateGuard],
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["ADMIN"],
            redirectTo: redirectFunc,
          },
          selectedProgramCode: CONSTANTS.BPD,
        },
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.SPDEdit}/:templateId`,
        component: TemplateFormPanel,
        canDeactivate: [CanDeactivateGuard],
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          
          permissions: {
            only: ["EDIT_GR_SPD_REQUESTS"],
            redirectTo: redirectFunc,
          },
          selectedProgramCode: CONSTANTS.SPD,
        },
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.SCEdit}/:templateId`,
        component: TemplateFormPanel,
        canDeactivate: [CanDeactivateGuard],
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["DO_STORE_COUPON_REQUESTS"],
            redirectTo: redirectFunc,
          },
        },
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.BPDSummary}/:templateId`,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["VIEW_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
        canDeactivate: [CanDeactivateGuard],
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.GRSummary}/:templateId`,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["VIEW_GR_SPD_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
        canDeactivate: [CanDeactivateGuard],
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.SPDSummary}/:templateId`,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          
          permissions: {
            only: ["VIEW_GR_SPD_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
        canDeactivate: [CanDeactivateGuard],
      },
      {
        path: `${ROUTES_CONST.TEMPLATES.SCSummary}/:templateId`,
        component: TemplateFormPanel,
        canActivate: [AuthGuard, PermissionsGuard],
        data: {
          permissions: {
            only: ["VIEW_OFFER_REQUESTS"],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`,
          },
        },
        canDeactivate: [CanDeactivateGuard],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TemplateFormRoutingModule {}

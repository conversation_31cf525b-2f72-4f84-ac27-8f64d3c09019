import { Validators } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { STORE_GROUP_CONSTANTS } from "../../../../../../groups/constants/store_group_constants";

/**offer request create rules */
export const TEMPLATE_CREATE_RULES = {
    BPD:{
        offerRequestManagement:["BPDTemplateManagementComponent"],
        programCode:"Base PD",
        components:['BPDTemplateSectionComponent',
        'GeneralComponent','BPDAdditionalInformationComponent', 
        'OfferDetailsComponent'],
        summaryComponents:['BPDTemplateSectionComponent',
        'GeneralComponent','BPDAdditionalInformationComponent',
        'OfferDetailsComponent'],
        podComponent: ["BPDPodDetailsTemplateComponent"],
        offerRequest:{
          otStatus:{
            validate:['save','submit'],
            impactFieldForOfferUpdate:true,
            control:['info','otStatus'],
            value:null,
            edit:{
              create:true,
              update:true
            },
            validators: [Validators.required],
            label:"Status",
            appDataOptions:"otStatusOptions",
            optionKey: 'name',
            optionValue: 'value',
            error:{
              required:'Status is required'
            }
           },
           otStatusReason:{
             validate:['save','submit'],
             impactFieldForOfferUpdate:true,
             control:['info','otStatusReason'],
             value:null,
             edit:{
               create:true,
               update:true
             },
             label:"Reason",
             appDataOptions:"otStatusReasonOptions",
             optionKey: 'name',
             optionValue: 'value',
            },
           otStatusReasonComment:{
            impactFieldForOfferUpdate:false,
            control:['info','otStatusReasonComment'],
            value:null,
            maxLength:1000,
            edit:{
              create:true,
              update:true
            },
            label:"Comments"
           },
           otStatusSetUntil:{
            impactFieldForOfferUpdate:false,
            control:['info','otStatusSetUntil'],
            value:null,
            edit:{
              create:true,
              update:true
            },
            label:"Set Status Until"
           },
          regionId:{
            validate:['save','submit'],
            impactFieldForOfferUpdate:true,
            control:['info','regionId'],
            value:null,
            edit:{
              create:true,
              update:true
            },
            onlyDisplay: true,
            label:"Region",
            appDataOptions:STORE_GROUP_CONSTANTS.SEARCH_STORE_GROUP_NEW_API,
            optionKey: "name",
            optionValue: 'code',
            error:{
              required:'Region is required'
            }
           },
          programType:{
             validate:['submit','process'],
             byPassValidateBeforeProcess:true,
             impactFieldForOfferUpdate:true,
             control:['info',"programType"],
             appDataOptions:"programTypeBPD",
             onlyDisplay: true,
             value:null,
             edit:{
              create:true,
              update:true
            },
             label:"Program Type",
             error:{
              required:'Program Type is required'
            }
           },
          //  allocationCode:{
          //   validate:['submit','process'],
          //   byPassValidateBeforeProcess:true,
          //   impactFieldForOfferUpdate:true,
          //   control:['info',"allocationCode"],
          //   appDataOptions:"allocationCodeApi",
          //   value: "01",
          //   optionKey: "name",
          //   optionValue: 'code',
          //   edit:{
          //    create:true,
          //    update:true
          //  },
          //  validators: [Validators.required],
          //   label:"Allocation Code",
          //   error:{
          //    required:'Allocation Code is required'
          //  }
          // },
          // allocationCodeName:{
          //   validate:[],
          //   byPassValidateBeforeProcess:true,
          //   impactFieldForOfferUpdate:true,
          //   control:['info',"allocationCodeName"],
          //   appDataOptions:"",
          //   value: "Default",
          //   optionKey: "name",
          //   optionValue: 'code',
          //   edit:{
          //    create:true,
          //    update:true
          //  }
          // },
          reviewFlags:{
            validate:['save','submit'],
            impactFieldForOfferUpdate:true,
            control:['info','reviewFlags'],
            isFormArray: false,
            displayKey: "flag",
            value: null,
            edit:{
              create:true,
              update:true
            },
            label:"Flags",
            appDataOptions:"reviewFlags",
           },
           brandAndSize:{
            validate:[],
            impactFieldForOfferUpdate:true,
            control:['info',"brandAndSize"],
            value:null,
            tooltip:true,
            tooltipTitle:"A few words to describe the products participating in the promotion (character limit = 100)",
            edit:{
              create:true,
              update:false
            },
            label:"Brand and Size",
            error:{
              required:'Brand and Size is required'
            }
           },
           customerSegment:{
            validate:['submit'],
            impactFieldForOfferUpdate:true,
            control:['rules','customerSegment'],
            appDataOptions:"offerCustomerSegments",
            value:null,
            edit:{
              create:true,
              update:false
            },
            label:"Segment",
            error:{
              required:'Segment is required'
            }
           },
           usageLimitTypePerUser:{
             impactFieldForOfferUpdate:false,
             control:['rules','usageLimitTypePerUser'],
             appDataOptions:"offerLimits",
             value:null,
             edit:{
               create:true,
               update:true
             },
             label:"Offer Limit"
            },
            bggm:{
              impactFieldForOfferUpdate:false,
              control:['info','bggm'],
              value:null,
              edit:{
                create:true,
                update:true
              },
              label:"BGGM"
             },
             bugm:{
               impactFieldForOfferUpdate:false,
               control:['info','bugm'],
               value:null,
               edit:{
                 create:true,
                 update:true
               },
               label:"BUGM"
              },
              categoryId:{
                impactFieldForOfferUpdate:false,
                control:['info','categoryId'],
                value:null,
                edit:{
                  create:true,
                  update:true
                },
                label:"Cat ID"
               },
               category:{
                 impactFieldForOfferUpdate:false,
                 control:['info','category'],
                 value:null,
                 edit:{
                   create:true,
                   update:true
                 },
                 label:"Category"
                },
                cic:{
                  impactFieldForOfferUpdate:false,
                  control:['info','cic'],
                  value:null,
                  edit:{
                    create:true,
                    update:true
                  },
                  label:"CIC"
                 },
                 cpg:{
                   impactFieldForOfferUpdate:false,
                   control:['info', 'cpg'],
                   value:null,
                   edit:{
                     create:true,
                     update:true
                   },
                   label:"CPG"
                  },
                  repUpc:{
                    impactFieldForOfferUpdate:false,
                    control:['info','repUpc' ],
                    value:null,
                    edit:{
                      create:true,
                      update:true
                    },
                    label:"Rep UPC"
                   },
                   desc:{
                    impactFieldForOfferUpdate:false,
                    control:['info','desc'],
                    value:null,
                    maxLength:1000,
                    edit:{
                      create:true,
                      update:true
                    },
                    label:"Additional Details"
                   }
      },
      podDetails:{
        scene7ImageId:{
         validate:['submit','process'],
         byPassValidateBeforeProcess:true,
         impactFieldForOfferUpdate:true,
         control:['podDetails','scene7ImageId'],
         value:null,
         edit:{
           create:true,
           update:true
         },
         required:true,
         label:"Scene 7 Image ID",
         appDataOptions:"",
         error:{
          customError:"Image ID is not found in Scene 7",
           required:'Scene 7 Image ID is required'
         }
        },
        priceText:{
         validate:['submit'],
         impactFieldForOfferUpdate:true,
         control:['podDetails','priceText'],
         value:null,
         edit:{
           create:true,
           update:true
         },
         maxLength:25,
         warning:{
           maxLength:16,
           message:"Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
         },
         onlyDisplay: true,
         required:true,
         label:"Price Text",
         appDataOptions:"",
         error:{
           required:'Price Text is required'
         }
        },
        headline1:{
         validate:['save'],
         impactFieldForOfferUpdate:true,
         control:['podDetails','headline1'],
         value:null,
         edit:{
           create:true,
           update:true
         },
         maxLength:100,
         warning:{
           maxLength:90,
           message:"Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
         },
         required:true,
         label:"Headline 1",
         appDataOptions:"",
         error:{
           required:'Headline 1 is required'
         }
        },
        headline2:{
         validate:['submit'],
         impactFieldForOfferUpdate:true,
         control:['podDetails','headline2'],
         value:null,
         maxLength:100,
         edit:{
           create:true,
           update:true
         },
         warning:{
           maxLength:90,
           message:"Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
         },
         required:true,
         label:"Headline 2",
         appDataOptions:"",
        },
        offerDescription:{
          validate:['submit'],
          impactFieldForOfferUpdate:true,
          control:['podDetails','offerDescription'],
          value:null,
          edit:{
            create:true,
            update:true
          },
          maxLength: 100,
          warning:{
            maxLength:90,
            message:"Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
          },
          required:true,
          label:"Offer Description",
          appDataOptions:"",
          error:{
            required:'Offer Description is required'
          }
         },
         upcQtyOrUOM:{
          validate:['submit'],
          impactFieldForOfferUpdate:true,
          control:['podDetails','upcQtyOrUOM'],
          value:null,
          edit:{
            create:true,
            update:true
          },
          maxLength: 95,
          warning:{
            maxLength:86,
            message:"Exceeds recommended characters. May not display correctly. ({{1}} characters left) "
          },
          required:true,
          label:"UPC Qty/UOM",
          appDataOptions:"",
          error:{
            required:'upcQtyOrUOM is required'
          }
         },
        offerDetailsCode:{
         validate:['submit','process'],
         byPassValidateBeforeProcess:true,
         impactFieldForOfferUpdate:true,
         control:['podDetails','offerDetailsCode'],
         value:'Base',
         appDataOptions: CONSTANTS.OFFER_DETAILS_CODE_LIST_API,
         isMultipleSelection: false,
         edit:{
           create:false,
           update:false
         },
         required:true,
         label:"Offer Details Code",
         error:{
           required:'Offer Details Code is required'
         }
        },
        shoppingListCategory:{
         validate:['submit'],
         impactFieldForOfferUpdate:true,
         control:['podDetails','shoppingListCategory'],
         value:null,
         edit:{
           create:true,
           update:true
         },
         required:true,
         label:"Shopping List Category",
         appDataOptions:"customerFriendlyProductCategories",
         error:{
           required:'Shopping List Category is required'
         }
        },
        leftNavCategory:{
         impactFieldForOfferUpdate:true,
         control:['podDetails','leftNavCategory'],
         value:null,
         edit:{
           create:true,
           update:true
         },
         required:true,
         label:"Left Nav Category",
         appDataOptions:"customerFriendlyProductCategories",
         isMultipleSelection: true,
         error:{
           required:'Left Nav Category is required'
         }
        },
        podUsageLimit:{
         impactFieldForOfferUpdate:true,
         control:['podDetails','podUsageLimit'],
         value: null,
         edit:{
           create:true,
           update:true
         },
         required:true,
         onlyDisplay: true,
         label:"Usage",
         appDataOptions:"podUsageLimits",
         error:{
           required:'podUsageLimit is required'
         }
        }
      }
  },
  SC:{
   },
   GR:{
   },

}

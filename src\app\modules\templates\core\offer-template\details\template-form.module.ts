import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { BsModalRef, ModalModule } from 'ngx-bootstrap/modal';
import { ProgressbarModule } from 'ngx-bootstrap/progressbar';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { CommonModule } from '@angular/common';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { FacetsModule } from '@appShared/components/common/facets/facets.module';

import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';


import { TypeaheadModule } from 'ngx-bootstrap/typeahead';

import { DigitDecimaNumberDirectiveModule } from '@appDirectives/digit-decimal/digit.decimal.module';
import { FeatureFlagDirectiveModule } from '@appDirectives/feature-flags/feature-flag-module';
import { LetDirectiveModule } from "@appDirectives/let/let.module";
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { OnlyNumberDirectiveModule } from '@appDirectives/only-number/only.number.module';
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { AppCommonModule } from '@appModules/common/app.common.module';
import { RequestFormModule } from '@appModules/request/core/offer-request/details/request-form.module';
import { LoadDynamicModule } from '@appModules/request/shared/components/load-dynamic/load-dynamic.module';
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { MobModule } from '@appShared/components/details/mob/mob.module';
import { PreviewCardModule } from '@appShared/components/details/preview-card/preview-card.module';
import { PreviewCommentsHistoryModule } from '@appShared/components/details/preview-comments-history/preview-comments-history.module';
import { SidebarModule } from '@appShared/ng-sidebar';
import { NgxConfirmBoxModule, NgxConfirmBoxService } from '@appShared/ngx-confirm-box';
import { ReviewFlagsComponent } from '@appTemplates/core/offer-template/details/components/review-flags/review-flags.component';
import { StatusSectionComponent } from '@appTemplates/core/offer-template/details/components/status-section/status-section.component';
import { TemplateFormContainer } from '@appTemplates/core/offer-template/details/components/template-form-container/template-form-container.component';
import { TemplateFormPanel } from '@appTemplates/core/offer-template/details/components/template-form-panel/template-form.panel.comp';
import { TemplateFormRoutingModule } from '@appTemplates/core/offer-template/details/routing/template-form-routing.module';
import { PopoverModule } from 'ngx-bootstrap/popover';

@NgModule({
  declarations: [
    TemplateFormContainer,
    TemplateFormPanel,
    StatusSectionComponent,
    ReviewFlagsComponent
  ],
  exports: [
    TemplateFormPanel,
    TemplateFormContainer
  ],
  imports: [
    RequestFormModule,
    TemplateFormRoutingModule,
    LoadDynamicModule,
    AppCommonModule,
    PreviewCardModule,
    PreviewCommentsHistoryModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FacetsModule,
    NgxDatatableModule,
    BsDatepickerModule.forRoot(),
    TabsModule.forRoot(),
    ModalModule.forRoot(),
    TypeaheadModule.forRoot(),
    SidebarModule.forRoot(),
    ApiErrorsModule,
    DigitDecimaNumberDirectiveModule,
    VarDirectiveModule,
    LetDirectiveModule,
    ProgressbarModule.forRoot(),
    TooltipModule.forRoot(),
    NgxConfirmBoxModule,
    NgSelectModule,
    NgOptionHighlightModule,
    OnlyNumberDirectiveModule,
    MarkAsTouchedOnFocusDirectiveModule,
    PermissionsModule.forChild(),
    FeatureFlagDirectiveModule,
    PopoverModule.forRoot(),
    MobModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [{ provide: BsModalRef, useValue: undefined }, NgxConfirmBoxService]
})
export class TemplateFormModule { }

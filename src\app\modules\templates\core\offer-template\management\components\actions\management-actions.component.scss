@import "scss/colors";
@import "scss/variables";
.more-button {
  border: 2px solid $theme-primary !important;
  color: $theme-primary !important;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4NCjxzdmcgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTBweCIgdmlld0JveD0iMCAwIDE2IDEwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICAgIDwhLS0gR2VuZXJhdG9yOiBTa2V0Y2ggNTIuNSAoNjc0NjkpIC0gaHR0cDovL3d3dy5ib2hlbWlhbmNvZGluZy5jb20vc2tldGNoIC0tPg0KICAgIDx0aXRsZT5NYXNrPC90aXRsZT4NCiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4NCiAgICA8ZGVmcz4NCiAgICAgICAgPHBvbHlnb24gaWQ9InBhdGgtMSIgcG9pbnRzPSI5My45ODQzNzk2IDIxIDEwMSAyNy42NTEyMzY5IDk5LjQzODI3NDYgMjkgOTEgMjEgOTkuNDM4Mjc0NiAxMyAxMDEgMTQuMzQ4NzYzMSI+PC9wb2x5Z29uPg0KICAgIDwvZGVmcz4NCiAgICA8ZyBpZD0iT2ZmZXItVHlwZSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+DQogICAgICAgIDxnIGlkPSJPUl9EZXRhaWxzX09mZmVyVHlwZV9TdG9yZS1DbG9zdXJlIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtOTI1LjAwMDAwMCwgLTg2LjAwMDAwMCkiPg0KICAgICAgICAgICAgPGcgaWQ9IkJ1dHRvbnMtLy1HbG9iYWwtLy1QcmltYXJ5LS8tQmx1ZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoODM3LjAwMDAwMCwgNzAuMDAwMDAwKSI+DQogICAgICAgICAgICAgICAgPG1hc2sgaWQ9Im1hc2stMiIgZmlsbD0id2hpdGUiPg0KICAgICAgICAgICAgICAgICAgICA8dXNlIHhsaW5rOmhyZWY9IiNwYXRoLTEiPjwvdXNlPg0KICAgICAgICAgICAgICAgIDwvbWFzaz4NCiAgICAgICAgICAgICAgICA8dXNlIGlkPSJNYXNrIiBmaWxsPSIjMDA1MjlGIiBmaWxsLXJ1bGU9Im5vbnplcm8iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDk2LjAwMDAwMCwgMjEuMDAwMDAwKSBzY2FsZSgxLCAtMSkgcm90YXRlKDkwLjAwMDAwMCkgdHJhbnNsYXRlKC05Ni4wMDAwMDAsIC0yMS4wMDAwMDApICIgeGxpbms6aHJlZj0iI3BhdGgtMSI+PC91c2U+DQogICAgICAgICAgICA8L2c+DQogICAgICAgIDwvZz4NCiAgICA8L2c+DQo8L3N2Zz4=) no-repeat right 8px center;
  padding: 6px 27px 10px 10px;
  height: 40px;
  // width: 120px;
  text-align: left;
  &:disabled {
    opacity: 0.65;
  }
}
.button-size {
  width: 120px;
}
.actions-button {
  padding-left: 12px;
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+) no-repeat right 8px center;
  background-color: #ffffff !important;
  //color: #000000 !important;
  border-color: $grey-lighter-hex !important;
  .action-lbl {
    margin-top: 8px;
  }
}
.dropdown-toggle::after {
  border: none;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+);
  vertical-align: 0;
  background-repeat: no-repeat;
}
.list-item-container {
  border: 1px solid #f0f4f7;
  background: #f0f4f7;
}
.list-collapsed {
  background-color: #f0f4f7;
}
.list-expanded {
  background-color: rgb(255, 255, 255);

  -moz-box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
  -webkit-box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
  box-shadow: 0px 10px 12px 0px #e6e7e8, 0px -10px 12px 0px #e6e7e8;
}
.bold-label {
  font-weight: 700;
  font-size: 16px;
}
.dropdown-item.active,
.dropdown-item:active {
  color: $grey-darkest-hex !important;
  text-decoration: none;
  background-color: $grey-hover-state;
}
// .text-label {
//   font-size: 16px;
// }
.offers-link {
  color: $theme-primary !important;
}
.offers-link-deactivated {
  color: $grey-light-rgb !important;
}

.digital-status-icon-red {
  color: $red-primary-rgb;
  font-size: 10px;
  padding-left: 34px;
}

.non-digital-status-icon-red {
  color: $red-primary-rgb;
  font-size: 10px;
  padding-left: 7px;
}

.digital-status-icon-green {
  color: $green-primary-hex;
  font-size: 10px;
  padding-left: 34px;
}

.non-digital-status-icon-green {
  color: $green-primary-hex;
  font-size: 10px;
  padding-left: 7px;
}
.more-dropdown-toggle {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+) no-repeat right 8px center;
}
.green-status {
  width: 80px;
  text-align: center;
  border: 1px solid $green-primary-hex;
  color: $green-primary-hex;
  font-weight: 800;
}
.yellow-status {
  width: 80px;
  text-align: center;
  border: 1px solid #e79023;
  color: #e79023;
  font-weight: 800;
}
.purple-status {
  width: 80px;
  text-align: center;
  border: 1px solid #841fa9;
  color: #841fa9;
  font-weight: 800;
}
.blue-status {
  width: 80px;
  text-align: center;
  border: 1px solid #59b1e3;
  color: #59b1e3;
  font-weight: 800;
}
.red-status {
  width: 80px;
  text-align: center;
  border: 1px solid $red-primary-rgb;
  color: $red-primary-rgb;
  font-weight: 800;
}
.dropdown-menu {
  min-width: 8rem !important;
  width: 100%;
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}
.dropdown-item {
  padding: 0.25rem 1rem;
  white-space: normal;
}
.requestIdLink {
  color: $theme-primary !important;
}
.offers-wrap .offer-line:last-child {
  display: none;
}

.digital-value {
  color: $theme-primary !important;
}

.zero-digital-value {
  color: $grey-dark-hex !important;
}

.warning-text {
  text-align: center;
  font-weight: 600;
}
.warning-text-cancel {
  text-align: center;
  height: 32px;
  width: 477px;
  color: $font-color;
  font-family: $font-family !important;
  font-size: 26px;
  letter-spacing: 0;
  line-height: 32px;
}

.text-label-style {
  // height: 22px;
  // width: 48px;
  color: $theme-primary;

  font-size: $button-font-size !important;
  font-weight: 800;
  letter-spacing: 1.2px;
  line-height: 22px;
}

.warning-text-remove {
  font-weight: 600;
  padding: 0px 0px 0px 60px;
}
.radio-remove {
  padding: 15px 0px 0px 16px;
}
.radio-txt-label {
  padding: 0px 0px 0px 10px;
}
.radio-spacing {
  padding: 0px 0px 10px 0px;
}
.modal-content {
  height: 225px !important;
  width: 562px !important;
  border-radius: 5px !important;
  background-color: #ffffff !important;
}
.modal-buttons-cancel {
  box-sizing: border-box;
  height: 40px;
  width: 141px;
  border: 2px solid #00529f;
  background-color: #ffffff;
}
.modal-buttons-yes {
  height: 40px;
  width: 141px;
  background-color: #00529f;
}
.button-text {
  height: 22px;
  font-size: 18px;
  font-weight: 800;
  letter-spacing: 1.2px;
  line-height: 22px;
  text-align: center;
}
.button-text-cancel {
  height: 22px;
  width: 88.52px;
  color: #00529f;
  font-family: $font-family;
  font-size: 18px;
  font-weight: 800;
  letter-spacing: 1.2px;
  line-height: 22px;
  text-align: center;
}
.isDisableSave{
  cursor: not-allowed;
  pointer-events: none;
  background-color: #dedede;
  color: #4b4b4b !important;
}

import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { ActionsService } from '@appTemplates/services/actions.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ManagementActionsComponent } from './management-actions.component';

describe('ManagementActionsComponent', () => {
  let component: ManagementActionsComponent;
  let fixture: ComponentFixture<ManagementActionsComponent>;

  beforeEach(() => {
    const routerStub = () => ({ navigateByUrl: editUrl => ({}) });
    const initialDataServiceStub = () => ({
      getAppData: () => ({
        offerRequestStatuses: {
          I: "Draft",
          S: "Submitted",
          A: "Assigned",
          P: "Processing",
          E: "Editing",
          U: "Updating",
          D: "Completed",
          C: "Cancelled",
          R: "Removing",
        }
      })
    });
    const actionsServiceStub = () => ({
      addAndRemoveRules: (rules, deletePermissions, addPermissions) => ({})
    });
    const bsModalServiceStub = () => ({ show: (template, options) => ({}) });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [ManagementActionsComponent],
      providers: [
        { provide: Router, useFactory: routerStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: ActionsService, useFactory: actionsServiceStub },
        { provide: BsModalService, useFactory: bsModalServiceStub }
      ]
    });
    fixture = TestBed.createComponent(ManagementActionsComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it('rules has default value', () => {
    expect(component.rules).toEqual([]);
  });

  it('openModel has default value', () => {
    expect(component.openModel).toEqual([]);
  });

  it('routingAction has default value', () => {
    expect(component.routingAction).toEqual([`Copy`, `Edit`]);
  });

  it('apiAction has default value', () => {
    expect(component.apiAction).toEqual([`Copy`, `Edit`]);
  });
  xdescribe("ngOnInit", () => {
    it("makes expected calls", () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(initialDataServiceStub, 'getAppData');
      component.templateData = {
        info:{
          digitalUiStatus:'S'
        }
      };
      component.ngOnInit();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
    });
  });

  describe('ngOnInit', () => {
    it('should initialize rules based on templateData and appData', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(InitialDataService);
      const actionsServiceStub: ActionsService = fixture.debugElement.injector.get(ActionsService);

      component.templateData = {
        info: {
          digitalUiStatus: 'I'
        }
      };

      const appData = initialDataServiceStub.getAppData();
      const statusData = appData.offerRequestStatuses;
      const ruleStatus = statusData['I'];
      const rules = []; // Mock TEMPLATE_WORKFLOW_RULES?.[ruleStatus]?.["Manage"];
      spyOn(actionsServiceStub, 'addAndRemoveRules').and.returnValue(rules);

      component.ngOnInit();

      // expect(actionsServiceStub.addAndRemoveRules).toHaveBeenCalledWith(
      //   rules,
      //   component.deletePermissions,
      //   component.addPermissions
      // );
      // expect(component.rules).toEqual(rules);
    });
  });
  
  describe('openModal', () => {
    it('makes expected calls', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(BsModalService);
      const spy = spyOn(bsModalServiceStub, 'show');
      component.openModal('template', 'actions');
      expect(spy).toHaveBeenCalled();
    });
  });
  describe('routingRedirectCallback', () => {
    it('makes expected calls', () => {
      const routerStub: Router = fixture.debugElement.injector.get(Router);
      const action = "Edit";
      component.templateData = {
        info: {
          id: {
            externalOfferId: '1911'
          },
          offerName: 'offerName',
          assignedTo: 'assignedToName',
          offerStatus: 'offerStatus',
          offerProgramCode: 'offerCode'
        }
      };
      const spy = spyOn(routerStub, 'navigateByUrl')
      component.routingRedirectCallback(action);
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('eventClickActionHandler', () => {
    it('should call openModal when type is in openModel', () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(BsModalService);
      const spy = spyOn(bsModalServiceStub, 'show');
      component.openModel = ['TestType'];
      component[`openTestTypeModel`] = 'template';
      component.eventClickActionHandler('TestType');
      expect(spy).toHaveBeenCalledWith('template', { keyboard: true, class: 'modal-m' });
    });

    it('should call routingRedirectCallback when type is in routingAction', () => {
      const spy = spyOn(component, 'routingRedirectCallback');
      component.routingAction = ['Edit'];
      component.eventClickActionHandler('Edit');
      expect(spy).toHaveBeenCalledWith('Edit');
    });
  });

  describe('apiCancelCallback', () => {
    it('should not throw any errors when called', () => {
      expect(() => component.apiCancelCallback()).not.toThrow();
    });
  });
});

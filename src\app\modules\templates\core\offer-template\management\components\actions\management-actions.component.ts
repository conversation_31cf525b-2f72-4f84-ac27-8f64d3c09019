import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { ActionsService } from '@appTemplates/services/actions.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TEMPLATE_WORKFLOW_RULES } from '../../shared/rules/management.actions';

@Component({
  selector: 'app-management-actions',
  templateUrl: "./management-actions.component.html",
  styleUrls: ['./management-actions.component.scss']
})
export class ManagementActionsComponent implements OnInit {
  @Input() templateData;
  rules: any = [];
  modalRef: BsModalRef;
  actionsDropDownCssBasedOnPermissions: string;
  deletePermissions = {
    [CONSTANTS.Permissions.AssignNonDigitalUsers]:{
      Manage: [ "Copy"]
    },
    [CONSTANTS.Permissions.AssignDigitalUsers]:{
      Manage: ["Save", "Copy", "Delete"]
    },
  }
  addPermissions = {
    [CONSTANTS.Permissions.AssignNonDigitalUsers]:{
      Manage: ["Save"]
    },
    [CONSTANTS.Permissions.AssignDigitalUsers]:{
      Manage: ["Save", "Copy", "Delete"]
    },
  }
  openModel = [];
  routingAction = ["Copy","Edit"];
  apiAction = ["Copy","Edit"];
  constructor(private actionService:ActionsService,
    private modalService: BsModalService,
    private initialDataService:InitialDataService,
    private _router: Router,
    ) {
      // intentionally left empty
     }

  ngOnInit(): void {
    const appData = this.initialDataService.getAppData(),
          statusData = appData.offerRequestStatuses,
          {info:{digitalUiStatus}} = this.templateData,
          ruleStatus = statusData[digitalUiStatus];
   const rules = TEMPLATE_WORKFLOW_RULES?.[ruleStatus]?.["Manage"];
   this.rules = this.actionService.addAndRemoveRules(rules,this.deletePermissions,this.addPermissions);
  
  }
  eventClickActionHandler(type){
    if(this.openModel.includes(type)){
      this.openModal(this?.[`open${type}Model`], { keyboard: true, class: "modal-m" });
    }else if(this.routingAction.includes(type)){
      this?.[`routingRedirectCallback`](type);
    }else if(this.apiAction.includes(type)){
      this?.[`api${type}Callback`]();
    }
  }
  openModal(template, options) {
    this.modalRef = this.modalService.show(template, options);
  }
  apiCancelCallback(){
    // intentionally left empty
  }
  routingRedirectCallback(action){
    if(action ==='Edit'){
      const {info:{id}} = this.templateData;
      const editUrl = `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDEdit}/${id}`;
      this._router.navigateByUrl(editUrl);

    }
  }

}

import { Injector, NO_ERRORS_SCHEMA } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { AppInjector } from "@appServices/common/app.injector.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { TemplateManagementListService } from "@appTemplates/services/template-management-list-service";
import { BehaviorSubject, Subject } from "rxjs";
import { BaseTemplateListComponent } from "./base-template-list.component";

// const chai = require("chai"),
//   spies = require("chai-spies"),
//   expect = chai.expect;
// chai.use(spies);
// const spyOn = chai.spy.on;

// const sinon = require("sinon");
// const sinonChai = require("sinon-chai");

// chai.use(sinonChai);
describe("BaseTemplateListComponent", () => {
  const templateManagementListServiceStub = () => ({
    displayDigitalNonDigitalStatus: () => ({}),
  });
  const facetItemServiceStub = () => ({
    populateStoreFacet: (facets, storesSearchCriteria, divisionRogCds) => ({}),
    getFacetItems: () => ({}),
    getdivsionStateFacetItems: () => ({}),
    sortDivisionRogCds: () => ({}),
  });
  const bulkUpdateServiceStub = () => ({
    updateTestingOffers: (arg) => ({ subscribe: (f) => f({}) }),
    publishBulkOffers: (arg) => ({ subscribe: (f) => f({}) }),
    preCheckBatch: (arg) => ({ subscribe: (f) => f({}) }),
    prePublishOffers: (arg) => ({ subscribe: (f) => f({}) }),
    displayPopup$: { subscribe: (f) => f({}), next: () => ({}) },
    allOffersSelected$: { subscribe: (f) => f({}), next: () => ({}) },
    requestIdsListSelected$: { subscribe: (f) => f({}), next: () => ({}) },
    offerIdsListSelected$: { subscribe: (f) => f({}), next: () => ({}) },
    offerBulkSelection: { subscribe: (f) => f({}), next: () => ({}) },
    bulkSelectionForOffers: { subscribe: (f) => f({}), next: () => ({}) },
    isSelectionReset: { subscribe: (f) => f(false), next: () => ({}) },
    bulkSelected$: ({ subscribe: () => ({}) }),
    isAllBatchSelected: { next: () => ({}) },
    reqIdsOnPage: {},
    requestIdArr: { push: () => ({}), length: {}, splice: () => ({}) },
    userTypeArray: {
      push: () => ({}),
      indexOf: () => ({}),
      includes: () => ({}),
      splice: () => ({})
    },
    OfferDatesArray: { push: () => ({}) },
  });
  const initialDataServiceStub = () => ({ getAppData: () => ({}) });
  const permissionsServiceStub = () => ({})
  let component: BaseTemplateListComponent;
  let fixture: ComponentFixture<BaseTemplateListComponent>;
  beforeEach(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [BaseTemplateListComponent],
      providers: [
        { provide: TemplateManagementListService, useFactory: templateManagementListServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: PermissionsService, useFactory: permissionsServiceStub }
      ],
    });
    AppInjector.setInjector(TestBed.inject(Injector));
    fixture = TestBed.createComponent(BaseTemplateListComponent);
    component = fixture.componentInstance;
  });

  it("can load instance", () => {
    expect(component).toBeTruthy()
  });
  describe("ngOnInit", () => {
    it("makes expected calls", () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(initialDataServiceStub, 'getAppData').and.returnValue({});
      component.ngOnInit();
    });
  });
  describe("initSubscribes", () => {
    it("should call expected method", () => {
      const bulkUpdateServiceStub: BulkUpdateService = TestBed.inject(BulkUpdateService);
      bulkUpdateServiceStub.bulkSelected$ = new Subject();
      bulkUpdateServiceStub.offerBulkSelection= new BehaviorSubject("selectAcrossAllPages");
      component.initSubscribes();
      expect(component.isSelected).toEqual(true);
    });
  });
  describe("getSummaryPage", () => {
    it("should call expected method", () => {
      const facetItemServiceStub: FacetItemService = TestBed.inject(FacetItemService);
      facetItemServiceStub.templateProgramCodeSelected = "BPD";
      component.getSummaryPage("4124545");
    });
  });
  describe("getOfferStatusClass ", () => {
    it("should return expected classes based on offer status", () => {
      let className = component.getStatus("A");
      expect(className).toEqual("blue-status bold-label");

      className = component.getStatus("P");
      expect(className).toEqual("green-status bold-label");

      className = component.getStatus("S");
      expect(className).toEqual("yellow-status bold-label");

      className = component.getStatus("I");
      expect(className).toEqual("red-status bold-label");

      className = component.getStatus("E");
      expect(className).toEqual("red-status bold-label");

      className = component.getStatus("U");
      expect(className).toEqual("yellow-status bold-label");

      className = component.getStatus("D");
      expect(className).toEqual("purple-status bold-label");

      className = component.getStatus("R");
      expect(className).toEqual("red-status bold-label");
    });
  });
  describe('displayDigitalNonDigitalStatus', () => {
    it('makes expected calls', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Processing', className: 'green-status bold-label' }
      let ndRes = { status: 'Processing', className: 'green-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Completed"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "D",
          "nonDigitalStatus": "D",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,
        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);
      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();
      let digitalREs = { status: 'Completed', className: 'purple-status bold-label' }
      let ndRes = { status: 'Completed', className: 'purple-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Completed"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "A",
          "nonDigitalStatus": "A",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Assigned', className: 'blue-status bold-label' }
      let ndRes = { status: 'Assigned', className: 'blue-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "null"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": null,
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      expect(component.digitalStatus.className).toEqual('')
    });
    it('if Both Digital and nondigital are "Editing', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",

          digitalEditStatus: {
            editStatus: 'E'
          },
          nonDigitalEditStatus: {
            editStatus: 'E'
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Editing', className: 'red-status bold-label' }
      let ndRes = { status: 'Editing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Updating', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "D",
          "nonDigitalStatus": "D",

          digitalEditStatus: {
            editStatus: 'U'
          },
          nonDigitalEditStatus: {
            editStatus: 'U'
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Updating', className: 'yellow-status bold-label' }
      let ndRes = { status: 'Updating', className: 'yellow-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Removing', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": "P",

          digitalEditStatus: {
            editStatus: 'R'
          },
          nonDigitalEditStatus: {
            editStatus: 'R'
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Removing', className: 'red-status bold-label' }
      let ndRes = { status: 'Removing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Digital is "Removing', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": null,

          digitalEditStatus: {
            editStatus: 'R'
          },
          nonDigitalEditStatus: {
            editStatus: null
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Removing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
    });
    it('if  Non-Digital is "Removing', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": 'D',

          digitalEditStatus: {
            editStatus: null
          },
          nonDigitalEditStatus: {
            editStatus: 'R'
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);
      expect(component.getNonDigitalStatus).toHaveBeenCalled();

      let ndRes = { status: 'Removing', className: 'red-status bold-label' }

      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Digital is "Editing', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "P",
          "nonDigitalStatus": null,

          digitalEditStatus: {
            editStatus: 'E'
          },
          nonDigitalEditStatus: {
            editStatus: null
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Editing', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
    });
    it('if  Non-Digital is "Editing', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": 'D',

          digitalEditStatus: {
            editStatus: null
          },
          nonDigitalEditStatus: {
            editStatus: 'E'
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getNonDigitalStatus).toHaveBeenCalled();


      let ndRes = { status: 'Editing', className: 'red-status bold-label' }

      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if  Digital is "Updating', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "D",
          "nonDigitalStatus": null,

          digitalEditStatus: {
            editStatus: 'U'
          },
          nonDigitalEditStatus: {
            editStatus: null
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.getDigitalStatus).toHaveBeenCalled();

      let digitalREs = { status: 'Updating', className: 'yellow-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
    });
    it('if  Non-Digital is "updating', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": 'p',

          digitalEditStatus: {
            editStatus: null
          },
          nonDigitalEditStatus: {
            editStatus: 'U'
          },

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);
      expect(component.getNonDigitalStatus).toHaveBeenCalled();


      let ndRes = { status: 'Updating', className: 'yellow-status bold-label' }

      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
    it('if Both Digital and nondigital are "Submitted"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "S",
          "nonDigitalStatus": "S",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      let digitalREs = { status: 'Submitted', className: 'yellow-status bold-label' }
      let ndRes = { status: 'Submitted', className: 'yellow-status bold-label' }

      expect(component.statusClassName).toEqual('yellow-status bold-label')
    });
    it('if Both Digital and nondigital are "Draft"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "I",
          "nonDigitalStatus": "I",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);

      expect(component.statusClassName).toEqual('red-status bold-label')
    });
    it('if only Digital is "Draft"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();
      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": "I",
          "nonDigitalStatus": null,
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);
      let digitalREs = { status: 'Draft', className: 'red-status bold-label' }
      expect(component.digitalStatus).toEqual(digitalREs)
    });
    it('if only non-Digital is "Draft"', () => {
      spyOn(component, 'getDigitalStatus').and.callThrough();
      spyOn(component, 'getNonDigitalStatus').and.callThrough();

      component.templateItem = {
        "info": {
          "id": 342889629,
          "digitalStatus": null,
          "nonDigitalStatus": "I",
          "digitalEditStatus": null,
          "nonDigitalEditStatus": null,

        }
      };
      component.configData = {
        offerRequestStatuses: {
          "I": "Draft",
          "S": "Submitted",
          "A": "Assigned",
          "P": "Processing",
          "E": "Editing",
          "U": "Updating",
          "D": "Completed",
          "C": "Canceled",
          "R": "Removing"
        }
      }
      component.displayDigitalNonDigitalStatus(component.templateItem);
      let ndRes = { status: 'Draft', className: 'red-status bold-label' }
      expect(component.nonDigitalStatus).toEqual(ndRes)
    });
  });
});
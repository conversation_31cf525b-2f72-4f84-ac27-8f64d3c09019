import { Component, Input, OnInit } from "@angular/core";
import { ROUTES_CONST } from "@appConstants//routes_constants";
import { CONSTANTS } from "@appConstants/constants";
import { AppInjector } from "@appServices/common/app.injector.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { TemplateManagementListService } from "@appTemplates/services/template-management-list-service";

@Component({template: ''})
export class BaseTemplateListComponent implements OnInit {
  @Input("templateItem") templateItem: any;
  categoryColor;
  digitalStatus: any;
  isSelected: any;
  bulkSelection;

  public templateManagementListService: TemplateManagementListService;
  public bulkUpdateService: BulkUpdateService;
  public facetItemService: FacetItemService;
  public initialDataService: InitialDataService

  statusClassName: any;
  nonDigitalStatus: any;
  configData: any;

  constructor() {
    this.injectServices();
  }

  injectServices() {
    const injector = AppInjector.getInjector();

    this.templateManagementListService = injector.get(TemplateManagementListService);
    this.bulkUpdateService = injector.get(BulkUpdateService);
    this.facetItemService = injector.get(FacetItemService);
    this.initialDataService = injector.get(InitialDataService);
  }
  ngOnInit(): void {
    this.configData = this.initialDataService.getAppData();
    this.initSubscribes();
  }

  initSubscribes() {
    this.bulkUpdateService.bulkSelected$.subscribe((val) => {
      this.isSelected = val;
    });

    this.bulkUpdateService.offerBulkSelection.subscribe((value) => {
      this.bulkSelection = this.isSelected = value === "selectAcrossAllPages";
    });
  }
  getSummaryPage(templateId){
    const templatePCode = this.facetItemService.templateProgramCodeSelected;
    let summarySrc = ROUTES_CONST.TEMPLATES.BPDSummary;
    if (templatePCode) {
      summarySrc = ROUTES_CONST.TEMPLATES[`${templatePCode}Summary`];
    }
    return `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${summarySrc}/${templateId}`;
  }
  
  displayDigitalNonDigitalStatus(templateItem) {
    const { digitalStatus, nonDigitalStatus } = templateItem.info;

    const { digitalEditStatus, nonDigitalEditStatus } = templateItem.info;
    if (["I", "S"].includes(digitalStatus) || ["I", "S"].includes(nonDigitalStatus)) {
      const iStatus = digitalStatus && ["I"].includes(digitalStatus) && nonDigitalStatus && ["I"].includes(nonDigitalStatus);
      const sStatus = digitalStatus && ["S"].includes(digitalStatus) && nonDigitalStatus && ["S"].includes(nonDigitalStatus);
      if (iStatus || sStatus) {
        this.statusClassName = digitalStatus === "S" || nonDigitalStatus === "S" ? "yellow-status bold-label" : "red-status bold-label";
        this.digitalStatus = {
          status: this.offerRequestStatuses[digitalStatus || nonDigitalStatus],
          className: this.statusClassName,
        };
        this.nonDigitalStatus = "";
      } else {
        this.digitalStatus = this.getDigitalStatus(digitalStatus);
        this.nonDigitalStatus = this.getNonDigitalStatus(nonDigitalStatus);
      }
    } else {
      this.digitalStatus = this.getDigitalStatus(digitalStatus, digitalEditStatus);
      this.nonDigitalStatus = this.getNonDigitalStatus(nonDigitalStatus);
    }
    // After the statuses are being set Edit/Update Statuses
    if (digitalEditStatus && digitalEditStatus.editStatus) {
      if (digitalEditStatus.editStatus === "E" || digitalEditStatus.editStatus === "U") {
        this.digitalStatus = this.getDigitalStatus(digitalEditStatus.editStatus);
      }
      if (digitalEditStatus.editStatus === "R") {
        this.digitalStatus = this.getDigitalStatus(digitalEditStatus.editStatus);
      }
    }
    if (nonDigitalEditStatus && nonDigitalEditStatus.editStatus) {
      if (nonDigitalEditStatus.editStatus === "E" || nonDigitalEditStatus.editStatus === "U" || nonDigitalEditStatus.editStatus === "R") {
        this.nonDigitalStatus = this.getNonDigitalStatus(nonDigitalEditStatus.editStatus);
      }
    }
    return { digitalStatus: this.digitalStatus };
  }

  get offerRequestStatuses() {
    const selectedProgramCode = this.facetItemService.programCodeSelected;
    return selectedProgramCode == CONSTANTS.GR || selectedProgramCode == CONSTANTS.SPD
      ? this.configData[`offerRequestStatuses${selectedProgramCode}`]
      : this.configData.offerRequestStatuses;
  }

  getStatus(status) {
    let className = "";
    if (status === "A") {
      className = "blue-status bold-label";
    } else if (status === "P") {
      className = "green-status bold-label";
    } else if (status === "S") {
      className = "yellow-status bold-label";
    } else if (status === "I") {
      className = "red-status bold-label";
    } else if (status === "E") {
      className = "red-status bold-label";
    } else if (status === "U") {
      className = "yellow-status bold-label";
    } else if (status === "D") {
      className = "purple-status bold-label";
    } else if (status !== null && status !== "NA") {
      className = "red-status bold-label";
    }
    return className;
  }
  getDigitalStatus(nonDigitalStatus, digitalEditStatus?) {
    let status = this.offerRequestStatuses[nonDigitalStatus];
    let className = this.getStatus(nonDigitalStatus);
    const selectedProgramCode = this.facetItemService.programCodeSelected;
    if (selectedProgramCode == CONSTANTS.GR && nonDigitalStatus == "D" && digitalEditStatus && digitalEditStatus.editStatus == "RU") {
      status = this.offerRequestStatuses[digitalEditStatus.editStatus];
      className = "red-status bold-label";
    }
    return {
      status,
      className,
    };
  }
  getNonDigitalStatus(nonDigitalStatus) {
    return {
      status: this.offerRequestStatuses[nonDigitalStatus],
      className: this.getStatus(nonDigitalStatus),
    };
  }

  selectIndividualRequest(event, offerRequest) {
    this.isSelected = event.target.checked;
    this.bulkUpdateService.isAllBatchSelected.next("noSelectAcrossAllPages");

    if (this.isSelected) {
      this.bulkUpdateService.userTypeArray.push(offerRequest.info.deliveryChannel);
    } else {
      let typeArrayIndex = this.bulkUpdateService.userTypeArray.indexOf(offerRequest.info.deliveryChannel);
      this.bulkUpdateService.userTypeArray.splice(typeArrayIndex, 1);
    }
    // }
    if (event.target.checked) {
      this.bulkUpdateService.requestIdArr.push(offerRequest.info.id);
      this.bulkUpdateService.OfferDatesArray.push({
        startDate: offerRequest.rules.startDate.offerEffectiveStartDate,
        endDate: offerRequest.rules.endDate.offerEffectiveEndDate,
      });
      const oRid = offerRequest.info.id;
      this.bulkUpdateService.bulkAssignedUsers[`${oRid}`] = {
        digitalUser: offerRequest.info.digitalUser,
        nonDigitalUser: offerRequest.info.nonDigitalUser,
        digitalStatus: offerRequest.info.digitalStatus,
        nonDigitalStatus: offerRequest.info.nonDigitalStatus,
      };
    } else {
      for (let i = 0; i < this.bulkUpdateService.requestIdArr.length; i++) {
        if (this.bulkUpdateService.requestIdArr[i] === offerRequest.info.id) {
          this.bulkUpdateService.requestIdArr.splice(i, 1);
          this.bulkUpdateService.OfferDatesArray.splice(i, 1);
          this.removeUncheckedUser(this.bulkUpdateService.bulkAssignedUsers, offerRequest.info.id);
        }
      }
    }

    this.bulkUpdateService.requestIdsListSelected$.next(this.bulkUpdateService.requestIdArr);
  }
  removeUncheckedUser(bulkusers, id) {
    bulkusers = delete bulkusers[id];
  }
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Injector, NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { PersistenceSearchService } from '@appServices/management/persistence-search.service';
import { TemplateManagementBaseComponent } from './template-management-base.component';
import { AppInjector } from '@appServices/common/app.injector.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { OfferDetailsService } from '@appOffersServices/offer-details.service';
import { StoreGroupService } from '@appGroupsServices/store-group.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { BaseManagementService } from '@appServices/management/base-management.service';
import { AuthService } from '@appServices/common/auth.service';
import { BehaviorSubject } from 'rxjs';

describe('TemplateManagementBaseComponent', () => {
  let component: TemplateManagementBaseComponent;
  let fixture: ComponentFixture<TemplateManagementBaseComponent>;

  beforeEach(() => {
    const routerStub = () => ({ events: { subscribe: f => f({}) } });
    const commonRouteServiceStub = () => ({ currentActivatedRoute: {} });
    const commonSearchServiceStub = () => ({
      resetFilters: object => ({}),
      fetchDefaultOptions: object => ({}),
      currentRouter: {},
      storeSearchSelections: () => ({}),
      setActiveCurrentSearchType: templateProgramCodeSelected => ({}),
      setFiltersForPersisted: object => ({}),
      updateSearchWithPersistedSelections: () => ({})
    });
    const facetItemServiceStub = () => ({
      templateProgramCodeSelected: {},
      programCodeInitial: {},
      programCodeChanged: {}
    });
    const initialDataServiceStub = () => ({ getAppData: () => ({}) });
    const baseInputSearchServiceStub = () => ({
      currentRouter: {},
      setActiveCurrentSearchType: templateProgramCodeSelected => ({}),
      populateChipList: () => ({}),
      createSubject: () => ({}),
      postDataForInputSearch: (arg, arg1) => ({})
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({})
    });
    const offerDetailsServiceStub = () => ({
      listOfferDetailsCode: obj => ({ subscribe: f => f({}) }),
      fetchOdcList: () => ({ subscribe: f => f({}) }),
      addOfferDetailsCode: array => ({
        subscribe: f => f({}),
        bind: () => ({})
      }),
      editOfferDetailsCode: { bind: () => ({ subscribe: f => f({}) }) }
    });
    const storeGroupServiceStub = {
      createInstance: () => ({}),
      createFacetInstance: () => ({}),
      setEnableForm: (arg) => ({}),
      searchStoreGroup: (groupName, arg) => ({ subscribe: () => ({}) }),
      setStoreQuery: (object) => ({}),
      getFeatureKeys: () => ({ length: {}, includes: () => ({}) }),
      setFeatureKeys: (arg) => ({}),
      populateStoreFacets: () => ({ subscribe: () => ({}) }),
      populateStoreFilterSearch: (object) => ({}),
      getStoreQuery: () => ({}),
      createStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
      updateStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
      call: (arg) => ({}),
      getStoreIds: (object) => ({ subscribe: () => ({}) }),
    };
    const bulkUpdateServiceStub = () => ({
      allOffersSelected$: { subscribe: f => f({}) },
      bulkSelectionForOffers: { subscribe: f => f({}) },
      displayPopup$: { next: () => ({}) },
      offersIdArr: {
        push: () => ({}),
        indexOf: () => ({}),
        splice: () => ({}),
        length: {}
      },
      offerIdsListSelected$: { next: () => ({}) }
    });
    const requestFormServiceStub = () => ({
      isPreviousNDStatusUpdating$: { subscribe: (f) => f({}), next: () => ({}) },
      isPreviousDGStatusUpdating$: { subscribe: (f) => f({}), next: () => ({}) },
      isEditNotificatonBoolean: { subscribe: (f) => f({}), next: () => ({}) },
      isUpdateNotificationBoolean: { subscribe: (f) => f({}), next: () => ({}) },
      isRemovedUnclippedOnBoolean: { subscribe: (f) => f({}), next: () => ({}) },
      isofferRequestRemovedBoolean: { subscribe: (f) => f({}), next: () => ({}) },
      getOfferStatusClass: () => ({})
    });
    const authServiceStub = () => ({ onUserDataAvailable: arg => ({}), getUserDetails: () => ({}) });
    const searchOfferServiceStub = () => ({
      getProgramAndProgramSubType: () => ({ subscribe: f => f({}) }),
      searchAllOffers: (arg, search, queryWithOrFilters) => ({
        subscribe: f => f({})
      })
    });
    const baseManagementServiceStub = () => ({
      getAllTemplatesApi: () =>({}),
      getDefaultTemplateFilters: () => ({})
    })
    const searchOfferRequestServiceStub = () => ({});
    const persistenceSearchServiceStub = () => ({});
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [TemplateManagementBaseComponent],
      providers: [
        { provide: Router, useFactory: routerStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: OfferDetailsService, useFactory: offerDetailsServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: SearchOfferService, useFactory: searchOfferServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: RequestFormService, useFactory: requestFormServiceStub },
        {provide: BaseManagementService, useFactory: baseManagementServiceStub},
        {
          provide: BaseInputSearchService,
          useFactory: baseInputSearchServiceStub
        },
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub
        },
        {
          provide: PersistenceSearchService,
          useFactory: persistenceSearchServiceStub
        },
        { provide: StoreGroupService, useValue: storeGroupServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: AuthService, useFactory: authServiceStub },
      ]
    });
    spyOn(TemplateManagementBaseComponent.prototype, 'setVariables');
    spyOn(TemplateManagementBaseComponent.prototype, 'routeChangeSub');
    AppInjector.setInjector(TestBed.inject(Injector));
    fixture = TestBed.createComponent(TemplateManagementBaseComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`isNoResultsMsg has default value`, () => {
    expect(component.isNoResultsMsg).toEqual(false);
  });

  it(`facetChipShow has default value`, () => {
    expect(component.facetChipShow).toEqual(false);
  });

  it(`pgCodeCount has default value`, () => {
    expect(component.pgCodeCount).toEqual(0);
  });

  it(`showList has default value`, () => {
    expect(component.showList).toEqual(true);
  });

  it(`showGrid has default value`, () => {
    expect(component.showGrid).toEqual(false);
  });

  it(`expand has default value`, () => {
    expect(component.expand).toEqual(false);
  });

  it(`collapse has default value`, () => {
    expect(component.collapse).toEqual(true);
  });

  it(`showFacets has default value`, () => {
    expect(component.showFacets).toEqual(false);
  });

  it(`isDisplayClearAllChipsLink has default value`, () => {
    expect(component.isDisplayClearAllChipsLink).toEqual(false);
  });

  it(`childRoutesArr has default value`, () => {
    expect(component.childRoutesArr.length).toEqual(2);
  });

  describe('constructor', () => {
    it('makes expected calls', () => {
      expect(
        TemplateManagementBaseComponent.prototype.setVariables
      ).toHaveBeenCalled();
      expect(
        TemplateManagementBaseComponent.prototype.routeChangeSub
      ).toHaveBeenCalled();
    });
  });

  describe('setVariables', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(initialDataServiceStub, 'getAppData').and.callThrough();
      (<jasmine.Spy>component.setVariables).and.callThrough();
      component.setVariables();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
    });
  });

  describe('resetSearchDataOnRouteChange', () => {
    it('makes expected calls', () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, 'resetFilters').and.callThrough();
      component.resetSearchDataOnRouteChange();
      expect(commonSearchServiceStub.resetFilters).toHaveBeenCalled();
    });
  });

  describe('routeChangeSub', () => {
    it('makes expected calls', () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(component, 'resetSearchDataOnRouteChange').and.callThrough();
      spyOn(commonSearchServiceStub, 'fetchDefaultOptions').and.callThrough();
      spyOn(commonSearchServiceStub, 'storeSearchSelections').and.callThrough();
      component.routeChangeSub();
      expect(component.resetSearchDataOnRouteChange).not.toHaveBeenCalled();
      expect(commonSearchServiceStub.fetchDefaultOptions).not.toHaveBeenCalled();
      expect(commonSearchServiceStub.storeSearchSelections).not.toHaveBeenCalled();
    });
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );
      spyOn(component, 'initSetup').and.callThrough();
      spyOn(component, 'initSubscribers');
      spyOn(
        commonSearchServiceStub,
        'setActiveCurrentSearchType'
      ).and.callThrough();
      spyOn(
        commonSearchServiceStub,
        'setFiltersForPersisted'
      ).and.callThrough();
      spyOn(
        baseInputSearchServiceStub,
        'setActiveCurrentSearchType'
      ).and.callThrough();
      spyOn(baseInputSearchServiceStub, 'populateChipList').and.callThrough();
      spyOn(baseInputSearchServiceStub, 'createSubject').and.callThrough();
      component.ngOnInit();
      expect(component.initSetup).toHaveBeenCalled();
      expect(component.initSubscribers).toHaveBeenCalled();
      expect(
        commonSearchServiceStub.setActiveCurrentSearchType
      ).toHaveBeenCalled();
      expect(commonSearchServiceStub.setFiltersForPersisted).toHaveBeenCalled();
      expect(
        baseInputSearchServiceStub.setActiveCurrentSearchType
      ).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.populateChipList).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.createSubject).toHaveBeenCalled();
    });
  });

  describe('ngAfterViewInit', () => {
    it('makes expected calls', () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(
        commonSearchServiceStub,
        'updateSearchWithPersistedSelections'
      ).and.callThrough();
      component.ngAfterViewInit();
      expect(
        commonSearchServiceStub.updateSearchWithPersistedSelections
      ).toHaveBeenCalled();
    });
  });

  describe('initSubscribers', () => {
    it('makes expected calls', () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(
        AuthService
      );
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      const bulkUpdateServiceStub: BulkUpdateService = fixture.debugElement.injector.get(
        BulkUpdateService
      );
      bulkUpdateServiceStub.offerBulkSelection = new BehaviorSubject("");
      authServiceStub.isUserDataAvailable = new BehaviorSubject(true);
      spyOn(component, 'searchAllTemplatesDataGrid').and.callThrough();
      spyOn(component, 'populateSearchTemplate').and.callThrough();
      component.initSubscribers();
      expect(component.searchAllTemplatesDataGrid).toHaveBeenCalled();
      expect(component.populateSearchTemplate).not.toHaveBeenCalled();
    });
  });

  describe('searchAllTemplatesDataGrid', () => {
    it('makes expected calls', () => {
      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );
      const baseManagementServiceStub: BaseManagementService = fixture.debugElement.injector.get(
        BaseManagementService
      );
      spyOn(baseManagementServiceStub, "getDefaultTemplateFilters");
      spyOn(baseInputSearchServiceStub, 'populateChipList').and.callThrough();
      spyOn(
        baseInputSearchServiceStub,
        'postDataForInputSearch'
      ).and.callThrough();
      component.searchAllTemplatesDataGrid();
      expect(baseInputSearchServiceStub.populateChipList).toHaveBeenCalled();
      expect(
        baseInputSearchServiceStub.postDataForInputSearch
      ).toHaveBeenCalled();
    });
  });

  describe('routeChangeSub', () => {
    it('should call resetSearchDataOnRouteChange when not redirected from child route', () => {
      spyOn(component, 'resetSearchDataOnRouteChange').and.callThrough();
      spyOnProperty(component, 'isRedirectedFromChildRoute', 'get').and.returnValue(false);
      component.routeChangeSub();
    });

    it('should not call resetSearchDataOnRouteChange when redirected from child route', () => {
      spyOn(component, 'resetSearchDataOnRouteChange').and.callThrough();
      spyOnProperty(component, 'isRedirectedFromChildRoute', 'get').and.returnValue(true);
      component.routeChangeSub();
      expect(component.resetSearchDataOnRouteChange).not.toHaveBeenCalled();
    });

    it('should call storeSearchSelections when routing to a child route', () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, 'storeSearchSelections').and.callThrough();
      spyOnProperty(component, 'isRedirectedFromChildRoute', 'get').and.returnValue(false);
      spyOn(component.childRoutesArr, 'some').and.returnValue(true);
      component.routeChangeSub();
    });
  });

  describe('storeGroupRegions', () => {
    it('should return storeGroupRegions from appData', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      const mockAppData = { storeGroupRegions: ['Region1', 'Region2'] };
      spyOn(initialDataServiceStub, 'getAppData').and.returnValue(mockAppData);
      expect(component.storeGroupRegions).toEqual(mockAppData.storeGroupRegions);
    });

    it('should return undefined if storeGroupRegions is not present in appData', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      const mockAppData = {};
      spyOn(initialDataServiceStub, 'getAppData').and.returnValue(mockAppData);
      expect(component.storeGroupRegions).toBeUndefined();
    });
  });
});

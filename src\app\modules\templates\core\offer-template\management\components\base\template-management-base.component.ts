import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { ActivationEnd, NavigationStart, Router } from "@angular/router";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { PersistenceSearchService } from "@appServices/management/persistence-search.service";

import { TemplateBaseComponent } from "../../../base/templates.base.component";

@Component({
  selector: "app-template-management-base",
  templateUrl: "./template-management-base.component.html"
})

export class TemplateManagementBaseComponent extends TemplateBaseComponent implements OnInit,AfterViewInit, OnDestroy {
  componentData: { component: any };
  pageNumber;
  totalCount;
  sid;
  isNoResultsMsg = false;
  filters: string[];
  facetChipShow = false;
  pgCodeCount: number = 0;
  search;
  showList = true;
  showGrid = false;
  expand = false;
  collapse = true;
  showFacets = false;
  items;
  defaultValue: string;
  appData: any;
  templateFilters: any;
  bulkSelection: string;
  isDisplayClearAllChipsLink = false;

  childRoutesArr = [`/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDSummary}`,
  `/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDEdit}`]
  pathName: string;

  constructor(public facetItemService:FacetItemService,public baseInputSearchService:BaseInputSearchService ,
    private router: Router,
    public commonRouteService: CommonRouteService,
    public persistenceSearchService: PersistenceSearchService,
    public commonSearchService: CommonSearchService,public searchOfferRequestService:SearchOfferRequestService,
  
    public initialDataService:InitialDataService) {
    super();
   
    this.setVariables();    
    this.routeChangeSub();
  }

  setVariables(){
    this.appData = this.initialDataService.getAppData();
   
    this.isDisplayClearAllChipsLink =  true;
  }

  get isTemplateMgmtPg(){
    this.pathName = window.location.pathname;    
    return this.pathName.includes('template') && !this.pathName.includes(ROUTES_CONST.TEMPLATES.TemplateForm);
  }

  resetSearchDataOnRouteChange(){   
 
    this.commonRouteService.currentActivatedRoute = this.pathName;

    //From  non template pages- In this case, we need to clear the filters    
    const selectedProgramCode = this.facetItemService.templateProgramCodeSelected;
    this.baseManagementService.defaultTemplateFilters = null;
    
    this.commonSearchService.resetFilters({pcSelected:selectedProgramCode, 
      currentRouter: this.baseInputSearchService.currentRouter, 
      resetChips: true
     });      
  }

  routeChangeSub(){
    this.subs.sink = this.router.events.subscribe((val) => {   
      if(!this.isTemplateMgmtPg){
        return false;
      }  
     
      //Triggers when routed to Template management page
      if(val instanceof ActivationEnd) {      
      
       //Reset default options
       this.commonSearchService.fetchDefaultOptions({key: this.facetItemService.templateProgramCodeSelected,
              currentRouter: this.commonSearchService.currentRouter});
       
       //Reset, if source is not child routes of template page
       if(!this.isRedirectedFromChildRoute){
         this.resetSearchDataOnRouteChange();
       }
     }
     
     //Triggers when leaving the Template management page
     if(val instanceof NavigationStart) {  
       const isRoutingToChild =  this.childRoutesArr?.some((elem) => val?.url?.includes(elem));
        
       //For cases where selection needs to persist, store the selections
       if(isRoutingToChild){
         this.commonSearchService.storeSearchSelections();        
       }
       
       //Reset, if source is not child routes of template page || not being routed to child routes
       if(!isRoutingToChild &&  !this.isRedirectedFromChildRoute){         
         this.resetSearchDataOnRouteChange();
       }
     }
   });  

  }
  
  //Checks if source is child routes of template page
  get isRedirectedFromChildRoute() {
    const urlComingFrom = this.router?.getCurrentNavigation()?.previousNavigation?.finalUrl?.toString();    
    if(urlComingFrom) {
      return this.childRoutesArr.some((elem) => urlComingFrom?.includes(elem));
    }
  }

  get storeGroupRegions() {
    const appData = this.initialDataService.getAppData();
    let { storeGroupRegions } = appData;
    return storeGroupRegions;
  }


  ngOnInit(): void {
    
    this.componentData = this.getOfferRequestComponent();
    this.baseInputSearchService.setActiveCurrentSearchType(this.facetItemService.templateProgramCodeSelected);
     
    this.commonSearchService.setActiveCurrentSearchType(this.facetItemService.templateProgramCodeSelected);

    // !this.isPersistenceFlagEnabled && this.commonSearchService.resetFilters({pcSelected:this.facetItemService.templateProgramCodeSelected, currentRouter: this.baseInputSearchService.currentRouter});//If Ks, is not turned on

    this.commonSearchService.setFiltersForPersisted({pcSelected:this.facetItemService.templateProgramCodeSelected, flag: true});
    
    this.baseInputSearchService.populateChipList();    
    
    this.baseInputSearchService.createSubject();
    this.initSetup();
    this.initSubscribers(); 
  }

  ngAfterViewInit(){
     //Update search with persisted selections
   this.commonSearchService.updateSearchWithPersistedSelections();
  }

   populateSearchTemplate(response){
    const {data, paginated} = response;
    this.baseManagementService.passPaginationData(data || {});
    this.baseManagementService.getAllTemplateData(data);
    const doReset = paginated || this.bulkSelection !== "selectAcrossAllPages";
    if(doReset) {
      this._bulkupdateservice.isSelectionReset.next(true);
    }
    this.baseManagementService.populateTemplateFilterAndChip(response);
  }

  initSubscribers() {
    this._authService.isUserDataAvailable.subscribe((user: boolean) => {
      user && this.searchAllTemplatesDataGrid();
    });
    this._bulkupdateservice.offerBulkSelection.subscribe((value) => {
      this.bulkSelection = value;
    })
    this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}BehaviorSubject`]?.
    subscribe((response)=>{
      if(response){
        this.populateSearchTemplate(response);
      }  
    });
  } 
  
  initSetup(): void {
    this.facetItemService.programCodeInitial = true;
    this.facetItemService.programCodeChanged = true;
  }

  searchAllTemplatesDataGrid() {
  this._bulkupdateservice.userTypeArray = [];
  this.templateFilters = this.baseManagementService.getDefaultTemplateFilters();
  this.baseInputSearchService.populateChipList();
  this.baseInputSearchService.postDataForInputSearch(true, true);
  }
  
  ngOnDestroy() {
    this.subs.unsubscribe();    
  }
}


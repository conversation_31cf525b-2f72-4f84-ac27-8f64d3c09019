<section
  *ngIf="showNoRecordMsg"
  class="alert alert-warning align-items- d-flex justify-content-center mt-5 ml-3 mb-5 text"
  role="alert"
>
  <strong>No records found!</strong>
</section>

<section *ngIf="templatesItems && templatesItems[0]">
  <section class="row pb-10 list-wrapper">
    <div class="cb-width"></div>
    <div class="col">
      <div class="row">
        <div class="col col-md-1 text-left align-templateId">
          <label class="bold-label text-left mb-0">Template ID</label>
          <label class="bold-label text-left mb-0 d-block">MOB ID</label>
        </div>
        <div class="col col-md-3 text-left">
          <label class="bold-label text-left mb-0">Brand and Size </label>
          <label class="bold-label text-left mb-0 d-block">MOB Name</label>
        </div>
        <div class="col col-md-2 text-left">
          <label class="bold-label text-left mb-0">Region</label>
          <label class="bold-label text-left mb-0 d-block">Rep UPC</label>
        </div>
        <div class="col col-md-2 text-left ">
          <label class="bold-label text-left mb-0">CPG</label>
          <label class="bold-label text-left mb-0 d-block">BUGM </label>
        </div>
        <div class="col col-md-2 text-left">
          <label class="bold-label text-left mb-0">Cat Description</label>
          <label class="bold-label text-left mb-0 d-block">Cat ID</label>
        </div>
        <div class="col col-md-1 text-left ">
          <label class="bold-label text-left mb-0 d-block">Status</label>
        </div>
      </div>
    </div>
  </section>

  <section
    *ngIf="templatesItems[0]"
    class="my-3 mt-5">
    <ng-container *ngFor="let templateItem of templatesItems; trackBy: trackByFnOnTemplateId; index as i">
      <div>
        <app-template-list [templateItem]="templateItem"></app-template-list>
      </div>
    </ng-container>
  </section>
</section>

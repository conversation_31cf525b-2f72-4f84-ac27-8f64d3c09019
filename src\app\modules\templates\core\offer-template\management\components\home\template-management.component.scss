@import "scss/_mixins";
@import "scss/variables";
@import "scss/_colors";

.bold-label{
    font-weight: $bold-font-weight;
    font-size: $base-font-size;
}
.list-wrapper {
    margin-left: 10px;
}
.pl-status{
    padding-left: 35px;
  }
.pl-70{
    padding-left: 70px;
}
.pl-20{
    padding-left:20px
}
.pl-30{
    padding-left: 30px;
}
.pl-100{
    padding-left: 100px;
}
.popover{
    width:auto !important;
    max-width:auto !important;
   }

.align-templateId {
    @media screen and (min-width: 768px) {
        flex: 0 0 11.33333% !important;
        max-width: 11.33333% !important;
    }
  }
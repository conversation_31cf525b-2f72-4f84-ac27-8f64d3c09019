import { BaseManagementService } from '@appServices/management/base-management.service';
import { of } from 'rxjs';
import { TemplateManagementComponent } from './template-management.component';

describe('TemplateManagementComponent', () => {
  let component: TemplateManagementComponent;
  let service: BaseManagementService;

  beforeEach(() => {
    service = jasmine.createSpyObj('BaseManagementService', ['templatesData$', 'passPaginationData']);
    component = new TemplateManagementComponent(service as any);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call initSubscribe on ngOnInit', () => {
    spyOn(component, 'initSubscribe');
    component.ngOnInit();
    expect(component.initSubscribe).toHaveBeenCalled();
  });

  it('should subscribe to templatesData$ in initSubscribe', () => {
    (service.templatesData$ as any) = of({ offerRequests: [] });
    component.initSubscribe();
    expect(component.templatesItems).toEqual([]);
  });

  it('should call passPaginationData with correct argument', () => {
    const templateObj = { offerRequests: [] };
    (service.templatesData$ as any) = of(templateObj);
    component.initSubscribe();
    expect(service.passPaginationData).toHaveBeenCalledWith(templateObj);
  });

  it('should update templatesItems correctly', () => {
    const templateObj = { offerRequests: ['item1', 'item2'] };
    (service.templatesData$ as any) = of(templateObj);
    component.initSubscribe();
    expect(component.templatesItems).toEqual(['item1', 'item2']);
  });

  it('should return correct id in trackByFnOnTemplateId', () => {
    const item = { info: { id: '123' } };
    expect(component.trackByFnOnTemplateId(0, item)).toEqual('123');
  });

  it('should return correct boolean in showNoRecordMsg', () => {
    component.templatesItems = [];
    expect(component.showNoRecordMsg).toBeTrue();
    component.templatesItems = ['item1', 'item2'];
    expect(component.showNoRecordMsg).toBeFalse();
  });
});
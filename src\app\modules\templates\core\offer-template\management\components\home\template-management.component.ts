import { Component, OnInit } from "@angular/core";
import { BaseManagementService } from "@appServices/management/base-management.service";

@Component({
  selector: "app-template-management",
  templateUrl: "./template-management.component.html",
  styleUrls: ["./template-management.component.scss"],
})
export class TemplateManagementComponent implements OnInit {
  templatesItems: any;

  constructor(private templateManagementService: BaseManagementService) {
    // intentionally left empty
  }

  ngOnInit(): void {
    this.initSubscribe();
  }
  initSubscribe() {
    this.templateManagementService.templatesData$.subscribe((templateObj: any) => {
      this.templatesItems = templateObj?.offerRequests;
      this.templateManagementService.passPaginationData(templateObj || {});
      
    });
  }
  trackByFnOnTemplateId(index, item) {
    return item?.info?.id;
  }
  get showNoRecordMsg() {
    return !this.templatesItems || this.templatesItems?.length === 0;
  }
}

import { HttpClient, HttpClientModule, <PERSON>ttp<PERSON>and<PERSON> } from '@angular/common/http';
import { Injector } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { BaseSavedSearchService } from '@appServices/management/base-saved-search.service';
import { PermissionsConfigurationService } from '@appShared/albertsons-angular-authorization';
import { MsalService } from '@azure/msal-angular';


import { TemplateInputSearchComponent } from './input-search.component';
import { CONSTANTS } from '@appConstants/constants';



describe('TemplateInputSearchComponent', () => {
  let component: TemplateInputSearchComponent;
  let fixture: ComponentFixture<TemplateInputSearchComponent>;

  beforeEach(() => {
    const baseSavedSearchServiceStub = () => ({
      fetchSavedSearch: () => ({subscribe: () => ({})}),
      deleteSaveSearch: () => ({subscribe: () => ({})}),
    });
    const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
    const authServiceStub = () => ({ onUserDataAvailable: arg => ({}) , getTokenString: () => ({}) });


    const searchUsersServiceStub = () => ({ getUsers: term => ({}) });
    const formBuilderStub = () => ({
      group: object => ({}),
      control: string => ({})
    });
    const baseInputSearchServiceStub = () => ({
      getDataForInputSearch: () => ({subscribe: () => ({})}),
      inputSearchOptions: [],
      getInputFieldSelected: ()=>({}),
      generateQueryForOptions: () => ({}),
      formQuery: () => ({}),
      formQueryWithFilter: () => ({}),
      setInputSearchOptions: () => ({})
    })
    const commonSearchServiceStub = () => ({
      resetFilters: object => ({}),
      fetchDefaultOptions: object => ({}),
      currentRouter: {},
      storeSearchSelections: () => ({}),
      setActiveCurrentSearchType: templateProgramCodeSelected => ({}),
      setFiltersForPersisted: object => ({}),
      updateSearchWithPersistedSelections: () => ({}),
      setAllFilterOptions: object => ({}),
      setInputSearchOption: (string, object) => ({}),
      setFilterOption: (string, object) => ({}),
      setDefaultOption: (string, object) => ({})
    });
    const permissionsConfigurationServiceStub = () => ({
      addPermissionStrategy: (string, function0) => ({}),
      setDefaultOnUnauthorizedStrategy: string => ({})
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({}),
      getConfigUrls: (pRINT_AD_IMPORT_LOG_API) => ({}),
    });
    const facetItemServiceStub = () => ({
      sortDivisionRogCds: divisionRogCds => ({}),
      populateStoreFacet: (
        facets,
        storesSearchCriteria,
        divisionRogCds
      ) => ({}),
      getFacetItems: () => ({}),
      getdivsionStateFacetItems: () => ({})
    });
    const commonRouteServiceStub = () => ({ currentActivatedRoute: {} });
    const commonServiceStub = () => ({});
    const featureFlagsServiceStub = () => ({
      isFeatureFlagEnabled: () => (false)
    });
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, FormsModule, HttpClientModule],
      declarations: [ TemplateInputSearchComponent ],
      providers: [
        HttpClient,
        HttpHandler,
        { provide: CommonService, useFactory: commonServiceStub },
        { provide: SearchUsersService, useFactory: searchUsersServiceStub },
        { provide: BaseInputSearchService, useFactory: baseInputSearchServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: UntypedFormBuilder, useFactory: formBuilderStub },
        { provide: AuthService, useFactory: authServiceStub },
        { provide: MsalService, useFactory: MsalServiceStub },
        { provide: PermissionsConfigurationService, useFactory: permissionsConfigurationServiceStub},
        { provide: BaseSavedSearchService, useFactory: baseSavedSearchServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagsServiceStub }
      ]
    })
    AppInjector.setInjector(TestBed.inject(Injector));
    fixture = TestBed.createComponent(TemplateInputSearchComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy()
  });

  it('should call setFilterOptions on ngOnInit', () => {
    spyOn(component, 'setFilterOptions');
    component.ngOnInit();
    expect(component.setFilterOptions).toHaveBeenCalled();
  });

  it('should set filter options correctly in setFilterOptions', () => {
    const commonSearchServiceSpy = spyOn(component['commonSearchService'], 'setAllFilterOptions');
    const setInputSearchOptionSpy = spyOn(component['commonSearchService'], 'setInputSearchOption');
    const setFilterOptionSpy = spyOn(component['commonSearchService'], 'setFilterOption');
    const setDefaultOptionSpy = spyOn(component['commonSearchService'], 'setDefaultOption');

    component.setFilterOptions();

    expect(commonSearchServiceSpy).toHaveBeenCalledWith({
      key: CONSTANTS.BPD,
      currentRouter: component['baseInputSearchService'].currentRouter
    });
    expect(setInputSearchOptionSpy).toHaveBeenCalledWith(
      CONSTANTS.BPD,
      component['commonSearchService'].inputSearchOption?.[CONSTANTS.BPD]
    );
    expect(setFilterOptionSpy).toHaveBeenCalledWith(
      CONSTANTS.BPD,
      component['commonSearchService'].filterOption?.[CONSTANTS.BPD]
    );
    expect(setDefaultOptionSpy).toHaveBeenCalledWith(
      CONSTANTS.BPD,
      component['commonSearchService'].defaultOption?.[CONSTANTS.BPD]
    );
  });
});

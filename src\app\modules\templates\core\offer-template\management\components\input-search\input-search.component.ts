import { Component, OnInit } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { BaseInputSearchComponent } from '@appShared/components/management/input-search/base-input-search/base-input-search.component';

@Component({
  selector: 'template-input-search',
  templateUrl: "./input-search.component.html"
})
export class TemplateInputSearchComponent extends BaseInputSearchComponent implements OnInit{

  constructor() { 
    super();
   
   // this.getInitialOptionsList();
  }
  ngOnInit(){
   this.setFilterOptions();
  }
  setFilterOptions(){
    this.commonSearchService.setAllFilterOptions( {key:CONSTANTS.BPD,currentRouter: this.baseInputSearchService.currentRouter});
    this.commonSearchService.setInputSearchOption(CONSTANTS.BPD,this.commonSearchService.inputSearchOption?.[CONSTANTS.BPD]);
    this.commonSearchService.setFilterOption(CONSTANTS.BPD,this.commonSearchService.filterOption?.[CONSTANTS.BPD]);
    this.commonSearchService.setDefaultOption(CONSTANTS.BPD,this.commonSearchService.defaultOption?.[CONSTANTS.BPD]);
    
  }
}

import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@angular/common/http";
import { ElementRef, Injector, NO_ERRORS_SCHEMA, Renderer2 } from "@angular/core";
import { ComponentFixture, TestBed, async } from "@angular/core/testing";
import { UntypedFormControl, UntypedFormGroup, FormsModule } from "@angular/forms";
import { CustomerGroupService } from "@appGroupsServices/customer-group.service";
import { PointGroupService } from "@appGroupsServices/point-group.service";
import { ProductGroupService } from "@appGroupsServices/product-group.service";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { SearchOfferService } from "@appOffersServices/search-offer.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AppInjector } from "@appServices/common/app.injector.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { BaseManagementService } from "@appServices/management/base-management.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { PermissionsConfigurationService, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { PodFilterComponent } from "@appShared/components/management/pod-filter/pod-filter.component";
import { MsalService } from "@azure/msal-angular";
import { BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject } from "rxjs";
import { TemplatePodFilterComponent } from "./templates-pod-filter.component";

describe("TemplatePodFilterComponent", () => {
  let component: TemplatePodFilterComponent;
  let fixture: ComponentFixture<TemplatePodFilterComponent>;

  beforeEach(async(() => {
    const authServiceStub = () => ({ onUserDataAvailable: (arg) => ({}) });
    const customerGroupServiceStub = () => ({
      getCustomerGroupData: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      getCustomerGroupByName: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      customerGroupSource: { next: () => ({}) },
    });
    const commonSearchServiceStub = () => ({
      inputSearchOption: {},
      setActiveCurrentSearchType: currentRouter => ({}),
      currentRouter: {},
      setAllFilterOptions: object => ({}),
      setFilters: object => ({}),
      setQueryValueForDefaultOption: () => ({})
    });
    const facetItemServiceStub = () => ({
      selectedOfferProgramCode$: { subscribe: (f) => f({}) },
      setOfferFilter: (string) => ({}),
      showGroupsFacet: new BehaviorSubject(true),
      expandSub: new BehaviorSubject(false),
    });
    const myTaskServiceStub = () => ({});
    const pointGroupServiceStub = () => ({
      searchPointGroup: (arg) => ({ subscribe: (f) => f({}) }),
      getAllPointGroupSearch: (result) => ({}),
    });
    const productGroupServiceStub = () => ({
      searchProductGroup: (arg) => ({ subscribe: (f) => f({}) }),
      getAllProductGroupSearch: (result) => ({}),
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({}),
    });
    const queryGeneratorStub = () => ({
      removeParam: (string) => ({}),
      removeParameters: (removeList) => ({}),
      pushParameters: (object) => ({}),
      getQuery: () => ({}),
      getInputValue: () => ({}),
      getQueryWithFilter: () => [],
      setQueryWithFilter: () => ({}),
      removeParamFromQueryFilter: () => ({}),
    });
    const bsModalServiceStub = () => ({ show: (template, options) => ({}) });
    const searchOfferRequestServiceStub = () => ({
      currentOfferRequests: { subscribe: (f) => f({}) },
      podFilterSearchSourceSearch: { subscribe: (f) => f({}) },
      searchAllOfferRequest: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      getOfferDetails: (result) => ({}),
    });
    const bulkUpdateServiceStub = () => ({
      updateTestingOffers: (arg) => ({ subscribe: (f) => f({}) }),
      publishBulkOffers: (arg) => ({ subscribe: (f) => f({}) }),
      preCheckBatch: (arg) => ({ subscribe: (f) => f({}) }),
      prePublishOffers: (arg) => ({ subscribe: (f) => f({}) }),
      displayPopup$: { subscribe: (f) => f({}), next: () => ({}) },
      allOffersSelected$: { subscribe: (f) => f({}), next: () => ({}) },
      requestIdsListSelected$: { subscribe: (f) => f({}), next: () => ({}) },
      offerIdsListSelected$: { subscribe: (f) => f({}), next: () => ({}) },
      offerBulkSelection: { subscribe: (f) => f({}), next: () => ({}) },
      bulkSelectionForOffers: { subscribe: (f) => f({}), next: () => ({}) },
      isSelectionReset: { subscribe: (f) => f(false), next: () => ({}) },
      bulkSelected$: { next: () => ({}) },
      isAllBatchSelected: { next: () => ({}) },
      reqIdsOnPage: {},
      requestIdArr: { length: {} },
      userTypeArray: { push: () => ({}), includes: () => ({}) },
      OfferDatesArray: { push: () => ({}) },
    });
    const searchOfferServiceStub = () => ({
      searchAllOffers: (arg, arg2) => ({ subscribe: (f) => f({}) }),
    });
    const baseMangementServiceStub = () => ({
      templatesData$: { subscribe: () => ({}) },
      getAllTemplates: () => ({}),
      fetchPaginationData: () => ({})
    });
    const renderer2Stub = () => ({
      setAttribute: (nextSibling, string, string1) => ({}),
      addClass: (nextSibling, string) => ({}),
      removeClass: (nextSibling, string) => ({}),
    });
    const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
    const storeGroupServiceStub = () => ({
      searchStoreGroup: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      getstoreGroupData: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      getAllStoreGroupSearch: (items) => ({}),
    });
    const baseInputSearchServiceStub = () => ({
      setActiveCurrentSearchType: aCTION_LOG => ({}),
      createSubject: () => ({}),
      currentRouter: {},
      populateChipList: () => ({}),
      postDataForInputSearch: arg => ({}),
      getActiveCurrentSearchType: () => ({})
    });
    const toastrServiceStub = () => ({
      warning: (string, string1, object) => ({}),
      success: (displayText, string, object) => ({}),
      error: (displayText, string, object) => ({}),
    });
    const elementRefStub = () => ({});

    TestBed.configureTestingModule({
      imports: [FormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [TemplatePodFilterComponent],
      providers: [
        HttpClient,
        HttpHandler,
        PermissionsService,
        PermissionsConfigurationService,
        { provide: MsalService, useFactory: MsalServiceStub },
        { provide: BaseManagementService, useFactory: baseMangementServiceStub },
        { provide: ElementRef, useFactory: elementRefStub },
        { provide: AuthService, useFactory: authServiceStub },
        { provide: CustomerGroupService, useFactory: customerGroupServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: Renderer2, useFactory: renderer2Stub },
        { provide: PointGroupService, useFactory: pointGroupServiceStub },
        {
          provide: BaseInputSearchService,
          useFactory: baseInputSearchServiceStub
        },
        { provide: ProductGroupService, useFactory: productGroupServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: BsModalService, useFactory: bsModalServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub,
        },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: SearchOfferService, useFactory: searchOfferServiceStub },
        { provide: StoreGroupService, useFactory: storeGroupServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub },
      ],
    })
    AppInjector.setInjector(TestBed.inject(Injector));
    fixture = TestBed.createComponent(TemplatePodFilterComponent);
    component = fixture.componentInstance;
  }));

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });

  it(`isHideExpandFeature has default value`, () => {
    expect(component.isHideExpandFeature).toEqual(true);
  });
  describe("batchSelection", () => {
    it("makes expected calls", () => {
      spyOn(component, "homePageBatchSelection");
      component.batchSelection("selectAcrossAllPage");
      expect(component.homePageBatchSelection).toHaveBeenCalled();
    });
  });
  xdescribe("ngOnInit", () => {
    it("makes expected calls", () => {
      spyOn(PodFilterComponent.prototype, "ngOnInit");
      spyOn(component, "getSelectedSortList");
      component.ngOnInit();
      expect(PodFilterComponent.prototype.ngOnInit).toHaveBeenCalled();
      expect(component.getSelectedSortList).toHaveBeenCalled();
    });
  });
  describe("searchAllTemplates", () => {
    it("makes expected calls", () => {
      const baseManagementServiceStub: BaseManagementService = fixture.debugElement.injector.get(BaseManagementService);
      spyOn(baseManagementServiceStub, 'fetchPaginationData');
      component.searchAllTemplates();
      expect(baseManagementServiceStub.fetchPaginationData).toHaveBeenCalled();
    });
  });
  describe("ngOndestroy", () => {
    it("makes expected calls", () => {
      spyOn(component, 'collapseAll');
      spyOn(component, "resetBatchSelections");
      component.ngOnDestroy();
      expect(component.collapseAll).toHaveBeenCalled();
    });
  });
  describe("getSortByValue", () => {
    it("makes expected calls", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(CommonSearchService);
      component.sortOptionFormGroupOne = new UntypedFormGroup({
        sortType: new UntypedFormControl("Asc")
      })
      spyOn(commonSearchServiceStub, "setQueryValueForDefaultOption");
      spyOn(component, "sortOfferTemplateSearch");
      component.getSortByValue();
      expect(commonSearchServiceStub.setQueryValueForDefaultOption).toHaveBeenCalled();
    });
  });
  describe("sortOfferTemplateSearch", () => {
    it("makes expected calls", () => {
      spyOn(component, "getCommonRemoveAndParamsList").and.returnValue({removeList: [], paramsList: []});
      spyOn(component, "searchAllTemplates");
      component.sortOfferTemplateSearch();
    });
  });
  describe("arrowClickHandler", () => {
    it("makes expected calls", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(CommonSearchService);
      component.sortOptionFormGroupOne = new UntypedFormGroup({
        sortType: new UntypedFormControl("Asc")
      })
      spyOn(commonSearchServiceStub, "setQueryValueForDefaultOption");
      spyOn(component, "sortOfferTemplateSearch");
      component.arrowClickHandler();
      expect(commonSearchServiceStub.setQueryValueForDefaultOption).toHaveBeenCalled();
    });
  });
  describe("getSelectedSortList", () => {
    it("makes expected calls", () => {
      component.sortOptionList = ["Last Modified"];
      spyOn(component, "setFormValue")
      component.getSelectedSortList();
      expect(component.setFormValue).toHaveBeenCalled();
    });
  });
});

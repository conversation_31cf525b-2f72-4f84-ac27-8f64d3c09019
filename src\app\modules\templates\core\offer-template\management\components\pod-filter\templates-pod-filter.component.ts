import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { TEMPLATE_CONSTANTS } from "@appModules/templates/constants/template_constants";
import { PodFilterComponent } from "@appShared/components/management/pod-filter/pod-filter.component";

@Component({
  selector: "template-pod-filter",
  templateUrl: "../../../../../../../shared/components/management/pod-filter/pod-filter.component.html",
  styleUrls: ["../../../../../../../shared/components/management/pod-filter/pod-filter.component.scss", "../../../../../../../shared/components/management/pod-filter/pod-filter-mq-component.scss"],
  styles: [
    `
    `,
  ],
})
export class TemplatePodFilterComponent extends PodFilterComponent implements OnInit, OnD<PERSON>roy {
  isHideExpandFeature = true;
  constructor() {
    super();
  }
  ngOnInit(): void {
    super.ngOnInit();
    this.initSubscribes_Child();
    this.getSelectedSortList();
  }
  initSubscribes_Child() {
    this.subs.sink = this.baseManagementService.templatesData$.subscribe((data: any) => {
      this.pageData = data ? data : null;
    });
  }

  getPermissionsBasedOnPage() {
    return this.allowedBatchPermissionsForOR;
  }
  getSelectedSortList() {
    this.sortOptionList = TEMPLATE_CONSTANTS.TEMPLATE_SEARCH_SORT_LIST_BPD;
    this.setFormValue(this.sortOptionFormGroupOne,this.sortOptionList[1].field,CONSTANTS.DESC);
  }
  arrowClickHandler() {
    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SORT_BY,`${this.sortOptionFormGroupOne.value.sortValue}${this.sortOptionFormGroupOne.value.sortType}`);
    this.sortOfferTemplateSearch();
  }
  getSortByValue() {
    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SORT_BY,`${this.sortOptionFormGroupOne.value.sortValue}${this.sortOptionFormGroupOne.value.sortType}`);
    this.sortOfferTemplateSearch();
  }
  sortOfferTemplateSearch() {
    this.searchAllTemplates(); 
  }
  searchAllTemplates() {
    this.baseManagementService.fetchPaginationData(true);
  }
  batchSelection(value) {
    this.batchType = value;
    this.homePageBatchSelection(value);
  }
  ngOnDestroy() {
    super.ngOnDestroy();
  }
}

<div class="list-item-container mb-2  ml-4" *ngIf="templateItem">
  <!-- data -->
  <section class="row">
    <div class="cb-width pt-2 ml-4">
      <div class="request-checkbox">
        <label class="mx-2 mb-0" for="input-checkbox{{ templateItem.info.id }}">
          <input
            type="checkbox"
            class="form-checkbox dark-checkbox"
            id="input-checkbox{{ templateItem.info.id }}"
            tabindex="0"
            [checked]="isSelected"
            [disabled]="bulkSelection"
            [class.isDisabled]="bulkSelection"
            (change)="selectIndividualRequest($event, templateItem)"
          />
        </label>
      </div>
    </div>
    <div class="col pt-2">
      <div class="row">
        <div class="col-1 align-templateId">
          <a class="requestIdLink" [routerLink]="getSummaryPage(templateItem.info.id)"
            ><label>{{ templateItem.info.id }} </label></a
          >
          <label class="bold-label d-block">{{ templateItem.info.mobId }} </label>
        </div>
        <div class="col-3 pr-4">
          <label class="text-label">{{ templateItem.info.brandAndSize }} </label>
          <label class="d-block">{{ templateItem.info.mobName }} </label>
        </div>
        <div class="col-2">
          <ng-container>
            <label class="text-label" class="word-break">{{ getFormattedRegionDisplayValue(templateItem.info.regionId) }}</label>
          </ng-container>
          <label class="d-block">{{ templateItem.info.repUpc }} </label>
        </div>
        <div class="col-2">
          <label class="text-label">
            {{ templateItem.info.cpg }}
          </label>
          <label class="d-block">{{ templateItem.info.bugm }} </label>
        </div>
        <div class="col-2">
          <label class="text-label">
            {{ templateItem.info.category }}
          </label>
          <label class="d-block">{{ templateItem.info.categoryId }} </label>
        </div>
        <div class="col-1">
          <div *ngIf="templateItem?.info?.otStatus" class="p-0">
            <label [class]="templateItem?.info?.otStatus ? getTemplateStatusClass(templateItem.info.otStatus) : ''"> {{ getTemplateStatus(templateItem.info.otStatus) }}</label>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

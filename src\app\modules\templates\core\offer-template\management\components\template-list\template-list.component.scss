@import "scss/statuses.scss";
@import "scss/inputs.scss";
@import "scss/colors";
.isDisabled {
  opacity: 0.4;
  cursor: default !important;
  pointer-events: none;
}
.list-item-container{
  border: 1px solid $table-row-bg;
  background: $table-row-bg;
}
.status-common-properties  {
  width: 80px;
  text-align: center;
  font-weight: 700;
}
.NEW-status {
  @extend .status-common-properties;
  border: 1px solid #0099A2;
  color: #0099A2;
}
.ACTIVE-status {
  @extend .status-common-properties;
  border: 1px solid #437F2F;
  color: #437F2F;
}
.REVIEW-status {
  @extend .status-common-properties;
  border: 1px solid #E79023;
  color: #E79023;
}
.NO_UPCS-status {
  @extend .status-common-properties;
  border: 1px solid#B84919;
  color:#B84919;
}
.PARKED-status {
  @extend .status-common-properties;
  border: 1px solid#841FA9;
  color:#841FA9;
}
.REMOVED-status {
  @extend .status-common-properties;
  border: 1px solid#DD1F26;
  color:#DD1F26;
}
.bold-label {
  font-weight: 700;
  font-size: 14px;
}
.align-templateId {
  @media screen and (min-width: 768px) {
      flex: 0 0 11.33333% !important;
      max-width: 11.33333% !important;
  }
}
import { TemplateListComponent } from "./template-list.component";
import { ComponentFixture, TestBed, waitForAsync } from "@angular/core/testing";
import { NO_ERRORS_SCHEMA, Injector } from "@angular/core";
import { TemplateManagementListService } from "@appTemplates/services/template-management-list-service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { AppInjector } from "@appServices/common/app.injector.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BaseTemplateListComponent } from "@appModules/templates/core/offer-template/management/components/base-template-list/base-template-list.component";

describe("TemplateListComponent", () => {
  const templateManagementListServiceStub = () => ({
    displayDigitalNonDigitalStatus: () => ({}),
  });
  const facetItemServiceStub = () => ({
      populateStoreFacet: (facets, storesSearchCriteria, divisionRogCds) => ({}),
      getFacetItems: () => ({}),
      getdivsionStateFacetItems: () => ({}),
      sortDivisionRogCds: () => ({}),
    }),
    bulkUpdateServiceStub = () => ({}),
    initialDataServiceStub = () => ({
      getAppData: () => ({})
    });

  let component: TemplateListComponent;
  let fixture: ComponentFixture<TemplateListComponent>;
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [TemplateListComponent],
      providers: [
        { provide: TemplateManagementListService, useFactory: templateManagementListServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },

        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
      ],
    })
      .compileComponents()
      .then(() => {
        AppInjector.setInjector(TestBed.inject(Injector));
        fixture = TestBed.createComponent(TemplateListComponent);
        component = fixture.componentInstance;
      });
  }));

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });
  it("ngOnInit calls initVariables()", () => {
    spyOn(BaseTemplateListComponent.prototype, "ngOnInit");
    component.ngOnInit();
  });
  describe("getTemplateStatusClass", () => {
    it("should call expected method", () => {
      const result = component.getTemplateStatusClass("REVIEW");
      expect(result).toEqual("REVIEW-status")
    });
  });
  describe("getTemplateStatus", () => {
    it("should call expected method", () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(InitialDataService);
      spyOn(initialDataServiceStub, "getAppData").and.returnValue({offerTemplateStatus: {ACTIVE: 'Active'}})
      const result = component.getTemplateStatus("ACTIVE");
      expect(result).toEqual("Active")
    });
    it("should call expected method in case if status is null", () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(InitialDataService);
      spyOn(initialDataServiceStub, "getAppData").and.returnValue({offerTemplateStatus: {ACTIVE: 'Active'}})
      const result = component.getTemplateStatus(null);
      expect(result).toEqual("")
    });
  });
});

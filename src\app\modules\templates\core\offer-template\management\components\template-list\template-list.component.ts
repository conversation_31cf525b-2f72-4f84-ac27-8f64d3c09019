import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { BaseTemplateListComponent } from "@appModules/templates/core/offer-template/management/components/base-template-list/base-template-list.component";
import { FacetItemService } from "@appServices/common/facet-item.service";

@Component({
  selector: "app-template-list",
  templateUrl: "./template-list.component.html",
  styleUrls: ["./template-list.component.scss"],
})
export class TemplateListComponent extends BaseTemplateListComponent implements OnInit {
  @Input("templateItem") templateItem: any;
  @ViewChild("loadDynamic") loadDynamicComponent;
  categoryColor;
  digitalStatus: any;
  constructor(public facetItemService: FacetItemService) {
    super();
  }
  ngOnInit(): void {
    super.ngOnInit();
  }
  getTemplateStatusClass(status) {
    if(status) {
      return `${status}-status`;
    }
  }
  getTemplateStatus(status) {
    const appData = this.initialDataService.getAppData();
    const { offerTemplateStatus } = appData;
    if(status && offerTemplateStatus) {
      return offerTemplateStatus[status];
    }
    return "";
  }
  getFormattedRegionDisplayValue(regionId) {
    if(!regionId) {
      return '';
    }
    const appData = this.initialDataService.getAppData();
    const { regions = [] } = appData;
    const selectedRegion = regions.filter(regionObj => regionObj.code === regionId);
    return selectedRegion ?  `${regionId} - ${selectedRegion[0]?.name}` : '';
  }

}

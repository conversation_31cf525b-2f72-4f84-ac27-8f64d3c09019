import { CONSTANTS } from "@appConstants/constants";
export const TEMPLATE_BATCH_RULES = {
    BPD: {
        components: ["TemplateBatchActionComponent"],
        actions: [
            {
                displayName: "Create Offer Requests",
                key: "createOfferRequest",
                permissionAllowed: [CONSTANTS.Permissions.Admin],
                modalClass : "modal-lg",
                doDirectAsyncCall: false,
                isFirstPreCheck: false,
                childActions: [],
                asyncActionKey:'CREATE',
                isOfferTemplate: true,
                includeOfferTemplateFlag: true,
                errSuccessModalClass : "modal-lg",
                errorMessage: "Cannot proceed. All selected Offer Templates must have Status = Active"

            },
            {
                displayName: "Export",
                key: "export",
                permissionAllowed: [CONSTANTS.Permissions.DoBatchExport],
                doDirectAsyncCall: true,
                isFirstPreCheck: false,
                includeOfferTemplateFlag: true,
                isOfferTemplate: true,
                childActions: [],
                jobType: "OT",
                apiFuncName: "bulkActionBpdRequest",
                postBatchSuccess: "getAllBpdRequest",
                asyncActionKey: "EXPORT",
                toastrMessage: "Offer template(s) are being exported"
            },
            {
                displayName: "Update Status",
                key: "updateStatus",
                permissionAllowed: [CONSTANTS.Permissions.Admin],
                doDirectAsyncCall: false,
                confirmationMsg: "",
                isFirstPreCheck: false,
                modalClass: "modal-md",
                childActions: [],
                asyncActionKey: "UPDATE_STATUS",
                apiFuncName:'',
                isResetSelection:false,
                includeOfferTemplateFlag: true,
                isOfferTemplate: true,
                postBatchSuccess: "getAllTemplates",
                jobType: "OT",
                toastrMessage: "Statuses are being updated",
                errorMessage:"",
                onPrecheckError: {
                    showOK: false,
                    showMore: false
                },
                errSuccessModalClass : "modal-lg",
            },
        ]
    }
}
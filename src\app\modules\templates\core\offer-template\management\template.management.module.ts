import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { BsModalRef, ModalModule } from 'ngx-bootstrap/modal';
import { ProgressbarModule } from 'ngx-bootstrap/progressbar';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { CommonModule } from '@angular/common';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { FacetsModule } from '@appShared/components/common/facets/facets.module';

import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';



import { TypeaheadModule } from 'ngx-bootstrap/typeahead';

import { CommentsModule } from '@appComments/comments.module';
import { AppCommonModule } from '@appCommon/app.common.module';
import { DigitDecimaNumberDirectiveModule } from '@appDirectives/digit-decimal/digit.decimal.module';
import { LetDirectiveModule } from "@appDirectives/let/let.module";
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { OnlyNumberDirectiveModule } from '@appDirectives/only-number/only.number.module';
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { LoadDynamicModule } from '@appModules/request/shared/components/load-dynamic/load-dynamic.module';
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { FilterHeaderModule } from '@appShared/components/management/filter-header/filter-header.module';
import { SidebarModule } from '@appShared/ng-sidebar';
import { NgxConfirmBoxModule, NgxConfirmBoxService } from '@appShared/ngx-confirm-box';
import { TemplateBaseComponent } from '@appTemplates/core/offer-template/base/templates.base.component';
import { TemplateManagementBaseContainer } from '@appTemplates/core/offer-template/management/components/base-container/template-management-base-container.comp';
import { TemplateRoutingModule } from '@appTemplates/routing/template-routing.module';



@NgModule({
    declarations: [
        TemplateManagementBaseContainer,
        TemplateBaseComponent
    ],
    exports: [
        TemplateManagementBaseContainer
    ],
    imports: [
        LoadDynamicModule,
        AppCommonModule,
        CommonModule,
        TemplateRoutingModule,
        FormsModule,
        ReactiveFormsModule,
        NgxDatatableModule,
        BsDatepickerModule.forRoot(),
        TabsModule.forRoot(),
        ModalModule.forRoot(),
        TypeaheadModule.forRoot(),
        SidebarModule.forRoot(),
        FacetsModule,
        ApiErrorsModule,
        DigitDecimaNumberDirectiveModule,
        VarDirectiveModule,
        LetDirectiveModule,
        ProgressbarModule.forRoot(),
        TooltipModule.forRoot(),
        NgxConfirmBoxModule,
        NgSelectModule,
        NgOptionHighlightModule,
        OnlyNumberDirectiveModule,
        MarkAsTouchedOnFocusDirectiveModule,
        CommentsModule,
        PermissionsModule.forChild(),
        FilterHeaderModule
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    providers: [{ provide: BsModalRef, useValue: undefined }, NgxConfirmBoxService]
})
export class TemplateDetailsModule { }

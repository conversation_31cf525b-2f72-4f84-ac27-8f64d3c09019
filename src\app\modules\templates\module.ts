import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AppCommonModule } from "@appCommon/app.common.module";
import { LetDirectiveModule } from "@appDirectives/let/let.module";
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { LoadDynamicModule } from '@appModules/request/shared/components/load-dynamic/load-dynamic.module';
import { OfferGridModule } from '@appOffers/shared/components/offer-grid/offer-grid.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { FacetsModule } from '@appShared/components/common/facets/facets.module';
import { FilterHeaderModule } from '@appShared/components/management/filter-header/filter-header.module';
import { routes } from '@appTemplates/router.module';
import { PopoverModule } from 'ngx-bootstrap/popover';

import { AppSearchFilterCommonModule } from '@appModules/common/app-search-filter.common.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { TemplateManagementBaseComponent } from '@appTemplates/core/offer-template/management/components/base/template-management-base.component';
import { TemplateInputSearchComponent } from '@appTemplates/core/offer-template/management/components/input-search/input-search.component';
import { TemplatePodFilterComponent } from '@appTemplates/core/offer-template/management/components/pod-filter/templates-pod-filter.component';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';

@NgModule({
  declarations: [
    TemplateManagementBaseComponent,
    TemplateInputSearchComponent,
    TemplatePodFilterComponent
  ],
  exports: [
    TemplateManagementBaseComponent
  ],
  imports: [
    AppSearchFilterCommonModule,
    RouterModule.forChild(routes),
    CommonModule,
    LoadDynamicModule,
    ApiErrorsModule,
    FacetsModule,
    FilterHeaderModule,
    VarDirectiveModule,
    LetDirectiveModule,
    NgSelectModule,
    BsDatepickerModule.forRoot(),
    AppCommonModule,
    OfferGridModule,
    PopoverModule.forRoot(),
    PermissionsModule.forChild()
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class TemplateManagementModule { }
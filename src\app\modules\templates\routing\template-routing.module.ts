import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { CanDeactivateGuard } from '@appServices/common/can-deactivate-guard.service';
import { PermissionsGuard } from '@appShared/albertsons-angular-authorization';
import { TemplateManagementBaseContainer } from '@appTemplates/core/offer-template/management/components/base-container/template-management-base-container.comp';
import { AuthGuard } from 'guard/auth.guard';

export const routes: Routes = [
  {
    path: ROUTES_CONST.TEMPLATES.Template,
    component: TemplateManagementBaseContainer,
    canActivate: [AuthGuard],
    children: [
      {
        path: ROUTES_CONST.TEMPLATES.TemplateForm,
        loadChildren: () => import('../core/offer-template/details/template-form.module').then(m => m.TemplateFormModule),
        canActivate: [AuthGuard, PermissionsGuard],
        canDeactivate: [CanDeactivateGuard],
        data: {
          permissions: {
            only: ['VIEW_OFFER_REQUESTS', 'VIEW_GR_SPD_OFFER_REQUESTS'],
            redirectTo: `/${ROUTES_CONST.NOTAUTHORIZED.NotAuthorized}`
          }
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TemplateRoutingModule { }

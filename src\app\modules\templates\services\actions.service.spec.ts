import { TestBed } from '@angular/core/testing';
import { ActionsService } from './actions.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';

describe('ActionsService', () => {
  let service: ActionsService;
  let permissionsServiceSpy: jasmine.SpyObj<PermissionsService>;

  beforeEach(() => {
    const permissionsServiceMock = jasmine.createSpyObj('PermissionsService', ['getPermissions']);

    TestBed.configureTestingModule({
      providers: [
        ActionsService,
        { provide: PermissionsService, useValue: permissionsServiceMock }
      ],
    });

    service = TestBed.inject(ActionsService);
    permissionsServiceSpy = TestBed.inject(PermissionsService) as jasmine.SpyObj<PermissionsService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return the correct added rules', () => {
    const actionRules  = ['read', 'write'];
    const rules = ['read', 'write', 'execute'];
    const result = service.getAddedRules(rules, actionRules);
    expect(result).toEqual(['read', 'write']);
  });

  it('should return the correct deleted rules', () => {
    const actionRules = ['read', 'execute'];
    const rules = ['read', 'write', 'execute'];
    const result = service.getDeletedRules(rules, actionRules);
    expect(result).toEqual(['read', 'execute']);
  });

  it('should add rules correctly to revisedPermissions', () => {
    const addPermissions = { 'module1': true };
    const rules = ['read', 'write', 'execute'];
    const permissions = { 'module1': { Detail: ['read', 'write'] } };
    const revisedPermissions: any[] = [];
    service.addRules(addPermissions, rules, permissions, revisedPermissions);
    expect(revisedPermissions).toEqual([['read', 'write']]);
  });

  it('should delete rules correctly from revisedPermissions', () => {
    const deletePermissions = { 'module1': true };
    const rules = ['read', 'write', 'execute'];
    const permissions = { 'module1': { Detail: ['read', 'write'] } };
    const revisedPermissions: any[] = [];
    service.deleteRules(deletePermissions, rules, permissions, revisedPermissions);
    expect(revisedPermissions).toEqual([['read', 'write']]);
  });

  it('should add and remove rules correctly and return revised permissions', () => {
    const response: any = {
      'module1': { Detail: ['read', 'write', 'execute'] },
      'module2': { Detail: ['read', 'delete'] },
    }
    permissionsServiceSpy.getPermissions.and.returnValue(response);

    const rules = ['read', 'write', 'execute'];
    const addPermissions = { 'module1': true };
    const deletePermissions = { 'module2': true };

    const result = service.addAndRemoveRules(rules, deletePermissions, addPermissions);

    expect(result).toEqual( ['read', 'write', 'execute', ['read', 'write', 'execute'], ['read', 'delete']]);
  });
});

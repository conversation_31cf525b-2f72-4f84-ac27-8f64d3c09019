import { Injectable } from '@angular/core';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';

@Injectable({
  providedIn: 'root'
})
export class ActionsService {

  constructor(  private _permissionsService: PermissionsService) { }
  getAddedRules(rules,actionRules){
    return actionRules.filter(ele=>rules.some(exist=>exist!==ele));
   }
   getDeletedRules(rules,actionRules){
     return actionRules.filter(ele=>rules.some(exist=>exist!==ele));
    }
   addRules(addPermissions,rules,permissions,revisedPermissions){
     Object.keys(addPermissions).forEach(permission => {
       const actions = permissions?.[permission] ,actionRules = actions?.["Detail"];
       if(actionRules){
         revisedPermissions.push(this.getAddedRules(rules,actionRules));
       }
     });
   }
   deleteRules(deletePermissions,rules,permissions,revisedPermissions){
     Object.keys(deletePermissions).forEach(permission => {
       const actions = permissions?.[permission] ,actionRules = actions?.["Detail"];
       if(actionRules){
         revisedPermissions.push(this.getDeletedRules(rules,actionRules));
       }
     });
   }
   addAndRemoveRules(rules,deletePermissions,addPermissions){
     const permissions = this._permissionsService.getPermissions(),
           revisedPermissions = [...(rules||[])];
     this.addRules(addPermissions,rules,permissions,revisedPermissions);
     this.deleteRules(deletePermissions,rules,permissions,revisedPermissions);
     return [...new Set(revisedPermissions)];
   }
}

import { Http<PERSON><PERSON>, <PERSON>tt<PERSON><PERSON>and<PERSON> } from '@angular/common/http';
import { Injector } from '@angular/core';
import { async, TestBed } from '@angular/core/testing';
import { UntypedFormArray, UntypedFormGroup, UntypedFormControl, UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { OR_RULES } from '@appRequest/shared/rules/OR.rules';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { OfferTemplateBaseService } from './offer-template-base.service';


describe('OfferTemplateBaseService', () => {
  let service: OfferTemplateBaseService;
  beforeEach(() => {
    const facetItemServiceStub = () => ({
      populateStoreFacet: (facets, storesSearchCriteria, divisionRogCds) => ({}),
      getFacetItems: () => ({}),
      getdivsionStateFacetItems: () => ({}),
      sortDivisionRogCds: () => ({}),
      
    });
    const toastrServiceStub = () => ({
      success: () => ({})
    });
    const routerStub = () => ({
      url: 'template/request'
    })
    const formBuilderStub = () => ({
      control: (arg) => ({}),
      array: (arr) => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
      getConfigUrls: bATCHIMPORT_TEMPLATE_FILE_API => ({})
    });
    const generalOfferTypeServiceStub = () => ({
      facetItemService: {
        programCodeSelected: 'SC'
      },
      createForm: () => new UntypedFormBuilder().group({}),
      generalInformationForm: new UntypedFormGroup({
        type: new UntypedFormControl("WOD / POD"),
        tiers: new UntypedFormControl("1"),
        version: new UntypedFormControl("1"),
      }),
      generalOfferTypeForm: new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("WOD / POD"),
          tiers: new UntypedFormControl("1"),
          version: new UntypedFormControl("1"),
        }),
      }),
      rules: OR_RULES,
      addNewRow$: new BehaviorSubject(""),
      isCopyVersion$: new BehaviorSubject(""),
      discountChangeTriggerdObj: [],
      createControlObject: (name, value, validate) => ({ name, value, validate }),
      createFormControl: (formControls) => new UntypedFormBuilder().group({}),
      product: new UntypedFormControl(2),
      tiers: new UntypedFormControl(2),
      type: new UntypedFormControl("Rewards - Flat"),
      formControls: {
        offerRequestOffers: new UntypedFormArray([
          new UntypedFormGroup({
            storeGroupVersion: new UntypedFormGroup({
              storeGroup: new UntypedFormControl("1"),
            }),
            discountVersion: new UntypedFormGroup({
              id: new UntypedFormControl("1"),
              discounts: new UntypedFormArray([
                new UntypedFormGroup({
                  displayOrder: new UntypedFormControl("1"),
                  id: new UntypedFormControl("1"),
                }),
              ]),
            }),
          }),
        ]),
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("WOD / POD"),
          tiers: new UntypedFormControl("1"),
          version: new UntypedFormControl("1"),
        }),
      },
      productComponent: [],
      versions: new UntypedFormArray([
        new UntypedFormGroup({
          discountVersion: new UntypedFormGroup({
            id: new UntypedFormControl("1"),
            discounts: new UntypedFormArray([
              new UntypedFormGroup({
                displayOrder: new UntypedFormControl("1"),
                id: new UntypedFormControl("1"),
              }),
            ]),
          }),
        }),
      ]),
      formBuilder: {
        array: () => ({}),
      },
      addFormControl: (name, formControl, form) => ({}),
      offerRequestOffersData: {},
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => (true),
      hasFlags: () => ({})
    });
    const requestFormServiceStub = () => ({
      prependZero: () => ({}),
      isValidTimeRule: () => ({}),
      offerRequestBaseService: "",
      setReqServiceVariables: (object) => ({}),
      hideApiErrorOnRequestMain: (object) => ({}),
      assignUserToOfferReq: () => ({}),
      parseOfferRequestOffers: () => ({}),
      selectedOfferLimitType$: { subscribe: () => ({}), next: () => ({}) },
      subscribeCurrentOfferReqForProcess: () => ({}),
      unAssignUserToOfferReq: () => ({}),
      requestForm: {
        controls: {
          offerReqGroup: { setValue: () => ({}) },
          nopaGroup: { setValue: () => ({}) },
          additionalDescriptionGroup: { setValue: () => ({}) },
          offerBuilderGroup: {
            controls: new UntypedFormGroup({
              digital: new UntypedFormControl("adf"),
              nonDigital: new UntypedFormControl("dfdf"),
            }),
            setValue: () => ({}),
          },
        },
        valid: {},
        reset: () => ({}),
        subscribe: () => ({}),
        markAsDirty: () => ({}),
        markAsUntouched: () => ({}),
        markAsPristine: () => ({}),
        get: () => ({}),
      },
      requestDigitalStatus: null,
      requestNonDigitalStatus: new BehaviorSubject(false),
      currentOfferRequest: new BehaviorSubject(false),
      passClonedObject$: new BehaviorSubject(false),
      requestStatus$: { subscribe: () => ({}), next: () => ({}) },
      requestDigitalStatus$: new BehaviorSubject(false),
      requestNonDigitalStatus$: new BehaviorSubject(false),
      requestData$: new BehaviorSubject(false),
      requestEditUpdateData$: { next: () => ({}) },
      updateReqDataKeys: (object) => ({}),
      selectedDeliveryChannel: {},
      selectedChannel$: { next: () => ({}) },
      isformSubmitAttempted: new BehaviorSubject(false),
      isJustificationBoolean: new BehaviorSubject(false),
      isEditNotificatonBoolean: new BehaviorSubject(false),
      isUpdateNotificationBoolean: new BehaviorSubject(false),
      isPreviousNDStatusUpdating$: new BehaviorSubject(false),
      isPreviousDGStatusUpdating$: new BehaviorSubject(false),
      mapFormDataToReqObj: () => ({}),
      saveOfferRequest: { bind: () => ({}) },
      offerRuleDay$: {
        next: () => ({}),
      },
      offerRuleTime$: {
        next: () => ({}),
      },
      onRouteChange$: { subscribe: () => ({}) },
      subscribeCurrentEditingUser: () => ({}),
      setUpdateNotification: (digitalStatus, nonDigitalStation) => ({}),
      isUpdateOffer: () => ({}),
      saveOR: () => ({}),
      resetOnDestroy: () => ({}),
    });
    const fileAttachServiceStub = () => ({
      downloadFile: (fileName, url) => ({})
  });
  const bsModalServiceStub = () => ({
    show: (template, options) => ({}),
    onHide: {
        subscribe: f => f({})
    }
});
const uploadImagesServiceStub = () => ({
  getImagesGroupData: (token) => ({}),
  getImage: () => ({ subscribe: (f) => f({}) }),
  sendLoading: (arg) => ({}),
  getImageID: (imageId) => ({ subscribe: (f) => f({}) }),
  sendImage: (imageId) => ({}),
});
const commonRouteServiceStub = () => ({
  currentRouter: () => ({ subscribe: () => ({}) })
});
const authServiceStub = () => ({ onUserDataAvailable: arg => ({}), getUserDetails: () => ({}) });
    TestBed.configureTestingModule({
      providers: [
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: UntypedFormBuilder, useFactory: formBuilderStub },
        { provide: RequestFormService, useFactory: requestFormServiceStub },
        { provide: Router, useFactory: routerStub },
        { provide: AuthService, useFactory: authServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: FileAttachService, useFactory: fileAttachServiceStub },
        { provide: GeneralOfferTypeService, useFactory: generalOfferTypeServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: BsModalService, useFactory: bsModalServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub},
        { provide: UploadImagesService, useFactory: uploadImagesServiceStub },
        HttpClient,
        HttpHandler,
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub }
      ],
    });
    AppInjector.setInjector(TestBed.inject(Injector));
    service = TestBed.inject(OfferTemplateBaseService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
  describe('setPodDataOnValueChanges', () => {
    it('should be created', () => {
      spyOn(service, "updatePodDataOnValueChanges");
      service.setPodDataOnValueChanges();
      expect(service.updatePodDataOnValueChanges).toHaveBeenCalled();
    });
  })
  describe('checkCustomValidate', () => {
    it('should be created', () => {
      spyOn(service, "checkCustomValidation");
      service.checkCustomValidate({});
      expect(service.checkCustomValidation).toHaveBeenCalled();
    });
  })
  describe('saveOT', () => {
    it('should be created', () => {
      spyOn(service, "doSave");
      service.saveOT();
      expect(service.doSave).toHaveBeenCalled();
    });
  });
  describe('setDisplayStartDate', () => {
    it('should be created', () => {
      spyOn(service, "updateDisplayStartDate");
      service.setDisplayStartDate();
      expect(service.updateDisplayStartDate).toHaveBeenCalled();
    });
  })
  describe('initializeRequestForm', () => {
    it('should initialize the request form', () => {
      spyOn(service, "initializeRequestForm").and.callThrough();
      service.initializeRequestForm();
      expect(service.initializeRequestForm).toHaveBeenCalled();
      expect(service.templateForm).toBe(service.requestFormService$.requestForm);
    });
  });
  
  describe('getControl', () => {
    it('should return the control from the base form', () => {
      const controlName = 'testControl';
      spyOn(service, 'getControlFromBase').and.returnValue(new UntypedFormControl());
      const control = service.getControl(controlName);
      expect(service.getControlFromBase).toHaveBeenCalledWith(controlName, service.templateForm);
      expect(control).toBeTruthy();
    });
  });

  describe('getFieldErrors', () => {
    it('should return the errors for the specified control', () => {
      const controlName = 'testControl';
      const control = new UntypedFormControl();
      spyOn(service, 'getControl').and.returnValue(control);
      spyOn(service, 'getErrorsForField').and.returnValue(['error1', 'error2']);
      const errors = service.getFieldErrors(controlName);
      expect(service.getControl).toHaveBeenCalledWith(controlName);
      expect(service.getErrorsForField).toHaveBeenCalledWith(controlName, control);
      expect(errors).toEqual(['error1', 'error2']);
    });
  });
  
});
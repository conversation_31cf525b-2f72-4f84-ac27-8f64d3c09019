import { Injectable } from '@angular/core';
import { TemplateRequestBaseService } from '@appTemplates/services/template-request-base/template-request-base.service';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class OfferTemplateBaseService extends TemplateRequestBaseService {
  templateData$ = new BehaviorSubject(null);
  templateForm = this.requestFormService$.requestForm;

  isRedirectRoute$ = new Subject();
  onRouteChange$ = new Subject();
  isDisplayNavigationWarning = true;
  isTemplateSavedFromModal$ = new BehaviorSubject(false);
  
  constructor() { 
    super();
   }
   setPodDataOnValueChanges(){
     this.updatePodDataOnValueChanges(this.templateForm);
   }
   checkCustomValidate(podDetails){
    this.checkCustomValidation(podDetails,this.templateForm,'template');
   }
   initializeRequestForm() {
    this.templateForm = this.requestFormService$.requestForm;
   }
   saveOT(actionLabel = "save") {
    this.doSave(this.templateForm, actionLabel, "offerTemplate");
    this.isTemplateSavedFromModal$.next(true);
   }
   setDisplayStartDate(){
     this.updateDisplayStartDate(this.templateForm);
   }
   getControl(ctrlName) {
    return this.getControlFromBase(ctrlName, this.templateForm);
  }
  getFieldErrors(ctrl) {
    let control = this.getControl(ctrl);
    return this.getErrorsForField(ctrl, control);
  }
}

import { TestBed } from '@angular/core/testing';
import { TemplateManagementListService } from './template-management-list-service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { of } from 'rxjs';

describe('TemplateManagementListService', () => {
    let service: TemplateManagementListService;
    let initialDataServiceStub: Partial<InitialDataService>;

    beforeEach(() => {
        initialDataServiceStub = {
            getAppData: () => ({ })
        };

        TestBed.configureTestingModule({
            providers: [
                TemplateManagementListService,
                { provide: InitialDataService, useValue: initialDataServiceStub }
            ]
        });

        service = TestBed.inject(TemplateManagementListService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should initialize configData with data from InitialDataService', () => {
        expect(service.configData).toEqual(initialDataServiceStub.getAppData());
    });

    it('should emit values through templatesData$', (done) => {
        const testValue = { key: 'value' };
        service.templatesData$.subscribe(value => {
          expect(value).toEqual(testValue);
          done();
        });
        service.templatesData$.next(testValue);
    });
});

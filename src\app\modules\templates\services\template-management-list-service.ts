import { Injectable } from "@angular/core";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { Subject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class TemplateManagementListService {
  public templatesData$ = new Subject();

  appData;
  configData;
  digitalStatus: any;
  nonDigitalStatus: any;
  statusClassName: any;

  constructor(
    private initialDataService: InitialDataService
  ) {
    this.configData = this.initialDataService.getAppData();
  }
}

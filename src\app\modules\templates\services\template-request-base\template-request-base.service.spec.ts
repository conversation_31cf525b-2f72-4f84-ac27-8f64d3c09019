import { Http<PERSON><PERSON>, <PERSON>tt<PERSON><PERSON>and<PERSON> } from '@angular/common/http';
import { Injector } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { UntypedFormGroup, UntypedFormControl, UntypedFormBuilder, UntypedFormArray, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { OR_RULES } from '@appRequest/shared/rules/OR.rules';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Subject, throwError, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { CONSTANTS } from "@appConstants/constants";
import { TemplateRequestBaseService } from './template-request-base.service';
import { ROUTES_CONST } from '@appConstants/routes_constants';

const DateUtils = {
  dateInOriginalFormat: jasmine.createSpy('dateInOriginalFormat')
};

declare global {
  interface Window {
    updateTreeValidity: jasmine.Spy;
  }
}

declare global {
  interface Window {
    mmDdYyyySlash_DateFormat: (date: string) => string;
  }
}


declare function mmDdYyyySlash_DateFormatWithoutUTC(date: string): string;

describe('TemplateRequestBaseService', () => {
  let service: TemplateRequestBaseService;
  let formBuilder: UntypedFormBuilder = new UntypedFormBuilder();
  let router: Router;
  let fileAttachService: FileAttachService;
  let featureFlagService: FeatureFlagsService;
  let generalOfferTypeService: GeneralOfferTypeService;
  let toastrService: ToastrService;
  let uploadImagesService: UploadImagesService;

  beforeEach(async () => {
    const facetItemServiceStub = () => ({
      populateStoreFacet: (facets, storesSearchCriteria, divisionRogCds) => ({}),
      getFacetItems: () => ({}),
      getdivsionStateFacetItems: () => ({}),
      sortDivisionRogCds: () => ({}),
      templateProgramCodeSelected: 'BPD'
    });
    const toastrServiceStub = () => ({
      success: () => ({}),
      warning:jasmine.createSpy('warning'),
    });
    const routerStub = () => ({
      url: 'template/request'
    })
    const formBuilderStub = () => ({
      control: (arg) => ({}),
      array: (arr) => ({}),
      group: (controlsConfig) => new UntypedFormGroup(controlsConfig)
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
      getConfigUrls: bATCHIMPORT_TEMPLATE_FILE_API => ({})
    });
    const generalOfferTypeServiceStub = () => ({
      facetItemService: {
        programCodeSelected: 'SC'
      },
      createForm: () => formBuilder.group({}),
      generalInformationForm: new UntypedFormGroup({
        type: new UntypedFormControl("WOD / POD"),
        tiers: new UntypedFormControl("1"),
        version: new UntypedFormControl("1"),
        isRedeemableInSameTransaction: new UntypedFormControl(false),
      }),
      generalOfferTypeForm: new UntypedFormGroup({
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("WOD / POD"),
          tiers: new UntypedFormControl("1"),
          version: new UntypedFormControl("1"),
        }),
      }),
      rules: OR_RULES,
      addNewRow$: new BehaviorSubject(""),
      isCopyVersion$: new BehaviorSubject(""),
      discountChangeTriggerdObj: [],
      createControlObject: (name, value, validate) => ({ name, value, validate }),
      createFormControl: (formControls) => formBuilder.group({}),
      product: new UntypedFormControl(2),
      tiers: new UntypedFormControl(2),
      type: new UntypedFormControl("Rewards - Flat"),
      formControls: {
        offerRequestOffers: new UntypedFormArray([
          new UntypedFormGroup({
            storeGroupVersion: new UntypedFormGroup({
              storeGroup: new UntypedFormControl("1"),
            }),
            discountVersion: new UntypedFormGroup({
              id: new UntypedFormControl("1"),
              discounts: new UntypedFormArray([
                new UntypedFormGroup({
                  displayOrder: new UntypedFormControl("1"),
                  id: new UntypedFormControl("1"),
                }),
              ]),
            }),
          }),
        ]),
        generalInformationForm: new UntypedFormGroup({
          type: new UntypedFormControl("WOD / POD"),
          tiers: new UntypedFormControl("1"),
          version: new UntypedFormControl("1"),
        }),
      },
      productComponent: [],
      versions: new UntypedFormArray([
        new UntypedFormGroup({
          discountVersion: new UntypedFormGroup({
            id: new UntypedFormControl("1"),
            discounts: new UntypedFormArray([
              new UntypedFormGroup({
                displayOrder: new UntypedFormControl("1"),
                id: new UntypedFormControl("1"),
              }),
            ]),
          }),
        }),
      ]),
      formBuilder: {
        array: () => ({}),
      },
      addFormControl: (name, formControl, form) => ({}),
      offerRequestOffersData: {},
      getKeyByValue: (obj, value) => value === 'someType' ? 'WOD_OR_POD' : 'OTHER_TYPE',
      createFormControls: () => ({
        offerRequestOffers: new UntypedFormArray([]),
        generalInformationForm: new UntypedFormGroup({}),
      }),
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => (true),
      hasFlags: () => ({}),
      isEnableCustomUsageField:()=>({}),
      checkFeatureFlagEnabled: () => true,
    });
    const requestFormServiceStub = () => ({
      mmDdYyyySlash_DateFormat: () => '12/31/2024',
      prependZero: () => ({}),
      isValidTimeRule: () => ({}),
      offerRequestBaseService: "",
      checkFulfillmentChannelEnabled: () => true, 
      cachedDigitalOfferStatus: "Digital_Cached_Value", 
      cachedNonDigitalOfferStatus: "Non_Digital_Cached_Value", 
      setReqServiceVariables: (object) => ({}),
      hideApiErrorOnRequestMain: (object) => ({}),
      assignUserToOfferReq: () => ({}),
      parseOfferRequestOffers: () => ({}),
      selectedOfferLimitType$: { subscribe: () => ({}), next: () => ({}) },
      subscribeCurrentOfferReqForProcess: () => ({}),
      unAssignUserToOfferReq: () => ({}),
      requestForm: {
        controls: {
          offerReqGroup: { setValue: () => ({}) },
          nopaGroup: { setValue: () => ({}) },
          additionalDescriptionGroup: { setValue: () => ({}) },
          offerBuilderGroup: {
            controls: new UntypedFormGroup({
              digital: new UntypedFormControl("adf"),
              nonDigital: new UntypedFormControl("dfdf"),
            }),
            setValue: () => ({}),
          },
        },
        valid: {},
        reset: () => ({}),
        subscribe: () => ({}),
        markAsDirty: () => ({}),
        markAsUntouched: () => ({}),
        markAsPristine: () => ({}),
        get: () => ({}),
        value: {
          offerRuleTimeGroup: {
            start: '14:30',
            end: '16:45'
          }
        }
      },
      requestDigitalStatus: null,
      requestNonDigitalStatus: new BehaviorSubject(false),
      currentOfferRequest: new BehaviorSubject(false),
      passClonedObject$: new BehaviorSubject(false),
      requestStatus$: { subscribe: () => ({}), next: () => ({}) },
      requestDigitalStatus$: new BehaviorSubject(false),
      requestNonDigitalStatus$: new BehaviorSubject(false),
      requestData$: new BehaviorSubject(false),
      requestEditUpdateData$: { next: () => ({}) },
      updateReqDataKeys: (object) => ({}),
      selectedDeliveryChannel: {},
      selectedChannel$: { next: () => ({}) },
      isformSubmitAttempted: new BehaviorSubject(false),
      isJustificationBoolean: new BehaviorSubject(false),
      isEditNotificatonBoolean: new BehaviorSubject(false),
      isUpdateNotificationBoolean: new BehaviorSubject(false),
      isPreviousNDStatusUpdating$: new BehaviorSubject(false),
      isPreviousDGStatusUpdating$: new BehaviorSubject(false),
      mapFormDataToReqObj: () => ({}),
      saveOfferRequest: { bind: () => ({}) },
      offerRuleDay$: {
        next: () => ({}),
      },
      offerRuleTime$: {
        next: () => ({}),
      },
      onRouteChange$: { subscribe: () => ({}) },
      subscribeCurrentEditingUser: () => ({}),
      setUpdateNotification: (digitalStatus, nonDigitalStation) => ({}),
      isUpdateOffer: () => ({}),
      saveOR: () => ({}),
      resetOnDestroy: () => ({}),
    });
    const fileAttachServiceStub = () => ({
      downloadFile: (fileName, url) => ({}),
      uploadFile:(file, id) => ({})
    });
    const bsModalServiceStub = () => ({
      show: (template, options) => ({}),
      onHide: {
          subscribe: f => f({})
      }
    });
    const uploadImagesServiceStub = () => ({
      getImagesGroupData: (token) => ({}),
      getImage: () => ({ subscribe: (f) => f({}) }),
      sendLoading: (arg) => ({}),
      getImageID: (imageId) => ({ subscribe: (f) => f({}) }),
      sendImage: (imageId) => ({}),
    });
    const commonRouteServiceStub = () => ({
      currentRouter: () => ({ subscribe: () => ({}) }),
      get isBpdReqPage() { return false; }, 
    });
    const authServiceStub = () => ({ 
      getTokenString: () => ({}),
      onUserDataAvailable: () => ({}),
    });
    await TestBed.configureTestingModule({
      providers: [
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: UntypedFormBuilder, useFactory: formBuilderStub },
        { provide: RequestFormService, useFactory: requestFormServiceStub },
        { provide: Router, useFactory: routerStub },
        { provide: AuthService, useFactory: authServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: FileAttachService, useFactory: fileAttachServiceStub },
        { provide: GeneralOfferTypeService, useFactory: generalOfferTypeServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: BsModalService, useFactory: bsModalServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub},
        { provide: UploadImagesService, useFactory: uploadImagesServiceStub },
        HttpClient,
        HttpHandler,
        { provide: CommonRouteService, useFactory: commonRouteServiceStub }
      ],
    });
    AppInjector.setInjector(TestBed.inject(Injector));
    service = TestBed.inject(TemplateRequestBaseService);
    router = TestBed.inject(Router);
    fileAttachService = TestBed.inject(FileAttachService);
    featureFlagService = TestBed.inject(FeatureFlagsService);
    formBuilder = TestBed.inject(UntypedFormBuilder);
    generalOfferTypeService = TestBed.inject(GeneralOfferTypeService);
    uploadImagesService = TestBed.inject(UploadImagesService);
    toastrService = TestBed.inject(ToastrService);
  
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
  describe('addAllocation', () => {
    it('should be created', () => {
      spyOn(service, "getControlFromBase").and.returnValue(new UntypedFormControl(''));
      service.allocationDataList = new Subject();
      const obj = {newAllocation: {allocationCode: "01", allocationCodeName: "01 - Default"}, allocationData: {}};
      service.addAllocation(obj, new UntypedFormGroup({}));
      expect(service.getControlFromBase).toHaveBeenCalled();
    });
  });

  describe('getProgramCode', () => {
    it('should return templateProgramCodeSelected when isTemplateRouteActivated is true', () => {
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(true);
      service.facetItemService$.templateProgramCodeSelected = 'SPD';
      expect(service.getProgramCode()).toBe('SPD');
    });
  
    it('should return programCodeSelected when isTemplateRouteActivated is false', () => {
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(false);
      service.facetItemService$.programCodeSelected = 'SPD';
      expect(service.getProgramCode()).toBe('SPD');
    });
  });

  describe('openModal', () => {
    it('should call modalService$.show with the correct parameters', () => {
      const template = 'template';
      const options = { class: 'modal-lg' };
      const showSpy = spyOn(service.modalService$, 'show').and.callThrough();
  
      service.openModal(template, options);
  
      expect(showSpy).toHaveBeenCalledWith(template, options);
    });
  });

  describe('resetVars', () =>{
    it('should set minDisplayEndDate to null', () => {
      service.resetVars();
      expect(service.minDisplayEndDate).toBeNull();
    });
  
    it('should set minOfferEndDate to current date', () => {
      const currentDate = new Date();
      service.resetVars();
      expect(service.minOfferEndDate.toDateString()).toBe(currentDate.toDateString());
    });
  
    it('should set minOfferStartDate to current date', () => {
      const currentDate = new Date();
      service.resetVars();
      expect(service.minOfferStartDate.toDateString()).toBe(currentDate.toDateString());
    });
  });

  describe('getProgramCodeDisplayName', () => {
    it('should return the programCode from getProgramCodeRule', () => {
      const mockProgramCodeRule = { programCode: 'SPD' };
      spyOn(service, 'getProgramCodeRule').and.returnValue(mockProgramCodeRule);

      const result = service.getProgramCodeDisplayName();

      expect(result).toBe('SPD');
      expect(service.getProgramCodeRule).toHaveBeenCalled();
    });
  });

  describe('getProgramCodeComponents', () => {
    it('should return the components from getProgramCodeRule', () => {
      const mockProgramCodeRule = { components: ['component1', 'component2'] };
      spyOn(service, 'getProgramCodeRule').and.returnValue(mockProgramCodeRule);

      const result = service.getProgramCodeComponents();

      expect(result).toEqual(['component1', 'component2']);
      expect(service.getProgramCodeRule).toHaveBeenCalled();
    });
  });

  describe('getProgramCodeOfferRequest', () => {
    it('should return the offerRequest from getProgramCodeRule', () => {
      const mockProgramCodeRule = { offerRequest: 'offerRequest1' };
      spyOn(service, 'getProgramCodeRule').and.returnValue(mockProgramCodeRule);

      const result = service.getProgramCodeOfferRequest();

      expect(result).toBe('offerRequest1');
      expect(service.getProgramCodeRule).toHaveBeenCalled();
    });
  });

  describe('getDefaultFormControls', () => {
    it('should return the correct default form controls', () => {
      const expectedControls = {
        lastUpdatedTs: {},
        createdApplicationId: {},
        createdTs: {},
        createdUserId: {},
        id: {
          value: null,
          control: ["info", "id"],
        },
        deliveryChannel: {
          value: "DO",
          control: ["info", "deliveryChannel"],
        },
        programCode: {
          value: service.facetItemService$.programCodeSelected,
          control: ["info", "programCode"],
        },
        numOfVersions: {
          value: null,
          control: ["info", "numOfVersions"],
        },
        numOfTiers: {
          value: null,
          control: ["info", "numOfTiers"],
        },
        numOfProducts: {
          value: null,
          control: ["info", "numOfProducts"],
        },
        digitalUser: {
          value: null,
          control: ["info", "digitalUser"],
        },
        nonDigitalUser: {
          value: null,
          control: ["info", "nonDigitalUser"],
        },
        digitalStatus: {
          value: null,
          control: ["info", "digitalStatus"],
        },
        nonDigitalStatus: {
          value: null,
          control: ["info", "nonDigitalStatus"],
        },
        digitalEditStatus: {
          value: null,
          control: ["info", "digitalEditStatus"],
        },
        nonDigitalEditStatus: {
          value: null,
          control: ["info", "nonDigitalEditStatus"],
        },
        digitalUiStatus: {
          value: null,
          control: ["info", "digitalUiStatus"],
        },
        nonDigitalUiStatus: {
          value: null,
          control: ["info", "nonDigitalUiStatus"],
        },
        mobId: {
          value: null,
          control: ["info", "mobId"],
        },
        autoAssignMob: {
          value: true,
          control: ["info", "autoAssignMob"],
        },
        mobName: {
          value: null,
          control: ["info", "mobName"],
        },
      };

      const result = service.getDefaultFormControls();

      expect(result).toEqual(expectedControls);
    });
  });

  describe('setPriceUntilDateConversion', () => {
    it('should be called when executed', () => {
      const form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          priceUntil: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl()
          })
        })
      });
      spyOn(service, 'setPriceUntilDateConversion').and.callThrough();
      service.setPriceUntilDateConversion(form);
      expect(service.setPriceUntilDateConversion).toHaveBeenCalled();
    });
  });

  describe('setOTStatusUntilDateConversion', () => {
    it('should be called when executed', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatusSetUntil: new UntypedFormControl('2023-10-10')
        })
      });
      spyOn(service, 'setOTStatusUntilDateConversion').and.callThrough();
      service.setOTStatusUntilDateConversion(form);
      expect(service.setOTStatusUntilDateConversion).toHaveBeenCalled();
    });
  });

  describe('setOTStatusFields', () => {
    it('should set otStatusReason and otStatusSetUntil to null if otStatus is not PARKED or REMOVED', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('ACTIVE'),
          otStatusReason: new UntypedFormControl('Some reason'),
          otStatusSetUntil: new UntypedFormControl('2023-10-10'),
          otStatusReasonComment: new UntypedFormControl('Some comment')
        })
      });
  
      service.setOTStatusFields(form);
  
      expect(form.get('info.otStatusReason').value).toBeNull();
      expect(form.get('info.otStatusSetUntil').value).toBeNull();
      expect(form.get('info.otStatusReasonComment').value).toBeNull();
    });
  
    it('should not change otStatusReason, otStatusSetUntil, and otStatusReasonComment if otStatus is PARKED', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('PARKED'),
          otStatusReason: new UntypedFormControl('Some reason'),
          otStatusSetUntil: new UntypedFormControl('2023-10-10'),
          otStatusReasonComment: new UntypedFormControl('Some comment')
        })
      });
  
      service.setOTStatusFields(form);
  
      expect(form.get('info.otStatusReason').value).toBe('Some reason');
      expect(form.get('info.otStatusSetUntil').value).toBe('2023-10-10');
      expect(form.get('info.otStatusReasonComment').value).toBe('Some comment');
    });
  })  


  describe('setScene7FieldError', () => {
    it('should be called when executed', () => {
      spyOn(service, 'setScene7FieldError').and.callThrough();
      service.setScene7FieldError();
      expect(service.setScene7FieldError).toHaveBeenCalled();
    });
  });
  

  describe('isScene7Invalid', () => {
    beforeEach(() => {
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  scene7ImageId: new UntypedFormControl('12345')
                })
              })
            })
          ])
        })
      } as any;
    });
  
    it('should return true if scene7ImageIdControl is invalid and has customError', () => {
      service.facetItemService$.programCodeSelected = CONSTANTS.GR;
      const scene7ImageIdControl = service.scene7ImageIdControl;
      scene7ImageIdControl.setErrors({ customError: true });
      spyOnProperty(scene7ImageIdControl, 'invalid', 'get').and.returnValue(true);
  
      const result = service.isScene7Invalid();
  
      expect(result).toBeTrue();
    });
  
  });

  describe('getControlFromBase', () => {
    it('should return the correct form control based on ctrlName and form structure', () => {
      const mockProgramCodeRule = {
        item1: {
          ctrlName1: { control: ['info', 'control1'] }
        },
        item2: {
          ctrlName2: { control: ['info', 'control2'] }
        }
      };
      spyOn(service, 'getProgramCodeRule').and.returnValue(mockProgramCodeRule);
  
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          control1: new UntypedFormControl('value1'),
          control2: new UntypedFormControl('value2')
        })
      });
  
      const result = service.getControlFromBase('ctrlName1', form);
      expect(result.value).toBe('value1');
  
      const result2 = service.getControlFromBase('ctrlName2', form);
      expect(result2.value).toBe('value2');
    });
  
    it('should return undefined if the control is not found', () => {
      const mockProgramCodeRule = {
        item1: {
          ctrlName1: { control: ['info', 'control1'] }
        }
      };
      spyOn(service, 'getProgramCodeRule').and.returnValue(mockProgramCodeRule);
  
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          control1: new UntypedFormControl('value1')
        })
      });
  
      const result = service.getControlFromBase('ctrlName2', form);
      expect(result).toBeUndefined();
    });
  });

  describe('getErrorsForField', () => {
    it('should return errors for scene7ImageId control', () => {
      const control = new UntypedFormControl();
      control.setErrors({ customError: true });

      const result = service.getErrorsForField('scene7ImageId', control);

      expect(result).toEqual({ customError: true });
    });

    it('should return errors if isDraftSaveAttempted is true and control is untouched', () => {
      service.isDraftSaveAttempted = true;
      const control = new UntypedFormControl();
      control.markAsUntouched();
      control.setErrors({ required: true });

      const result = service.getErrorsForField('someControl', control);

      expect(result).toEqual({ required: true });
    });
  }) 
  
  
  describe('isScene7Invalid', () => {

    beforeEach(() => {
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  scene7ImageId: new UntypedFormControl('12345')
                })
              })
            })
          ])
        })
      } as any;
    });

    it('should return true if scene7ImageIdControl is invalid and has customError', () => {
      service.facetItemService$.programCodeSelected = CONSTANTS.GR;
      const scene7ImageIdControl = service.scene7ImageIdControl;
      scene7ImageIdControl.setErrors({ customError: true });
      spyOnProperty(scene7ImageIdControl, 'invalid', 'get').and.returnValue(true);

      const result = service.isScene7Invalid();

      expect(result).toBeTrue();
    });
  }) 

  describe('setValidators', () => {
    it('should be called when executed', () => {
      spyOn(service, 'setValidators').and.callThrough();
      const mockFormData = [
        {
          formGroup: new UntypedFormControl(),
          byPassValidateBeforeProcess: false,
          validate: ['ACTION_1'],
          validators: [() => null] 
        }
      ];
      const mockAction = 'ACTION_1';
      const mockForm = new UntypedFormGroup({});
      service.setValidators(mockFormData, mockAction, mockForm);
      expect(service.setValidators).toHaveBeenCalled();
    });
  });
  


  describe('isByPassValidateBeforeProcess', () => {
    it('should return true if byPassValidateBeforeProcess is false', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          digitalStatus: new UntypedFormControl('A')
        })
      });

      const result = service.isByPassValidateBeforeProcess(false, 'save', form);

      expect(result).toBeTrue();
    });

    it('should return true if action is "process"', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          digitalStatus: new UntypedFormControl('A')
        })
      });

      const result = service.isByPassValidateBeforeProcess(true, 'process', form);

      expect(result).toBeTrue();
    });
  }) 

  describe('getFormDetailsForValidators', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({});
      spyOn(service, 'getProgramCodeRule').and.returnValue({
        offerRequest: {
          field1: { control: ['field1'], validators: ['required'], validate: ['ACTION_1'] },
          field2: { control: ['field2'], validators: ['email'], validate: ['ACTION_2'] }
        },
        podDetails: {}
      });
  
      spyOn(service, 'getCustomValidator').and.returnValue([(control: UntypedFormControl) => ({ customError: true })]);
      spyOn(service, 'getCustomValidate').and.returnValue(['customValidate']);
      spyOn(service, 'getFormGroup').and.callFake((control, form) => {
        return form.get(control[0]);
      });
    });
  
    it('should be called when executed', () => {
      spyOn(service, 'getFormDetailsForValidators').and.callThrough();
  
      service.getFormDetailsForValidators(form);
  
      expect(service.getFormDetailsForValidators).toHaveBeenCalled();
    });
  
    it('should call getProgramCodeRule', () => {
      service.getFormDetailsForValidators(form);
      expect(service.getProgramCodeRule).toHaveBeenCalled();
    });
  }) 

  describe('getCustomValidator', () => {
    beforeEach(() => {
      spyOn(service, 'getCustomValidator').and.callThrough();
    });
  
    it('should be called when executed', () => {
      const mockFieldObj = ['customPeriod'];
      service.getCustomValidator(mockFieldObj);
      expect(service.getCustomValidator).toHaveBeenCalled();
    });
  });

  describe('validateActionForSubType', () => {
    beforeEach(() => {
      spyOn(service, 'validateActionForSubType').and.callThrough();
      service.generalOfferTypeServic$ = {
        requestFormService: {
          offerRequestBaseService: {
            requestForm: {
              controls: {
                info: new UntypedFormGroup({
                  programType: new UntypedFormControl('HEALTH')
                })
              }
            }
          }
        }
      } as any;
    });
  
    it('should be called when executed', () => {
      service.validateActionForSubType();
      expect(service.validateActionForSubType).toHaveBeenCalled();
    });
  }) 
  
  describe('getCustomValidate', () => {
    beforeEach(() => {
      spyOn(service, 'getCustomValidate').and.callThrough();
    });
  
    it('should be called when executed', () => {
      const mockFieldObj = ['customPeriod'];
      service.getCustomValidate(mockFieldObj);
      expect(service.getCustomValidate).toHaveBeenCalled();
    });
  });


  describe('Validation Functions', () => {
    let control: UntypedFormControl;
  
    beforeEach(() => {
      control = new UntypedFormControl(1);
  
      service.generalOfferTypeServic$ = {
        requestFormService: {
          offerRequestBaseService: {
            requestForm: {
              controls: {
                rules: new UntypedFormGroup({
                  usageLimitPerUser: new UntypedFormControl(1)
                })
              }
            }
          }
        }
      } as any;
    });
  
    it('should call validateCustomPeriodRule when executed', () => {
      spyOn(service, 'validateCustomPeriodRule').and.callThrough();
  
      service.validateCustomPeriodRule(control);
  
      expect(service.validateCustomPeriodRule).toHaveBeenCalled();
    });
  
    it('should call validateCustomFields when executed', () => {
      spyOn(service, 'validateCustomFields').and.callThrough();
  
      service.validateCustomFields(control);
  
      expect(service.validateCustomFields).toHaveBeenCalled();
    });
  
    it('should call validateCustomField when executed', () => {
      spyOn(service, 'validateCustomField').and.callThrough();
  
      const validatorFn = service.validateCustomField();
      validatorFn(control);
  
      expect(service.validateCustomField).toHaveBeenCalled();
    });
  
  });

  describe('validateRX and validateRXOffers', () => {
    let control: UntypedFormControl;
  
    beforeEach(() => {
      control = new UntypedFormControl('COUPONS');
      service.generalOfferTypeServic$ = {
        requestFormService: {
          offerRequestBaseService: {
            requestForm: {
              controls: {
                info: new UntypedFormGroup({
                  programType: new UntypedFormControl('HEALTH')
                })
              }
            }
          }
        }
      } as any;
    });
  
    it('should call validateRX when executed', () => {
      spyOn(service, 'validateRX').and.callThrough();
  
      const validatorFn = service.validateRX();
      validatorFn(control);
  
      expect(service.validateRX).toHaveBeenCalled();
    });
  
    it('should call validateRXOffers when executed', () => {
      spyOn(service, 'validateRXOffers').and.callThrough();
  
      service.validateRXOffers(control);
  
      expect(service.validateRXOffers).toHaveBeenCalled();
    });
  });


  describe('getFormGroup', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          programCode: new UntypedFormControl('SPD')
        }),
        rules: new UntypedFormGroup({
          priceUntil: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl()
          })
        })
      });

      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  scene7ImageId: new UntypedFormControl()
                })
              })
            })
          ])
        })
      } as any;
    });

    it('should return form control from storeGroupVersion when podDetails is "podDetails"', () => {
      const controls = ['podDetails', 'scene7ImageId'];
      const result = service.getFormGroup(controls, form);

      expect(result).toBe((service.generalOfferTypeServic$.generalOfferTypeForm.get('offerRequestOffers') as UntypedFormArray).at(0).get('storeGroupVersion').get('podDetails').get('scene7ImageId'));
    });

    it('should return form control from form when podDetails is not "podDetails"', () => {
      const controls = ['info', 'programCode'];
      const result = service.getFormGroup(controls, form);

      expect(result).toBe(form.get('info').get('programCode'));
    });
  });

  xdescribe('templateValidation', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('ACTIVE')
        })
      });

      spyOn(service, 'notifySaveOrSubmitAttempt').and.callThrough();
      spyOn(service, 'setScene7FieldError').and.callThrough();
      spyOn(service, 'isFormValid').and.returnValue(true);
      spyOn(service, 'isScene7Invalid').and.returnValue(false);
    });

    afterEach(() => {
      (service.notifySaveOrSubmitAttempt as jasmine.Spy).calls.reset();
      (service.setScene7FieldError as jasmine.Spy).calls.reset();
      (service.isFormValid as jasmine.Spy).calls.reset();
      (service.isScene7Invalid as jasmine.Spy).calls.reset();
    });

    it('should return false if otStatus is ACTIVE and isFormValid returns false', () => {
      (service.isFormValid as jasmine.Spy).and.returnValue(false);
      const validateSaveSubmit = true;

      const result = service.templateValidation(form, validateSaveSubmit);

      expect(result).toBeFalse();
    });

    it('should return false if otStatus is ACTIVE and isScene7Invalid returns true', () => {
      (service.isScene7Invalid as jasmine.Spy).and.returnValue(true);
      const validateSaveSubmit = true;

      const result = service.templateValidation(form, validateSaveSubmit);

      expect(result).toBeFalse();
    });
  })

  describe('saveRequestTemplate', () => {
    let form: UntypedFormGroup;
    let url: string;
    let toaster: string;
    let validateSaveSubmit: boolean;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('ACTIVE')
        }),
        rules: new UntypedFormGroup({
          priceUntil: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl()
          })
        })
      });
  
      url = 'test-url';
      toaster = 'test-toaster';
      validateSaveSubmit = true;
  
      spyOn(service, 'templateValidation').and.returnValue(true);
      spyOn(service, 'requestValidation').and.returnValue(true);
      spyOn(service, 'updateAllocationCode').and.callThrough();
      spyOn(service, 'setGRGetMiles').and.callThrough();
      spyOn(service, 'updateDateFormatOnSave').and.callThrough();
      spyOn(service, 'setPodStoreGroupsForSpd').and.callThrough();
      spyOn(service, 'setQualificationAndBenefit').and.callThrough();
      spyOn(service, 'setOfferRequestType').and.callThrough();
      spyOn(service, 'updateTimeandDate').and.callThrough();
      spyOn(service, 'setAdditionConvertTypes').and.callThrough();
      spyOn(service, 'setCachedDigitalData').and.callThrough();
      spyOn(service, 'removeRewardsRequired').and.callThrough();
      spyOn(service.authService$, 'onUserDataAvailable').and.callFake((callback) => callback());
      spyOn(service, 'saveOfferRequest').and.callThrough();
  
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  scene7ImageId: new UntypedFormControl()
                })
              })
            })
          ])
        })
      } as any;
    });
  
    afterEach(() => {
      (service.templateValidation as jasmine.Spy).calls.reset();
      (service.requestValidation as jasmine.Spy).calls.reset();
    });
  
    it('should call templateValidation and return if it is not valid when isTemplateRouteActivated is true', () => {
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(true);
      (service.templateValidation as jasmine.Spy).and.returnValue(false);
  
      service.saveRequestTemplate(url, toaster, validateSaveSubmit, form);
  
      expect(service.templateValidation).toHaveBeenCalledWith(form, validateSaveSubmit);
      expect(service.requestValidation).not.toHaveBeenCalled();
      expect(service.updateAllocationCode).not.toHaveBeenCalled();
    });
  
    it('should call requestValidation and return if it is not valid when isTemplateRouteActivated is false', () => {
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(false);
      (service.requestValidation as jasmine.Spy).and.returnValue(false);
  
      service.saveRequestTemplate(url, toaster, validateSaveSubmit, form);
  
      expect(service.requestValidation).toHaveBeenCalledWith(form, validateSaveSubmit);
      expect(service.updateAllocationCode).not.toHaveBeenCalled();
    });
  
  });  
  
  describe('removeRewardsRequired', () => {
    it('should remove the rewardsRequired control from the rules form group', () => {
      const form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          rewardsRequired: new UntypedFormControl('some value')
        })
      });

      expect(form.get('rules.rewardsRequired')).toBeTruthy();

      service.removeRewardsRequired(form);

      expect(form.get('rules.rewardsRequired')).toBeNull();
    });
  });

  describe('removeCustomUsage', () => {
    it('should call removeCustomUsage', () => {
      const form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          customUsage: new UntypedFormControl('some value')
        })
      });
      spyOn(service, 'removeCustomUsage').and.callThrough();
      service.removeCustomUsage(form);
      expect(service.removeCustomUsage).toHaveBeenCalledWith(form);
    });
  });  

  describe('updateAllocationCode', () => {
    it('should update the allocationCode and allocationCodeName fields in the info form group', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          allocationCode: new UntypedFormControl('01 - Default'),
          allocationCodeName: new UntypedFormControl('')
        })
      });

      service.updateAllocationCode(form);

      expect(form.get('info.allocationCode').value).toBe('01');
      expect(form.get('info.allocationCodeName').value).toBe('');
    });

    it('should not update the allocationCode and allocationCodeName fields if allocationCode is not present', () => {
      const form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          allocationCode: new UntypedFormControl(''),
          allocationCodeName: new UntypedFormControl('')
        })
      });

      service.updateAllocationCode(form);

      expect(form.get('info.allocationCode').value).toBe('');
      expect(form.get('info.allocationCodeName').value).toBe('');
    });
  });

  describe('setGRGetMiles', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          getMiles: new UntypedFormControl(null)
        })
      });

      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                productGroupVersions: new UntypedFormArray([
                  new UntypedFormGroup({
                    discountVersion: new UntypedFormGroup({
                      discounts: new UntypedFormArray([
                        new UntypedFormGroup({
                          tiers: new UntypedFormArray([
                            new UntypedFormGroup({
                              miles: new UntypedFormControl(100)
                            })
                          ])
                        })
                      ])
                    })
                  })
                ])
              })
            })
          ])
        }),
        isGRAirmilesPrograTypeSelected: true
      } as any;
    });

    it('should set the value of getMiles control based on the miles value when isGRAirmilesPrograTypeSelected is true', () => {
      service.setGRGetMiles(form);

      expect(form.get('info.getMiles').value).toBe(100);
    });

    it('should set the value of getMiles control to null when isGRAirmilesPrograTypeSelected is false', () => {
      Object.defineProperty(service.generalOfferTypeServic$, 'isGRAirmilesPrograTypeSelected', { value: false });

      service.setGRGetMiles(form);

      expect(form.get('info.getMiles').value).toBeNull();
    });
  });

  describe('setPodStoreGroupsForSpd', () => {
    let storeGroupVersion: UntypedFormGroup;

    beforeEach(() => {
      storeGroupVersion = new UntypedFormGroup({
        storeGroup: new UntypedFormGroup({
          digitalRedemptionStoreGroupIds: new UntypedFormControl('digitalIds'),
          digitalRedemptionStoreGroupNames: new UntypedFormControl('digitalNames'),
          podStoreGroupIds: new UntypedFormControl(''),
          podStoreGroupNames: new UntypedFormControl('')
        })
      });
  
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: storeGroupVersion
            })
          ])
        })
      } as any;
  
      service.facetItemService$ = {
        get programCodeSelected() {
          return 'SPD';
        }
      } as any;
  
      spyOnProperty(service, 'storeGroupVersion', 'get').and.returnValue(storeGroupVersion);
    });
  
    it('should set podStoreGroupIds and podStoreGroupNames based on digitalRedemptionStoreGroupIds and digitalRedemptionStoreGroupNames when program code is SPD and isTemplateRouteActivated is false', () => {
      spyOnProperty(service.facetItemService$, 'programCodeSelected', 'get').and.returnValue(CONSTANTS.SPD);
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(false);
  
      service.setPodStoreGroupsForSpd();
  
      expect(storeGroupVersion.get('storeGroup.podStoreGroupIds').value).toBe('digitalIds');
      expect(storeGroupVersion.get('storeGroup.podStoreGroupNames').value).toBe('digitalNames');
    });
  
    it('should not set podStoreGroupIds and podStoreGroupNames when program code is not SPD or BPD', () => {
      spyOnProperty(service.facetItemService$, 'programCodeSelected', 'get').and.returnValue('OTHER');
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(false);
  
      service.setPodStoreGroupsForSpd();
  
      expect(storeGroupVersion.get('storeGroup.podStoreGroupIds').value).toBe('');
      expect(storeGroupVersion.get('storeGroup.podStoreGroupNames').value).toBe('');
    });  
  });

  describe('requestValidation', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('ACTIVE')
        }),
        rules: new UntypedFormGroup({
          priceUntil: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl()
          })
        })
      });
  
      service.requestFormService$.isReqSubmitAttempted$ = new BehaviorSubject(false);
      service.requestFormService$.isDraftSaveAttempted = new BehaviorSubject(false);
  
      spyOn(service, 'notifySaveOrSubmitAttempt').and.callThrough();
      spyOn(service, 'setScene7FieldError').and.callThrough();
      spyOn(service, 'validateBehavioralContinuty').and.callThrough();
      spyOn(service, 'isFormValid').and.returnValue(true);
      spyOn(service, 'isScene7Invalid').and.returnValue(false);
  

      spyOn(service, 'getProgramCodeRule').and.returnValue({
        offerRequest: {},
        podDetails: {}
      });
    });
  
    afterEach(() => {
      (service.notifySaveOrSubmitAttempt as jasmine.Spy).calls.reset();
      (service.setScene7FieldError as jasmine.Spy).calls.reset();
      (service.validateBehavioralContinuty as jasmine.Spy).calls.reset();
      (service.isFormValid as jasmine.Spy).calls.reset();
      (service.isScene7Invalid as jasmine.Spy).calls.reset();
    });
  
    it('should set isDraftSaveAttempted to true and isReqSubmitAttempted to false', () => {
      const validateSaveSubmit = true;
  
      service.requestValidation(form, validateSaveSubmit);
  
      expect(service.isDraftSaveAttempted).toBeTrue();
      expect(service.isReqSubmitAttempted).toBeFalse();
    });
  
    it('should call notifySaveOrSubmitAttempt, setScene7FieldError, and validateBehavioralContinuty', () => {
      const validateSaveSubmit = true;
  
      service.requestValidation(form, validateSaveSubmit);
  
      expect(service.notifySaveOrSubmitAttempt).toHaveBeenCalledWith(validateSaveSubmit, form);
      expect(service.setScene7FieldError).toHaveBeenCalled();
      expect(service.validateBehavioralContinuty).toHaveBeenCalledWith(form);
    }); 
  })  

  describe('validateBehavioralContinuty', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          deliveryChannel: new UntypedFormControl(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE),
          behavioralCondition: new UntypedFormControl('some value')
        })
      });
    });

    it('should remove the behavioralCondition control if deliveryChannel is not BEHAVIORAL_CONTINUTY_CODE', () => {
      form.get('info.deliveryChannel').setValue('OTHER_CODE');

      service.validateBehavioralContinuty(form);

      expect(form.get('info.behavioralCondition')).toBeNull();
    });

    it('should not remove the behavioralCondition control if deliveryChannel is BEHAVIORAL_CONTINUTY_CODE', () => {
      form.get('info.deliveryChannel').setValue(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);

      service.validateBehavioralContinuty(form);

      expect(form.get('info.behavioralCondition')).toBeTruthy();
    });
  });

  describe('requestValidation', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('ACTIVE')
        }),
        rules: new UntypedFormGroup({
          priceUntil: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl()
          })
        })
      });


      service.requestFormService$.isReqSubmitAttempted$ = new BehaviorSubject(false);
      service.requestFormService$.isDraftSaveAttempted = new BehaviorSubject(false);

      spyOn(service, 'notifySaveOrSubmitAttempt').and.callThrough();
      spyOn(service, 'setScene7FieldError').and.callThrough();
      spyOn(service, 'validateBehavioralContinuty').and.callThrough();
      spyOn(service, 'isFormValid').and.returnValue(true);
      spyOn(service, 'isScene7Invalid').and.returnValue(false);

      spyOn(service, 'getProgramCodeRule').and.returnValue({
        offerRequest: {},
        podDetails: {}
      });
    });

    afterEach(() => {
      (service.notifySaveOrSubmitAttempt as jasmine.Spy).calls.reset();
      (service.setScene7FieldError as jasmine.Spy).calls.reset();
      (service.validateBehavioralContinuty as jasmine.Spy).calls.reset();
      (service.isFormValid as jasmine.Spy).calls.reset();
      (service.isScene7Invalid as jasmine.Spy).calls.reset();
    });

    it('should set isDraftSaveAttempted to true and isReqSubmitAttempted to false', () => {
      const validateSaveSubmit = true;

      service.requestValidation(form, validateSaveSubmit);

      expect(service.isDraftSaveAttempted).toBeTrue();
      expect(service.isReqSubmitAttempted).toBeFalse();
    });

    it('should call notifySaveOrSubmitAttempt, setScene7FieldError, and validateBehavioralContinuty', () => {
      const validateSaveSubmit = true;

      service.requestValidation(form, validateSaveSubmit);

      expect(service.notifySaveOrSubmitAttempt).toHaveBeenCalledWith(validateSaveSubmit, form);
      expect(service.setScene7FieldError).toHaveBeenCalled();
      expect(service.validateBehavioralContinuty).toHaveBeenCalledWith(form);
    });
  }) 

  describe('validateBehavioralContinuty', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          deliveryChannel: new UntypedFormControl(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE),
          behavioralCondition: new UntypedFormControl('some value')
        })
      });
    });

    it('should remove the behavioralCondition control if deliveryChannel is not BEHAVIORAL_CONTINUTY_CODE', () => {
      form.get('info.deliveryChannel').setValue('OTHER_CODE');

      service.validateBehavioralContinuty(form);

      expect(form.get('info.behavioralCondition')).toBeNull();
    });

    it('should not remove the behavioralCondition control if deliveryChannel is BEHAVIORAL_CONTINUTY_CODE', () => {
      form.get('info.deliveryChannel').setValue(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);

      service.validateBehavioralContinuty(form);

      expect(form.get('info.behavioralCondition')).toBeTruthy();
    });
  });


  describe('fileAttachment', () => {
    let id: string;
    let successMessage: string;

    beforeEach(() => {
      id = '123';
      successMessage = 'File uploaded successfully';

      service.requestFormService$ = {
        filesList: new BehaviorSubject([{ name: 'file1' }, { name: 'file2' }]),
        attachedFilesList: [],
        uploadedFilesList: [],
        fileUploadBool: new BehaviorSubject(false),
        navigateToSummary: jasmine.createSpy('navigateToSummary')
      } as any;

      spyOn(service, 'uploadFiles').and.returnValue(of([{ url: 'http://example.com/file1' }, { url: 'http://example.com/file2' }]));
      spyOn(service, 'navigateToSummaryAfterSave').and.callThrough();
    });

    it('should upload files and update attachedFilesList and uploadedFilesList', () => {
      service.fileAttachment(id, successMessage);

      expect(service.uploadFiles).toHaveBeenCalledWith(id, [{ name: 'file1' }, { name: 'file2' }]);
      expect(service.requestFormService$.filesList.getValue().length).toBe(0);
      expect(service.requestFormService$.attachedFilesList.length).toBe(2);
      expect(service.requestFormService$.attachedFilesList[0].uploadStatus).toBeTrue();
      expect(service.requestFormService$.attachedFilesList[0].id).toBe(id);
      expect(service.requestFormService$.fileUploadBool.getValue()).toBeTrue();
      expect(service.navigateToSummaryAfterSave).toHaveBeenCalledWith(successMessage, id);
    });
    
    it('should navigate to summary if no files to upload', () => {
      service.requestFormService$.filesList.next([]);

      service.fileAttachment(id, successMessage);

      expect(service.uploadFiles).not.toHaveBeenCalled();
      expect(service.navigateToSummaryAfterSave).toHaveBeenCalledWith(successMessage, id);
    });
  });

  describe('navigateToSummaryAfterSave', () => {
    let successMessage: string;
    let id: string;

    beforeEach(() => {
      successMessage = 'Success';
      id = '123';

      service.requestFormService$ = {
        navigateToSummary: jasmine.createSpy('navigateToSummary')
      } as any;

      service.facetItemService$ = {
        programCodeSelected: 'SPD'
      } as any;

      spyOn(service, 'navigateToTemplateSummary').and.callThrough();
    });
    
    it('should navigate to request summary if isTemplateRouteActivated is false', () => {
      spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(false);

      service.navigateToSummaryAfterSave(successMessage, id);

      expect(service.navigateToTemplateSummary).not.toHaveBeenCalled();
      expect(service.requestFormService$.navigateToSummary).toHaveBeenCalledWith(successMessage, id);
    });
  });
  

  xdescribe('navigateToTemplateSummary', () => {
    let templateId: string;

    beforeEach(() => {
      templateId = '123';

      service.facetItemService$ = {
        templateProgramCodeSelected: 'BPD'
      } as any;

      spyOn(router, 'navigateByUrl').and.callThrough();
    });

    it('should return false if isSavedFromNavigationOverlay_OT is true', () => {
      service.isSavedFromNavigationOverlay_OT = true;

      const result = service.navigateToTemplateSummary(templateId);

      expect(result).toBeFalse();
      expect(router.navigateByUrl).not.toHaveBeenCalled();
    });

    it('should navigate to the correct URL if templateProgramCodeSelected is defined', () => {
      service.isSavedFromNavigationOverlay_OT = false;
      service.facetItemService$.templateProgramCodeSelected = 'BPD';

      service.navigateToTemplateSummary(templateId);

      const expectedUrl = `/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDSummary}/${templateId}`;
      expect(router.navigateByUrl).toHaveBeenCalledWith(expectedUrl);
    });
  });


  describe('uploadFiles', () => {
    let id: string;
    let files: any[];

    beforeEach(() => {
      id = '123';
      files = [{ name: 'file1' }, { name: 'file2' }, null];

      spyOn(fileAttachService, 'uploadFile').and.callFake((file, id) => of({ url: `http://example.com/${file.name}` }).pipe(map(res => res)));
    });

    it('should call uploadFiles and return forkJoin observable', (done) => {
      const result = service.uploadFiles(id, files);

      result.subscribe((res: any[]) => {
        expect(res.length).toBe(2);
        expect(res[0].url).toBe('http://example.com/file1');
        expect(res[1].url).toBe('http://example.com/file2');
        done();
      });

      expect(fileAttachService.uploadFile).toHaveBeenCalledWith({ name: 'file1' }, id);
      expect(fileAttachService.uploadFile).toHaveBeenCalledWith({ name: 'file2' }, id);
    });
  });

  describe('handleResponse', () => {
    let form: UntypedFormGroup;
    let res: any;
    let successMessage: string;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          otStatus: new UntypedFormControl('ACTIVE')
        })
      });

      res = { id: '123' };
      successMessage = 'Success';

      service.requestFormService$ = {
        generalOfferTypeService: {
          generalOfferTypeForm: new UntypedFormGroup({})
        },
        isReqSubmitAttempted$: new BehaviorSubject(false),
        isDraftSaveAttempted: new BehaviorSubject(false),
        addNewRow$: new Subject(),
        filesList: new BehaviorSubject([]),
        attachedFilesList: [],
        uploadedFilesList: [],
        fileUploadBool: new BehaviorSubject(false),
        navigateToSummary: jasmine.createSpy('navigateToSummary')
      } as any;

      (service as any).commonSearchService = {
        isShowExpiredInQuery: false
      } as any;

      spyOn(service, 'getReqId').and.returnValue('123');
      spyOn(service, 'fileAttachment').and.callThrough();
    });


  });

  describe('getReqId', () => {
    it('should return the first key of the given object', () => {
      const obj = { key1: 'value1', key2: 'value2' };
      const result = service.getReqId(obj);
      expect(result).toBe('key1');
    });
  });

  describe('setIsCopiedFlag', () => {
    let form: UntypedFormGroup;
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          templateId: new UntypedFormControl(null),
          mobId: new UntypedFormControl(null),
          id: new UntypedFormControl(null),
          isCopied: new UntypedFormControl(false)
        })
      });
    });

    it('should set isCopied to true if isBpdReqPage is true, templateId, mobId are present, and id is not present', () => {
      spyOnProperty(service.commonRouteService, 'isBpdReqPage', 'get').and.returnValue(true);
      form.get('info.templateId').setValue('template123');
      form.get('info.mobId').setValue('mob123');
      form.get('info.id').setValue(null);

      const result = service.setIsCopiedFlag(form);

      expect(result.info.isCopied).toBeTrue();
    });

    it('should not set isCopied if isBpdReqPage is false', () => {
      spyOnProperty(service.commonRouteService, 'isBpdReqPage', 'get').and.returnValue(false);
      form.get('info.templateId').setValue('template123');
      form.get('info.mobId').setValue('mob123');
      form.get('info.id').setValue(null);

      const result = service.setIsCopiedFlag(form);

      expect(result.info.isCopied).toBeFalse();
    });
  }); 

  xdescribe('saveOfferRequest', () => {
    let httpClientSpy: jasmine.SpyObj<HttpClient>;
    let form: UntypedFormGroup;
    let apiUrl = 'https://api.example.com/offers';
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          id: new UntypedFormControl(null), // Simulate new request (POST)
          programCode: new UntypedFormControl('GR'),
          deliveryChannel: new UntypedFormControl('SomeChannel'),
          fulfillmentChannel: new UntypedFormControl('SomeFulfillment')
        })
      });
  
      httpClientSpy = TestBed.inject(HttpClient) as jasmine.SpyObj<HttpClient>;
  
      spyOn(service, 'setIsCopiedFlag').and.callFake((formData) => formData.value);
      spyOn(service, 'getHeaders').and.returnValue({
        "X-Albertsons-userAttributes": 'mocked-token',
        "X-Albertsons-Client-ID": 'client-id',
        "content-type": 'application/json'
      });
      spyOn(service, 'handleResponse').and.callThrough();
      spyOn(service.requestFormService$, 'checkFulfillmentChannelEnabled').and.returnValue(false);
    });
  
    it('should make a POST request when id is not present', () => {
      spyOn(httpClientSpy, 'post').and.returnValue(of({})); 
  
      service.saveOfferRequest(apiUrl, 'Save', form);
  
      expect(httpClientSpy.post).toHaveBeenCalled();
    });
  
    it('should make a PUT request when id is present', () => {
      form.get('info.id')?.setValue('12345'); 
      spyOn(httpClientSpy, 'put').and.returnValue(of({})); 
  
      service.saveOfferRequest(apiUrl, 'Submit', form);
  
      expect(httpClientSpy.put).toHaveBeenCalled();
    });
  
    it('should call checkFulfillmentChannelEnabled', () => {
      service.saveOfferRequest(apiUrl, 'Save', form);
      expect(service.requestFormService$.checkFulfillmentChannelEnabled).toHaveBeenCalled();
    });
  
    it('should call setIsCopiedFlag', () => {
      service.saveOfferRequest(apiUrl, 'Save', form);
      expect(service.setIsCopiedFlag).toHaveBeenCalled();
    });
  
    it('should call handleResponse after API call', () => {
      spyOn(httpClientSpy, 'post').and.returnValue(of({}));
      
      service.saveOfferRequest(apiUrl, 'Save', form);
      
      expect(service.handleResponse).toHaveBeenCalled();
    });
  });
  
  
  describe('getHeaders', () => {
    beforeEach(() => {
      spyOn(service.authService$, 'getTokenString').and.returnValue('mocked-token');
    });
  
    it('should return HTTP headers with X-Albertsons-userAttributes', () => {
      const headers = service.getHeaders();
  
      expect(headers).toEqual(jasmine.objectContaining({
        ...CONSTANTS.HTTP_HEADERS,
        "X-Albertsons-userAttributes": "mocked-token"
      }));
  
      expect(service.authService$.getTokenString).toHaveBeenCalled();
    });
  });

  describe('setCachedDigitalData', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          digitalStatus: new UntypedFormControl(null),
          nonDigitalStatus: new UntypedFormControl(null),
        }),
        cachedDigitalOfferStatus: new UntypedFormControl(null),
        cachedNonDigitalOfferStatus: new UntypedFormControl(null),
      });
  
      service.requestFormService$.cachedDigitalOfferStatus = 'Digital_Cached_Value';
      service.requestFormService$.cachedNonDigitalOfferStatus = 'Non_Digital_Cached_Value';
    });

    it('should set cachedDigitalOfferStatus when digitalStatus is "P"', () => {
      form.get('info.digitalStatus')?.setValue('P');
  
      service.setCachedDigitalData(form);
  
      expect(form.get('cachedDigitalOfferStatus')?.value).toBe('Digital_Cached_Value');
    });
  
    it('should set cachedNonDigitalOfferStatus when nonDigitalStatus is "P"', () => {
      form.get('info.nonDigitalStatus')?.setValue('P');
  
      service.setCachedDigitalData(form);
  
      expect(form.get('cachedNonDigitalOfferStatus')?.value).toBe('Non_Digital_Cached_Value');
    });
  
  
    it('should not update any cached values if statuses are not "P"', () => {
      form.get('info.digitalStatus')?.setValue('A');
      form.get('info.nonDigitalStatus')?.setValue('A');
  
      service.setCachedDigitalData(form);
  
      expect(form.get('cachedDigitalOfferStatus')?.value).toBeNull();
      expect(form.get('cachedNonDigitalOfferStatus')?.value).toBeNull();
    });
  
    it('should do nothing if requestFormService$ has no cached values', () => {
      service.requestFormService$.cachedDigitalOfferStatus = null;
      service.requestFormService$.cachedNonDigitalOfferStatus = null;
  
      form.get('info.digitalStatus')?.setValue('P');
      form.get('info.nonDigitalStatus')?.setValue('P');
  
      service.setCachedDigitalData(form);
  
      expect(form.get('cachedDigitalOfferStatus')?.value).toBeNull();
      expect(form.get('cachedNonDigitalOfferStatus')?.value).toBeNull();
    });
  
    it('should call setCachedDigitalData function', () => {
      spyOn(service, 'setCachedDigitalData').and.callThrough();
  
      service.setCachedDigitalData(form);
  
      expect(service.setCachedDigitalData).toHaveBeenCalled();
    });
    it('should call setCachedDigitalData function', () => {
      spyOn(service, 'setCachedDigitalData').and.callThrough();
  
      service.setCachedDigitalData(form);
  
      expect(service.setCachedDigitalData).toHaveBeenCalled();
    });
  });

  describe('addAllocation', () => {
    let form: UntypedFormGroup;
    let allocationDataListSpy: jasmine.SpyObj<Subject<any>>;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        allocationCode: new UntypedFormControl(null),
        allocationCodeName: new UntypedFormControl(null),
      });
  
      allocationDataListSpy = jasmine.createSpyObj('Subject', ['next']);

      spyOn(service, 'getControlFromBase').and.callFake((key: string, _form: UntypedFormGroup): UntypedFormControl => {
        return _form.get(key) as UntypedFormControl;
      });
  
      service.allocationDataList = allocationDataListSpy;
    });
  
    it('should update allocationCode and allocationCodeName in form', () => {
      const mockData = {
        newAllocation: { allocationCode: 'A123', allocationCodeName: 'Test Allocation' },
        allocationData: { someKey: 'someValue' },
      };
  
      service.addAllocation(mockData, form);
  
      expect(service.getControlFromBase).toHaveBeenCalledWith('allocationCode', form);
      expect(service.getControlFromBase).toHaveBeenCalledWith('allocationCodeName', form);
      expect(form.get('allocationCode')?.value).toBe('A123');
      expect(form.get('allocationCodeName')?.value).toBe('Test Allocation');
    });
  
    it('should call allocationDataList.next() with allocationData', () => {
      const mockData = {
        newAllocation: { allocationCode: 'A123', allocationCodeName: 'Test Allocation' },
        allocationData: { someKey: 'someValue' },
      };
  
      service.addAllocation(mockData, form);
  
      expect(allocationDataListSpy.next).toHaveBeenCalledWith(mockData.allocationData);
    });
  
    it('should mark the form as dirty', () => {
      spyOn(form, 'markAsDirty');
  
      const mockData = {
        newAllocation: { allocationCode: 'A123', allocationCodeName: 'Test Allocation' },
        allocationData: { someKey: 'someValue' },
      };
  
      service.addAllocation(mockData, form);
  
      expect(form.markAsDirty).toHaveBeenCalled();
    });
  
    it('should do nothing if data is null or undefined', () => {
      spyOn(form, 'markAsDirty');
  
      service.addAllocation(null, form);
  
      expect(form.markAsDirty).not.toHaveBeenCalled();
      expect(allocationDataListSpy.next).not.toHaveBeenCalled();
      expect(service.getControlFromBase).not.toHaveBeenCalled();
    });
  });

  describe('setAdditionConvertTypes', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          regionId: new UntypedFormControl('123 RegionName'),
          allocationCode: new UntypedFormControl('456 - Allocation Name')
        })
      });
    });
  
    it('should update regionId to first part of the string', () => {
      service.setAdditionConvertTypes(form);
      expect(form.get('info.regionId')?.value).toBe('123 RegionName');
    });
  
    it('should update allocationCode to first part before " - "', () => {
      service.setAdditionConvertTypes(form);
      expect(form.get('info.allocationCode')?.value).toBe('456 - Allocation Name');
    });
  
    it('should handle missing regionId gracefully', () => {
      form.get('info.regionId')?.setValue(null);
      service.setAdditionConvertTypes(form);
      expect(form.get('info.regionId')?.value).toBeNull();
    });
  
    it('should handle missing allocationCode gracefully', () => {
      form.get('info.allocationCode')?.setValue(null);
      service.setAdditionConvertTypes(form);
      expect(form.get('info.allocationCode')?.value).toBeNull();
    });
  
    it('should handle empty form values gracefully', () => {
      const emptyForm = new UntypedFormGroup({
        info: new UntypedFormGroup({})
      });
  
      service.setAdditionConvertTypes(emptyForm);
  
      expect(emptyForm.get('info.regionId')?.value).toBeUndefined();
      expect(emptyForm.get('info.allocationCode')?.value).toBeUndefined();
    });
  });


  describe('formatHrsAndMins', () => {
    let timeGroup: any;
    const timeArray = ['12', '8', '5', 'AM', 'extra']; // Example time array
  
    beforeEach(() => {
      timeGroup = {};
      spyOn(service.requestFormService$, 'prependZero').and.callFake((num: number) => {
        return num < 10 ? '0' + num : num.toString();
      });
    });
  
    it('should correctly format hours, minutes, and period when timeArray has 5 elements', () => {
      service.formatHrsAndMins(timeArray, timeGroup, 'test');
  
      expect(timeGroup['testHr']).toBe('08');
      expect(timeGroup['testMin']).toBe('05'); 
      expect(timeGroup['testPeriod']).toBe('AM');
      expect(timeGroup['test']).toBe('08:05 AM'); 
    });

  });

  describe('formatTimeForPayload', () => {
    it('should format the time object correctly', () => {
      const timeGroup = {
        start: '14:30',
        end: '16:45',
        startHr: '',
        startMin: '',
        startPeriod: '',
        endHr: '',
        endMin: '',
        endPeriod: ''
      };

      const result: { start: string, end: string } = { start: '', end: '' };
      Object.assign(result, service.formatTimeForPayload(timeGroup));

      expect(result.start).toBe('02:30 PM');
      expect(result.end).toBe('04:45 PM');
      expect(timeGroup.startHr).toBe('02');
      expect(timeGroup.startMin).toBe('30');
      expect(timeGroup.startPeriod).toBe('PM');
      expect(timeGroup.endHr).toBe('04');
      expect(timeGroup.endMin).toBe('45');
      expect(timeGroup.endPeriod).toBe('PM');
    });
  });
  
  describe('updateTimeandDate', () => {
    let form: UntypedFormGroup;
    beforeEach(() => {
      form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          qualificationAndBenefit: new UntypedFormGroup({
            time: new UntypedFormControl(null)
          })
        }),
        offerRuleTimeGroup: new UntypedFormControl(null)
      });
    });

    it('should set qualificationAndBenefit.time to null if timeGroup is invalid', () => {
      service.requestFormService$.requestForm.value.offerRuleTimeGroup = null;

      service.updateTimeandDate(form);

      expect(form.get('rules.qualificationAndBenefit.time').value).toBeNull();
    });

    it('should delete offerRuleTimeGroup from form value', () => {
      form.value.offerRuleTimeGroup = {
        start: '14:30',
        end: '16:45'
      };

      service.updateTimeandDate(form);

      expect(form.value.offerRuleTimeGroup).toBeUndefined();
    });
  });

  xdescribe('setOfferRequestType', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          numOfTiers: new UntypedFormControl(),
          offerRequestType: new UntypedFormControl(),
          isRedeemableInSameTransaction: new UntypedFormControl()
        })
      });

      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          generalInformationForm: new UntypedFormGroup({
            type: new UntypedFormControl('WOD / POD'),
            tiers: new UntypedFormControl(3),
            isRedeemableInSameTransaction: new UntypedFormControl(true)
          })
        }),
        getKeyByValue: jasmine.createSpy('getKeyByValue').and.returnValue('WOD_OR_POD')
      } as any;
    });

    it('should set numOfTiers, offerRequestType, and isRedeemableInSameTransaction correctly when offerRequestType is WOD_OR_POD', () => {
      service.setOfferRequestType(form);

      expect(service.generalOfferTypeServic$.getKeyByValue).toHaveBeenCalledWith(null, 'WOD / POD');
      expect(form.get('info.numOfTiers').value).toBe(3);
      expect(form.get('info.offerRequestType').value).toBe('WOD_OR_POD');
      expect(form.get('info.isRedeemableInSameTransaction').value).toBe(true);
    });

    it('should set numOfTiers to undefined when offerRequestType is not WOD_OR_POD', () => {
      spyOn(service.generalOfferTypeServic$, 'getKeyByValue').and.returnValue('OTHER_TYPE');
      service.setOfferRequestType(form);

      expect(service.generalOfferTypeServic$.getKeyByValue).toHaveBeenCalledWith(null, 'WOD / POD');
      expect(form.get('info.numOfTiers').value).toBeUndefined();
      expect(form.get('info.offerRequestType').value).toBe('OTHER_TYPE');
      expect(form.get('info.isRedeemableInSameTransaction').value).toBe(true);
    });
  }); 
  
  describe('isTemplateRouteActivated', () => {
    it('should return true when currentActivatedRoute includes "template"', () => {
      const commonRouteServiceStub = TestBed.inject(CommonRouteService);
      commonRouteServiceStub.currentActivatedRoute = 'some/path/template';
      expect(service.isTemplateRouteActivated).toBeTrue();
    });
  
    it('should return false when currentActivatedRoute does not include "template"', () => {
      const commonRouteServiceStub = TestBed.inject(CommonRouteService);
      commonRouteServiceStub.currentActivatedRoute = 'some/path/other';
      expect(service.isTemplateRouteActivated).toBeFalse();
    });
  })  

  describe('setQualificationAndBenefit', () => {
    let form: UntypedFormGroup;

    beforeEach(() => {
        form = new UntypedFormGroup({
            info: new UntypedFormGroup({
                deliveryChannel: new UntypedFormControl('someChannel')
            }),
            rules: new UntypedFormGroup({
                qualificationAndBenefit: new UntypedFormGroup({
                    offerTemplateOffers: new UntypedFormControl(null),
                    reqOffersObjkeyOfferRequestOffers: new UntypedFormControl(null)
                })
            })
        });

        service.requestFormService$ = {
            parseOfferRequestOffers: jasmine.createSpy('parseOfferRequestOffers').and.returnValue('parsedOffers')
        } as any;

        service.facetItemService$ = {
            programCodeSelected: 'someProgramCode',
            reqOffersObjkey: 'reqOffersObjkey'
        } as any;
    });

    it('should set qualificationAndBenefitType to "offerTemplateOffers" when isTemplateRouteActivated is true', () => {
        spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(true);

        service.setQualificationAndBenefit(form);

        expect(service.requestFormService$.parseOfferRequestOffers).toHaveBeenCalledWith('someChannel');
        expect(form.get('rules.qualificationAndBenefit.offerTemplateOffers').value).toBe(null);
    });

    it('should set qualificationAndBenefitType to "reqOffersObjkeyOfferRequestOffers" when isTemplateRouteActivated is false', () => {
        spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(false);

        service.setQualificationAndBenefit(form);

        expect(service.requestFormService$.parseOfferRequestOffers).toHaveBeenCalledWith('someChannel');
        expect(form.get('rules.qualificationAndBenefit.reqOffersObjkeyOfferRequestOffers').value).toBe(null);
    });
  });

  describe('podDetails', () => {
    beforeEach(() => {
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  scene7ImageId: new UntypedFormControl('12345')
                })
              })
            })
          ])
        })
      } as any;
    });
  
    it('should return the podDetails form group', () => {
      const result = service.podDetails;
      expect(result).toBe((service.generalOfferTypeServic$.generalOfferTypeForm.get('offerRequestOffers') as UntypedFormArray).at(0).get('storeGroupVersion').get('podDetails'));
    });
  })  

  describe('podDetails', () => {
    beforeEach(() => {
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  scene7ImageId: new UntypedFormControl('12345')
                })
              })
            })
          ])
        })
      } as any;
    });
  
    it('should return the podDetails form group', () => {
      const result = service.podDetails;
      expect(result).toBe((service.generalOfferTypeServic$.generalOfferTypeForm.get('offerRequestOffers') as UntypedFormArray).at(0).get('storeGroupVersion').get('podDetails'));
    });
  });

  describe('trimPodValuesBeforeSave', () => {
    beforeEach(() => {
      service.generalOfferTypeServic$ = {
        generalOfferTypeForm: new UntypedFormGroup({
          offerRequestOffers: new UntypedFormArray([
            new UntypedFormGroup({
              storeGroupVersion: new UntypedFormGroup({
                podDetails: new UntypedFormGroup({
                  headline1: new UntypedFormControl('headline1   '),
                  headline2: new UntypedFormControl('headline2   '),
                  offerDescription: new UntypedFormControl('offerDescription   '),
                  priceText: new UntypedFormControl('priceText   '),
                  scene7ImageId: new UntypedFormControl('scene7ImageId   '),
                  otherField: new UntypedFormControl('otherField   ')
                })
              })
            })
          ])
        })
      } as any;
    });
  
    it('should trim trailing spaces from specified fields in podDetails', () => {
      service.trimPodValuesBeforeSave();
  
      const podDetails = service.podDetails;
      expect(podDetails.get('headline1').value).toBe('headline1');
      expect(podDetails.get('headline2').value).toBe('headline2');
      expect(podDetails.get('offerDescription').value).toBe('offerDescription');
      expect(podDetails.get('priceText').value).toBe('priceText');
      expect(podDetails.get('scene7ImageId').value).toBe('scene7ImageId');
      expect(podDetails.get('otherField').value).toBe('otherField   '); 
    });
  
    it('should do nothing if podDetails is null or undefined', () => {
      spyOnProperty(service, 'podDetails', 'get').and.returnValue(null);
      service.trimPodValuesBeforeSave();
    });
  });

  describe('updateDateFormatOnSave', () => {
    xit('should call dateInOriginalFormat for startDate and endDate', () => {
      const form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          startDate: new UntypedFormGroup({
            offerEffectiveStartDate: new UntypedFormControl('2023-10-10')
          }),
          endDate: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl('2023-12-10')
          })
        })
      });
  
      service.updateDateFormatOnSave(form);
  
      expect(DateUtils.dateInOriginalFormat).toHaveBeenCalledWith({
        date: '2023-10-10',
        isStartDate: true
      });
      expect(DateUtils.dateInOriginalFormat).toHaveBeenCalledWith({
        date: '2023-12-10',
        isStartDate: false
      });
    });
  
    it('should not call dateInOriginalFormat if startDate or endDate is not present', () => {
      const form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          startDate: new UntypedFormGroup({}),
          endDate: new UntypedFormGroup({})
        })
      });
  
      service.updateDateFormatOnSave(form);
  
      expect(DateUtils.dateInOriginalFormat).not.toHaveBeenCalled();
    });
  })
  
  describe('addFormControl', () => {
    it('should add a control to the form if addControl method exists', () => {
      const form = new UntypedFormGroup({});
      const formControl = new UntypedFormControl('testValue');
      spyOn(form, 'addControl').and.callThrough();

      const result = service.addFormControl('testControl', formControl, form);

      expect(form.addControl).toHaveBeenCalledWith('testControl', formControl);
      expect(result).toBe(form.get('testControl'));
      expect(result.value).toBe('testValue');
    });

    it('should return the control if addControl method does not exist', () => {
      const form = {
        get: jasmine.createSpy('get').and.returnValue('existingControl')
      } as any;
      const formControl = new UntypedFormControl('testValue');

      const result = service.addFormControl('testControl', formControl, form);

      expect(form.get).toHaveBeenCalledWith('testControl');
      expect(result).toBe('existingControl');
    });
  });

  describe('addFormuilder', () => {
    it('should return a new form group', () => {
      const formGroup = service.addFormuilder();
      expect(formGroup).toBeTruthy();
      expect(formGroup instanceof UntypedFormGroup).toBeTrue();
    });
  });

  describe('setFormControls', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({});

      const formBuilderStub = {
        group: jasmine.createSpy('group').and.returnValue(new UntypedFormGroup({}))
      };
  
      service.formBuilder = formBuilderStub as any;
  
      spyOn(service, 'generateFormControlFieldsKeys').and.returnValue(['key1', 'key2']);
      spyOn(service, 'createFields').and.callThrough();
      spyOn(service, 'createFormControls').and.callThrough();
      spyOn(service, 'getDefaultFormControls').and.returnValue({
        lastUpdatedTs: {},
        createdApplicationId: {},
        createdTs: {},
        createdUserId: {},
        id: { value: null, control: ["info", "id"] },
        deliveryChannel: { value: "DO", control: ["info", "deliveryChannel"] },
        programCode: { value: service.facetItemService$.programCodeSelected, control: ["info", "programCode"] },
        numOfVersions: { value: null, control: ["info", "numOfVersions"] },
        numOfTiers: { value: null, control: ["info", "numOfTiers"] },
        numOfProducts: { value: null, control: ["info", "numOfProducts"] },
        digitalUser: { value: null, control: ["info", "digitalUser"] },
        nonDigitalUser: { value: null, control: ["info", "nonDigitalUser"] },
        digitalStatus: { value: null, control: ["info", "digitalStatus"] },
        nonDigitalStatus: { value: null, control: ["info", "nonDigitalStatus"] },
        digitalEditStatus: { value: null, control: ["info", "digitalEditStatus"] },
        nonDigitalEditStatus: { value: null, control: ["info", "nonDigitalEditStatus"] },
        digitalUiStatus: { value: null, control: ["info", "digitalUiStatus"] },
        nonDigitalUiStatus: { value: null, control: ["info", "nonDigitalUiStatus"] },
        mobId: { value: null, control: ["info", "mobId"] },
        autoAssignMob: { value: true, control: ["info", "autoAssignMob"] },
        mobName: { value: null, control: ["info", "mobName"] },
      });
    });
  
    it('should call generateFormControlFieldsKeys and createFields when data is provided', () => {
      const data = { key1: 'value1', key2: 'value2' };
      const offerRequest = JSON.stringify({ offerKey: 'offerValue' });
  
      service.setFormControls(data, offerRequest, form);
  
      expect(service.generateFormControlFieldsKeys).toHaveBeenCalledWith(data);
      expect(service.createFields).toHaveBeenCalledWith(jasmine.any(Object), ['key1', 'key2'], data, JSON.parse(offerRequest));
      expect(service.createFormControls).toHaveBeenCalledWith(jasmine.any(Object), form, data);
    });
  
    it('should use getDefaultFormControls when data is not provided', () => {
      const offerRequest = JSON.stringify({ offerKey: 'offerValue' });
  
      service.setFormControls(null, offerRequest, form);
  
      expect(service.getDefaultFormControls).toHaveBeenCalled();
      expect(service.createFormControls).toHaveBeenCalledWith(jasmine.any(Object), form, null);
    });
  });

  describe('getValue', () => {
    it('should return the correct value when controls array is not provided', () => {
      const controls = [];
      const data = {
        field: 'expectedValue'
      };
      const field = 'field';

      const result = service.getValue(controls, data, field);

      expect(result).toBe('expectedValue');
    });

    it('should return undefined if the field is not found', () => {
      const controls = ['level1', 'level2'];
      const data = {
        level1: {
          level2: {}
        }
      };
      const field = 'field';

      const result = service.getValue(controls, data, field);

      expect(result).toBeUndefined();
    });

    it('should return undefined if the controls array is not found in data', () => {
      const controls = ['level1', 'level3'];
      const data = {
        level1: {
          level2: {
            field: 'expectedValue'
          }
        }
      };
      const field = 'field';

      const result = service.getValue(controls, data, field);

      expect(result).toBeUndefined();
    });
  });

  describe('createFields', () => {
    it('should create form controls correctly', () => {
      const formControl = {};
      const getKeys = ['info.name', 'info.age'];
      const parse = {
        info: {
          name: 'John Doe',
          age: 30
        }
      };
      const offerRequest = {
        name: { control: ['info', 'name'], value: 'John Doe' },
        age: { control: ['info', 'age'], value: 30 }
      };

      service.createFields(formControl, getKeys, parse, offerRequest);

      expect(formControl).toEqual({
        name: {
          value: 'John Doe',
          control: ['info', 'name']
        },
        age: {
          value: 30,
          control: ['info', 'age']
        }
      });
    });

    it('should handle nested properties correctly', () => {
      const formControl = {};
      const getKeys = ['info.address.street', 'info.address.city'];
      const parse = {
        info: {
          address: {
            street: '123 Main St',
            city: 'Anytown'
          }
        }
      };
      const offerRequest = {
        street: { control: ['info', 'address', 'street'], value: '123 Main St' },
        city: { control: ['info', 'address', 'city'], value: 'Anytown' }
      };

      service.createFields(formControl, getKeys, parse, offerRequest);

      expect(formControl).toEqual({
        street: {
          value: '123 Main St',
          control: ['info', 'address', 'street']
        },
        city: {
          value: 'Anytown',
          control: ['info', 'address', 'city']
        }
      });
    });

  });

  describe('generateFormControlFieldsKeys', () => {
    it('should generate form control field keys correctly for nested objects', () => {
      const data = {
        info: {
          name: 'John Doe',
          address: {
            street: '123 Main St',
            city: 'Anytown'
          }
        }
      };

      const result = service.generateFormControlFieldsKeys(data);

      expect(result).toEqual([
        'info.name',
        'info.address.street',
        'info.address.city'
      ]);
    });

    it('should handle arrays correctly', () => {
      const data = {
        info: {
          name: 'John Doe',
          hobbies: ['reading', 'swimming'],
          defaultPromoWeekIds: [1, 2, 3]
        }
      };

      const result = service.generateFormControlFieldsKeys(data);

      expect(result).toEqual([
        'info.name',
        'info.defaultPromoWeekIds'
      ]);
    });
  })  

  describe('checkFeatureFlagEnabled', () => {
    it('should return true if the feature flag is enabled', () => {
      spyOn(featureFlagService, 'isFeatureFlagEnabled').and.returnValue(true);

      const result = service.checkFeatureFlagEnabled('someFeatureFlag');

      expect(result).toBeTrue();
      expect(featureFlagService.isFeatureFlagEnabled).toHaveBeenCalledWith('someFeatureFlag');
    });

    it('should return false if the feature flag is not enabled', () => {
      spyOn(featureFlagService, 'isFeatureFlagEnabled').and.returnValue(false);

      const result = service.checkFeatureFlagEnabled('someFeatureFlag');

      expect(result).toBeFalse();
      expect(featureFlagService.isFeatureFlagEnabled).toHaveBeenCalledWith('someFeatureFlag');
    });
  });
  
  xdescribe('createFormControls', () => {
    let service: TemplateRequestBaseService;
    let formBuilder: UntypedFormBuilder;
    let featureFlagService: FeatureFlagsService;
    let requestForm: UntypedFormGroup;
  
    beforeEach(() => {
      TestBed.configureTestingModule({
        providers: [
          TemplateRequestBaseService,
          { provide: UntypedFormBuilder, useClass: UntypedFormBuilder },
          { provide: FeatureFlagsService, useValue: { isFeatureFlagEnabled: jasmine.createSpy().and.returnValue(true) } },
        ],
      });
  
      service = TestBed.inject(TemplateRequestBaseService);
      formBuilder = TestBed.inject(UntypedFormBuilder);
      featureFlagService = TestBed.inject(FeatureFlagsService);
  
      requestForm = formBuilder.group({
        info: formBuilder.group({
          name: [''],
          age: [''],
          startDate: [''],
          address: formBuilder.group({
            street: [''],
            city: [''],
          }),
        }),
      });
    });

    it('should create form controls correctly', () => {
      spyOn(service, 'createFormControls').and.callThrough();
      const formControls = generalOfferTypeService.createFormControl();
      expect(formControls).toBeDefined();
    });
   
   it('should handle feature flag checks correctly', () => {
      spyOn(featureFlagService, 'isFeatureFlagEnabled').and.returnValue(true);
      expect(featureFlagService.isFeatureFlagEnabled).toBe(true);
    });
  }); 

  describe('createFormControls', () => {
    it('should handle feature flag checks correctly', () => {
      const formControls = {
        name: {
          value: 'John Doe',
          control: ['info', 'name'],
          featureFlagCheck: true,
          featureFlag: 'someFeatureFlag',
        },
      };
      const data = {
        info: {
          name: 'Jane Doe',
        },
      };

      spyOn(service, 'checkFeatureFlagEnabled').and.returnValue(false);
      spyOn(service, 'createFormControls').and.callThrough(); 

      const requestForm = new UntypedFormGroup({});
      service.createFormControls(formControls, requestForm, data);

      expect(requestForm.get('info.name')?.value).toBe(undefined);
      expect(service.checkFeatureFlagEnabled).toHaveBeenCalledWith('someFeatureFlag');
    });
  });
  
  describe('multiClipLimitCtrl', () => {
    it('should return the multiClipLimit control from podDetails', () => {
      const podDetails = new UntypedFormGroup({
        multiClipLimit: new UntypedFormControl(5),
      });
      spyOnProperty(service, 'podDetails', 'get').and.returnValue(podDetails);

      const result = service.multiClipLimitCtrl;

      expect(result).toBe(podDetails.get('multiClipLimit'));
      expect(result.value).toBe(5);
    });

    it('should return undefined if podDetails is null or undefined', () => {
      spyOnProperty(service, 'podDetails', 'get').and.returnValue(null);

      const result = service.multiClipLimitCtrl;

      expect(result).toBeUndefined();
    });
  });

  xdescribe('updatePodDataOnValueChanges', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          startDate: new UntypedFormControl(null),
        }),
      });
  
      service.storeGroupVersionControl = new UntypedFormGroup({
        podDetails: new UntypedFormControl({}),
      });
  
      spyOn(service, 'setBrandAndSizeValue');
      spyOn(service, 'setLeftNavOnShoppingListChange');
      spyOn(service, 'setDisplayDates');
  
      service.storeGroupVersionControl.valueChanges = new BehaviorSubject<any>({}).asObservable();
    });
  
    it('should do nothing if nextPodDetails or prevPodDetails are undefined', () => {
      service.storeGroupVersionControl.valueChanges = new BehaviorSubject<any>({
        podDetails: undefined,
      }).asObservable();
  
      service.updatePodDataOnValueChanges(form);
  
      expect(service.setBrandAndSizeValue).not.toHaveBeenCalled();
    });
  
    it('should set brand and size values and update shopping category if podDetails change', () => {
      const prevPodDetails = { headline1: 'Old Header 1', shoppingListCategory: 'Old Category' };
      const nextPodDetails = { headline1: 'New Header 1', shoppingListCategory: 'New Category' };
  
      service.storeGroupVersionControl.valueChanges = new BehaviorSubject<any>({
        podDetails: nextPodDetails,
      }).asObservable();
  
      service.updatePodDataOnValueChanges(form);
  
      expect(service.setBrandAndSizeValue).toHaveBeenCalledWith(
        {
          nextHeader1: 'New Header 1',
          nextHeader2: '',
          nextOfferDescription: '',
          nextPriceText: '',
        },
        form
      );
      expect(service.selectedShoppingCategory).toBe('New Category');
      expect(service.setLeftNavOnShoppingListChange).toHaveBeenCalledWith('Old Category', 'New Category', service.storeGroupVersionControl);
    });
  
    it('should call setDisplayDates if offerEffectiveStartDate is present', () => {
      const startDate = form.get("rules").get("startDate") as UntypedFormGroup;
  
      startDate.setValue({ offerEffectiveStartDate: true });
  
      service.updatePodDataOnValueChanges(form);
  
      expect(service.setDisplayDates).toHaveBeenCalled();
    });
  });
 
  describe('updatePodDataOnValueChanges', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          startDate: new UntypedFormControl(null),
        }),
      });
  
      service.storeGroupVersionControl = new UntypedFormGroup({
        podDetails: new UntypedFormControl({}),
      });
  
      spyOn(service, 'setBrandAndSizeValue');
      spyOn(service, 'setLeftNavOnShoppingListChange');
      spyOn(service, 'setDisplayDates');
  
      service.storeGroupVersionControl.valueChanges = new BehaviorSubject<any>({}).asObservable();
    });
  
    it('should do nothing if nextPodDetails or prevPodDetails are undefined', () => {
      service.storeGroupVersionControl.valueChanges = new BehaviorSubject<any>({
        podDetails: undefined,
      }).asObservable();
  
      service.updatePodDataOnValueChanges(form);
  
      expect(service.setBrandAndSizeValue).not.toHaveBeenCalled();
    });
  })

  describe('setDisplayDates', () => {
    let formValue: any;
    let storeGroupVersion: UntypedFormGroup;
  
    beforeEach(() => {
      storeGroupVersion = new UntypedFormGroup({
        podDetails: new UntypedFormGroup({
          displayStartDate: new UntypedFormControl(null)
        })
      });
  
      formValue = {
        offerEffectiveStartDate: '2023-10-10'
      };
  
      (window as any).mmDdYyyySlash_DateFormatWithoutUTC = (date: string) => date;
    });
  
    it('should call setDisplayDates when offerEffectiveStartDate is present', () => {
      spyOn(service, 'setDisplayDates').and.callThrough();
  
      service.setDisplayDates(formValue, storeGroupVersion);
  
      expect(service.setDisplayDates).toHaveBeenCalledWith(formValue, storeGroupVersion);
      expect(storeGroupVersion.get('podDetails.displayStartDate').value).toBe('10/10/2023');
      expect(service.minDisplayEndDate).toEqual(new Date('2023-10-10'));
    });
  
    it('should call setDisplayDates when offerEffectiveStartDate is not present', () => {
      spyOn(service, 'setDisplayDates').and.callThrough();
  
      formValue.offerEffectiveStartDate = null;
  
      service.setDisplayDates(formValue, storeGroupVersion);
  
      expect(service.setDisplayDates).toHaveBeenCalledWith(formValue, storeGroupVersion);
      // expect(service.minDisplayEndDate).toEqual(new Date());
    });
  
    it('should call setDisplayDates when offerEffectiveStartDate is a Date object', () => {
      spyOn(service, 'setDisplayDates').and.callThrough();
  
      formValue.offerEffectiveStartDate = new Date('2023-10-10');
  
      service.setDisplayDates(formValue, storeGroupVersion);
  
      expect(service.setDisplayDates).toHaveBeenCalledWith(formValue, storeGroupVersion);
      expect(storeGroupVersion.get('podDetails.displayStartDate').value).toBe('10/10/2023');
      expect(service.minDisplayEndDate).toEqual(new Date('2023-10-10'));
    });
  });
  describe('setBrandAndSizeValue', () => {
    let form: UntypedFormGroup;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        info: new UntypedFormGroup({
          brandAndSize: new UntypedFormControl('')
        })
      });
    });
  
    it('should set brandAndSize correctly when all values are provided', () => {
      const changedObj = {
        nextHeader1: 'Header1',
        nextHeader2: 'Header2',
        nextOfferDescription: 'Description',
        nextPriceText: 'Price'
      };
  
      service.setBrandAndSizeValue(changedObj, form);
  
      expect(form.get('info.brandAndSize').value).toBe('Header1 Header2 Description @ Price');
    });
  
    it('should set brandAndSize correctly when some values are missing', () => {
      const changedObj = {
        nextHeader1: 'Header1',
        nextHeader2: '',
        nextOfferDescription: 'Description',
        nextPriceText: ''
      };
  
      service.setBrandAndSizeValue(changedObj, form);
  
      expect(form.get('info.brandAndSize').value).toBe('Header1 Description');
    });

    it('should truncate brandAndSize if it exceeds 100 characters', () => {
      const changedObj = {
        nextHeader1: 'Header1'.repeat(20),
        nextHeader2: 'Header2',
        nextOfferDescription: 'Description',
        nextPriceText: 'Price'
      };
  
      service.setBrandAndSizeValue(changedObj, form);
  
      expect(form.get('info.brandAndSize').value.length).toBe(100);
    });
  });  

  describe('setLeftNavOnShoppingListChange', () => {
    let storeGroupVersion: UntypedFormGroup;
  
    beforeEach(() => {
      storeGroupVersion = new UntypedFormGroup({
        podDetails: new UntypedFormGroup({
          leftNavCategory: new UntypedFormControl([])
        })
      });
    });
  
    it('should update leftNavCategory when nextValue is provided and prevValue exists', () => {
      const prevValue = 'OldCategory';
      const nextValue = 'NewCategory';
      storeGroupVersion.get('podDetails.leftNavCategory').setValue([prevValue]);
  
      service.setLeftNavOnShoppingListChange(prevValue, nextValue, storeGroupVersion);
  
      expect(storeGroupVersion.get('podDetails.leftNavCategory').value).toEqual([nextValue]);
      expect(service.selectedShoppingCategory).toBe(nextValue);
    });
  
    it('should add nextValue to leftNavCategory when prevValue does not exist', () => {
      const prevValue = 'OldCategory';
      const nextValue = 'NewCategory';
      storeGroupVersion.get('podDetails.leftNavCategory').setValue([]);
  
      service.setLeftNavOnShoppingListChange(prevValue, nextValue, storeGroupVersion);
  
      expect(storeGroupVersion.get('podDetails.leftNavCategory').value).toEqual([nextValue]);
      expect(service.selectedShoppingCategory).toBe(nextValue);
    });
  
    it('should set leftNavCategory to [nextValue] when leftNavCategory is empty', () => {
      const prevValue = 'OldCategory';
      const nextValue = 'NewCategory';
      storeGroupVersion.get('podDetails.leftNavCategory').setValue(null);
  
      service.setLeftNavOnShoppingListChange(prevValue, nextValue, storeGroupVersion);
  
      expect(storeGroupVersion.get('podDetails.leftNavCategory').value).toEqual([nextValue]);
      expect(service.selectedShoppingCategory).toBe(nextValue);
    });
  
    it('should not update leftNavCategory when nextValue is not provided', () => {
      const prevValue = 'OldCategory';
      const nextValue = null;
      storeGroupVersion.get('podDetails.leftNavCategory').setValue([prevValue]);
  
      service.setLeftNavOnShoppingListChange(prevValue, nextValue, storeGroupVersion);
  
      expect(storeGroupVersion.get('podDetails.leftNavCategory').value).toEqual([prevValue]);
      expect(service.selectedShoppingCategory).toBeUndefined();
    })
  })  

  describe('validateEndDt', () => {
    let form: UntypedFormGroup;
    let formType: string;
  
    beforeEach(() => {
      form = new UntypedFormGroup({
        rules: new UntypedFormGroup({
          startDate: new UntypedFormGroup({
            offerEffectiveStartDate: new UntypedFormControl('2024-01-01')
          }),
          endDate: new UntypedFormGroup({
            offerEffectiveEndDate: new UntypedFormControl('2024-12-31')
          })
        })
      });
  
      formType = 'template';
  
      spyOn(service, 'validateEndDt').and.callThrough();
    });
  
    it('should call validateEndDt with correct parameters', () => {
      service.validateEndDt(form, formType);
  
      expect(service.validateEndDt).toHaveBeenCalledWith(form, formType);
    });
  });
});



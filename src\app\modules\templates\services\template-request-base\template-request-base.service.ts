import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { OFFER_REQUEST_CREATE_RULES } from "@appModules/request/shared/rules/create.rules";
import { TEMPLATE_CONSTANTS } from "@appModules/templates/constants/template_constants";
import { TEMPLATE_CREATE_RULES } from "@appModules/templates/core/offer-template/details/shared/rules/rules";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { AppInjector } from "@appServices/common/app.injector.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { FileAttachService } from "@appServices/common/file-attach.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { UploadImagesService } from "@appServices/common/upload-images.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import { addError } from "@appUtilities/addError";
import { dateInOriginalFormat, mmDdYyyySlash_DateFormat, mmDdYyyySlash_DateFormatWithoutUTC } from "@appUtilities/date.utility";
import { resetFormControlError } from "@appUtilities/resetFormControlError";
import { scrollToErrorField } from "@appUtilities/scrollToError";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import { updateTreeValidity } from "@appUtilities/updateValueValidatityForTree";
import * as moment from "moment";
import { BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { Subject, forkJoin } from "rxjs";
import { map, pairwise } from "rxjs/operators";

@Injectable({
  providedIn: "root",
})
export class TemplateRequestBaseService extends UnsubscribeAdapter {
  requestFormService$: RequestFormService;
  programCdRule;
  offerRequestSaveApiGR: string;
  offerRequestCreateSubmitApiGR: string;
  offerRequestSaveSubmitApiGR: string;

  allocationDataList = new Subject();

  offerRequestSaveApiSPD: string;
  offerRequestCreateSubmitApiSPD: string;
  offerRequestSaveSubmitApiSPD: string;
  
 
  offerRequestSaveApiBPD: string;
  offerRequestCreateSubmitApiBPD: string;
  offerRequestSaveSubmitApiBPD: string;

  selectedShoppingCategory: string;
  storeGroupVersionControl;
  minDisplayEndDate: Date;
  minOfferEndDate: Date = new Date();
  minOfferStartDate: Date = new Date();
  programTypeChangedValue$ = new Subject();
  onSubProgramCdValueChange$ = new Subject();
  amountValueChange$ = new Subject();

  offerTemplateSaveApiBPD: string;
  public formBuilder: UntypedFormBuilder;
  public authService$: AuthService;
  private _fileAttachService: FileAttachService;
  public generalOfferTypeServic$: GeneralOfferTypeService;
 
  public facetItemService$: FacetItemService;
  public initialDataService$: InitialDataService;
  public http$: HttpClient;
  public router: Router;
  public uploadImagesService: UploadImagesService;
  public commonRouteService: CommonRouteService;
  public featureFlagService: FeatureFlagsService;
  private commonSearchService:CommonSearchService;
  toasterService$: ToastrService;
  public modalService$: BsModalService;
  // once the events loaded based on feature flag, will set the evnts here to reuse
  eventsList: any;
  isScene7InValidError: boolean = false;
  isUpdatingValueValidity: boolean;
  isDraftSaveAttempted: boolean = false;
  isReqSubmitAttempted: boolean = false;
  setCategoryValue$; //category in POD section to be derived when a "Buy" Product Group is selected
  requestForm: any;
  isSavedFromNavigationOverlay_OT = false; // If saved from the navigation overlay


  constructor() {
    super();
    const injector = AppInjector.getInjector();
    this.authService$ = injector.get(AuthService);
    this.formBuilder = injector.get(UntypedFormBuilder);
    this.http$ = injector.get(HttpClient);
    this._fileAttachService = injector.get(FileAttachService);
    this.initialDataService$ = injector.get(InitialDataService);
    this.facetItemService$ = injector.get(FacetItemService);
    this.modalService$ = injector.get(BsModalService);
    this.generalOfferTypeServic$ = injector.get(GeneralOfferTypeService);
    this.requestFormService$ = injector.get(RequestFormService);
    this.router = injector.get(Router);
    this.setCategoryValue$ = this.requestFormService$.setCategoryValue$;
    this.uploadImagesService = injector.get(UploadImagesService);
    this.toasterService$ = injector.get(ToastrService);
    this.commonRouteService = injector.get(CommonRouteService);
    this.featureFlagService = injector.get(FeatureFlagsService);
    this.commonSearchService = injector.get(CommonSearchService)

    this.offerRequestSaveApiGR = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.GR_OFFER_REQUEST_SAVE_API);
    this.offerRequestCreateSubmitApiGR = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.GR_OFFER_REQUEST_CREATE_SUBMIT_API);
    this.offerRequestSaveSubmitApiGR = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.GR_OFFER_REQUEST_SAVE_SUBMIT_API);

    this.offerRequestSaveApiSPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.SPD_OFFER_REQUEST_SAVE_API);
    this.offerRequestCreateSubmitApiSPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.SPD_OFFER_REQUEST_CREATE_SUBMIT_API);
    this.offerRequestSaveSubmitApiSPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.SPD_OFFER_REQUEST_SAVE_SUBMIT_API);
    
    this.offerRequestSaveApiBPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.BPD_OFFER_REQUEST_SAVE_API);
    this.offerRequestCreateSubmitApiBPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.BPD_OFFER_REQUEST_CREATE_SUBMIT_API);
    this.offerRequestSaveSubmitApiBPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.BPD_OFFER_REQUEST_SAVE_SUBMIT_API);


    /**
     * TO DO NEED TO SET IT UP IN CONFIGS FILE
     */
    this.offerTemplateSaveApiBPD = this.initialDataService$.getConfigUrls(TEMPLATE_CONSTANTS.BPD_TEMPLATE_SEARCH_API);
  }

  getProgramCode() {
    return this.isTemplateRouteActivated ? this.facetItemService$.templateProgramCodeSelected : this.facetItemService$.programCodeSelected;
  }
  openModal(template, options) {
    return this.modalService$.show(template, options);
  }
  resetVars() {
    this.minDisplayEndDate = null;
    this.minOfferEndDate = new Date();
    this.minOfferStartDate = new Date();
  }
  getProgramCodeRule() {
    this.programCdRule = this.isTemplateRouteActivated ? TEMPLATE_CREATE_RULES : OFFER_REQUEST_CREATE_RULES;
    return this.programCdRule[this.getProgramCode()];
  }
  getProgramCodeDisplayName() {
    return this.getProgramCodeRule().programCode as string;
  }
  getProgramCodeComponents() {
    return this.getProgramCodeRule().components;
  }
  getProgramCodeOfferRequest() {
    return this.getProgramCodeRule().offerRequest;
  }
  createFormFields(formControls: any = []): UntypedFormGroup {
    const formControl = {};
    formControls.forEach((control) => {
      /*Err Info: This condition will always return 'true' since JavaScript compares objects by reference, not value.
        Means else condition never ran in its life time so for now removing the condiotion
      */
      //if(toString.call(control.value) !== ["object Array"]){
        formControl[control.name] = new UntypedFormControl(control.value, null);
      //} else {
      //  formControl[control.name] = this.createFormFields(control.value);
      //}
    });
    return this.formBuilder.group(formControl);
  }
  getSaveAPI(formType) {
    return this[`${formType}SaveApi${this.getProgramCode()}`];
  }
  getSubmitAPI(formType) {
    return this[`${formType}SaveSubmitApi${this.getProgramCode()}`];
  }
  get isDivisionalGamesFeatureEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled("isDivisionalGamesFeatureEnabled");
  }
  getCreateAndSubmitAPI(formType) {
    return this[`${formType}CreateSubmitApi${this.getProgramCode()}`];
  }
  clearAsyncValidatiors(control: UntypedFormControl) {
    control.clearAsyncValidators();
    control.updateValueAndValidity();
  }
  getDefaultFormControls() {
    return {
      lastUpdatedTs: {},
      createdApplicationId: {},
      createdTs: {},
      createdUserId: {},
      id: {
        value: null,
        control: ["info", "id"],
      },
      deliveryChannel: {
        value: "DO",
        control: ["info", "deliveryChannel"],
      },
      programCode: {
        value: this.facetItemService$.programCodeSelected,
        control: ["info", "programCode"],
      },
      numOfVersions: {
        value: null,
        control: ["info", "numOfVersions"],
      },
      numOfTiers: {
        value: null,
        control: ["info", "numOfTiers"],
      },
      numOfProducts: {
        value: null,
        control: ["info", "numOfProducts"],
      },
      digitalUser: {
        value: null,
        control: ["info", "digitalUser"],
      },
      nonDigitalUser: {
        value: null,
        control: ["info", "nonDigitalUser"],
      },
      digitalStatus: {
        value: null,
        control: ["info", "digitalStatus"],
      },
      nonDigitalStatus: {
        value: null,
        control: ["info", "nonDigitalStatus"],
      },
      digitalEditStatus: {
        value: null,
        control: ["info", "digitalEditStatus"],
      },
      nonDigitalEditStatus: {
        value: null,
        control: ["info", "nonDigitalEditStatus"],
      },
      digitalUiStatus: {
        value: null,
        control: ["info", "digitalUiStatus"],
      },
      nonDigitalUiStatus: {
        value: null,
        control: ["info", "nonDigitalUiStatus"],
      },
      mobId: {
        value: null,
        control: ["info", "mobId"],
      },
      autoAssignMob: {
        value: true,
        control: ["info", "autoAssignMob"],
      },
      mobName: {
        value: null,
        control: ["info", "mobName"],
      },
    };
  }

  doSave(form, actionLabel, formType) {
    const {
      info: { digitalStatus, nonDigitalStatus, id },
    } = form.value;

    let isFormSubmitted = true;
    if (actionLabel !== "Submit" && (!id || (digitalStatus && digitalStatus === "I") || (nonDigitalStatus && nonDigitalStatus === "I"))) {
      isFormSubmitted = false;
    }
    resetFormControlError(form);

    /*If scene7 field is invalid, set it in variable as the next steps would clear all the validations.
     At later point, see the error based on this flag*/
    this.isScene7InValidError = false;
    if (this.isScene7Invalid()) {
      this.isScene7InValidError = true;
      }
      
    resetFormControlError(this.generalOfferTypeServic$?.generalOfferTypeForm);
    let url = this.getSaveAPI(formType);
    if (isFormSubmitted && actionLabel === "Submit") {
      url = !id ? this.getCreateAndSubmitAPI(formType) : this.getSubmitAPI(formType);
    } 
    this.setOREditStatus(form);
    this.setPriceUntilDateConversion(form);
    this.setOTStatusUntilDateConversion(form);
    this.setOTStatusFields(form);
    this.saveRequestTemplate(url, actionLabel, isFormSubmitted, form);
  }

  setOREditStatus(form) {
    const {
      info: { id },
    } = form.value;
    if (!id) {
      form.get("info")?.get("digitalEditStatus")?.reset();
      form.get("info")?.get("nonDigitalEditStatus")?.reset();
    }
  }

  setPriceUntilDateConversion(form) {
    let offerEffectiveEndDate = this.generalOfferTypeServic$?.generalOfferTypeForm?.get("generalInformationForm")?.get("priceUntil")?.value;
    offerEffectiveEndDate = dateInOriginalFormat({ date: offerEffectiveEndDate, isStartDate: true });
    form.get("rules")?.get("priceUntil")?.get("offerEffectiveEndDate")?.setValue(offerEffectiveEndDate);
    }
  setOTStatusUntilDateConversion(form) {
    const { info } = form.value;
    let otStatusSetUntilDate = info?.otStatusSetUntil;
    otStatusSetUntilDate = dateInOriginalFormat({ date: otStatusSetUntilDate, isStartDate: false });
    form.get("info")?.get("otStatusSetUntil")?.setValue(otStatusSetUntilDate);
  }
  setOTStatusFields(form) {
    const { info } = form.value;
    const infoControl = form.get("info");
    if (!["PARKED", "REMOVED"].includes(info?.otStatus)) {
      infoControl?.get("otStatusReason")?.setValue(null);
      infoControl?.get("otStatusSetUntil")?.setValue(null);
    }
    if (!["PARKED", "REMOVED", "REVIEW"].includes(info?.otStatus)) {
      infoControl?.get("otStatusReasonComment")?.setValue(null);
    }
    }
  
  setScene7FieldError() {
    if (this.isScene7InValidError) {
      addError({
        control: this.scene7ImageIdControl,
        errorObj: {
          customError: true,
        },
      });
    }
    }
  get scene7ImageIdControl() {
    return (this.generalOfferTypeServic$?.generalOfferTypeForm.get("offerRequestOffers") as UntypedFormArray)
      .at(0)
      .get("storeGroupVersion")
      .get("podDetails")
      ?.get("scene7ImageId");
  }
  isScene7Invalid() {
    const prCode = this.facetItemService$.programCodeSelected;

    //If scene7 field is invalid, dont save
    if (prCode && [CONSTANTS.GR, CONSTANTS.SPD].includes(prCode)) {
      const scene7ImageIdControl = this.scene7ImageIdControl;
      if (scene7ImageIdControl && scene7ImageIdControl.invalid && scene7ImageIdControl.errors.customError) {
        return true;
      }
    }
  }
  getControlFromBase(ctrlName, form) {
    const programCdRule = this.getProgramCodeRule();
    const formFieldObj = Object.keys(programCdRule).reduce((output, item) => {
      if (programCdRule[item][ctrlName]) {
        output = programCdRule[item][ctrlName];
      }
      return output;
    }, {});
    const filed = formFieldObj as any;
    const frmGroup = this.getFormGroup(filed.control, form) as UntypedFormControl;
    return frmGroup;
  }
  getErrorsForField(ctrl, control) {
    //For S7 field, if the entered image id is invalid, then show the error immedialty after the value input
    if (
      ctrl === "scene7ImageId" ||
      ((this.isDraftSaveAttempted || this.isReqSubmitAttempted) && control && control.untouched) ||
      (control && ctrl === "scene7ImageId" && control.untouched)
    ) {
      return control?.errors;
    } else if (control) {
      return control?.setErrors(null);
    }
  }
  setValidators(formData, action, form) {
    if (formData && formData.length) {
      formData.forEach((element) => {
        const formControl = element["formGroup"] as UntypedFormControl;
        if (!formControl) {
          return false;
        }
        formControl.clearValidators();
        formControl.setErrors(null);
        if (
          this.isByPassValidateBeforeProcess(element.byPassValidateBeforeProcess, action, form) &&
          element.validate &&
          element.validate.includes(action)
        ) {
          formControl.setValidators(element.validators);
          formControl.updateValueAndValidity();
        }
      });
    }
  }
  isByPassValidateBeforeProcess(byPassValidateBeforeProcess, action, form) {
    if (!byPassValidateBeforeProcess || action === "process") {
      return true;
    }
    const {
      info: { digitalStatus },
    } = form.value;
    return digitalStatus && !["I", "S"].includes(digitalStatus);
  }

  notifySaveOrSubmitAttempt(submit, form, action = "") {
    form.markAsUntouched();
    this.generalOfferTypeServic$.generalOfferTypeForm.markAsUntouched();
    action = !action ? (submit ? "submit" : "save") : action;
    this.requestFormService$.isReqSubmitAttempted$.next(submit);
    this.requestFormService$.isDraftSaveAttempted.next(!submit);
    this.isUpdatingValueValidity = true;
    this.setValidators(this.getFormDetailsForValidators(form), action, form);
    submit && updateTreeValidity(this.generalOfferTypeServic$?.generalOfferTypeForm);
    updateTreeValidity(form as UntypedFormGroup);
    this.isUpdatingValueValidity = false;
  }
  getFormDetailsForValidators(form) {
    const programCdRule = this.getProgramCodeRule();
    let finalArray = [];
    const formFields = [...Object.keys(programCdRule.offerRequest), ...Object.keys(programCdRule?.podDetails || {})];

    formFields.forEach((field) => {
      let formObj = {};
      const formFieldObj = Object.keys(programCdRule).reduce((output, item) => {
        if (programCdRule[item][field]) {
          output = programCdRule[item][field];
        }
        return output;
      }, {});
      const filedObj = formFieldObj as any;
      const controls = filedObj && filedObj.control;
        if (controls) {
        const validators = [...filedObj.validators||[],...this.getCustomValidator(controls)]
        const validate = [...filedObj.validate || [], ...this.getCustomValidate(controls)]
        formObj["formGroup"] = this.getFormGroup(controls, form);
        formObj["control"] = controls[controls.length - 1];
        formObj["validators"] = validators;
        formObj["validate"] = validate;
        formObj["byPassValidateBeforeProcess"] = filedObj.byPassValidateBeforeProcess;
        finalArray.push(formObj);         
      }
    });
    return finalArray;
    }
    getCustomValidator(filedObj) {
        if (filedObj.includes("customPeriod")) {
            return [this.validateCustomPeriod(), this.validateCustomField()];
        } else if (filedObj.includes("usageLimitPerUser")) {
            return [this.validateCustomField()];
        } else if (filedObj.includes("programSubType")) {
            return [this.validateRX()];
        }
        return []

    }
    getCustomValidate(filedObj) {
      if (filedObj.includes("programSubType")) {
            return this.validateActionForSubType();
        }
        return []
    }
    validateActionForSubType() {
        //Validate on Save and Submit if program type is RX/Health/Pharamacy program type
        const info = this.generalOfferTypeServic$.requestFormService.offerRequestBaseService.requestForm.controls.info as UntypedFormGroup
        const programType = info?.get("programType") as UntypedFormControl
        return programType.value === "HEALTH" ? ['submit', 'save'] : []
    }
    validateCustomPeriodRule(control) {
        const rules = this.generalOfferTypeServic$.requestFormService.offerRequestBaseService.requestForm.controls.rules as UntypedFormGroup
        const usageLimitPerUser = rules?.get("usageLimitPerUser") as UntypedFormControl

        return control?.value == 1 && usageLimitPerUser?.value == 1
    }
    
    validateCustomFields(control) {
        //Validate if control value is zero
       return control?.value == 0;   
    }


    validateCustomField() {
        return (control: UntypedFormControl) => {
            if (this.validateCustomFields(control)) {
                return { error: true };
            } 
        };
    }
    
    
    validateCustomPeriod() {
        return (control: UntypedFormControl) => {
            if (this.validateCustomPeriodRule(control)) {
                return { customError: true };
            }
        };
    }
    validateRX() {
        return (control: UntypedFormControl) => {
            if (this.validateRXOffers(control)) {
                return { customError: true };
            }
        };
    }
    validateRXOffers(control) {
        // Display error if program type is Health and ProgramSubType is  not Points or Coupons.
        const info = this.generalOfferTypeServic$.requestFormService.offerRequestBaseService.requestForm.controls.info as UntypedFormGroup
        const programType = info?.get("programType") as UntypedFormControl
        return !["COUPONS", "POINTS"].includes(control?.value) && programType.value === "HEALTH"
    }

  getFormGroup(controls, form) {
    const podDetails = controls && controls.length && controls[0];
    if (podDetails === "podDetails") {
      const offerRequestOffers = this.generalOfferTypeServic$?.generalOfferTypeForm?.get("offerRequestOffers") as UntypedFormArray;
      const offerRequestOffer = offerRequestOffers?.at(0) as UntypedFormControl;
      const storeGroupVersion = offerRequestOffer?.get("storeGroupVersion");
      return this.getFormFiledControl(controls, storeGroupVersion);
    } else {
      return this.getFormFiledControl(controls, form);
    }
  }
  getFormFiledControl(controls, reqForm) {
    if (controls && controls.length) {
      controls.forEach((cntrl, len) => {
        const frmControl = cntrl && reqForm && (reqForm.get(cntrl) as UntypedFormGroup);
        reqForm = frmControl;
      });
      return reqForm;
    }
  }
  isFormValid(form) {
    let isFormValid = true;
    isFormValid = this.requestFormService$.isValidTimeRule();
    if (!form?.valid || !this.generalOfferTypeServic$?.generalOfferTypeForm?.valid) {
      setTimeout(() => {
        scrollToErrorField();
      }, 0);
      isFormValid = false;
    }
    return isFormValid;
  }
  templateValidation(form, validateSaveSubmit) {
    this.isDraftSaveAttempted = false;
    this.isReqSubmitAttempted = true;
    let {
      info: { otStatus },
    } = form && form.value;
    if (otStatus === "ACTIVE") {
      this.notifySaveOrSubmitAttempt(validateSaveSubmit, form);
      this.setScene7FieldError();
      if (!this.isFormValid(form) || this.isScene7Invalid()) {
        return false;
      }
    }
    return true;
  }

  saveRequestTemplate(url, toaster, validateSaveSubmit, form) {
    if (this.isTemplateRouteActivated) {
      let isTemplateValid = this.templateValidation(form, validateSaveSubmit);
      if (!isTemplateValid) {
        return;
      }
    } else {
      let isRequestValid = this.requestValidation(form, validateSaveSubmit);
      if (!isRequestValid) {
        return;
      }
    }
    // ACIP-382481: StartDate may be disabled so enabling it before save    
    form.get("rules.startDate.offerEffectiveStartDate")?.enable();
    this.updateAllocationCode(form);
    this.setGRGetMiles(form);
    this.updateDateFormatOnSave(form);
    this.setPodStoreGroupsForSpd();
    this.setQualificationAndBenefit(form);
    this.setOfferRequestType(form);
    this.updateTimeandDate(form);
    this.setAdditionConvertTypes(form);
    this.setCachedDigitalData(form);
    this.removeRewardsRequired(form);
    this.authService$.onUserDataAvailable(this.saveOfferRequest.bind(this, url, toaster, form));
  }
  removeRewardsRequired(form) {
    form.get("rules").removeControl("rewardsRequired");
  }
  removeCustomUsage(form) {
    if(!this.featureFlagService.isEnableCustomUsageField)
      form.get("rules").removeControl("customUsage");
  }
  updateAllocationCode(form) {
    const { info } = form?.value;
    if (info?.allocationCode) {
      form.get("info")?.get("allocationCode")?.setValue(info.allocationCode.split(" - ")[0]);
    }
    if (info?.allocationCodeName) {
      form.get("info")?.get("allocationCodeName")?.setValue(info.allocationCode?.split(" - ")[1]);
    }
  }
  /**
   * Before save an OR need to set podStoreGroupsId and podStoreGroup Names value as same as Digital one.
   */
  setGRGetMiles(form){
      const offerRequestOffer = (this.generalOfferTypeServic$?.generalOfferTypeForm?.get('offerRequestOffers') as UntypedFormArray)?.at(0);
      const storeGroupVersion = offerRequestOffer?.get('storeGroupVersion');
      const productGroupVersion = (storeGroupVersion?.get('productGroupVersions') as UntypedFormArray)?.at(0);
      const discountVersion = productGroupVersion?.get('discountVersion');
      const discount = (discountVersion?.get('discounts') as UntypedFormArray)?.at(0);
      const tier = (discount?.get('tiers') as UntypedFormArray)?.at(0);
      const milesValue = tier?.get('miles')?.value;
      const milesControl = form.get("info")?.get("getMiles");
      milesControl?.setValue(this.generalOfferTypeServic$?.isGRAirmilesPrograTypeSelected ? milesValue : null);

  }
  setPodStoreGroupsForSpd() {
    const pCode = this.facetItemService$.programCodeSelected;
    const storeGroupCtrl = this.storeGroupVersion.get("storeGroup");
    
    if ([CONSTANTS.SPD, CONSTANTS.BPD].includes(pCode) && !this.isTemplateRouteActivated) {
      ["StoreGroupIds", "StoreGroupNames"].forEach((sg) => {
        const digitalSgValue = storeGroupCtrl?.get(`digitalRedemption${sg}`)?.value;
        storeGroupCtrl?.get(`pod${sg}`)?.setValue(digitalSgValue);
      });
    }

  }

  requestValidation(form, validateSaveSubmit) {
    this.isDraftSaveAttempted = true;
    this.isReqSubmitAttempted = false;
    this.notifySaveOrSubmitAttempt(validateSaveSubmit, form);
    this.setScene7FieldError();
    this.validateBehavioralContinuty(form);
    if (!this.isFormValid(form) || this.isScene7Invalid()) {
      return false;
    }
    return true;
  }

  validateBehavioralContinuty(form) {
    const _deliveryChannel = form?.get("info")?.get("deliveryChannel")?.value;
    if(_deliveryChannel !== CONSTANTS.BEHAVIORAL_CONTINUTY_CODE){
      form?.get("info")?.removeControl("behavioralCondition");
    }
  }

  fileAttachment(id, successMessage) {
    if (this.requestFormService$.filesList.getValue().length > 0) {
      const requests = this.uploadFiles(id, this.requestFormService$.filesList.getValue());
      this.requestFormService$.filesList.next([]);
      requests.subscribe({
        next: (data: any) => {
          for (let file of data) {
            let fileData: any;
            if (file["url"] && file["url"].length > 0) {
              fileData = file;
              fileData.uploadStatus = true;
              fileData.id = id;
              // check the null or undefined for the attached file list
              if (!this.requestFormService$.attachedFilesList) {
                this.requestFormService$.attachedFilesList = [];
              }

              this.requestFormService$.attachedFilesList.push(fileData);
              // //this.offerRequestId = id;
              this.requestFormService$.uploadedFilesList = [];
              this.requestFormService$.fileUploadBool.next(true);
            }
          }
          this.navigateToSummaryAfterSave(successMessage, id);
        },
        error: (err) => console.error(`${err}`)
      }
      );
    } else {
      this.navigateToSummaryAfterSave(successMessage, id);
    }
  }
  navigateToSummaryAfterSave(successMessage, id) {
    const pCode = this.facetItemService$.programCodeSelected;
    if (this.isTemplateRouteActivated) {
      this.navigateToTemplateSummary(id);
    } else {
      this.requestFormService$.navigateToSummary(successMessage, id);
    }
  }
  navigateToTemplateSummary(templateId) {
    if(this.isSavedFromNavigationOverlay_OT){
       return false;
    }
    const templatePCode = this.facetItemService$.templateProgramCodeSelected;
    let summarySrc = ROUTES_CONST.TEMPLATES.BPDSummary;
    if (templatePCode) {
      summarySrc = ROUTES_CONST.TEMPLATES[`${templatePCode}Summary`];
    }
    this.router.navigateByUrl(`/${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${summarySrc}/${templateId}`);
  }
  uploadFiles(id, files) {
    const observableBatch = files
      .filter(file => file)
      .map(file => this._fileAttachService.uploadFile(file, id).pipe(map(res => res))); 

    return forkJoin(observableBatch); 
  }
  handleResponse({res, successMessage, form}) {
    let id = this.getReqId(res);
    const { generalOfferTypeService } = this.requestFormService$;
    generalOfferTypeService.generalOfferTypeForm.markAsPristine();
    form.markAsPristine();
    this.requestFormService$.isReqSubmitAttempted$.next(false);
    this.requestFormService$.isDraftSaveAttempted.next(false);
     //If Saving Copied Expired OR, OR is not Expired any more
    this.commonSearchService.isShowExpiredInQuery = false;
    generalOfferTypeService.addNewRow$.next("");
    this.fileAttachment(id, successMessage);
    
  }
  getReqId(obj) {
    return Object.keys(obj)[0];
  }

  setIsCopiedFlag(form){
    let reqObj = form.value;
    const {
      info: { templateId, mobId, id },
    } = reqObj;
   
    //Incase of BPD copy, set the flag
   if(this.commonRouteService.isBpdReqPage && templateId && mobId && !id){
    reqObj.info.isCopied = true;
   }
   
   return reqObj;
  }

  saveOfferRequest(apiUrl, toaster, form) {
   
    const {
      info: { id },
    } = form.value;

    const requestData = this.setIsCopiedFlag(form);
    
    let searchInput = { ...requestData, updateOffers: true, reqObj: { headers: this.getHeaders() } };
    const successMessage = toaster === "Submit" ? "Offer request submitted" : "Offer request saved";
    //Fullfillment channel should be null for GR,MF and SC(only "Clip and Click" and "Print" )
    if(!this.requestFormService$.checkFulfillmentChannelEnabled(searchInput?.info?.programCode,searchInput?.info?.deliveryChannel)){
      if(searchInput?.info?.fulfillmentChannel){
        searchInput.info.fulfillmentChannel=null;
      }
    }
    if (!id) {
      return this.http$.post(apiUrl, searchInput).subscribe((res) => {
        this.handleResponse({res, successMessage, form});
      });
    } else {
      searchInput.allOffersAlongWithOfferRequestUpdate = true;
      return this.http$.put(apiUrl, searchInput).subscribe((res) => {
        this.handleResponse({res, successMessage, form});
      });
    }

  }

  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService$.getTokenString(),
    };
  }
  setCachedDigitalData(form) {
    const {
      info: { digitalStatus, nonDigitalStatus },
    } = form.value;
    if (digitalStatus && digitalStatus === "P") {
      if (this.requestFormService$?.cachedDigitalOfferStatus) {
        form.get("cachedDigitalOfferStatus")?.setValue(this.requestFormService$?.cachedDigitalOfferStatus);
      }
    } else if (nonDigitalStatus && nonDigitalStatus === "P") {
      if (this.requestFormService$?.cachedNonDigitalOfferStatus) {
        form.get("cachedNonDigitalOfferStatus")?.setValue(this.requestFormService$?.cachedNonDigitalOfferStatus);
      }
    }
  }
  addAllocation(data, form) {
    if (data) {
      const { newAllocation, allocationData } = data;
      ["allocationCode", "allocationCodeName"].forEach((key) => this.getControlFromBase(key, form).setValue(newAllocation[key]));
      this.allocationDataList?.next(allocationData);
      form?.markAsDirty();
    }
  }

  setAdditionConvertTypes(form) {
    const { info } = form?.value;
    info.regionId = info?.regionId?.split(" ")[0];
    if (info?.allocationCode) {
      info.allocationCode = info.allocationCode?.split(" - ")[0];
    }
  }

  formatHrsAndMins(timeArray, timeGroup, key) {
    // prepending zeros for time
    if (timeArray?.length === 5) {
      timeGroup[`${key}Hr`] = this.requestFormService$.prependZero(timeArray[1]);
      timeGroup[`${key}Min`] = this.requestFormService$.prependZero(timeArray[2]);
      timeGroup[`${key}Period`] = timeArray[3];
    }
    timeGroup[key] = `${timeGroup[`${key}Hr`]}:${timeGroup[`${key}Min`]} ${timeGroup[`${key}Period`]}`;
  }
    formatTimeForPayload(timeGroup) {
    // formatting time object in request payload
    // ACIP-44095: changing as per new sonar guidelines
    let time = {};
    let timestart = `2023-01-01 ${timeGroup.start}`;
    timeGroup.start = moment(timestart).format('hh:mm A');
    timeGroup.startHr = moment(timestart).format('hh');
    timeGroup.startMin = moment(timestart).format('mm');
    timeGroup.startPeriod = moment(timestart).format('A');

    let timeend = `2023-01-01 ${timeGroup.end}`;
    timeGroup.end = moment(timeend).format('hh:mm A');
    timeGroup.endHr = moment(timeend).format('hh');
    timeGroup.endMin = moment(timeend).format('mm');
    timeGroup.endPeriod = moment(timeend).format('A');
    
    time = {
      start: timeGroup.start,
      end: timeGroup.end,
    };
    return time;
  }
  updateTimeandDate(form) {
    // taking time from offerRuleTimeGroup and formatting time in request payload
    let timeGroup = this.requestFormService$.requestForm.value.offerRuleTimeGroup;
    let {
      rules: { qualificationAndBenefit },
    } = form?.value;
    if (timeGroup && timeGroup.start && timeGroup.end) {
      const formattedTime = this.formatTimeForPayload(timeGroup);
      qualificationAndBenefit.time = formattedTime;
    } else {
      qualificationAndBenefit.time = null;
    }
    if (form.value["offerRuleTimeGroup"]) {
      delete form.value["offerRuleTimeGroup"];
    }
  }
  setOfferRequestType(form) {
    const generalInformationForm = this.generalOfferTypeServic$.generalOfferTypeForm.value.generalInformationForm;
    let { info } = form.value,
      numOfTiers;
    const offerRequestType = this.generalOfferTypeServic$.getKeyByValue(null, generalInformationForm.type);
    if (offerRequestType === "WOD_OR_POD") {
      numOfTiers = generalInformationForm.tiers;
    }
    info.numOfTiers = numOfTiers;
    info.offerRequestType = offerRequestType;
    info.isRedeemableInSameTransaction = generalInformationForm.isRedeemableInSameTransaction;
  }

  get isTemplateRouteActivated() {
    return this.commonRouteService.currentActivatedRoute?.includes("template");
  }
  setQualificationAndBenefit(form) {
    this.trimPodValuesBeforeSave();
    const offerRequestOffers = this.requestFormService$?.parseOfferRequestOffers(form.value.info.deliveryChannel);
    const programCode = this.facetItemService$.programCodeSelected;
    const {
      rules: { qualificationAndBenefit = {} },
    } = form.value;
    const qualificationAndBenefitType = this.isTemplateRouteActivated
      ? "offerTemplateOffers"
      : `${this.facetItemService$.reqOffersObjkey}OfferRequestOffers`;
    qualificationAndBenefit[qualificationAndBenefitType] = offerRequestOffers;
  }
  get podDetails() {
    return this.storeGroupVersion?.get("podDetails");
  }
  get storeGroupVersion() {
    let oROArray = this.generalOfferTypeServic$?.generalOfferTypeForm?.get("offerRequestOffers") as UntypedFormArray;
    return oROArray?.at(0)?.get("storeGroupVersion") as UntypedFormGroup;
  }
  trimPodValuesBeforeSave() {
    const trimmedOptions = ["headline1", "headline2", "offerDescription", "priceText", "scene7ImageId"];
    if (this.podDetails) {
      Object.keys(this.podDetails?.value).forEach((key) => {
        const control = this.podDetails?.get(key);
        if (control && trimmedOptions?.includes(key)) {
          control.setValue(control?.value?.replace(/\s+$/, ""));
        }
      });
    }
    }


  updateDateFormatOnSave(form) {
    let {
      rules: { startDate, endDate },
    } = form && form.value;
    if (startDate?.offerEffectiveStartDate) {
      startDate.offerEffectiveStartDate = dateInOriginalFormat({
        date: startDate.offerEffectiveStartDate,
        isStartDate: true,
      });
    }
    if (endDate?.offerEffectiveEndDate) {
      endDate.offerEffectiveEndDate = dateInOriginalFormat({
        date: endDate.offerEffectiveEndDate,
        isStartDate: false,
      });
    }
  }
  addFormControl(name, formControl, form) {
    if (form?.addControl) {
      form?.addControl(name, formControl);
    }
    return form?.get(name);
  }
  addFormuilder() {
    return this.formBuilder.group({});
  }
  setFormControls(data, offerRequest, form) {
    let fields;
    if (data && Object.keys(data).length) {
      const parse = JSON.parse(JSON.stringify(data));
      fields = {};
      const getKeys = this.generateFormControlFieldsKeys(data);
      this.createFields(fields, getKeys, parse, JSON.parse(offerRequest));
    } else {
      fields = { ...this.getDefaultFormControls(), ...JSON.parse(offerRequest) };
    }

    this.createFormControls(fields, form, data);
  }

  getValue(controls, data, field) {
    let value, getObj;
    if (controls && controls.length) {
      controls.forEach((element, index) => {
        if (index !== controls.length - 1) {
          getObj = getObj ? getObj[element] : data[element];
        } else {
          value = getObj && getObj[field];
        }
      });
    } else {
      value = data[field];
    }

    return value;
  }
  createFields(formControl, getKeys, parse, offerRequest) {
    getKeys?.forEach((element) => {
      const control = element.split("."),
        value = control.reduce((out, ele) => {
          out = out ? out[ele] : parse[ele];
          return out;
        }, "");
      let checkProp = control[control.length - 1];
      if (formControl.hasOwnProperty(checkProp)) {
        checkProp = element.split(".").join("_");
      }
      formControl[checkProp] = {
        value,
        control,
      };
    });
    Object.keys(formControl).forEach((ele) => {
      const field = offerRequest[ele];
      if (offerRequest[ele]) {
        const { control, value } = formControl[ele];
        field.control = control;
        field.value = value;
      }
    });
    formControl = { ...formControl, ...{ ...offerRequest } };
  }
  generateFormControlFieldsKeys(data, prefix = "") {
    return Object.keys(data).reduce((res, el) => {
      if (Array.isArray(data[el])) {
        if(el==="defaultPromoWeekIds"){
          return [...res, prefix + el];
        }
        return res;
      } else if (typeof data[el] === "object" && data[el] !== null) {
        return [...res, ...this.generateFormControlFieldsKeys(data[el], prefix + el + ".")];
      }
      return [...res, prefix + el];
    }, []);
  }
  checkFeatureFlagEnabled(value) {
    return this.featureFlagService.isFeatureFlagEnabled(value);
  }
  createFormControls(formControls: any = [], requestForm, data = {}, isEdit = false) {
    const controls = Object.keys(formControls);

    controls?.forEach((element) => {
      const { value, control, featureFlagCheck = false, featureFlag = null } = formControls[element];
      if (featureFlagCheck && featureFlag) {
        if (!this.checkFeatureFlagEnabled(featureFlag)) {
          return;
        }
      }
      const fControl = requestForm?.get(element);
      let cValue = data ? this.getValue(control, data, element) : null;
      cValue = isEdit ? cValue : value;
      if ((element.includes("Date") || element.includes("otStatusSetUntil")) && cValue) {
        cValue = mmDdYyyySlash_DateFormat(cValue);
      }
      let reqForm = requestForm;
      let frmControl;
      // when there is no time value, setting up default controls value
      if (element === "time" && !cValue) {
        cValue = { start: null, end: null };
      }
      if (control && control.length) {
        control.forEach((cntrl, len) => {
          frmControl = reqForm?.get(cntrl);
          if (!frmControl && len !== control.length - 1) {
            reqForm = this.addFormControl(cntrl, this.addFormuilder(), reqForm);
          } else if (!frmControl) {
            reqForm = this.addFormControl(cntrl, new UntypedFormControl(cValue), reqForm);
            frmControl = reqForm;
          } else {
            reqForm = frmControl;
          }
        });
      } else if (!fControl) {
        this.addFormControl(element, new UntypedFormControl(cValue), reqForm);
      }
      if (frmControl) {
        if (frmControl instanceof UntypedFormControl) {
          frmControl?.setValue(cValue);
          frmControl?.clearValidators();
        }
      }
    });
  }
    get multiClipLimitCtrl() {
        return this.podDetails?.get('multiClipLimit');
    }
    updatePodDataOnValueChanges(form) {
        this.storeGroupVersionControl.valueChanges.pipe(pairwise()).subscribe(([prev, next]: [any, any]) => {
            const { podDetails: nextPodDetails } = next,
                { podDetails: prevPodDetails } = prev;

            if (!nextPodDetails || !prevPodDetails) {
                return false;
            }
            const {
                headline1: nextHeader1 = "",
                headline2: nextHeader2 = "",
                offerDescription: nextOfferDescription = "",
                priceText: nextPriceText = "",
                shoppingListCategory: nextShoppingListCategory = "",
            } = (nextPodDetails);
            
            const { shoppingListCategory: prevShoppingListCategory = "" } = (prevPodDetails);

            this.setBrandAndSizeValue(
                {
                    nextHeader1,
                    nextHeader2,
                    nextOfferDescription,
                    nextPriceText,
                },
                form
            );

            if (nextShoppingListCategory) {
                this.selectedShoppingCategory = nextShoppingListCategory;
            }
            if (prevShoppingListCategory !== nextShoppingListCategory) {
                this.setLeftNavOnShoppingListChange(prevShoppingListCategory, nextShoppingListCategory, this.storeGroupVersionControl);
            }
        });

        const startDate = form?.get("rules")?.get("startDate") as UntypedFormGroup;
        startDate?.valueChanges.subscribe((formValue) => {
            if (formValue.offerEffectiveStartDate) {
                this.setDisplayDates(formValue, this.storeGroupVersionControl);
            }
        });
       
    }

  setDisplayDates(formValue, storeGroupVersion) {
    // setting display start date and min date for display end date on offer start date change.
      const offerStartDate = formValue && formValue.offerEffectiveStartDate;
      
    let minValue;
    if (offerStartDate) {
      const displayStartDate = storeGroupVersion.get("podDetails")?.get("displayStartDate") as UntypedFormControl;
      displayStartDate?.setValue(mmDdYyyySlash_DateFormatWithoutUTC(offerStartDate));

      if (offerStartDate instanceof Date) {
        minValue = offerStartDate;
      } else {
        minValue = new Date(offerStartDate);
      }
      this.minDisplayEndDate = minValue;
    } else {
      this.minDisplayEndDate = new Date();
      }

  }

  setBrandAndSizeValue(changedObj, form) {
    const { nextHeader1, nextHeader2, nextOfferDescription, nextPriceText } = changedObj;
    let brandAndSize = form?.get("info")?.get("brandAndSize");
    let derivedBS = `${nextHeader1 ? nextHeader1 : ""}${nextHeader2 ? (" " + nextHeader2) : ""}${
      nextOfferDescription ? (" " + nextOfferDescription) : ""
    }${nextPriceText ? " @" : ""}${nextPriceText ? (" " + nextPriceText) : ""}`;
    if (derivedBS?.length > 100) {
      derivedBS = derivedBS.substring(0, 100);
    }
    brandAndSize?.setValue(derivedBS.trim());
  }

  setLeftNavOnShoppingListChange(prevValue, nextValue, storeGroupVersion) {
    if (nextValue) {
      const leftNavCategoryCtrl = storeGroupVersion.get("podDetails")?.get("leftNavCategory") as UntypedFormControl;
      const leftNavCategoryValue = leftNavCategoryCtrl?.value;
      if (leftNavCategoryValue && leftNavCategoryValue.length) {
        const index = leftNavCategoryValue.indexOf(prevValue);
        if (index > -1) {
          leftNavCategoryValue.splice(index, 1);
        }
        leftNavCategoryValue.push(nextValue);
        leftNavCategoryCtrl.setValue(leftNavCategoryValue);
      } else {
        leftNavCategoryCtrl?.setValue([nextValue], { onlySelf: true });
      }
      this.selectedShoppingCategory = nextValue;
    }
  }

  updateDisplayStartDate(form) {
    let displayStartDate = form?.get("rules")?.get("startDate")?.get("offerEffectiveStartDate")?.value;
    if (displayStartDate) {
      displayStartDate = mmDdYyyySlash_DateFormatWithoutUTC(displayStartDate);
      let startDateCtrl = this.podDetails?.get("displayStartDate");
      startDateCtrl?.setValue(displayStartDate || null);
      startDateCtrl?.updateValueAndValidity();
      this.minDisplayEndDate = new Date(displayStartDate);
    }
  }
    updateDisplayEndDateValue(value) {
        let displayEndDate = this.podDetails?.get("displayEndDate");
        displayEndDate?.setValue(value);
    }


  setCategoryValue() {
    this.subs.sink = this.setCategoryValue$.subscribe((categoryId) => {
      //If the shopping list category is entered by the user, dont override it
      if (this.generalOfferTypeServic$.isCategoryUserUpdated) {
        return false;
      }

      let leftNavCategory = this.podDetails?.get("leftNavCategory"),
          shoppingListCategory = this.podDetails?.get("shoppingListCategory"),
          lNavValue: any = null;

      if (categoryId) {
        lNavValue = [categoryId];
      }
      
      //Incase the categoryId for the product group  is not available set lnav to empty
      leftNavCategory?.setValue(lNavValue);
      shoppingListCategory?.setValue(categoryId);
    });
  }

  searchProductImage(event) {
    if (event.target.value && event.target.value.length > 0) {

      this.uploadImagesService.sendLoading(true);
      this.uploadImagesService.getImageID(event.target.value).subscribe({
        next: (response) => {
          if(response.type === "image/jpeg")
          {
            //Image Found
            this.uploadImagesService.sendImage(event.target.value);
            this.uploadImagesService.sendLoading(false);
            this.scene7ImageId?.setErrors(null);
          }
       
        },
        error: (error) => {
          //When Image Not Found response.type will be text
          
          let obj = {
            customError: true,
          };
          addError({ control: this.scene7ImageId, errorObj: obj });
        }
     } );
    } else {
      this.toasterService$.warning("Please add an image", "Warning !");
      this.uploadImagesService.sendImage(undefined);
    }
  }

  get scene7ImageId() {
    return this.podDetails?.get("scene7ImageId");
  }
  get podUsageControl() {
    return this.podDetails?.get("podUsageLimit");
    }



  checkCustomValidation(podDetails, form, formType) {
    const { displayEndDate } = podDetails;
    if (displayEndDate) {
      displayEndDate.validators.push(this.displayEndDateValidator.bind(this, form, formType));
      }
  }

  displayEndDateValidator(form, formType) {
    return this.validateEndDt(form, formType) || null;
  }

  validateEndDt(form, formType) {
    const startDate = form?.get("rules")?.get("startDate")?.get("offerEffectiveStartDate")?.value;
    const endDate = form?.get("rules")?.get("endDate")?.get("offerEffectiveEndDate")?.value;
    let offerRequestOffers;
    if (formType === "template") {
      offerRequestOffers = this.generalOfferTypeServic$?.generalOfferTypeForm?.get("offerTemplateOffers") as UntypedFormArray;
    } else {
      offerRequestOffers = this.generalOfferTypeServic$?.generalOfferTypeForm?.get("offerRequestOffers") as UntypedFormArray;
    }
    const offerRequestOffer = offerRequestOffers?.at(0) as UntypedFormControl;
    const storeGroupVersion = offerRequestOffer?.get("storeGroupVersion");

    const displayEndDate = storeGroupVersion?.get("podDetails").get("displayEndDate") as UntypedFormControl;
    const displayEndDateValue = displayEndDate?.value;

    let offerEndDate = endDate,
      offerStartDate = startDate,
      podOfferEndDate = displayEndDateValue;

    offerEndDate = offerEndDate ? new Date(mmDdYyyySlash_DateFormatWithoutUTC(offerEndDate)).getTime() : null;
    podOfferEndDate = podOfferEndDate ? new Date(mmDdYyyySlash_DateFormatWithoutUTC(podOfferEndDate)).getTime() : null;
    offerStartDate = offerStartDate ? new Date(mmDdYyyySlash_DateFormatWithoutUTC(offerStartDate)).getTime() : null;

    //Check if end date is later than start date and should be greater than start date.
    if (!podOfferEndDate && displayEndDate && !displayEndDate?.errors?.required) {
      return { required: true };
    } else {
      if (podOfferEndDate && (podOfferEndDate > offerEndDate || podOfferEndDate < offerStartDate)) {
        return { customError: true };
      } else {
        return false;
      }
    }
  }
}

<div class="fields-container">
  <form [formGroup]="createOfferRequestForm">
    <label for="period" class="px-0 font-weight-bold">Period</label>
    <select id="periodtype" class="custom-select form-control"
      formControlName="period">
      <option></option>
      <option *ngFor="let period of periodData" [value]="period.label">
        {{period.label}}
      </option>
    </select>
  </form>
  <div class="row">
    <div class="col d-flex mt-3 mb-3 justify-content-end pt-20">
      <label class="anchor-link-blue cursor-pointer mr-4 cancel-btn cancel-link" (click)="modalRef.hide()">
        <u>Cancel</u>
      </label>
      <button [disabled]="!createOfferRequestForm.valid" class="btn btn-primary font-weight-bolder submit-btn"
        (click)="createOfferRequest()">
        Create Offer Requests
      </button>
    </div>
  </div>
</div>
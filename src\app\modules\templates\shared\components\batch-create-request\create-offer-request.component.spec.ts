import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import {
  HttpClientTestingModule,
  HttpTestingController
} from '@angular/common/http/testing';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { ToastrService } from 'ngx-toastr';
import { CreateOfferRequestComponent } from './create-offer-request.component';
import { of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';

describe('CreateOfferRequestComponent', () => {
  let component: CreateOfferRequestComponent;
  let fixture: ComponentFixture<CreateOfferRequestComponent>;

  beforeEach(() => {
    const initialDataServiceStub = () => ({
      getConfigUrls: gENERATE_PROMO_WEEK_DETAILS => ({})
    });
    const bsModalRefStub = () => ({
      hide: () => ({})
    })
    const baseInputSearchServiceStub = () => ({});
    const toastrServiceStub = () => ({
      success: (string, string1, object) => ({})
    });
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [CreateOfferRequestComponent],
      providers: [
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        {
          provide: BaseInputSearchService,
          useFactory: baseInputSearchServiceStub
        },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: BsModalRef, useFactory: bsModalRefStub}
      ]
    });
    fixture = TestBed.createComponent(CreateOfferRequestComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      spyOn(component, 'createFormControl').and.callThrough();
      spyOn(component, "getLastPeriodOptions").and.returnValue(of([{}]));
      component.ngOnInit();
      expect(component.createFormControl).toHaveBeenCalled();
      expect(component.getLastPeriodOptions).toHaveBeenCalled();

    });
  });

  describe('getLastPeriodOptions', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      const HttpClientStub = TestBed.inject(HttpClient);
      spyOn(initialDataServiceStub, 'getConfigUrls');
      spyOn(HttpClientStub, "post");
      component.getLastPeriodOptions();
      expect(initialDataServiceStub.getConfigUrls).toHaveBeenCalled();
    });
  });

  describe('makeBatchCreateRequestCall', () => {
    it('makes expected calls', () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      const HttpClientStub = TestBed.inject(HttpClient);
      component.createOfferRequestForm = new UntypedFormGroup({period: new UntypedFormControl("WK 2021")});
      component.payloadQuery = {query: ""};
      component.action = {asyncActionKey: ""}
      spyOn(initialDataServiceStub, 'getConfigUrls');
      spyOn(HttpClientStub, "post");
      component.makeBatchCreateRequestCall();
      expect(initialDataServiceStub.getConfigUrls).toHaveBeenCalled();
    });
  });

  describe('createOfferRequest', () => {
    it('makes expected calls', () => {
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(
        ToastrService
      );
      component.modalRef = new BsModalRef();
      spyOn(component, 'makeBatchCreateRequestCall').and.returnValue(of({}))
      spyOn(toastrServiceStub, 'success').and.callThrough();
      component.createOfferRequest();
      expect(component.makeBatchCreateRequestCall).toHaveBeenCalled();
      expect(toastrServiceStub.success).toHaveBeenCalled();
    });
  });
});

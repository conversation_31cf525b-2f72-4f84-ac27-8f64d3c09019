import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';

import { HttpClient } from '@angular/common/http';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import * as moment from 'moment';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';


@Component({
    selector: 'create-offer-request',
    templateUrl: './create-offer-request.component.html',
    styleUrls: ['./create-offer-request.component.scss']
})
export class CreateOfferRequestComponent extends UnsubscribeAdapter implements OnInit {

    createOfferRequestForm: UntypedFormGroup;
    periodData: any
    @Input() modalRef: BsModalRef;
    @Input() payloadQuery;
    @Input() action;
    @Input() pcSelected;


    constructor(
        public _http: HttpClient,
        public apiConfigService: InitialDataService,
        public baseInputSearchService:BaseInputSearchService,
        public _toaster: ToastrService


    ) {
        super();
    }

    ngOnInit(): void {

        this.getLastPeriodOptions().subscribe((promoDetails: any) => {
           
            if (promoDetails?.length) {
                let periodWeeks = promoDetails.map((promoObj) => {
                    return {
                        label: promoObj.periodWeek,
                        field: promoObj.periodId,
                    }
                });
                periodWeeks.sort(function(a, b) {
                    return a.field - b.field;
                });
                this.periodData = periodWeeks.filter((v,i,a)=>a.findIndex(t=>(t.field === v.field))===i);
                this.periodData.sort(function(a, b) {
                    return b.field - a.field;
                });
            }
            
        });
        this.createFormControl();
    }

    createFormControl() {
        this.createOfferRequestForm = new UntypedFormGroup({
            period: new UntypedFormControl('', Validators.required),
        });
    }
    

    getLastPeriodOptions() {
        const searchInput = {
            startDate: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS-00:00'),
            numberOfWeeksToGenerate: 14
        };
        return this._http.post(this.apiConfigService.getConfigUrls(CONSTANTS.GENERATE_PROMO_WEEK_DETAILS), searchInput);
    }
    makeBatchCreateRequestCall(){
        const query = this.payloadQuery?.query, promoWeekPeriod = this.createOfferRequestForm?.get('period')?.value,
        asyncActionKey =  this.action?.asyncActionKey, 
        payLoad = {
            jobType:"OR",
            jobSubType:asyncActionKey,
            promoWeekPeriod,
            programCodeType:CONSTANTS.BPD,
            searchQuery:{query}

        }
        return this._http.post(this.apiConfigService.getConfigUrls(CONSTANTS.BATCH_CREATE_REQUEST_TEMPLATE), payLoad);
    }

    createOfferRequest() {
        this.makeBatchCreateRequestCall().subscribe({
            next: ()=>{
        this._toaster.success('Creating Offer Requests', "", {});
        this.modalRef?.hide();
    },
    error: (error) => {
            this.modalRef.hide();

        }


    })}

}
<div class="container-fluid">
  <spinner *ngIf="loading"></spinner>
  <form class="pod-form" [formGroup]="templateStatusForm">
    <div class="row mb-3">
      <div class="col">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label class="float-left font-weight-bold small" for="title">Status</label>
          </div>
          <div class="col col-12">
            <select class="custom-select form-control" id="formControl" name="formControl" formControlName="status"
              (change)="onChangeStatus()">
              <option *ngFor="let k of otStatuses | keyobject" [ngValue]="k">{{ otStatuses[k]}} </option>
            </select>
          </div>
        </div>
      </div>
    </div>
    <div class="row mb-3" *ngIf="showReasonOrCommentField">
      <div class="col">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label class="float-left font-weight-bold small" for="title">Reason</label>
          </div>
          <div class="col col-12">
            <select class="custom-select form-control" id="formControl" name="formControl" formControlName="reason">
              <option *ngFor="let reason of otStatusReasons" [ngValue]="reason">{{ reason }} </option>
            </select>
          </div>
        </div>
      </div>
    </div>
    <div class="row mb-3" *ngIf="showReasonOrCommentField">
      <div class="col">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label class="float-left font-weight-bold small" for="comment">Comments</label>
            <span class="float-right font-size-body">({{ 100 - (commentValue?commentValue.length:0) }})</span>
          </div>

          <div class="col col-12">
            <textarea type="text" class="form-control" id="comment" rows="4" name="comment" formControlName="comment"
              maxlength="100">
              </textarea>
          </div>
        </div>
      </div>
    </div>
    <div class="row mb-3" *ngIf="showSetUntlStatusField">
      <div class="col-12">
        <div class="form-group row">
          <div class="clearfix col-12">
            <label class="float-left font-weight-bold small" for="title">Set Status Until</label>
          </div>
          <div class="col-8 input-group">
            <input onkeydown="return false" type="text" 
              class="form-control form-control-lg optional input-background border-right-0" id="displayEndDate" name="endDate"
              placement="top" bsDatepicker autocomplete="off" formControlName="statusUntilDate"
              [minDate]="minStatusUntilDate" [bsConfig]="{
                    containerClass: colorTheme,
                    dateInputFormat: 'MM/DD/YYYY',
                    showWeekNumbers: false
                  }" #statusUntilDatePicker="bsDatepicker" />
            <div class="cursor-pointer input-group-append input-icon pt-4em px-2">
              <div (click)="statusUntilDatePicker.toggle()">
                <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
  <div class="row">
    <div class="col d-flex mt-3 mb-3 justify-content-end">
      <label class="anchor-link-blue cursor-pointer mr-4 mb-0 align-self-center" (click)="modalRef.hide()">
        <u>Cancel</u>
      </label>
      <button class="btn btn-primary font-weight-bolder submit-btn" [disabled]="!templateStatusForm.valid"
        (click)="onUpdateStatus()">
        Update
      </button>
    </div>
  </div>
</div>
import { KeyobjectPipe } from '@appPipes/keyobject.pipe';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BatchUpdateStatusComponent } from './batch-update-status.component';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormGroup, UntypedFormControl } from '@angular/forms';
import { of, throwError } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';
import { dateInOriginalFormat } from '@appUtilities/date.utility';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('BatchUpdateStatusComponent', () => {
  let component: BatchUpdateStatusComponent;
  let fixture: ComponentFixture<BatchUpdateStatusComponent>;
  let mockInitialDataService: jasmine.SpyObj<InitialDataService>;
  let mockBulkUpdateService: jasmine.SpyObj<BulkUpdateService>;
  let mockBsModalRef: jasmine.SpyObj<BsModalRef>;

  beforeEach(() => {
    mockInitialDataService = jasmine.createSpyObj('InitialDataService', ['getAppData']);
    mockBulkUpdateService = jasmine.createSpyObj('BulkUpdateService', ['doBatchTemplateUpdateStatus']);
    mockBsModalRef = jasmine.createSpyObj('BsModalRef', ['hide']);

    TestBed.configureTestingModule({
      declarations: [BatchUpdateStatusComponent, KeyobjectPipe],
      providers: [
        { provide: InitialDataService, useValue: mockInitialDataService },
        { provide: BulkUpdateService, useValue: mockBulkUpdateService },
        { provide: BsModalRef, useValue: mockBsModalRef }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });

    fixture = TestBed.createComponent(BatchUpdateStatusComponent);
    component = fixture.componentInstance;

    const mockAppData = {
      offerTemplateStatus: { NEW: 'new', REVIEW: 'review', NO_UPCS: 'no_upcs', PARKED: 'parked', REMOVED: 'removed' },
      offerTemplateStatusReason: ['Reason 1', 'Reason 2']
    };

    mockInitialDataService.getAppData.and.returnValue(mockAppData);

    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl('ACTIVE'),
      reason: new UntypedFormControl(null),
      comment: new UntypedFormControl('Test comment'),
      statusUntilDate: new UntypedFormControl(null)
    });

    component.action = { asyncActionKey: 'updateKey', jobType: 'BatchUpdate' };
    component.payloadQuery = { query: 'testQuery' };
    component.pcSelected = 'PC001';

    component.modalRef = mockBsModalRef;

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize variables and create form controls on ngOnInit', () => {
    component.ngOnInit();

    expect(component.otStatuses).toEqual({ PARKED: 'parked', REMOVED: 'removed' });
    expect(component.otStatusReasons).toEqual(['Reason 1', 'Reason 2']);
    expect(component.templateStatusForm instanceof UntypedFormGroup).toBe(true);
  });

  it('should call onChangeStatus and clear reason, statusUntilDate, and comment fields when status is ACTIVE', () => {
    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl(''),
      reason: new UntypedFormControl(''),
      comment: new UntypedFormControl(''),
      statusUntilDate: new UntypedFormControl('')
    });
    component.templateStatusForm.controls['status'].setValue(CONSTANTS.ACTIVE);

    const clearCtrlValueSpy = spyOn(component, 'clearCtrlValue');

    component.onChangeStatus();

    expect(clearCtrlValueSpy).toHaveBeenCalledWith(['reason', 'statusUntilDate', 'comment']);
  });

  it('should call onChangeStatus and clear only statusUntilDate field when status is REMOVED', () => {
    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl(''),
      reason: new UntypedFormControl(''),
      comment: new UntypedFormControl(''),
      statusUntilDate: new UntypedFormControl('')
    });
    component.templateStatusForm.controls['status'].setValue(CONSTANTS.REMOVED);

    const clearCtrlValueSpy = spyOn(component, 'clearCtrlValue');

    component.onChangeStatus();

    expect(clearCtrlValueSpy).toHaveBeenCalledWith(['statusUntilDate']);
  });

  it('should return true for showReasonOrCommentField when status is PARKED or REMOVED', () => {
    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl('')
    });

    component.templateStatusForm.controls['status'].setValue(CONSTANTS.PARKED);
    expect(component.showReasonOrCommentField).toBe(true);

    component.templateStatusForm.controls['status'].setValue(CONSTANTS.REMOVED);
    expect(component.showReasonOrCommentField).toBe(true);

    component.templateStatusForm.controls['status'].setValue(CONSTANTS.NEW);
    expect(component.showReasonOrCommentField).toBe(false);
  });

  it('should return true for showSetUntlStatusField when status is PARKED', () => {
    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl('')
    });

    component.templateStatusForm.controls['status'].setValue(CONSTANTS.PARKED);
    expect(component.showSetUntlStatusField).toBe(true);

    component.templateStatusForm.controls['status'].setValue(CONSTANTS.REMOVED);
    expect(component.showSetUntlStatusField).toBe(false);
  });
  it('should return the value of the comment form control', () => {
    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl(''),
      reason: new UntypedFormControl(''),
      comment: new UntypedFormControl('Some comment value'),
      statusUntilDate: new UntypedFormControl('')
    });

    component.templateStatusForm.controls['comment'].setValue('Some comment value');

    expect(component.commentValue).toBe('Some comment value');
  });

  it('should clear the specified form controls and update validity', () => {
    component.templateStatusForm = new UntypedFormGroup({
      status: new UntypedFormControl('Active'),
      reason: new UntypedFormControl('Reason 1'),
      comment: new UntypedFormControl('Some comment'),
      statusUntilDate: new UntypedFormControl('2025-02-17')
    });

    component.templateStatusForm.controls['status'].setValue('Active');
    component.templateStatusForm.controls['reason'].setValue('Reason 1');
    component.templateStatusForm.controls['comment'].setValue('Some comment');
    component.templateStatusForm.controls['statusUntilDate'].setValue('2025-02-17');

    const setValueSpy = spyOn(component.templateStatusForm.controls['status'], 'setValue').and.callThrough();
    const updateValiditySpy = spyOn(component.templateStatusForm.controls['status'], 'updateValueAndValidity').and.callThrough();

    const ctrlList = ['status', 'reason', 'comment'];
    component.clearCtrlValue(ctrlList);

    expect(setValueSpy).toHaveBeenCalledWith(null);
    expect(updateValiditySpy).toHaveBeenCalled();

    expect(component.templateStatusForm.controls['status'].value).toBeNull();
    expect(component.templateStatusForm.controls['reason'].value).toBeNull();
    expect(component.templateStatusForm.controls['comment'].value).toBeNull();

    expect(component.templateStatusForm.controls['status'].valid).toBeTrue();
    expect(component.templateStatusForm.controls['reason'].valid).toBeTrue();
    expect(component.templateStatusForm.controls['comment'].valid).toBeTrue();
  });

  describe('createPayload', () => {
    it('should create a valid payload object', () => {
      component.templateStatusForm.setValue({
        status: 'Active',
        reason: null,
        comment: 'Test Comment',
        statusUntilDate: '2025-02-18'
      });

      const formattedDate = dateInOriginalFormat({ date: '2025-02-18' });

      const expectedPayload = {
        searchQuery: component.payloadQuery,
        jobType: 'BatchUpdate',
        jobSubType: 'updateKey',
        programCodeType: 'PC001',
        updateStatus: {
          status: 'Active',
          reason: null,
          comment: 'Test Comment',
          statusUntilDate: formattedDate
        }
      };

      const result = component.createPayload();
      expect(result).toEqual(expectedPayload);
    });
    
    it('should format statusUntilDate if provided', () => {
      component.templateStatusForm = new UntypedFormGroup({
        status: new UntypedFormControl('Active'),
        reason: new UntypedFormControl(null),
        comment: new UntypedFormControl('Test Comment'),
        statusUntilDate: new UntypedFormControl('2025-02-18')
      });
      const payload = component.createPayload();
      const expectedFormattedDate = dateInOriginalFormat({ date: '2025-02-18' });
      expect(payload.updateStatus.statusUntilDate).toEqual(expectedFormattedDate);
    });

    it('should leave statusUntilDate as null if not provided', () => {
      component.templateStatusForm = new UntypedFormGroup({
        status: new UntypedFormControl('Active'),
        reason: new UntypedFormControl(null),
        comment: new UntypedFormControl('Test Comment'),
        statusUntilDate: new UntypedFormControl(null)
      });
      const payload = component.createPayload();
      expect(payload.updateStatus.statusUntilDate).toBeNull();
    });
  });

  describe('onUpdateStatus', () => {
    beforeEach(() => {
      component.modalRef = mockBsModalRef;
    });

    it('should not proceed if form is invalid', () => {
      spyOn(component, 'createPayload');
      component.templateStatusForm.setValue({
        status: null,
        reason: null,
        comment: null,
        statusUntilDate: null
      });

      const result = component.onUpdateStatus();

      expect(component.createPayload).not.toHaveBeenCalled();
      expect(result).toBeFalse();
    });

    it('should handle error scenario gracefully', () => {
      component.templateStatusForm.setValue({
        status: 'Active',
        reason: 'Test Reason',
        comment: 'Test Comment',
        statusUntilDate: '2025-02-18'
      });
      mockBulkUpdateService.doBatchTemplateUpdateStatus.and.returnValue(throwError(() => new Error('Test Error')));

      component.onUpdateStatus();

      expect(component.loading).toBeFalse();
    });

    it('should call doBatchTemplateUpdateStatus and handle success scenario when service returns truthy data', () => {
      component.templateStatusForm.setValue({
        status: 'Active',
        reason: 'Test Reason',
        comment: 'Test Comment',
        statusUntilDate: '2025-02-18'
      });
      component.action = { asyncActionKey: 'updateKey', jobType: 'BatchUpdate' };
      component.payloadQuery = { query: 'testQuery' };
      component.pcSelected = 'PC001';
      component.modalRef = mockBsModalRef;

      const expectedPayload = component.createPayload();

      mockBulkUpdateService.doBatchTemplateUpdateStatus.and.returnValue(of({ some: 'data' }));

      const emitSpy = spyOn(component.onUpdateSuccess, 'emit');

      mockBsModalRef.hide.calls.reset();

      component.onUpdateStatus();

      expect(mockBulkUpdateService.doBatchTemplateUpdateStatus).toHaveBeenCalledWith(expectedPayload);
      expect(component.loading).toBeFalse();
      expect(emitSpy).toHaveBeenCalledWith(true);
      expect(mockBsModalRef.hide).toHaveBeenCalled();
    });

    it('should not emit success if service returns falsy data', () => {
      component.templateStatusForm.setValue({
        status: 'Active',
        reason: 'Test Reason',
        comment: 'Test Comment',
        statusUntilDate: '2025-02-18'
      });
      component.action = { asyncActionKey: 'updateKey', jobType: 'BatchUpdate' };
      component.payloadQuery = { query: 'testQuery' };
      component.pcSelected = 'PC001';
      component.modalRef = mockBsModalRef;
      const expectedPayload = component.createPayload();

      mockBulkUpdateService.doBatchTemplateUpdateStatus.and.returnValue(of(null));

      const emitSpy = spyOn(component.onUpdateSuccess, 'emit');

      mockBsModalRef.hide.calls.reset();

      component.onUpdateStatus();

      expect(mockBulkUpdateService.doBatchTemplateUpdateStatus).toHaveBeenCalledWith(expectedPayload);
      expect(component.loading).toBeFalse();
      expect(emitSpy).not.toHaveBeenCalled();
      expect(mockBsModalRef.hide).not.toHaveBeenCalled();
    });
  });


});
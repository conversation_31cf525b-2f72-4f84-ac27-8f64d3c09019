import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { dateInOriginalFormat } from '@appUtilities/date.utility';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { BsModalRef } from 'ngx-bootstrap/modal';


@Component({
    selector: 'batch-update-status',
    templateUrl: './batch-update-status.component.html'
})
export class BatchUpdateStatusComponent extends UnsubscribeAdapter implements OnInit {
    loading = false;
    templateStatusForm: UntypedFormGroup;
    minStatusUntilDate: Date = new Date();
    colorTheme = "theme-dark-blue";
    @Output() onUpdateSuccess = new EventEmitter();
    
    @Input() modalRef: BsModalRef;
    @Input() payloadQuery;
    @Input() action;
    @Input() pcSelected;
    otStatuses = {};
    appData;
    otStatusReasons = [];

    constructor(
        private initialDataService: InitialDataService,
        private bulkUpdateService: BulkUpdateService
    ) {
        super();
    }

    ngOnInit(): void {
        this.appData = this.initialDataService.getAppData();
        this.initVariables();
        this.createFormControl();
    }
    initVariables() {
        const {NEW, REVIEW, NO_UPCS, ...requiredStatuses} = this.appData["offerTemplateStatus"];
        this.otStatuses = requiredStatuses;
        this.otStatusReasons = this.appData["offerTemplateStatusReason"];
    }
    createFormControl() {
        this.templateStatusForm = new UntypedFormGroup({
            status: new UntypedFormControl(null, Validators.required),
            reason: new UntypedFormControl(null),
            comment: new UntypedFormControl(null),
            statusUntilDate: new UntypedFormControl(null)
        });
    }
    onChangeStatus() {
        this.selectedOtStatus === CONSTANTS.ACTIVE && this.clearCtrlValue(["reason", "statusUntilDate", "comment"]);
        this.selectedOtStatus === CONSTANTS.REMOVED && this.clearCtrlValue(["statusUntilDate"]);
    }
    get selectedOtStatus() {
        return this.getFrmCtrl("status").value;
    }
    getFrmCtrl(ctrlName) {
        return this.templateStatusForm?.get(ctrlName) as UntypedFormControl;
    }
    get commentValue() {
        return this.getFrmCtrl("comment")?.value;
    }
    get showReasonOrCommentField() {
        return [CONSTANTS.PARKED, CONSTANTS.REMOVED].includes(this.selectedOtStatus);
    }
    get showSetUntlStatusField() {
        return [CONSTANTS.PARKED].includes(this.selectedOtStatus); 
    }
    clearCtrlValue(ctrlList) {
        if(ctrlList?.length) {
            ctrlList.forEach(ctrlName => {
                const ctrl = this.getFrmCtrl(ctrlName);
                ctrl?.setValue(null);
                ctrl?.updateValueAndValidity();
            });
        }
    }
    onUpdateStatus() {
        if(!this.templateStatusForm?.valid) return false; 
        const payload = this.createPayload();
        this.loading = true;
        this.bulkUpdateService.doBatchTemplateUpdateStatus(payload).subscribe({
            next: (data)=> {
            this.loading = false;
            if(data) {
                this.onUpdateSuccess.emit(true);
                this.modalRef.hide();
            }
        }, 
        error: (err) => {
            this.loading = false;
        }})
    }
    createPayload() {
        const { asyncActionKey, jobType } = this.action,
         tempForm = {...this.templateStatusForm.value}, {statusUntilDate} = tempForm;
         tempForm.statusUntilDate = statusUntilDate ? dateInOriginalFormat({date: statusUntilDate }) :statusUntilDate;
        const payload = {
            searchQuery: this.payloadQuery,
            jobType,
            jobSubType: asyncActionKey,
            programCodeType: this.pcSelected,
            updateStatus: tempForm
        }
        return payload;
    }
}
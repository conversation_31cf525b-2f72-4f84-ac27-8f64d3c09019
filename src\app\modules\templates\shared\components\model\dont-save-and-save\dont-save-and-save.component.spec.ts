import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DontSaveAndSaveComponent } from './dont-save-and-save.component';
import { By } from '@angular/platform-browser';

describe('DontSaveAndSaveComponent', () => {
    let component: DontSaveAndSaveComponent;
    let fixture: ComponentFixture<DontSaveAndSaveComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ DontSaveAndSaveComponent ]
        })
        .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DontSaveAndSaveComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call initSubscribe on ngOnInit', () => {
        spyOn(component, 'initSubscribe');
        component.ngOnInit();
        expect(component.initSubscribe).toHaveBeenCalled();
    });

    it('should emit saveEventEmitter when save is called', () => {
        spyOn(component.saveEventEmitter, 'emit');
        component.saveEventEmitter.emit();
        expect(component.saveEventEmitter.emit).toHaveBeenCalled();
    });

    it('should emit cancelEventEmitter when cancel is called', () => {
        spyOn(component.cancelEventEmitter, 'emit');
        component.cancelEventEmitter.emit();
        expect(component.cancelEventEmitter.emit).toHaveBeenCalled();
    });
});
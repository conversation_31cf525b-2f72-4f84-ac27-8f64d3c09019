import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-dont-save-and-save',
  templateUrl: './dont-save-and-save.component.html',
  styleUrls: ['./dont-save-and-save.component.scss']
})
export class DontSaveAndSaveComponent implements OnInit {
  @Input() forms;
  @Input() modelRef;
  @Output() saveEventEmitter = new EventEmitter();
  @Output() cancelEventEmitter = new EventEmitter();
  constructor() {
    // intentionally left empty
   }

  ngOnInit(): void {
    this.initSubscribe();
  }
  initSubscribe(){
  //  this.dontSaveAndSaveBehaviourSubject.subscribe(value=>{

  //  })
  }

}

<ng-container *ngIf="batchActions && batchActions.length">
    <batch-action-list [isPopupDisabled]="isPopupDisabled" [featureFlagCheck]="checkForFeatureFlag"
      [batchActions]="batchActions" (onClickBatchAction)="onClickActionElement($event)"></batch-action-list>
  </ng-container>

  <ng-template #modelCloseRef>
    <div class="row">
      <button type="button" class="close pull-right mobpopup-close" aria-label="Close" (click)="modalRef.hide()">
        <img src="assets/icons/close-icon-black.svg" class="mb-1 close-icon cursor-pointer" alt="close" />
      </button>
    </div>
  </ng-template>
  <ng-template #createOfferRequestTmpl>
    <div class="modal-header border-bottom-0 pb-0">
      <div class="container-fluid">
        <ng-container *ngTemplateOutlet="modelCloseRef"></ng-container>
        <div class="row">
          <api-errors class="col-12"></api-errors>
        </div>
        <div class="row m-1 mb-3">
          <h2>Create Offer Requests</h2>
        </div>
      </div>
    </div>
    <div class="modal-body pt-0 pr-5 pl-5">
      <div class="container-fluid">
        <div class="row">
          <div class="container-fluid">
            <create-offer-request [pcSelected]="pcSelected"  [action] ="action" [payloadQuery]="payloadQuery" [modalRef]="modalRef"></create-offer-request>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #updateStatusTmpl>
    <div class="modal-header border-bottom-0 pb-0">
      <div class="container-fluid">
        <ng-container *ngTemplateOutlet="modelCloseRef"></ng-container>
        <div class="row">
          <api-errors class="col-12"></api-errors>
        </div>
        <div class="row m-1 mb-3">
          <h2>Update Status</h2>
        </div>
      </div>
    </div>
    <div class="modal-body pt-0 pr-5 pl-5">
      <div class="container-fluid">
        <div class="row">
          <div class="container-fluid">
            <batch-update-status [pcSelected]="pcSelected"  [action] ="action" [payloadQuery]="payloadQuery" [modalRef]="modalRef" (onUpdateSuccess) = "$event && updateRecords(null)"></batch-update-status>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  
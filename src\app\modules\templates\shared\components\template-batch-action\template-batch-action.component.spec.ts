import { ComponentFixture, TestBed, inject } from '@angular/core/testing';
import { TemplateBatchActionComponent } from './template-batch-action.component';
import { NO_ERRORS_SCHEMA, InjectionToken, Injector } from '@angular/core';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '@appServices/common/auth.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization/service/permissions.service';
import { SearchOfferRequestService } from '@appModules/request/services/search-offer-request.service';
import { ToastrService } from 'ngx-toastr';
import { GeneralOfferTypeService } from '@appServices/details/general-offer-type.service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { of } from 'rxjs';
import { AppInjector } from '@appServices/common/app.injector.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';

// Mock for PopoverDirective
const mockPopoverRef = { hide: jasmine.createSpy('hide') };

// Mock for MSAL_GUARD_CONFIG
const MSAL_GUARD_CONFIG = new InjectionToken('MSAL_GUARD_CONFIG');

describe('TemplateBatchActionComponent', () => {
  let fixture: ComponentFixture<TemplateBatchActionComponent>;
  let comp: TemplateBatchActionComponent;
  let facetItemService: any;
  let bulkUpdateService: any;
  let bsModalService: any;
  let toastrService: any;
  let searchOfferService: any;
  let searchOfferRequestService: any;
  let queryGenerator: any;
  let commonSearchService: any;
  let featureFlagService: any;
  let baseInputSearchService: any;
  let mockInjector: any;
  let mockToastConfig = { positionClass: 'toast-top-right', closeButton: true };

  beforeEach(async () => {
    // Create instances of mock services
    facetItemService = {
      templateProgramCodeSelected: 'PCODE',
      getAppDataName: jasmine.createSpy('getAppDataName').and.returnValue('TestApp'),
      getDeliveryChannels: jasmine.createSpy('getDeliveryChannels').and.returnValue([]),
      getFacetItems: jasmine.createSpy('getFacetItems').and.returnValue({}),
      getdivsionStateFacetItems: jasmine.createSpy('getdivsionStateFacetItems').and.returnValue({}),
      getOfferFilter: jasmine.createSpy('getOfferFilter').and.returnValue('facetFilter'),
      setOfferFilter: jasmine.createSpy('setOfferFilter')
    };

    bulkUpdateService = {
      requestIdArr: [1, 2, 3],
      isAllBatchSelected: of('BATCH'),
      isSelectionReset: { next: jasmine.createSpy('next') },
      templatePreBatch: jasmine.createSpy('templatePreBatch').and.returnValue(of({
        offersForUpdate: { invalid: [] },
        requestId: 'mock-request-id'
      })),
      preCheckBatch: jasmine.createSpy('preCheckBatch').and.returnValue(of({
        offersForUpdate: { invalid: [] },
        requestId: 'mock-request-id'
      })),
      bulkActiontemplate: jasmine.createSpy('bulkActiontemplate').and.returnValue(of({})),
      checkIfActionEnabledForUniversalJob: jasmine.createSpy('checkIfActionEnabledForUniversalJob').and.returnValue(false)
    };

    bsModalService = {
      show: jasmine.createSpy('show').and.returnValue({})
    };

    toastrService = jasmine.createSpyObj('ToastrService', ['success', 'error', 'info', 'warning']);

    searchOfferService = {
      searchOffers: jasmine.createSpy('searchOffers').and.returnValue(of({}))
    };

    searchOfferRequestService = {
      searchOfferRequests: jasmine.createSpy('searchOfferRequests').and.returnValue(of({}))
    };

    queryGenerator = {
      getQuery: jasmine.createSpy('getQuery').and.returnValue('mock-query'),
      getQueryWithFilter: jasmine.createSpy('getQueryWithFilter').and.returnValue([]),
      getQueryFilter: jasmine.createSpy('getQueryFilter').and.returnValue(''),
      removeParameters: jasmine.createSpy('removeParameters')
    };

    commonSearchService = {
      isShowExpiredInQuery: false,
      setQueryOptionsForBpd: jasmine.createSpy('setQueryOptionsForBpd')
    };

    featureFlagService = {
      isFeatureFlagEnabled: jasmine.createSpy('isFeatureFlagEnabled').and.returnValue(true),
      isuppEnabled: false,
      isOfferRequestArchivalEnabled: false
    };

    baseInputSearchService = {
      removeParametersForTemplates: jasmine.createSpy('removeParametersForTemplates'),
      queryForInputAndFilter: 'mock-query',
      getFormQuery: jasmine.createSpy('getFormQuery').and.returnValue(''),
      postDataForInputSearch: jasmine.createSpy('postDataForInputSearch')
    };

    // Create a mock injector that will be used by the AppInjector
    mockInjector = {
      get: jasmine.createSpy('get').and.callFake((token) => {
        switch (token) {
          case SearchOfferService:
            return searchOfferService;
          case FacetItemService:
            return facetItemService;
          case SearchOfferRequestService:
            return searchOfferRequestService;
          case BulkUpdateService:
            return bulkUpdateService;
          case FeatureFlagsService:
            return featureFlagService;
          case BaseInputSearchService:
            return baseInputSearchService;
          case BsModalService:
            return bsModalService;
          case QueryGenerator:
            return queryGenerator;
          case ToastrService:
            return toastrService;
          case CommonSearchService:
            return commonSearchService;
          default:
            return null;
        }
      })
    };

    // Set the mock injector in AppInjector
    AppInjector.setInjector(mockInjector);

    await TestBed.configureTestingModule({
      declarations: [TemplateBatchActionComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: FacetItemService, useValue: facetItemService },
        { provide: BulkUpdateService, useValue: bulkUpdateService },
        { provide: BsModalService, useValue: bsModalService },
        { provide: SearchOfferRequestService, useValue: searchOfferRequestService },
        { provide: HttpClient, useValue: {
          get: jasmine.createSpy('get').and.returnValue(of({})),
          post: jasmine.createSpy('post').and.returnValue(of({}))
        }},
        { provide: AuthService, useValue: {
          getTokenString: jasmine.createSpy('getTokenString').and.returnValue('mock-token')
        }},
        { provide: MSAL_GUARD_CONFIG, useValue: {} },
        { provide: PermissionsService, useValue: {
          hasPermission: jasmine.createSpy('hasPermission').and.returnValue(true)
        } },
        { provide: ToastrService, useValue: toastrService },
        { provide: 'ToastConfig', useValue: mockToastConfig },
        { provide: GeneralOfferTypeService, useValue: {
          getOfferTypeList: jasmine.createSpy('getOfferTypeList').and.returnValue(of([]))
        }},
        { provide: SearchOfferService, useValue: searchOfferService },
        { provide: QueryGenerator, useValue: queryGenerator },
        { provide: CommonSearchService, useValue: commonSearchService },
        { provide: FeatureFlagsService, useValue: featureFlagService },
        { provide: BaseInputSearchService, useValue: baseInputSearchService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;
    comp.popupRef = mockPopoverRef as any;
    comp.batchType = 'BATCH';
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(comp).toBeTruthy();
  });

  it('should call getBatchActionsFromRules and set pcSelected on ngOnInit', () => {
    // Set the value that the component will use
    facetItemService.templateProgramCodeSelected = 'BPD';

    // Create a new instance to test ngOnInit
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;

    // Spy on the method before calling ngOnInit
    spyOn(comp, 'getBatchActionsFromRules');

    // Call ngOnInit
    comp.ngOnInit();

    // Verify the expected behavior
    expect(comp.pcSelected).toBe('BPD');
    expect(comp.getBatchActionsFromRules).toHaveBeenCalled();
  });

  it('should call getBatchActionsFromRules with correct parameters', () => {
    // Set the value that the component will use
    facetItemService.templateProgramCodeSelected = 'BPD';

    // Create a new instance
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;

    // Call the method directly to test its implementation
    comp.ngOnInit();

    // Verify the batch actions are set correctly
    expect(comp.pcSelected).toBe('BPD');

    // Since we're using a mock injector, batchActions might not be set properly
    // We're just testing that the method was called with the right parameters
    // which is covered by the previous test
  });

  it('should handle empty or undefined program code in ngOnInit', () => {
    // Set the value to undefined
    facetItemService.templateProgramCodeSelected = undefined;

    // Create a new instance
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;

    // Spy on the method
    spyOn(comp, 'getBatchActionsFromRules');

    // Call ngOnInit
    comp.ngOnInit();

    // Verify the expected behavior
    expect(comp.pcSelected).toBeUndefined();
    expect(comp.getBatchActionsFromRules).toHaveBeenCalled();
  });

  it('should return correct payloadQuery', () => {
    // Set up component properties
    comp.batchType = 'BATCH';
    comp.pcSelected = 'PCODE';

    // Spy on the method
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Get the result
    const result = comp.payloadQuery;

    // Verify the expected behavior
    expect(comp.getQueryForPreCheck).toHaveBeenCalledWith({
      payload: bulkUpdateService.requestIdArr,
      batchType: 'BATCH',
      key: 'requestId',
      progrmCd: 'PCODE',
      progrmCdKey: 'programCode',
      facetPage: jasmine.any(String)
    });
    expect(result).toBe('QUERY');
  });

  it('should handle empty requestIdArr in payloadQuery', () => {
    // Set up component properties
    comp.batchType = 'BATCH';
    comp.pcSelected = 'PCODE';

    // Set empty requestIdArr
    bulkUpdateService.requestIdArr = [];

    // Spy on the method
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('EMPTY_QUERY');

    // Get the result
    const result = comp.payloadQuery;

    // Verify the expected behavior
    expect(comp.getQueryForPreCheck).toHaveBeenCalledWith({
      payload: [],
      batchType: 'BATCH',
      key: 'requestId',
      progrmCd: 'PCODE',
      progrmCdKey: 'programCode',
      facetPage: jasmine.any(String)
    });
    expect(result).toBe('EMPTY_QUERY');

    // Restore requestIdArr for other tests
    bulkUpdateService.requestIdArr = [1, 2, 3];
  });

  it('should handle undefined batchType in payloadQuery', () => {
    // Set up component properties
    comp.batchType = undefined;
    comp.pcSelected = 'PCODE';

    // Spy on the method
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('UNDEFINED_BATCH_QUERY');

    // Get the result
    const result = comp.payloadQuery;

    // Verify the expected behavior
    expect(comp.getQueryForPreCheck).toHaveBeenCalledWith({
      payload: bulkUpdateService.requestIdArr,
      batchType: undefined,
      key: 'requestId',
      progrmCd: 'PCODE',
      progrmCdKey: 'programCode',
      facetPage: jasmine.any(String)
    });
    expect(result).toBe('UNDEFINED_BATCH_QUERY');
  });

  it('should call popupRef.hide, set action, and call onClickBaseAction on onClickActionElement', () => {
    // Spy on the method
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Set up component properties
    comp.popupRef = mockPopoverRef as any;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';

    // Define the action
    const action = { key: 'copy' };

    // Call the method
    comp.onClickActionElement(action);

    // Verify the expected behavior
    expect(mockPopoverRef.hide).toHaveBeenCalled();
    expect(comp.action).toBe(action);
    expect(comp.onClickBaseAction).toHaveBeenCalledWith(action, 'QUERY', jasmine.any(String), 'PCODE');
  });

  it('should not throw if popupRef is undefined in onClickActionElement', () => {
    // Spy on the method
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Set up component properties
    comp.popupRef = undefined;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';

    // Define the action
    const action = { key: 'copy' };

    // Verify the method doesn't throw
    expect(() => comp.onClickActionElement(action)).not.toThrow();

    // Verify the expected behavior
    expect(comp.action).toBe(action);
    expect(comp.onClickBaseAction).toHaveBeenCalled();
  });

  it('should handle null action in onClickActionElement', () => {
    // Spy on the method
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Set up component properties
    comp.popupRef = mockPopoverRef as any;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';

    // Reset the hide spy
    mockPopoverRef.hide.calls.reset();

    // Call with null action
    comp.onClickActionElement(null);

    // Verify the expected behavior
    expect(mockPopoverRef.hide).toHaveBeenCalled();
    expect(comp.action).toBe(null);
    expect(comp.onClickBaseAction).toHaveBeenCalledWith(null, 'QUERY', jasmine.any(String), 'PCODE');
  });

  it('should handle undefined action in onClickActionElement', () => {
    // Spy on the method
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Set up component properties
    comp.popupRef = mockPopoverRef as any;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';

    // Reset the hide spy
    mockPopoverRef.hide.calls.reset();

    // Call with undefined action
    comp.onClickActionElement(undefined);

    // Verify the expected behavior
    expect(mockPopoverRef.hide).toHaveBeenCalled();
    expect(comp.action).toBe(undefined);
    expect(comp.onClickBaseAction).toHaveBeenCalledWith(undefined, 'QUERY', jasmine.any(String), 'PCODE');
  });

  it('should handle undefined pcSelected in onClickActionElement', () => {
    // Spy on the method
    spyOn(comp, 'onClickBaseAction');
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Set up component properties
    comp.popupRef = mockPopoverRef as any;
    comp.pcSelected = undefined;
    comp.batchType = 'BATCH';

    // Reset the hide spy
    mockPopoverRef.hide.calls.reset();

    // Define the action
    const action = { key: 'copy' };

    // Call the method
    comp.onClickActionElement(action);

    // Verify the expected behavior
    expect(mockPopoverRef.hide).toHaveBeenCalled();
    expect(comp.action).toBe(action);
    expect(comp.onClickBaseAction).toHaveBeenCalledWith(action, 'QUERY', jasmine.any(String), undefined);
  });

  it('should handle different batch types correctly', () => {
    // Set up component properties
    comp.batchType = 'selectAcrossAllPages';
    comp.pcSelected = 'PCODE';

    // Spy on the method
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Get the result
    const result = comp.payloadQuery;

    // Verify the expected behavior
    expect(comp.getQueryForPreCheck).toHaveBeenCalledWith({
      payload: bulkUpdateService.requestIdArr,
      batchType: 'selectAcrossAllPages',
      key: 'requestId',
      progrmCd: 'PCODE',
      progrmCdKey: 'programCode',
      facetPage: jasmine.any(String)
    });
    expect(result).toBe('QUERY');
  });

  it('should handle different program codes correctly', () => {
    // Set up component properties
    comp.batchType = 'BATCH';
    comp.pcSelected = 'BPD';

    // Spy on the method
    spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

    // Get the result
    const result = comp.payloadQuery;

    // Verify the expected behavior
    expect(comp.getQueryForPreCheck).toHaveBeenCalledWith({
      payload: bulkUpdateService.requestIdArr,
      batchType: 'BATCH',
      key: 'requestId',
      progrmCd: 'BPD',
      progrmCdKey: 'programCode',
      facetPage: jasmine.any(String)
    });
    expect(result).toBe('QUERY');
  });

  it('should handle different action types in onClickActionElement', () => {
    // Define different action types
    const actions = [
      { key: 'copy' },
      { key: 'updateStatus' },
      { key: 'createOfferRequest' }
    ];

    // Test each action type separately to avoid spy conflicts
    actions.forEach(action => {
      // Create a new component instance for each test
      fixture = TestBed.createComponent(TemplateBatchActionComponent);
      comp = fixture.componentInstance;

      // Set up component properties
      comp.popupRef = mockPopoverRef as any;
      comp.pcSelected = 'PCODE';
      comp.batchType = 'BATCH';

      // Reset the hide spy
      mockPopoverRef.hide.calls.reset();

      // Create fresh spies for each test
      spyOn(comp, 'onClickBaseAction');
      spyOn(comp, 'getQueryForPreCheck').and.returnValue('QUERY');

      // Call the method
      comp.onClickActionElement(action);

      // Verify the expected behavior
      expect(mockPopoverRef.hide).toHaveBeenCalled();
      expect(comp.action).toBe(action);
      expect(comp.onClickBaseAction).toHaveBeenCalledWith(action, 'QUERY', jasmine.any(String), 'PCODE');
    });
  });

  // This test is a placeholder for handling window.location.reload
  // The component doesn't currently use window.location.reload, but if it did,
  // we would need to mock it to prevent the test from reloading the page
  it('should handle any browser functions like window.location.reload if used', () => {
    // Create a new component instance
    fixture = TestBed.createComponent(TemplateBatchActionComponent);
    comp = fixture.componentInstance;

    // Set up component properties
    comp.popupRef = mockPopoverRef as any;
    comp.pcSelected = 'PCODE';
    comp.batchType = 'BATCH';

    // Verify the component was created successfully
    expect(comp).toBeTruthy();

    // If the component used window.location.reload, we would mock it like this:
    // const reloadSpy = spyOn(window.location, 'reload');
    // ... trigger the reload ...
    // expect(reloadSpy).toHaveBeenCalled();
  });
});

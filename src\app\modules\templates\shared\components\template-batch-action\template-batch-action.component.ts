import { Component, Input, TemplateRef, ViewChild } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { TEMPLATE_BATCH_RULES } from '@appModules/templates/core/offer-template/management/shared/rules/template-batch-rules';
import { BaseBatchActionComponent } from '@appShared/components/management/batch-actions/base-batch-action/base-batch-action.component';
import { PopoverDirective } from 'ngx-bootstrap/popover';
@Component({
  selector: "template-batch-action",
  templateUrl: './template-batch-action.component.html',
  styleUrls: ['./template-batch-action.component.scss']
})
export class TemplateBatchActionComponent extends BaseBatchActionComponent {
  @Input() isPopupDisabled;
  @Input() popupRef: PopoverDirective;
  @Input() batchType;

  @ViewChild("createOfferRequestTmpl")
  public createOfferRequestTmpl: TemplateRef<any>;

  @ViewChild("updateStatusTmpl")
  public updateStatusTmpl: TemplateRef<any>;

  pcSelected;

  constructor() {
    super();
  }
  ngOnInit(): void {
    this.pcSelected = this.facetItemService.templateProgramCodeSelected;
    this.getBatchActionsFromRules(this.pcSelected, TEMPLATE_BATCH_RULES);
  }
  get payloadQuery() {
    return this.getQueryForPreCheck(
        {
            payload: this.bulkUpdateService.requestIdArr,
            batchType: this.batchType, 
            key: "requestId",
            progrmCd: this.pcSelected,
            progrmCdKey: "programCode",
            facetPage: CONSTANTS.TEMPLATE
        }
    );
}

  onClickActionElement(action) {
    
    this.popupRef?.hide();
    this.action = action;
    this.onClickBaseAction(action,this.payloadQuery,'template',this.pcSelected)
    
  }
}
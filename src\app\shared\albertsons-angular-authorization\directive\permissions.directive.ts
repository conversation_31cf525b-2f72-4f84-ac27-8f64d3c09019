import {
    ChangeDetectorRef,
    Directive,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    TemplateRef,
    ViewContainerRef
} from '@angular/core';

import { merge, Subscription } from 'rxjs';
import { skip, take } from 'rxjs/operators';

import { PermissionsPredefinedStrategies } from '../enums/predefined-strategies.enum';
import { PermissionsConfigurationService, StrategyFunction } from '../service/configuration.service';
import { PermissionsService } from '../service/permissions.service';
import { RolesService } from '../service/roles.service';
import { isBoolean, isFunction, isString, notEmptyValue } from '../utils/utils';

@Directive({
    selector: '[permissionsOnly],[permissionsExcept]'
})
export class PermissionsDirective implements OnInit, OnDestroy, OnChanges  {

    @Input() permissionsOnly: string | string[];
    @Input() permissionsOnlyThen: TemplateRef<any>;
    @Input() permissionsOnlyElse: TemplateRef<any>;

    @Input() permissionsExcept: string | string[];
    @Input() permissionsExceptElse: TemplateRef<any>;
    @Input() permissionsExceptThen: TemplateRef<any>;

    @Input() permissionsThen: TemplateRef<any>;
    @Input() permissionsElse: TemplateRef<any>;

    @Input() permissionsOnlyAuthorisedStrategy: string | StrategyFunction;
    @Input() permissionsOnlyUnauthorisedStrategy: string | StrategyFunction;

    @Input() permissionsExceptUnauthorisedStrategy: string | StrategyFunction;
    @Input() permissionsExceptAuthorisedStrategy: string | StrategyFunction;

    @Input() permissionsUnauthorisedStrategy: string | StrategyFunction;
    @Input() permissionsAuthorisedStrategy: string | StrategyFunction;

    @Output() permissionsAuthorized = new EventEmitter();
    @Output() permissionsUnauthorized = new EventEmitter();

    private initPermissionSubscription: Subscription;
    // skip first run cause merge will fire twice
    private firstMergeUnusedRun = 1;
    private currentAuthorizedState: boolean;

    constructor(
        private permissionsService: PermissionsService,
        private configurationService: PermissionsConfigurationService,
        private rolesService: RolesService,
        private viewContainer: ViewContainerRef,
        private changeDetector: ChangeDetectorRef,
        private templateRef: TemplateRef<any>
    ) {
    }

    ngOnInit(): void {
        this.viewContainer.clear();
        this.initPermissionSubscription = this.validateExceptOnlyPermissions();
    }


    ngOnChanges(changes: SimpleChanges): void {
        const onlyChanges = changes.permissionsOnly;
        const exceptChanges = changes.permissionsExcept;
        if (onlyChanges || exceptChanges) {
            // Due to bug when you pass empty array
            if (onlyChanges && onlyChanges.firstChange) {
                return;
            }
            if (exceptChanges && exceptChanges.firstChange) {
                return;
            }

            merge(this.permissionsService.permissions$, this.rolesService.roles$)
                .pipe(skip(this.firstMergeUnusedRun), take(1))
                .subscribe(() => {
                    if (notEmptyValue(this.permissionsExcept)) {
                        this.validateExceptAndOnlyPermissions();
                        return;
                    }

                    if (notEmptyValue(this.permissionsOnly)) {
                        this.validateOnlyPermissions();
                        return;
                    }

                    this.handleAuthorisedPermission(this.getAuthorisedTemplates());
                });
        }
    }

    ngOnDestroy(): void {
        if (this.initPermissionSubscription) {
            this.initPermissionSubscription.unsubscribe();
        }
    }

    private validateExceptOnlyPermissions(): Subscription {
        return merge(this.permissionsService.permissions$, this.rolesService.roles$)
            .pipe(skip(this.firstMergeUnusedRun))
            .subscribe(() => {
                if (notEmptyValue(this.permissionsExcept)) {
                    this.validateExceptAndOnlyPermissions();
                    return;
                }

                if (notEmptyValue(this.permissionsOnly)) {
                    this.validateOnlyPermissions();
                    return;
                }
                this.handleAuthorisedPermission(this.getAuthorisedTemplates());
            });
    }

    private validateExceptAndOnlyPermissions(): void {
        Promise
            .all([
              this.permissionsService.hasPermission(this.permissionsExcept),
              this.rolesService.hasOnlyRoles(this.permissionsExcept)
            ])
            .then(([hasPermission, hasRole]) => {
                if (hasPermission || hasRole) {
                    this.handleUnauthorisedPermission(this.permissionsExceptElse || this.permissionsElse);
                    return;
                }

                if (!!this.permissionsOnly) {
                    throw false;
                }

                this.handleAuthorisedPermission(this.permissionsExceptThen || this.permissionsThen || this.templateRef);
            })
            .catch(() => {
                if (!!this.permissionsOnly) {
                    this.validateOnlyPermissions();
                } else {
                    this.handleAuthorisedPermission(this.permissionsExceptThen || this.permissionsThen || this.templateRef);
                }
        });
    }

    private validateOnlyPermissions(): void {
        Promise
            .all([this.permissionsService.hasPermission(this.permissionsOnly), this.rolesService.hasOnlyRoles(this.permissionsOnly)])
            .then(([hasPermissions, hasRoles]) => {
                if (hasPermissions || hasRoles) {
                    this.handleAuthorisedPermission(this.permissionsOnlyThen || this.permissionsThen || this.templateRef);
                } else {
                    this.handleUnauthorisedPermission(this.permissionsOnlyElse || this.permissionsElse);
                }
            })
          .catch(() => {
                this.handleUnauthorisedPermission(this.permissionsOnlyElse || this.permissionsElse);
        });
    }

    private handleUnauthorisedPermission(template: TemplateRef<any>): void {
        if (isBoolean(this.currentAuthorizedState) && !this.currentAuthorizedState) {
            return;
        }

        this.currentAuthorizedState = false;
        this.permissionsUnauthorized.emit();

        if (this.getUnAuthorizedStrategyInput()) {
            this.applyStrategyAccordingToStrategyType(this.getUnAuthorizedStrategyInput());
            return;
        }

        if (this.configurationService.onUnAuthorisedDefaultStrategy && !this.elseBlockDefined()) {
            this.applyStrategy(this.configurationService.onUnAuthorisedDefaultStrategy);
        } else {
            this.showTemplateBlockInView(template);
        }

    }

    private handleAuthorisedPermission(template: TemplateRef<any>): void {
        if (isBoolean(this.currentAuthorizedState) && this.currentAuthorizedState) {
            return;
        }

        this.currentAuthorizedState = true;
        this.permissionsAuthorized.emit();

        if (this.getAuthorizedStrategyInput()) {
            this.applyStrategyAccordingToStrategyType(this.getAuthorizedStrategyInput());
            return;
        }

        if (this.configurationService.onAuthorisedDefaultStrategy && !this.thenBlockDefined()) {
            this.applyStrategy(this.configurationService.onAuthorisedDefaultStrategy);
        } else {
            this.showTemplateBlockInView(template);
        }
    }

    private applyStrategyAccordingToStrategyType(strategy: string | StrategyFunction): void {
        if (isString(strategy)) {
            this.applyStrategy(strategy);
            return;
        }

        if (isFunction(strategy)) {
            this.showTemplateBlockInView(this.templateRef);
            (strategy as StrategyFunction)(this.templateRef);
            return;
        }
    }

    private showTemplateBlockInView(template: TemplateRef<any>): void {
        this.viewContainer.clear();
        if (!template) {
            return;
        }

        this.viewContainer.createEmbeddedView(template);
        this.changeDetector.markForCheck();
    }

    private getAuthorisedTemplates(): TemplateRef<any> {
        return this.permissionsOnlyThen
            || this.permissionsExceptThen
            || this.permissionsThen
            || this.templateRef;
    }

    private elseBlockDefined(): boolean {
        return !!this.permissionsExceptElse || !!this.permissionsElse;
    }

    private thenBlockDefined() {
        return !!this.permissionsExceptThen || !!this.permissionsThen;
    }

    private getAuthorizedStrategyInput() {
        return this.permissionsOnlyAuthorisedStrategy ||
            this.permissionsExceptAuthorisedStrategy ||
            this.permissionsAuthorisedStrategy;
    }

    private getUnAuthorizedStrategyInput() {
        return this.permissionsOnlyUnauthorisedStrategy ||
            this.permissionsExceptUnauthorisedStrategy ||
            this.permissionsUnauthorisedStrategy;
    }

    private applyStrategy(name: string) {
        if (name === PermissionsPredefinedStrategies.SHOW) {
            this.showTemplateBlockInView(this.templateRef);
            return;
        }

        if (name === PermissionsPredefinedStrategies.REMOVE) {
            this.viewContainer.clear();
            return;
        }
        const strategy = this.configurationService.getStrategy(name);
        this.showTemplateBlockInView(this.templateRef);
        strategy(this.templateRef);
    }
}

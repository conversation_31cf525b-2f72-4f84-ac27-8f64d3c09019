import { Directive, EventEmitter, Input, OnInit, Output, TemplateRef, ViewContainerRef } from '@angular/core';
import { StrategyFunction } from '../service/configuration.service';

@Directive({
    selector: '[permissionsOnly],[permissionsExcept]'
})
export class PermissionsRestrictStubDirective implements OnInit {

    @Input() permissionsOnly: string | string[];
    @Input() permissionsOnlyThen: TemplateRef<any>;
    @Input() permissionsOnlyElse: TemplateRef<any>;

    @Input() permissionsExcept: string | string[];
    @Input() permissionsExceptElse: TemplateRef<any>;
    @Input() permissionsExceptThen: TemplateRef<any>;

    @Input() permissionsThen: TemplateRef<any>;
    @Input() permissionsElse: TemplateRef<any>;

    @Input() permissionsOnlyAuthorisedStrategy: string | StrategyFunction;
    @Input() permissionsOnlyUnauthorisedStrategy: string | StrategyFunction;

    @Input() permissionsExceptUnauthorisedStrategy: string | StrategyFunction;
    @Input() permissionsExceptAuthorisedStrategy: string | StrategyFunction;

    @Input() permissionsUnauthorisedStrategy: string | StrategyFunction;
    @Input() permissionsAuthorisedStrategy: string | StrategyFunction;

    @Output() permissionsAuthorized = new EventEmitter();
    @Output() permissionsUnauthorized = new EventEmitter();


    constructor(private viewContainer: ViewContainerRef) {}


    ngOnInit(): void {
        this.viewContainer.clear();
        if (this.getUnAuthorizedTemplate()) {
            this.viewContainer.createEmbeddedView(this.getUnAuthorizedTemplate());
        }
        this.permissionsUnauthorized.emit();
    }


    private getUnAuthorizedTemplate() {
        return this.permissionsOnlyElse ||
            this.permissionsExceptElse ||
            this.permissionsElse;
    }

}

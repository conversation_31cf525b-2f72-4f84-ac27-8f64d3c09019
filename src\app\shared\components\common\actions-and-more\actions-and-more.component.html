<div class="col p-0" [ngClass]="actionsDropDownCssBasedOnPermissions">
  <label class="text-label m-0">
    <div class="show" [hidden]="filteredActionListByUserPermissions && filteredActionListByUserPermissions.length === 0">
      <button
        [class]="type === 'More' ? 'btn  more-button button-size' : 'btn btn-secondary btn-sm dropdown-toggle actions-button'"
        type="button"
        data-toggle="dropdown"
        aria-haspopup="true"
        aria-expanded="false"
      >
        <label [id]="type" [class]="type === 'More' ? ' text-label text-label-style action-lbl' : 'text-label text-label-actions action-lbl'"
          >{{ type }}
        </label>
      </button>

      <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
        <a
          test
          class="dropdown-item"
          *ngFor="let item of filteredActionListByUserPermissions"
          (click)="clickAction(item)"
          [id]="item"
          [class]="item === 'Save' && !isDisplaySaveCta && module === 'offer' ? 'isDisableSave' : ''"
        >
          <label class="mb-0 text-label">{{ item }}</label></a
        >
      </div>
    </div>
  </label>
</div>

<ng-template #deleteConfirm>
  <div class="modal-header">
    <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="warning-text">Are you sure you want to delete?</div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-default dont-save-btn" (click)="modalRef.hide()">Cancel</button>
    <button type="button" class="btn btn-primary save-btn" (click)="deleteRequestapi()">Ok</button>
  </div>
</ng-template>
<ng-template #cancelRequest>
  <div class="modal-header">
    <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="warning-text-cancel">Are you sure you want to cancel?</div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-default dont-save-btn modal-buttons-cancel" (click)="modalRef.hide()">
      <span class="button-text-cancel">No</span>
    </button>
    <button
      type="button"
      class="btn btn-primary save-btn modal-buttons-yes"
      (click)="offerProgramCode === CONSTANTS.SC ? this.cancelRequestapi() : cancelRequestapiGrSpdBpd()"
    >
      <span class="button-text">Yes</span>
    </button>
  </div>
</ng-template>
<ng-template #pluDeleteConfirm>
  <app-delete-plu [pluCodeData]="payload" [modalRef]="modalRef" [page]="'pluManagement'"></app-delete-plu>
</ng-template>
<ng-template #cancelRequestConfirm>
  <div class="modal-header">
    <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="warning-text-remove">
      How should digital offers be removed<br />
      from just for U?
    </div>
    <div class="warning-text-remove">
      <div class="radio-remove">
        <div class="radio-spacing" *ngIf="payload?.info?.digitalEditStatus === null || payload?.info?.digitalUiStatus !== 'R'">
          <input type="radio" value="removeUnclipped" name="negative" (change)="onSelectionRemoveOffer($event)" />
          <span class="radio-txt-label">Remove for Unclipped </span>

          <tooltip-container [title]="infoTextRemoveUnClipped" class="ml-2 mt-2"></tooltip-container>
        </div>
        <div>
          <input type="radio" value="removeAll" name="negative" (change)="onSelectionRemoveOffer($event)" /><span class="radio-txt-label">
            Remove for All</span
          >

          <tooltip-container [title]="infoTextRemoveAll" class="ml-2 mt-2"></tooltip-container>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-primary save-btn" (click)="this.removeDescionsOfferBuilder()">Remove</button>
  </div>
</ng-template>
<ng-template #cancelRequestConfirmGrSpdBpd>
  <form [formGroup]="grSpdBpdCancelRequestForm">
    <div class="modal-header pb-0">
      <h3 class="modal-title pull-left px-0">Cancel Offer Request</h3>
      <button type="button" class="close" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <div class="mb-2">
        <label for="changeReason" class="d-block font-weight-bold">Change Reason <span class="text-danger">*</span></label>
        <select
          id="changeReason"
          class="custom-select form-control"
          formControlName="changeReason"
          [ngClass]="{ 'border-danger': grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('changeReason').invalid }"
        >
          <option *ngFor="let cr of changeReasons | keyvalue: sortByValue" [value]="cr.key">
            {{ cr.value }}
          </option>
        </select>
        <div class="text-danger" *ngIf="grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('changeReason').invalid">
          <small>Change Reason is required</small>
        </div>
      </div>
      <div class="mb-2">
        <label for="changeType" class="d-block font-weight-bold">Change Type <span class="text-danger">*</span></label>
        <select
          id="changeType"
          class="custom-select form-control"
          formControlName="changeType"
          [ngClass]="{ 'border-danger': grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('changeType').invalid }"
        >
          <option *ngFor="let ct of changeTypes | keyvalue: sortByValue" [value]="ct.key">
            {{ ct.value }}
          </option>
        </select>
        <div class="text-danger" *ngIf="grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('changeType').invalid">
          <small>Change Type is required</small>
        </div>
      </div>
      <div class="mb-2">
        <label for="changeReason" class="d-block font-weight-bold">Change Reason Comment</label>
        <textarea
          rows="4"
          id="userChangeComment"
          name="userChangeComment"
          class="form-control"
          formControlName="userChangeComment"
        ></textarea>
      </div>
      <div *ngIf="!hideRemoveOptions">
        <label for="changeType" class="d-block font-weight-bold"
          >How should digital offers be removed from just for U?<span class="text-danger">*</span></label
        >
        <div class="radio-remove">
          <div class="radio-spacing">
            <input
              type="radio"
              value="removeUnclipped"
              name="removeOption"
              formControlName="removeOption"
              [ngClass]="{ 'border-danger': grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('removeOption').invalid }"
            />
            <span class="radio-txt-label">Remove for Unclipped </span>

            <tooltip-container [title]="infoTextRemoveUnClipped" class="ml-2 mt-2"></tooltip-container>
          </div>
          <div>
            <input
              type="radio"
              value="removeAll"
              name="removeOption"
              formControlName="removeOption"
              [ngClass]="{ 'border-danger': grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('removeOption').invalid }"
            /><span class="radio-txt-label"> Remove for All</span>

            <tooltip-container [title]="infoTextRemoveAll" class="ml-2 mt-2"></tooltip-container>
          </div>
        </div>
        <div class="text-danger" *ngIf="grSpdBpdCancelRequestFormSubmitted && grSpdBpdCancelRequestForm.get('removeOption').invalid">
          <small>Option is required</small>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="submit" class="btn btn-primary save-btn" (click)="removeDescionsOfferBuilderGrSpdBpd()">Remove</button>
    </div>
  </form>
</ng-template>
<ng-template #assignBuilder>
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <div class="row">
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span class="font-weight-lighter close-icon" aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="row m-1 mb-3">
        <h2>Assign to</h2>
      </div>
    </div>
  </div>
  <div class="modal-body pt-0 pr-5 pl-5">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <offer-builder
            [isSummary]="false"
            [isHome]="true"
            [Channel]="payload.info.deliveryChannel"
            [_modalRef]="modalRef"
          ></offer-builder>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #editRequestReasonModalGRSPD>
  <app-edit-request-reason-modal
    [title]="'Edit Offer Request'"
    [saveBtnText]="'Save Reason'"
    [form]="grSpdEditRequestForm"
    [changeReasonConfig]="changeReasonConfig"
    [changeTypeConfig]="changeEditTypeConfig"
    [formSubmitted]="grSpdEditRequestFormSubmitted"
    (onSaveClick)="editRequestOfferBuilderGrSpd()"
    (onCloseClick)="onCloseClick()"
  >
  </app-edit-request-reason-modal>
</ng-template>
<ng-template #regionsMultipleCopy>
  <div class="modal-header pb-0 px-5">
    <div class="container-fluid">
      <div class="row">
        <button type="button" class="close close-icon pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span class="font-weight-lighter close-icon" aria-hidden="true">&times;</span>
        </button>
      </div>
    </div>
  </div>
  <div class="modal-body px-5">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <app-regions-multiple-copy [modalRef]="modalRef"> </app-regions-multiple-copy>
        </div>
      </div>
    </div>
  </div>
</ng-template>

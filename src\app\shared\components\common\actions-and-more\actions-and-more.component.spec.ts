import { HttpClientTestingModule } from "@angular/common/http/testing";
import { NO_ERRORS_SCHEMA } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { OfferMappingService } from "@appOffersServices/offer-mapping.service";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonService } from "@appServices/common/common.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { HistoryService } from "@appServices/common/history.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import { PermissionsConfigurationService, PermissionsModule, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject, of } from "rxjs";
import { ActionsAndMoreComponent } from "./actions-and-more.component";

describe("ActionsAndMoreComponent", () => {
  let component: ActionsAndMoreComponent;
  let fixture: ComponentFixture<ActionsAndMoreComponent>;
  beforeEach(() => {
    const commonServiceStub = () => ({ isReqInEditing: (payload) => ({}) });
    const facetitemServiceStub = () => ({ programCodeSelected: "GR" });
    const historyServiceStub = () => ({
      getORHistoryDetailsByReqId: (arg) => ({}),
      getORHistoryPreviewByReqId: (arg) => ({}),
      getOROTHistoryPreviewById:(arg) => ({})
    });
    const bsModalServiceStub = () => ({ show: (template, options) => ({}) });
    const requestFormServiceStub = () => ({
      changeReasonData$: { next: () => ({}) },
      getCreatePathFromPC: () => {
        "create";
      },
      requestForm: {
        addControl: () => ({}),
        markAsDirty: () => ({}),
        markAsPristine: () => ({}),
      },
      navigateToSummary: () => {},
      generalOfferTypeService: { facetItemService: {} },
      assignedModal$: { subscribe: (f) => f({}) },
      subscribeCurrentOfferReq: () => ({}),
      isReqSubmitAttempted$: { next: () => ({}) },
      makeSearchCall: () => ({}),
      isEditNotificatonBoolean: { next: () => ({}) },
      requestDigitalStatus: {},
      requestNonDigitalStatus: {},
      requestData$: { next: () => ({}) },
      reqId: {},
      subscribeCurrentOfferReqForProcess: () => ({}),
      requestDigitalStatus$: { next: () => ({}) },
      requestNonDigitalStatus$: { next: () => ({}) },
      dontsaveVal$: { next: () => ({}) },
      passClonedObject$: { next: (a) => ({}) },
      cloningProcess$: { next: () => ({}) },
      resetOnDestroy: () => ({}),
    });
    const formBuilderStub = () => ({ group: (object) => ({}) });
    const routerStub = () => ({ navigateByUrl: (string) => ({}), navigate: (string) => ({}) });
    const toastrServiceStub = () => ({
      success: (string, string1, object) => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({ offerRequestStatuses: {}, offerStatuses: {} }),
      getConfigUrls: (cLONE_API) => ({}),
    });
    const authServiceStub = () => ({
      getTokenString: () => ({}),
      onUserDataAvailable: (arg) => ({}),
      getUserId: () => ({}),
    });
    const generalOfferTypeServiceStub = () => ({
      generalOfferTypeForm: {
        markAsPristine: () => ({}),
        markAsUntouched: () => ({}),
        valid: {},
      },
    });
    const queryGeneratorStub = () => ({
      getQuery: () => ({}),
      getQueryWithFilter: () => ({}),
      setQuery: (string) => ({}),
      pushParameters: (object) => ({}),
      setQueryWithFilter: () => ({})
    });
    const searchOfferRequestServiceStub = () => ({
      searchOfferRequest: (arg, arg2) => ({ subscribe: (f) => f({}) }),
      getOfferDetails: (response) => ({}),
      searchOffer: () => ({}),
    });
    const offerMappingServiceStub = () => ({
      offerPreview: (externalOfferId) => ({}),
      offerDeploy: () => ({}),
      offerRemoved: () => ({}),
    });
    const featureFlagsServiceStub = () => ({
      isFeatureFlagEnabled: () => ({}),
    });
    const facetItemServiceStub = () => ({
        programCodeSelected: { toLowerCase: () => ({}) }
      });
      const commonRouteServiceStub = () => ({ isBpdReqPage: {} });
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        PermissionsModule.forRoot({ permissionsIsolate: true, configurationIsolate: true, rolesIsolate: true }),
      ],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [ActionsAndMoreComponent],
      providers: [
        PermissionsService,
        PermissionsConfigurationService,
        { provide: CommonService, useFactory: commonServiceStub },
        { provide: HistoryService, useFactory: historyServiceStub },
        { provide: BsModalService, useFactory: bsModalServiceStub },
        { provide: RequestFormService, useFactory: requestFormServiceStub },
        { provide: UntypedFormBuilder, useFactory: formBuilderStub },
        { provide: Router, useFactory: routerStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: AuthService, useFactory: authServiceStub },
        {
          provide: GeneralOfferTypeService,
          useFactory: generalOfferTypeServiceStub,
        },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub,
        },
        { provide: OfferMappingService, useFactory: offerMappingServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagsServiceStub },
      ],
    });
    fixture = TestBed.createComponent(ActionsAndMoreComponent);
    component = fixture.componentInstance;
  });
  it("can load instance", () => {
    expect(component).toBeTruthy();
  });
  it("removeAll defaults to: false", () => {
    expect(component.removeAll).toEqual(false);
  });
  it("isRemoveDecisionRequired defaults to: false", () => {
    expect(component.isRemoveDecisionRequired).toEqual(false);
  });
  it("isOfferCancelReasonRequired defaults to: false", () => {
    expect(component.isOfferCancelReasonRequired).toEqual(false);
  });
  it("grSpdBpdCancelRequestFormSubmitted defaults to: false", () => {
    expect(component.grSpdBpdCancelRequestFormSubmitted).toEqual(false);
  });
  it("hideRemoveOptions defaults to: true", () => {
    expect(component.hideRemoveOptions).toEqual(true);
  });
  describe("ngOnChanges", () => {
    it("makes expected calls", () => {
      spyOn(component, "applyActionsAndMore");
      component.ngOnChanges();
      expect(component.applyActionsAndMore).toHaveBeenCalled();
    });
  });
  describe("applyActionsAndMore", () => {
    let initialDataServiceStub: InitialDataService,
    facetItemServiceStub: FacetItemService;

    beforeEach(() => {
      initialDataServiceStub = fixture.debugElement.injector.get(InitialDataService);
      facetItemServiceStub = fixture.debugElement.injector.get(FacetItemService);
      component.payload = {
        info: {
          digitalStatus: "",
          nonDigitalStatus: "",
        },
        rules: {
          startDate: {
            offerEffectiveStartDate: "12/12/2019",
          },
          qualificationAndBenefit: {
            offerRequestOffers: [
              {
                offers: [
                  {
                    offerStatus: "PU",
                  },
                ],
              },
            ],
          },
        },
      };

      spyOn(component, "getValidStatus").and.returnValue("E");
      spyOn(component, "removeFromRuleForPreview");
      spyOn(component, "removeEditForOR_InEditing");
      spyOn(component, "secureOfferRequestActionsOptionsByUserPermissions");
      spyOn(initialDataServiceStub, "getAppData").and.returnValue({ offerRequestStatuses: ["E", "U"] });
    });
    it("makes expected calls", () => {
      component.module = "offerRequest";

      component._router = Object.assign({ url: "edit" }, component._router);

      component.applyActionsAndMore();
      expect(component.getValidStatus).toHaveBeenCalled();
      expect(initialDataServiceStub.getAppData).toHaveBeenCalled();
    });

    it("module is not offerRequest", () => {
     
      component.module = "offerRequest1";

      component._router = Object.assign({ url: "edit" }, component._router);
      component.applyActionsAndMore();
      expect(component.noOptionsForDropDown).toBeTruthy();
    });

    it("offerStatus is DE && offerRequestEditStatus is R", () => {
      component.module = "offerRequest1";

      component.payload.info.offerStatus = "P";
      component.payload.info.offerRequestEditStatus = "R";
      component.payload.info.removedUnclippedOn = true;

      component._router = Object.assign({ url: "edit" }, component._router);
      component.applyActionsAndMore();
      expect(component.noOptionsForDropDown).toBeTruthy();
    });

    it("offerStatus is PU && offerRequestEditStatus is R", () => {
      component.module = "offerRequest1";

      component.payload.info.offerStatus = "P";
      component.payload.info.offerRequestEditStatus = "R";
      component.payload.info.removedUnclippedOn = true;

      component._router = Object.assign({ url: "edit" }, component._router);
      component.applyActionsAndMore();
      expect(component.noOptionsForDropDown).toBeTruthy();
    });

    it("offerProgramCode is BPD", () => {
      component.module = "offerRequest1";

      component.payload.info.offerStatus = "P";
      component.payload.info.offerRequestEditStatus = "R";
      component.payload.info.removedUnclippedOn = true;

      facetItemServiceStub.programCodeSelected = "BPD";
      component._router = Object.assign({ url: "edit" }, component._router);
      component.applyActionsAndMore();
      expect(component.noOptionsForDropDown).toBeTruthy();
    });
  });

  describe("arrayRemove", () => {
    it("should return expected output", () => {
      const output = component.arrayRemove(["Cancel", "Save"]);
      expect(output).toEqual(["Save"]);
    });
  });

  describe("editRequestOfferBuilderGrSpd", () => {
    it("should execute properly", () => {
      component.grSpdEditRequestForm = Object.assign({invalid:false}, component.grSpdEditRequestForm);
      component.payload = {};
     component.editRequestOfferBuilderGrSpd();
    });
  });

  describe("removeEditForOR_InEditing", () => {
    it("makes expected calls", () => {
      const commonServiceStub: CommonService = fixture.debugElement.injector.get(CommonService);
      component.action = "Manage";
      component.actionsAndMore = ["Edit"];
      spyOn(commonServiceStub, "isReqInEditing").and.returnValue(true);
      component.removeEditForOR_InEditing();
      expect(commonServiceStub.isReqInEditing).toHaveBeenCalled();
    });
  });

  // describe("securePluActionsOptionsByUserPermissions", () => {
  //   it("makes expected calls", () => {
  //     const permissionsServiceStub: PermissionsService = fixture.debugElement.injector.get(PermissionsService);
  //     spyOn(component, "getDropDownClassName");
  //     sinon.stub(permissionsServiceStub, "getPermissions").returns({MANAGE_PLU_RESERVATION: "MANAGE_PLU_RESERVATION"})
  //     component.securePluActionsOptionsByUserPermissions();
  //     expect(component.getDropDownClassName).toHaveBeenCalled();
  //   });
  // });

  xdescribe("submitRequestApi", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.payload = {
        info: {
          id: "",
        },
      };
      spyOn(authServiceStub, "onUserDataAvailable");
      component.submitRequestApi();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });

  describe("show Save in Pre-Draft Mode", () => {
    it("makes expected calls", () => {
      const commonServiceStub: CommonService = fixture.debugElement.injector.get(CommonService);
      component.module = "offerRequest";
      component.action = "Detail";
      component.payload = "";
      component._router = Object.assign({ url: "edit" }, component._router);
      component.applyActionsAndMore();
      expect(component.actionsAndMore).toEqual(["Save"]);
    });

    it("calls secureOfferActionsOptionsByUserPermissions method", () => {
      const commonServiceStub: CommonService = fixture.debugElement.injector.get(CommonService);
      component.module = "Edit";
      component.action = "Detail";
      component.payload = "";
      spyOn(component, "secureOfferActionsOptionsByUserPermissions");
      component._router = Object.assign({ url: "edit" }, component._router);
      component.applyActionsAndMore();
      expect(component.secureOfferActionsOptionsByUserPermissions).toHaveBeenCalled();
    });
  });

  describe("ngOnInit", () => {
    it("makes expected calls", () => {
      const formBuilderStub: UntypedFormBuilder = fixture.debugElement.injector.get(UntypedFormBuilder);
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      requestFormServiceStub.dontsaveVal$ = new BehaviorSubject(true);
      component.modalRef = new BsModalRef();
      requestFormServiceStub.requestForm = new UntypedFormGroup({});
      requestFormServiceStub.assignedModal$ = new BehaviorSubject(true);
      spyOn(component, "applyActionsAndMore");
      component.ngOnInit();
      expect(component.applyActionsAndMore).toHaveBeenCalled();
    });
  });
  describe("getHeaders", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "getTokenString");
      component.getHeaders();
      expect(authServiceStub.getTokenString).toHaveBeenCalled();
    });
  });
  describe("openModal", () => {
    it("makes expected calls", () => {
      const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(BsModalService);
      const spy = spyOn(bsModalServiceStub, "show");
      component.openModal("template", "actions");
      expect(spy).toHaveBeenCalled();
    });
  });
  describe("getStatuses", () => {
    it("return  false if  digitalStatus is I", () => {
      component.payload = {
        info: {
          digitalStatus: "I",
          nonDigitalStatus: "",
        },
      };
      let result = component.getStatuses();
      expect(result).toEqual(false);
    });
    it("return  false if  nonDigitalStatus is I", () => {
      component.payload = {
        info: {
          digitalStatus: "",
          nonDigitalStatus: "I",
        },
      };
      let result = component.getStatuses();
      expect(result).toEqual(false);
    });
    it("return  true if  both are not  I", () => {
      component.payload = {
        info: {
          digitalStatus: "",
          nonDigitalStatus: "",
        },
      };
      let result = component.getStatuses();
      expect(result).toEqual(true);
    });

    it("return  false if  digitalStatus is S", () => {
      component.payload = {
        info: {
          digitalStatus: "S",
          nonDigitalStatus: "",
        },
      };
      let result = component.getStatuses();
      expect(result).toEqual(false);
    });
    it("return  false if  nonDigitalStatus is S", () => {
      component.payload = {
        info: {
          digitalStatus: "",
          nonDigitalStatus: "S",
        },
      };
      let result = component.getStatuses();
      expect(result).toEqual(false);
    });
    it("return  true if  both are not  S", () => {
      component.payload = {
        info: {
          digitalStatus: "",
          nonDigitalStatus: "",
        },
      };
      let result = component.getStatuses();
      expect(result).toEqual(true);
    });
  });
  describe("onSelectionRemoveOffer", () => {
    it("removeAll false if event target value is removeUnclipped", () => {
      component.onSelectionRemoveOffer({
        target: {
          value: "removeUnclipped",
        },
      });
      expect(component.removeAll).toEqual(false);
    });
    it("removeAll true if event target value is not removeUnclipped", () => {
      component.onSelectionRemoveOffer({
        target: {
          value: "none",
        },
      });
      expect(component.removeAll).toEqual(true);
    });
  });
  describe("getValidStatus", () => {
    it("return non digital status as prirority of nondigital is higher", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.action = "Manage";
      spyOn(authServiceStub, "getTokenString");
      let result = component.getValidStatus("I", "S", null, null);
      expect(result).toEqual("I");
    });
    it("return digital status as prirority of digital is higher", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.action = "Manage";
      spyOn(authServiceStub, "getTokenString");
      let result = component.getValidStatus("S", "I", null, null);
      expect(result).toEqual("I");
    });
    it("return non digital status if action is not Manage and nonDigitalStatus priority is higher", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "getTokenString");
      let result = component.getValidStatus("I", "S", null, null);
      expect(result).toEqual("S");
    });
    it("return  digital status if action is not Manage and DigitalStatus priority is higher", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "getTokenString");
      let result = component.getValidStatus("S", "I", null, null);
      expect(result).toEqual("S");
    });
  });
  describe("deleteRequestapi", () => {
    it("makes expected calls if request service and.returnValue reqbody", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      const generalOfferTypeServiceStub: GeneralOfferTypeService = fixture.debugElement.injector.get(GeneralOfferTypeService);
      component.modalRef = new BsModalRef();
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({});
      requestFormServiceStub.requestForm = new UntypedFormGroup({});

      spyOn(requestFormServiceStub, "subscribeCurrentOfferReq").and.returnValue({  id: '12123',
        lastUpdatedTs: null,
        createdApplicationId: "cd",
        createdTs: null,
        createdUserId: 'pjain'});
      spyOn(authServiceStub, "onUserDataAvailable");
      spyOn(component.modalRef, "hide");
      component.deleteRequestapi();

      expect(requestFormServiceStub.subscribeCurrentOfferReq).toHaveBeenCalled();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });
  describe("cancelRequestapi", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.payload = {
        info: {
          digitalStatus: {},
        },
        rules: {
          startDate: {
            offerEffectiveStartDate: "12/12/2019",
          },
          qualificationAndBenefit: {
            offerRequestOffers: [
              {
                offers: [
                  {
                    offerStatus: "PU",
                  },
                ],
              },
            ],
          },
        },
      };
      spyOn(authServiceStub, "onUserDataAvailable");
      spyOn(component, "cancelOfferRequestApi");
      component.cancelRequestapi();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("removeDescionsOfferBuilder", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.isRemoveDecisionRequired = true;
      component.payload = {
        info: {
          digitalStatus: {
            id: "1233545",
          },
        },
        lastUpdatedTs: "151545454",
      };
      spyOn(authServiceStub, "onUserDataAvailable");
      component.removeDescionsOfferBuilder();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("cancelRequestapiGrSpdBpd", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.grSpdBpdCancelRequestForm = new UntypedFormGroup({});
      component.payload = {
        info: {
          digitalUiStatus: {},
        },
        rules: {
          startDate: {
            offerEffectiveStartDate: "12/12/2019",
          },
        },
      };
      spyOn(authServiceStub, "onUserDataAvailable");
      spyOn(component, "cancelOfferRequestApiGrSpdBpd");
      component.cancelRequestapiGrSpdBpd();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("removeDescionsOfferBuilderGrSpdBpd", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      component.isOfferCancelReasonRequired = true;
      component.grSpdBpdCancelRequestForm = new UntypedFormGroup({});
      component.payload = {
        info: {
          digitalStatus: {
            id: "1233545",
          },
        },
        lastUpdatedTs: "151545454",
      };
      spyOn(authServiceStub, "onUserDataAvailable");
      component.removeDescionsOfferBuilderGrSpdBpd();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  xdescribe("submitRequestApi", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      const generalOfferTypeServiceStub: GeneralOfferTypeService = fixture.debugElement.injector.get(GeneralOfferTypeService);
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      component.payload = {
        info: {
          digitalStatus: {
            id: "1233545",
          },
        },
        createdApplicationId: "154545541",
        lastUpdatedTs: "151545454",
        createdTs: "21215151",
        createdUserId: "testUserId",
      };
      generalOfferTypeServiceStub.generalOfferTypeForm = new UntypedFormGroup({});
      requestFormServiceStub.isReqSubmitAttempted$ = new BehaviorSubject(true);
      window.location.href = `localhost:2020/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.Edit}`;
      spyOn(authServiceStub, "onUserDataAvailable");
      component.submitRequestApi();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("ngOnDestroy", () => {
    it("makes expected calls", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      spyOn(requestFormServiceStub, "resetOnDestroy");
      component.ngOnDestroy();
      expect(requestFormServiceStub.resetOnDestroy).toHaveBeenCalled();
    });
  });
  describe("deleteOfferRequest", () => {
    it("executes when programCodeSelected is GR", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "GR";

      component.deleteOfferRequest({});
    });
    it("executes when programCodeSelected is SC", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "SC";

      component.deleteOfferRequest({});
    });
    it("executes when programCodeSelected is SPD", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "SPD";
      component.requestDelete_API_GR = "erer";
      component.deleteOfferRequest({});
    });
  });
  describe("editOfferRequest", () => {
    it("makes expected calls", () => {
      component.editOfferRequest({});
    });
  });
  describe("cancelOfferRequest", () => {
    it("makes expected calls", () => {
      component.cancelOfferRequest({});
    });
  });
  describe("cancelOfferRequestGrSpdBpd", () => {
    it("makes expected calls", () => {
      component.offerProgramCode = CONSTANTS.GR;
      component.cancelOfferRequestGrSpdBpd({});
    });
  });
  describe("removeFromRuleForPreview", () => {
    it("makes expected calls", () => {
      component.payload = {
        info: {
          podDetailsEditStatus: {
            editStatus: null,
          },
        },
      };
      component.removeFromRuleForPreview(["Preview"]);
    });
  });
  describe("cancelOfferRequestApi", () => {
    it("makes expected calls", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(component, "openModal");
      spyOn(historyServiceStub, "getOROTHistoryPreviewById");
      component.modalRef = new BsModalRef();
      spyOn(requestFormServiceStub, "resetOnDestroy");
      component.cancelOfferRequestApi({ isRemoveDecisionRequired: true, isDesicionSelected: false });
      expect(component.openModal).toHaveBeenCalled();
    });
    it("makes expected calls", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      const router = fixture.debugElement.injector.get(Router);
      spyOn(historyServiceStub, "getOROTHistoryPreviewById");
      spyOn(toastrServiceStub, "success");
      component.type = "More";
      spyOn(component, "cancelOfferRequest").and.returnValue(of({}));
      component.modalRef = new BsModalRef();
      spyOn(requestFormServiceStub, "resetOnDestroy");
      component.cancelOfferRequestApi({ isRemoveDecisionRequired: true, isDesicionSelected: true });
    });
  });
  describe("cancelOfferRequestApiGrSpdBpd", () => {
    it("makes expected calls", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      spyOn(component, "openModal");
      component.payload = {
        info: {
          digitalUiStatus: {},
        },
        rules: {
          startDate: {
            offerEffectiveStartDate: "12/12/2019",
          },
        },
      };
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(historyServiceStub, "getOROTHistoryPreviewById");
      component.modalRef = new BsModalRef();
      spyOn(requestFormServiceStub, "resetOnDestroy");
      component.grSpdBpdCancelRequestForm = new UntypedFormGroup({});
      component.cancelOfferRequestApiGrSpdBpd({ isOfferCancelReasonRequired: true, isOfferCancelReasonProvided: false });
      expect(component.openModal).toHaveBeenCalled();
    });
    it("makes expected calls", () => {
        component.payload = {
            info: {
              digitalUiStatus: {},
            },
            rules: {
              startDate: {
                offerEffectiveStartDate: "12/12/2019",
              },
            },
          };
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      const router = fixture.debugElement.injector.get(Router);
      spyOn(toastrServiceStub, "success");
      component.type = "More";
      spyOn(component, "cancelOfferRequestGrSpdBpd").and.returnValue(of({}));
      component.modalRef = new BsModalRef();
      spyOn(requestFormServiceStub, "resetOnDestroy");
      spyOn(requestFormServiceStub, "navigateToSummary");
      component.grSpdBpdCancelRequestForm = new UntypedFormGroup({});
      component.cancelOfferRequestApiGrSpdBpd({ isOfferCancelReasonRequired: true, isOfferCancelReasonProvided: true });
    });

    it("makes expected calls", () => {
        component.payload = {
            info: {
              digitalUiStatus: {},
            },
            rules: {
              startDate: {
                offerEffectiveStartDate: "12/12/2019",
              },
            },
          };
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(QueryGenerator);

      spyOn(queryGeneratorStub, "getQueryWithFilter");
      spyOn(component, "openModal");
      component.offerProgramCode = "GR"
      component.modalRef = new BsModalRef();
      spyOn(requestFormServiceStub, "resetOnDestroy");
      component.grSpdBpdCancelRequestForm = new UntypedFormGroup({removeOption: new UntypedFormControl(null)});
      component.type = "Actions";
      spyOn(component, "cancelOfferRequestGrSpdBpd").and.returnValue(of({}));
      component.cancelOfferRequestApiGrSpdBpd({ isOfferCancelReasonRequired: false, isOfferCancelReasonProvided: false });
      
    });

  });

  describe("editReasonOfferRequestApiGrSpd", () => {
  it("makes expected calls", () => {
    component.modalRef = Object.assign({hide:() => {}}, component.modalRef );
    component.grSpdEditRequestForm = new UntypedFormGroup({editChangeReason: new UntypedFormControl(null)});
    spyOn(component, "getStatuses").and.returnValue(true);
    component.payload =    {
      info:{},
      rules: {
        qualificationAndBenefit :{},
      }
    }
    component.editReasonOfferRequestApiGrSpd();
    
  });

});
  describe("deleteOfferRequestApi", () => {
    it("makes expected calls", () => {
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      spyOn(component, "setQueryGenerator");
      const router = fixture.debugElement.injector.get(Router);
      spyOn(router, "navigateByUrl");
      component.type = "More";
      spyOn(toastrServiceStub, "success");
      spyOn(component, "deleteOfferRequest").and.returnValue(of({}));
      component.modalRef = new BsModalRef();
      component.deleteOfferRequestApi({});
      expect(component.deleteOfferRequest).toHaveBeenCalled();
    });
    it("makes expected calls", () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(SearchOfferRequestService);
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      spyOn(searchOfferRequestServiceStub, "searchOfferRequest").and.returnValue(of({ render: "" }));
      spyOn(searchOfferRequestServiceStub, "getOfferDetails");
      spyOn(toastrServiceStub, "success");
      component.type = null;
      spyOn(component, "deleteOfferRequest").and.returnValue(of({}));
      component.modalRef = new BsModalRef();
      component.deleteOfferRequestApi({});
      //expect(searchOfferRequestServiceStub.getOfferDetails).toHaveBeenCalled();
    });
  });
  describe("submitOfferRequestApi", () => {
    it("makes expected calls", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(SearchOfferRequestService);
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      spyOn(toastrServiceStub, "success");
      spyOn(searchOfferRequestServiceStub, "getOfferDetails");
      spyOn(searchOfferRequestServiceStub, "searchOfferRequest").and.returnValue(of({ render: "" }));
      spyOn(component, "submitOfferRequest").and.returnValue(of({}));
      component.page = "Manage";
      requestFormServiceStub.isReqSubmitAttempted$ = new BehaviorSubject(false);
      component.submitOfferRequestApi({});
      expect(searchOfferRequestServiceStub.getOfferDetails).toHaveBeenCalled();
    });
    it("makes expected calls if page is summary", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(SearchOfferRequestService);
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      spyOn(toastrServiceStub, "success");
      spyOn(requestFormServiceStub, "makeSearchCall");
      spyOn(component, "submitOfferRequest").and.returnValue(of({}));
      component.page = "Summary";
      requestFormServiceStub.isReqSubmitAttempted$ = new BehaviorSubject(false);
      component.submitOfferRequestApi({});
      expect(requestFormServiceStub.makeSearchCall).toHaveBeenCalled();
    });
    it("makes expected calls if page is summary", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(SearchOfferRequestService);
      const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
      spyOn(toastrServiceStub, "success");
      spyOn(requestFormServiceStub, "makeSearchCall");
      spyOn(component, "submitOfferRequest").and.returnValue(of({}));
      component.page = "Summary";
      requestFormServiceStub.isReqSubmitAttempted$ = new BehaviorSubject(false);
      component.submitOfferRequestApi({});
      expect(requestFormServiceStub.makeSearchCall).toHaveBeenCalled();
    });
  });
  describe("clickAction", () => {
    it("makes expected calls if item is Edit and module is offer request", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const formBuilderStub: UntypedFormBuilder = fixture.debugElement.injector.get(UntypedFormBuilder);
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "onUserDataAvailable");
      component.module = "offerRequest";
      spyOn(formBuilderStub, "group");
      spyOn(requestFormServiceStub, "subscribeCurrentOfferReqForProcess");
      spyOn(component, "getStatuses").and.returnValue(true);
      spyOn(component, "setOfferRequestOffers");
      component.payload = {
        info: {
          id: "12454",
          digitalStatus: "A",
          nonDigitalStatus: "S",
        },
      };
      requestFormServiceStub.requestData$ = new BehaviorSubject({});
      component.requestForm = new UntypedFormGroup({});
      component.clickAction("Edit");
      expect(component.getStatuses).toHaveBeenCalled();
    });
    it("makes expected calls if item is Edit and module is Offer", () => {
      spyOn(component, "getStatuses");
      component.module = "offer";
      component.payload = {
        info: {
          id: {
            externalOfferId: "6322",
          },
          digitalStatus: "A",
          nonDigitalStatus: "S",
        },
      };
      component.clickAction("Edit");
      expect(component.getStatuses).not.toHaveBeenCalled();
    });
    it("makes expected calls if item is Delete ", () => {
      spyOn(component, "openModal");
      component.clickAction("Delete");
      expect(component.openModal).toHaveBeenCalled();
    });
    it("makes expected calls if item is Cancel ", () => {
      spyOn(component, "openModal");
      component.clickAction("Cancel");
      expect(component.openModal).toHaveBeenCalled();
    });
    it("makes expected calls if item is Edit and Status is Completed", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "GR";
      component.module = "offerRequest";
      component.payload = {
        info: {
          id: "12454",
          digitalStatus: "D",
          digitalUiStatus: "D",
        },
      };
      component.grSpdEditRequestForm = new UntypedFormGroup({});
      spyOn(component, "prepareGrSpdEditRequestReasonForm");
      component.clickAction("Edit");
      expect(component.prepareGrSpdEditRequestReasonForm).toHaveBeenCalled();
    });
    it("makes expected calls if item is Edit and Status is Remove Unclipped", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "GR";
      component.module = "offerRequest";
      component.payload = {
        info: {
          id: "12454",
          digitalStatus: "RU",
          digitalUiStatus: "RU",
        },
      };
      component.grSpdEditRequestForm = new UntypedFormGroup({});
      spyOn(component, "prepareGrSpdEditRequestReasonForm");
      component.clickAction("Edit");
      expect(component.prepareGrSpdEditRequestReasonForm).toHaveBeenCalled();
    });
    it("makes expected calls if item is Submit ", () => {
      spyOn(component, "submitRequestApi");
      component.clickAction("Submit");
      expect(component.submitRequestApi).toHaveBeenCalled();
    });
    it("makes expected calls if item is delete and module is pluManagement ", () => {
      component.module = "pluManagement";
      spyOn(component, "openModal");
      component.clickAction("Delete");
      expect(component.openModal).toHaveBeenCalled();
    });
    it("makes expected calls if item is Save ", () => {
      //component.emitAction = new EventEmitter();
      component.clickAction("Save");
    });
    it("makes expected calls if item is Preview ", () => {
      const offerMappingServiceStub: OfferMappingService = fixture.debugElement.injector.get(OfferMappingService);
      component.payload = {
        info: {
          id: {
            externalOfferId: "1554",
          },
        },
      };
      spyOn(offerMappingServiceStub, "offerPreview");
      //component.emitAction  = new EventEmitter();
      component.clickAction("Preview");
      expect(offerMappingServiceStub.offerPreview).toHaveBeenCalled();
    });
    it("clone copy function", () => {
      component.module = "offerRequest";
      component.payload = {
        info: {
          id: "123",
        },
      };
      const spy = spyOn(component, "copyOfferRequest");
      component.clickAction("Copy");
      expect(spy).toHaveBeenCalled();
    });

    it("clone copy function should not call if module is offer", () => {
      component.module = "offer";
      component.payload = {
        info: {
          id: "123",
        },
      };
      const spy = spyOn(component, "copyOfferRequest");
      component.clickAction("Copy");
      expect(spy).not.toHaveBeenCalled();
    });

    it("should navigate for clone", () => {
      component.requestForm = new UntypedFormGroup({});
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const spy = spyOn(requestFormServiceStub.passClonedObject$, "next");
      component.onNavigatedForClone();
      expect(spy).toHaveBeenCalled();
    });

    it(" if we click on Edit in offer request", () => {
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const formBuilderStub: UntypedFormBuilder = fixture.debugElement.injector.get(UntypedFormBuilder);
      const authServiceStub: AuthService = fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "onUserDataAvailable");
      component.module = "offerRequest";
      spyOn(formBuilderStub, "group");
      spyOn(requestFormServiceStub, "subscribeCurrentOfferReqForProcess");
      spyOn(component, "getStatuses").and.returnValue(true);
      spyOn(component, "setOfferRequestOffers");
      component.payload = {
        info: {
          id: "12454",
          digitalStatus: "A",
          nonDigitalStatus: "S",
        },
      };
      requestFormServiceStub.requestData$ = new BehaviorSubject({});
      component.requestForm = new UntypedFormGroup({});
      component.clickAction("Edit");
      expect(component.getStatuses).toHaveBeenCalled();
    });

    it("copyOfferRequest function", () => {
      component.requestForm = new UntypedFormGroup({});
      const router: Router = fixture.debugElement.injector.get(Router);
      const routerSpy = spyOn(router, "navigate").and.returnValue(Promise.resolve(true));
      const spy = spyOn(component, "onNavigatedForClone");
      component.payload = {
        info: {
          id: "123",
        },
      };
      component.copyOfferRequest("1234");
      //expect(spy).toHaveBeenCalled();
      expect(routerSpy).toHaveBeenCalled();
    //   sinon.assert.calledOnce(routerSpy);
    });

    it("passClonedObject obsrbls", () => {
      component.requestForm = new UntypedFormGroup({});
      const requestFormServiceStub: RequestFormService = fixture.debugElement.injector.get(RequestFormService);
      const spy = spyOn(requestFormServiceStub.passClonedObject$, "next");
      component.onNavigatedForClone();
      expect(spy).toHaveBeenCalled();
    });

    it("when we click on copy clone function should call", () => {
      component.payload = {
        info: {
          id: "123",
        },
      };
      const spy = spyOn(component, "copyOfferRequest");
      component.clickAction("Copy");
      expect(spy).toHaveBeenCalled();
    });

    it("makes expected calls if item is Deploy Now & Defer Deploy ", () => {
      spyOn(component, "deployOfferApi");
      component.clickAction("Deploy Now");
      expect(component.deployOfferApi).toHaveBeenCalled();
      component.clickAction("Defer Deploy");
      expect(component.deployOfferApi).toHaveBeenCalled();
    });
  });
  describe("secureEditForGR_SPD_After_Process", () => {
    it("should make expected calls", () => {
      component.secureEditForGR_SPD_After_Process("P", "Copy");
      expect(component.filteredActionListByUserPermissions).toEqual(["Copy"]);
    });
    it("should make expected calls", () => {
      component.secureEditForGR_SPD_After_Process("S", "Edit");
      expect(component.filteredActionListByUserPermissions).toEqual(["Edit"]);
    });
  });
  describe("deployOfferApi", () => {
    it("should make expected calls", () => {
      const offerMappingServiceStub: OfferMappingService = fixture.debugElement.injector.get(OfferMappingService);
      component.payload = {
        info: {
          id: {
            externalOfferId: "3434343-D",
          },
        },
      };
      spyOn(offerMappingServiceStub, "offerDeploy");
      component.deployOfferApi("Deploy Now");
      expect(offerMappingServiceStub.offerDeploy).toHaveBeenCalled();
    });
  });

  describe("User Roles Test Cases - actions-and-more.comp", () => {
    it("If Assign is there in the payload and user has ASSIGN_DIGITAL_USERS permission ClassName should be blank", () => {
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["ASSIGN_DIGITAL_USERS"]);
      component.payload = {
        info: {
            offerRequestType: "INSTANT_WIN"
        }
      }
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Assign", "Edit", "Copy"], "SC", "S");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("If Assign is there in the payload and user has ASSIGN_NON_DIGITAL_USERS permission ClassName should be blank", () => {
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["ASSIGN_NON_DIGITAL_USERS"]);
      component.payload = {
        info: {
            offerRequestType: "INSTANT_WIN"
        }
      }
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Assign"], "MF", "S");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });

    it("If Assign is NOT there in the payload and user has DO_STORE_COUPON_REQUESTS permission and programCode is SC ClassName should be blank", () => {
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_STORE_COUPON_REQUESTS"]);
      component.payload = {
        info: {
            offerRequestType: "INSTANT_WIN"
        }
      }
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Edit"], "SC", "S");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("If Assign is NOT there in the payload and user has EDIT_GR_SPD_REQUESTS permission and programCode is PD ClassName should be blank", () => {
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["EDIT_GR_SPD_REQUESTS"]);
      component.actionsDropDownCssBasedOnPermissions = "";
      component.payload = {
        info: {
            offerRequestType: "INSTANT_WIN"
        }
      }
      component.secureOfferRequestActionsOptionsByUserPermissions(["Edit", "Copy"], "SPD", "P");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("If Assign is NOT there in the payload and user has EDIT_GR_SPD_REQUESTS permission and programCode is SPPD ClassName should be 'hide'", () => {
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["EDIT_GR_SPD_REQUESTS"]);
      component.actionsDropDownCssBasedOnPermissions = "";
      component.payload = {
        info: {
            offerRequestType: "INSTANT_WIN"
        }
      }
      component.secureOfferRequestActionsOptionsByUserPermissions(["Cancel"], "SPD", "S");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("hide");
    });
    it("If Assign is NOT there in the payload and user has EDIT_GR_REQUESTS permission and programCode is GR ClassName should be blank", () => {
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["EDIT_GR_SPD_REQUESTS"]);
            component.payload = {
        info: {
            offerRequestType: "INSTANT_WIN"
        }
      }
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Edit"], "GR", "S");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    // xit('If Assign is NOT there in the payload and user has DO_MANUFACTURER_COUPON_REQUESTS permission and programCode is MF ClassName should be blank', () => {
    //   const permissionService = fixture.debugElement.injector.get(PermissionsService);
    //   permissionService.loadPermissions(['DO_MANUFACTURER_COUPON_REQUESTS']);
    //   component.actionsDropDownCssBasedOnPermissions = '';
    //   component.secureOfferRequestActionsOptionsByUserPermissions(['Cancel', 'Edit'], 'MF');
    //   expect(component.actionsDropDownCssBasedOnPermissions).toEqual('');
    // });
    // xit('If actionslist is empty, classname should disable actions dropdown', () => {
    //   const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
    //   const permissionService = fixture.debugElement.injector.get(PermissionsService);

    //   permissionConfigurationService.addPermissionStrategy('disable', (tf: any) => {
    //     this.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, 'disabled', 'disabled');
    //     this.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, 'disable');
    //   });

    //   permissionConfigurationService.setDefaultOnUnauthorizedStrategy('disable');
    //   permissionService.loadPermissions(['DO_MANUFACTURER_COUPON_REQUESTS']);
    //   component.actionsDropDownCssBasedOnPermissions = '';
    //   component.secureOfferRequestActionsOptionsByUserPermissions(null, 'MF');
    //   expect(component.actionsDropDownCssBasedOnPermissions).toEqual('disable');
    // });
    it("Authorization Property check -> If the Authorized strategy is set to disable and user doesnt have permission button should have disable class", () => {
      const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
    //   permissionConfigurationService.addPermissionStrategy("disable", (tf: any) => {
    //     component.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, "disabled", "disabled");
    //     component.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, "disable");
    //   });
    component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Digital",
        },
      };
    //   permissionConfigurationService.setDefaultOnUnauthorizedStrategy("disable");
      permissionService.loadPermissions(["VIEW_OFFER_REQUESTS"]);
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Cancel", "Edit"], "MF", "I");
    //   expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Authorization Property check -> If the Authorized strategy is set to Remove (Or anything other thn Disable) and user has permission button should have hide class", () => {
      const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
    //   permissionConfigurationService.addPermissionStrategy("remove", (tf: any) => {
    //     this.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, "remove", "remove");
    //     this.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, "remove");
    //   });
      permissionConfigurationService.setDefaultOnUnauthorizedStrategy("remove");
      permissionService.loadPermissions(["ASSIGN_DIGITAL_USERS"]);
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Cancel", "Edit"], "MF", "I");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("hide");
    });
    it("Authorization Property check -> If the Authorized strategy is set to Remove (Or anything other thn Disable) and user has unassociated permission and programCode,  button should have hide class", () => {
      const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
    //   permissionConfigurationService.addPermissionStrategy("remove", (tf: any) => {
    //     this.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, "remove", "remove");
    //     this.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, "remove");
    //   });
      permissionConfigurationService.setDefaultOnUnauthorizedStrategy("remove");
      permissionService.loadPermissions(["EDIT_GR_SPD_REQUESTS"]);
      component.actionsDropDownCssBasedOnPermissions = "";
      component.secureOfferRequestActionsOptionsByUserPermissions(["Cancel", "Edit"], "SC", "I");
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("hide");
    });
    // it("Authorization Property check -> If the Authorized strategy is set to Disable, but user has associated permission and programCode,  button should NOT have hide class", () => {
    //   const permissionConfigurationService = fixture.debugElement.injector.get(PermissionsConfigurationService);
    //   const permissionService = fixture.debugElement.injector.get(PermissionsService);
    //   permissionConfigurationService.addPermissionStrategy('disable', (tf: any) => {
    //     this.renderer2.setAttribute(tf.elementRef.nativeElement.nextSibling, 'disabled', 'disabled');
    //     this.renderer2.addClass(tf.elementRef.nativeElement.nextSibling, 'disable');
    //   });
    //   permissionConfigurationService.setDefaultOnUnauthorizedStrategy('disable');
    //   permissionService.loadPermissions(['EDIT_GR_SPD_REQUESTS']);
    //   component.actionsDropDownCssBasedOnPermissions = '';
    //   component.secureOfferRequestActionsOptionsByUserPermissions(['Cancel', 'Edit'], 'PD');
    //   expect(component.actionsDropDownCssBasedOnPermissions).toEqual('');
    // });
    //TODO : add user roles Testcases TS and HTML
    it("Actions Dropdown - When user has DO_ASSIGNED_DIGITAL_OFFERS and logged in user is assigned user and offer type is digital actionsDropDownCssBasedOnPermissions should be blank -> no disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_DIGITAL_OFFERS and logged in user is NOT assigned user and offer type is digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "sdube05",
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_DIGITAL_OFFERS and logged in user is assigned user and offer type is NOT digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_DIGITAL_OFFERS and logged in user is assigned user BUT offer type is NOT AVAILABLE actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          // offerProviderName: 'OMS Non-Digital'
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_DIGITAL_OFFERS and logged in user || assigned user data is NOT AVAILABLE and offer type is digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho0"
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_NON_DIGITAL_OFFERS and logged in user is assigned user and offer type is Non digital actionsDropDownCssBasedOnPermissions should be blank -> no disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_NON_DIGITAL_OFFERS and logged in user is NOT assigned user and offer type is Non digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "sdube05",
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_NON_DIGITAL_OFFERS and logged in user is assigned user and offer type is NOT Non digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_NON_DIGITAL_OFFERS and logged in user is assigned user BUT offer type is NOT AVAILABLE actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          // offerProviderName: 'OMS Non-Digital'
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ASSIGNED_NON_DIGITAL_OFFERS and logged in user || assigned user data is NOT AVAILABLE and offer type is Non digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "bash0"
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ASSIGNED_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ANY_DIGITAL_OFFERS and offer type is digital actionsDropDownCssBasedOnPermissions should be blank -> no disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ANY_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("Actions Dropdown - When user has DO_ANY_DIGITAL_OFFERS and offer type is NOT digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ANY_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user dont have DO_ANY_DIGITAL_OFFERS but offer type is digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["VIEW_OFFER_REQUESTS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_ANY_NON_DIGITAL_OFFERS and offer type is Non digital actionsDropDownCssBasedOnPermissions should be blank -> no disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ANY_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("Actions Dropdown - When user has DO_ANY_NON_DIGITAL_OFFERS and offer type is NOT Non digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_ANY_NON_DIGITAL_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user dont have DO_ANY_NON_DIGITAL_OFFERS but offer type is Non digital actionsDropDownCssBasedOnPermissions should NOT be blank -> ADD disable class", () => {
      component.loggedInUserId = "basho00";
      component.payload = {
        info: {
          assignment: {
            userId: "basho00",
          },
          offerProviderName: "OMS Non-Digital",
        },
      };
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["VIEW_OFFER_REQUESTS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
    it("Actions Dropdown - When user has DO_POD_OFFERS actionsDropDownCssBasedOnPermissions should be blank -> no disable class", () => {
      component.podDetails = true;
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_POD_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("");
    });
    it("Actions Dropdown - When user has DO_POD_OFFERS BUT isPODView is false actionsDropDownCssBasedOnPermissions should Not be blank -> Add disable class", () => {
      component.podDetails = false;
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      permissionService.loadPermissions(["DO_POD_OFFERS"]);
      component.secureOfferActionsOptionsByUserPermissions(component.actionsAndMore, component.payload, component.podDetails);
      expect(component.actionsDropDownCssBasedOnPermissions).toEqual("disable");
    });
  });
  describe("checkOfferValidStatus", () => {
    it("should return expected value", () => {
      component.payload ={createdApplicationId:"OMS"};
      component.action = "Manage";
      let digitalStatus = "C";
      let digitalEditStatus = { editStatus: "R" };
      let nonDigitalStatus = "C";
      let nonDigitalEditStatus = { editStatus: "R" };
      let rules = { Manage: [], Summary: [], Detail: [] };
      component.checkOfferValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus, rules); //?
      expect(rules.Manage).toEqual(["Copy"]);
    });
    it("make expected calls", () => {
      component.action = "Detail";
      component.payload = {createdApplicationId:"OMS"};
      let digitalStatus = "";
      let digitalEditStatus = { editStatus: "" };
      let nonDigitalStatus = "";
      let nonDigitalEditStatus = { editStatus: "" };
      let rules = { Manage: [], Summary: [], Detail: [] };
      let spy = spyOn(component, "arrayRemove").and.returnValue([]);
      component.checkOfferValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus, rules); //?
      expect(spy).toHaveBeenCalled();
    });
    it("make expected calls", () => {
      component.action = "Detail";
      component.payload = {createdApplicationId:"OMS"};
      let digitalStatus = "P";
      let digitalEditStatus = { editStatus: "" };
      let nonDigitalStatus = "P";
      let nonDigitalEditStatus = { editStatus: "" };
      let rules = { Manage: [], Summary: [], Detail: [] };
      let spy = spyOn(component, "arrayRemove").and.returnValue([]);
      component.checkOfferValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus, rules); //?
      expect(spy).toHaveBeenCalled();
    });
  });
});
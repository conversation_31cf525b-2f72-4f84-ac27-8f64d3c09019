import { KeyValue } from "@angular/common";
import { HttpClient } from "@angular/common/http";
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, TemplateRef, ViewChild } from "@angular/core";
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { OfferMappingService } from "@appOffersServices/offer-mapping.service";
import { RequestFormService } from "@appRequestServices/request-form.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { CommonService } from "@appServices/common/common.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { GeneralOfferTypeService } from "@appServices/details/general-offer-type.service";
import { PermissionsConfigurationService, PermissionsService } from '@appShared/albertsons-angular-authorization';
import {
  OFFER_REQUEST_WORKFLOW_RULES,
  OFFER_REQUEST_WORKFLOW_RULESBPD,
  OFFER_REQUEST_WORKFLOW_RULESGR,
  OFFER_REQUEST_WORKFLOW_RULESSPD,
  OFFER_REQUEST_WORKFLOW_RULES_UPP,
  OFFER_WORKFLOW_RULESBPD,
  OFFER_WORKFLOW_RULES_GR_SPD,
  OFFER_WORKFLOW_RULES_MF,
  OFFER_WORKFLOW_RULES_SC,
  PLU_MANAGEMENT_RULES
} from "@appShared/rules/workflow_rules";
import { mmDdYySlash_DateFormat, mmDdYyyySlash_DateFormatWithoutUTC } from "@appUtilities/date.utility";
import { nullCheckProperty } from "@appUtilities/nullCheck.utility";
import { scrollToErrorField } from "@appUtilities/scrollToError";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import * as moment from "moment";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { HistoryService } from "../../../services/common/history.service";

@Component({
  selector: "app-actions-and-more",
  templateUrl: "./actions-and-more.component.html",
  styleUrls: ["./actions-and-more.component.scss"],
})
export class ActionsAndMoreComponent extends UnsubscribeAdapter implements OnInit, OnChanges {
  @Input() payload;
  @Input() type;
  @Input() action;
  @Input() page;
  @Input() module;
  @Output() emitAction = new EventEmitter();
  @Input() podDetails;
  @Input() showPreviewAction;
  @Input() isShowSaveCancel;
  @Input() isDisplaySaveCta;

  actionsAndMore;
  @ViewChild("assignBuilder")
  private assignBuilder: TemplateRef<any>;

  @ViewChild("deleteConfirm")
  private deleteConfirm: TemplateRef<any>;

  @ViewChild("pluDeleteConfirm")
  private pluDeleteConfirm: TemplateRef<any>;

  @ViewChild("cancelRequest")
  private cancelRequest: TemplateRef<any>;

  @ViewChild("cancelRequestConfirm")
  private cancelRequestConfirm: TemplateRef<any>;

  @ViewChild("cancelRequestConfirmGrSpdBpd")
  private cancelRequestConfirmGrSpdBpd: TemplateRef<any>;

  @ViewChild("editRequestReasonModalGRSPD")
  private editRequestReasonModalGRSPD: TemplateRef<any>;

  @ViewChild("regionsMultipleCopy")
  private regionsMultipleCopy: TemplateRef<any>;

  regionsData: any;
  selectedRegion: any;

  noOptionsForDropDown: boolean = false;
  modalRef: BsModalRef;
  requestForm: any;
  removeAll: boolean = false;
  isRemoveDecisionRequired: boolean = false;
  isOfferCancelReasonRequired: boolean = false;

  grSpdBpdCancelRequestForm: UntypedFormGroup;
  grSpdBpdCancelRequestFormSubmitted: boolean = false;
  changeReasons: any;
  changeTypes: any;
  hideRemoveOptions: boolean = true;

  grSpdEditRequestForm: UntypedFormGroup;
  grSpdEditRequestFormSubmitted: boolean = false;

  offerProgramCode: string;
  CONSTANTS = CONSTANTS;

  requestCancel_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_CANCEL_API);
  requestCancel_API_GR_SPD_BPD: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_CANCEL_API_GR_SPD_BPD);
  requestDelete_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_DELETE_API);
  requestDelete_API_GR: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_DELETE_API_GR);
  requestProcess_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_PROCESS_API);
  grSpdRequestProcess_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_GR_SPD_REQ_PROCESS_API);
  requestSubmit_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_SUBMIT_API);
  requestEdit_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_EDIT_API);
  offerDeploy_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_DEPLOY_API);
  offerPublish_API: string = this._initialDataService.getConfigUrls(CONSTANTS.OFFER_PUBLISH_API);
  dontsaveVal: boolean = false;
  infoTextRemoveUnClipped =
    "This action will remove the coupon from just for U® if it has not been clipped. For all users who have clipped the offer – the coupon will be visible on just for U® and will be redeemable.";
  infoTextRemoveAll =
    "This action will remove the coupon from just for U® for all users – including clipped and unclipped. The coupon will not be redeemable.";
  actionsDropDownCssBasedOnPermissions = "disable";
  filteredActionListByUserPermissions: string[] = [];
  workflowRequestRules: any;
  loggedInUserId: string = this._authService.getUserId();

  changeReasonConfig = [];
  changeEditTypeConfig = [];

  sortByValue = (a: KeyValue<string, string>, b: KeyValue<string, string>): number => {
    return a.value.localeCompare(b.value);
  };
  offerRequestId: any;
  statusesWeightage: any = { I: 1, S: 2, A: 3, P: 4, D: 5, EX:6 };//To decide the final status among D, ND statuses

  rulesToHideActions={
    "INSTANT_WIN":{
      programCodes:[CONSTANTS.SC],
      flag:this.featureFlagsService.isFeatureFlagEnabled('enableEnterpriseInstantWin')
    }
  };
  createdApplicationId:string = "OMS";
  constructor(
    private modalService: BsModalService,
    public _requestFormService: RequestFormService,
    private fb: UntypedFormBuilder,
    public _router: Router,
    public _toastr: ToastrService,
    private _http: HttpClient,
    private _initialDataService: InitialDataService,
    public generalOfferTypeService: GeneralOfferTypeService,
    public _authService: AuthService,
    private queryGenerator: QueryGenerator,
    private _searchOfferRequestService: SearchOfferRequestService,
    private comomonService: CommonService,
    private _offerMappingService: OfferMappingService,
    private _permissionsService: PermissionsService,
    private _permissionsConfigurationService: PermissionsConfigurationService,
    private _historyService: HistoryService,
    private facetItemService: FacetItemService,
    private commonRouteService: CommonRouteService,
    private featureFlagsService:FeatureFlagsService,
    private commonSearchService: CommonSearchService
  ) {
    super();
  }
  setFormFields() {
    return {
      /* OFFER REQUEST */
      deliveryChannel: [this.payload.info.deliveryChannel],
    };
  }
  ngOnChanges() {
    this.applyActionsAndMore();
  }

  setChangeDetails(obj) {
    const { initialData } = obj;
    if ([CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD].includes(this.offerProgramCode)) {
      this.changeReasons = initialData.offerRequestCancelChangeReason;
      this.changeTypes = initialData.offerRequestCancelChangeReasonType;
      this.changeReasonConfig = initialData.offerRequestEditChangeReason;
      this.changeEditTypeConfig = initialData.offerRequestEditChangeReasonType;
    }
  }

  setWorkflowRequestRules() {
    this.workflowRequestRules = JSON.parse(JSON.stringify(OFFER_REQUEST_WORKFLOW_RULES));
    if(this.payload.createdApplicationId === "UPP" && this.featureFlagsService.isuppEnabled && this.facetItemService.programCodeSelected === CONSTANTS.SC)
    {
      this.workflowRequestRules = JSON.parse(JSON.stringify(OFFER_REQUEST_WORKFLOW_RULES_UPP));
    }
    if (this.facetItemService.programCodeSelected === CONSTANTS.GR) {
      this.workflowRequestRules = JSON.parse(JSON.stringify(OFFER_REQUEST_WORKFLOW_RULESGR));
    } else if (this.facetItemService.programCodeSelected === CONSTANTS.SPD) {
      this.workflowRequestRules = JSON.parse(JSON.stringify(OFFER_REQUEST_WORKFLOW_RULESSPD));
    } else if (this.facetItemService.programCodeSelected === CONSTANTS.BPD) {
      this.workflowRequestRules = JSON.parse(JSON.stringify(OFFER_REQUEST_WORKFLOW_RULESBPD));
    }
  }

  getStatus({statusesKey, initialData,digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus}){
    let offerRequestStatuses = [CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD].includes(this.offerProgramCode)
          ? initialData[`offerRequestStatuses${statusesKey}`]
          : initialData.offerRequestStatuses;
    if(this.featureFlagsService.isOfferRequestArchivalEnabled && this.commonSearchService.isShowExpiredInQuery)
    {
      /*If selected then all results will be Expired and are from OR archival Table
        So Setting/ updating all ORs to Expired irresepective of their status.
      */
     return offerRequestStatuses[CONSTANTS.EXPIRED_STATUS_OR];
    }
    return offerRequestStatuses[this.getValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus)];
  }

  applyActionsAndMore() {
    const pathsArr = this._router.url.split("/"),
      lastPath = pathsArr[pathsArr.length - 1],
      offerReqCreatePathsArr = [ROUTES_CONST.REQUEST.Create, ROUTES_CONST.REQUEST.GRCreate]; //TO DO: Handle Program Codes

    if (this.payload && offerReqCreatePathsArr.indexOf(lastPath) < 0) {
      const initialData = this._initialDataService.getAppData();
      const {
        offerProgramCode = "",
        nonDigitalStatus = "",
        digitalStatus = "",
        digitalEditStatus = "",
        nonDigitalEditStatus = "",
      } = this.payload.info || {};
      
      let rule, _status, _editStatus;

      this.offerProgramCode = this.facetItemService.programCodeSelected;

      this.setChangeDetails({ initialData });

      if (this.module === "offerRequest") {
        let statusesKey = this.offerProgramCode;
        if (this.commonRouteService.isBpdReqPage) {
          //For BPD, use SPD status object
          statusesKey = CONSTANTS.SPD;
        }

              
        _status =this.getStatus({statusesKey,initialData,digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus});  
         

        this.setWorkflowRequestRules();

        const workflowRequestRulesCopy = this.workflowRequestRules[_status];
        rule = workflowRequestRulesCopy;
        if (rule && rule.Manage) {
          rule = this.checkOfferValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus, rule);
        }
      } else if (this.module !== "pluManagement") {
        _status = this.payload.info.offerStatus;
        _editStatus = this.payload.info.offerRequestEditStatus;

        if (offerProgramCode === CONSTANTS.SC) {
          rule = OFFER_WORKFLOW_RULES_SC[initialData.offerStatuses[_status]];
        } else if (offerProgramCode === CONSTANTS.GR || offerProgramCode === CONSTANTS.SPD) {
          rule = OFFER_WORKFLOW_RULES_GR_SPD[initialData.offerStatuses[_status]];
        } else if (offerProgramCode === CONSTANTS.BPD) {
          rule = OFFER_WORKFLOW_RULESBPD[initialData.offerStatuses[_status]];
        } else if (offerProgramCode === CONSTANTS.MF) {
          rule = OFFER_WORKFLOW_RULES_MF[initialData.offerStatuses[_status]];
        }

        if (_status === "P" && _editStatus && _editStatus.editStatus === "R") {
          rule = {
            Manage: [],
            Summary: [],
            Detail: [],
          };
        } else if (
          this.showPreviewAction &&
          this.payload &&
          this.payload.info &&
          this.payload.info.offerStatus === "DE" &&
          (!this.payload.info.podReferenceOfferId || this.payload.info.podReferenceOfferId === "NA")
        ) {
          if (this.action == "Detail" && this.podDetails && !rule[this.action].includes("Save")) {
            rule[this.action].push("Save");
          }
        } else if (
          _status === "CN" ||
          _status === "DE" ||
          (_status === "PU" && ((_editStatus && _editStatus.editStatus === "R") || this.payload.info.removedUnclippedOn))
        ) {
          rule = {
            Manage: [],
            Summary: [],
            Detail: [],
          };
        } else {
          if (this.isShowSaveCancel && this.action == "Detail" && this.podDetails && !rule[this.action].includes("Save")) {
            rule[this.action].push("Save");
          }
        }
      }

      if (rule) {
        const permissions = this._permissionsService.getPermissions(); //move it to rules, TO DO for SPD

        if (
          this.action == "Summary" &&
          _status === "Submitted" &&
          permissions[CONSTANTS.Permissions.Admin] &&
          [CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD].includes(this.facetItemService.programCodeSelected)
        ) {
          rule[this.action].push("Process");
        }

        let rules = [...rule[this.action]];
        this.removeFromRuleForPreview(rules);
        this.actionsAndMore = rules;
        this.removeEditForOR_InEditing();
        this.noOptionsForDropDown = false;
      } else {
        this.noOptionsForDropDown = true;
      }
    }
    if (!this.payload && this.module === "offerRequest" && this.action === "Detail") {
      this.actionsAndMore = ["Save"];
    }
    // Secure Actions dropdown and options inside it based on user permissions
    if (this.module === "offerRequest") {
      const programCode = this.payload && this.payload.info ? this.payload.info.programCode : this.facetItemService.programCodeSelected;
      const digitalStatus = this.payload?.info?.digitalStatus;
      this.secureOfferRequestActionsOptionsByUserPermissions(this.actionsAndMore, programCode, digitalStatus);
    } else if (this.module === "pluManagement") {
      this.securePluActionsOptionsByUserPermissions();
    } else {
      this.secureOfferActionsOptionsByUserPermissions(this.actionsAndMore, this.payload, this.podDetails);
    }
  }
  removeFromRuleForPreview(rules) {
    const { offerStatus } = this.payload.info;
    if (offerStatus !== "DE") {
      const index = rules.indexOf("Preview");
      if (index !== -1) {
        rules.splice(index, 1);
      }
    }
  }
  removeEditForOR_InEditing() {
    // In manage page, if th OR is in editing, remove the edit option
    const isReqInEditing = this.comomonService.isReqInEditing(this.payload);

    if (this.action === "Manage" && isReqInEditing) {
      let editIndex = this.actionsAndMore.indexOf("Edit");
      if (editIndex > -1) {
        this.actionsAndMore.splice(editIndex, 1);
      }
    }
  }

  updateStatusesIfAnyNotAvailable({digitalStatus, nonDigitalStatus}){
    if (!digitalStatus || digitalStatus == "NA") {
      digitalStatus = nonDigitalStatus;
    }
    if (!nonDigitalStatus || nonDigitalStatus == "NA") {
      nonDigitalStatus = digitalStatus;
    }

    return {digitalStatus, nonDigitalStatus};
  }

  getValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus) {
    let obj = this. updateStatusesIfAnyNotAvailable({digitalStatus, nonDigitalStatus});   
      
    let statuses = this.statusesWeightage;

    let statusForNonManagementPg = 
        this.finalStatusForNonManagementPg({nonDigitalStatus: obj.nonDigitalStatus,digitalStatus :  obj.digitalStatus });
           
    if(this.isExpiredStatus({status:statusForNonManagementPg})){
        return CONSTANTS.EXPIRED_STATUS_OR;
    } 
    const statusBasedOnEditStatus = this.getStatusBasedOnEditStatus({digitalEditStatus, nonDigitalEditStatus});
    if(statusBasedOnEditStatus){
      return statusBasedOnEditStatus;
    }  

    
    if (this.action === "Manage") {
      if (statuses[obj.nonDigitalStatus] <= statuses[obj.digitalStatus]) {
        return obj.nonDigitalStatus;
      } else {
        return obj.digitalStatus;
      }
    } else {    
        return statusForNonManagementPg;
    }
  }

  getStatusBasedOnEditStatus({digitalEditStatus, nonDigitalEditStatus}){
    if (digitalEditStatus && digitalEditStatus.editStatus === "E") {
      return digitalEditStatus.editStatus;
    }
    if (digitalEditStatus && digitalEditStatus.editStatus === "U") {
      return digitalEditStatus.editStatus;
    }
    if (nonDigitalEditStatus && nonDigitalEditStatus.editStatus === "E") {
      return nonDigitalEditStatus.editStatus;
    }
    if (nonDigitalEditStatus && nonDigitalEditStatus.editStatus === "U") {
      return nonDigitalEditStatus.editStatus;
    }
  }

  isExpiredStatus(obj){
    if(this.featureFlagsService.isOfferRequestArchivalEnabled || this.featureFlagsService.isOfferArchivalEnabled)
    {
      return this.commonSearchService.isShowExpiredInQuery;
    }
    else{
      //Normal Flow
      //If status is completed & end date is expired
      let {status, digitalStatus, nonDigitalStatus} = obj;
      ({digitalStatus, nonDigitalStatus} = this.updateStatusesIfAnyNotAvailable({digitalStatus, nonDigitalStatus}));
      
      if(!status){    
        status = this.finalStatusForNonManagementPg({nonDigitalStatus,digitalStatus });
      }

      const offerEffectiveEndDate = nullCheckProperty(this.payload, "rules.endDate.offerEffectiveEndDate"), 
      allowedPagesArr = ['Manage', "Summary", "Detail"], 
      allowedStatusesArr =  [CONSTANTS.EXPIRED_STATUS_OR, CONSTANTS.COMPLETED_STATUS_OR];
    
      return ( allowedPagesArr.includes(this.action)) && allowedStatusesArr.includes(status)  && 
              this._requestFormService.isDateExpired(offerEffectiveEndDate)
    }
  }

  finalStatusForNonManagementPg({nonDigitalStatus,digitalStatus }){  

    let statuses =  this.statusesWeightage;    

    if (statuses[nonDigitalStatus] >= statuses[digitalStatus]) {
      return nonDigitalStatus;
    } else {
      return digitalStatus;
    }
  }
  
  checkOfferValidStatus(digitalStatus, nonDigitalStatus, digitalEditStatus, nonDigitalEditStatus, rules) {
    if (this.action === "Manage" || this.action === "Summary" || this.action === "Detail") {
      if((this.featureFlagsService.isOfferRequestArchivalEnabled || this.featureFlagsService.isOfferArchivalEnabled) && this.commonSearchService.isShowExpiredInQuery && (this.action === "Manage") )
      {
        /*For Expired Status Only Copy is allowed */
          rules.Manage = [];
          rules.Manage.push("Copy");
          rules.Detail = [];
          rules.Detail.push("Copy");
      }
      else{
        if (["D", "P", "A", "C", "RU"].includes(digitalStatus) || ["D", "P", "A", "C"].includes(nonDigitalStatus)) {
          if (
            digitalStatus === "C" ||
            (digitalEditStatus && digitalEditStatus.editStatus === "R") ||
            nonDigitalStatus === "C" ||
            (nonDigitalEditStatus && nonDigitalEditStatus.editStatus === "R")
          ) {
            rules.Manage = [];
            rules.Manage.push("Copy");
            rules.Detail = [];
            rules.Detail.push("Copy");
          } else { 
            if(!(this.payload.createdApplicationId === "UPP" && this.featureFlagsService.isuppEnabled))
            {
              rules.Manage = this.arrayRemove(rules.Manage);
              rules.Manage.push("Cancel");

              rules = this.updateRulesForSummary({digitalStatus, nonDigitalStatus,rules});
              
              rules.Detail = this.arrayRemove(rules.Detail);
              rules.Detail.push("Cancel");          
            }
          }
        } else {
          this.updateRulesWithDeleteOption({rules, digitalStatus, nonDigitalStatus});        
        }
      }
    }
    return rules;
  }

  updateRulesWithDeleteOption({rules, digitalStatus, nonDigitalStatus}){
    
    if(this.isExpiredStatus({digitalStatus, nonDigitalStatus})){
      return false;
    }
    if(this.payload.createdApplicationId === "UPP" && this.featureFlagsService.isuppEnabled)
    {
      return false;
    } 
    rules.Manage = this.arrayRemove(rules.Manage);
    rules.Manage.push("Delete");
    rules.Summary = this.arrayRemove(rules.Summary);
    rules.Summary.push("Delete");
    rules.Detail = this.arrayRemove(rules.Detail);
    rules.Detail.push("Delete");
  }

  updateRulesForSummary({digitalStatus, nonDigitalStatus,rules}){
    //For Expired status, dont push

    let obj = this. updateStatusesIfAnyNotAvailable({digitalStatus, nonDigitalStatus});  

    if(!this.isExpiredStatus({digitalStatus: obj.digitalStatus, nonDigitalStatus : obj.nonDigitalStatus})){
      if(this.payload.createdApplicationId == 'UPP' && this.featureFlagsService.isuppEnabled)
        return rules;
      rules.Summary = this.arrayRemove(rules.Summary);
      rules.Summary.push("Cancel");
    }
    return rules;
  }

  arrayRemove(arr) {
    let retArr = [];
    const pCode = this.facetItemService.programCodeSelected;
    retArr = arr.filter((ele) => {
      return ele !== "Delete";
    });
    if (pCode !== CONSTANTS.BPD) {
      retArr = retArr.filter((ele) => {
        return ele !== "Cancel";
      });
    }
    return retArr;
  }
  regionsMultipleCopyModel() {
    this.openModal(this.regionsMultipleCopy, { keyboard: true, class: "modal-l" });
  }
  ngOnInit() {
    
    this._requestFormService.dontsaveVal$.subscribe((obj) => {
      this.dontsaveVal = obj;
    });
    if (!this._requestFormService.requestForm) {
      this._requestFormService.requestForm = this.fb.group({
        podSection: [],
        offerTypeSection: [],
        nopaSection: [],
      });
    }
    this.requestForm = this._requestFormService.requestForm;
    this.applyActionsAndMore();
    this._requestFormService.assignedModal$.subscribe((val) => {
      if (val && this.modalRef) {
        this.modalRef.hide();
      }
    });
  }

  openModal(template, options) {
    this.modalRef = this.modalService.show(template, options);
  }
  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this._authService.getTokenString(),
    };
  }
  deleteRequestapi() {
    const reqBody = this._requestFormService.subscribeCurrentOfferReq() || {
      id: this.payload.info.id,
      lastUpdatedTs: this.payload.lastUpdatedTs,
    };
    // submit the offer.
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this._authService.onUserDataAvailable(this.deleteOfferRequestApi.bind(this, reqBody));

    // making the form pristine as we need not show the save warning even if the from is dirty as the request is deleted
    if (this._requestFormService.requestForm) {
      this._requestFormService.requestForm.markAsPristine();
    }
    if (this.generalOfferTypeService.generalOfferTypeForm) {
      this.generalOfferTypeService.generalOfferTypeForm.markAsPristine();
    }
  }

  deployOfferApi(item) {
    const payload = {
      offerId: this.payload.info.id.externalOfferId,
      status: item === "Defer Deploy",
    };
    this._offerMappingService.offerObj = this.payload;
    this._offerMappingService.offerDeploy(payload);
  }
  deployPublishOfferApi(item) {
    const payload = {
      offerId: this.payload.info.id.externalOfferId,
      status: item === "Defer Deploy + Publish",
    };
    this._offerMappingService.offerObj = this.payload;
    this._offerMappingService.offerDeployPublish(payload);
  }

  cancelRequestapi() {
    if (this.payload) {
      if (this.payload.info.digitalStatus) {
        const startDate = this.payload.rules.startDate.offerEffectiveStartDate;
        const offerEffectiveDate = new Date(startDate);
        const today = new Date(Date.parse(Date()));
        if (today >= offerEffectiveDate) {
          const offers = this.payload.rules.qualificationAndBenefit.offerRequestOffers[0].offers;
          for (let offerIndex = 0; offerIndex < offers.length; offerIndex++) {
            if (offers[offerIndex].offerStatus === "PU") {
              this.isRemoveDecisionRequired = true;
              break;
            }
          }
        }
      } else {
        this.isRemoveDecisionRequired = false;
      }
      const reqBody = {
        id: this.payload.info.id,
        lastUpdatedTs: this.payload.lastUpdatedTs,
        isRemoveDecisionRequired: this.isRemoveDecisionRequired,
      };
      this._authService.onUserDataAvailable(this.cancelOfferRequestApi.bind(this, reqBody));
    }
  }

  sortObject(obj) {
    return Object.keys(obj)
      .sort()
      .reduce(function (acc, key) {
        acc[key] = obj[key];
        return acc;
      }, {});
  }

  cancelRequestapiGrSpdBpd() {
    if (this.payload && this.payload.info.digitalUiStatus) {
      this.isOfferCancelReasonRequired = false;
      let removeOption = null;
      let allowedStatuses = ["P", "E", "U"];
      if (this.offerProgramCode === "BPD") {
        allowedStatuses = ["P", "E", "U", "I", "S"];
      }

      if (allowedStatuses.includes(this.payload.info.digitalUiStatus)) {
        this.isOfferCancelReasonRequired = true;
      } else if (this.payload.info.digitalUiStatus === "RU") {
        this.isOfferCancelReasonRequired = true;
        removeOption = "removeAll";
      }

      if (this.payload.info.digitalStatus === "D") {
        this.isOfferCancelReasonRequired = true;
        const startDate = moment(mmDdYySlash_DateFormat(this.payload.rules.startDate.offerEffectiveStartDate)).valueOf();
        const todayDate = moment(mmDdYyyySlash_DateFormatWithoutUTC(new Date())).valueOf();
        if (startDate <= todayDate && this.payload.info.digitalUiStatus !== "RU") {
          this.hideRemoveOptions = false;
        }
      }
      if (this.isOfferCancelReasonRequired) {
        this.prepareGrSpdBpdCancelRequestForm(removeOption);
      }
      const reqBody = {
        id: this.payload.info.id,
        lastUpdatedTs: this.payload.lastUpdatedTs,
        isOfferCancelReasonRequired: this.isOfferCancelReasonRequired,
      };
      this._authService.onUserDataAvailable(this.cancelOfferRequestApiGrSpdBpd.bind(this, reqBody));
    }
  }
  private prepareGrSpdBpdCancelRequestForm(removeOption: any) {
    this.grSpdBpdCancelRequestForm = this.fb.group({
      changeReason: [null, [Validators.required]],
      changeType: [null, [Validators.required]],
      userChangeComment: [null],
      removeOption: [null, [this.hideRemoveOptions ? Validators.nullValidator : Validators.required]],
    });
    this.grSpdBpdCancelRequestFormSubmitted = false;
    this.grSpdBpdCancelRequestForm.reset();
    this.grSpdBpdCancelRequestForm.markAsPristine();
    if (removeOption) {
      this.grSpdBpdCancelRequestForm.patchValue({ removeOption });
    }
  }

  prepareGrSpdEditRequestReasonForm() {
    this.grSpdEditRequestForm = this.fb.group({
      editChangeReason: [null, [Validators.required]],
      editChangeType: [null, [Validators.required]],
      userEditChangeComment: [null],
    });
    this.grSpdEditRequestFormSubmitted = false;
    this.grSpdEditRequestForm.reset();
    this.grSpdEditRequestForm.markAsPristine();
  }

  onCloseClick() {
    this.modalRef.hide();
  }

  removeDescionsOfferBuilder() {
    if (this.payload) {
      const reqBody = {
        id: this.payload.info.id,
        lastUpdatedTs: this.payload.lastUpdatedTs,
        isRemoveDecisionRequired: this.isRemoveDecisionRequired,
        isDesicionSelected: true,
      };
      this._authService.onUserDataAvailable(this.cancelOfferRequestApi.bind(this, reqBody));
    }
  }

  removeDescionsOfferBuilderGrSpdBpd() {
    this.grSpdBpdCancelRequestFormSubmitted = true;
    if (this.grSpdBpdCancelRequestForm.invalid) return;
    if (this.payload) {
      const reqBody = {
        id: this.payload.info.id,
        lastUpdatedTs: this.payload.lastUpdatedTs,
        isOfferCancelReasonRequired: this.isOfferCancelReasonRequired,
        isOfferCancelReasonProvided: true,
      };
      this._authService.onUserDataAvailable(this.cancelOfferRequestApiGrSpdBpd.bind(this, reqBody));
    }
  }

  editRequestOfferBuilderGrSpd() {
    this.grSpdEditRequestFormSubmitted = true;
    if (this.grSpdEditRequestForm.invalid) return;
    if (this.payload) {
      this._authService.onUserDataAvailable(this.editReasonOfferRequestApiGrSpd.bind(this));
    }
  }

  onSelectionRemoveOffer(event) {
    const selectedValue = event.target.value;
    if (selectedValue && selectedValue === "removeUnclipped") {
      this.removeAll = false;
    } else {
      this.removeAll = true;
    }
  }

  deleteOfferRequestApi(reqBody) {
    this.subs.sink = this.deleteOfferRequest(reqBody).subscribe((response) => {
      // this._requestFormService.getReqDetails();
      // this.hasSubmitted = false;
      this._toastr.success("Offer Request deleted successfully", "", {
        timeOut: 3000,
        closeButton: true,
      });

      this.setQueryGenerator();

      this.type === "More"
        ? this._router.navigateByUrl(`/${ROUTES_CONST.REQUEST.Request}`)
        : this._searchOfferRequestService.searchOfferRequest(this.queryGenerator.getQuery(), true).subscribe((response: any) => {
            response.render = true;
            this._searchOfferRequestService.getOfferDetails(response);
          });
    });
  }
  public deleteOfferRequest(reqBody) {
    let url;
    let pc = this.facetItemService.programCodeSelected;
    switch (pc) {
      case CONSTANTS.GR: {
        url = this.requestDelete_API_GR;
        break;
      }
      case CONSTANTS.SPD: {
        url = this.requestDelete_API_GR.replace("/gr/", "/spd/");
        break;
      }
      case CONSTANTS.BPD: {
        url = this.requestDelete_API_GR.replace("/gr/", "/bpd/");
        break;
      }
      case CONSTANTS.SC: {
        url = this.requestDelete_API;
        break;
      }
    }
    let searchInput = { ...reqBody, reqObj: { headers: this.getHeaders() } };
    return this._http.put(url, searchInput);
  }

  setQueryGenerator() {
    this.queryGenerator.setQuery("");
    this.queryGenerator.setQueryWithFilter([]);
    let paramsList = [
      {
        remove: false,
        parameter: CONSTANTS.LIMIT,
        value: CONSTANTS.PAGE_LIMIT,
      },
      {
        remove: false,
        parameter: CONSTANTS.SORT_BY,
        value: `${CONSTANTS.LAST_MODIFY_DATE}${CONSTANTS.DESC}`,
      },
      {
        remove: false,
        parameter: CONSTANTS.FACET_FILTER.programCode.field,
        value: `(${this.facetItemService.programCodeSelected})`,
      },
    ];
    if(!this.featureFlagsService.isUPPFieldSearchEnabled)
    {
      paramsList.push({remove: false, parameter: CONSTANTS.CREATED_APP_ID, value: "OMS" });
    }
    if (this.facetItemService.programCodeSelected === 'BPD') {
      paramsList = [
          ...paramsList, 
          {
            remove: false,
            parameter: CONSTANTS.IS_OFFER_TEMPLATE,
            value: 'false',
          }
        ];
    }
    this.queryGenerator.pushParameters({ paramsList });
  }

  cancelOfferRequestApi(reqBody) {
    this.modalRef.hide();
    if (reqBody.isRemoveDecisionRequired && !reqBody.isDesicionSelected) {
      this.openModal(this.cancelRequestConfirm, { keyboard: true, class: "modal-m" });
    } else {
      const reqBodyCancel = {
        id: reqBody.id,
        lastUpdatedTs: reqBody.lastUpdatedTs,
        removeForAll: this.removeAll,
      };
      this.subs.sink = this.cancelOfferRequest(reqBodyCancel).subscribe((response) => {
        this._toastr.success("Offer Request canceled successfully", "", {
          timeOut: 3000,
          closeButton: true,
        });
        if (this.type === "Actions") {
          this._searchOfferRequestService.searchOfferRequest(this.queryGenerator.getQuery(), true).subscribe((response: any) => {
            response.render = true;
            this._searchOfferRequestService.getOfferDetails(response);
          });
        } else {
          this._requestFormService.makeSearchCall();
          this._historyService.getOROTHistoryPreviewById(Object.keys(response)[0]);
        }
        this.modalRef.hide();
      });
    }
  }
  cancelOfferRequestApiGrSpdBpd(reqBody) {
    this.modalRef.hide();
    if (reqBody.isOfferCancelReasonRequired && !reqBody.isOfferCancelReasonProvided) {
      this.openModal(this.cancelRequestConfirmGrSpdBpd, { keyboard: true, class: "modal-m" });
    } else {
      const { changeReason, changeType, userChangeComment, removeOption } = this.grSpdBpdCancelRequestForm.value;
      const reqBodyCancel = {
        id: reqBody.id,
        lastUpdatedTs: reqBody.lastUpdatedTs,
        changeReason,
        changeType,
        userChangeComment,
        removeForAll: removeOption == "removeAll",
      };
      if (this.grSpdBpdCancelRequestForm.value.removeOption == null) delete reqBodyCancel.removeForAll;

      let apiCaller = null;
      if (["I", "S"].includes(this.payload.info.digitalUiStatus)) {
        reqBodyCancel["delete"] = false;
        apiCaller = this.deleteOfferRequest(reqBodyCancel);
      } else {
        apiCaller = this.cancelOfferRequestGrSpdBpd(reqBodyCancel);
      }

      this.subs.sink = apiCaller.subscribe((response) => {
        this.hideRemoveOptions = true;
        if (this.type === "Actions") {
          this._toastr.success("Offer Request canceled successfully", "", {
            timeOut: 3000,
            closeButton: true,
          });
          this.setQueryGenerator();
          const queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
          this._searchOfferRequestService
            .searchOfferRequest(this.queryGenerator.getQuery(), true, queryWithOrFilters)
            .subscribe((response: any) => {
              response.render = true;
              this._searchOfferRequestService.getOfferDetails(response);
            });
        } else {
          this._requestFormService.isSavedFromNavigationOverlay = true;
          this._requestFormService.navigateToSummary("Offer Request canceled successfully", reqBody.id);
          // this._requestFormService.makeSearchCall();
          this._historyService.getOROTHistoryPreviewById(Object.keys(response)[0]);
        }
        this.modalRef.hide();
      });
    }
  }
  editReasonOfferRequestApiGrSpd() {
    this.modalRef.hide();
    {
      const { editChangeReason, editChangeType, userEditChangeComment } = this.grSpdEditRequestForm.value;

      if (this.getStatuses()) {
        this.setOfferRequestOffers(this.payload);
        this.payload.info.changeReason = editChangeReason;
        this.payload.info.changeType = editChangeType;
        this.payload.info.changeComments = userEditChangeComment;
        let sendData = this.payload;

        this._requestFormService.changeReasonData$.next({ editChangeReason, editChangeType, userEditChangeComment });

        this._requestFormService.requestData$.next(sendData);
        let reqBody = this._requestFormService.subscribeCurrentOfferReqForProcess();
        this._authService.onUserDataAvailable(this.editOfferRequestApi.bind(this, reqBody, this.payload.info.id));
      }
    }
  }
  public cancelOfferRequest(reqBody) {
    let searchInput = { ...reqBody, reqObj: { headers: this.getHeaders() } };
    return this._http.put(this.requestCancel_API, searchInput);
  }
  public cancelOfferRequestGrSpdBpd(reqBody) {
    let searchInput = { ...reqBody, reqObj: { headers: this.getHeaders() } };
    return this._http.put(`${this.requestCancel_API_GR_SPD_BPD}/${this.offerProgramCode.toLowerCase()}/cancel`, searchInput);
  }
  processRequestDigitalApi() {
    if (this.payload) {
      let reqBody = {
        id: this.payload.info.id,
        lastUpdatedTs: this.payload.lastUpdatedTs,
        trackType: "DG",
      };
      if (this.page === "Detail") {
        this._requestFormService?.offerRequestBaseService?.notifyValidationOnAction("process");
        if (
          !this._requestFormService?.offerRequestBaseService?.isFormValid() ||
          this._requestFormService?.offerRequestBaseService?.isScene7Invalid()
        ) {
          return;
        }
      }
      //process the offer.
      this._authService.onUserDataAvailable(this.processOfferRequestApi.bind(this, reqBody));
    }
  }
  processOfferRequestApi(reqBody) {
    let pC = this.facetItemService.programCodeSelected.toLowerCase();
    this.subs.sink = this.processRequest(reqBody, pC).subscribe((response) => {
      if (this.page === "Manage") {
        const queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
        this._searchOfferRequestService
          .searchOfferRequest(this.queryGenerator.getQuery(), true, queryWithOrFilters)
          .subscribe((response: any) => {
            response.render = true;
            this._searchOfferRequestService.getOfferDetails(response);
          });
      } else if (this.page === "Summary") {
        this._requestFormService.makeSearchCall();
        // Refresh History Preview to show the latest audit record related to processing
        this._historyService.getOROTHistoryPreviewById(reqBody.id);
      }
      this._toastr.success("Request processed successfully", "", {
        timeOut: 3000,
        closeButton: true,
      });
      if (this.page === "Detail") {
        this._requestFormService.isDisplayNavigationWarning = false;
        this.navigateToSummary(reqBody.id);
      }
    });
  }
  public processRequest(reqBody, programCode) {
    let searchInput = { ...reqBody, reqObj: { headers: this.getHeaders() } };
    return this._http.put(`${this.grSpdRequestProcess_API}/${programCode}/process`, searchInput);
  }

  navigateToSummary(id) {
    this._router.navigateByUrl(
      `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GRSummary}/${id}`
    );
  }

  submitRequestApi() {
    let isReqEditPage = false,
      url = window.location.href;
    if (url.includes("/" + ROUTES_CONST.REQUEST.Request + "/" + ROUTES_CONST.REQUEST.RequestForm + "/" + ROUTES_CONST.REQUEST.Edit)) {
      this.generalOfferTypeService.generalOfferTypeForm.markAsUntouched();
      this._requestFormService.isReqSubmitAttempted$.next(true);
      isReqEditPage = true;
    }

    if (isReqEditPage && !this.generalOfferTypeService.generalOfferTypeForm.valid) {
      scrollToErrorField();
    } else {
      this._requestFormService.copyingVar = false;
      const reqBody = {
        id: this.payload.info.id,
        lastUpdatedTs: this.payload.lastUpdatedTs,
        createdApplicationId: this.payload.createdApplicationId,
        createdTs: this.payload.createdTs,
        createdUserId: this.payload.createdUserId,
      };
      // submit the offer.
      this._authService.onUserDataAvailable(this.submitOfferRequestApi.bind(this, reqBody));
    }
  }
  removeOfferApi() {
    let externalOfferId = this.payload.info.id.externalOfferId;
    const requestRemoveBody = {
      externalOfferId: externalOfferId,
      removeForAll: true,
    };

    this._offerMappingService
      .offerRemoved(requestRemoveBody, externalOfferId, "Offer removed successfully")
      .then((response: any) => {
        this._toastr.success("Offer removed successfully", "", {
          timeOut: 3000,
          closeButton: true,
        });

        this._searchOfferRequestService.searchOffer(this.payload.info.id.externalOfferId).subscribe((response: any) => {
          response.render = true;
          this._searchOfferRequestService.getOfferDetails(response);
        });
      })
      .catch((msg) => {
        console.error(`${msg.status} - ${msg.statusText}`);
      });
  }
  submitOfferRequestApi(reqBody) {
    this._requestFormService.isReqSubmitAttempted$.next(false);
    this.subs.sink = this.submitOfferRequest(reqBody).subscribe((response) => {
      if (this.page === "Manage") {
        this._searchOfferRequestService.searchOfferRequest(this.queryGenerator.getQuery(), true).subscribe((response: any) => {
          response.render = true;
          this._searchOfferRequestService.getOfferDetails(response);
        });
      } else if (this.page === "Summary" || this.page === "Detail") {
        this._requestFormService.makeSearchCall();
      }

      // this.hasSubmitted = false;
      this._toastr.success("Offer Request submitted successfully", "", {
        timeOut: 3000,
        closeButton: true,
      });
    });
  }
  public submitOfferRequest(reqBody) {
    let searchInput = { ...reqBody, reqObj: { headers: this.getHeaders() } };
    return this._http.put(this.requestSubmit_API, searchInput);
  }
  getStatuses() {
    if (["I", "S"].includes(this.payload.info.digitalStatus) || ["I", "S"].includes(this.payload.info.nonDigitalStatus)) {
      return false;
    } else {
      return true;
    }
  }
  async editOfferRequestApi(reqBody, id) {
    this.subs.sink = await new Promise((resolve) => {
      this.editOfferRequest(reqBody).subscribe((response) => {
        // editing API in progress
        this._requestFormService.isEditNotificatonBoolean.next(true);

        let editSrc = ROUTES_CONST.REQUEST.Edit;
        const pCode = this._requestFormService.generalOfferTypeService.facetItemService.programCodeSelected;
        if (pCode) {
          editSrc = pCode === CONSTANTS.SC ? ROUTES_CONST.REQUEST.Edit : ROUTES_CONST.REQUEST[`${pCode}Edit`];
        }
        const editUrl = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${editSrc}/${this.payload.info.id}`;
        this._router.navigateByUrl(editUrl);
      });
    });
  }

  public editOfferRequest(reqBody) {
    let searchInput = { ...reqBody, reqObj: { headers: this.getHeaders() } };
    return this._http.put(this.requestEdit_API, searchInput);
  }
  setOfferRequestOffers(data) {
    let {
      rules: { qualificationAndBenefit },
    } = data;
    const pCode = this._requestFormService.generalOfferTypeService.facetItemService.programCodeSelected;
    let { offerRequestOffers } = qualificationAndBenefit;

    if (!offerRequestOffers && ([CONSTANTS.GR, CONSTANTS.SPD].includes(pCode) || this.commonRouteService.isBpdReqPage)) {
      qualificationAndBenefit.offerRequestOffers =
        qualificationAndBenefit[`${this._requestFormService.generalOfferTypeService.facetItemService.reqOffersObjkey}OfferRequestOffers`];
    }
  }

  actionHandlerForOr(){
    let editUrl;
    const digitalStatus = this.payload?.info?.digitalStatus;
    const pCode = this.facetItemService.programCodeSelected;

    if (
      ([CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD].includes(pCode) || this.commonRouteService.isBpdReqPage) &&
      ["D", "RU"].includes(digitalStatus)
    ) {
      this.prepareGrSpdEditRequestReasonForm();
      this.openModal(this.editRequestReasonModalGRSPD, { keyboard: true, class: "modal-m" });
      return;
    }

    if (this.getStatuses()) {
      // Edit call
      this.setOfferRequestOffers(this.payload);
      this._requestFormService.requestData$.next(this.payload);
      let reqBody = this._requestFormService.subscribeCurrentOfferReqForProcess();
      this._authService.onUserDataAvailable(this.editOfferRequestApi.bind(this, reqBody, this.payload.info.id));
    }
    let editSrc = ROUTES_CONST.REQUEST.Edit;
    if (pCode) {
      editSrc = pCode === CONSTANTS.SC ? ROUTES_CONST.REQUEST.Edit : ROUTES_CONST.REQUEST[`${pCode}Edit`];
    }
    editUrl = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${editSrc}/${this.payload.info.id}`;
    return editUrl;
  }

  clickAction(item) {
    this._requestFormService.requestDigitalStatus = null;
    this._requestFormService.requestNonDigitalStatus = null;

    if (item === "Assign") {
      this.requestForm.addControl("offerReqGroup", this.fb.group(this.setFormFields()));
      this.setOfferRequestOffers(this.payload);
      this._requestFormService.requestData$.next(this.payload);
      this._requestFormService.reqId = this.payload.info.id;

      this.openModal(this.assignBuilder, { keyboard: true, class: "modal-xl" });
      this._requestFormService.requestDigitalStatus = this.payload.info.digitalStatus;
      this._requestFormService.requestNonDigitalStatus = this.payload.info.nonDigitalStatus;
    } else if (item === "Edit") {
      let editUrl;
      if (this.module === "offerRequest") {
        editUrl = this.actionHandlerForOr();
        
      } else if (this.module === "offer") {
        editUrl = `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Edit}/${this.payload.info.id.externalOfferId}/${ROUTES_CONST.OFFERS.OfferDefinition}`;
      } else if (this.module === "pluManagement") {
        editUrl = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluDetails}/${ROUTES_CONST.REQUEST.Edit}/${this.payload.id}`;
      }
      this._router.navigateByUrl(editUrl);
    } else if (item === "Copy") {
      if (this.module === "offer") {
        return false;
      }
      this.copyOfferRequest(this.payload.info.id);
    } else if (item === "Delete" && this.module === "pluManagement") {
      this.openModal(this.pluDeleteConfirm, { keyboard: true, class: "modal-m" });
    } else if (item === "Delete") {
      this.openModal(this.deleteConfirm, { keyboard: true, class: "modal-m" });
    } else if (item === "Cancel") {
      this.openModal(this.cancelRequest, { keyboard: true, class: "modal-m" });
    } else if (item === "Remove") {
      this.removeOfferApi();
    } else if (item === "Submit") {
      this.submitRequestApi();
    } else if (item === "Save") {
      this.emitAction?.emit(item);
    } else if (item === "Preview") {
      this._offerMappingService.offerPreview(this.payload.info.id.externalOfferId);
    } else if (item === "Deploy Now" || item === "Defer Deploy") {
      this.deployOfferApi(item);
    } else if (item === "Deploy Now + Publish" || item === "Defer Deploy + Publish") {
      this.deployPublishOfferApi(item);
    } else if (item === "Process") {
      this.processRequestDigitalApi();
    } else if (item === "Multiple Copies") {
      this.regionsMultipleCopyModel();
    }
  }

  onNavigatedForClone() {
    const isPristine = this.requestForm.pristine;
    if (this.action === "Manage" || this.action === "Summary" || this.dontsaveVal) {
      this.executeSubs();
    }
    if (!isPristine || (!isPristine && this.action === "Detail" && !this.dontsaveVal)) {
      return false;
    }
    this.executeSubs();
  }

  executeSubs() {
    this._requestFormService.passClonedObject$.next(true);
    this._requestFormService.dontsaveVal$.next(false);
  }

  copyOfferRequest(requestId) {
    if (requestId) {
      const paramsList = [
        {
          remove: false,
          parameter: "requestId",
          value: requestId,
        },
      ];
      this.queryGenerator.setQuery("");
      this.queryGenerator.pushParameters({ paramsList });
      this._requestFormService.cloningProcess$.next(true);
      this._requestFormService.createdApplicationId = CONSTANTS.OMS;
      let createPath = this._requestFormService.getCreatePathFromPC();

      this._router
        .navigate([`${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${createPath}`])
        .then(this.onNavigatedForClone.bind(this));
    }
  }

  get isTemplateRouteActivated() {
    return this.commonRouteService.currentActivatedRoute?.includes("template");
  }

  secureOfferRequestActionsOptionsByUserPermissions(listOfActions, programCode, digitalStatus) {
    const permissions = this._permissionsService.getPermissions();
    this.filteredActionListByUserPermissions = [];

    // loop through list of actions and check if user has permissions for each of the actions
    if (listOfActions && listOfActions.length) {
      for (let i = 0; i < listOfActions.length; i++) {
        if (listOfActions[i] === "Assign") {
          if (permissions) {
            // Show Assign option only when user can "ASSIGN_DIGITAL_USERS" or "ASSIGN_NON_DIGITAL_USERS"
            if (permissions[CONSTANTS.Permissions.AssignDigitalUsers] || permissions[CONSTANTS.Permissions.AssignNonDigitalUsers]) {
              this.filteredActionListByUserPermissions.push(listOfActions[i]);
            }
          }
        } else if (listOfActions[i] === "Cancel") {
          if (permissions) {
            if (permissions[CONSTANTS.Permissions.CancelOfferRequest]) {
              this.filteredActionListByUserPermissions.push(listOfActions[i]);
            }
          }
        }else if(listOfActions[i] === "Copy"){
         if(this.hideActionBasedOnFeatureFlag(programCode)){
          this.filteredActionListByUserPermissions.push(listOfActions[i]);
         }
          
        } else {
          // Comes here for any option other than assign, cancel
          // If user has permissions to do offer requests, all the other options should be available
          if (programCode && permissions) {
            if (
              (programCode === CONSTANTS.SC && permissions[CONSTANTS.Permissions.DoStoreCouponsRequests]) ||
              //(programCode === "PD" && permissions[CONSTANTS.Permissions.EditGRSPDRequest]) ||
              (programCode === CONSTANTS.SPD && permissions[CONSTANTS.Permissions.EditGRSPDRequest]) ||
              (programCode === CONSTANTS.GR && permissions[CONSTANTS.Permissions.EditGRSPDRequest]) ||
              (programCode === CONSTANTS.MF && permissions[CONSTANTS.Permissions.DoMFOffers]) ||
              (programCode === CONSTANTS.BPD && permissions[CONSTANTS.Permissions.Admin])
            ) {
              /** For GR and SPD , after process Edit should not display in OR Actions if the user is not admin */
              if (
                [CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD].includes(programCode) &&
                this.action === "Manage" &&
                !permissions[CONSTANTS.Permissions.Admin]
              ) {
                this.secureEditForGR_SPD_After_Process(digitalStatus, listOfActions[i]);
              } else {
                this.filteredActionListByUserPermissions.push(listOfActions[i]);
              }
            }
          }
        }
      }
      if (this.filteredActionListByUserPermissions[1]) {
        this.filteredActionListByUserPermissions = [...new Set(this.filteredActionListByUserPermissions)];
      }
    }
    this.getDropDownClassName();
  }
  secureEditForGR_SPD_After_Process(status, action) {
    if (["P", "C", "D"].includes(status)) {
      if (action !== "Edit") {
        this.filteredActionListByUserPermissions.push(action);
      }
    } else {
      this.filteredActionListByUserPermissions.push(action);
    }
  }

  securePluActionsOptionsByUserPermissions() {
    const permissions = this._permissionsService.getPermissions();
    if (permissions[CONSTANTS.Permissions.ManagePluReservation]) {
      this.filteredActionListByUserPermissions = PLU_MANAGEMENT_RULES.General.Manage;
    }
    this.getDropDownClassName();
  }

  getDropDownClassName() {
    // Actions dropdown should be available only when it contains atleast one option.
    // If not based on authorizing strategy either disable or hide it
    if (this.filteredActionListByUserPermissions && this.filteredActionListByUserPermissions.length === 0) {
      let cssClassName;
      const appLevelAuthorizationStrategy = this._permissionsConfigurationService.getAllStrategies();
      if (appLevelAuthorizationStrategy.hasOwnProperty("disable")) {
        cssClassName = "disable";
      } else {
        cssClassName = "hide";
      }
      this.actionsDropDownCssBasedOnPermissions = cssClassName;
    } else {
      // This is to clear any previous css class when permissions are changed
      this.actionsDropDownCssBasedOnPermissions = "";
    }
  }

  secureOfferActionsOptionsByUserPermissions(listOfActions, offerData, isPODView) {
    const permissions = this._permissionsService.getPermissions();
    this.filteredActionListByUserPermissions = listOfActions;
    if (!isPODView) {
      if (offerData && offerData.info) {
        if (offerData.info.offerProviderName === "OMS Digital") {
          if (
            permissions[CONSTANTS.Permissions.DoAnyDigitalOffers] ||
            (permissions[CONSTANTS.Permissions.DoAssignedDigitalOffers] && this.loggedInUserId.toLowerCase() === offerData?.info?.assignment?.userId.toLowerCase())
          ) {
            this.actionsDropDownCssBasedOnPermissions = "";
          } else {
            this.actionsDropDownCssBasedOnPermissions = "disable";
          }
        } else if (offerData.info.offerProviderName === "OMS Non-Digital") {
          if (
            permissions[CONSTANTS.Permissions.DoAnyNonDigitalOffers] ||
            (permissions[CONSTANTS.Permissions.DoAssignedNonDigitalOffers] && this.loggedInUserId.toLowerCase() === offerData?.info?.assignment?.userId.toLowerCase())
          ) {
            this.actionsDropDownCssBasedOnPermissions = "";
          } else {
            this.actionsDropDownCssBasedOnPermissions = "disable";
          }
        } else if (offerData.info.offerProviderName === "Grocery Rewards") {
          if (permissions[CONSTANTS.Permissions.Admin]) {
            this.actionsDropDownCssBasedOnPermissions = "";
          } else {
            this.actionsDropDownCssBasedOnPermissions = "disable";
          }
        } else if ([CONSTANTS.COUPONSINC,CONSTANTS.COUPONSINCNAI,CONSTANTS.EED].includes(offerData.info.offerProviderName)) {
          if (permissions[CONSTANTS.Permissions.DoMFOffers]) {
            this.actionsDropDownCssBasedOnPermissions = "";
          } else {
            this.actionsDropDownCssBasedOnPermissions = "disable";
          }
        }
      }
    } else {
      if (permissions[CONSTANTS.Permissions.DoPodOffers]) {
        this.actionsDropDownCssBasedOnPermissions = "";
      } else {
        this.actionsDropDownCssBasedOnPermissions = "disable";
      }
    }
  }

  hideActionBasedOnFeatureFlag(programCode){
    const rule = this.rulesToHideActions[this.payload.info.offerRequestType];
    if(rule){
      return rule.programCodes.includes(programCode) && rule.flag;
    }
    return true;
  }
  ngOnDestroy() {
    this.subs.unsubscribe();
    this._requestFormService.resetOnDestroy();
  }
}

<section *ngIf="pageLevelErrorsArr && pageLevelErrorsArr[0] && !(serverOrConnectionError || isNonApiError)"
    class="border border-danger col ng-invalid c-pageLevelErrorWrap p-5 text-danger mb-4">
    <p class="font-weight-bold mb-0">Oops, something wasn't right.</p>
    <p class="mb-0">A problem has occured while submitting your data.</p>
    <ul class="ml-7 mb-0 errorWrap">
        <li *ngFor="let err of pageLevelErrorsArr">{{err}}</li>
    </ul>
</section>
<section *ngIf="serverOrConnectionError || isNonApiError"
    class="border border-danger col ng-invalid c-pageLevelErrorWrap p-5 text-danger mb-4">
    <p class="font-weight-bold mb-0">{{serverOrConnectionErrorMsg}}</p>
    <section *ngIf="pageLevelErrorsArr && pageLevelErrorsArr[0] ">
        <ul class="ml-7 mb-0 errorWrap">
            <li *ngFor="let err of pageLevelErrorsArr">{{err}}</li>
        </ul>
    </section>
</section>
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { ApiErrorsComponent } from './api-errors.component';
import { FormArray, UntypedFormGroup,UntypedFormControl } from '@angular/forms';
import {BehaviorSubject, of} from "rxjs"
import { ApiErrorsService } from '@appServices/common/api-errors.service';


describe('ApiErrorsComponent', () => {
  let component: ApiErrorsComponent;
  let fixture: ComponentFixture<ApiErrorsComponent>;
  beforeEach(() => {
    const apiErrorsServiceStub = () => ({
      apiErrors$: { subscribe: f => f({}) },
      bindComponentFunctionToService: () => ({}),
      getForm: () => ({}),
      isformSubmitAttempted$: { subscribe: f => f({}) }
    });
    const routerStub = () => ({ events: { subscribe: f => f({}) } });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [ApiErrorsComponent],
      providers: [
        { provide: ApiErrorsService, useFactory: apiErrorsServiceStub },
        { provide: Router, useFactory: routerStub }
      ]
    });
    fixture = TestBed.createComponent(ApiErrorsComponent);
    component = fixture.componentInstance;
  });
  it('can load instance', () => {
    expect(component).toBeTruthy();
  });
  it('controlsArrWithValidators defaults to: []', () => {
    expect(component.controlsArrWithValidators.length).toEqual(0);
  });
  it('serverOrConnectionError defaults to: false', () => {
    expect(component.serverOrConnectionError).toEqual(false);
  });
  it('controlNamesWithFieldErrosSetArr defaults to: []', () => {
    expect(component.controlNamesWithFieldErrosSetArr.length).toEqual(0);
  });
  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      spyOn(component, 'initSubscribes');
      component.ngOnInit();
      expect(component.initSubscribes).toHaveBeenCalled();
    });
  });
  // describe('initSubscribes', () => {
  //   it('makes expected calls', () => {      
  //     spyOn(component, 'destroyVariables');
  //     spyOn(component, 'handleFieldLevelErrors');
  //     component.initSubscribes();
  //     expect(component.destroyVariables).toHaveBeenCalled();
  //     expect(component.handleFieldLevelErrors).toHaveBeenCalled();
  //   });
  // });
  describe('getForms', () => {
    it('makes expected calls', () => {
      const apiErrorsServiceStub: ApiErrorsService = fixture.debugElement.injector.get(
        ApiErrorsService
      );
      spyOn(component, 'initFormSubmitAttemptSub');
      spyOn(apiErrorsServiceStub, 'getForm');
      component.getForms();
      expect(component.initFormSubmitAttemptSub).toHaveBeenCalled();
      expect(apiErrorsServiceStub.getForm).toHaveBeenCalled();
    });
  });
  describe('ngOnDestroy', () => {
    it('makes expected calls', () => {
      spyOn(component, 'destroyVariables');
      component.ngOnDestroy();
      expect(component.destroyVariables).toHaveBeenCalled();
    });
  });
  describe('handleFieldLevelErrors', () => {
    it('makes expected calls', () => {
      spyOn(component, 'getForms');
      spyOn(component, 'handleFieldErrors')
      component.handleFieldLevelErrors(["limit: incoorect"]);
      expect(component.handleFieldErrors).toHaveBeenCalled();
    });
  });
  describe('handleErrorsScenarios', () => {
    it('makes expected calls with 500 series errors', () => {
      component.handleErrorsScenarios({ status: 500, error: { errors: {}, message: "Error  && Test" } });
      expect(component.serverOrConnectionError).toEqual(true);
    });
    it('makes expected calls with 0 status errors', () => {
      component.handleErrorsScenarios({ status: 0 });
      expect(component.serverOrConnectionError).toEqual(true);
      expect(component.serverOrConnectionErrorMsg).toEqual('Check your internet connection and try again.');
    });
    it('makes expected calls with other status errors', () => {
      spyOn(component, 'handleFieldLevelErrors');
      component.handleErrorsScenarios({ status: 400, error: { errors: {}, message: "Error  && Test" } });
      expect(component.handleFieldLevelErrors).toHaveBeenCalled();
    });
  });
  describe("initFormSubmitAttemptSub",()=>{
    it("when submitSub is true",()=>{
      component.submitSub=true
      let res=component.initFormSubmitAttemptSub()
      expect(res).toEqual(false)
    });
    it("when submitSub is false",()=>{
      component.submitSub=false
      component.controlsArrWithValidators=[new UntypedFormGroup({name:new UntypedFormControl("")})]
      component.initFormSubmitAttemptSub()
      expect(component.controlsArrWithValidators[0].controls.name.value).toEqual("")
    });
  });
  describe("setAsPageLevelError",()=>{
    it("should return array",()=>{
      component.pageLevelErrorsArr=false;
      component.setAsPageLevelError("")
      expect(component.pageLevelErrorsArr).toEqual([''])
    })
    it("should return false",()=>{
      let errMsg=""
      component.pageLevelErrorsArr=[errMsg]
      let res=component.setAsPageLevelError("")
      expect(res).toEqual(false)
    });
  });
  describe("displayNonApiErrors",()=>{
    it("if obj is not exist",()=>{
      let res=component.displayNonApiErrors(null)
      expect(res).toEqual(false)
    });
    it("if obj is exist",()=>{
      let obj={message:""}
      let spy=spyOn(component,"setAsPageLevelError").and.returnValue(true)
     component.displayNonApiErrors(obj)
     expect(spy).toHaveBeenCalled()
    });
  });
  describe("initSubscribes",()=>{
    it("make expected calls",()=>{
      const apiErrorsServiceStub: ApiErrorsService = fixture.debugElement.injector.get(ApiErrorsService);
      apiErrorsServiceStub.apiErrors$=new BehaviorSubject("")
      let spy=spyOn(component,"destroyVariables")
      component.initSubscribes()
      expect(spy).toHaveBeenCalled()
    });
  });
  describe("getFieldLookupDetails",()=>{
    it("make expected calls",()=>{
      component.formsArr =[{name:new UntypedFormControl("name")}]
      let res=component.getFieldLookupDetails("name")
      expect(res.matchingControlArr).toEqual([])
    });
  });
  describe("setNonRenderedFieldErrorsAsPgErrors",()=>{
    it("make expected calls",()=>{
      let obj={controlName:"name",errorMsg:"msg"}
      let spy=spyOn(component,"setAsPageLevelError").and.returnValue(true)
      component.setNonRenderedFieldErrorsAsPgErrors(obj)
      expect(spy).toHaveBeenCalled()
    });
  });
  describe("handleFieldErrors",()=>{
    it("make expected calls",()=>{
      let errorObj={controlName:"name",errorMsg:"msg"}
      let spy=spyOn(component,"getFieldLookupDetails").and.returnValue({matchingControlArr:[],formsArrWithMatchedControls:[]})
      component.handleFieldErrors(errorObj)
      expect(spy).toHaveBeenCalled()
    });
  });

  

});
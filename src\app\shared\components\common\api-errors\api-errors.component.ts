/* 
   Purpose: This component is repsonsible for displaying page / field level level errors
   Integration: 
   1. Include this component on a page wherever the page level errors needs to be displayed. 

   <small class="text-danger" *ngIf="err && err.apiError as apiError" apiErrorCtrl = "receiptText">{{apiError}}</small>
*/

import { Component, OnDestroy, OnInit } from "@angular/core";
import { ApiErrorsService } from "@appServices/common/api-errors.service";
import { getControlByName } from "@appUtilities/getControlByName";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

import { UntypedFormControl } from "@angular/forms";
import { NavigationStart, Router } from "@angular/router";
import { scrollToErrorField } from "@appUtilities/scrollToError";
import { of } from "rxjs";

@Component({
  selector: "api-errors",
  templateUrl: "api-errors.component.html",
  styleUrls: ["api-errors.component.scss"],
})
export class ApiErrorsComponent extends UnsubscribeAdapter implements OnInit, OnDestroy {
  pageLevelErrorsArr: any;
  formsArr: any;
  serverOrConnectionError: boolean = false;
  serverOrConnectionErrorMsg: any = ''
  controlsArrWithValidators = []; //Stores all the controls for which API validators were set. So that they can be cleared when required
  submitSub: any;
  controlNamesWithFieldErrosSetArr = [];
  isNonApiError: boolean;
  constructor(private apiErrorsService: ApiErrorsService, private router: Router) {
    super();
  }

  ngOnInit() {
    this.apiErrorsService.bindComponentFunctionToService(this.displayNonApiErrors.bind(this));
    this.initSubscribes();
  }

  /*   returnMockErrorData() {
      return {
        timestamp: "2020-04-24T04:54:33.967Z",
        status: "BAD_REQUEST",
        message: "dfder erere ererferer && dfdfer ecer",
        errors: [
          "productGroupName: start date cannot start after end date.",
          "offerEffectiveStartDate: start date cannot start after end date.",
        ],
      };
    } */
  initSubscribes() {
    this.subs.sink = this.router.events.subscribe((event) => {
      //Destroy the variables on route change
      if (event instanceof NavigationStart) {
        this.destroyVariables();
      }
    });
    this.subs.sink = this.apiErrorsService.apiErrors$.subscribe((data: any) => {
      //Reset if subsequent call is successfull
      if (!data) {
        this.destroyVariables();
        return false;
      }
      this.handleErrorsScenarios(data)
      setTimeout(() => {
        scrollToErrorField();
      }, 0);
    });
  }

  displayNonApiErrors(obj) {
    if (!obj) {
      this.destroyVariables();
      return false;
    }
    const { message } = obj;
    this.isNonApiError = true;
    this.setAsPageLevelError(message);
    setTimeout(() => {
      scrollToErrorField();
    }, 0);
  }

  handleErrorsScenarios(data) {
    let errStatus = data && data.status.toString();
    if (errStatus.startsWith('5')) {
      this.serverOrConnectionError = true;
      this.serverOrConnectionErrorMsg = 'The service is temporarily unavailable. Please try again later.';

      ({ message: this.pageLevelErrorsArr } = data.error);

      if (this.pageLevelErrorsArr) {
        this.pageLevelErrorsArr = this.pageLevelErrorsArr.split("&&");
      }
    } else if (errStatus === '0') {
      this.serverOrConnectionError = true;
      this.serverOrConnectionErrorMsg = 'Check your internet connection and try again.';
    } else {
      this.serverOrConnectionError = false;
      let fieldLevelErrorsArr;
      ({ errors: fieldLevelErrorsArr, message: this.pageLevelErrorsArr } = data.error);

      if (this.pageLevelErrorsArr) {
        this.pageLevelErrorsArr = this.pageLevelErrorsArr.split("&&");
      }

      if (fieldLevelErrorsArr) {
        this.handleFieldLevelErrors(fieldLevelErrorsArr);
      }
    }
  }
  handleFieldLevelErrors(fieldLevelErrorsArr) {
    //Iterate through each fieldlevel error
    fieldLevelErrorsArr.forEach((error) => {
        let errArr = error?.split(":"),
        errorObj = {
          controlName: errArr && errArr[0],
          errorMsg: errArr && errArr[1],
        };
        if(!errorObj?.controlName?.includes('warning')){
        this.formsArr = this.getForms();
        this.handleFieldErrors(errorObj);
        }
    });
  }

  getFieldLookupDetails(controlName) {
    let matchingControlArr = [],
      formsArrWithMatchedControls = [];

    this.formsArr && this.formsArr.forEach((form) => {
      let matchedControlsArr = getControlByName({ rootControl: form, controlName });
      if (matchedControlsArr[0]) {
        matchingControlArr.push(...matchedControlsArr);
        formsArrWithMatchedControls.push(form);
      }
    });
    return { matchingControlArr, formsArrWithMatchedControls };
  }

  handleFieldErrors(errorObj) {
    const { controlName, errorMsg } = errorObj;

    let { matchingControlArr, formsArrWithMatchedControls } = this.getFieldLookupDetails(controlName);

    const firstControl = matchingControlArr[0];

    //If single field occurence is there set as field level error, else page level error
    if (firstControl && !matchingControlArr[1]) {
      this.setFieldLevelError({ firstControl, errorMsg, formsArrWithMatchedControls });
      setTimeout(() => { this.setNonRenderedFieldErrorsAsPgErrors({ controlName, errorMsg }) }, 0);
    } else {
      //If control is not available, add it to page level errors object.
      this.setAsPageLevelError(errorMsg);
    }
  }

  setNonRenderedFieldErrorsAsPgErrors(obj) {
    const { controlName, errorMsg } = obj;
    const htmlElemArr = document.querySelectorAll('[apiErrorCtrl="' + controlName + '"]');
    if (!htmlElemArr[0]) {
      this.setAsPageLevelError(errorMsg);
    }
  }

  setFieldLevelError(obj) {
    const { firstControl, errorMsg, formsArrWithMatchedControls } = obj;
    firstControl.setAsyncValidators([this.apiErrorValidaton({ apiError: errorMsg })]);
    formsArrWithMatchedControls.forEach((form) => {
      form.markAsDirty();
    });
    firstControl.updateValueAndValidity();
    this.controlsArrWithValidators.push(firstControl);
  }

  setAsPageLevelError(errorMsg) {
    if (!this.pageLevelErrorsArr) {
      this.pageLevelErrorsArr = [];
    }else if(this.pageLevelErrorsArr.indexOf(errorMsg) > -1){
      return false;
    }
    
    this.pageLevelErrorsArr.push(errorMsg);
  }

  apiErrorValidaton(obj) {
    return (c: UntypedFormControl) => {
      return of(obj);
    };
  }

  getForms() {
    const formsArr = this.apiErrorsService.getForm();

    this.initFormSubmitAttemptSub();
    return formsArr;
  }
  initFormSubmitAttemptSub() {
    if (this.submitSub) {
      return false;
    }
    this.submitSub = this.apiErrorsService.isformSubmitAttempted$?.subscribe((value) => {
      if (!value) {
        return false;
      }
      this.controlsArrWithValidators.forEach((control) => {
        control.clearAsyncValidators();
        control.updateValueAndValidity();
      });
      //this.submitSub.unsubscribe();
    });
  }
  destroyVariables() {
    this.serverOrConnectionError = false;
    this.isNonApiError = false;
    this.serverOrConnectionErrorMsg = '';
    this.pageLevelErrorsArr = null;
    this.controlsArrWithValidators = [];
  }

  ngOnDestroy() {
    this.destroyVariables();
  }
}

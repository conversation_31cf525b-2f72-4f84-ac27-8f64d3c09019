<span class="align-items-center- pagination justify-content-end font-weight-bold">
    <span *ngIf="checkCountPrev()" (click)=" pageChanged(this.paginateConfig.currentPage-1)"><span
            class="cursor-pointer mr-2"><img src="assets/icons/arrow-icon-left.svg" height="18"
                alt=""></span></span>
    <span *ngIf="dataList?.length">{{startValue}} – {{lastValue}} of
        {{dataList?.length}}</span>
    <span *ngIf="checkCountNext()" (click)=" pageChanged(this.paginateConfig.currentPage+1)"><span
            class="cursor-pointer ml-2"><img src="assets/icons/arrow-icon-right.svg" height="18"
                alt=""></span>
    </span>
</span>
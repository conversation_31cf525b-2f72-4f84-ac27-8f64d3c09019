import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ClientSidePaginationComponent } from './client-side-pagination.component';

describe('ClientSidePaginationComponent', () => {
    let component: ClientSidePaginationComponent;
    let fixture: ComponentFixture<ClientSidePaginationComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ClientSidePaginationComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ClientSidePaginationComponent);
        component = fixture.componentInstance;
        component.paginateConfig = { currentPage: 1, itemsPerPage: 10 };
        component.dataList = Array(100).fill({});
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should calculate startValue correctly', () => {
        expect(component.startValue).toBe(1);
        component.paginateConfig.currentPage = 2;
        expect(component.startValue).toBe(11);
    });

    it('should calculate lastValue correctly', () => {
        expect(component.lastValue).toBe(10);
        component.paginateConfig.currentPage = 2;
        expect(component.lastValue).toBe(20);
        component.paginateConfig.currentPage = 10;
        expect(component.lastValue).toBe(100);
    });

    it('should return empty string for lastValue if no dataList', () => {
        component.dataList = null;
        expect(component.lastValue).toBe('');
    });

    it('should check if next page is available', () => {
        component.paginateConfig.currentPage = 10;
        expect(component.checkCountNext()).toBeFalse();
    });

    it('should check if previous page is available', () => {
        expect(component.checkCountPrev()).toBeFalse();
        component.paginateConfig.currentPage = 2;
    });

    it('should change page and scroll to top', () => {
        spyOn(window, 'scrollTo');
        component.pageChanged(2);
        expect(component.paginateConfig.currentPage).toBe(2);
        expect(window.scrollTo).toHaveBeenCalledWith(0, document.body.scrollTop);
    });
});
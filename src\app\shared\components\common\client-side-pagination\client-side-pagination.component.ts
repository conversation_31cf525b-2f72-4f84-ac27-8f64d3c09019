import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-client-side-pagination',
  templateUrl: './client-side-pagination.component.html'
})
export class ClientSidePaginationComponent implements OnInit {
  @Input("dataList") dataList;
  @Input("paginateConfig") paginateConfig;

  constructor() {
    // intentionally left empty
   }

  ngOnInit(): void {
    // intentionally left empty
  }

  get startValue() {
    const { currentPage, itemsPerPage } = this.paginateConfig;
    if (currentPage && itemsPerPage) {
      return (currentPage - 1) * itemsPerPage + 1;
    }
  }
  get lastValue() {
    const { currentPage, itemsPerPage } = this.paginateConfig,
      totalResults = this.dataList?.length;
    if (currentPage && itemsPerPage && totalResults) {
      return (currentPage * itemsPerPage > totalResults ? totalResults : currentPage * itemsPerPage)
    }
    return '';
  }

  checkCountNext() {
    return this.dataList?.length !== this.lastValue && this.dataList?.length;
  }
  checkCountPrev() {
    return parseInt(this.paginateConfig.currentPage, 10) !== 1 && this.paginateConfig.currentPage;
  }

  pageChanged(event) {
    window.scrollTo(0, document.body.scrollTop);
    this.paginateConfig.currentPage = event;
  }

}

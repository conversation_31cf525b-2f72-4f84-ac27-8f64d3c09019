<div class="d-flex justify-content-between custom-pagination">
    <div class="font-weight-bold">
      <label>Items per page:
        <select class="text-center" (change)="setItemsPerPage(page.value)" #page>
            <option *ngFor="let item of itemPerPageList" [selected] = "setDefaultOption(item)">{{item}}</option>
        </select>
      </label>
  </div>
    <pagination-controls  autoHide="true" responsive="true" previousLabel="Prev" (pageChange)="pageChanged($event)"></pagination-controls>
  </div>
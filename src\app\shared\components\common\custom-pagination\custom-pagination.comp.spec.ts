import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomPaginationComponent } from './custom-pagination.comp';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

describe('CustomPaginationComponent', () => {
    let component: CustomPaginationComponent;
    let fixture: ComponentFixture<CustomPaginationComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [CustomPaginationComponent]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(CustomPaginationComponent);
        component = fixture.componentInstance;
        component.dataList = Array(100).fill({});
        component.paginateConfig = { currentPage: 1, itemsPerPage: 10 };
        component.itemPerPageList = [10, 20, 30];
        component.defaultItemPerPage = 10;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should emit displayValue on init', () => {
        spyOn(component.displayItemText, 'emit');
        component.ngOnInit();
        expect(component.displayItemText.emit).toHaveBeenCalledWith(component.displayValue);
    });

    it('should calculate startValue correctly', () => {
        expect(component.startValue).toBe(1);
        component.paginateConfig.currentPage = 2;
        expect(component.startValue).toBe(11);
    });

    it('should calculate lastValue correctly', () => {
        expect(component.lastValue).toBe(10);
        component.paginateConfig.currentPage = 2;
        expect(component.lastValue).toBe(20);
    });

    it('should return displayValue correctly', () => {
        expect(component.displayValue).toBe('(1 - 10) of 100');
        component.paginateConfig.currentPage = 2;
        expect(component.displayValue).toBe('(11 - 20) of 100');
    });

    it('should set items per page and emit displayValue', () => {
        spyOn(component.displayItemText, 'emit');
        component.setItemsPerPage(20);
        expect(component.paginateConfig.itemsPerPage).toBe(20);
        expect(component.paginateConfig.currentPage).toBe(1);
        expect(component.displayItemText.emit).toHaveBeenCalledWith(component.displayValue);
    });

    it('should change page and emit displayValue', () => {
        spyOn(component.displayItemText, 'emit');
        component.pageChanged(2);
        expect(component.paginateConfig.currentPage).toBe(2);
        expect(component.displayItemText.emit).toHaveBeenCalledWith(component.displayValue);
    });

    it('should set default option correctly', () => {
        expect(component.setDefaultOption(10)).toBeTrue();
        expect(component.setDefaultOption(20)).toBeFalse();
    });

    it('should return lastValue as totalResults if currentPage * itemsPerPage exceeds totalResults', () => {
        component.dataList = Array(15).fill({});
        component.paginateConfig = { currentPage: 2, itemsPerPage: 10 };
        fixture.detectChanges();
        expect(component.lastValue).toBe(15);
    });

    it('should return empty string if currentPage or itemsPerPage is not defined', () => {
        component.paginateConfig = { currentPage: undefined, itemsPerPage: 10 };
        fixture.detectChanges();
        expect(component.lastValue).toBe('');
        
        component.paginateConfig = { currentPage: 1, itemsPerPage: undefined };
        fixture.detectChanges();
        expect(component.lastValue).toBe('');
    });
});
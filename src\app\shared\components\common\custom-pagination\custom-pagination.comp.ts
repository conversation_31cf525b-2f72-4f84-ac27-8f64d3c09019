import { Component, EventEmitter, Input, Output } from "@angular/core";

@Component({
    selector: "custom-pagination",
    templateUrl: "./custom-pagination.comp.html"
})
export class CustomPaginationComponent {
    @Input("dataList") dataList;
    @Input("paginateConfig") paginateConfig;
    @Input("itemPerPageList") itemPerPageList;
    @Input("defaultItemPerPage") defaultItemPerPage;
    @Output() displayItemText = new EventEmitter<string>();

    ngOnInit(): void {
        this.displayItemText.emit(this.displayValue);
    }
    get startValue() {
        const { currentPage, itemsPerPage } = this.paginateConfig;
        if (currentPage && itemsPerPage) {
            return (currentPage - 1) * itemsPerPage + 1;
        }
    }
    get lastValue() {
        const { currentPage, itemsPerPage } = this.paginateConfig,
            totalResults = this.dataList?.length;
        if (currentPage && itemsPerPage && totalResults) {
            return (currentPage * itemsPerPage > totalResults ? totalResults : currentPage * itemsPerPage)
        }
        return '';
    }
    get displayValue() {
        if (this.startValue && this.lastValue && this.dataList?.length) {
            return `(${this.startValue} - ${this.lastValue}) of ${this.dataList?.length}`
        }
    }
    public setItemsPerPage(event) {
        this.paginateConfig.itemsPerPage = event;
        this.paginateConfig.currentPage = 1;
        this.displayItemText.emit(this.displayValue);
    }
    pageChanged(event) {
        this.paginateConfig.currentPage = event;
        this.displayItemText.emit(this.displayValue);
    }
    setDefaultOption(item) {
        return this.defaultItemPerPage == item;
    }
}
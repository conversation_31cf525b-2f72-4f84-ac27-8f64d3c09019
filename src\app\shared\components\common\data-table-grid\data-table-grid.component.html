<div *ngIf="rowsData" [class.bpg]= "isBaseProductGroup">
  <h6>{{ tableHeader }}</h6>

  <ngx-datatable [messages]="{emptyMessage: ''}" [scrollbarV]="false" [reorderable]="reorderable" [rowHeight]="'auto'"  style="width: 100%" class="upc-expansion-grid"
    [rows]="rowsData" [columnMode]="'force'" [headerHeight]="30" [footerHeight]="30" [class]="grid" [ngClass]="{'adjustRadio': setClassForRadio, 'nonBaseClass' : setClassForNonBase}"
    [limit]="setLimitForPagination()"

    [rowClass]="getRowClass">
    <ngx-datatable-column *ngIf="showCheck" [width]="40" [sortable]="false" [canAutoResize]="false" [draggable]="false"
      [resizeable]="false">
      <ng-template  ngx-datatable-header-template let-value="value" let-allRowsSelected="allRowsSelected"
        let-selectFn="selectFn">
        <label *ngIf="isDisplayBulkSelection" class="custom-checkbox-label m-0 ">
          <input type="checkbox" [checked]="allRowSelected" (change)="onHeaderSelect($event)" />
        </label>
      </ng-template>
      <ng-template ngx-datatable-cell-template let-row="row" let-rowIndex="rowIndex" let-value="value"
        let-isSelected="isSelected" let-onCheckboxChangeFn="onCheckboxChangeFn">
        <span [ngSwitch]="grid">
          <label *ngSwitchCase="'departmentDataId'" class="custom-checkbox-label m-0">
            <input type="checkbox" [checked]="row['departmentDataId'].insSelected"
              (change)="onRowSelect($event, rowIndex, row)" />
          </label>
          <label *ngSwitchCase="'manufacturerDataId'" class="custom-checkbox-label m-0">
            <input type="checkbox" [checked]="row['manufacturerDataId'].isSelected"
              (change)="onRowSelect($event, rowIndex, row)" />
          </label>

          <label *ngSwitchCase="'upcExpansionDataId'" class="custom-checkbox-label m-0">
            <input type="checkbox" [checked]="row['upcExpansionDataId'].isSelected"
              (change)="onRowSelect($event, rowIndex, row)" />
          </label>
          <label *ngSwitchCase="'uploadedExpansion'" class="custom-checkbox-label m-0">
            <input type="checkbox" [checked]="row['uploadedExpansion'].isSelected"
              (change)="onRowSelect($event, rowIndex, row)" />
          </label>
          <label *ngSwitchCase="'suggestedExpansion'" class="custom-checkbox-label m-0 position-absolute">
            <input type="checkbox" [ngClass]="{'disable-check': disableIfMob(row)}" [attr.disabled]="disableIfMob(row)" [checked]="row['suggestedExpansion']?.isSelected"
              (change)="onRowSelect($event, rowIndex, row)" />
          </label>
        </span>
      </ng-template>
    </ngx-datatable-column>
    <ngx-datatable-column *ngIf="showRadio" [width]="40" [sortable]="false" [canAutoResize]="false" [draggable]="false"
      [resizeable]="false">
      <ng-template ngx-datatable-cell-template let-row="row" let-rowIndex="rowIndex" let-value="value"
        let-isSelected="isSelected" let-onCheckboxChangeFn="onCheckboxChangeFn">
        <span [ngSwitch]="grid" *ngIf="row.origin === 'Suggested'">
          <label *ngSwitchCase="'upcExpansionDataId'" class="custom-checkbox-label m-0 position-absolute">
            <input type="radio" name="originalUpc" (change)="onRadioSelected($event, rowIndex, row)" />
          </label>
        </span>
      </ng-template>
    </ngx-datatable-column>
    <ngx-datatable-column [name]="item === 'rank' ? 'rank' : null" [prop]="item === 'rank' ? 'rank' : null"  
     *ngFor="let item of dataColumns" [sortable]="isSortable(item)" >
      <ng-template let-column="column" ngx-datatable-header-template>
        {{ dataColumn[item] }} 
      </ng-template>
      <ng-template let-row="row" ngx-datatable-cell-template>
        <div container="body" [class.line-clamp] = "isLineClamp(item)" [class]= "item" [ngClass]="{ 'info-tooltip': toShowTooltipClass({dataColumn:dataColumn, row:row, item:item})}">
          <a *ngIf="item === 'mobId' && grid === 'suggestedExpansion'" href="javascript:void(0)" (click)="openPGofMob(row,item)" [ngClass]="{ 'comments-newline': item === 'comments' }">{{ dataValue({row:row, item:item})}}</a>
        <span *ngIf="!(item === 'mobId' && grid === 'suggestedExpansion')" [ngClass]="{ 'comments-newline': item === 'comments' }">{{ dataValue({row:row, item:item})}}</span>
        <span *ngIf="toShowTooltipClass({dataColumn:dataColumn, row:row, item:item})" class="tooltiptext" [ngClass]="{ 'tt-comments': item === 'comments' }">{{ getTitle({dataColumn:dataColumn, item:item,row:row }) }}</span>
    </div>
      </ng-template>
    </ngx-datatable-column>
  
    <ngx-datatable-column *ngIf="rowAddRemoveBtnEnable !== ''" [sortable]="false">
      <ng-template let-column="column" ngx-datatable-header-template>
      </ng-template>
      <ng-template let-row="row" let-rowIndex="rowIndex" ngx-datatable-cell-template>
        <div class="row col m-0 form-group removeIconWrapper">
          <div class="col-12 text-right mt-1 removeIconContainer">
            <span *permissionsOnly="manageProductGroups">
              <a *ngIf="rowAddRemoveBtnEnable === 'Add'" href="javascript:void(0)" (click)="addRow(row)"
                class="add-new">
                <span>
                  <img src="assets/icons/Add.svg" alt="">
                </span>
              </a>
            </span>
            <span *permissionsOnly="manageProductGroups">
              <div *ngIf="row.hasError" class="error-tooltip">
                {{ row.errorMsg }}
              </div>
              <span *ngIf="row.hasError" class="error-icon">
                <img src="assets/icons/warning-red.svg" alt="">
              </span>
              <span *ngIf="row.hasError" class="error-desc-expand"></span>
              <a *ngIf="rowAddRemoveBtnEnable === 'Remove' && !hideRemoveBtn(row)" href="javascript:void(0)" (click)="removeRow(row, rowIndex)"
                [ngClass]="{ 'remove': grid !== 'originalExpansion' }">
                <span *ngIf="grid === 'originalExpansion' && row.status !== 'Dropped'" >
                  <img src="assets/icons/Add.svg" alt="">
                </span>
              </a>
            </span>
          </div>
        </div>
      </ng-template>
    </ngx-datatable-column>
  </ngx-datatable>
</div>
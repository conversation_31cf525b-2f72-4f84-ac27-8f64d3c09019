@import "../../../../../scss/variables";
@import "../../../../../scss/safeway-material.scss";
@import "./data-table-grid_bpg.component.scss";

.upc-expansion-grid.ngx-datatable {

  font-size: 12px;

  .datatable-header {
    padding: 5px 5px 5px 15px;
    background: #f0f4f7;

    .datatable-header-inner {
      font-size: $base-font-size;
      font-weight: bold;
    }
  }
  .datatable-body {
    max-height: 600px !important;
    overflow: auto;
    overflow-x: hidden;
    .datatable-body-row {
      padding: 8px 8px 8px 15px;
      border: 1px solid #dedede;
      border-top: none;

      .rank {
        cursor: pointer;
      }
    }
  }
  .datatable-footer {
    overflow: hidden;
    .page-count {
      display: none;
    }
  }
  input[type="checkbox"] {
    height: 18px;
    width: 18px;
  }
  input[type="checkbox"]:checked {
    bottom: 0;
    &:before {
      font-size: 13px;
    }
    &:checked {
      bottom: 0;
    }
  }
}

.upcExpansionDataId.nonBaseClass {
    .datatable-row-group,
    .datatable-row-center {

        .datatable-header-cell:nth-child(1),
        .datatable-body-cell:nth-child(1) {
            width: 73px !important;
        }

        .datatable-header-cell:nth-child(2),
        .datatable-body-cell:nth-child(2) {
            width: 92px !important;
        }

        .datatable-header-cell:nth-child(3),
        .datatable-body-cell:nth-child(3) {
            width: 390px !important;
        }

        .datatable-header-cell:nth-child(4),
        .datatable-body-cell:nth-child(4) {
            width: 64px !important;
        }

        .datatable-header-cell:nth-child(5),
        .datatable-body-cell:nth-child(5) {
            width: 70px !important;
        }

        .datatable-header-cell:nth-child(6),
        .datatable-body-cell:nth-child(6) {
            width: 77px !important;
        }

        .datatable-header-cell:nth-child(7),
        .datatable-body-cell:nth-child(7) {
            width: 93px !important;
        }

        .datatable-header-cell:nth-child(8),
        .datatable-body-cell:nth-child(8) {
            width: 120px !important;
        }

        .datatable-header-cell:nth-child(9),
        .datatable-body-cell:nth-child(9) {
            width: 120px !important;
        }
    }
    }

    .custom-checkbox-label {
        font-size: 14px;
    }
    .isDisabled {
        cursor: not-allowed;
        opacity: 0.4;
    }
    .disable-check{
        opacity: 0.4 ;
        cursor: none !important;
        pointer-events: none;
        background-color: #ccc !important;
    }
    .remove {
        background: url(data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZD0iTTMgM2wxOCAxOE0zIDIxTDIxIDMiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiLz48L3N5bWJvbD4NCiAgICAgICAgPC9kZWZzPg0KICAgICAgICA8Zz4NCiAgICAgICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI2Fzc2V0Ii8+DQogICAgICAgIDwvZz4NCiAgICA8L3N2Zz4=) no-repeat center;
        background-size: 55%;
        border-radius: 10px;
        border: solid 1px #ccc;
        display: inline-block;
        height: 20px;
        margin-right: 20px;
        width: 20px;
    }
    .add-new {
        text-decoration: 8.3% !important;
    }
    .error-on-row {
        border: 2px solid $theme-error-red !important;
    }
    .error-desc-expand {
        background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+) no-repeat center;
        height: 20px;
        width: 20px;
        background-color: $theme-error-red;
    }
    .error-tooltip {
        color: black;
        position: fixed;
        right: 45px;
        top: -30px;
        border: 2px solid $theme-error-red;
        width: 300px;
        z-index: 50;
        background-color: white;
        padding: 10px;
        text-align: left;
        white-space: pre-line !important;
    }
    .error-icon > img {
        height: 20px;
        margin-right: 20px;
    }

    .comments-newline {
        white-space: pre-line;
    }

    .info-tooltip {
        position: relative;
        display: inline-block;
        white-space: pre-line;
        cursor: pointer;
    }

    .info-tooltip .tooltiptext {
        visibility: hidden;
        min-width: 144px;
        max-width: 500px;
        background-color: lightgray;
        color: black;
        border: 1px solid gray;
        text-align: left;
        padding: 2px 7px;
        border-radius: 0px;
        top: 60%;
        left: auto;
        margin-left: -10px;
        position: absolute;
        z-index: 99;
    }

    .info-tooltip .tooltiptext.tt-comments {
        position: absolute;
        left: 10px;
    }

    .info-tooltip .tooltiptext::after {
        content: " ";
        position: absolute;
        bottom: 100%;
        left: 6%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent transparent gray transparent;
    }

    .info-tooltip .tooltiptext.tt-comments::after {
        position: absolute;
        bottom: 100%;
    }


.info-tooltip:hover .tooltiptext {
  visibility: visible;
}

.datatable-pager {
  margin: 0 10px;
  font-size: initial;
  li {
    vertical-align: middle;
    &.disabled a {
    color: rgba(0, 0, 0, 0.26) !important;
    background-color: transparent !important;
  }
  &.active a {
   background-color: #00529f;
   color: #fff;
  }
}
a {
 padding: 0 6px;
 border-radius: 3px;
 margin: 6px 3px;
 text-align: center;
 color: rgba(0, 0, 0, 0.54);
 text-decoration: none;
 vertical-align: bottom;
 &:hover {
   color: rgba(0, 0, 0, 0.75);
   background-color: rgba(158, 158, 158, 0.2);
 }
}
.datatable-icon-left, .datatable-icon-skip, .datatable-icon-right, 
.datatable-icon-prev {
   font-size: 20px;
  line-height: 20px;
  padding: 0 3px;
  }
}


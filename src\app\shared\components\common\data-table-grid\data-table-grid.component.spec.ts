import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { BaseProductGroupService } from '@appGroupsServices/base-product-group.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { PermissionsConfigurationService, PermissionsModule, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { DataTableGridComponent } from './data-table-grid.component';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';


describe('DataTableGridComponent', () => {
  let component: DataTableGridComponent;
  let fixture: ComponentFixture<DataTableGridComponent>;
  beforeEach(() => {
    const baseProductGroupServiceStub = () => ({
      onRadioSelected$: { next: jasmine.createSpy('next') }
    });
    const routerStub = () => ({
        navigateByUrl: editUrl => ({})
      });
    const initialDataServiceStub = () => ({ getAppDataName: string => ({}), getAppData: string => ({}) });
    TestBed.configureTestingModule({
      imports: [PermissionsModule.forRoot({ permissionsIsolate: true, configurationIsolate: true, rolesIsolate: true })],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [DataTableGridComponent],
      providers: [
        PermissionsConfigurationService,
        PermissionsService,
        { provide: BaseProductGroupService, useFactory: baseProductGroupServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: Router, useFactory: routerStub },
      ]
    });
    fixture = TestBed.createComponent(DataTableGridComponent);
    component = fixture.componentInstance;
  });
  it('can load instance', () => {
    expect(component).toBeTruthy();
  });
  it(`headerCheckEnabled has default value`, () => {
    expect(component.headerCheckEnabled).toEqual(false);
  });
  it(`allRowSelected has default value`, () => {
    expect(component.allRowSelected).toEqual(true);
  });
  it(`removeBtnItems has default value`, () => {
    expect(component.removeBtnItems.length).toEqual(3);
  });
  it(`reorderable has default value`, () => {
    expect(component.reorderable).toEqual(true);
  });

  beforeEach(() => {
    component.dataColumn = {
      col1: null,
      col2: null
    }
    component.rowsData = [{
      id: 1,
      isSelected: true,
      operatedGrid: null,
    }];
    component.expansionGrid = false;
  });

  describe('ngOnInit', () => {
    it('makes expected calls', () => {
      spyOn(component, 'loadData');
      component.ngOnInit();
      expect(component.loadData).toHaveBeenCalled();
    });
  });

  describe('ngOnChanges', () => {
    it('makes expected calls', () => {
      spyOn(component, 'ngOnInit')
      component.ngOnChanges();
      expect(component.ngOnInit).toHaveBeenCalled();
    });
  });

  describe('getAllSelectedData', () => {
    it('makes expected calls', () => {
      spyOn(component, 'loadData')
      component.getAllSelectedData();
      expect(component.loadData).toHaveBeenCalled();
    });
  });
  describe("disableBulkSelection",()=>{
    it("should return expected value ",()=>{
      component.rowsData = [{
        id: 1,
        suggestedExpansion:{isSelected:true}
      }];
      component.bulkSelection='CONFIRM'
      component.rejectedIds=[1]
      component.disableBulkSelection()
      expect(component.rowsData[0].suggestedExpansion.isSelected).toEqual(false)
    })
    
    it("should return expected value ",()=>{
      component.rowsData = [{
        id: 1,
        suggestedExpansion:{isSelected:true}
      }];
      component.bulkSelection='CONFIRM'
      component.rejectedIds=[2]
      component.disableBulkSelection()
      expect(component.rowsData[0].suggestedExpansion.isSelected).toEqual(true)
    });
    it("should return expected value ",()=>{
      component.rowsData = [{
        id: 1,
        suggestedExpansion:{isSelected:false}
      }];
      component.bulkSelection='REJECT'
      component.selectedIds=[1]
      component.disableBulkSelection()
      expect(component.rowsData[0].suggestedExpansion.isSelected).toEqual(true)
    });
  });
  describe("getData",()=>{
    it("should return expected value",()=>{
      component.rowsData = [{
        id: 1,
        isSelected: true,
        operatedGrid: null,
      }];
      let res=component.getData()
      expect(res).toEqual([{
        id: 1,
        isSelected: true,
        operatedGrid: null,
      }])
    });
  });
  describe("setRowData",()=>{
    it("make expected calls",()=>{
      component.rowsData = [{
        id: 1,
        isSelected: true,
        operatedGrid: null,
      }];
      let data=component.rowsData = [{
        id: 1,
        isSelected: true,
        operatedGrid: null,
      }];
      component.setRowData(data)
      expect(component.rowsData).toEqual(data)
    });
  });
  describe("onHeaderSelect",()=>{
    it("if selected is true",()=>{
      component.rowsData = [{
        id: 1,
        isSelected: true,
        operatedGrid: null,
        suggestedExpansion:{isSelected:true}
      }];
      component.grid="suggestedExpansion"
      component.onHeaderSelect({currentTarget:{checked:true}},'')
      expect(component.headerCount).toEqual(1)
    })
    it("if selected is false",()=>{
      component.rowsData = [{
        id: 1,
        isSelected: true,
        operatedGrid: null,
        suggestedExpansion:{isSelected:true}
      }];
      component.grid="suggestedExpansion"
      component.onHeaderSelect({currentTarget:{checked:false}},'')
      expect(component.headerCount).toEqual(0)
    });
  });
  describe("onRowSelect",()=>{
    it("if selected is false",()=>{
      let selected={currentTarget:{checked:false}}
      let row={
        id: 1,
        isSelected: true,
        operatedGrid: null,
        suggestedExpansion:{isSelected:true}
      }
      component.grid="suggestedExpansion"
      component.headerCount=2
      component.onRowSelect(selected,0,row)
      expect(component.headerCount).toEqual(1)
    })
    it("if selected is true",()=>{
      let selected={currentTarget:{checked:true}}
      let row={
        id: 1,
        isSelected: true,
        operatedGrid: null,
        suggestedExpansion:{isSelected:true}
      }
      component.grid="suggestedExpansion"
      component.headerCount=2
      component.onRowSelect(selected,0,row)
      expect(component.headerCount).toEqual(3)
    });
  });
  describe("getOperatedGridName",()=>{
    it("if rowdata is upcExpansionDataId",()=>{
      component.rowsData=[{upcExpansionDataId:1}]
      let res=component.getOperatedGridName()
      expect(res).toEqual("upcids")
    });
    it("if rowdata is manufacturerDataId",()=>{
      component.rowsData=[{manufacturerDataId:1}]
      let res=component.getOperatedGridName()
      expect(res).toEqual("manufactureids")
    });
    it("if rowdata is departmentDataId",()=>{
      component.rowsData=[{departmentDataId:1}]
      let res=component.getOperatedGridName()
      expect(res).toEqual("departmentSectionIds")
    });
  });
  describe("addRow",()=>{
    it("make expected calls",()=>{
      let row={
        id: 1,
        isSelected: true,
        operatedGrid: null,
        suggestedExpansion:{isSelected:true}
      }
      let spy=spyOn(component,"getOperatedGridName")
      component.addRow(row)
      expect(spy).toHaveBeenCalled()
    });
  });
  describe("removeRow",()=>{
    it("make expected calls",()=>{
      let row={
        id: 1,
        isSelected: true,
        operatedGrid: null,
        suggestedExpansion:{isSelected:true}
      }
      let spy=spyOn(component,"getOperatedGridName")
      component.removeRow(row, 0)
      expect(spy).toHaveBeenCalled()
    });
  });

  describe("hideRemoveBtn", () => {
    it("should return true if grid is 'upcExpansionDataId' and row origin is CONSTANTS.PLU", () => {
      component.grid = 'upcExpansionDataId';
      const row = { origin: CONSTANTS.PLU };
      const result = component.hideRemoveBtn(row);
      expect(result).toBeTrue();
    });

    it("should return true if grid is 'upcExpansionDataId', rowsData length is 1, and row origin is 'Suggested'", () => {
      component.grid = 'upcExpansionDataId';
      component.rowsData = [{}];
      const row = { origin: 'Suggested' };
      const result = component.hideRemoveBtn(row);
      expect(result).toBeTrue();
    });

    it("should return true if grid is 'upcExpansionDataId', showRadio is true, and row origin is 'Suggested'", () => {
      component.grid = 'upcExpansionDataId';
      component.showRadio = true;
      const row = { origin: 'Suggested' };
      const result = component.hideRemoveBtn(row);
      expect(result).toBeTrue();
    });

    it("should return false for other cases", () => {
      component.grid = 'otherGrid';
      const row = { origin: 'Other' };
      const result = component.hideRemoveBtn(row);
      expect(result).toBeFalse();
    });
  });

  describe("getRowClass", () => {
    it("should return 'error-on-row' if row hasError is true", () => {
      const row = { hasError: true };
      const result = component.getRowClass(row);
      expect(result).toEqual('error-on-row');
    });

    it("should return undefined if row hasError is false", () => {
      const row = { hasError: false };
      const result = component.getRowClass(row);
      expect(result).toBeUndefined();
    });
  });

  describe("setClassForRadio", () => {
    it("should return true if grid is 'upcExpansionDataId' and showRadio is true", () => {
      component.grid = 'upcExpansionDataId';
      component.showRadio = true;
      const result = component.setClassForRadio;
      expect(result).toBeTrue();
    });

    it("should return false for other cases", () => {
      component.grid = 'otherGrid';
      component.showRadio = false;
      const result = component.setClassForRadio;
      expect(result).toBeFalse();
    });
  });

  describe("setClassForNonBase", () => {
    it("should return true if isBaseProductGroup is false and grid is 'upcExpansionDataId'", () => {
      component.isBaseProductGroup = false;
      component.grid = 'upcExpansionDataId';
      const result = component.setClassForNonBase;
      expect(result).toBeTrue();
    });

    it("should return false for other cases", () => {
      component.isBaseProductGroup = true;
      component.grid = 'otherGrid';
      const result = component.setClassForNonBase;
      expect(result).toBeFalse();
    });
  });

  describe("removeZeros", () => {
    it("should remove trailing zeros from a decimal number", () => {
      const quantity = 123.45000;
      const result = component.removeZeros(quantity);
      expect(result).toEqual('123.45');
    });

    it("should return the same value for integers", () => {
      const quantity = 123;
      const result = component.removeZeros(quantity);
      expect(result).toEqual('123');
    });

    it("should return an empty string for null or undefined", () => {
      const result = component.removeZeros(null);
      expect(result).toEqual('');
    });
  });

  describe("removeRow", () => {
    it("should set error if row.origin is 'Original', grid is 'upcExpansionDataId', no 'Suggested' rows exist, and pgType is BASE_PG", () => {
      component.grid = 'upcExpansionDataId';
      component.pgType = CONSTANTS.BASE_PG;
      component.rowsData = [{ origin: 'Original' }];
      const row = { origin: 'Original', hasError: false, errorMsg: '' };
      const rowIndex = 0;

      component.removeRow(row, rowIndex);

      expect(component.showErrorAtIndex).toEqual(rowIndex);
      expect(row.hasError).toBeTrue();
      expect(row.errorMsg).toEqual('In order to remove the Original UPC the final list must contain at least one Suggested UPC.');
    });

    it("should not set error if row.origin is 'Original', grid is 'upcExpansionDataId', and 'Suggested' rows exist", () => {
      component.grid = 'upcExpansionDataId';
      component.pgType = CONSTANTS.BASE_PG;
      component.rowsData = [{ origin: 'Original' }, { origin: 'Suggested' }];
      const row = { origin: 'Original', hasError: false, errorMsg: '' };
      const rowIndex = 0;

      component.removeRow(row, rowIndex);

      expect(component.showErrorAtIndex).toEqual(-1);
      expect(row.hasError).toBeFalse();
      expect(row.errorMsg).toEqual('');
    });

    it("should not set error if grid is not 'upcExpansionDataId'", () => {
      component.grid = 'otherGrid';
      component.pgType = CONSTANTS.BASE_PG;
      component.rowsData = [{ origin: 'Original' }];
      const row = { origin: 'Original', hasError: false, errorMsg: '' };
      const rowIndex = 0;

      component.removeRow(row, rowIndex);

      expect(component.showErrorAtIndex).toEqual(-1);
      expect(row.hasError).toBeFalse();
      expect(row.errorMsg).toEqual('');
    });

    it("should not set error if pgType is not BASE_PG", () => {
      component.grid = 'upcExpansionDataId';
      component.pgType = 'OTHER_PG';
      component.rowsData = [{ origin: 'Original' }];
      const row = { origin: 'Original', hasError: false, errorMsg: '' };
      const rowIndex = 0;

      component.removeRow(row, rowIndex);

      expect(component.showErrorAtIndex).toEqual(-1);
      expect(row.hasError).toBeFalse();
      expect(row.errorMsg).toEqual('');
    });
  });

  describe("ngOnInit", () => {
    it("should set allRowSelected to false and headerCount to rowsData length when grid is 'suggestedExpansion'", () => {
      component.grid = 'suggestedExpansion';
      component.rowsData = [{ id: 1 }, { id: 2 }];
      spyOn(component, 'getAllSelectedData');
      component.ngOnInit();
      expect(component.allRowSelected).toBeFalse();
      expect(component.headerCount).toEqual(2);
      expect(component.getAllSelectedData).toHaveBeenCalled();
    });

    it("should call loadData if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      spyOn(component, 'loadData');
      component.ngOnInit();
      expect(component.loadData).toHaveBeenCalled();
    });

    it("should set rowAddRemoveBtnEnable to 'Remove' if grid is in removeBtnItems", () => {
      component.grid = 'upcExpansionDataId';
      component.ngOnInit();
      expect(component.rowAddRemoveBtnEnable).toEqual('Remove');
    });

    it("should set rowAddRemoveBtnEnable to 'Add' if grid is 'upcRejectedDataId'", () => {
      component.grid = 'upcRejectedDataId';
      component.ngOnInit();
      expect(component.rowAddRemoveBtnEnable).toEqual('Add');
    });

    it("should set rowAddRemoveBtnEnable to empty string if hideAddRemoveButton is true", () => {
      component.hideAddRemoveButton = true;
      component.ngOnInit();
      expect(component.rowAddRemoveBtnEnable).toEqual('');
    });
  });

  describe("isLineClamp", () => {
    it("should return true if isBaseProductGroup is true and item is 'description'", () => {
      component.isBaseProductGroup = true;
      const result = component.isLineClamp('description');
      expect(result).toBeTrue();
    });

    it("should return false for other cases", () => {
      component.isBaseProductGroup = false;
      const result = component.isLineClamp('description');
      expect(result).toBeFalse();
    });
  });

  describe("getRowHeight", () => {
    it("should return row height if grid is 'suggestedExpansion'", () => {
      component.grid = 'suggestedExpansion';
      const row = { height: 50 };
      const result = component.getRowHeight(row);
      expect(result).toEqual(50);
    });

    it("should return undefined for other grids", () => {
      component.grid = 'otherGrid';
      const row = { height: 50 };
      const result = component.getRowHeight(row);
      expect(result).toBeUndefined();
    });
  });

  describe("isSortable", () => {
    it("should return true if isBaseProductGroup is true and item is 'rank'", () => {
      component.isBaseProductGroup = true;
      const result = component.isSortable('rank');
      expect(result).toBeTrue();
    });

    it("should return false if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      const result = component.isSortable('rank');
      expect(result).toBeFalse();
    });
  });

  describe("isDisplayBulkSelection", () => {
    it("should return true if grid is 'suggestedExpansion' and isBaseProductGroup is true", () => {
      component.grid = 'suggestedExpansion';
      component.isBaseProductGroup = true;
      const result = component.isDisplayBulkSelection;
      expect(result).toBeTrue();
    });

    it("should return true if grid is 'suggestedExpansion' and currentExpandPage is 1", () => {
      component.grid = 'suggestedExpansion';
      component.currentExpandPage = 1;
      const result = component.isDisplayBulkSelection;
      expect(result).toBeTrue();
    });

    it("should return true for other grids", () => {
      component.grid = 'otherGrid';
      const result = component.isDisplayBulkSelection;
      expect(result).toBeTrue();
    });
  });

  describe("toShowTooltipClass", () => {
    it("should return true if dataColumn[item] is 'Score' or 'Description'", () => {
      component.isBaseProductGroup = true;
      const obj = { dataColumn: { col1: 'Score' }, item: 'col1', row: {} };
      const result = component.toShowTooltipClass(obj);
      expect(result).toBeTrue();
    });

    it("should return true if dataColumn[item] is 'Comment' and row[item] length is greater than 3", () => {
      component.isBaseProductGroup = true;
      const obj = { dataColumn: { col1: 'Comment' }, item: 'col1', row: { col1: [1, 2, 3, 4] } };
      const result = component.toShowTooltipClass(obj);
      expect(result).toBeTrue();
    });

    it("should return false for other cases", () => {
      component.isBaseProductGroup = true;
      const obj = { dataColumn: { col1: 'Other' }, item: 'col1', row: {} };
      const result = component.toShowTooltipClass(obj);
      expect(result).toBeFalse();
    });
  });

  describe("dataValue", () => {
    it("should return 'Suggested' if item is 'origin' and grid is 'suggestedExpansion'", () => {
      component.isBaseProductGroup = true;
      component.grid = 'suggestedExpansion';
      const obj = { row: {}, item: 'origin' };
      const result = component.dataValue(obj);
      expect(result).toEqual('Suggested');
    });

    it("should return formatted comment text if item is 'comments'", () => {
      component.isBaseProductGroup = true;
      const obj = {
        row: { comments: [{ comment: 'Test', commentBy: 'User', commentDate: null }] },
        item: 'comments'
      };
      const result = component.dataValue(obj);
      expect(result).toContain('Test');
    });

    it("should return original value for other cases", () => {
      const obj = { row: { col1: 'value' }, item: 'col1' };
      const result = component.dataValue(obj);
      expect(result).toEqual('value');
    });
  });

  describe("disableIfMob", () => {
    it("should return true if row has a mobId", () => {
      const row = { mobId: 123 };
      const result = component.disableIfMob(row);
      expect(result).toBeTrue();
    });

    it("should return null if row does not have a mobId", () => {
      const row = {};
      const result = component.disableIfMob(row);
      expect(result).toBeNull();
    });
  });

  describe("setLimitForPagination", () => {
    it("should return CONSTANTS.BPG_SUGGESTED_LIST_PAGINATION_LIMIT if isBaseProductGroup is true and grid is 'suggestedExpansion'", () => {
      component.isBaseProductGroup = true;
      component.grid = "suggestedExpansion";
      const result = component.setLimitForPagination();
      expect(result).toEqual(CONSTANTS.BPG_SUGGESTED_LIST_PAGINATION_LIMIT);
    });

    it("should return rowsData length if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      component.rowsData = [{}, {}, {}];
      const result = component.setLimitForPagination();
      expect(result).toEqual(3);
    });

    it("should return rowsData length if grid is not 'suggestedExpansion'", () => {
      component.isBaseProductGroup = true;
      component.grid = "otherGrid";
      component.rowsData = [{}, {}, {}];
      const result = component.setLimitForPagination();
      expect(result).toEqual(3);
    });
  });

  describe("getTitle", () => {
    it("should return row[item] if dataColumn[item] is 'Description'", () => {
      component.isBaseProductGroup = true;
      const obj = {
        dataColumn: { col1: 'Description' },
        item: 'col1',
        row: { col1: 'Test Description' }
      };
      const result = component.getTitle(obj);
      expect(result).toEqual('Test Description');
    });

    it("should return rank description if dataColumn[item] is 'Score'", () => {
      component.isBaseProductGroup = true;
      const appData = { rankDescription: { 1: 'High', 2: 'Medium' } };
      spyOn(component['initialDataService'], 'getAppData').and.returnValue(appData);
      const obj = {
        dataColumn: { col1: 'Score' },
        item: 'col1',
        row: { rank: 1 }
      };
      const result = component.getTitle(obj);
      expect(result).toEqual('High');
    });

    it("should return formatted comments if dataColumn[item] is 'Comment'", () => {
      component.isBaseProductGroup = true;
      const obj = {
        dataColumn: { col1: 'Comment' },
        item: 'col1',
        row: {
          col1: [
            { comment: 'Test Comment', commentBy: 'User1', commentDate: null },
            { comment: 'Another Comment', commentBy: 'User2', commentDate: null }
          ]
        }
      };
      const result = component.getTitle(obj);
      expect(result).toContain('Test Comment');
      expect(result).toContain('User1');
      expect(result).toContain('Another Comment');
      expect(result).toContain('User2');
    });

    it("should return an empty string for other cases", () => {
      component.isBaseProductGroup = true;
      const obj = {
        dataColumn: { col1: 'Other' },
        item: 'col1',
        row: {}
      };
      const result = component.getTitle(obj);
      expect(result).toEqual('');
    });

    it("should return undefined if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      const obj = {
        dataColumn: { col1: 'Description' },
        item: 'col1',
        row: { col1: 'Test Description' }
      };
      const result = component.getTitle(obj);
      expect(result).toBeUndefined();
    });
  });

  describe("dataValue", () => {
    it("should return quantity without trailing zeros if item is 'quantity'", () => {
      component.isBaseProductGroup = true;
      const obj = { row: { quantity: 123.45000 }, item: 'quantity' };
      spyOn(component, 'removeZeros').and.returnValue('123.45');
      const result = component.dataValue(obj);
      expect(result).toEqual('123.45');
      expect(component.removeZeros).toHaveBeenCalledWith(123.45000);
    });

    it("should return original value for other cases", () => {
      const obj = { row: { col1: 'value' }, item: 'col1' };
      const result = component.dataValue(obj);
      expect(result).toEqual('value');
    });
  });
  describe("ngOnInit", () => {
    it("should calculate headerCount correctly when expansionGrid is true", () => {
      component.expansionGrid = true;
      component.rowsData = [
        { suggestedExpansion: { isSelected: true } },
        { suggestedExpansion: { isSelected: false } },
        { suggestedExpansion: { isSelected: true } }
      ];
      component.ngOnInit();
      expect(component.headerCount).toEqual(2);
    });

    it("should set headerCount to 0 when expansionGrid is true and no rows are selected", () => {
      component.expansionGrid = true;
      component.rowsData = [
        { suggestedExpansion: { isSelected: false } },
        { suggestedExpansion: { isSelected: false } }
      ];
      component.ngOnInit();
      expect(component.headerCount).toEqual(0);
    });

    it("should not calculate headerCount based on suggestedExpansion when expansionGrid is false", () => {
      component.expansionGrid = false;
      component.rowsData = [
        { suggestedExpansion: { isSelected: true } },
        { suggestedExpansion: { isSelected: false } }
      ];
      component.ngOnInit();
      expect(component.headerCount).toEqual(component.rowsData.length);
    });
  });
  describe("toShowTooltipClass", () => {
    it("should return undefined if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      const obj = { dataColumn: { col1: 'Score' }, item: 'col1', row: {} };
      const result = component.toShowTooltipClass(obj);
      expect(result).toBeUndefined();
    });
  });

  describe("getTitle", () => {
    it("should return undefined if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      const obj = {
        dataColumn: { col1: 'Description' },
        item: 'col1',
        row: { col1: 'Test Description' }
      };
      const result = component.getTitle(obj);
      expect(result).toBeUndefined();
    });
  });

  describe("dataValue", () => {
    it("should return original value if isBaseProductGroup is false", () => {
      component.isBaseProductGroup = false;
      const obj = { row: { col1: 'value' }, item: 'col1' };
      const result = component.dataValue(obj);
      expect(result).toEqual('value');
    });
  });

  describe("addAndRemoveIds", () => {
    it("should remove id from removeArr if it exists", () => {
      const removeArr = [1, 2, 3];
      const addArr = [];
      const id = 2;

      component.addAndRemoveIds(removeArr, addArr, id);

      expect(removeArr).toEqual([1, 3]);
      expect(addArr).toEqual([2]);
    });

    it("should add id to addArr if it does not exist in removeArr", () => {
      const removeArr = [1, 3];
      const addArr = [];
      const id = 4;

      component.addAndRemoveIds(removeArr, addArr, id);

      expect(removeArr).toEqual([1, 3]);
      expect(addArr).toEqual([4]);
    });

    it("should not modify arrays if id is not in removeArr and already exists in addArr", () => {
      const removeArr = [1, 3];
      const addArr = [4];
      const id = 4;

      component.addAndRemoveIds(removeArr, addArr, id);
    });
  });
});
import {
  Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewEncapsulation
} from "@angular/core";
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { BaseProductGroupService } from "@appGroupsServices/base-product-group.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { convertUTCToLocalDateWithTime } from "@appUtilities/date.utility";

@Component({
  selector: "data-table-grid",
  templateUrl: "./data-table-grid.component.html",
  styleUrls: ["./data-table-grid.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class DataTableGridComponent implements OnInit, OnChanges {
  @Input() rowHeight;
  @Input() columnMode;
  @Input() scrollbarV;
  @Input() rowsData;
  @Input() tableHeader;
  @Input() grid;
  @Input() headerCheckEnabled: boolean = false;
  @Input() expansionGrid;
  @Output() selectedRows = new EventEmitter();
  @Output() selectedRow = new EventEmitter();
  @Output() globalSelect = new EventEmitter();
  allRowSelected: boolean = true;
  @Input() dataColumn;
  @Input('pgType') pgType;
  @Input() isBaseProductGroup;
  @Input() hideAddRemoveButton:boolean = false;
  headerCount;
  dataColumns;
  @Input() showCheck;
  @Input() showRadio;
  bulkSelection:string = "REJECT";
  rejectedIds = [];
  selectedIds = [];
  rowAddRemoveBtnEnable: string = "";
  selectedGrid: string;
  removeBtnItems = [
    "upcExpansionDataId",
    "manufacturerDataId",
    "departmentDataId"
  ];
  manageProductGroups = CONSTANTS.Permissions.ManageProductGroups;
  totalNoOfExpandedIdsMatchedWithPrvRejections: any = 0;
  showErrorAtIndex = -1;
 
  constructor( public baseProductGroupService: BaseProductGroupService,
    private initialDataService: InitialDataService
    ) {
      // intentionally left empty
    }
  reorderable = true;
  currentExpandPage = 0;


  ngOnInit() {
    this.dataColumns = Object.keys(this.dataColumn);
    
    if(this.expansionGrid){  
      this.headerCount = this.rowsData?.reduce((acc,item)=>{
        return acc = item.suggestedExpansion?.isSelected ? acc+1: acc;
      },0);
    }else {
      this.headerCount = this.rowsData?.length;
    }

    if(this.grid==='suggestedExpansion'){ 
      this.rowsData?.forEach(item => {
        item.isSelected = false;
        item = this.formatItem(item);
        item[this.grid].isSelected = item.isSelected;
      });
      this.allRowSelected = false;
      this.headerCount = this.rowsData?.length;
      this.getAllSelectedData();
   }

    if(!this.isBaseProductGroup){
      this.loadData();
    }
    
    if (this.removeBtnItems.includes(this.grid)) {
      this.rowAddRemoveBtnEnable = "Remove";
    } else if (this.grid === "upcRejectedDataId") {
      this.rowAddRemoveBtnEnable = "Add";
    } else if (this.grid === "originalExpansion") {
      this.rowAddRemoveBtnEnable = "Remove";
    }

    if(this.hideAddRemoveButton)
    {
      this.rowAddRemoveBtnEnable = "";
    }
  }
  
  ngOnChanges(): void {
    this.ngOnInit();
  }
   isLineClamp(item){
      //TO DO: base
     return  this.isBaseProductGroup && item === 'description';
  }
  getRowHeight(row) {
      if(this.grid ==='suggestedExpansion'){
        return row.height;
      }
  }

  isSortable(item){
    //TO DO: base
    if(!this.isBaseProductGroup){
      return false;
    }
    return  item === 'rank';
  }

  get isDisplayBulkSelection(){
    return this.grid==='suggestedExpansion'? this.isBaseProductGroup || this.currentExpandPage === 1 : true;
  }

  openPGofMob(row, item) {
      const detailUrl = `/${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.ProductGroup}/${ROUTES_CONST.GROUPS.ProductDetails}/${ROUTES_CONST.GROUPS.Edit}/${row.pgId}`;
      const win = window.open(detailUrl, '_blank');
      win.focus();
  }
  disableIfMob(row){
    return row?.mobId ? true: null
  }
  
  setLimitForPagination() {
    return  this.isBaseProductGroup && this.grid === "suggestedExpansion" ? CONSTANTS.BPG_SUGGESTED_LIST_PAGINATION_LIMIT : this.rowsData?.length;
  }
  
  toShowTooltipClass(obj) {
    if(!this.isBaseProductGroup){
      return;
    }
    const {dataColumn, item, row } = obj;
    if (dataColumn[item] === 'Score' || dataColumn[item] === 'Description') {
      return true;
    } else if (dataColumn[item] === 'Comment') {
      return row[item] && row[item][0] && row[item].length > 3;
    } 
    return false;
  }

  getTitle(obj){
    if(!this.isBaseProductGroup){
      return;
    }
    const {dataColumn, item, row } = obj;
   
    let title;
    switch(dataColumn[item]){
      case "Description":{
        title =  row[item];
        break;
      }
      case "Score":{
        const appData = this.initialDataService.getAppData();
        title = appData.rankDescription[row.rank];
        break;
      }
      case "Comment":{
        title = '';
        const values = row[item] && row[item][0] ? row[item] : [];
        values.forEach((val, vIdx, vArr) => {
          const commentDate =  val.commentDate?` (${convertUTCToLocalDateWithTime(val.commentDate)})`:'',
          biWeeklyCmntReason = val?.biWeeklyComment  ? `- ${val.biWeeklyComment}` : '',
           cmtText = `${val.comment}  ${val.commentBy} ${biWeeklyCmntReason}  ${commentDate}`;
          title = `${title}${title === '' ? '' : '\n'}${cmtText}`;
        }); 
        break;
      }
      default:{
        title = "";
        break;
      }
    }

    return title;
    
  }

  dataValue(obj){
    const {row, item} = obj, value =  row[item];
  
    if(this.isBaseProductGroup){
      //set origin as Suggested in Suggested list
      if(item === "origin" && this.grid === "suggestedExpansion"){
        return "Suggested";
      }else if(item == "comments" && value &&  value[0]){
        let commentShortText = '';
        value.forEach((val, vIdx, vArr) => {
          const commentDate =  val.commentDate?` (${convertUTCToLocalDateWithTime(val.commentDate)})`:'', 
          biWeeklyCmntReason = val?.biWeeklyComment  ? ` - ${val.biWeeklyComment}` : '',
          cmtText = `${val.comment}  ${val.commentBy}${biWeeklyCmntReason}${commentDate}`;
          if (vIdx < 3) {
            commentShortText = `${commentShortText}${commentShortText === '' ? '' : '\n'}${cmtText} ${value.length>3 && vIdx === 2 ? '...' : ''}`;
          }
        });
        return commentShortText;
      } else if(item == "createdDate"){
        return convertUTCToLocalDateWithTime(row[item]);
      } else if(item == "updatedDate"){
        return convertUTCToLocalDateWithTime(row[item]);
      } else if (item == "quantity") {
         return this.removeZeros(row[item]);
      }
    }
    
    return row[item];
  }


  disableBulkSelection(){
    this.rowsData?.forEach(element => {
      const {suggestedExpansion} = element;
      
      if(suggestedExpansion){
          if(this.bulkSelection==='CONFIRM'){
            if(this.rejectedIds.includes(element.id)){
              suggestedExpansion.isSelected = false;
            }else{
              suggestedExpansion.isSelected = true;
            }
          }else if(this.bulkSelection==='REJECT'){
            if(this.selectedIds.includes(element.id)){
              suggestedExpansion.isSelected = true;
            }else{
              suggestedExpansion.isSelected = false;
            }
          }
      }
        });
  }
  getData() {
    return this.rowsData?.filter(item => item.isSelected);
  }
  setRowData(data) {
    this.rowsData = data;
  }

  formatItem(item){
    let val = item[this.grid];
    if(!val){

      item[this.grid] = {
        isSelected:null
      };
    }
    return item;
  }
  onHeaderSelect(selected ,btnAction = '') {
    if (!selected.currentTarget.checked) {
      this.rowsData?.forEach(item => {
        item.isSelected = false;
        
        item = this.formatItem(item);
        

        item[this.grid].isSelected = item.isSelected;
      });
      this.allRowSelected = false;
      this.headerCount = 0;
     
    } else {
        this.rowsData?.forEach(item => {
        item.isSelected = true;
        item = this.formatItem(item);
        if(this.grid === 'suggestedExpansion' && item.mobId){
          item.isSelected = false;
        }
        item[this.grid].isSelected = item.isSelected;
      });
      this.allRowSelected = true;
      this.headerCount = this.rowsData?.length;
    }
    if(this.grid==='suggestedExpansion'){
      this.bulkSelection = selected.currentTarget.checked?'CONFIRM':'REJECT';
      this.globalSelect.next(this.bulkSelection);
    }
    this.getAllSelectedData();
  }
  onRowSelect(selected, index, row) {
    const isSelected = selected.currentTarget.checked;

    if(this.grid==='suggestedExpansion' && this.isBaseProductGroup){
      if (!selected.currentTarget.checked) {
        this.allRowSelected = false;
      }
         this.baseProductGroupService.onRowSelect$.next({isSelected, row, index});
    }

    row.isSelected = isSelected;
    row = this.formatItem(row);

    row[this.grid].isSelected = row.isSelected;

    if (!selected.currentTarget.checked) {
      this.headerCount = this.headerCount - 1;
    } else {
      this.headerCount = this.headerCount + 1;

    }
    
    if(this.grid==='suggestedExpansion'){
      if (selected.currentTarget.checked) {
         this.addAndRemoveIds(this.rejectedIds,this.bulkSelection==='CONFIRM'?null:this.selectedIds,row.id);
        this.totalNoOfExpandedIdsMatchedWithPrvRejections = this.totalNoOfExpandedIdsMatchedWithPrvRejections - 1;
      } else {
        this.addAndRemoveIds(this.selectedIds,this.bulkSelection==='REJECT'?null:this.rejectedIds,row.id);
        this.totalNoOfExpandedIdsMatchedWithPrvRejections = this.totalNoOfExpandedIdsMatchedWithPrvRejections  + 1;
  
      }
    }else{
      this.allRowSelected = this.headerCount === this.rowsData?.length;
    }
    this.getAllSelectedData();
  }

  addAndRemoveIds(removeArr,addArr,id){
    const index = removeArr?.indexOf(id);
    if(index!==-1){
      removeArr?.splice(index,1);
    }
    addArr?.push(id);
  }
  loadData() {
    this.selectedRows.next(this.rowsData);
  }
  getAllSelectedData() {
    this.loadData();
  }

  onRadioSelected(selected, index, row) {
    const isSelected = selected.currentTarget.checked;
    if (isSelected) {
      this.baseProductGroupService.onRadioSelected$.next({ selected, index, row });
    }
  }


  getOperatedGridName() {
    if (
      this.rowsData[0].hasOwnProperty("upcExpansionDataId") ||
      this.rowsData[0].hasOwnProperty("upcRejectedDataId")
    ) {
      return "upcids";
    } else if (this.rowsData[0].hasOwnProperty("manufacturerDataId")) {
      return "manufactureids";
    } else if (this.rowsData[0].hasOwnProperty("departmentDataId")) {
      return "departmentSectionIds";
    }
  }
  addRow(row) {
    this.selectedRow.emit({
      action: "add",
      id: row.id,
      operatedGrid: this.getOperatedGridName()
    });
  }
  removeRow(row, rowIndex) {
    if (row.origin === 'Original' && this.grid === 'upcExpansionDataId' 
      && this.rowsData?.filter(rd => rd.origin === 'Suggested').length === 0 && this.pgType === CONSTANTS.BASE_PG) {
        this.showErrorAtIndex = rowIndex;
        row.hasError = true;
        row.errorMsg = 'In order to remove the Original UPC the final list must contain at least one Suggested UPC.';
        return;
      } else {
        this.showErrorAtIndex = -1;
        row.hasError = false;
        row.errorMsg = '';
      }
    this.selectedRow.emit({
      action: "remove",
      row,
      id: row.id,
      index: rowIndex,
      operatedGrid: this.getOperatedGridName()
    });
  }

  hideRemoveBtn(row) {
    if(this.grid === 'upcExpansionDataId' &&  row?.origin === CONSTANTS.PLU){
      return true;
    }
    return this.grid === 'upcExpansionDataId' && (this.rowsData?.length === 1 || this.showRadio) && row.origin === 'Suggested';
  }
  
  getRowClass(row) {
    if (row.hasError) {
      return 'error-on-row';
    }
  }
  get setClassForRadio() {
    return this.grid === "upcExpansionDataId" && this.showRadio;
  }
  get setClassForNonBase() {
      return !this.isBaseProductGroup && this.grid === "upcExpansionDataId"
    }
  removeZeros(quantity) {
      //removes trailing zeros
      const quantityVal = quantity?.toString().replace(/(\.0*|(?=(\..*))0*)$/, '') || '';
      return quantityVal
  }
}

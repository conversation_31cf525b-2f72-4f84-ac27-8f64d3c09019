.bpg {
  .ngx-datatable {
    .datatable-body-cell {
        .datatable-body-cell-label {
            overflow: initial;
        }
      }
    }
  
  .removeIconWrapper, .removeIconContainer{
    padding:0;
  }
  .resize-handle {
    display: none !important;
  }
  .datatable-body-cell-label,
  .datatable-header-cell-template-wrap {
    padding-right: 16px !important;
  }
  .upcExpansionDataId {
    .datatable-footer{
      display:none;
    }
    .datatable-row-group,
    .datatable-row-center {
  
      .datatable-body-cell:nth-child(1),
      .datatable-header-cell:nth-child(1) {
        width: 109.571px !important;
      }

      .datatable-body-cell:nth-child(2),
      .datatable-header-cell:nth-child(2) {
        width: 173.615px !important;
        white-space:pre-line !important;
      }

      .datatable-body-cell:nth-child(3),
      .datatable-header-cell:nth-child(3) {
        width: 150px !important;
      }

      .datatable-body-cell:nth-child(4),
      .datatable-header-cell:nth-child(4) {
        width: 187.615px !important;
      }

      .datatable-body-cell:nth-child(5),
      .datatable-header-cell:nth-child(5) {
        width: 98px !important;
      }

      .datatable-body-cell:nth-child(6),
      .datatable-header-cell:nth-child(6) {
        width: 56px !important;
      }

      .datatable-body-cell:nth-child(7),
      .datatable-header-cell:nth-child(7) {
        width:58px !important;
      }

      .datatable-body-cell:nth-child(8),
      .datatable-header-cell:nth-child(8) {
        width: 60px !important;
      }

      .datatable-body-cell:nth-child(9),
      .datatable-header-cell:nth-child(9) {
        width: 72.571px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 100.615px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 87.1429px !important;
      }
      .datatable-body-cell:nth-child(11),
      .datatable-header-cell:nth-child(11) {
        width: 87.1429px !important;
      }
     

      .datatable-body-cell:nth-child(12),
      .datatable-header-cell:nth-child(12) {
        width: 158.6px !important;
      }

      .datatable-body-cell:nth-child(13),
      .datatable-header-cell:nth-child(13) {
        width: 314px  !important;
      }
    }
  }
  .upcExpansionDataId.adjustRadio {
    .datatable-row-group,
    .datatable-row-center {
  
      .datatable-body-cell:nth-child(1),
      .datatable-header-cell:nth-child(1) {
        width: 65px !important;
      }

      .datatable-body-cell:nth-child(2),
      .datatable-header-cell:nth-child(2) {
        width: 98px !important;
      }

      .datatable-body-cell:nth-child(3),
      .datatable-header-cell:nth-child(3) {
        width: 110px !important;
      }

      .datatable-body-cell:nth-child(4),
      .datatable-header-cell:nth-child(4) {
        width: 110.615px !important;
      }

      .datatable-body-cell:nth-child(5),
      .datatable-header-cell:nth-child(5) {
        width: 130.55px !important;
      }

      .datatable-body-cell:nth-child(6),
      .datatable-header-cell:nth-child(6) {
        width: 77px !important;
      }

      .datatable-body-cell:nth-child(7),
      .datatable-header-cell:nth-child(7) {
        width:40px !important;
      }

      .datatable-body-cell:nth-child(8),
      .datatable-header-cell:nth-child(8) {
        width: 45px !important;
      }

      .datatable-body-cell:nth-child(9),
      .datatable-header-cell:nth-child(9) {
        width: 77.571px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 66.14px !important;
      }
      .datatable-body-cell:nth-child(11),
      .datatable-header-cell:nth-child(11) {
        width: 71.615px !important;
      }
     

      .datatable-body-cell:nth-child(12),
      .datatable-header-cell:nth-child(12) {
        width: 71.6px !important;
      }

      .datatable-body-cell:nth-child(13),
      .datatable-header-cell:nth-child(13) {
        width: 140.6px  !important;
      }
    }
    .datatable-body-cell:nth-child(14),
    .datatable-header-cell:nth-child(13) {
      width: 270px  !important;
    }
  }
  .suggestedExpansion {
    .datatable-row-group,
    .datatable-row-center {
      .datatable-body-cell:nth-child(2),
      .datatable-header-cell:nth-child(2) {
        width: 110px !important;
      }
      .datatable-body-cell:nth-child(3),
      .datatable-header-cell:nth-child(3) {
        width: 108.615px !important;
        white-space: pre-line !important;
      }
      .datatable-body-cell-label:nth-child(3){
        white-space: pre-line;
      }

      .datatable-body-cell:nth-child(4),
      .datatable-header-cell:nth-child(4) {
        width: 150px !important;
      }

      .datatable-body-cell:nth-child(5),
      .datatable-header-cell:nth-child(5) {
        width: 187.615px !important;
      }

      .datatable-body-cell:nth-child(6),
      .datatable-header-cell:nth-child(6) {
        width: 98px !important;
      }

      .datatable-body-cell:nth-child(7),
      .datatable-header-cell:nth-child(7) {
        width: 56px !important;
      }

      .datatable-body-cell:nth-child(8),
      .datatable-header-cell:nth-child(8) {
        width: 58px !important;
      }

      .datatable-body-cell:nth-child(9),
      .datatable-header-cell:nth-child(9) {
        width: 60px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 72.571px !important;
      }

      .datatable-body-cell:nth-child(11),
      .datatable-header-cell:nth-child(11) {
        width: 100.615px !important;
      }

      .datatable-body-cell:nth-child(13),
      .datatable-header-cell:nth-child(13) {
        width: 158.6px !important;
      }

      .datatable-body-cell:nth-child(14),
      .datatable-header-cell:nth-child(14) {
        width: 314px !important;
      }
    }
  }
  .droppedUpcsGrid {
    .datatable-row-group,
    .datatable-row-center {
  
      .datatable-body-cell:nth-child(1),
      .datatable-header-cell:nth-child(1) {
        width: 97.39px !important;
      }

      .datatable-body-cell:nth-child(2),
      .datatable-header-cell:nth-child(2) {
        width: 104.54px !important;
      }

      .datatable-body-cell:nth-child(3),
      .datatable-header-cell:nth-child(3) {
        width: 97.78 !important;
      }

      .datatable-body-cell:nth-child(4),
      .datatable-header-cell:nth-child(4) {
        width: 166.75px !important;
      }

      .datatable-body-cell:nth-child(5),
      .datatable-header-cell:nth-child(5) {
        width: 98px !important;
      }

      .datatable-body-cell:nth-child(6),
      .datatable-header-cell:nth-child(6) {
        width: 81px !important;
      }

      .datatable-body-cell:nth-child(7),
      .datatable-header-cell:nth-child(7) {
        width:71px !important;
      }

      .datatable-body-cell:nth-child(8),
      .datatable-header-cell:nth-child(8) {
        width: 92px !important;
      }

      .datatable-body-cell:nth-child(9),
      .datatable-header-cell:nth-child(9) {
        width: 80.571px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 76.1429px !important;
      }


      .datatable-body-cell:nth-child(11),
      .datatable-header-cell:nth-child(11) {
        width: 142.6px !important;
      }

      .datatable-body-cell:nth-child(12),
      .datatable-header-cell:nth-child(12) {
        width: 320px  !important;
      }
    }
  }
  .originalExpansion {
    .datatable-row-group,
    .datatable-row-center {
  
      .datatable-body-cell:nth-child(1),
      .datatable-header-cell:nth-child(1) {
        width: 109.571px !important;
      }

      .datatable-body-cell:nth-child(2),
      .datatable-header-cell:nth-child(2) {
        width: 117.615px !important;
      }

      .datatable-body-cell:nth-child(3),
      .datatable-header-cell:nth-child(3) {
        width: 150px !important;
      }

      .datatable-body-cell:nth-child(4),
      .datatable-header-cell:nth-child(4) {
        width: 187.615px !important;
      }

      .datatable-body-cell:nth-child(5),
      .datatable-header-cell:nth-child(5) {
        width: 98px !important;
      }

      .datatable-body-cell:nth-child(6),
      .datatable-header-cell:nth-child(6) {
        width: 56px !important;
      }

      .datatable-body-cell:nth-child(7),
      .datatable-header-cell:nth-child(7) {
        width:58px !important;
      }

      .datatable-body-cell:nth-child(8),
      .datatable-header-cell:nth-child(8) {
        width: 60px !important;
      }

      .datatable-body-cell:nth-child(9),
      .datatable-header-cell:nth-child(9) {
        width: 72.571px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 100.615px !important;
      }

      .datatable-body-cell:nth-child(10),
      .datatable-header-cell:nth-child(10) {
        width: 87.1429px !important;
      }
      .datatable-body-cell:nth-child(11),
      .datatable-header-cell:nth-child(11) {
        width: 87.1429px !important;
      }
     

      .datatable-body-cell:nth-child(12),
      .datatable-header-cell:nth-child(12) {
        width: 158.6px !important;
      }

      .datatable-body-cell:nth-child(13),
      .datatable-header-cell:nth-child(13) {
        width: 314px  !important;
      }
    }
  }
}

.bpg .upc-expansion-grid.ngx-datatable {
  font-size: 12px;

  .line-clamp {
    height: 52px;
    width: 105px;
    margin: 0 0 1em 0;
    overflow: hidden;
    span {
      height: 52px;
      -webkit-line-clamp: 3;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .datatable-header {
    padding: 5px 5px 5px 15px;
    background: #f0f4f7;

    .datatable-header-inner {
      font-size: $base-font-size;
      font-weight: bold;
    }
  }


  .datatable-body {
    max-height: 600px !important;
    overflow: auto;
    overflow-x: hidden;
    .datatable-body-row {
      padding: 18px 8px 18px 15px;
      border: 1px solid #dedede;
      border-top: none;

      .rank {
        cursor: pointer;
      }
    }
  }
  .datatable-footer .page-count {
    display: none;
  }
  input[type="checkbox"] {
    height: 18px;
    width: 18px;
  }
  input[type="checkbox"]:checked {
    bottom: 0;
    &:before {
      font-size: 13px;
    }
    &:checked {
      bottom: 0;
    }
  }
}


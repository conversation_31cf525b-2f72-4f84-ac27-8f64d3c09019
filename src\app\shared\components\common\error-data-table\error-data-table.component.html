<div *ngIf="errorIds" class="product-error-grid pr-5 pl-5">

    <div class="row">
        <div class="col-11">
            <h2>{{tableHeader}}</h2>
        </div>
        <div class="col-1 text-end mt-3">
            <span class="d-block ml-8">({{ errorIds.length }})</span>
        </div>
    </div>
    <ngx-datatable style="width:100%" class="material overlay-window" [rows]='errorIds' [columnMode]="'force'"
        [headerHeight]="60" [footerHeight]="80" [rowHeight]="auto">

        <ngx-datatable-column>
            <ng-template let-column="column" ngx-datatable-header-template>
                Original Value
                <span class="aui-icon aui-icon-small aui-iconfont-success ml-8 copy-text">
                    <svg class="icon cursor-pointer modal-icon" (click)="onCopyClick()">
                        <use xlink:href="assets/icons/modal.svg#asset"></use>
                    </svg>
                    <span class="ml-2">Copy List</span>
                </span>
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>
                {{row.invalidId}}
            </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column>
            <ng-template let-column="column" ngx-datatable-header-template>
                Corrected Value
            </ng-template>
            <ng-template let-row="row" ngx-datatable-cell-template>

                <input (input)="correctedValInput($event, row.invalidId)" type="text" autocomplete="off">
            </ng-template>
        </ngx-datatable-column>
    </ngx-datatable>

</div>

<ng-template #copyClipboardTmpl>
    <div class="modal-header border-bottom-0 pb-0">

        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body  pt-0 copy-to-clipboard-modal">
        <div class="row justify-content-center">
            <h6 class="modal-title  modal-title font-weight-bold">Copied To clipBoard</h6>
            <p class="mt-4 col-11 d-flex justify-content-center">
                <span *ngIf="!HHIds">{{errorCount}} UPC Product ID(s) were copied</span>
                <span *ngIf="HHIds">{{errorCount}} HHID(s) were copied</span>
            </p>

        </div>
        <div class="mt-62 row justify-content-center">
            <button type="button" class="btn btn-primary btn-sm col-sm-4" aria-label="Ok"
                (click)="redirectToProductGroupPg()">
                <span aria-hidden="true">Ok</span>
            </button>
        </div>
    </div>
</ng-template>
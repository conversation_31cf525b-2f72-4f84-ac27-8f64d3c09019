import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ErrorDataTableComponent } from './error-data-table.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ClipboardService } from 'ngx-clipboard';
import { TemplateRef } from '@angular/core';

describe('ErrorDataTableComponent', () => {
    let component: ErrorDataTableComponent;
    let fixture: ComponentFixture<ErrorDataTableComponent>;
    let modalService: BsModalService;
    let clipboardService: ClipboardService;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ ErrorDataTableComponent ],
            providers: [
                BsModalService,
                BsModalRef,
                ClipboardService
            ]
        })
        .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ErrorDataTableComponent);
        component = fixture.componentInstance;
        modalService = TestBed.inject(BsModalService);
        clipboardService = TestBed.inject(ClipboardService);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should open modal', () => {
        const template: TemplateRef<any> = {} as TemplateRef<any>;
        spyOn(modalService, 'show').and.callThrough();
        component.openModal(template, {});
        expect(modalService.show).toHaveBeenCalledWith(template, {});
    });

    it('should hide modal on redirectToProductGroupPg', () => {
        component.modalRef = modalService.show({} as TemplateRef<any>);
        spyOn(component.modalRef, 'hide').and.callThrough();
        component.redirectToProductGroupPg();
        expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should copy error IDs to clipboard on onCopyClick', () => {
        component.errorIds = [{ invalidId: '1' }, { invalidId: '2' }];
        spyOn(clipboardService, 'copyFromContent').and.callThrough();
        spyOn(component, 'openModal').and.callThrough();
        component.onCopyClick();
        expect(clipboardService.copyFromContent).toHaveBeenCalledWith('1,2');
        expect(component.errorCount).toBe(2);
        expect(component.openModal).toHaveBeenCalledWith(component['_copyClipboardTmpl'], { keyboard: true, class: 'modal-dialog-centered' });
    });

    it('should update correctedValObj on correctedValInput', () => {
        const event = { target: { value: 'newValue' } };
        const data = 'dataKey';
        spyOn(component.selectedValues, 'next').and.callThrough();
        component.correctedValInput(event, data);
        expect(component.correctedValObj[data]).toBe('newValue');
        expect(component.selectedValues.next).toHaveBeenCalledWith(component.correctedValObj);
    });

    it('should return corrected values', () => {
        component.correctedValObj = { key: 'value' };
        expect(component.getCorrectedValues()).toEqual({ key: 'value' });
    });
});
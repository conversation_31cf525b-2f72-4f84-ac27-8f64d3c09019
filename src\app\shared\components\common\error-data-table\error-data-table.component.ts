import { Component, EventEmitter, Input, OnChanges, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ClipboardService } from 'ngx-clipboard';

@Component({
  selector: 'error-data-table',
  templateUrl: './error-data-table.component.html',
  styleUrls: ['./error-data-table.component.scss']
})
export class ErrorDataTableComponent implements OnInit ,OnChanges {
  @Input() errorIds;
  @Input() HHIds;
  tableHeader = 'Error Correction';
  modalRef: BsModalRef;
  errorCount;
  @Output() selectedValues = new EventEmitter();
  @ViewChild('copyClipboardTmpl')
  private _copyClipboardTmpl: TemplateRef<any>;
  correctedValObj = {};
  constructor(private clipboardService: ClipboardService,private _modalService: BsModalService,) {
    // intentionally left empty
   }

  ngOnInit() {
    // intentionally left empty
  }
  ngOnChanges(): void {
    this.ngOnInit();
  }
  openModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
  }
  redirectToProductGroupPg(){
    if(this.modalRef){
      this.modalRef.hide();
    }
  }
  onCopyClick(){
    const count = this.errorIds.reduce((arr,item)=>{arr.push(item.invalidId);return arr;},[]).join(",");
    this.errorCount = this.errorIds.length;
    this.clipboardService.copyFromContent(count);
    this.openModal(this._copyClipboardTmpl, {  keyboard: true, class:  'modal-dialog-centered' });
  }

  correctedValInput(event,data){
     this.correctedValObj[data] = event.target.value;
     this.selectedValues.next(this.correctedValObj);
  }
  getCorrectedValues(){
    return this.correctedValObj;
  }
}

<div *ngIf="storeLocationBool; else homePage">
    <button  type="button" class="btn btn-secondary btn-sm my-1 text-capitalize" style="font-size:14px;border-radius: 16px;">
    <div *ngIf="facetChip; else noFacet">
          {{chip}}
    </div>
    <ng-template #noFacet>
        {{chip}}
    </ng-template>
    <span *ngIf = "!hideCloseIcon" class="chip-close-store" (click)="close()">✕</span>
    </button>
</div>

<ng-template #homePage>
  <div class="chip-wrapper">
    <div class="chip-container">
      <span class="chip-info"  [tooltip]='facetChipList'> {{facetChip}}</span>
    </div>
    <span>
      <span  *ngIf = "(!createStoreGroup && !xMarkShow && (chip!=='programCode') && hideCloseIconForPodView && hideCloseIconForPCcode)" class="chip-close" (click)="close($event,false)">✕</span>
    </span>
  </div>
</ng-template>


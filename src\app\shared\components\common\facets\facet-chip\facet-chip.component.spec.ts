import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FacetChipComponent } from './facet-chip.component';
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { MsalService } from '@azure/msal-angular';
import { AuthService } from '@appServices/common/auth.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { PermissionsObject, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BehaviorSubject } from 'rxjs';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonService } from '@appServices/common/common.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { FacetItemService } from "@appServices/common/facet-item.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { CONSTANTS } from "@appConstants/constants";
import * as moment from "moment";
import { ROUTES_CONST } from "@appConstants/routes_constants";

const featureFlagServiceStub = () => ({
    isFeatureEnabled: () => ({}),
    get isOfferArchivalEnabled() {
        return true;
    },
    get isOfferRequestArchivalEnabled() {
        return true;
    }
});

describe('FacetChipComponent', () => {
    let component: FacetChipComponent;
    let fixture: ComponentFixture<FacetChipComponent>;

    const authServiceStub = () => ({ onUserDataAvailable: () => ({}), getTokenString: () => ({}) });
    const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
    const initialDataServiceStub = () => ({
        getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
        getConfigUrls: () => ({})
    });
    const permissionsServiceStub = () => ({ 
        loadPermissions: () => ({}),
        getPermissions: () => ({}) 
    });
    const facetItemServiceStub = () => ({
        setOfferFilter: () => ({}),
        sortProperties: () => ({}),
        populateStoreFacet: () => ({}),
        getFacetItems: () => ({}),
        populateFacetSearch: () => ({}),
        populateFacet: () => ({}),
        getdivsionStateFacetItems: () => ({}),
        sortDivisionRogCds: () => ({}),
        getOfferFilter: () => ({}),
        getSearchFacetKeys: () => ({}),
        getSearchFacetKeysForOffer: () => ({}),
        retainProgramCodeSelected: () => ({}),
        get templateProgramCodeSelected() {
            return 'BPD';
        },
        clearLeftFilters$: new BehaviorSubject<any>({}),
        clearLeftFilters: function() { this.clearLeftFilters$.next({}); },
        chipComponent: {},
        get podView() {
            return true;
        },
        getTodayOption: () => ([]),
        chipCloseEvent$: new BehaviorSubject<any>({}),
        chipCloseEvent: {
            next: () => {}
        },
    });
    const baseInputSearchServiceStub = () => ({
        setActiveCurrentSearchType: () => ({}),
        createSubject: () => ({}),
        showChip: () => ({}),
        get currentRouter() {
            return {};
        },
        populateChipList: () => ({}),
        postDataForInputSearch: () => ({}),
        getActiveCurrentSearchType: () => ({}),
        clearLeftFilters$: new BehaviorSubject<any>({}),
        clearLeftFilters: function() { this.clearLeftFilters$.next({}); },
        commonSearchService: {
            clearLeftFilters$: new BehaviorSubject<any>({}),
        }
    });
    const searchOfferRequestServiceStub = () => ({
        fetchRegionsIds: () => ({}),
        mapRegionName: () => ({}),
        fetchProgramTypes: () => ({}),
        populateHomeFilterSearch: () => ({}),
        getOfferDetails: () => ({}),
        searchOfferRequest: () => ({
            subscribe: () => ({}),
            bind: () => ({}),
        }),
        getFacetCountsData: () => ({}),
        searchAllOfferRequest: () => ({
            subscribe: (f: (value: any) => void) => f({}),
            bind: () => ({}),
        }),
        currentOfferRequests: { subscribe: () => ({}) },
        paginationCriteria: () => ({}),
        myTasksObj: {
            myTasksText: ''
        }
    });
    const commonRouteServiceStub = () => ({ currentActivatedRoute: "request" });
    const commonServiceStub = () => ({ getHeaders: () => ({}), passPaginationData$: new BehaviorSubject({}) });
    const commonSearchServiceStub = () => ({
        batchActionActiveTab: {},
        setActiveCurrentSearchType: () => ({}),
        get currentRouter() {
            return {};
        },
        setAllFilterOptions: () => ({}),
        resetAllFilterOptions: () => ({}),
        getFilterOption: () => ({}),
        filterOption: {},
        assignFeatureFlag: () => ({}),
        isFeatureFlagEnabled: () => ({}),
        hasFlags: () => ({}),
        clearLeftFilters$: new BehaviorSubject<any>({}),
        get isArchivalEnabled() {
            return true;
        }
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                { provide: StoreGroupService, useValue: { populateStoreFilterSearch: () => ({}) } },
                { provide: AuthService, useFactory: authServiceStub },
                { provide: MsalService, useFactory: MsalServiceStub },
                { provide: InitialDataService, useFactory: initialDataServiceStub },
                { provide: PermissionsService, useFactory: permissionsServiceStub },
                { provide: CommonService, useFactory: commonServiceStub },
                { provide: CommonRouteService, useFactory: commonRouteServiceStub },
                { provide: CommonSearchService, useFactory: commonSearchServiceStub },
                { provide: SearchOfferRequestService, useFactory: searchOfferRequestServiceStub },
                { provide: FacetItemService, useFactory: facetItemServiceStub },
                { provide: FeatureFlagsService, useFactory: featureFlagServiceStub },
                { provide: BaseInputSearchService, useFactory: baseInputSearchServiceStub },
            ],
            declarations: [FacetChipComponent]
        }).compileComponents();
    });

    afterEach(() => {
        TestBed.resetTestingModule();
    });

    beforeEach(() => {
        spyOn(TestBed.inject(FacetItemService), 'getSearchFacetKeys').and.callThrough();
        fixture = TestBed.createComponent(FacetChipComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should return true for isReqAndBpd when facetpage is "home" and programCodeSelected is CONSTANTS.BPD', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeSelected = CONSTANTS.BPD;
        component.facetpage = 'home';
        expect(component.isReqAndBpd).toBeTrue();
    });

    it('should return false for isReqAndBpd when facetpage is not "home"', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeSelected = CONSTANTS.BPD;
        component.facetpage = 'otherPage';
        expect(component.isReqAndBpd).toBeFalse();
    });

    it('should return false for isReqAndBpd when programCodeSelected is not CONSTANTS.BPD', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeSelected = 'OTHER_CODE';
        component.facetpage = 'home';
        expect(component.isReqAndBpd).toBeFalse();
    });

    it('should not update facets for PLU when facetpage is not "pluManagement"', () => {
        component.facetpage = 'otherPage';
        const result = component.updateFacetsForPlu();
        expect(result).toBeFalse();
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Department"', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            departmentsWithCodes: { 'Dept1': 'Department 1', 'Dept2': 'Department 2' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Department';
        component.facetChip = '(Dept1) OR (Dept2)';
        component.updateFacetsForPlu();
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is not "Department"', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            divisions: { 'Div1': 'Division 1', 'Div2': 'Division 2' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Division';
        component.facetChip = '(Div1) OR (Div2)';
        component.updateFacetsForPlu();
    });

    it('should set facetChip correctly when facetChip is not defined', () => {
        component.facetChip = undefined;
        component.setFacetChip();
        expect(component.facetChip).toBeUndefined();
    });

    it('should set facetChip correctly for rangeDates chip without "/"', () => {
        component.chip = 'effectiveStartDate';
        component.facetChip = '(2021-01-01Z TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe('01/01/21-12/31/21');
    });

    it('should set facetChip correctly for non-rangeDates chip', () => {
        component.chip = 'someChip';
        component.facetChip = 'value1;value2';
        component.setFacetChip();
    });

    it('should set facetChipList correctly for specific facetpages', () => {
        component.facetpage = 'j4uOfferStoreTerminal';
        component.facetChip = 'someValue';
        component.setFacetChip();
        expect(component.facetChipList).toBe('someValue');
    });

    it('should set facetChipList correctly for other facetpages', () => {
        component.facetpage = 'otherPage';
        component.chip = 'someChip';
        component.facetChip = 'sgManagement';
        component.setFacetChip();
    });

    it('should prevent default for programCodePreventDefault when chip is "offerProgramCd" and only one program code is checked', () => {
        const event = { preventDefault: jasmine.createSpy('preventDefault') };
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeChecked = { 'code1': true };

        component.chip = 'offerProgramCd';
        const result = component.programCodePreventDefault(event);

        expect(event.preventDefault).toHaveBeenCalled();
        expect(result).toBeTrue();
    });

    it('should not prevent default for programCodePreventDefault when chip is "offerProgramCd" and more than one program code is checked', () => {
        const event = { preventDefault: jasmine.createSpy('preventDefault') };
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeChecked = { 'code1': true, 'code2': true };

        component.chip = 'offerProgramCd';
        const result = component.programCodePreventDefault(event);

        expect(event.preventDefault).not.toHaveBeenCalled();
        expect(result).toBeUndefined();
    });

    it('should not prevent default for programCodePreventDefault when chip is not "offerProgramCd"', () => {
        const event = { preventDefault: jasmine.createSpy('preventDefault') };
        component.chip = 'otherChip';
        const result = component.programCodePreventDefault(event);

        expect(event.preventDefault).not.toHaveBeenCalled();
        expect(result).toBeUndefined();
    });

    it('should emit facetChipClick event with correct parameters when close is called', () => {
        spyOn(component.facetChipClick, 'emit');
        const event = new Event('click');
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        component.close(event);

        expect(component.facetChipClick.emit).toHaveBeenCalledWith({
            facetChip: 'someFacetChip',
            chip: 'someChip',
            facetClose: 'someFacet',
            removed: false
        });
    });

    it('should call programCodePreventDefault and return false when it prevents default', () => {
        spyOn(component, 'programCodePreventDefault').and.returnValue(true);
        const event = new Event('click');
        const result = component.close(event);

        expect(component.programCodePreventDefault).toHaveBeenCalledWith(event);
        expect(result).toBeFalse();
    });

    it('should call storeGroupService.populateStoreFilterSearch when facetpage is "storeGroup"', () => {
        const storeGroupService = TestBed.inject(StoreGroupService);
        spyOn(storeGroupService, 'populateStoreFilterSearch');
        const event = new Event('click');
        component.facetpage = 'storeGroup';
        component.chip = 'someChip';
        component.facet = 'someFacet';

        component.close(event);

        expect(storeGroupService.populateStoreFilterSearch).toHaveBeenCalledWith({
            chip: 'someChip',
            facetClose: 'someFacet'
        });
    });

    it('should call _searchOfferRequestService.populateHomeFilterSearch when facetpage is not "storeGroup"', () => {
        const searchOfferRequestService = TestBed.inject(SearchOfferRequestService);
        spyOn(searchOfferRequestService, 'populateHomeFilterSearch');
        const event = new Event('click');
        component.facetpage = 'otherPage';
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        component.close(event);

        expect(searchOfferRequestService.populateHomeFilterSearch).toHaveBeenCalledWith({
            facetFilter: {
                facetChip: 'someFacetChip',
                chip: 'someChip',
                facetClose: 'someFacet'
            }
        });
    });

    it('should call facetItemService.setOfferFilter with correct parameters', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        spyOn(facetItemService, 'setOfferFilter');
        const event = new Event('click');
        component.facet = 'someFacet';

        component.close(event);

        expect(facetItemService.setOfferFilter).toHaveBeenCalledWith('someFacet');
    });

    it('should call facetItemService.chipCloseEvent$.next with correct parameters', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        spyOn(facetItemService.chipCloseEvent$, 'next');
        const event = new Event('click');
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        component.close(event);

        expect(facetItemService.chipCloseEvent$.next).toHaveBeenCalledWith({
            facetChip: 'someFacetChip',
            chip: 'someChip',
            facetClose: 'someFacet',
            removed: false
        });
    });

    it('should set facetChipList correctly in setChips when featureFlagService.isOfferRequestArchivalEnabled is true and facetpage is "home" and facetChipList contains "Status" and "Expired"', () => {
        const featureFlagService = TestBed.inject(FeatureFlagsService);
        spyOnProperty(featureFlagService, 'isOfferRequestArchivalEnabled', 'get').and.returnValue(true);
        const commonSearchService = TestBed.inject(CommonSearchService);
        component.facetpage = 'home';
        component.facetChip = { Status: 'Expired' };
        component.chip = 'Status';
        component.setChips();
        expect(commonSearchService.isShowExpiredInQuery).toBeTrue();
    });

    it('should set facetChipList correctly in setChips when featureFlagService.isOfferArchivalEnabled is true and facetpage is "offerHome" and facetChipList contains "Status" and "Expired"', () => {
        const featureFlagService = TestBed.inject(FeatureFlagsService);
        spyOnProperty(featureFlagService, 'isOfferArchivalEnabled', 'get').and.returnValue(true);
        const commonSearchService = TestBed.inject(CommonSearchService);
        component.facetpage = 'offerHome';
        component.facetChip = { Status: 'Expired' };
        component.chip = 'Status';
        component.setChips();
        expect(commonSearchService.isShowExpiredInQuery_O).toBeTrue();
    });

    it('should set facetChipList correctly in setChips when featureFlagService.isOfferRequestArchivalEnabled is false and facetpage is "home"', () => {
        const featureFlagService = TestBed.inject(FeatureFlagsService);
        spyOnProperty(featureFlagService, 'isOfferRequestArchivalEnabled', 'get').and.returnValue(false);
        component.facetpage = 'home';
        component.facetChip = { Status: 'Active' };
        component.chip = 'Status';
        component.setChips();
        expect(component.facetChipList).toBe('Status : Active');
    });

    it('should set facetChipList correctly in setChips when featureFlagService.isOfferArchivalEnabled is false and facetpage is "offerHome"', () => {
        const featureFlagService = TestBed.inject(FeatureFlagsService);
        spyOnProperty(featureFlagService, 'isOfferArchivalEnabled', 'get').and.returnValue(false);
        component.facetpage = 'offerHome';
        component.facetChip = { Status: 'Active' };
        component.chip = 'Status';
        component.setChips();
        expect(component.facetChipList).toBe('Status : Active');
    });

    it('should set facetChipList correctly in setChips for other facetpages', () => {
        component.facetpage = 'otherPage';
        component.facetChip = { someKey: 'someValue' };
        component.setChips();
        expect(component.facetChipList).toBe('someKey : someValue');
    });

    it('should set secureFacetCloseBasedOnPermissions to true when currentRoute is PodPlayground and ViewPodPlayGround permission is granted', () => {
        const mockPermissions: PermissionsObject = {
            [CONSTANTS.Permissions.ViewPodPlayGround]: {
                name: 'ViewPodPlayGround',
                validationFunction: async (name, store) => true
            }
        };
        const permissionsService = TestBed.inject(PermissionsService);
        spyOn(permissionsService, 'getPermissions').and.returnValue(mockPermissions);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.PodPlayground}`;
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeTrue();
    });

    it('should set secureFacetCloseBasedOnPermissions to true when currentRoute is Management and ViewOffers permission is granted', () => {
        const permissionsService = TestBed.inject(PermissionsService);
        spyOn(permissionsService, 'getPermissions').and.returnValue({
            [CONSTANTS.Permissions.ViewOffers]: {
                name: 'ViewOffers',
                validationFunction: async (name, store) => true
            }
        });
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeTrue();
    });

    it('should set secureFacetCloseBasedOnPermissions to true when currentRoute is Request and ViewOfferRequests permission is granted', () => {
        const permissionsService = TestBed.inject(PermissionsService);
        spyOn(permissionsService, 'getPermissions').and.returnValue({
            [CONSTANTS.Permissions.ViewOfferRequests]: {
                name: 'ViewOfferRequests',
                validationFunction: async (name, store) => true
            }
        });
        component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}`;
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeTrue();
    });

    it('should set secureFacetCloseBasedOnPermissions to true when currentRoute contains StoreGroup and ManageStoreGroups permission is granted', () => {
        const permissionsService = TestBed.inject(PermissionsService);
        spyOn(permissionsService, 'getPermissions').and.returnValue({
            [CONSTANTS.Permissions.ManageStoreGroups]: {
                name: 'ManageStoreGroups',
                validationFunction: async (name, store) => true
            }
        });
        component.currentRoute = `/${ROUTES_CONST.GROUPS.StoreGroup}/somePath`;
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeTrue();
    });

    it('should set secureFacetCloseBasedOnPermissions to true when currentRoute contains ProductGroup and ManageProductGroups permission is granted', () => {
        const permissionsService = TestBed.inject(PermissionsService);
        spyOn(permissionsService, 'getPermissions').and.returnValue({
            [CONSTANTS.Permissions.ManageProductGroups]: {
                name: 'ManageProductGroups',
                validationFunction: async (name, store) => true
            }
        });
        component.currentRoute = `/${ROUTES_CONST.GROUPS.ProductGroup}/somePath`;
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeTrue();
    });

    it('should set secureFacetCloseBasedOnPermissions to true when currentRoute is PluManagement', () => {
        component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluManagement}`;
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeTrue();
    });

    it('should set secureFacetCloseBasedOnPermissions to false when no matching route or permissions are found', () => {
        const permissionsService = TestBed.inject(PermissionsService);
        spyOn(permissionsService, 'getPermissions').and.returnValue({});
        component.currentRoute = '/some/other/route';
        component.secureFacetCloseByUserPermissions();
        expect(component.secureFacetCloseBasedOnPermissions).toBeFalse();
    });

    it('should call setChips when ngOnChanges is called and programCodeSelected is BPD and currentRouter is REQUEST', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        const baseInputSearchService = TestBed.inject(BaseInputSearchService);
        spyOn(component, 'setChips');
        spyOn(component, 'setFacetChip');
        facetItemService.programCodeSelected = CONSTANTS.BPD;
        Object.defineProperty(baseInputSearchService, 'currentRouter', { value: CONSTANTS.REQUEST });

        component.ngOnChanges();

        expect(component.setChips).toHaveBeenCalled();
        expect(component.setFacetChip).not.toHaveBeenCalled();
    });

    it('should call setChips when ngOnChanges is called and currentRouter is in specific list', () => {
        const baseInputSearchService = TestBed.inject(BaseInputSearchService);
        spyOn(component, 'setChips');
        spyOn(component, 'setFacetChip');
        Object.defineProperty(baseInputSearchService, 'currentRouter', { value: CONSTANTS.TEMPLATE });

        component.ngOnChanges();

        expect(component.setChips).toHaveBeenCalled();
        expect(component.setFacetChip).not.toHaveBeenCalled();
    });

    it('should call setFacetChip when ngOnChanges is called and conditions are not met for setChips', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        const baseInputSearchService = TestBed.inject(BaseInputSearchService);
        spyOn(component, 'setChips');
        spyOn(component, 'setFacetChip');
        facetItemService.programCodeSelected = 'OTHER_CODE';
        spyOnProperty(baseInputSearchService, 'currentRouter', 'get').and.returnValue('OTHER_ROUTER');

        component.ngOnChanges();

        expect(component.setFacetChip).toHaveBeenCalled();
        expect(component.setChips).not.toHaveBeenCalled();
    });

    it('should emit facetChipClick event and return when facetpage is in specific list', () => {
        spyOn(component.facetChipClick, 'emit');
        const event = new Event('click');
        component.facetpage = 'template';
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        component.close(event);

        expect(component.facetChipClick.emit).toHaveBeenCalledWith({
            facetChip: 'someFacetChip',
            chip: 'someChip',
            facetClose: 'someFacet',
            removed: false
        });
    });

    it('should emit facetChipClick event and return when isReqAndBpd is true', () => {
        spyOn(component.facetChipClick, 'emit');
        const event = new Event('click');
        component.facetpage = 'home';
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeSelected = CONSTANTS.BPD;
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        component.close(event);

        expect(component.facetChipClick.emit).toHaveBeenCalledWith({
            facetChip: 'someFacetChip',
            chip: 'someChip',
            facetClose: 'someFacet',
            removed: false
        });
    });
    it('should emit facetChipClick event with isClearAll_LeftFilter true when chip is not in excludedChipsArr', () => {
        const baseInputSearchService = TestBed.inject(BaseInputSearchService);
        spyOn(component.facetChipClick, 'emit');
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        baseInputSearchService.commonSearchService.clearLeftFilters$.next({ excludedChipsArr: ['otherChip'] });

        expect(component.facetChipClick.emit).toHaveBeenCalledWith({
            facetChip: 'someFacetChip',
            chip: 'someChip',
            facetClose: 'someFacet',
            removed: false,
            isClearAll_LeftFilter: true
        });
    });

    it('should not emit facetChipClick event when chip is in excludedChipsArr', () => {
        const baseInputSearchService = TestBed.inject(BaseInputSearchService);
        spyOn(component.facetChipClick, 'emit');
        component.chip = 'someChip';
        component.facet = 'someFacet';
        component.facetChip = 'someFacetChip';

        baseInputSearchService.commonSearchService.clearLeftFilters$.next({ excludedChipsArr: ['someChip'] });

        expect(component.facetChipClick.emit).not.toHaveBeenCalled();
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Division" with multiple values', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            divisions: { 'Div1': 'Division 1', 'Div2': 'Division 2' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Division';
        component.facetChip = '(Div1) OR (Div2)';
        component.updateFacetsForPlu();
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Department" with single value', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            departmentsWithCodes: { 'Dept1': 'Department 1' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Department';
        component.facetChip = '(Dept1)';
        component.updateFacetsForPlu();
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Division" with single value', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            divisions: { 'Div1': 'Division 1' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Division';
        component.facetChip = '(Div1)';
        component.updateFacetsForPlu();
    });

    it('should set facetChip correctly when chip is in rangeDates and facetChip does not contain "/"', () => {
        component.chip = 'effectiveStartDate';
        component.facetChip = '(2021-01-01Z TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe('01/01/21-12/31/21');
    });

    it('should not modify facetChip when chip is not in rangeDates and facetChip does not contain ";"', () => {
        component.chip = 'someChip';
        component.facetChip = 'value1,value2';
        component.setFacetChip();
        expect(component.facetChip).toBe('value1,value2');
    });

    it('should set endDate correctly when chip is "endDt" and facetChip does not contain "/"', () => {
        component.chip = 'endDt';
        component.facetChip = '(2021-01-01Z TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe('01/01/21-12/30/21');
    });

    it('should set endDate correctly when chip is "endDt" and facetChip contains a valid date range', () => {
        component.chip = 'endDt';
        component.facetChip = '(2021-01-01Z TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe('01/01/21-12/30/21');
    });

    it('should set endDate correctly when chip is "endDt" and facetChip contains an invalid date range', () => {
        component.chip = 'endDt';
        component.facetChip = '( TO )';
        component.setFacetChip();
    });

    it('should set endDate correctly when chip is "endDt" and facetChip contains only start date', () => {
        component.chip = 'endDt';
        component.facetChip = '(2021-01-01Z TO )';
        component.setFacetChip();
    });

    it('should set endDate correctly when chip is "endDt" and facetChip contains only end date', () => {
        component.chip = 'endDt';
        component.facetChip = '( TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe('*-12/30/21');
    });

    it('should return true for hideCloseIconForPodView when podView is true and chip is "digital"', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        spyOnProperty(facetItemService, 'podView', 'get').and.returnValue(true);
        component.chip = 'digital';
    });

    it('should return false for hideCloseIconForPodView when podView is false', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        Object.defineProperty(facetItemService, 'podView', { value: false });
        component.chip = 'digital';
    });

    it('should return false for hideCloseIconForPodView when chip is not "digital"', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        spyOnProperty(facetItemService, 'podView', 'get').and.returnValue(true);
        component.chip = 'otherChip';
    });

    it('should return true for hideCloseIconForPCcode when chip is "offerProgramCd" and only one program code is selected', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeChecked = { 'code1': true };
        component.chip = 'offerProgramCd';
    });

    it('should return false for hideCloseIconForPCcode when chip is "offerProgramCd" and more than one program code is selected', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeChecked = { 'code1': true, 'code2': true };
        component.chip = 'offerProgramCd';
    });

    it('should return false for hideCloseIconForPCcode when chip is not "offerProgramCd"', () => {
        const facetItemService = TestBed.inject(FacetItemService);
        facetItemService.programCodeChecked = { 'code1': true };
        component.chip = 'otherChip';
    });

    it('should set facetChip correctly when chip is not in rangeDates and facetChip contains ";"', () => {
        component.chip = 'someChip';
        component.facetChip = 'value1;value2';
        component.setFacetChip();
        expect(component.facetChip).toBe('value1;value2');
    });

    it('should remove trailing comma from facetChip when chip is not in rangeDates and facetChip contains ";"', () => {
        component.chip = 'someChip';
        component.facetChip = 'value1;value2;';
        component.setFacetChip();
    });

    it('should set startDate and endDate correctly when chip is "createTimeStamp" and facetChip does not contain "/"', () => {
        component.chip = 'createTimeStamp';
        component.facetChip = '(2021-01-01Z TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe(`${convertUTCToLocalDateWithoutTZ('2021-01-01')}-${convertUTCToLocalDateWithoutTZ('2021-12-31')}`);
    });

    it('should set startDate and endDate correctly when chip is "lastUpdateTimestamp" and facetChip does not contain "/"', () => {
        component.chip = 'lastUpdateTimestamp';
        component.facetChip = '(2021-01-01Z TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe(`${convertUTCToLocalDateWithoutTZ('2021-01-01')}-${convertUTCToLocalDateWithoutTZ('2021-12-31')}`);
    });

    it('should set startDate to "*" when chip is "createTimeStamp" and start date is empty', () => {
        component.chip = 'createTimeStamp';
        component.facetChip = '( TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe(`*-${convertUTCToLocalDateWithoutTZ('2021-12-31')}`);
    });

    it('should set endDate to "*" when chip is "createTimeStamp" and end date is empty', () => {
        component.chip = 'createTimeStamp';
        component.facetChip = '(2021-01-01Z TO )';
        component.setFacetChip();
        expect(component.facetChip).toBe(`${convertUTCToLocalDateWithoutTZ('2021-01-01')}- *`);
    });

    it('should set startDate to "*" when chip is "lastUpdateTimestamp" and start date is empty', () => {
        component.chip = 'lastUpdateTimestamp';
        component.facetChip = '( TO 2021-12-31Z)';
        component.setFacetChip();
        expect(component.facetChip).toBe(`*-${convertUTCToLocalDateWithoutTZ('2021-12-31')}`);
    });

    it('should set endDate to "*" when chip is "lastUpdateTimestamp" and end date is empty', () => {
        component.chip = 'lastUpdateTimestamp';
        component.facetChip = '(2021-01-01Z TO )';
        component.setFacetChip();
        expect(component.facetChip).toBe(`${convertUTCToLocalDateWithoutTZ('2021-01-01')}- *`);
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Department" with multiple values', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            departmentsWithCodes: { 'Dept1': 'Department 1', 'Dept2': 'Department 2' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Department';
        component.facetChip = '(Dept1) OR (Dept2)';
        component.updateFacetsForPlu();
    });

    it('should set startDate and endDate to today\'s date when facetChip is "Today"', () => {
        component.chip = 'effectiveStartDate';
        component.facetChip =  '(Today TO 2025-12-31Z)';
        component.setFacetChip();
        const todayStart = moment().startOf('day').format('MM/DD/YY');
        const todayEnd = moment().endOf('day').format('MM/DD/YY');
        expect(component.facetChip).toBe(`12/31/25-12/31/25`);
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Department to cover condition"', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            departmentsWithCodes: { 'Dept1': 'Department 1', 'Dept2': 'Department 2' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Department';
        component.facetSearchKey = {'Department': 'Department'};
        component.facetChip = '(Dept1) OR (Dept2)';
        component.updateFacetsForPlu();
    });

    it('should update facets for PLU when facetpage is "pluManagement" and chip is "Division to cover condition"', () => {
        const initialDataService = TestBed.inject(InitialDataService);
        spyOn(initialDataService, 'getAppData').and.returnValue({
            divisions: { 'Div1': 'Division 1', 'Div2': 'Division 2' },
            departmentsWithCodes: { 'Dept1': 'Department 1', 'Dept2': 'Department 2' }
        });

        component.facetpage = 'pluManagement';
        component.chip = 'Division';
        component.facetSearchKey = {'Division': 'Division'};
        component.facetChip = '(Dept1) OR (Dept2)';
        component.updateFacetsForPlu();
    });
});
function convertUTCToLocalDateWithoutTZ(dateString: string): string {
    const date = moment.utc(dateString).local();
    return date.format('MM/DD/YY');
}

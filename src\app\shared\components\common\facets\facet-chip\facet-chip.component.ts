import * as moment from "moment";

import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from "@angular/core";

import { Router } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { convertUTCToLocalDateWithoutTZ } from "@appUtilities/date.utility";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
@Component({
  selector: "facet-chip",
  templateUrl: "./facet-chip.component.html",
  styleUrls: ["./facet-chip.component.scss"],
})
export class FacetChipComponent extends UnsubscribeAdapter implements OnInit, OnChanges {
  storeLocationBool: boolean;
  @Input() hideCloseIcon: boolean;
  @Input() facetChip;
  @Output() facetChipClick = new EventEmitter();
  @Input() facetpage;
  @Input() createStoreGroup;
  @Input() xMarkShow;
  @Input() chip;
  @Input() facet;
  facetChipList;
  facetSearchKey;
  rangeDates = [
    "effectiveStartDate",
    "createTimeStamp",
    "lastUpdateTimestamp",
    "startDt",
    "endDt",
    "promotionStartDt",
    "effectiveEndDate",
    "startDate",
  ];

  secureFacetCloseBasedOnPermissions = false;
  currentRoute = this.router.url;
  constructor(
    private storeGroupService: StoreGroupService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private _initialDataService: InitialDataService,
    private facetItemService: FacetItemService,
    private _permissionsService: PermissionsService,
    private router: Router, 
    private baseInputSearchService:BaseInputSearchService,
    private featureFlagService:FeatureFlagsService,
    private commonSearchService: CommonSearchService
  ) {
    super();
    this.facetSearchKey = this.facetItemService.getSearchFacetKeys();
    this.facetSearchKey.userId = "User Id";
  }

  get isReqAndBpd(){
     return this.facetpage === "home" && this.facetItemService.programCodeSelected === CONSTANTS.BPD;
  }

  ngOnInit() {
    this.initSubscribes();
    const pcSelected = this.facetItemService.programCodeSelected;

    const isTemplateOrPgPage  = [CONSTANTS.TEMPLATE, CONSTANTS.PRODUCTMANAGEMENT, CONSTANTS.ACTION_LOG, 
                                CONSTANTS.IMPORT_LOG_BPD, CONSTANTS.STOREMANAGEMENT].indexOf(this.baseInputSearchService.currentRouter) > -1;
    const isReqAndBpd = this.facetpage === "home" && pcSelected === CONSTANTS.BPD;
   
    if(!(isTemplateOrPgPage || isReqAndBpd)) {

      this.facetItemService.chipComponent[this.chip] = this;
      this.facetpage === "storeLocation" ? (this.storeLocationBool = true) : (this.storeLocationBool = false);
      const todaysOptions = this.facetItemService.getTodayOption() || [];
      const chipParam = todaysOptions.filter((ele) => ele.chip === this.chip);
      
      if (!(chipParam && chipParam.length) && this.rangeDates.indexOf(this.chip) !== -1 && this.facetChip.indexOf("/") === -1) {
        
        const endDateValue = this.facetChip.split(" TO")[1]?.slice(0, this.facetChip.split(" TO")[1]?.length - 1);
        let startDate =
            this.facetChip.split(" TO")[0]?.slice(1)?.trim().length > 0
              ? moment(this.facetChip.split("Z")[0].slice(1)).startOf("date").format("MM/DD/YY")
              : "*",
          endDate = endDateValue?.trim()?.length
            ? moment(endDateValue.split("Z")[0])
                .startOf("date")
                .format("MM/DD/YY")
            : " *";
          if (this.facetChip === "Today") {
            startDate = moment().startOf("date").format("MM/DD/YY")
            endDate = moment().endOf("date").format("MM/DD/YY")
          }
        this.facetChip = `${startDate}-${endDate}`;
      }

    }
    
    this.secureFacetCloseByUserPermissions();
  }

  updateFacetsForPlu(){
    if (this.facetpage !== "pluManagement"){
      return false;
    }
    const selectedChip = this.facetSearchKey[this.chip];

    if(["Department"].indexOf(selectedChip) > -1) {
      let formattedChipVal = this.facetChip.split(" OR "),
        arr = [];
      
      formattedChipVal.forEach((item) => {
        item = item.replaceAll("(", "");
        item = item.replaceAll(")", "");
        
        let data;
        if(selectedChip === "Department"){
          data =  this._initialDataService.getAppData().departmentsWithCodes[item];
        }else if(selectedChip === "Division"){
          data =  this._initialDataService.getAppData().divisions[item];
        }
       
        arr.push(data);
      });
      this.facetChip = arr.join("  ;  ");
    }
  }

  setFacetChip() {
    if (!this.facetChip) {
      return;
    }
    const todaysOptions = this.facetItemService.getTodayOption() || [];
    const chipParam = todaysOptions.filter((ele) => ele.chip === this.chip);
    
    this.updateFacetsForPlu();
    

    if (!(chipParam && chipParam.length) && this.rangeDates.indexOf(this.chip) !== -1 && this.facetChip.indexOf("/") === -1) {
      const endDateValue = this.facetChip.split(" TO")[1].slice(0, this.facetChip.split(" TO")[1].length - 1);
      let startDate,endDate;
      if(this.chip == 'createTimeStamp' || this.chip == "lastUpdateTimestamp") {
        startDate =this.facetChip.split(" TO")[0].slice(1).trim().length > 0
          ? convertUTCToLocalDateWithoutTZ(this.facetChip.split("Z")[0].slice(1))
          : "*";
        endDate = endDateValue.trim().length > 0
          ? convertUTCToLocalDateWithoutTZ(endDateValue.split("Z")[0])
          : " *";
      } else {
      startDate =
        this.facetChip.split(" TO")[0].slice(1).trim().length > 0
          ? moment(this.facetChip.split("Z")[0].slice(1)).startOf("date").format("MM/DD/YY")
          : "*";
      endDate =
        endDateValue.trim().length > 0
          ? moment(endDateValue.split("Z")[0])
              .startOf("date")
              .format("MM/DD/YY")
          : " *";
      if (this.chip === "endDt") {
      endDate = `${moment(endDateValue).utc().subtract(1, "days").startOf("date").format("MM/DD/YY")}`;
      }
    }
      this.facetChip = `${startDate}-${endDate}`;
    } else if (!this.rangeDates.indexOf(this.chip)) {
      this.facetChip = this.facetChip.split(";").join(",");
      if (this.facetChip.indexOf(",", this.facetChip.length - 1) !== -1) {
        this.facetChip = this.facetChip.slice(0, this.facetChip.length - 1);
      }
    }

    this.facetChipList =
      this.facetpage === "j4uOfferStoreTerminal" ||
      this.facetpage === "createProductGroup" ||
      this.facetpage === "copientOfferStoreTerminal" ||
      this.facetpage === "offerStoreTerminal" ||
      this.facetpage === "offerLeftNav" ||
      this.facetpage === "offerEventName"
        ? `${this.facetChip}`
        : `${this.facetSearchKey[this.chip]} : ${this.facetChip}`;
  } 
  ngOnChanges() {
     if((this.facetItemService.programCodeSelected === CONSTANTS.BPD && 
      this.baseInputSearchService.currentRouter===CONSTANTS.REQUEST) || 
      ([CONSTANTS.TEMPLATE, CONSTANTS.PRODUCTMANAGEMENT, CONSTANTS.ACTION_LOG, CONSTANTS.IMPORT_LOG_BPD, CONSTANTS.STOREMANAGEMENT].indexOf(this.baseInputSearchService.currentRouter) > -1)) {
       this.setChips();
     }else{
      this.setFacetChip();
     }
  }
  setChips(){
    this.facetChipList = `${Object.keys(this.facetChip)[0]} : ${Object.values(this.facetChip)[0]}`;
    if(this.featureFlagService.isOfferRequestArchivalEnabled && this.facetpage == 'home' && this.facetChipList.indexOf("Status") > -1 && this.facetChipList.indexOf("Expired") > -1 )
    {
      this.commonSearchService.isShowExpiredInQuery =  (this.facetChipList.indexOf("Expired") > -1);
    }
    if(this.featureFlagService.isOfferArchivalEnabled && this.facetpage == 'offerHome' && this.facetChipList.indexOf("Status") > -1 && this.facetChipList.indexOf("Expired") > -1 )
    {
      this.commonSearchService.isShowExpiredInQuery_O = (this.facetChipList.indexOf("Expired") > -1);
    }
    this.facetChip = Object.values(this.facetChip)[0];
  }
  get hideCloseIconForPodView(){
    return !(this.facetItemService.podView && this.chip === 'digital');
  }
  get hideCloseIconForPCcode() {
    /** 
    *params  none
    *this getter function is used to hide close icon for chip if offer program code selected is 1
    **/

    const pcCheckedObj = this.facetItemService.programCodeChecked;
    const offerProgramCdSelected = pcCheckedObj && Object.values(pcCheckedObj).filter(programCode=>programCode).length;
    return !(this.chip === "offerProgramCd" && offerProgramCdSelected == 1);
  }
  programCodePreventDefault(event){
    if(this.chip === "offerProgramCd"){
      const checkProgramCode = Object.values(this.facetItemService.programCodeChecked).filter(programCode=>programCode);
      if(checkProgramCode.length ===1){
        event.preventDefault();
        return true;
      }else{
        this.facetItemService.retainProgramCodeSelected();
      }
    }
  }

  initSubscribes(){
    this.subs.sink = this.baseInputSearchService.commonSearchService.clearLeftFilters$.subscribe((obj:any)=>{    
      //Clear All functionality for Left side filters  
      const { chip, facetClose, facetChip } = { chip: this.chip, facetClose: this.facet, facetChip: this.facetChip };
      const {excludedChipsArr} =    obj ; 
      
      if(!excludedChipsArr.includes(chip)){
        this.facetChipClick.emit({ facetChip, chip, facetClose, removed:false,isClearAll_LeftFilter: true });     
      }     
    });
  }
  close(event,removed = false) {
    const { chip, facetClose, facetChip } = { chip: this.chip, facetClose: this.facet, facetChip: this.facetChip };
    this.facetItemService.setOfferFilter(facetClose);
    
    if(["template","baseProductGroup", "pgManagement", "action-log","import-log-bpd","sgManagement"].includes(this.facetpage) || 
       this.isReqAndBpd){
      this.facetChipClick.emit({ facetChip, chip, facetClose, removed });
      return;
    }
    
    const preventDefault =  this.programCodePreventDefault(event);
    if(preventDefault){
      return false;
    }

    if (this.facetpage === "storeGroup") {
      this.storeGroupService.populateStoreFilterSearch({ chip, facetClose });
    } else {
      this._searchOfferRequestService.populateHomeFilterSearch({ facetFilter: { facetChip, chip, facetClose } });
    }

    this.facetItemService.chipCloseEvent$.next({ facetChip, chip, facetClose, removed })
    this.facetChipClick.emit({ facetChip, chip, facetClose, removed });
  }

  secureFacetCloseByUserPermissions() {
    const permissions = this._permissionsService.getPermissions();
    
    if (this.currentRoute === `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.PodPlayground}`) {
      if (permissions[CONSTANTS.Permissions.ViewPodPlayGround]) {
        this.secureFacetCloseBasedOnPermissions = true;
      }
    } else if (this.currentRoute === `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`) {
      if (permissions[CONSTANTS.Permissions.ViewOffers]) {
        this.secureFacetCloseBasedOnPermissions = true;
      }
    } else if (this.currentRoute === `/${ROUTES_CONST.REQUEST.Request}`) {
      if (permissions[CONSTANTS.Permissions.ViewOfferRequests]) {
        this.secureFacetCloseBasedOnPermissions = true;
      }
    } else if (this.currentRoute.indexOf(ROUTES_CONST.GROUPS.StoreGroup) > -1) {
      if (permissions[CONSTANTS.Permissions.ManageStoreGroups]) {
        this.secureFacetCloseBasedOnPermissions = true;
      }
    } else if (this.currentRoute.indexOf(ROUTES_CONST.GROUPS.ProductGroup) > -1) {
      if (permissions[CONSTANTS.Permissions.ManageProductGroups]) {
        this.secureFacetCloseBasedOnPermissions = true;
      }
    } else if (this.currentRoute === `/${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.PluManagement}`) {
        this.secureFacetCloseBasedOnPermissions = true;
    } else {
      this.secureFacetCloseBasedOnPermissions = false;
    } 
  }
}

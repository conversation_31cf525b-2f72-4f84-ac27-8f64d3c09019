<div *ngIf="facetChipItems" class="chips-wrapper">
  <div *ngIf="key(facetSearch).length" class="categories-chip">
     
      <div class="d-flex flex-row flex-wrap">
          <span class="pt-2" *ngIf="!isHideSearchLabel">Search</span>
          <ng-container *ngFor="let item of key(facetSearch); trackBy: trackByFn;index as i">
              <facet-chip [createStoreGroup] ="createStoreGroup" [chip]='item' [facetChip] = 'facetSearch[item]'
               [facet]= "'facetSearch'" (facetChipClick)="onFacetChipClick($event)" [facetpage] ='facetPage'></facet-chip>
            </ng-container>
            <div class="ml-3 align-self-center">
                <a *ngIf="isDisplayClearLink({chipCount:key(facetSearch).length})" href="javascript:void(0)" (click)="clearChips()" 
                class="align-items-center font-weight-bold">Clear</a>
            </div>
            
        </div>
     
  </div>
  <div *ngIf="key(facetFilter).length" class="categories-chip">
     
      <div class="d-flex flex-row flex-wrap">
          <span class="pt-2">Filters</span>
          <ng-container *ngFor="let item of key(facetFilter); trackBy: trackByFn;index as i">
              <facet-chip [createStoreGroup] ="createStoreGroup" [chip]='item' [facetChip] = 'facetFilter[item]' 
              [facet]= "'facetFilter'" (facetChipClick)="onFacetChipClick($event)" [facetpage] ='facetPage'></facet-chip>
            </ng-container>
            <div class="ml-3 align-self-center">
                <a *ngIf="isDisplayClearLink({chipCount: key(facetFilter).length})" href="javascript:void(0)" (click)="clearChipsForLeftFilters()" 
                class="align-items-center font-weight-bold">Clear</a>
            </div>
      </div>
      
  </div>

  <div *ngIf="key(rogsFilterForUpc).length && facetPage ==='createProductGroup'" class="categories-chip">
     
    <div class="d-flex flex-row flex-wrap">
        <ng-container *ngFor="let item of key(rogsFilterForUpc); trackBy: trackByFn;index as i">
            <facet-chip [xMarkShow] ="xMarkShow" [createStoreGroup] ="createStoreGroup" [chip]='item' [facetChip] = 'rogsFilterForUpc[item]' [facet]= "'rogsFilterForUpc'" (facetChipClick)="onFacetChipClick($event)" [facetpage] ='facetPage'></facet-chip>
          </ng-container>
    </div>
    
</div>

  <div *ngIf="key(copientStoreTerminalFacetFilter).length && facetPage ==='copientOfferStoreTerminal'" class="categories-chip">
     
        <div class="d-flex flex-row flex-wrap">
            <ng-container *ngFor="let item of key(copientStoreTerminalFacetFilter); trackBy: trackByFn;index as i">
                <facet-chip [createStoreGroup] ="createStoreGroup" [chip]='copientStoreTerminalFacetFilter[item]' [facetChip] = 'copientStoreTerminalFacetFilter[item]' [facet]= "'copientStoreTerminalFacetFilter'" (facetChipClick)="onFacetChipClick($event)" [facetpage] ='facetPage'></facet-chip>
              </ng-container>
        </div>
        
    </div>
    <div *ngIf="key(j4uStoreTerminalFacetFilter).length && facetPage ==='j4uOfferStoreTerminal'" class="categories-chip">
     
            <div class="d-flex flex-row flex-wrap">
                <ng-container *ngFor="let item of key(j4uStoreTerminalFacetFilter); trackBy: trackByFn;index as i">
                    <facet-chip [createStoreGroup] ="createStoreGroup" [chip]='j4uStoreTerminalFacetFilter[item]' [facetChip] = 'j4uStoreTerminalFacetFilter[item]' [facet]= "'j4uStoreTerminalFacetFilter'" (facetChipClick)="onFacetChipClick($event)" [facetpage] ='facetPage'></facet-chip>
                  </ng-container>
            </div>
            
        </div>
 
</div>

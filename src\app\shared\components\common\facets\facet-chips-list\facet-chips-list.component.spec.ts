import { NO_ERRORS_SCHEMA } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";

import { of, Subject } from "rxjs";
import { FacetChipsListComponent } from "./facet-chips-list.component";

import { RouterTestingModule } from "@angular/router/testing";
import { SuggestedUPCFilterService } from "@appGroup/core/product-groups/services/suggested-upcfilter.service";
import { CommonGroupService } from "@appGroupsServices/common-group.service";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { FacetItem } from "@appModels/offer-request.model";
import { OfferMappingService } from "@appOffersServices/offer-mapping.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { CONSTANTS } from "@appConstants/constants";




describe("FacetChipsListComponent", () => {
  let component: FacetChipsListComponent;
  let fixture: ComponentFixture<FacetChipsListComponent>;
  beforeEach(() => {
   
    const baseInputSearchServiceStub = () => ({
      getDataForInputSearch: () => ({subscribe: () => ({})}),
      inputSearchOptions: [],
      getInputFieldSelected: ()=>({}),
      generateQueryForOptions: () => ({}),
      formQuery: () => ({}),
      formQueryWithFilter: () => ({}),
      setInputSearchOptions: () => ({}),
      updateCategoryChipSavedSearch:()=> ({subscribe: () => ({})}),
      postDataForInputSearch: () => ({}),
      getActiveCurrentSearchType: () => ({}),
      getInputSearchOptions: () => ({}),
    });
    
    const facetItemServiceStub = () => ({
      getOfferFilter: () => ({}),
      setOfferFilter: (facetClose) => ({}),
      getSearchFacetFields: () => ({ includes: () => ({}) }),
      getFilterFacetFields: () => ({ includes: () => ({}) }),
      getTodayOption: () => ({ filter: () => ({}) }),
      programCodeSelected: 'GR',
    });

    const offerMappingServiceStub = () => ({
      copientSelectSearchSourceSearch: { subscribe: () => ({}) },
      j4uSelectSearchSourceSourceSearch: { subscribe: () => ({}) },
    });
    const queryGeneratorStub = {
      setQuery: (string) => ({}),
      getQuery: () => ({}),
      pushParameters: (object) => ({}),
      getQueryWithFilter: () => ({}),
    };
    const searchOfferRequestServiceStub = () => ({
      homeFilterSearchSourceSearch: { subscribe: () => ({}) },
    });
    const storeGroupServiceStub = () => ({
      storeFilterSearchSourceSearch: { subscribe: () => ({}) },
    });

    const commonGroupServiceStub = () => ({ 
      onSearchInput: object => ({}), 
      getPopulateChipForRogsUpcExpansion: () => of({}) // Return an observable
    });

    const suggestedUPCFilterServiceStub = () => ({
      resetFilter: () => ({}),
      clearUpcFilter:() =>({}),
      clearAllFilters: () => ({}),
      upcFilterChipData$: new Subject<any>(), // Initialize upcFilterChipData$
    });

    const commonSearchServiceStub = () => ({
      batchActionActiveTab: {},
      setActiveCurrentSearchType: currentRouter => ({}),
      currentRouter: {},
      setAllFilterOptions: object => ({}),
      setFilters: object => ({}),
      resetAllFilterOptions: object => ({}),
      clearLeftFilters$: { next: () => ({}) },
      getFilterOptions: () => ({}),
      setIsHideExpiredStatusReqs: () => ({}),
      setQueryOptionsForBpd: () => ({}),
      getInputSearchChip: () => ({}),
      getFacetFilterChip: () => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
      getConfigUrls: bATCHIMPORT_TEMPLATE_FILE_API => ({}),
      getAppDataName: () => ({}),
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({}),
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      imports:[RouterTestingModule],
      declarations: [FacetChipsListComponent],
      providers: [
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: OfferMappingService, useFactory: offerMappingServiceStub },
        { provide: SearchOfferRequestService, useFactory: searchOfferRequestServiceStub  },
        { provide: QueryGenerator, useValue: queryGeneratorStub },
        { provide: StoreGroupService, useFactory: storeGroupServiceStub },
        { provide: BaseInputSearchService, useFactory: baseInputSearchServiceStub },
        { provide: CommonGroupService, useFactory: commonGroupServiceStub },
        { provide: SuggestedUPCFilterService, useFactory: suggestedUPCFilterServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: FeatureFlagsService,useFactory: featureFlagServiceStub}
       
      ],
    });
    fixture = TestBed.createComponent(FacetChipsListComponent);
    component = fixture.componentInstance;
  });


  it("can load instance", () => {
    expect(component).toBeTruthy();
  });
  it("key defaults to: Object.keys", () => {
    expect(component.key).toEqual(Object.keys);
  });
  describe("ngOnInit", () => {
    it("makes expected calls when facetPage is home", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const searchOfferRequestServicStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      const offerMappingServiceStub: OfferMappingService = fixture.debugElement.injector.get(
        OfferMappingService
      );
      searchOfferRequestServicStub.homeFilterSearchSourceSearch = of(
        new FacetItem()
      );

      const commonGroupServiceStub: CommonGroupService = fixture.debugElement.injector.get(
        CommonGroupService
      );

      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );

      const suggestedUPCFilterServiceStub:SuggestedUPCFilterService = fixture.debugElement.injector.get(SuggestedUPCFilterService);


      storeGroupServiceStub.storeFilterSearchSourceSearch = of({ chip: "" });
      offerMappingServiceStub.eventNameSearchSourceSearch = of(true);
      offerMappingServiceStub.j4uSelectSearchSourceSourceSearch = of(true);
      offerMappingServiceStub.copientSelectSearchSourceSearch = of(true);
      baseInputSearchServiceStub.updateCategoryChipSavedSearch$ = new Subject();
      suggestedUPCFilterServiceStub.clearUpcFilter$ = new Subject();

      component.facetPage = "home";
      spyOn(component,'subscribeUpcFilter');
      spyOn(commonGroupServiceStub,'getPopulateChipForRogsUpcExpansion');
      
      spyOn(component, "facetHomeChips");
      spyOn(component, "facetStoreChips");
      spyOn(component, "generateStoreChip");
      spyOn(facetItemServiceStub, "getOfferFilter");
      
      component.ngOnInit();
      expect(component.facetHomeChips).toHaveBeenCalled();
      expect(component.facetStoreChips).not.toHaveBeenCalled();
      expect(component.generateStoreChip).not.toHaveBeenCalled();
      expect(facetItemServiceStub.getOfferFilter).toHaveBeenCalled();
    });

    it("makes expected calls when facetPage is storeGroup", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const searchOfferRequestServicStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      const offerMappingServiceStub: OfferMappingService = fixture.debugElement.injector.get(
        OfferMappingService
      );
      searchOfferRequestServicStub.homeFilterSearchSourceSearch = of(
        new FacetItem()
      );
      const commonGroupServiceStub: CommonGroupService = fixture.debugElement.injector.get(
        CommonGroupService
      );

      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );

      const suggestedUPCFilterServiceStub:SuggestedUPCFilterService = fixture.debugElement.injector.get(SuggestedUPCFilterService);



      storeGroupServiceStub.storeFilterSearchSourceSearch = of({
        chip: "",
        facetFilter: "TestFilter",
      });
      offerMappingServiceStub.eventNameSearchSourceSearch = of(true);
      offerMappingServiceStub.j4uSelectSearchSourceSourceSearch = of(true);
      offerMappingServiceStub.copientSelectSearchSourceSearch = of(true);
      baseInputSearchServiceStub.updateCategoryChipSavedSearch$ = new Subject();
      suggestedUPCFilterServiceStub.clearUpcFilter$ = new Subject();

      component.facetPage = "storeGroup";
      spyOn(component,'subscribeUpcFilter');
      spyOn(commonGroupServiceStub,'getPopulateChipForRogsUpcExpansion');

      spyOn(component, "facetHomeChips");
      spyOn(component, "facetStoreChips");
      spyOn(component, "generateStoreChip");
      spyOn(facetItemServiceStub, "getOfferFilter");
      component.ngOnInit();
      expect(component.facetHomeChips).not.toHaveBeenCalled();
      expect(component.facetStoreChips).toHaveBeenCalled();
      expect(component.generateStoreChip).toHaveBeenCalled();
      expect(facetItemServiceStub.getOfferFilter).toHaveBeenCalled();
      
    });

    it("makes expected calls when storeFilterSearchSourceSearch is false", () => {
      const searchOfferRequestServicStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      const offerMappingServiceStub: OfferMappingService = fixture.debugElement.injector.get(
        OfferMappingService
      );
      const commonGroupServiceStub: CommonGroupService = fixture.debugElement.injector.get(
        CommonGroupService
      );

      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );

      const suggestedUPCFilterServiceStub:SuggestedUPCFilterService = fixture.debugElement.injector.get(SuggestedUPCFilterService);


      searchOfferRequestServicStub.homeFilterSearchSourceSearch = of(
        new FacetItem()
      );
      baseInputSearchServiceStub.updateCategoryChipSavedSearch$ = new Subject();
      suggestedUPCFilterServiceStub.clearUpcFilter$ = new Subject();

      storeGroupServiceStub.storeFilterSearchSourceSearch = of(false);
      offerMappingServiceStub.eventNameSearchSourceSearch = of(true);
      offerMappingServiceStub.j4uSelectSearchSourceSourceSearch = of(true);
      offerMappingServiceStub.copientSelectSearchSourceSearch = of(true);
      component.facetPage = "storeGroup";
      spyOn(component,'subscribeUpcFilter');
      spyOn(commonGroupServiceStub,'getPopulateChipForRogsUpcExpansion');
      spyOn(component, "facetHomeChips");
      spyOn(component, "facetStoreChips");
      spyOn(component, "generateStoreChip");
      component.ngOnInit();
      expect(component.facetHomeChips).not.toHaveBeenCalled();
      expect(component.facetStoreChips).not.toHaveBeenCalled();
      expect(component.generateStoreChip).not.toHaveBeenCalled();
      
      
    });
  });

  describe("generateStoreChip", () => {
    it("when chip is features", () => {
      const res = component.generateStoreChip({ features: ["ele"] });
      expect(res).toEqual({ "0": "Yes" });
    });
    it("no item not features", () => {
      const res = component.generateStoreChip(false);
      expect(res).toEqual({});
    });
    it("should trace else when features is not a key", () => {
      const res = component.generateStoreChip({ elements: ["ele"] });
      expect(res).toEqual({ elements: "ele" });
    });
  });

  describe("onFacetChipClick", () => {
    it("makes expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "setOfferFilter");
      spyOn(component, "removeFacetHomeChip");
      component.facetPage = "offerHome";
      component.onFacetChipClick({ facetClose: "" });
      expect(facetItemServiceStub.setOfferFilter).toHaveBeenCalled();
      expect(component.removeFacetHomeChip).toHaveBeenCalled();
     
    });
    it("should trace else when facetPage is not home", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const spy = spyOn(facetItemServiceStub, "setOfferFilter");
      const spy1 = spyOn(component, "removeFacetHomeChip");
      component.facetPage = "Request";
      component.onFacetChipClick({ facetClose: "" });
      expect(facetItemServiceStub.setOfferFilter).not.toHaveBeenCalled();
      expect(component.removeFacetHomeChip).not.toHaveBeenCalled();
     
    });
  });

  describe("removeFacetHomeChip", () => {
    it("makes expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(component, "facetHomeChips");
      spyOn(facetItemServiceStub, "getTodayOption");
      component.removeFacetHomeChip({ facetClose: "", chip: {} });
      
      expect(component.facetHomeChips).toHaveBeenCalled();
     
      
    });

    it("should trace if when facetClose is facetSearch", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(component, "facetHomeChips");
      spyOn(facetItemServiceStub, "getTodayOption").and.returnValue([{ chip: "TodaysDate" }, { chip: "querySearch" }]);
      component.removeFacetHomeChip({
        facetClose: "facetSearch",
        chip: "TodaysDate",
      });
      expect(component.facetHomeChips).toHaveBeenCalled();
      
    });

    it("should trace if when Index is undefined", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const spy = spyOn(component, "facetHomeChips");
      spyOn(facetItemServiceStub, "getTodayOption")
        .and.returnValue([{ chip: "SearchQuery" }, { chip: "querySearch" }]);
      component.removeFacetHomeChip({
        facetClose: "facetSearch",
        chip: "TodaysDate",
      });
      expect(component.facetHomeChips).toHaveBeenCalled();
      
    });
  });

  describe("facetStoreChips", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const res = component.facetStoreChips({});
      spyOn(facetItemServiceStub, "getTodayOption");
      expect(res).toEqual(false);
    });

    it("when facet items", () => {
      const res = component.facetStoreChips({ item: "all" });
      expect(res).toEqual(undefined);
      const res1 = component.facetStoreChips({ item: "none" });
      expect(res1).toEqual(undefined);
    });

    it("when no facet items", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "getSearchFacetFields").and.returnValue(["ele1"]);
      spyOn(facetItemServiceStub, "getFilterFacetFields");
      spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(null);
      component.facetHomeChips({ ele1: "" });

      expect(facetItemServiceStub.getSearchFacetFields).toHaveBeenCalled();
      expect(facetItemServiceStub.getFilterFacetFields).toHaveBeenCalled();
      expect(facetItemServiceStub.getOfferFilter).toHaveBeenCalled();     
    });
  });

  describe("splitQueryWithFilter", () => {
    it("should make expected calls", () => {
      const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(
        QueryGenerator
      );
      spyOn(queryGeneratorStub, "getQueryWithFilter")
        .and.returnValue(
          of([
            "nonDigitalStatus=S#digitalStatus=S#combinedDigitalUser=S",
            "combinedDigitalUser",
          ])
        );
      component.splitQueryWithFilter({ assignedTo: "gvuyy00" });
    });
  });

  describe("getStatusData", () => {
    it("should make expected calls", () => {
      const status = component.getStatusData([
        "Completed",
        "Expired",
        "Pending",
      ]);
      expect(status).toEqual(" + Expired ; Pending");
    });
    it("should trace else when no output", () => {
      const status = component.getStatusData(["Completed"]);
      expect(status).toEqual("");
    });
  });

  describe("getStatusDigitalAndNonDigital", () => {
    it("should make expected calls", () => {
      const status = component.getStatusDigitalAndNonDigital([
        "Completed",
        "Expired",
        "Pending",
      ]);
      expect(status).toEqual(" + Pending");
    });
    it("should trace else when no output", () => {
      const status = component.getStatusData([""]);
      expect(status).toEqual("");
    });
  });

  describe("isReqAndBpd", () => {
    it("should return true when facetPage is 'home' and programCodeSelected is 'BPD'", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetPage = "home";
      facetItemServiceStub.programCodeSelected = "BPD";
      expect(component.isReqAndBpd).toBeTrue();
    });

    it("should return false when facetPage is not 'home'", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetPage = "offerHome";
      facetItemServiceStub.programCodeSelected = "BPD";
      expect(component.isReqAndBpd).toBeFalse();
    });

    it("should return false when programCodeSelected is not 'BPD'", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetPage = "home";
      facetItemServiceStub.programCodeSelected = "GR";
      expect(component.isReqAndBpd).toBeFalse();
    });
  });

  describe("clearChips", () => {
    it("should reset filters and emit clearAllChipClick when facetPage is pgManagement", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );
      spyOn(commonSearchServiceStub, "resetAllFilterOptions");
      spyOn(baseInputSearchServiceStub, "postDataForInputSearch");
      spyOn(component.clearAllChipClick, "emit");

      component.facetPage = "pgManagement";
      component.clearChips();

      expect(commonSearchServiceStub.resetAllFilterOptions).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.postDataForInputSearch).toHaveBeenCalledWith(true);
      expect(component.clearAllChipClick.emit).not.toHaveBeenCalled();
    });

    it("should emit clearAllChipClick when facetPage is offerHome", () => {
      spyOn(component.clearAllChipClick, "emit");

      component.facetPage = "offerHome";
      component['facetSearch'] = { key1: "value1" };
      component.clearChips();

      expect(component.clearAllChipClick.emit).toHaveBeenCalledWith({
        chipType: "search",
        clearAll: true,
        selectedFacets: { key1: "value1" },
      });
    });

    it("should call clearAllFilters when facetPage is not pgManagement or offerHome", () => {
      const suggestedUPCFilterServiceStub: SuggestedUPCFilterService = fixture.debugElement.injector.get(
        SuggestedUPCFilterService
      );
      spyOn(suggestedUPCFilterServiceStub, "clearAllFilters");

      component.facetPage = "template";
      component.clearChips();
    });

    it("should reset facetSearch after clearing chips", () => {
      component['facetSearch'] = { key1: "value1" };
      component.clearChips();
      expect(component['facetSearch']).toEqual({});
    });

    it("should handle isReqAndBpd condition", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );
      spyOn(commonSearchServiceStub, "resetAllFilterOptions");
      spyOn(baseInputSearchServiceStub, "postDataForInputSearch");

      component.facetPage = "home";
      spyOnProperty(component, "isReqAndBpd", "get").and.returnValue(true);
      component.clearChips();

    });
  });

  describe("isDisplayClearLink", () => {
    it("should return true when isDisplayClearAllChips is true and chipCount > 2 for offerHome with podView", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.isDisplayClearAllChips = true;
      component.facetPage = "offerHome";
      facetItemServiceStub.podView = true;

      const result = component.isDisplayClearLink({ chipCount: 3 });
      expect(result).toBeTrue();
    });

    it("should return false when isDisplayClearAllChips is false", () => {
      component.isDisplayClearAllChips = false;
      const result = component.isDisplayClearLink({ chipCount: 3 });
    });

    it("should return true when isDisplayClearAllChips is true and chipCount > 1 for offerHome without podView", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.isDisplayClearAllChips = true;
      component.facetPage = "offerHome";
      facetItemServiceStub.podView = false;

      const result = component.isDisplayClearLink({ chipCount: 2 });
      expect(result).toBeTrue();
    });

    it("should return false when chipCount is not greater than the required threshold", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.isDisplayClearAllChips = true;
      component.facetPage = "offerHome";
      facetItemServiceStub.podView = true;

      const result = component.isDisplayClearLink({ chipCount: 2 });
      expect(result).toBeFalse();
    });
  });

  describe("subscribeUpcFilter", () => {
    it("should subscribe to upcFilterChipData$ and update facetSearch and isDisplayClearAllChips", () => {
      const suggestedUPCFilterServiceStub: SuggestedUPCFilterService = fixture.debugElement.injector.get(
        SuggestedUPCFilterService
      );
      const upcFilterChipData$ = new Subject<any>();
      suggestedUPCFilterServiceStub.upcFilterChipData$ = upcFilterChipData$;

      component.subscribeUpcFilter();

      upcFilterChipData$.next({ key1: ["value1", "value2"], key2: ["value3"] });

      expect(component['facetSearch']).toEqual({
        key1: "value1 ; value2",
        key2: "value3",
      });
      expect(component.isHideSearchLabel).toBeTrue();
      expect(component.isDisplayClearAllChips).toBeTrue();
    });

    it("should reset facetSearch when upcFilterChipData$ emits null or undefined", () => {
      const suggestedUPCFilterServiceStub: SuggestedUPCFilterService = fixture.debugElement.injector.get(
        SuggestedUPCFilterService
      );
      const upcFilterChipData$ = new Subject<any>();
      suggestedUPCFilterServiceStub.upcFilterChipData$ = upcFilterChipData$;

      component.subscribeUpcFilter();

      upcFilterChipData$.next(null);

      expect(component['facetSearch']).toEqual({});
    });
  });

  describe("clearChipsForLeftFilters", () => {
    it("should clear left filters and post data for input search when facetPage is template", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );
      spyOn(commonSearchServiceStub.clearLeftFilters$, "next");
      spyOn(baseInputSearchServiceStub, "postDataForInputSearch");

      component.facetPage = "template";
      component.clearChipsForLeftFilters();

      expect(commonSearchServiceStub.clearLeftFilters$.next).toHaveBeenCalledWith({
        excludedChipsArr: ["programCode"],
      });
      expect(baseInputSearchServiceStub.postDataForInputSearch).toHaveBeenCalledWith(true);
    });

    it("should emit clearAllChipClick when facetPage is offerHome", () => {
      spyOn(component.clearAllChipClick, "emit");

      component.facetPage = "offerHome";
      component['facetFilter'] = { key1: "value1" };
      component.clearChipsForLeftFilters();

      expect(component.clearAllChipClick.emit).toHaveBeenCalledWith({
        chipType: "filter",
        clearAll: true,
        selectedFacets: { key1: "value1" },
      });
    });

    it("should reset facetFilter after clearing chips", () => {
      component['facetFilter'] = { key1: "value1" };
      component.clearChipsForLeftFilters();
      expect(component['facetFilter']).toEqual({});
    });

    it("should handle isReqAndBpd condition", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      const baseInputSearchServiceStub: BaseInputSearchService = fixture.debugElement.injector.get(
        BaseInputSearchService
      );
      spyOn(commonSearchServiceStub.clearLeftFilters$, "next");
      spyOn(baseInputSearchServiceStub, "postDataForInputSearch");

      component.facetPage = "home";
      spyOnProperty(component, "isReqAndBpd", "get").and.returnValue(true);
      component.clearChipsForLeftFilters();
    });
  });

  describe("populatePODList", () => {
    it("should add 'digital' to facetFilter when podView is true", () => {
      component['facetFilter'] = {};
      component.populatePODList(true);
      expect(component['facetFilter']).toEqual({ digital: "Digital;" });
    });

    it("should remove 'digital' from facetFilter when podView is false", () => {
      component['facetFilter'] = { digital: "Digital;" };
      component.populatePODList(false);
      expect(component['facetFilter']).toEqual({});
    });
  });

  describe("populatePageModQuery", () => {
    it("should set 'deliveryChannel' to 'Digital Only-In Ad' when selectedItem is 'pageMod'", () => {
      component['facetFilter'] = {};
      component.populatePageModQuery("pageMod");
      expect(component['facetFilter']).toEqual({ deliveryChannel: "Digital Only-In Ad" });
    });

    it("should remove 'deliveryChannel' from facetFilter when podFilterChanged is true", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.podFilterChanged = true;
      component['facetFilter'] = { deliveryChannel: "Digital Only-In Ad" };
      component.populatePageModQuery("otherItem");
      expect(component['facetFilter']).toEqual({});
    });

    it("should not modify facetFilter when selectedItem is not 'pageMod' and podFilterChanged is false", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.podFilterChanged = false;
      component['facetFilter'] = { deliveryChannel: "Digital Only-In Ad" };
      component.populatePageModQuery("otherItem");
      expect(component['facetFilter']).toEqual({ deliveryChannel: "Digital Only-In Ad" });
    });
  });

  describe("setDisplayExpiredStatusVal", () => {
    it("should set isHideExpiredStatusReqs and update query options when chip is expired", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      component.setDisplayExpiredStatusVal({ chip: "status"});
    });

    it("should not set isHideExpiredStatusReqs when chip is not expired", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      component.setDisplayExpiredStatusVal({ chip: "status"});

      expect(commonSearchServiceStub.setIsHideExpiredStatusReqs).not.toHaveBeenCalled();
      expect(commonSearchServiceStub.setQueryOptionsForBpd).not.toHaveBeenCalled();
    });
  });

  describe("isExpiredChip", () => {
    it("should return true when chip is expired and facetPage is home", () => {
      component.facetPage = "home";
      const result = component.isExpiredChip({
        chip: "status",
        facetChip: [CONSTANTS.EXPIRED_STATUS_OR_DISPLAY],
      });
      expect(result).toBeTrue();
    });

    it("should return false when chip is not expired", () => {
      component.facetPage = "home";
      const result = component.isExpiredChip({
        chip: "status",
        facetChip: ["Active"],
      });
      expect(result).toBeFalse();
    });

    it("should return false when facetPage is not home", () => {
      component.facetPage = "offerHome";
      const result = component.isExpiredChip({
        chip: "status",
        facetChip: [CONSTANTS.EXPIRED_STATUS_OR_DISPLAY],
      });
      expect(result).toBeFalse();
    });
  });

  describe("onFacetChipClick", () => {
    it("should call setDisplayExpiredStatusVal when chip is expired", () => {
      spyOn(component, "setDisplayExpiredStatusVal");
      spyOn(component.facetChipListClick, "emit");

      component.facetPage = "home";
      component.onFacetChipClick({
        chip: "status",
        facetChip: [CONSTANTS.EXPIRED_STATUS_OR_DISPLAY],
        facetClose: "facetFilter",
      });
      expect(component.facetChipListClick.emit).toHaveBeenCalled();
    });

    it("should not call setDisplayExpiredStatusVal when chip is not expired", () => {
      spyOn(component, "setDisplayExpiredStatusVal");
      spyOn(component.facetChipListClick, "emit");

      component.facetPage = "home";
      component.onFacetChipClick({
        chip: "status",
        facetChip: ["Active"],
        facetClose: "facetFilter",
      });

      expect(component.setDisplayExpiredStatusVal).not.toHaveBeenCalled();
      expect(component.facetChipListClick.emit).toHaveBeenCalled();
    });

    it("should handle facetPage other than home", () => {
      spyOn(component, "setDisplayExpiredStatusVal");
      spyOn(component.facetChipListClick, "emit");

      component.facetPage = "offerHome";
      component.onFacetChipClick({
        chip: "status",
        facetChip: [CONSTANTS.EXPIRED_STATUS_OR_DISPLAY],
        facetClose: "facetFilter",
      });

      expect(component.setDisplayExpiredStatusVal).not.toHaveBeenCalled();
      expect(component.facetChipListClick.emit).toHaveBeenCalled();
    });
  });

  describe("setFacetChips", () => {
    it("should set facetSearch and facetFilter when data is available", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, "getInputSearchChip").and.returnValue({
        key1: "value1",
        key2: "value2",
      });
      spyOn(commonSearchServiceStub, "getFacetFilterChip").and.returnValue({
        filter1: "filterValue1",
        filter2: "filterValue2",
      });

      component.setFacetChips();

      expect(component['facetSearch']).toEqual({
        key1: "value1",
        key2: "value2",
      });
      expect(component['facetFilter']).toEqual({
        filter1: "filterValue1",
        filter2: "filterValue2",
      });
    });

    it("should reset facetSearch and facetFilter when no data is available", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, "getInputSearchChip").and.returnValue(null);
      spyOn(commonSearchServiceStub, "getFacetFilterChip").and.returnValue(null);

      component.setFacetChips();

      expect(component['facetSearch']).toEqual({});
      expect(component['facetFilter']).toEqual({});
    });

    it("should set only facetSearch when facetFilter data is not available", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, "getInputSearchChip").and.returnValue({
        key1: "value1",
      });
      spyOn(commonSearchServiceStub, "getFacetFilterChip").and.returnValue(null);

      component.setFacetChips();

      expect(component['facetSearch']).toEqual({
        key1: "value1",
      });
      expect(component['facetFilter']).toEqual({});
    });

    it("should set only facetFilter when facetSearch data is not available", () => {
      const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(
        CommonSearchService
      );
      spyOn(commonSearchServiceStub, "getInputSearchChip").and.returnValue(null);
      spyOn(commonSearchServiceStub, "getFacetFilterChip").and.returnValue({
        filter1: "filterValue1",
      });

      component.setFacetChips();

      expect(component['facetSearch']).toEqual({});
      expect(component['facetFilter']).toEqual({
        filter1: "filterValue1",
      });
    });
  });

  describe("getVendorChip", () => {
    it("should return concatenated vendor keys when selected values match vendor names", () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(initialDataServiceStub, "getAppDataName").and.returnValue({
        vendor1: "Vendor Name 1",
        vendor2: "Vendor Name 2",
      });

      const result = component.getVendorChip(
        { vendors: "Vendor Name 1;Vendor Name 2" },
        "vendors"
      );

      expect(result).toEqual("vendor1;vendor2;");
    });

    it("should return undefined when no selected values match vendor names", () => {
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(initialDataServiceStub, "getAppDataName").and.returnValue({
        vendor1: "Vendor Name 1",
        vendor2: "Vendor Name 2",
      });

      const result = component.getVendorChip(
        { vendors: "Vendor Name 3" },
        "vendors"
      );

      expect(result).toBeUndefined();
    });
  });

  describe("facetHomeChips", () => {
    it("should update facetSearch and facetFilter based on facetChipListItems", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "getSearchFacetFields").and.returnValue([
        "searchKey1",
        "searchKey2",
      ]);
      spyOn(facetItemServiceStub, "getFilterFacetFields").and.returnValue([
        "filterKey1",
        "filterKey2",
      ]);
      spyOn(facetItemServiceStub, "getTodayOption").and.returnValue([]);

      component.facetHomeChips({
        searchKey1: "value1",
        filterKey1: "value2",
      });

      expect(component["facetSearch"]).toEqual({
        searchKey1: "value1",
      });
      expect(component["facetFilter"]).toEqual({
        filterKey1: "value2",
      });
    });

    it("should handle vendors key in facetChipListItems", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const initialDataServiceStub: InitialDataService = fixture.debugElement.injector.get(
        InitialDataService
      );
      spyOn(facetItemServiceStub, "getSearchFacetFields").and.returnValue([
        "vendors",
      ]);
      spyOn(facetItemServiceStub, "getFilterFacetFields").and.returnValue([]);
      spyOn(initialDataServiceStub, "getAppDataName").and.returnValue({
        vendor1: "Vendor Name 1",
        vendor2: "Vendor Name 2",
      });

      component.facetHomeChips({
        vendors: "Vendor Name 1;Vendor Name 2",
      });
    });

    it("should clear facetSearch and facetFilter when facetChipListItems is empty", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "getSearchFacetFields").and.returnValue([]);
      spyOn(facetItemServiceStub, "getFilterFacetFields").and.returnValue([]);

      component.facetHomeChips({});

      expect(component["facetSearch"]).toEqual({});
      expect(component["facetFilter"]).toEqual({});
    });
  });

  it("should update facetChipListItems when element is 'status' and value is '(E OR I)'", () => {
    const facetChipListItems = { status: "(E OR I)" };
    component.facetHomeChips(facetChipListItems);
  });

  it("should handle 'status' when digital and nonDigital are both present", () => {
    const facetChipListItems = { status: "Digital;Non-Digital;Pending" };
    component.facetHomeChips(facetChipListItems);

  });

  it("should handle 'status' when only digital is present", () => {
    const facetChipListItems = { status: "Digital;Pending" };
    component.facetHomeChips(facetChipListItems);

    expect(facetChipListItems.status).toEqual("Digital;Pending");
  });

  it("should handle 'status' when only nonDigital is present", () => {
    const facetChipListItems = { status: "Non-Digital;Pending" };
    component.facetHomeChips(facetChipListItems);

    expect(facetChipListItems.status).toEqual("Non-Digital;Pending");
  });

  it("should handle 'status' when neither digital nor nonDigital is present", () => {
    const facetChipListItems = { status: "Pending;Completed" };
    component.facetHomeChips(facetChipListItems);

    expect(facetChipListItems.status).toEqual("Pending;Completed");
  });

});
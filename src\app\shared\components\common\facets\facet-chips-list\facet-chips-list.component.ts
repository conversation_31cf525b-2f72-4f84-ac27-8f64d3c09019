
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

import { CONSTANTS } from "@appConstants/constants";
import { SuggestedUPCFilterService } from "@appGroup/core/product-groups/services/suggested-upcfilter.service";
import { CommonGroupService } from '@appGroupsServices/common-group.service';
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { OfferMappingService } from "@appOffersServices/offer-mapping.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
  selector: "facet-chips-list",
  templateUrl: "./facet-chips-list.component.html",
  styleUrls: ["./facet-chips-list.component.scss"],
})
export class FacetChipsListComponent extends UnsubscribeAdapter
  implements OnInit {
  facetChipItems = {};
  
  @Output() facetChipListClick = new EventEmitter();
  @Output() clearAllChipClick  = new EventEmitter();
  @Input() facetPage;
  @Input() createStoreGroup;
  @Input() xMarkShow;
  @Input() isDisplayClearAllChips;

  key = Object.keys;
  private facetSearch = {};
  private facetFilter = {};
  private facetClose;
  j4uStoreTerminalFacetFilter: {};
  copientStoreTerminalFacetFilter: any;
  rogsFilterForUpc: {};
  shoppingListCategory: any;
  rangeDates = [
    "effectiveStartDate",
    "createTimeStamp",
    "lastUpdateTimestamp",
    "startDt",
    "endDt",
    "startDate",
    'promotionStartDt',
    'effectiveEndDate'
  ];
  upcFilterChips: any;
  isHideSearchLabel: boolean = false;
  
  constructor(
    private queryGenerator: QueryGenerator,
    private _offerMappingService: OfferMappingService,
    private storeGroupService: StoreGroupService,
    private facetItemService: FacetItemService,
    private _commonGroupService: CommonGroupService,
    private suggestedUPCFilterService: SuggestedUPCFilterService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private commonSearchService:CommonSearchService,
    private baseInputSearchService:BaseInputSearchService,
    private initialDataService:InitialDataService,
    private featureFlagService:FeatureFlagsService
  ) {
    super();
  }

  ngOnInit() {

    
    this.facetItemService.facetChipComponent = this;
    this.subscribeUpcFilter();
    
    this.subs.sink = this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}BehaviorSubject`]?.subscribe(response=>{
      this.setFacetChips();
    });
    this.subs.sink = this._searchOfferRequestService.resetProgramTypeGr$?.subscribe((isReset) => {
      if(isReset) {
        this.removeFacetHomeChip({chip: 'programType', facetClose: 'facetFilter', removed: false});
      }
    })  
    this.subs.sink = this._searchOfferRequestService.homeFilterSearchSourceSearch.subscribe(
      (facetChipListItems: any) => {
        if (
          ["offerPODPage", "home", "pluManagement","offerHome",CONSTANTS.PRODUCTMANAGEMENT].includes(this.facetPage) &&
          facetChipListItems
        ) {
          if (this.queryGenerator.getQueryWithFilter()) {
            if (facetChipListItems["facetChip"]) {
              this.splitQueryWithFilter(facetChipListItems["facetChip"]);
            }
          }
          if(this.facetPage === "offerHome" && facetChipListItems.facetChip &&  facetChipListItems.facetChip.divisions
           && facetChipListItems.facetChip.divisions.length){
            facetChipListItems.facetChip.divisions = facetChipListItems?.facetChip?.divisions?.join(";");
          }
          // Offer request page is there and Program code is BPD then we should not call below method
          const isReqAndBpd = this.facetPage === "home" && this.facetItemService?.programCodeSelected === CONSTANTS.BPD;
          !isReqAndBpd && this.facetHomeChips(facetChipListItems.facetChip);
        }
      }
    );
    if (this.storeGroupService.storeFilterSearchSourceSearch) {
      this.subs.sink = this.storeGroupService.storeFilterSearchSourceSearch.subscribe(
        (list: any) => {
          const offerFilter = this.facetItemService.getOfferFilter();
          if (this.facetPage === "storeGroup" && list) {
            if (offerFilter === "facetFilter" && list.facetChip) {
              this.facetStoreChips(list.facetChip);
            } else if (offerFilter === "facetFilter" && list.chip) {
              delete this.facetFilter[list.chip];
            } else {
              this.facetStoreChips(
                this.generateStoreChip(list.storesSearchCriteria)
              );
            }
          }
        }
      );
    }

    if(this._commonGroupService.getPopulateChipForRogsUpcExpansion()) {
      this.subs.sink =this._commonGroupService.getPopulateChipForRogsUpcExpansion().subscribe(
        (list: any) => {
          this.rogsFilterForUpc = {};
          if (list) {
            const facetChip = list.facetChip;
            for(let key of Object.keys(facetChip)){
              if(facetChip[key]) {
                this.rogsFilterForUpc[key] = facetChip[key];
              }
            }
          }
          else {
            this.rogsFilterForUpc = {};
          }
        }
      );
    }

    this.subs.sink = this._offerMappingService.copientSelectSearchSourceSearch.subscribe(
      (list: any) => {
        this.copientStoreTerminalFacetFilter = {};
        if (list && list.length) {
          list.forEach((element, index) => {
            if (element) {
              this.copientStoreTerminalFacetFilter[index] = element;
            }
          });
        }
      }
    );
    this.subs.sink = this.baseInputSearchService.updateCategoryChipSavedSearch$.subscribe(() => {
      // updating category chip when we apply saved search
      const updatedFilterChips = this.commonSearchService.getFacetFilterChip();
      if(updatedFilterChips?.['categoryId']) {
        this.facetFilter['categoryId'] = updatedFilterChips['categoryId'];
      }
    })
    this.subs.sink = this._offerMappingService.j4uSelectSearchSourceSourceSearch.subscribe(
      (list: any) => {
        this.j4uStoreTerminalFacetFilter = {};
        if (list && list.length) {
          list.forEach((element, index) => {
            if (element) {
              this.j4uStoreTerminalFacetFilter[index] = element;
            }
          });
        }
      }
    );
    this.subs.sink = this.suggestedUPCFilterService.clearUpcFilter$.subscribe((val) => {
      if (val) {
        this.clearChips();
      }
    });
  }

  isDisplayClearLink({chipCount}){
    if(this.isDisplayClearAllChips) {
      return this.facetPage === "offerHome" && this.facetItemService.podView ?  chipCount > 2   :  chipCount > 1;
    }
  }
  subscribeUpcFilter(){
    this.subs.sink = this.suggestedUPCFilterService.upcFilterChipData$.subscribe((val)=>{
      if(val){
        if (!this.facetItemService.getOfferFilter()) {
          this.facetSearch = {};
        }
        this.isHideSearchLabel = this.isDisplayClearAllChips = true;
        for(let key of Object.keys(val)){
          if(val[key].length) {
            this.facetSearch[key] = val[key].join(" ; ");
          }
        }
      }
    });
  }
  get isReqAndBpd() {
    return this.facetPage === "home" && this.facetItemService?.programCodeSelected === CONSTANTS.BPD;
  }
  clearChips(){ 
    //Event handler for clearing multiple chips
    
    
   if(["pgManagement", "template", "sgManagement"].indexOf(this.facetPage) > -1 || this.isReqAndBpd){
      
      let key = this.baseInputSearchService.currentRouter, 
          activeCurrentSearchType = this.baseInputSearchService.getActiveCurrentSearchType();

      if(["template"].indexOf(this.facetPage) > -1  || this.isReqAndBpd){
        key = activeCurrentSearchType;
      }
      
      this.commonSearchService.resetAllFilterOptions({current:activeCurrentSearchType, key});      
      this.baseInputSearchService.postDataForInputSearch(true); 
    } else if(["offerHome"].includes(this.facetPage) || !this.isReqAndBpd) {
      const selectedFacets = this.facetSearch;
      this.clearAllChipClick.emit({chipType:"search", clearAll: true, selectedFacets});
    } else{
      this.suggestedUPCFilterService.clearAllFilters();
    }
    this.facetSearch = {};

  }
  
  clearChipsForLeftFilters(){
    const isReqAndBpd  = this.facetPage === "home" && this.facetItemService?.programCodeSelected === CONSTANTS.BPD;

    //Event handler for clear All functionality on Left side filters
    if(["template"].includes(this.facetPage) ||  isReqAndBpd) {
      const obj = {excludedChipsArr:['programCode']};//includes chips which should'nt be cleared    
      this.commonSearchService.clearLeftFilters$.next(obj);  
      this.baseInputSearchService.postDataForInputSearch(true);
    } else if(["offerHome"].includes(this.facetPage) || !isReqAndBpd) {
      const selectedFacets = this.facetFilter;
      this.clearAllChipClick.emit({chipType:"filter", clearAll: true, selectedFacets});
      this.facetFilter = {};
    }
  }

  splitQueryWithFilter(facetChip) {
    let queryWithFilter = this.queryGenerator.getQueryWithFilter();
    queryWithFilter.forEach((element) => {
      if (element.indexOf("combinedDigitalUser") !== -1) {
        if(typeof(element) != "string"){
          element = element.toString();
        }
        let splitAssigne = element.split("#");
        if (splitAssigne.length) {
          const index = splitAssigne[0].indexOf("=");
          facetChip["assignedTo"] = splitAssigne[0].slice(
            index + 1,
            splitAssigne[0].length
          );
        }
      }
    });
  }
  
  populatePODList(podView){
    if(podView){
      this.facetFilter['digital'] = "Digital;";
    } else {
      delete this.facetFilter['digital'];
    } 
  }

  populatePageModQuery(selectedItem){
    if(selectedItem === "pageMod"){
      this.facetFilter['deliveryChannel'] = "Digital Only-In Ad";
    }else if(this.facetItemService.podFilterChanged) {
      delete this.facetFilter['deliveryChannel'];
    }
  }
  
  generateStoreChip(items) {
    if (!items) {
      return {};
    }
    return Object.keys(items).reduce((obj, element) => {
      if (element === "features" && items[element]) {
        Object.keys(items[element]).forEach((ele) => {
          obj[ele] = items[element][ele] ? "Yes" : "No";
        });
      } else {
        if (items[element] && items[element][0]) {
          obj[element] = items[element].join(";");
        }
      }

      return obj;
    }, {});
  }

  setDisplayExpiredStatusVal({chip}){
   //IN OR, if Expired status chip is closed, set the flags so that query is updated 
   if(this.isExpiredChip(chip)){
    const isHideExpiredStatusReqs =   true;
    this.commonSearchService.setIsHideExpiredStatusReqs(isHideExpiredStatusReqs);
    this.commonSearchService.setQueryOptionsForBpd({isHideExpiredStatusReqs});
   }
  }
  
  isExpiredChip(chip){
    let isExpiredChip = false;
    if(chip.chip === "status" &&  this.facetPage === "home"){
      if(chip.facetChip.includes(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY) ){
        //Filter selection
        isExpiredChip = true; 
      }else if(chip.facetChip.includes(CONSTANTS.COMPLETED_STATUS_OR_DISPLAY) &&
        this.baseInputSearchService.getFormQuery().includes(CONSTANTS.END_DATE_QUERY_OR)){
          //In case of Saved search
        isExpiredChip = true;
      }
    }
    return isExpiredChip;
  }

  onFacetChipClick(chip) {
    if (["baseProductGroup","offerPODPage","home","pluManagement","offerHome","template", "pgManagement", "action-log","import-log-bpd","sgManagement"].
    includes(this.facetPage)) {
      this.facetItemService.setOfferFilter(chip.facetClose);
      if(this.featureFlagService.isOfferRequestArchivalEnabled && this.facetPage == 'home' && chip.facetChip.indexOf(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY) > -1 && chip.facetClose)
      {
        this.commonSearchService.isShowExpiredInQuery = !(chip.facetChip.indexOf(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY) > -1);
      }
      if(this.featureFlagService.isOfferArchivalEnabled && this.facetPage == 'offerHome'  && chip.facetChip.indexOf(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY) > -1 && chip.facetClose)
      {
        this.commonSearchService.isShowExpiredInQuery_O = !(chip.facetChip.indexOf(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY) > -1);
      }
      if(this.facetPage==="baseProductGroup"){
        delete this.facetSearch[chip.chip];
      } else if((this.baseInputSearchService.currentSearchType===CONSTANTS.BPD && this.facetPage==="home") ||
              (["template", "pgManagement","action-log","import-log-bpd","sgManagement"].indexOf(this.facetPage) > -1)){
        this.commonSearchService.isFiltered = false;
        this.setDisplayExpiredStatusVal({chip});
        
        this.commonSearchService.removeQueryAndChipFromOptions({programCode : this.baseInputSearchService.getActiveCurrentSearchType(),
          chip, currentRouter : this.baseInputSearchService.currentRouter});

         this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}FacetFilterBehaviorSubject`].next(chip.chip);
         // when we clear chips for bugm, bggm, we need to reset bggm and categories
        if(["bggm", "bugm"].includes(chip?.chip)) {
          this.baseInputSearchService.onClearBggmBugmChip$.next({chip: chip?.chip});
        }
        this.baseInputSearchService.populateChipList();
      
        if(!chip?.isClearAll_LeftFilter){//If clear all filters clicked, dont execute
          this.baseInputSearchService.postDataForInputSearch(true);
        }
        return;
      }else {
        if(["subProgramCode"].includes(chip?.chip)) {
          this.removeFacetHomeChip({chip: 'programType', facetClose: 'facetFilter', removed: false})
          this._searchOfferRequestService.onClearSubProgramCdChip$.next({chip: chip?.chip})
        }
        this.removeFacetHomeChip(chip);
      }

    }

    if(!chip.removed){
      this.facetChipListClick.emit(chip);
    }
    
  }
  removeFacetHomeChip(chip) {
    this.facetClose = chip.facetClose;
    const todaysOptions = this.facetItemService.getTodayOption();

    if (this.facetClose === "facetSearch") {
      let index;
      todaysOptions.forEach((ele, indx) => {
        if (ele.chip === chip.chip) {
          index = indx;
        }
      });
      if (index !== undefined) {
        todaysOptions.splice(index, 1);
      }
    }
    
    let object = {};
    object[chip.chip] = "";
    this.facetHomeChips(object);
  }
  facetStoreChips(facetChipListItems) {
    if (!Object.keys(facetChipListItems).length) {
      this.facetFilter = {};
      return false;
    }

    const _keys = Object.keys(facetChipListItems);
    _keys.forEach((element) => {
      if (
        facetChipListItems[element] === "all" ||
        !facetChipListItems[element]
      ) {
        delete this.facetFilter[element];
      } else {
        this.facetFilter[element] = facetChipListItems[element];
      }
    });
  }
  getStatusData(statusList) {
    let statusData = "";
    const output = statusList.reduce((output, ele, index) => {
      if (index !== 0 && ele) {
        output.push(ele);
      }
      return output;
    }, []);
    if (output.length) {
      statusData = ` + ${output.join(" ; ")}`;
    }
    return statusData;
  }
  getStatusDigitalAndNonDigital(statusList) {
    let statusData = "";
    const output = statusList.reduce((output, ele, index) => {
      if (index !== 0 && index !== 1 && ele) {
        output.push(ele);
      }
      return output;
    }, []);
    if (output.length) {
      statusData = ` + ${output.join(" ; ")}`;
    }
    return statusData;
  }
  setFacetChips(){
    const facetSearch = this.commonSearchService.getInputSearchChip(),
    facetFilter = this.commonSearchService.getFacetFilterChip();
    if(facetSearch){
      this.facetSearch = facetSearch;
    }
    if(facetFilter){
      this.facetFilter = facetFilter;
    } 
  }

  removeStartDtAndEndDtChip({element, facetChipListItems}){
    //Start Date and End Date query is being set for expired status, but it doesnt need to be displayed as chip in OR, O pages
    if(['endDt', 'startDt', CONSTANTS.END_DATE_QUERY_OR, CONSTANTS.CURRENT_DATE_QUERY_OR].includes(element)) {
      // Added condition for start date and end date, if start date and end date range have *, not generating chip.
      let startDtAndendDtRangeValue = this.queryGenerator.getInputRangeValue(element);
      let startDtAndendDtRangeFromValue = startDtAndendDtRangeValue && startDtAndendDtRangeValue.split(' TO ')[1];
      let startDtAndendDtRangeToValue =  startDtAndendDtRangeValue && startDtAndendDtRangeValue.split(' TO ')[0];
      if(startDtAndendDtRangeFromValue == '*' || startDtAndendDtRangeToValue == '*') {
        delete facetChipListItems[element];
      }     
    }
    return facetChipListItems;
  }

  getVendorChip(facetChipListItems,element){
    let chipValue;
    const vendors = this.initialDataService.getAppDataName("vendors"),
    selectedVal = facetChipListItems[element];
    for(const vendor in vendors){
        if(selectedVal.includes(vendors[vendor])){
          chipValue = `${chipValue ||''}${vendor};`;
        }
    }
  return chipValue;
  }
  facetHomeChips(facetChipListItems) {
    const searchKeys = this.facetItemService.getSearchFacetFields(),
      filterKeys = this.facetItemService.getFilterFacetFields();

    if (this.facetItemService.getOfferFilter() === null || (this.facetPage === "home"  && this.facetItemService.programFilterChange)) {
      this.facetFilter = {};
      this.facetSearch = {};
      this.facetItemService.programFilterChange = false;
    }
    if (facetChipListItems) {
      const _keys = Object.keys(facetChipListItems);
      _keys.forEach((element) => {
        let chipValue, chipElement;

        if (searchKeys.includes(element)) {
          chipElement = this.facetSearch;
          const regex = /[*]/g;
          if(element==="vendors"){
            chipValue = this.getVendorChip(facetChipListItems,element);         
          }else{
            let len = 2;
            if(element === 'requestId' || element === 'adPageNbr' || element === "pointsRequired" || element === "weekId" || element === "redemptionStoreId") {
              len = 1;
            }
  
           facetChipListItems =  this.removeStartDtAndEndDtChip({element, facetChipListItems});
  
            const todaysOptions = this.facetItemService.getTodayOption();
            const chipParam = todaysOptions.filter((ele) => ele.chip === element);
            if (chipParam && chipParam.length) {
              chipValue = chipParam[0].value;
            } else {
              chipValue = facetChipListItems[element] && facetChipListItems[element].replace(regex, " ");
            }
  
            if (!(["hhid","upc","dept","household"].includes(element)) && this.rangeDates.indexOf(element) === -1) {
              let _chipValue = chipValue.replace(/ OR /g, " ; ");
              chipValue = _chipValue.indexOf(';') > -1 ? _chipValue.slice(1,_chipValue.length - len) : chipValue;
                //.slice(1, chipValue.replace(/ OR /g, " ; ").length - len);
              }
              
            chipValue = chipValue && chipValue.replace(/[\\ ]/g, " ");
          }
          
        } else if (filterKeys.includes(element)) {
          chipElement = this.facetFilter;
          if (element === "status") {
            const statusList = facetChipListItems[element].split(";");
            if(this.featureFlagService.isOfferRequestArchivalEnabled)
              this.commonSearchService.isShowExpiredInQuery = (statusList.indexOf("Expired") > -1);            
            if (facetChipListItems[element] === "(E OR I)") {
              facetChipListItems[element] = "Initial/Draft,Edited";
            } else if (statusList.length) {
              const digital = statusList[0],
                nonDigital = statusList[1];
              let statusData = "";
              if (
                ["Digital", "Non-Digital"].includes(digital) ||
                nonDigital === "Non-Digital"
              ) {
                if (digital === "Digital" && nonDigital === "Non-Digital") {
                  facetChipListItems[
                    element
                  ] = `Digital,Non-Digital${this.getStatusDigitalAndNonDigital(
                    statusList
                  )}`;
                } else if (digital === "Non-Digital" || digital === "Digital") {
                  facetChipListItems[element] = `${digital}${this.getStatusData(
                    statusList
                  )}`;
                }
              } else {
                let pCode = this.facetItemService.programCodeSelected;
                //append digital nondigital in facet chips only for SC as GR doesnt have non digi 
                if (pCode === CONSTANTS.SC && facetChipListItems[element]) {
                  statusData = ` + ${facetChipListItems[element]}`;
                  facetChipListItems[
                    element
                  ] = `Digital,Non-Digital${statusData}`;
                }
              }
            }
          } else if (element === "offerStatus")
          {
            if(facetChipListItems[element] === "(I OR R)")
              facetChipListItems[element] = "Initial/Draft,Ready To Approve";
            const statusList = facetChipListItems[element].split(";");
            if(this.featureFlagService.isOfferArchivalEnabled)
              this.commonSearchService.isShowExpiredInQuery_O = (statusList.indexOf("Expired") > -1);
          }
          chipValue = facetChipListItems[element];
        }else{
          const userId = facetChipListItems[element];
          this.facetSearch[element] = userId.slice(1,userId.length-2) ;
        }
        if (chipValue) {
          chipElement[element] = chipValue.split(";").join(" ; ");
        } else if(chipElement && chipElement[element]) {
          delete chipElement[element];
        }
        if(this.facetSearch[element] ===''){
          delete this.facetSearch[element];
        }
      });
    }
  }
}

<div *ngIf="mainFacetItems && key(mainFacetItems).length">
  <form [formGroup]="form">
    <div *ngIf="mainFacetItems" class="facets-list p-0 mr-4">
      <ng-container *ngFor="let item of key(mainFacetItems); trackBy: trackByFn;index as i">
        <div class="pod-list-basis item-flex pb-2" *ngIf="!doHide(item)">
          <facet-item [facetShow]="facetShow" [totalStores]="totalStores" [createStoreGroup]="createStoreGroup"
            [form]="form" [facetItem]='mainFacetItems[item]' (facetClick)="onFacetClick($event)" [item]='item'
            [facetpage]='facetPage'></facet-item>
        </div>
      </ng-container>
    </div>
    <div class="title-divider"></div>


  </form>
</div>
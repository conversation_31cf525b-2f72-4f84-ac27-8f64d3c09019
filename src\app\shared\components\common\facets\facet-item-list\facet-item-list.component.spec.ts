import { ChangeDetector<PERSON><PERSON>, ElementRef, NO_ERRORS_SCHEMA, Renderer2 } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { By } from "@angular/platform-browser";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AppService } from "@appServices/common/app.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { PermissionsConfigurationService, PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BehaviorSubject, Subject, of } from "rxjs";
import { FacetItemListComponent } from "./facet-item-list.component";
import { CONSTANTS } from "@appConstants/constants";
import { Permission } from "@appShared/albertsons-angular-authorization/model/permission.model";

describe("FacetItemListComponent", () => {
  let component: FacetItemListComponent;
  let fixture: ComponentFixture<FacetItemListComponent>;
  beforeEach(() => {
    const baseInputSearchServiceStub = () => ({
      getDataForInputSearch: () => ({ subscribe: () => ({}) }),
      inputSearchOptions: [],
      getInputFieldSelected: () => ({}),
      generateQueryForOptions: () => ({}),
      formQuery: () => ({}),
      formQueryWithFilter: () => ({}),
      setInputSearchOptions: () => ({}),
      getFilterFieldSelected: () => ({}),
      setActiveCurrentSearchType: () => ({}),
      postDataForInputSearch: () => ({}),
      populateChipList: () => ({}),
      updateCategoryChipSavedSearch$: new Subject(), // Initialize as Subject
      getActiveCurrentSearchType: () => ({}),
      getActiveCurrentSearchType$: new BehaviorSubject({}).asObservable(),
    });
    const changeDetectorRefStub = () => ({ detectChanges: () => ({}) });
    const searchOfferRequestServiceStub = () => ({
      homeFilterSearchSourceSearch: new BehaviorSubject({}).asObservable(),
      fetchBUGMIds: () => ({}),
      fetchCategoriesIds: () => ({}),
      fetchProgramDetails: () => ({}),
      fetchSubProgramDetails: () => ({}),
    });
    const formBuilderStub = () => ({
      control: (facet) => ({}),
      array: (arr) => ({}),
    });
    const renderer2Stub = () => ({
      setAttribute: (nextSibling, string, string1) => ({}),
      addClass: (nextSibling, string) => ({}),
      removeClass: (nextSibling, string) => ({}),
    });
    const elementRefStub = () => ({});
    const appServiceStub = () => ({ getFeatureFlags: () => ({}) });
    const facetItemServiceStub = () => ({
      getOfferFilter: () => ({}),
      getFilterFacetFields: () => ({ forEach: () => ({}) }),
      populateStoreFacet: (facets, storesSearchCriteria) => ({}),
      getFacetItems: () => ({}),
      getdivsionStateFacetItems: () => ({}),
      showGroupsFacet: new BehaviorSubject(true),
    });
    const storeGroupServiceStub = () => ({
      getFeatureKeys: () => ({ includes: () => ({}) }),
      storeFilterSearchSourceSearch: { subscribe: () => ({}) },
    });
    const permissionsServiceStub = () => ({
      loadPermissions: (adminPermissions) => ({}),
      getPermissions: () => ({}),
    });
    const permissionsConfigurationServiceStub = () => ({
      addPermissionStrategy: (string, function0) => ({}),
      setDefaultOnUnauthorizedStrategy: (string) => ({}),
    });
    const featureFlagsServiceStub = () => ({
      isFeatureFlagEnabled: () => ({}),
      isEnableFreePerLbDiscountSPD: () => ({}),
      isEnableFreePerLbDiscountSC: () => ({}),
      isEnableFreePerLbDiscountGR: () => ({}),
      isEnableFreePerLbDiscountBPD: () => ({}),
      isNoDiscountEnabled: () => ({}),
      isEnableFreePerLbDiscount: () => ({}),
      isEnablePercentOffPerLbDiscountBPD: () => ({}),
      isEnablePercentOffPerLbDiscountSPD: () => ({}),
      isEnablePercentOffPerLbDiscountSC: () => ({}),
      isEnablePercentOffPerLbDiscountGR: () => ({}),
      isEnablePercentOffPerLbDiscount: () => ({}),
      isEnablePercentOffDiscount: () => ({}),
      isEnableFreeDiscount: () => ({}),
      isEnableContinuityPointsDiscountForSPD: () => ({}),
      isEnableContinuityPointsDiscountForSC: () => ({}),
      isEnableContinuityPointsDiscountForGR: () => ({}),
      isEnableContinuityPointsDiscountForBPD: () => ({}),
      isEnableContinuityPointsDiscount: () => ({}),
      isEnableFreeDiscountForSPD: () => ({}),
      isOfferRequestArchivalEnabled: () => ({}),
      isOfferArchivalEnabled: () => ({}),
    });

    const commonSearchServiceStub = () => ({
      batchActionActiveTab: {},
      setActiveCurrentSearchType: (currentRouter) => ({}),
      currentRouter: {},
      setAllFilterOptions: (object) => ({}),
      setFilters: (object) => ({}),
      resetAllFilterOptions: (object) => ({}),
      setIsHideExpiredStatusReqs: (object) => ({}),
      setInputSearchOption: (object) => ({}),
      setQueryOptionsForBpd: (object) => ({}),
      setFilterOption: (object) => ({}),
      setDefaultOption: (object) => ({}),
      populateFilterOption: (object) => ({}),
      isShowExpiredInQuery_O: () => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({}),
      getConfigUrls: (pRINT_AD_IMPORT_LOG_API) => ({}),
    });
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [FacetItemListComponent],
      providers: [
        { provide: ChangeDetectorRef, useFactory: changeDetectorRefStub },
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub,
        },
        { provide: UntypedFormBuilder, useFactory: formBuilderStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: StoreGroupService, useFactory: storeGroupServiceStub },
        { provide: ElementRef, useFactory: elementRefStub },
        { provide: AppService, useFactory: appServiceStub },
        { provide: Renderer2, useFactory: renderer2Stub },
        { provide: PermissionsService, useFactory: permissionsServiceStub },
        {
          provide: PermissionsConfigurationService,
          useFactory: permissionsConfigurationServiceStub,
        },
        {
          provide: BaseInputSearchService,
          useFactory: () => ({
            ...baseInputSearchServiceStub(),
            updateCategoryChipSavedSearch$: new Subject(),
          }),
        },
        { provide: FeatureFlagsService, useFactory: featureFlagsServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
      ],
    });
    fixture = TestBed.createComponent(FacetItemListComponent);
    component = fixture.componentInstance;
  });
  // private init: InitialDataService

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });
  it("totalStores defaults to: 0", () => {
    expect(component.totalStores).toEqual(0);
  });
  describe("buildFormControl", () => {
    it("call expected method", () => {
      const spy = spyOn(component, "buildFormControlList");
      component.buildFormControl({});
      expect(spy).toHaveBeenCalled();
    });
  });
  xdescribe("buildFormControlList", () => {
    it("should build expected list", () => {
      component.buildFormControlList([
        { testKey: "testValue", selected: true },
      ]);
      expect(component.form.control.selected.value).toEqual(true);
    });
  });
  describe("renderFacetItems", () => {
    it("set expected values", () => {
      const changeDetectorRefStub: ChangeDetectorRef =
        fixture.debugElement.injector.get(ChangeDetectorRef);
      component.form = new UntypedFormGroup({
        divisions: new UntypedFormArray([
          new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
        ]),
        divisionRogCds: new UntypedFormGroup({
          Acme: new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
          Portland: new UntypedFormArray([new UntypedFormControl("false")]),
        }),
      });
      component.mainFacetItems = {};
      const fcontrol = new UntypedFormArray([new UntypedFormControl("")]);

      spyOn(component, "buildFormControl").and.returnValue(fcontrol);
      spyOn(component, "buildStorFormStateGroup").and.returnValue(fcontrol);

      spyOn(changeDetectorRefStub, "detectChanges");
      component.renderFacetItems(
        {
          "programCd": [
              {
                  "id": "SC",
                  "count": 3658,
                  "value": "Store Coupon",
                  "selected": true,
                  "color": "",
                  "permission": "VIEW_OFFER_REQUESTS"
              },
              {
                  "id": "BPD",
                  "count": 0,
                  "value": "Base PD",
                  "selected": false,
                  "color": "",
                  "permission": "VIEW_BPD_OFFER_REQUESTS"
              },
              {
                  "id": "SPD",
                  "count": 0,
                  "value": "Specialty PD",
                  "selected": false,
                  "color": "",
                  "permission": "VIEW_GR_SPD_OFFER_REQUESTS"
              },
              {
                  "id": "GR",
                  "count": 0,
                  "value": "Grocery Reward",
                  "selected": false,
                  "color": "",
                  "permission": "VIEW_GR_SPD_OFFER_REQUESTS"
              },
              {
                  "id": "MF",
                  "count": 0,
                  "value": "Manufacturer Coupon",
                  "selected": false,
                  "color": "",
                  "permission": null
              }
          ],
          "deliveryChannel": [
              {
                  "id": "CC",
                  "count": 1009,
                  "value": "Clip and Click",
                  "color": ""
              },
              {
                  "id": "IS",
                  "count": 856,
                  "value": "In Store",
                  "color": ""
              },
              {
                  "id": "PO",
                  "count": 519,
                  "value": "Print Only",
                  "color": ""
              },
              {
                  "id": "EC",
                  "count": 54,
                  "value": "eComm Only",
                  "color": ""
              },
              {
                  "id": "BA",
                  "count": 32,
                  "value": "Behavioral",
                  "color": ""
              },
              {
                  "id": "Digital Only-In Ad",
                  "count": 838,
                  "value": "Digital Only-In Ad",
                  "color": ""
              },
              {
                  "id": "Digital Only-Not In Ad",
                  "count": 349,
                  "value": "Digital Only-Not In Ad",
                  "color": ""
              }
          ],
          "eCommPromoType": [
              {
                  "id": "EPC",
                  "count": 0,
                  "value": "eComm Promo Code",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "ESS",
                  "count": 0,
                  "value": "Schedule & Save",
                  "selected": false,
                  "color": ""
              }
          ],
          "offerType": [
              {
                  "id": "ITEM_DISCOUNT",
                  "count": 0,
                  "value": "Item Discount",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "BUYX_GETX",
                  "count": 0,
                  "value": "Buy X Get X",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "BUYX_GETY",
                  "count": 0,
                  "value": "Buy X Get Y",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "MEAL_DEAL",
                  "count": 0,
                  "value": "Meal Deal",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "BUNDLE",
                  "count": 0,
                  "value": "Bundle",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "MUST_BUY",
                  "count": 0,
                  "value": "Must Buy",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "FAB_5_OR_SCORE_4",
                  "count": 0,
                  "value": "Fab 5 / Score 4",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "WOD_OR_POD",
                  "count": 0,
                  "value": "WOD / POD",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "REWARDS_ACCUMULATION",
                  "count": 0,
                  "value": "Rewards - Accumulation",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "REWARDS_FLAT",
                  "count": 0,
                  "value": "Rewards - Flat",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "CONTINUITY",
                  "count": 0,
                  "value": "Continuity",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "INSTANT_WIN",
                  "count": 0,
                  "value": "Enterprise Instant Win",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "ALASKA_AIRMILES",
                  "count": 0,
                  "value": "Alaska Airmiles",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "STORE_CLOSURE",
                  "count": 0,
                  "value": "Store Closure",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "ITEM_PLUS_BASKET",
                  "count": 0,
                  "value": "Item + Basket",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "CUSTOM",
                  "count": 0,
                  "value": "Custom",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "DEPARTMENT",
                  "count": 0,
                  "value": "Department",
                  "selected": false,
                  "color": ""
              }
          ],
          "discountType": [
              {
                  "id": "AMOUNT_OFF",
                  "count": 0,
                  "value": "Cents Off",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "AMOUNT_OFF_WEIGHT_VOLUME",
                  "count": 0,
                  "value": "Cents Off (Per Lb)",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "FREE",
                  "count": 0,
                  "value": "Free",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "PRICE_POINT_ITEMS",
                  "count": 0,
                  "value": "Price Point (Items)",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "PRICE_POINT_WEIGHT_VOLUME",
                  "count": 0,
                  "value": "Price Point (Per Lb)",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "PERCENT_OFF_ITEMS",
                  "count": 0,
                  "value": "Percent Off",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "NO_DISCOUNT",
                  "count": 0,
                  "value": "No Discount",
                  "selected": false,
                  "color": ""
              }
          ],
          "group": [
              {
                  "id": "CORP",
                  "count": 0,
                  "value": "Corporate",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "DE",
                  "count": 0,
                  "value": "Denver",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "HG",
                  "count": 0,
                  "value": "Haggen",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "IM",
                  "count": 0,
                  "value": "Intermountain",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "JW",
                  "count": 0,
                  "value": "Jewel",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "MA",
                  "count": 0,
                  "value": "Mid-Atlantic",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "NC",
                  "count": 0,
                  "value": "Norcal",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "PT",
                  "count": 0,
                  "value": "Portland",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "SE",
                  "count": 0,
                  "value": "Seattle",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "SH",
                  "count": 0,
                  "value": "Shaws/Star Market",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "SC",
                  "count": 0,
                  "value": "SoCal",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "SO",
                  "count": 0,
                  "value": "Southern",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "SW",
                  "count": 0,
                  "value": "Southwest",
                  "selected": false,
                  "color": ""
              }
          ],
          "status": [
              {
                  "id": "DI",
                  "count": 0,
                  "value": "Digital",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "NDI",
                  "count": 0,
                  "value": "Non-Digital",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "I",
                  "count": 0,
                  "value": "Draft",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "S",
                  "count": 0,
                  "value": "Submitted",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "A",
                  "count": 0,
                  "value": "Assigned",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "P",
                  "count": 0,
                  "value": "Processing",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "E",
                  "count": 0,
                  "value": "Editing",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "U",
                  "count": 0,
                  "value": "Updating",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "D",
                  "count": 0,
                  "value": "Completed",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "C",
                  "count": 0,
                  "value": "Canceled",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "R",
                  "count": 0,
                  "value": "Removing",
                  "selected": false,
                  "color": ""
              },
              {
                  "id": "EX",
                  "count": 0,
                  "value": "Expired",
                  "selected": false,
                  "color": ""
              }
          ],
          "createdAppId": [
              {
                  "id": "OMS",
                  "count": 0,
                  "value": "OMS",
                  "color": ""
              },
              {
                  "id": "UPP",
                  "count": 0,
                  "value": "UPP",
                  "color": ""
              }
          ]
        },
        {
          
        }
      );
      expect(component.buildFormControl).toHaveBeenCalled();
      //expect(component.buildStorFormStateGroup).toHaveBeenCalled();
      expect(changeDetectorRefStub.detectChanges).not.toHaveBeenCalled();
    });
    it("should  trace else when no facets", () => {
      const changeDetectorRefStub: ChangeDetectorRef =
        fixture.debugElement.injector.get(ChangeDetectorRef);
      component.form = new UntypedFormGroup({});
      component.mainFacetItems = {};
      spyOn(changeDetectorRefStub, "detectChanges");
      component.renderFacetItems(
        {},
        {
          Portland: [
            { id: "SPRT", count: 128, value: "SPRT", selected: false },
          ],
        }
      );
      expect(changeDetectorRefStub.detectChanges).not.toHaveBeenCalled();
    });
  });

  describe("populateFacetHome", () => {
    it("set expected values and make expected calls", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.facetPage = "offerHome";
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetFilter"
      );
      const spy1 = spyOn(component, "renderFacetItems");
      const spy2 = spyOn(component, "removeFacets");
      spyOn(facetItemServiceStub, "getdivsionStateFacetItems");
      component.populateFacetHome({
        testFacetItemKey: "testFacetItemValue",
        testFacetItemKey2: "testFacetItemValue2",
      });
      expect(spy).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(facetItemServiceStub.getdivsionStateFacetItems).toHaveBeenCalled();
    });
    it("set expected values and make expected calls when facet page is Home", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.facetPage = "home";
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetFilter"
      );
      const spy1 = spyOn(component, "renderFacetItems");
      const spy2 = spyOn(component, "removeFacets");
      spyOn(facetItemServiceStub, "getdivsionStateFacetItems");
      component.populateFacetHome({
        testFacetItemKey: "testFacetItemValue",
        testFacetItemKey2: "testFacetItemValue2",
      });
      expect(spy).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(facetItemServiceStub.getdivsionStateFacetItems).toHaveBeenCalled();
    });
    it("set expected values and make expected calls when facet page is Home", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.facetPage = "home";
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetFilter"
      );
      const spy1 = spyOn(component, "renderFacetItems");
      const spy2 = spyOn(component, "removeFacets");
      spyOn(facetItemServiceStub, "getdivsionStateFacetItems");
      component.populateFacetHome({
        testFacetItemKey: "testFacetItemValue",
        testFacetItemKey2: "testFacetItemValue2",
      });
      expect(spy).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(facetItemServiceStub.getdivsionStateFacetItems).toHaveBeenCalled();
    });
    it("set expected values and make expected calls when facet page is null", () => {
      component.facetPage = null;
      const spy = spyOn(component, "renderFacetItems");
      component.populateFacetHome({
        testFacetItemKey: "testFacetItemValue",
        testFacetItemKey2: "testFacetItemValue2",
      });
      expect(spy).not.toHaveBeenCalled();
    });
    it("set expected values and make expected calls when offer filter is facetSearch", () => {
      component.facetPage = null;
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.facetPage = "home";
      spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetSearch"
      );
      const spy1 = spyOn(component, "renderFacetItems");
      const spy4 = spyOn(facetItemServiceStub, "getdivsionStateFacetItems");
      component.populateFacetHome({
        testFacetItemKey: "testFacetItemValue",
        testFacetItemKey2: "testFacetItemValue2",
      });
      expect(spy1).toHaveBeenCalled();
      expect(component.mainFacetItems).toEqual(null);
    });
    it("set expected values and make expected calls when offer filter is facetSearch", () => {
      component.facetPage = null;
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.facetPage = "home";
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetSearch"
      );
      const spy1 = spyOn(component, "renderFacetItems");
      const spy4 = spyOn(facetItemServiceStub, "getdivsionStateFacetItems");
      component.populateFacetHome({});
      expect(spy).toHaveBeenCalled();
      expect(spy1).not.toHaveBeenCalled();
      expect(component.mainFacetItems).toEqual(null);
    });
    it("set expected values and make expected calls when facetPage is offerPODPage", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      const changeDetectorRefStub: ChangeDetectorRef =
        fixture.debugElement.injector.get(ChangeDetectorRef);
      component.mainFacetItems = {
        divisions: [
          { id: "Acme", count: 164, value: "Acme", selected: true },
          { id: "Denver", count: 123, value: "Denver", selected: false },
          { id: "Eastern", count: 111, value: "Eastern", selected: false },
        ],
        couponTypeNm: {},
        vehicleTypNm: {},
        color: {},
        isImageFound: {},
        isProofed: {},
        offerLinked: {},
      };
      component.facetPage = "offerPODPage";
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetSearch"
      );
      const spy1 = spyOn(component, "renderFacetItems");
      spyOn(changeDetectorRefStub, "detectChanges");
      const spy4 = spyOn(facetItemServiceStub, "getdivsionStateFacetItems");
      component.populateFacetHome({
        testFacetItemKey: "testFacetItemValue",
        testFacetItemKey2: "testFacetItemValue2",
      });
      expect(spy).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalled();
      expect(component.mainFacetItems).not.toEqual(null);
    });
  });

  describe("ngOnInit", () => {
    it("makes expected calls if facet page is home", () => {
      component.mainFacetItems = {};
      component.facetPage = "home";
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = of({});
      baseInputSearchServiceStub.onClearBggmBugmChip$ = new Subject();
      searchOfferRequestServiceStub.onClearSubProgramCdChip$ = new Subject();

      const spy = spyOn(component, "populateFacetHome");
      component.ngOnInit();
      expect(spy).not.toHaveBeenCalled();
    });
    it("makes expected calls if facet page is offerHome", () => {
      component.mainFacetItems = {};
      component.facetPage = "offerHome";
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = of({});
      baseInputSearchServiceStub.onClearBggmBugmChip$ = new Subject();
      searchOfferRequestServiceStub.onClearSubProgramCdChip$ = new Subject();

      const spy = spyOn(component, "populateFacetHome");
      component.ngOnInit();
      expect(spy).not.toHaveBeenCalled();
    });
    // it("makes expected calls if facet page is storeGroup populateStoreFacet", () => {
    //   component.mainFacetItems = {};
    //   component.facetPage = "storeGroup";
    //   const storeGroupServiceStub: StoreGroupService =
    //     fixture.debugElement.injector.get(StoreGroupService);
    //   const searchOfferRequestServiceStub: SearchOfferRequestService =
    //     fixture.debugElement.injector.get(SearchOfferRequestService);
    //   const baseInputSearchServiceStub: BaseInputSearchService =
    //     fixture.debugElement.injector.get(BaseInputSearchService);

    //   const facetItemServiceStub: FacetItemService =
    //     fixture.debugElement.injector.get(FacetItemService);
    //   storeGroupServiceStub.storeFilterSearchSourceSearch = of({});

    //   baseInputSearchServiceStub.onClearBggmBugmChip$ = new Subject();
    //   searchOfferRequestServiceStub.onClearSubProgramCdChip$ = new Subject();
    //   spyOn(facetItemServiceStub, "populateStoreFacet");
    //   component.ngOnInit();
    //   expect(facetItemServiceStub.populateStoreFacet).toHaveBeenCalled();
    // });
    it("makes expected calls if facet page is storeGroup populateFacetHome", () => {
      component.mainFacetItems = {};
      component.facetPage = "storeGroup";

      const storeGroupServiceStub: StoreGroupService =
        fixture.debugElement.injector.get(StoreGroupService);
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = of({});
      baseInputSearchServiceStub.onClearBggmBugmChip$ = new Subject();
      searchOfferRequestServiceStub.onClearSubProgramCdChip$ = new Subject();
      spyOn(component, "populateFacetHome");
      component.ngOnInit();
      expect(component.populateFacetHome).not.toHaveBeenCalled();
    });

    it("cover else pasrt if facet page is storeGroup but service returns null ", () => {
      component.mainFacetItems = {};
      component.facetPage = "storeGroup";
      const storeGroupServiceStub: StoreGroupService =
        fixture.debugElement.injector.get(StoreGroupService);
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = of({});
      baseInputSearchServiceStub.onClearBggmBugmChip$ = new Subject();
      searchOfferRequestServiceStub.onClearSubProgramCdChip$ = new Subject();
      const spy = spyOn(component, "populateStoreFacet");
      component.ngOnInit();
      expect(spy).not.toHaveBeenCalled();
    });
    it("cover else pasrt if facet page is storeGroup but service returns null ", () => {
      component.mainFacetItems = {};
      component.facetPage = "offerHome";
      const renderer = fixture.debugElement.injector.get(Renderer2);
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      const storeGroupServiceStub: StoreGroupService =
        fixture.debugElement.injector.get(StoreGroupService);
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = of({});
      baseInputSearchServiceStub.onClearBggmBugmChip$ = new Subject();
      searchOfferRequestServiceStub.onClearSubProgramCdChip$ = new Subject();
      let de = fixture.debugElement.queryAll(
        By.css("facets-title[data-target='#offerRequestorGroup']")
      );
      component.ngOnInit();
    });
  });

  describe("populateStoreFacet", () => {
    it("make expected call in covering if part", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        "facetFilter"
      );
      const spy1 = spyOn(component, "removeFacets");
      component.populateStoreFacet({
        facetClose: "facetFilter",
        render: false,
      });
      expect(spy).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalled();
    });
    it("make expected call in covering else part", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      const spy = spyOn(facetItemServiceStub, "getOfferFilter").and.returnValue(
        ""
      );
      const spy1 = spyOn(facetItemServiceStub, "populateStoreFacet");
      const spy2 = spyOn(component, "renderStoreFacetItems");
      const spy3 = spyOn(facetItemServiceStub, "getFacetItems");
      component.populateStoreFacet({ facetClose: "facetFilter" });
      expect(spy).toHaveBeenCalled();
      expect(spy1).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(spy3).toHaveBeenCalled();
    });
  });

  describe("removeFacets", () => {
    it("should make expected calls", () => {
      const storeGroupServiceStub: StoreGroupService =
        fixture.debugElement.injector.get(StoreGroupService);
      spyOn(storeGroupServiceStub, "getFeatureKeys").and.returnValue([
        "divisions",
        "Starbucks",
        "Delivery",
      ]);
      component.form = new UntypedFormGroup({
        divisions: new UntypedFormArray([
          new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
        ]),
        divisionRogCds: new UntypedFormGroup({
          Acme: new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
          Portland: new UntypedFormArray([new UntypedFormControl("false")]),
        }),
      });
      spyOn(component, "removeDivisionRogs");
      component.removeFacets("divisions");
      expect(component.removeDivisionRogs).toHaveBeenCalled();
    });
    it("should trace else when no form controls", () => {
      const storeGroupServiceStub: StoreGroupService =
        fixture.debugElement.injector.get(StoreGroupService);
      component.form = "";
      spyOn(component, "removeDivisionRogs");
      spyOn(storeGroupServiceStub, "getFeatureKeys").and.returnValue([]);
      component.removeFacets("divisions");
      expect(component.removeDivisionRogs).not.toHaveBeenCalled();
    });
  });

  describe("removeDivisionRogs", () => {
    it("should make expected calls", () => {
      const renderer2Stub: Renderer2 =
        fixture.debugElement.injector.get(Renderer2);
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormGroup({
          Acme: new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
          Portland: new UntypedFormArray([new UntypedFormControl("false")]),
        }),
      });
      const divisionRogCds = component.form.get("divisionRogCds");
      spyOn(renderer2Stub, "removeClass");
      component.removeDivisionRogs("divisions", divisionRogCds);
      expect(renderer2Stub.removeClass).toHaveBeenCalled();
    });
    it("should trace else when item is not divisions", () => {
      const renderer2Stub: Renderer2 =
        fixture.debugElement.injector.get(Renderer2);
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormGroup({
          Acme: new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
          Portland: new UntypedFormArray([new UntypedFormControl("false")]),
        }),
      });
      const divisionRogCds = component.form.get("divisionRogCds");
      spyOn(renderer2Stub, "removeClass");
      component.removeDivisionRogs("podStatus", divisionRogCds);
      expect(renderer2Stub.removeClass).not.toHaveBeenCalled();
    });
  });

  describe("onFacetClick", () => {
    it("should make expected calls", () => {
      const event = {
        divisions: [
          { id: "Acme", count: 0, value: "Acme", selected: true, color: "" },
          false,
        ],
      };
      component.onFacetClick({
        form: component.form,
        event: event,
        item: null,
      });
    });
  });

  describe("populatePODList", () => {
    it("should make expected calls", () => {
      component.form = new UntypedFormGroup({
        digital: new UntypedFormArray([
          new UntypedFormGroup({
            count: new UntypedFormControl(0),
            id: new UntypedFormControl("isApplicableToJ4U"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Digital"),
            color: new UntypedFormControl(""),
          }),
          new UntypedFormControl(false),
        ]),
      });
      component.populatePODList(true);
    });
    it("should trace else when podView false", () => {
      component.form = new UntypedFormGroup({
        digital: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
        ]),
      });
      component.populatePODList(false);
      expect(component.form.get("digital").value).toEqual([false, false]);
    });
    it("should trace else when no formControls", () => {
      component.form = new UntypedFormGroup({});
      component.populatePODList(false);
      expect(component.form.get("digital")).toEqual(null);
    });
  });

  describe("populatePageModQuery", () => {
    it("should make expected calls", () => {
      spyOn(component, "clearChannel");
      component.form = new UntypedFormGroup({
        deliveryChannel: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
          new UntypedFormControl(false),
          new UntypedFormGroup({
            count: new UntypedFormControl(0),
            id: new UntypedFormControl("Digital Only-In Ad"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Digital Only-In Ad"),
            color: new UntypedFormControl(""),
          }),
          new UntypedFormControl(false),
          new UntypedFormControl(false),
        ]),
      });
      component.populatePageModQuery("pageMod");
      expect(component.clearChannel).toHaveBeenCalled();
    });
    it("should trace else when podFilter has changed", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.form = new UntypedFormGroup({
        deliveryChannel: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
          new UntypedFormGroup({
            count: new UntypedFormControl(0),
            id: new UntypedFormControl("Digital Only-In Ad"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Digital Only-In Ad"),
            color: new UntypedFormControl(""),
          }),
          new UntypedFormControl(false),
        ]),
      });
      Object.assign(facetItemServiceStub, { podFilterChanged: true });
      spyOn(component, "clearChannel");
      component.populatePageModQuery("headLine");
      expect(component.clearChannel).toHaveBeenCalled();
    });
    it("should trace else when podFilter has changed", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.form = new UntypedFormGroup({
        deliveryChannel: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
          new UntypedFormControl(false),
          new UntypedFormGroup({
            count: new UntypedFormControl("20"),
            id: new UntypedFormControl("Digital Only-In Ad"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Digital Only-In Ad"),
            color: new UntypedFormControl("green"),
          }),
          new UntypedFormControl(false),
          new UntypedFormControl(false),
        ]),
      });
      Object.assign(facetItemServiceStub, { podFilterChanged: false });
      spyOn(component, "clearChannel");
      component.populatePageModQuery("headLine");
      expect(component.clearChannel).not.toHaveBeenCalled();
    });
  });

  xdescribe("clearChannel", () => {
    it("should make expected calls", () => {
      component.form = {
        deliveryChannel: new UntypedFormArray([
          new UntypedFormControl({
            count: 20,
            id: "Digital Only-In Ad",
            selected: true,
            value: "Digital Only-In Ad",
            color: "green",
          }),
        ]),
      };
      component.clearChannel(component.form.get("deliveryChannel"));
      expect(component.form.get("deliveryChannel")).toEqual([
        false,
        false,
        false,
        false,
        false,
        false,
      ]);
    });
  });

  describe("buildFormControlList", () => {
    it("should make expected calls", () => {
      const facetItems = [
        { id: "PD", count: 0, value: "PD", selected: false, color: "" },
        { id: "SPD", count: 0, value: "Sp PD", selected: false, color: "" },
        {
          id: "SC",
          count: 26107,
          value: "Store Coupon",
          selected: true,
          color: "",
        },
        {
          id: "GR",
          count: 0,
          value: "Grocery Reward",
          selected: false,
          color: "",
        },
        {
          id: "MF",
          count: 0,
          value: "Manufacturer Coupon",
          selected: false,
          color: "",
        },
      ];
      const res = component.buildFormControlList(facetItems);

      expect(res).not.toBeNull();
    });
  });

  describe("buildStoreFormControl", () => {
    it("should make expected calls", () => {
      const facetItem = [
        { id: "Acme", count: 164, value: "Acme", selected: false },
        { id: "Denver", count: 123, value: "Denver", selected: false },
        { id: "Eastern", count: 111, value: "Eastern", selected: false },
        { id: "Haggen", count: 15, value: "Haggen", selected: false },
        { id: "InterMtn", count: 88, value: "InterMtn", selected: false },
        { id: "JewelOsco", count: 188, value: "JewelOsco", selected: false },
        { id: "Norcal", count: 284, value: "Norcal", selected: true },
      ];
      const res = component.buildStoreFormControl(facetItem, "divisions");

      expect(res).not.toBeNull();
    });
  });

  describe("buildStorFormStateGroup", () => {
    it("should make expected calls", () => {
      const divisions = [
        { id: "Acme", count: 164, value: "Acme", selected: false },
        { id: "Denver", count: 123, value: "Denver", selected: false },
        { id: "Eastern", count: 111, value: "Eastern", selected: false },
        { id: "Haggen", count: 15, value: "Haggen", selected: false },
        { id: "InterMtn", count: 88, value: "InterMtn", selected: false },
        { id: "JewelOsco", count: 188, value: "JewelOsco", selected: false },
        { id: "Norcal", count: 284, value: "Norcal", selected: true },
      ];
      const res = component.buildStorFormStateGroup(divisions);

      expect(res).not.toBeNull();
    });
  });

  describe("renderStoreFacetItems", () => {
    it("should make expected calls", () => {
      const fcontrol = new UntypedFormArray([new UntypedFormControl("")]);
      spyOn(component, "buildStorFormStateGroup").and.returnValue(fcontrol);
      spyOn(component, "buildStoreFormControl").and.returnValue(fcontrol);
      const facetItems = {
          divisions: [
            { id: "Acme", count: 164, value: "Acme", selected: true },
            { id: "Denver", count: 123, value: "Denver", selected: false },
            { id: "Eastern", count: 111, value: "Eastern", selected: false },
          ],
        },
        stateItems = {
          Acme: { count: 150, id: "ACME", selected: true, value: "ACME" },
          Denver: { count: 111, id: "SDEN", selected: false, value: "SDEN" },
        };
      component.mainFacetItems = {
        divisions: [
          { id: "Acme", count: 164, value: "Acme", selected: true },
          { id: "Denver", count: 123, value: "Denver", selected: false },
        ],
      };
      component.renderStoreFacetItems(facetItems, stateItems, 30, true);
      expect(component.totalStores).toEqual(30);
    });
    it("should trace else when render is false", () => {
      const fcontrol = new UntypedFormArray([new UntypedFormControl("")]);
      spyOn(component, "buildStorFormStateGroup").and.returnValue(fcontrol);
      spyOn(component, "buildStoreFormControl").and.returnValue(fcontrol);
      const facetItems = {
          divisions: [
            { id: "Acme", count: 164, value: "Acme", selected: false },
            { id: "Denver", count: 123, value: "Denver", selected: false },
            { id: "Eastern", count: 111, value: "Eastern", selected: false },
          ],
        },
        stateItems = {
          Acme: { count: 150, id: "ACME", selected: true, value: "ACME" },
          Denver: { count: 111, id: "SDEN", selected: false, value: "SDEN" },
        };
      component.mainFacetItems = {
        divisions: [
          { id: "Acme", count: 164, value: "Acme", selected: true },
          { id: "Denver", count: 123, value: "Denver", selected: false },
        ],
      };
      component.renderStoreFacetItems(facetItems, stateItems, 10, false);
      expect(component.totalStores).toEqual(10);
    });
  });

  describe("clearFacetItemsFormGroup", () => {
    it("should clear the form group and reset mainFacetItems for the given item", () => {
      component.mainFacetItems = {
        divisions: [{ id: "Acme", count: 164, value: "Acme", selected: true }],
      };
      component.form = new UntypedFormGroup({
        divisions: new UntypedFormArray([
          new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
        ]),
      });

      spyOn(component.form.get("divisions") as UntypedFormArray, "clear");

      component.clearFacetItemsFormGroup("divisions");

      expect(component.mainFacetItems["divisions"]).toEqual([]);
      expect(
        (component.form.get("divisions") as UntypedFormArray).clear
      ).toHaveBeenCalled();
    });

    it("should not clear the query and showChip if clear is set to false", () => {
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      spyOn(baseInputSearchServiceStub, "getFilterFieldSelected").and.returnValue({
        query: ["testQuery"],
        showChip: ["testChip"],
      });

      component.mainFacetItems = {
        divisions: [{ id: "Acme", count: 164, value: "Acme", selected: true }],
      };
      component.form = new UntypedFormGroup({
        divisions: new UntypedFormArray([
          new UntypedFormGroup({
            count: new UntypedFormControl(150),
            id: new UntypedFormControl("Acme"),
            selected: new UntypedFormControl(true),
            value: new UntypedFormControl("Acme"),
          }),
        ]),
      });

      spyOn(component.form.get("divisions") as UntypedFormArray, "clear");

      component.clearFacetItemsFormGroup("divisions", false);

      expect(component.mainFacetItems["divisions"]).toEqual([]);
      expect(
        (component.form.get("divisions") as UntypedFormArray).clear
      ).toHaveBeenCalled();
      expect(
        baseInputSearchServiceStub.getFilterFieldSelected
      ).toHaveBeenCalledWith("divisions");
    });

    it("should not throw an error if form control is null", () => {
      component.mainFacetItems = {
        divisions: [{ id: "Acme", count: 164, value: "Acme", selected: true }],
      };
      component.form = new UntypedFormGroup({});
    });
  });

  describe("setStartDate and setEndDate", () => {
    it("should not throw errors when called", () => {
      expect(() => component.setStartDate()).not.toThrow();
      expect(() => component.setEndDate()).not.toThrow();
      expect(() => component.setFacetFilter()).not.toThrow();
    });
  });

  describe("getPermissionsByProgramCode", () => {
    it("should return 'VIEW_OFFERS' for BPD, SPD, or GR when facetPage is 'offerHome'", () => {
      const permissionsServiceStub: PermissionsService =
        fixture.debugElement.injector.get(PermissionsService);
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({});
      component.facetPage = "offerHome";

      expect(component.getPermissionsByProgramCode(CONSTANTS.BPD, "offerHome")).toEqual("VIEW_OFFERS");
      expect(component.getPermissionsByProgramCode(CONSTANTS.SPD, "offerHome")).toEqual("VIEW_OFFERS");
      expect(component.getPermissionsByProgramCode(CONSTANTS.GR, "offerHome")).toEqual("VIEW_OFFERS");
    });

    it("should return 'DO_STORE_COUPON_OFFERS' if permission exists for SC when facetPage is 'offerHome'", () => {
      const permissionsServiceStub: PermissionsService =
        fixture.debugElement.injector.get(PermissionsService);
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        DO_STORE_COUPON_OFFERS: { name: 'DO_STORE_COUPON_OFFERS', granted: true } as Permission,
      });
      component.facetPage = "offerHome";

      expect(component.getPermissionsByProgramCode(CONSTANTS.SC, "offerHome")).toEqual("DO_STORE_COUPON_OFFERS");
    });

    it("should return 'VIEW_OFFERS' if permission does not exist for SC when facetPage is 'offerHome'", () => {
      const permissionsServiceStub: PermissionsService =
        fixture.debugElement.injector.get(PermissionsService);
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({});
      component.facetPage = "offerHome";

      expect(component.getPermissionsByProgramCode(CONSTANTS.SC, "offerHome")).toEqual("VIEW_OFFERS");
    });

    it("should return 'VIEW_MF_OFFERS' for MF when facetPage is 'offerHome'", () => {
      const permissionsServiceStub: PermissionsService =
        fixture.debugElement.injector.get(PermissionsService);
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({});
      component.facetPage = "offerHome";

      expect(component.getPermissionsByProgramCode(CONSTANTS.MF, "offerHome")).toEqual("VIEW_MF_OFFERS");
    });

    it("should return null for unknown programCode when facetPage is 'offerHome'", () => {
      const permissionsServiceStub: PermissionsService =
        fixture.debugElement.injector.get(PermissionsService);
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({});
      component.facetPage = "offerHome";

      expect(component.getPermissionsByProgramCode("UNKNOWN_CODE", "offerHome")).toBeNull();
    });
  });

  describe("getBPDPermissions", () => {
    it("should return 'VIEW_OFFERS' when facetPage is 'offerHome'", () => {
      component.facetPage = "offerHome";
      const result = component.getBPDPermissions();
      expect(result).toEqual("VIEW_OFFERS");
    });

    it("should return 'VIEW_BPD_OFFER_REQUESTS' when facetPage is 'home'", () => {
      component.facetPage = "home";
      const result = component.getBPDPermissions();
      expect(result).toEqual("VIEW_BPD_OFFER_REQUESTS");
    });

    it("should return 'VIEW_BPD_OFFER_REQUESTS' when facetPage is 'template'", () => {
      component.facetPage = "template";
      const result = component.getBPDPermissions();
      expect(result).toEqual("VIEW_BPD_OFFER_REQUESTS");
    });

    it("should return undefined when facetPage is not 'offerHome', 'home', or 'template'", () => {
      component.facetPage = "unknownPage";
      const result = component.getBPDPermissions();
      expect(result).toBeUndefined();
    });
  });

  describe("clearChannel", () => {
    it("should set all controls in the channel to false", () => {
      const channel = new UntypedFormArray([
        new UntypedFormControl(true),
        new UntypedFormControl(true),
        new UntypedFormControl(true),
      ]);

      component.clearChannel(channel);

      expect(channel.value).toEqual([false, false, false]);
    });

    it("should not throw an error if the channel is empty", () => {
      const channel = new UntypedFormArray([]);

      expect(() => component.clearChannel(channel)).not.toThrow();
      expect(channel.value).toEqual([]);
    });

    it("should handle a channel with mixed initial values", () => {
      const channel = new UntypedFormArray([
        new UntypedFormControl(true),
        new UntypedFormControl(false),
        new UntypedFormControl(true),
      ]);

      component.clearChannel(channel);

      expect(channel.value).toEqual([false, false, false]);
    });

    it("should not modify the channel if it is null", () => {
      const channel = null;

      expect(channel).toBeNull();
    });
  });

  describe("setDisplayExpiredStatusVal", () => {
    it("should set isHideExpiredStatusReqs to false when status is expired", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: [{ value: CONSTANTS.EXPIRED_STATUS_OR_DISPLAY }],
      };

      component.setDisplayExpiredStatusVal({ form, item: "status" });

    });

    it("should not set flags when item is not 'status'", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: [{ value: CONSTANTS.EXPIRED_STATUS_OR_DISPLAY }],
      };

      component.setDisplayExpiredStatusVal({ form, item: "offerStatus" });

      expect(commonSearchServiceStub.setIsHideExpiredStatusReqs).not.toHaveBeenCalled();
      expect(commonSearchServiceStub.setQueryOptionsForBpd).not.toHaveBeenCalled();
    });
  });

  describe("clearBggmBugmOnChipClick", () => {
    it("should clear bugm and categoryId when chip is 'bggm'", async () => {
      spyOn(component, "clearFacetItemsFormGroup");
      spyOn(component, "populateFacetItem");
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      spyOn(searchOfferRequestServiceStub, "fetchBUGMIds").and.returnValue(Promise.resolve({}));

      await component.clearBggmBugmOnChipClick("bggm");

      expect(component.clearFacetItemsFormGroup).toHaveBeenCalledWith("bugm");
      expect(component.clearFacetItemsFormGroup).toHaveBeenCalledWith("categoryId");
      expect(searchOfferRequestServiceStub.fetchBUGMIds).toHaveBeenCalledWith("*");
      expect(component.populateFacetItem).toHaveBeenCalled();
    });

    it("should only clear categoryId when chip is not 'bggm'", async () => {
      spyOn(component, "clearFacetItemsFormGroup");

      await component.clearBggmBugmOnChipClick("bugm");

      expect(component.clearFacetItemsFormGroup).toHaveBeenCalledWith("categoryId");
      expect(component.clearFacetItemsFormGroup).not.toHaveBeenCalledWith("bugm");
    });
  });

  describe("onFacetClick", () => {
    it("should call setFilterOptions and emit facetListClick when item is 'programCode'", () => {
      spyOn(component, "setFilterOptions");
      spyOn(component.facetListClick, "emit");

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "programCode" });

      expect(component.facetListClick.emit).toHaveBeenCalledWith(form);
    });

    it("should call setDisplayExpiredStatusVal when item is not 'programCode'", () => {
      spyOn(component, "setDisplayExpiredStatusVal");

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "status" });
    });

    it("should call populateFacetHome when facetPage is 'template'", () => {
      spyOn(component, "getFacetForTemplate");
      component.facetPage = "template";

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "status" });

      expect(component.getFacetForTemplate).toHaveBeenCalled();
    });
  });

  describe("setFilterOptions", () => {
    it("should set filter options for BPD", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      spyOn(commonSearchServiceStub, "setAllFilterOptions");
      spyOn(commonSearchServiceStub, "setInputSearchOption");
      spyOn(commonSearchServiceStub, "setFilterOption");
      spyOn(commonSearchServiceStub, "setDefaultOption");
      spyOn(baseInputSearchServiceStub, "setActiveCurrentSearchType");

      component.setFilterOptions();

      expect(commonSearchServiceStub.setAllFilterOptions).toHaveBeenCalled();
      expect(commonSearchServiceStub.setInputSearchOption).toHaveBeenCalled();
      expect(commonSearchServiceStub.setFilterOption).toHaveBeenCalled();
      expect(commonSearchServiceStub.setDefaultOption).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.setActiveCurrentSearchType).toHaveBeenCalledWith(CONSTANTS.BPD);
    });
  });

  describe("populateFacetItem", () => {
    it("should call renderFacetItem with the correct parameters", () => {
      spyOn(component, "renderFacetItem");

      const list = {
        key1: "value1",
        key2: "value2",
      };

      component.populateFacetItem(list, "testItem");

      expect(component.renderFacetItem).toHaveBeenCalledWith(
        [
          { id: "key1", value: "value1", selected: false },
          { id: "key2", value: "value2", selected: false },
        ],
        "testItem"
      );
    });

    it("should not call renderFacetItem if list is null", () => {
      spyOn(component, "renderFacetItem");

      component.populateFacetItem(null, "testItem");

      expect(component.renderFacetItem).not.toHaveBeenCalled();
    });
  });

  describe("renderFacetItem", () => {
    it("should populate formArrayControl with the correct values", () => {
      component.mainFacetItems = {};
      component.form = new UntypedFormGroup({
        testItem: new UntypedFormArray([]),
      });

      const itemData = [
        { id: "key1", value: "value1", selected: true },
        { id: "key2", value: "value2", selected: false },
      ];

      component.renderFacetItem(itemData, "testItem");

      const formArrayControl = component.form.get("testItem") as UntypedFormArray;

    });

    it("should update mainFacetItems with the correct data", () => {
      component.mainFacetItems = {};
      component.form = new UntypedFormGroup({
        testItem: new UntypedFormArray([]),
      });

      const itemData = [
        { id: "key1", value: "value1", selected: true },
        { id: "key2", value: "value2", selected: false },
      ];

      component.renderFacetItem(itemData, "testItem");
    });

    it("should call detectChanges after updating the form", () => {
      const changeDetectorRefStub: ChangeDetectorRef =
        fixture.debugElement.injector.get(ChangeDetectorRef);
      spyOn(changeDetectorRefStub, "detectChanges");

      component.mainFacetItems = {};
      component.form = new UntypedFormGroup({
        testItem: new UntypedFormArray([]),
      });

      const itemData = [
        { id: "key1", value: "value1", selected: true },
        { id: "key2", value: "value2", selected: false },
      ];

      component.renderFacetItem(itemData, "testItem");
    });

    it("should not throw an error if form control is null", () => {
      component.mainFacetItems = {};
      component.form = new UntypedFormGroup({});

      const itemData = [
        { id: "key1", value: "value1", selected: true },
        { id: "key2", value: "value2", selected: false },
      ];

      expect(() => component.renderFacetItem(itemData, "testItem")).not.toThrow();
    });
  });

  xdescribe("ngOnInit - Filtering Logic", () => {
    it("should filter out FREE_WEIGHT_VOLUME when shouldFilterFreeWeightVolume condition is met", () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isEnableFreePerLbDiscountBPD").and.returnValue(false);
      spyOn(featureFlagsServiceStub, "isEnableFreePerLbDiscountSPD").and.returnValue(true);
      spyOn(featureFlagsServiceStub, "isEnableFreePerLbDiscountSC").and.returnValue(true);
      spyOn(featureFlagsServiceStub, "isEnableFreePerLbDiscountGR").and.returnValue(true);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = new BehaviorSubject({
        facetFilter: {
          discountType: [
            { id: "FREE_WEIGHT_VOLUME", value: "Free Weight Volume", selected: true },
            { id: "PERCENT_OFF_WEIGHT_VOLUME", value: "Percent Off Weight Volume", selected: true },
          ],
        },
      }).asObservable();

      component.facetPage = "offerHome";
      component['facetItemService'].programCodeSelected = CONSTANTS.BPD;

      spyOn(component, "populateFacetHome");

      component.ngOnInit();

      expect(component.populateFacetHome).toHaveBeenCalledWith({
        discountType: [{ id: "PERCENT_OFF_WEIGHT_VOLUME", value: "Percent Off Weight Volume", selected: true }],
      });
    });

    it("should filter out PERCENT_OFF_WEIGHT_VOLUME when shouldFilterPercentOffWeightVolume condition is met", () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isEnablePercentOffPerLbDiscountBPD").and.returnValue(false);
      spyOn(featureFlagsServiceStub, "isEnablePercentOffPerLbDiscountSPD").and.returnValue(true);
      spyOn(featureFlagsServiceStub, "isEnablePercentOffPerLbDiscountSC").and.returnValue(true);
      spyOn(featureFlagsServiceStub, "isEnablePercentOffPerLbDiscountGR").and.returnValue(true);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch =  new BehaviorSubject({
        facetFilter: {
          discountType: [
            { id: "FREE_WEIGHT_VOLUME", value: "Free Weight Volume", selected: true },
            { id: "PERCENT_OFF_WEIGHT_VOLUME", value: "Percent Off Weight Volume", selected: true },
          ],
        },
      }).asObservable();

      component.facetPage = "offerHome";
      component['facetItemService'].programCodeSelected = CONSTANTS.BPD;

      spyOn(component, "populateFacetHome");

      component.ngOnInit();

      expect(component.populateFacetHome).toHaveBeenCalledWith({
        discountType: [{ id: "FREE_WEIGHT_VOLUME", value: "Free Weight Volume", selected: true }],
      });
    });

    it("should filter out POINTS when shouldFilterPoints condition is met", () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isEnableContinuityPointsDiscountForSPD").and.returnValue(false);
      spyOn(featureFlagsServiceStub, "isEnableContinuityPointsDiscountForSC").and.returnValue(true);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = new BehaviorSubject({
        facetFilter: {
          discountType: [
            { id: "POINTS", value: "Points", selected: true },
            { id: "NO_DISCOUNT", value: "No Discount", selected: true },
          ],
        },
      }).asObservable();

      component.facetPage = "offerHome";
      component['facetItemService'].programCodeSelected = CONSTANTS.SPD;

      spyOn(component, "populateFacetHome");

      component.ngOnInit();

      expect(component.populateFacetHome).toHaveBeenCalledWith({
        discountType: [{ id: "NO_DISCOUNT", value: "No Discount", selected: true }],
      });
    });

    it("should filter out NO_DISCOUNT when shouldFilterNoDiscount condition is met", () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isNoDiscountEnabled").and.returnValue(false);

      searchOfferRequestServiceStub.homeFilterSearchSourceSearch = of({
        facetFilter: {
          discountType: [
            { id: "POINTS", value: "Points", selected: true },
            { id: "NO_DISCOUNT", value: "No Discount", selected: true },
          ],
        },
      });

      component.facetPage = "offerHome";
      component['facetItemService'].programCodeSelected = CONSTANTS.BPD;

      spyOn(component, "populateFacetHome");

      component.ngOnInit();

      expect(component.populateFacetHome).toHaveBeenCalledWith({
        discountType: [{ id: "POINTS", value: "Points", selected: true }],
      });
    });
  });

  describe("populateFacetListItems", () => {
    it("should call clearFacetItemsFormGroup and populateFacetItem for bggm and bugm", async () => {
      spyOn(component, "clearFacetItemsFormGroup");
      spyOn(component, "populateFacetItem");
      spyOn(component, "setValueForSavedSearchFilter");
      spyOn(component, "updateChipForSavedSearchCategoryId");

      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      spyOn(searchOfferRequestServiceStub, "fetchBUGMIds").and.returnValue(Promise.resolve({ key1: "value1" }));
      spyOn(searchOfferRequestServiceStub, "fetchCategoriesIds").and.returnValue(Promise.resolve({ key2: "value2" }));

      const facet = [
        { field: "bggm", query: ["bggm1", "bggm2"] },
        { field: "bugm", query: ["bugm1"] },
      ];

      await component.populateFacetListItems(facet);

      expect(component.clearFacetItemsFormGroup).toHaveBeenCalledWith("categoryId", false);
      expect(component.clearFacetItemsFormGroup).toHaveBeenCalledWith("bugm", false);
      expect(searchOfferRequestServiceStub.fetchBUGMIds).toHaveBeenCalledWith("bggm1 OR bggm2");
      expect(component.populateFacetItem).toHaveBeenCalledWith({ key1: "value1" }, "bugm");
      expect(component.populateFacetItem).toHaveBeenCalledWith({ key2: "value2" }, "categoryId");
      expect(component.setValueForSavedSearchFilter).toHaveBeenCalledWith(facet, "bggm");
      expect(component.setValueForSavedSearchFilter).toHaveBeenCalledWith(facet, "bugm");
      expect(component.updateChipForSavedSearchCategoryId).toHaveBeenCalledWith(facet, { key2: "value2" });
    });

  });

  describe("updateChipForSavedSearchCategoryId", () => {
    it("should update showChip array for categoryId in facet when categoryId is selected", () => {
      const facet = [
        { field: "categoryId", query: ["cat1", "cat2"], showChip: [] },
      ];
      const categoriesList = {
        cat1: "Category 1",
        cat2: "Category 2",
      };

      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);
      spyOn(baseInputSearchServiceStub, "populateChipList");
      spyOn(baseInputSearchServiceStub.updateCategoryChipSavedSearch$, "next");

      component.updateChipForSavedSearchCategoryId(facet, categoriesList);

      expect(facet[0].showChip).toEqual(["Category 1", "Category 2"]);
      expect(baseInputSearchServiceStub.populateChipList).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.updateCategoryChipSavedSearch$.next).toHaveBeenCalledWith(null);
    });

    it("should not update showChip array if categoryId is not selected", () => {
      const facet = [
        { field: "status", query: ["active"], showChip: [] },
      ];
      const categoriesList = {
        cat1: "Category 1",
        cat2: "Category 2",
      };

      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);
      spyOn(baseInputSearchServiceStub, "populateChipList");
      spyOn(baseInputSearchServiceStub.updateCategoryChipSavedSearch$, "next");

      component.updateChipForSavedSearchCategoryId(facet, categoriesList);

      expect(facet[0].showChip).toEqual([]);
      expect(baseInputSearchServiceStub.populateChipList).not.toHaveBeenCalled();
      expect(baseInputSearchServiceStub.updateCategoryChipSavedSearch$.next).not.toHaveBeenCalled();
    });

    it("should handle empty categoriesList gracefully", () => {
      const facet = [
        { field: "categoryId", query: ["cat1", "cat2"], showChip: [] },
      ];
      const categoriesList = {};

      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);
      spyOn(baseInputSearchServiceStub, "populateChipList");
      spyOn(baseInputSearchServiceStub.updateCategoryChipSavedSearch$, "next");

      component.updateChipForSavedSearchCategoryId(facet, categoriesList);

      expect(facet[0].showChip).toEqual(["cat1", "cat2"]);
      expect(baseInputSearchServiceStub.populateChipList).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.updateCategoryChipSavedSearch$.next).toHaveBeenCalledWith(null);
    });

    it("should handle empty facet gracefully", () => {
      const facet = [];
      const categoriesList = {
        cat1: "Category 1",
        cat2: "Category 2",
      };

      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);
      spyOn(baseInputSearchServiceStub, "populateChipList");
      spyOn(baseInputSearchServiceStub.updateCategoryChipSavedSearch$, "next");

      component.updateChipForSavedSearchCategoryId(facet, categoriesList);

      expect(baseInputSearchServiceStub.populateChipList).not.toHaveBeenCalled();
      expect(baseInputSearchServiceStub.updateCategoryChipSavedSearch$.next).not.toHaveBeenCalled();
    });

    it("should handle null facet gracefully", () => {
      const facet = null;
      const categoriesList = {
        cat1: "Category 1",
        cat2: "Category 2",
      };

      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);
      spyOn(baseInputSearchServiceStub, "populateChipList");
      spyOn(baseInputSearchServiceStub.updateCategoryChipSavedSearch$, "next");

      component.updateChipForSavedSearchCategoryId(facet, categoriesList);

      expect(baseInputSearchServiceStub.populateChipList).not.toHaveBeenCalled();
      expect(baseInputSearchServiceStub.updateCategoryChipSavedSearch$.next).not.toHaveBeenCalled();
    });
  });

  describe("setValueForSavedSearchFilter", () => {
    it("should update form controls based on the saved search filter", () => {
      const facet = [
        { field: "status", query: ["active", "inactive"] },
      ];
      const filterName = "status";

      component.mainFacetItems = {
        status: [
          { id: "active", value: "Active", selected: false },
          { id: "inactive", value: "Inactive", selected: false },
          { id: "pending", value: "Pending", selected: false },
        ],
      };

      component.form = new UntypedFormGroup({
        status: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
          new UntypedFormControl(false),
        ]),
      });

      component.setValueForSavedSearchFilter(facet, filterName);

      const formArray = component.form.get(filterName) as UntypedFormArray;
    });

    it("should handle empty facet gracefully", () => {
      const facet = [];
      const filterName = "status";

      component.mainFacetItems = {
        status: [
          { id: "active", value: "Active", selected: false },
          { id: "inactive", value: "Inactive", selected: false },
        ],
      };

      component.form = new UntypedFormGroup({
        status: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
        ]),
      });

      component.setValueForSavedSearchFilter(facet, filterName);

      const formArray = component.form.get(filterName) as UntypedFormArray;
      expect(formArray.at(0).value).toBeFalse();
      expect(formArray.at(1).value).toBeFalse();
    });

    it("should handle null facet gracefully", () => {
      const facet = null;
      const filterName = "status";

      component.mainFacetItems = {
        status: [
          { id: "active", value: "Active", selected: false },
          { id: "inactive", value: "Inactive", selected: false },
        ],
      };

      component.form = new UntypedFormGroup({
        status: new UntypedFormArray([
          new UntypedFormControl(false),
          new UntypedFormControl(false),
        ]),
      });

      component.setValueForSavedSearchFilter(facet, filterName);

      const formArray = component.form.get(filterName) as UntypedFormArray;
      expect(formArray.at(0).value).toBeFalse();
      expect(formArray.at(1).value).toBeFalse();
    });

    it("should not throw an error if form control is null", () => {
      const facet = [
        { field: "status", query: ["active"] },
      ];
      const filterName = "status";

      component.mainFacetItems = {
        status: [
          { id: "active", value: "Active", selected: false },
        ],
      };

      component.form = new UntypedFormGroup({});

    });

    it("should handle cases where mainFacetItems is null", () => {
      const facet = [
        { field: "status", query: ["active"] },
      ];
      const filterName = "status";

      component.mainFacetItems = null;

      component.form = new UntypedFormGroup({
        status: new UntypedFormArray([
          new UntypedFormControl(false),
        ]),
      });
    });
  });

  describe("postDataForInputSearch", () => {
    it("should call populateFilterOption and populateChipList", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      spyOn(commonSearchServiceStub, "populateFilterOption");
      spyOn(baseInputSearchServiceStub, "populateChipList");

      const event = {
        item: "status",
        form: {
          status: [{ value: "Expired" }],
        },
      };

      component.postDataForInputSearch(event);

      expect(commonSearchServiceStub.populateFilterOption).toHaveBeenCalled();
      expect(baseInputSearchServiceStub.populateChipList).toHaveBeenCalled();
    });

    it("should not throw an error if event is null", () => {
      const baseInputSearchServiceStub: BaseInputSearchService =
        fixture.debugElement.injector.get(BaseInputSearchService);

      spyOn(baseInputSearchServiceStub, "postDataForInputSearch");
    });
  });

  describe("onFacetClick", () => {
    it("should call setFilterOptions and emit facetListClick when item is 'programCode' and BPD is selected", () => {
      spyOn(component, "setFilterOptions");
      spyOn(component.facetListClick, "emit");

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "programCode" });
    });

    it("should call setDisplayExpiredStatusVal when item is not 'programCode' and BPD is selected", () => {
      spyOn(component, "setDisplayExpiredStatusVal");

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "status" });

    });

    it("should call getFacetForTemplate when facetPage is 'template'", () => {
      spyOn(component, "getFacetForTemplate");
      component.facetPage = "template";

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "status" });

      expect(component.getFacetForTemplate).toHaveBeenCalledWith({
        form,
        item: "status",
      });
    });

    it("should call populatePrgmTypeBasedOnSubPrgmCd when isDivisionalGamesEnabled is true and item is 'subProgramCode'", () => {
      spyOn(component, "populatePrgmTypeBasedOnSubPrgmCd");
      spyOnProperty(component, "isDivisionalGamesEnabled", "get").and.returnValue(true);
      component.facetPage = "home";

      const form = {
        subProgramCode: [{ selected: true, id: "Base" }],
      };

      component.onFacetClick({ form, event: {}, item: "subProgramCode" });

      expect(component.populatePrgmTypeBasedOnSubPrgmCd).toHaveBeenCalledWith({
        form,
        item: "subProgramCode",
      });
    });

    it("should emit facetListClick when isDivisionalGamesEnabled is false and item is not 'programCode'", () => {
      spyOn(component.facetListClick, "emit");
      spyOnProperty(component, "isDivisionalGamesEnabled", "get").and.returnValue(false);
      component.facetPage = "home";

      const form = {
        programCode: [{ selected: true, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "status" });

    });

    it("should not call setFilterOptions or emit facetListClick when programCode is not selected", () => {
      spyOn(component, "setFilterOptions");
      spyOn(component.facetListClick, "emit");

      const form = {
        programCode: [{ selected: false, id: "BPD" }],
      };

      component.onFacetClick({ form, event: {}, item: "programCode" });

      expect(component.setFilterOptions).not.toHaveBeenCalled();
    });
  });

  describe("postDataForInputSearch", () => {
    it("should set isShowExpiredInQuery_O when item is 'offerStatus' and 'Expired' status is present", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isOfferArchivalEnabled").and.returnValue(true);

      const event = {
        item: "offerStatus",
        form: {
          offerStatus: [{ value: "Expired" }],
        },
      };

      component.postDataForInputSearch(event);
    });

    it("should not set isShowExpiredInQuery_O when item is 'offerStatus' and 'Expired' status is not present", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isOfferArchivalEnabled").and.returnValue(true);

      const event = {
        item: "offerStatus",
        form: {
          offerStatus: [{ value: "Active" }],
        },
      };

      component.postDataForInputSearch(event);
    });

    it("should not set isShowExpiredInQuery_O when item is not 'offerStatus'", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      const featureFlagsServiceStub: FeatureFlagsService =
        fixture.debugElement.injector.get(FeatureFlagsService);

      spyOn(featureFlagsServiceStub, "isOfferArchivalEnabled").and.returnValue(true);

      const event = {
        item: "status",
        form: {
          offerStatus: [{ value: "Expired" }],
        },
      };

      component.postDataForInputSearch(event);
    });
  });

  describe("setDisplayExpiredStatusVal", () => {
    it("should set isHideExpiredStatusReqs to false when status contains EXPIRED_STATUS_OR_DISPLAY", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: [
          { value: CONSTANTS.EXPIRED_STATUS_OR_DISPLAY },
          { value: "ACTIVE" },
        ],
      };

      component.setDisplayExpiredStatusVal({ form, item: "status" });

    });

    it("should set isHideExpiredStatusReqs to true when status does not contain EXPIRED_STATUS_OR_DISPLAY", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: [
          { value: "ACTIVE" },
          { value: "INACTIVE" },
        ],
      };

      component.setDisplayExpiredStatusVal({ form, item: "status" });

    });

    it("should not modify isHideExpiredStatusReqs when item is not 'status'", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: [
          { value: CONSTANTS.EXPIRED_STATUS_OR_DISPLAY },
          { value: "ACTIVE" },
        ],
      };

      component.setDisplayExpiredStatusVal({ form, item: "offerStatus" });

      expect(commonSearchServiceStub.setIsHideExpiredStatusReqs).not.toHaveBeenCalled();
      expect(commonSearchServiceStub.setQueryOptionsForBpd).not.toHaveBeenCalled();
    });

    it("should handle empty status array gracefully", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: [],
      };

      component.setDisplayExpiredStatusVal({ form, item: "status" });

    });

    it("should handle null status gracefully", () => {
      const commonSearchServiceStub: CommonSearchService =
        fixture.debugElement.injector.get(CommonSearchService);
      spyOn(commonSearchServiceStub, "setIsHideExpiredStatusReqs");
      spyOn(commonSearchServiceStub, "setQueryOptionsForBpd");

      const form = {
        status: null,
      };

      component.setDisplayExpiredStatusVal({ form, item: "status" });

    });
  });
});

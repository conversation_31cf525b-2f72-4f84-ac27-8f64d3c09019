import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2 } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { StoreGroupService } from '@appGroupsServices/store-group.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { StringUtilsHelperService } from '@appServices/common/string-utils.helper.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';

@Component({
  selector: 'facet-item-list',
  templateUrl: './facet-item-list.component.html',
  styleUrls: ['./facet-item-list.component.scss']
})
export class FacetItemListComponent implements OnInit, OnDestroy {


  key = Object.keys;
  @Input() facetPage;
  @Input() createStoreGroup;
  facetList;
  @Output() facetListClick = new EventEmitter<any>();
  private facetFilterConst = CONSTANTS.FACET_FILTER;
  
  form;
  totalStores: number = 0;
  mainFacetItems: any = null;
  facetShow = {};
  private homeFilterSearchSourceSearch;
  storeFilterSearchSourceSearch: any;  

  constructor(private storeGroupService: StoreGroupService,
    private facetItemService: FacetItemService,
    private cdr: ChangeDetectorRef, private fb: UntypedFormBuilder, private _searchOfferRequestService: SearchOfferRequestService,
    private el: ElementRef,
    private _featureFlagService: FeatureFlagsService,
    private baseInputSearchService:BaseInputSearchService,
    private commonSearchService:CommonSearchService,
    private renderer: Renderer2, private init: InitialDataService,
    private stringHelper:StringUtilsHelperService,
    private _permissionsService: PermissionsService ) {
  }
  buildFormControl(facetItem) {
    return this.buildFormControlList(facetItem);
  }
  buildFormControlList(facetItem) {
    const arr = facetItem.map(facet => {
      const { selected } = facet;
      if (selected) {
        return this.fb.control(facet);
      } else {
        return this.fb.control(selected);
      }
    })
    return this.fb.array(arr);
  }
  buildStoreFormControl(facetItem, item) {
    const arr = Object.keys(facetItem).map(facet => {
      const { selected } = facetItem[facet];
      if (selected) {
        if (selected !== 'all') {
          this.facetShow[item] = true;
        }

        return this.fb.control(facetItem[facet]);
      } else {
        return this.fb.control(selected);
      }
    })
    return this.fb.array(arr);
  }
  renderStoreFacetItems(facetItems, stateItems, totalStores, render) {
    let facets = Object.keys(facetItems);
    let statesFacets = Object.keys(stateItems);
    if (totalStores) {
      this.totalStores = totalStores;
    }
    
    if (render) {
      if (facets.length) {
        Object.keys(this.mainFacetItems).forEach((item: any) => {
          const facet = this.mainFacetItems[item], facetItem = {};
          if (facetItems[item]) {
            facetItems[item].reduce((obj, item) => {
              obj[item['id']] = item['count'];
              return obj;
            }, facetItem);
            facet.forEach(element => {
              const ele = facetItem[element['id']];
              if (ele) {
                element['count'] = ele;
              } else {
                element['count'] = 0;
              }
            });
          } else {
            facet.map(item => item.count = 0);
          }

        })
      }
    } else {
      if (facets.length) {
        facets.splice(0, 0, facets.splice(facets.indexOf('divisions'), 1)[0]);
        this.mainFacetItems = {};
        this.form = null;
        this.cdr.detectChanges();
        let obj = {};
        let obj2 = {};
        facets.forEach((item: any) => {
          this.mainFacetItems[item] = facetItems[item];
          obj[item] = this.buildStoreFormControl(facetItems[item], item);
        })
        statesFacets.forEach((item: any) => {
          obj2[item] = this.buildStorFormStateGroup(stateItems[item]);
        })
        let divisionRogCds = new UntypedFormGroup(obj2)
        this.form = new UntypedFormGroup({ ...obj, divisionRogCds });

      }
    }
  }
  buildStorFormStateGroup(division) {
    const arr = Object.keys(division).map(state => {
      const { selected } = division[state];
      if (selected) {
        return this.fb.control(division[state]);
      } else {
        return this.fb.control(selected);
      }
    });
    this.fb.array(arr);
    return this.fb.array(arr);
  }
  clearFacetItemsFormGroup(item,clear=true){
   const filter = this.baseInputSearchService.getFilterFieldSelected(item);
   if(clear && filter){
    filter.query = [];
    filter.showChip = [];
   }
   this.mainFacetItems[item] = [];
   const formArrayControl = this.form.get(item) as UntypedFormArray;
   formArrayControl?.clear();
  }
  renderFacetItem(itemData,item){
    const _item = this.facetFilterConst[item];
    if (_item) {
      this.mainFacetItems[_item.field] = itemData;
      const formArrayControl = this.form.get(item) as UntypedFormArray;
      itemData.forEach(element => {
        formArrayControl.push(this.fb.control(element.selected));
      });
    }
    this.cdr.detectChanges();
  }
  renderFacetItems(facetItems,stateItems) {
    let facetlength = Object.keys(facetItems);
    if (facetlength.length) {
      this.mainFacetItems = {};
      this.form = null;
      this.cdr.detectChanges();
      let obj = {};
      let obj2 = {};
      let divisionRogCds;
      Object.keys(facetItems).forEach((item: any) => {
        let itemData = facetItems[item];

        if (item === 'programCd') {
          item = 'programCode';
        }
        if (item === 'divisionId') {
          item = 'divisions';
        }
        const _item = this.facetFilterConst[item];
        if (_item) {
          this.mainFacetItems[_item.field] = itemData; 
          if(item == 'createdAppId')
          {
            if(this._featureFlagService.isUPPFieldSearchEnabled)       
              obj[_item.field] = this.buildFormControl(itemData);
          }
          else{
            obj[_item.field] = this.buildFormControl(itemData);
          }
        }
        let statesFacets = Object.keys(stateItems);
        if (statesFacets && statesFacets.length) {

          statesFacets.forEach((item: any) => {
            obj2[item] = this.buildStorFormStateGroup(stateItems[item]);
          })
          divisionRogCds = new UntypedFormGroup(obj2);
        }
      })
      if (divisionRogCds) {
        this.form = new UntypedFormGroup({ ...obj, divisionRogCds });
      } else {
        this.form = new UntypedFormGroup(obj);
      }

    }
  }
  removeFacets(item) {
    const features = this.storeGroupService.getFeatureKeys();
    if (this.form) {
      const controls = this.form.controls[item];
      const divisionRogCds = this.form.get("divisionRogCds");
      if (controls) {
        if (item !== 'divisionRogCds') {
          this.form.controls[item].controls.forEach(element => {
            if (features.includes(item)) {
              element.value['selected'] = 'all';
            } else {
              element.setValue(false);
            }
          });
        }
      }
      this.removeDivisionRogs(item, divisionRogCds);
    }
  }
  
  removeDivisionRogs(item, divisionRogCds) {
    if (item === 'divisions' && divisionRogCds && divisionRogCds.controls) {
      const divControls = divisionRogCds.controls;
      Object.keys(divControls).forEach((division) => {
        const divisionRogForm = divisionRogCds['controls'] && divisionRogCds['controls'][division];
        this.renderer.removeClass(this.el.nativeElement.querySelector(`#custom-check-${this.stringHelper.escapeValue(division)}`), 'partiallyFilledCB');
        const divisionRogFormArray = divisionRogForm.controls as UntypedFormArray;
        for (let i = 0; i < divisionRogFormArray.length; i++) {
          divisionRogForm.at(i).setValue(false);
        }
      })
    }
  }
  setStartDate() {
    // intentionally left empty
  }
  setEndDate() {
    // intentionally left empty
  }

  getPermissionsByProgramCode(programCode, facetPage) {
      const permissions = this._permissionsService.getPermissions();

      // Common cases for facetPage "offerHome"
      if (facetPage === "offerHome") {
          switch (programCode) {
              case CONSTANTS.BPD:
              case CONSTANTS.SPD:
              case CONSTANTS.GR:
                  return "VIEW_OFFERS";
              case CONSTANTS.SC:
                  return permissions["DO_STORE_COUPON_OFFERS"] ? "DO_STORE_COUPON_OFFERS" : "VIEW_OFFERS";
              case CONSTANTS.MF:
                  return "VIEW_MF_OFFERS";
              default:
                  return null;
          }
      }

      // Other cases based on programCode and facetPage
      const programPermissions = {
          [CONSTANTS.BPD]: {
              home: "VIEW_BPD_OFFER_REQUESTS",
              template: "VIEW_BPD_OFFER_REQUESTS"
          },
          [CONSTANTS.SPD]: {
              default: "VIEW_GR_SPD_OFFER_REQUESTS"
          },
          [CONSTANTS.SC]: {
              default: "VIEW_OFFER_REQUESTS"
          },
          [CONSTANTS.GR]: {
              default: "VIEW_GR_SPD_OFFER_REQUESTS"
          }
      };

      // Return specific permission if it exists, otherwise null
      return programPermissions[programCode]?.[facetPage] || programPermissions[programCode]?.default || null;
  }

  populateFacetHome(facetListItems) {
    let tempProgramCd = facetListItems?.programCd || facetListItems?.offerProgramCd || facetListItems?.programCode;
   
    if (facetListItems && tempProgramCd) {
      let _discountTypes =  facetListItems.discountType.sort((a, b) => a.id.localeCompare(b.id));
      facetListItems.discountType =_discountTypes;
      //setting the permission as part of the facet item
      tempProgramCd.map((programCode) => {
        programCode.permission = [CONSTANTS.SC, CONSTANTS.SPD, CONSTANTS.GR, CONSTANTS.BPD, CONSTANTS.MF].includes(programCode.id)
            ? this.getPermissionsByProgramCode(programCode.id, this.facetPage)
            : null;
      });
    
    if(this.facetPage!=='offerHome' && this.facetPage!=='template'  ){
        
        tempProgramCd.map((programCode) =>{
          /**
           * Returns a new array with selected flag as true from the service observable programCodeSelected in facetItemService
           * used throughout the application
           */
          programCode.id === this.facetItemService.programCodeSelected ? programCode.selected = true : programCode.selected = false;
        });
      }

      if(this.facetPage==='template' ){
        tempProgramCd.map((programCode) =>{
          /**
           * Returns a new array with selected flag as true from the service observable programCodeSelected in facetItemService
           * used throughout the application
           */
          programCode.id === this.facetItemService.templateProgramCodeSelected ? programCode.selected=true : programCode.selected=false;
        }); 

      }
      
    }

    const offerFilter = this.facetItemService.getOfferFilter(), fields = facetListItems && Object.keys(facetListItems);
    this.facetItemService.facetItemComponent = this;

    if ((this.facetPage === "offerPODPage" ||  this.facetPage === "offerHome" || this.facetPage === "home" ||
     this.facetPage === "template")) {
      if (offerFilter === "facetFilter" && facetListItems) {
        this.removeFacets(facetListItems.chip);
      }
      if (fields && !fields.includes('facetClose') && facetListItems) {
        if (fields.length) {
          const divisionStatesObj = this.facetItemService.getdivsionStateFacetItems();
          if (!divisionStatesObj || (divisionStatesObj && !Object.keys(divisionStatesObj).length)) {
            this.facetItemService.populateStoreFacet(facetListItems, facetListItems.storesSearchCriteria, facetListItems.divisionRogCds);
          }
          delete facetListItems.divisionRogCds;
          this.renderFacetItems(facetListItems, this.facetItemService.getdivsionStateFacetItems());

          if (this.facetPage === 'offerPODPage' && this.mainFacetItems.divisions 
          && Object.keys(this.mainFacetItems).length === 7) {
            let podFacet: any = null;
            podFacet = this.mainFacetItems;
            podFacet = JSON.parse(JSON.stringify(podFacet, ["divisions", "couponTypeNm", "vehicleTypNm", "color", "isImageFound", "isProofed", "offerLinked"], 4));
            podFacet.color = this.mainFacetItems.color;
            podFacet.couponTypeNm = this.mainFacetItems.couponTypeNm;
            this.mainFacetItems.divisions.sort(this.sortByProperty('value'));
            podFacet.divisions = this.mainFacetItems.divisions;
            podFacet.isImageFound = this.mainFacetItems.isImageFound;
            podFacet.isProofed = this.mainFacetItems.isProofed;
            podFacet.offerLinked = this.mainFacetItems.offerLinked;
            podFacet.vehicleTypNm = this.mainFacetItems.vehicleTypNm;
            this.mainFacetItems = null;
            this.mainFacetItems = podFacet;
            this.cdr.detectChanges();
          }
        } else if (offerFilter === "facetSearch") {
          this.mainFacetItems = null;
          this.cdr.detectChanges();
        }
      }
    }
  }

  getBPDPermissions(){
    if(this.facetPage === "offerHome"){
      return "VIEW_OFFERS";
    }else if(["home","template"].includes(this.facetPage) ){
      return "VIEW_BPD_OFFER_REQUESTS";
    }
  }

  sortByProperty(property) {
    return (x, y) => {
      return ((x[property] === y[property]) ? 0 : ((x[property] > y[property]) ? 1 : -1));
    };
  }
  populatePageModQuery(selectedItem){
    const channel = this.form?.get('deliveryChannel') as UntypedFormArray;
    if(selectedItem === 'pageMod' ){
      this.clearChannel(channel);
      channel.at(3).setValue({ color: "", count: 0, id: "Digital Only-In Ad", selected: true, value: "Digital Only-In Ad" });
    } else if (this.facetItemService.podFilterChanged) {
      this.clearChannel(channel);
    }
  }

  clearChannel(channel) {
    for (let control of channel.controls) {
      control.setValue(false);
    }
  }

  populatePODList(podView) {
    const digital = this.form.get("digital") as UntypedFormArray;
    if (digital && digital.length) {
      if (podView) {
        digital.at(1).setValue({ color: "", count: 0, id: "isApplicableToJ4U", selected: true, value: "Digital" });
      } else {
        digital.at(1).setValue(false);
      }
    }

  }
  populateStoreFacet(facetListItems) {
    const offerFilter = this.facetItemService.getOfferFilter();
    if (!facetListItems.render && offerFilter === "facetFilter" && facetListItems && facetListItems.facetClose === "facetFilter") {
      this.removeFacets(facetListItems.chip);
    } else {
      this.facetItemService.populateStoreFacet(facetListItems.facets, facetListItems.storesSearchCriteria, facetListItems.divisionRogCds);
      this.renderStoreFacetItems(this.facetItemService.getFacetItems(), this.facetItemService.getdivsionStateFacetItems(),
        facetListItems.totalStores, false);
    }

  }
  doHide(item) {
    const facetListControls = this.form?.get(item)
    return facetListControls?.length ? false : true;
  }
  ngOnInit() {
    this.mainFacetItems = {};

    this.homeFilterSearchSourceSearch = this._searchOfferRequestService.homeFilterSearchSourceSearch.
    subscribe((facetListItems: any) => {
      if (["offerPODPage","home","offerHome","template"].includes(this.facetPage) && facetListItems) {
        if(facetListItems.facetFilter)
        {
          let pgSelected = this.facetItemService?.programCodeSelected;
          if (facetListItems.facetFilter?.discountType) {
              let itemData = facetListItems.facetFilter.discountType;

              const shouldFilterFreeWeightVolume = [
                  [!this._featureFlagService.isEnableFreePerLbDiscountBPD, CONSTANTS.BPD],
                  [!this._featureFlagService.isEnableFreePerLbDiscountSPD, CONSTANTS.SPD],
                  [!this._featureFlagService.isEnableFreePerLbDiscountSC, CONSTANTS.SC],
                  [!this._featureFlagService.isEnableFreePerLbDiscountGR, CONSTANTS.GR]
              ];

              const shouldFilterPercentOffWeightVolume = [
                  [!this._featureFlagService.isEnablePercentOffPerLbDiscountBPD, CONSTANTS.BPD],
                  [!this._featureFlagService.isEnablePercentOffPerLbDiscountSPD, CONSTANTS.SPD],
                  [!this._featureFlagService.isEnablePercentOffPerLbDiscountSC, CONSTANTS.SC],
                  [!this._featureFlagService.isEnablePercentOffPerLbDiscountGR, CONSTANTS.GR]
              ];
              const shouldFilterPoints =   [
                [!this._featureFlagService.isEnableContinuityPointsDiscountForSPD, CONSTANTS.SPD],
                [!this._featureFlagService.isEnableContinuityPointsDiscountForSC, CONSTANTS.SC]
              ]
              
              const shouldFilterNoDiscount = [
                [!this._featureFlagService.isNoDiscountEnabled, CONSTANTS.SPD],
                [this._featureFlagService.isNoDiscountEnabled, CONSTANTS.SC],
                [this._featureFlagService.isNoDiscountEnabled, CONSTANTS.GR],
                [this._featureFlagService.isNoDiscountEnabled, CONSTANTS.BPD]
              
              ];
              
              shouldFilterFreeWeightVolume.forEach(([shouldFilter, pgCode]) => {
                  if (shouldFilter && [pgCode].includes(pgSelected)) {
                      itemData = itemData.filter(x => x.id !== "FREE_WEIGHT_VOLUME");
                  }
              });

              shouldFilterPercentOffWeightVolume.forEach(([shouldFilter, pgCode]) => {
                  if (shouldFilter && [pgCode].includes(pgSelected)) {
                      itemData = itemData.filter(x => x.id !== "PERCENT_OFF_WEIGHT_VOLUME");
                  }
              });
              shouldFilterPoints.forEach(([shouldFilter, pgCode]) => {
                if (shouldFilter && [pgCode].includes(pgSelected)) {
                    itemData = itemData.filter(x => x.id !== "POINTS");
                }
              });
              shouldFilterNoDiscount.forEach(([shouldFilter, pgCode]) => {
                if (shouldFilter && [pgCode].includes(pgSelected)) {
                    itemData = itemData.filter(x => x.id !== "NO_DISCOUNT");
                }
              });

              facetListItems.facetFilter.discountType = itemData;
          }
          this.populateFacetHome(facetListItems.facetFilter);
        }
      }
    });

    if (this.facetPage === 'storeGroup') {
      this.storeFilterSearchSourceSearch = this.storeGroupService.storeFilterSearchSourceSearch.subscribe((list: any) => {
        if (list) {
          this.populateStoreFacet(list);
        }
      })
    }

    if(this.facetPage ==="template" || (this.facetPage === "home" || this.facetPage === "offerHome")){
      
      this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}BehaviorSubject`]?.subscribe(response=>{
        this.setFacetFilter();
      });

      this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}FacetFilterBehaviorSubject`]?.subscribe(facet=>{
        if(facet) {
          if(toString.call(facet)==="[object Array]"){
            const filteredFacet = facet.filter((ele)=> ele?.query?.length && ele.field !== "programCode");
            if(filteredFacet?.length) {
              this.populateFacetListItems(facet);
            }
          }else{
            const facetArray = this.form?.get(facet) as UntypedFormArray;
            if(facetArray){
             for (let index = 0; index < facetArray.length; index++) {
               const element = facetArray.at(index) as UntypedFormControl;
               element.setValue(false);
             }
            }
          }
        }       
      });
      
    }
    this.baseInputSearchService.onClearBggmBugmChip$.subscribe((chipObj: any) => {
      const { chip = null } = chipObj;
      if(chip) {
        this.clearBggmBugmOnChipClick(chip)
      }
    })
    this._searchOfferRequestService.onClearSubProgramCdChip$.subscribe((chipObj: any) => {
      const { chip = null } = chipObj;
      if(chip) {
        this.clearFacetItemsFormGroup("programType");
        this.populateFacetItem([],"programType");
      }
    })
  }
  setFacetFilter(){
    // intentionally left empty
  }
  async  populateFacetListItems(facet){

    const filterValue = facet.reduce((out,ele)=>{out[ele.field]=ele.query;return out;},{});
    const  removeSpace = (ele)=>`${ele?.trim().replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
    const replaceSlash = (ele) => toString.call(ele) === "[object String]" ? ele.replace(/[\/\\]/g,'') : ele ; 
    const bggmDescIds = filterValue["bggm"].map(ele => replaceSlash(ele)).map(ele=>removeSpace(ele)),
          bugmDescIds = filterValue["bugm"].map(ele => replaceSlash(ele)).map(ele=>removeSpace(ele));
    let bggmDescQuery = "*",bugmDescQuery = "*";

    bggmDescQuery = bggmDescIds?.join(" OR ") || bggmDescQuery;
    bugmDescQuery = bugmDescIds?.join(" OR ") || bugmDescQuery;

    if(bggmDescIds?.length || bugmDescIds?.length) {
      this.clearFacetItemsFormGroup("categoryId",false);
      this.clearFacetItemsFormGroup("bugm",false);
      const bugmList = await this._searchOfferRequestService.fetchBUGMIds(bggmDescQuery);
      const categoriesList = await this._searchOfferRequestService.fetchCategoriesIds(bggmDescQuery ,bugmDescQuery,true);
      this.populateFacetItem(bugmList,"bugm");
      if(bugmDescIds?.length){
        this.populateFacetItem(categoriesList,"categoryId");
      }
      ["bggm","bugm","categoryId"].forEach((filterName)=> this.setValueForSavedSearchFilter(facet, filterName));
      this.updateChipForSavedSearchCategoryId(facet, categoriesList);
    } 
    facet?.forEach(async ele=>{
      if(!["bugm","categoryId", "bggm"].includes(ele.field)){
        this.setValueForSavedSearchFilter(facet, ele.field);
      }
    });
  }
  /**
   *   when we apply a saved search for categories, we need to show category name in chip instead of ID which is coming from BE
   *   This method will get category Id from query and update show chip array for category to display correct chip
   */
  updateChipForSavedSearchCategoryId(facet, categoriesList) {
    const isCategorySelected = facet?.find(facetObj => facetObj.field === "categoryId" && facetObj?.query?.length > 0);
    if(isCategorySelected) {
      const updatedChipValue = isCategorySelected?.query?.map(ele => categoriesList[ele] ? categoriesList[ele] : ele);
      isCategorySelected.showChip = updatedChipValue;
      this.baseInputSearchService.populateChipList();
      this.baseInputSearchService.updateCategoryChipSavedSearch$.next(null);
    }
  }
  setValueForSavedSearchFilter(facet, filterName) {

    const facetItem = this.mainFacetItems[filterName],
    filterObj = facet?.find((ele)=> ele.field === filterName),
    selectedFacet = facetItem?.reduce((output,fact,id)=> 
    { 
      const updatedQuery = filterObj?.query?.map(ele => toString.call(ele) === "[object String]" ?  ele.replace(/[\/\\]/g,'') : ele);            
      if(updatedQuery?.includes(fact?.id)){
        fact.selected = true;
        output[id] = fact;
        } else {
          output[id] = false;
        }
        return output;
  
    },{});
 const formArray = this.form.get(filterName) as UntypedFormArray;
 selectedFacet && Object.keys(selectedFacet).forEach((selFact:any)=>{
   const factElement = formArray.at(selFact) as UntypedFormControl;
     factElement.setValue(selectedFacet[selFact]);
 
 });
  }
  
  async getFacetForTemplate(event){
    const {item,form:{bggm:bggmDesc,bugm:bugmDesc}} = event,
     removeSpace = (ele)=>`${ele?.trim().replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;

     if(["bggm","bugm"].includes(item)){
     
      let bggmDescQuery = "*",bugmDescQuery = "*";
      const bggmDescIds = bggmDesc?.filter(ele=>ele).map(ele=>removeSpace(ele.id)),
      bugmDescIds = bugmDesc?.filter(ele=>ele).map(ele=>removeSpace(ele.id));
      this.clearFacetItemsFormGroup("categoryId");
      bggmDescQuery = bggmDescIds?.join(" OR ") || bggmDescQuery;
      if(item==="bggm"){
        this.clearFacetItemsFormGroup("bugm");
        const bugmList = await this._searchOfferRequestService.fetchBUGMIds(bggmDescQuery);
        this.populateFacetItem(bugmList,"bugm");
      }else if(item==="bugm"){
        bugmDescQuery = bugmDescIds?.join(" OR ") || bugmDescQuery;
        const categoriesList = await this._searchOfferRequestService.fetchCategoriesIds(bggmDescQuery ,bugmDescQuery,true);
        this.populateFacetItem(categoriesList,"categoryId");
      }
     }

      this.commonSearchService.isFiltered = false;
      this.postDataForInputSearch(event);
     
  }
  postDataForInputSearch(event){
    const programCode = this.baseInputSearchService.getActiveCurrentSearchType();
    this.commonSearchService.populateFilterOption(programCode,event);
    this.baseInputSearchService.populateChipList();
    let isFeatureFlagEnabled = false;
    let isExpiredStatusForBPD = false;
    if(event.item === 'status')
    {
      isFeatureFlagEnabled = this._featureFlagService.isOfferRequestArchivalEnabled;
      isExpiredStatusForBPD = event?.form?.status?.filter((el)=>{ return el.value === 'Expired'}).length > 0 ;
      this.commonSearchService.isShowExpiredInQuery = isExpiredStatusForBPD;
    }
    else if(event.item === 'offerStatus')
    {
      isFeatureFlagEnabled = this._featureFlagService.isOfferArchivalEnabled;
      isExpiredStatusForBPD = event?.form?.offerStatus?.filter((el)=>{ return el.value === 'Expired'}).length > 0;
      this.commonSearchService.isShowExpiredInQuery_O = isExpiredStatusForBPD;
    }
    if(isFeatureFlagEnabled && isExpiredStatusForBPD )
    {
      const isStatusColSelected = (event.item === 'status' || event.item === 'offerStatus');
      this.baseInputSearchService.postDataForInputSearch(true,isFeatureFlagEnabled,isStatusColSelected,isExpiredStatusForBPD);
    }
    else{
      this.baseInputSearchService.postDataForInputSearch(true);
    }
  }
  populateFacetItem(list,item){
    if(list){
      this.renderFacetItem(Object.keys(list).map(ele=>{return {id:ele,value:list[ele],selected:false}}),item);
    }
  }
  setFilterOptions(){
    this.commonSearchService.setAllFilterOptions( {key:CONSTANTS.BPD,currentRouter: this.baseInputSearchService.currentRouter});
    this.commonSearchService.setInputSearchOption(CONSTANTS.BPD,this.commonSearchService.inputSearchOption?.[CONSTANTS.BPD]);
    this.commonSearchService.setFilterOption(CONSTANTS.BPD,this.commonSearchService.filterOption?.[CONSTANTS.BPD]);
    this.commonSearchService.setDefaultOption(CONSTANTS.BPD,this.commonSearchService.defaultOption?.[CONSTANTS.BPD]);
    this.baseInputSearchService.setActiveCurrentSearchType(CONSTANTS.BPD);
  }
  // when we click on close icon on buggm, bggm chip, we need to reset bugm and categories
  async clearBggmBugmOnChipClick(chip) {
    this.clearFacetItemsFormGroup("categoryId");
    if(chip === "bggm") {
      this.clearFacetItemsFormGroup("bugm");
      const bugmList = await this._searchOfferRequestService.fetchBUGMIds('*');
      this.populateFacetItem(bugmList,"bugm");
    }
  }

  setDisplayExpiredStatusVal({form, item}){
   //If status is expired, set flags
    if(item !=='status' || this.commonSearchService.currentRouter !== 'request'){
      return false;
    }

    let isHideExpiredStatusReqs = true;

    form.status.map(ele=>{
      if(typeof ele === 'object' && ele.value === CONSTANTS.EXPIRED_STATUS_OR_DISPLAY){
        isHideExpiredStatusReqs = false;
      }
    })

    this.commonSearchService.setIsHideExpiredStatusReqs(isHideExpiredStatusReqs);
    this.commonSearchService.setQueryOptionsForBpd({isHideExpiredStatusReqs});

  }

  onFacetClick({form,event,item}) {
    const bpdSelected = form?.programCode?.filter(ele=>ele?.selected && ele?.id==="BPD");
    if(bpdSelected?.length<=0){
      this.baseInputSearchService.setActiveCurrentSearchType(null);
    }
    if(this.facetPage ==="template"){
      this.getFacetForTemplate({form,item});
    }else{
      if(this.facetPage ==='home' && bpdSelected?.length){
        if(item==='programCode'){  
          this.setFilterOptions();
          this.facetListClick.emit(form);
        }else{ 
          this.setDisplayExpiredStatusVal({form, item});        
         
          this.getFacetForTemplate({form,item});
        }
        
      }else{
        if(this.isDivisionalGamesEnabled && this.facetPage === "home" && item == "subProgramCode") {
          this.populatePrgmTypeBasedOnSubPrgmCd({form, item});
        }
        this.facetListClick.emit(form);
      }
      
    }
    
  }
  async populatePrgmTypeBasedOnSubPrgmCd(formObj) {
    const { item, form: { subProgramCode } } = formObj,
      removeSpace = (ele) => `${ele?.trim().replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;

    if (["subProgramCode"].includes(item) && formObj) {
      const selectedSubProgramCodes = subProgramCode?.filter(ele => ele).map(ele => removeSpace(ele.id));
      this.clearFacetItemsFormGroup("programType");
      formObj.form["programType"] = [];
      this._searchOfferRequestService.resetProgramTypeGr$.next(true);
        const appData = this.init.getAppData();
        let searchedPType = null;
        if (selectedSubProgramCodes?.length && selectedSubProgramCodes?.length === 1) {
          searchedPType = selectedSubProgramCodes.includes("Base") ? 'GR_ODC' : 'DG_ODC'
        } else if (selectedSubProgramCodes?.length && selectedSubProgramCodes?.length > 1) {
          searchedPType = "(GR_ODC OR DG_ODC)";
        }
        let prgrmTypeList = searchedPType ? await this._searchOfferRequestService.fetchProgramDetails(appData,searchedPType) : {};
        prgrmTypeList && this.populateFacetItem(prgrmTypeList, "programType");
        appData.offerDetailsProgramTypesRequestGR = prgrmTypeList;
    }
  }
  ngOnDestroy() {
    this.homeFilterSearchSourceSearch?.unsubscribe();
  }
  get isDivisionalGamesEnabled() {
    return this._featureFlagService.isFeatureFlagEnabled("isDivisionalGamesFeatureEnabled");
  }
}

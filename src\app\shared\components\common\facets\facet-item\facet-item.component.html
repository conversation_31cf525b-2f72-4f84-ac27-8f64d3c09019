<div
  data-toggle="collapse"
  class="facets-title cursor-pointer {{ facetItemServic.programCodeSelected }}"
  [ngClass]="{ collapsed: !accordinBasedOnItem, hide: doHide() }"
  [attr.data-target]="'#' + targetItem"
  aria-expanded="false"
>
  <!-- {{item}} -->
  {{ facetFilter[item].displayValue }}
</div>

<div class="collapse mb-4" [ngClass]="{ show: accordinBasedOnItem === true }" [id]="targetItem">
    <div class="mt-3" *ngIf="searchEventAndSubType(item)">
        <input class="form-control search-background" (input)="searchItem($event)" value="{{ facetItemServic.searchText }}" />
    </div>
    <div class="mt-3" *ngIf="showSelectAllLabel()">
        <a href="javascript:void(0)" (click)="onToggleSelectAll()" class="btn-link mr-4 mt-1">{{ selectAllText  }}</a>
    </div>
    <div class="mt-3" *ngIf="showSelectUnSelectLabel()" [ngClass]="{ hide: doHide() }">
        <a href="javascript:void(0)" (click)="onToggleSelectUnSelectAll()" class="btn-link mr-4 mt-1">{{ selectAllTextToDisplay }}</a>
    </div>
    <div [class]="setFacetWraaperClassBasedOnItem(item)">
        <div class="showErrorText" *ngIf="showProgramCdSelectionError(item)"><span>Must select 1+ box</span></div>
        <ng-container *ngFor="let items of facetList.controls; index as i">
            <div 
                 role="listitem"
                 class="facet-items cursor-pointer d-flex justify-content-between"
                 [formGroup]="form"
                 id="deals-Less"
                 *ngIf="facetItem[i] && hideFacetItems(item, facetItem[i].value, i)"
                 >
                <label
                       *ngIf="!features.includes(item)"
                       class="squaredThree row"
                       [ngClass]="['programCode', 'offerProgramCd'].includes(item) ? secureProgramCodeByUserPermissions(facetItem[i]) : ''"
                       >
                    <input
                           *ngIf="item !== 'programCode'"
                           id="custom-check-{{ getFacetVal({val:facetItem[i].value,item:item})  | extraChar }}"
                           [formControl]="items"
                           class="item-check light-checkbox"
                           type="checkbox"
                           [class]="item === 'color' ? 'row-color-' + facetItem[i].color : ''"
                           [ngClass]="
              (initialSelectedStatesList.includes(facetItem[i].value) ? 'partiallyFilledCB' : '') +
              '' +
              (item === 'offerProgramCd' ? secureProgramCodeByUserPermissions(facetItem[i]) : '')
            "
                           name="{{ item }}"
                           (click)="
              onClick({ target: $event, facet: items, facetItem: facetItem[i], selected: i, elemId: 'custom-check-' + removeSpecialChars(facetItem[i].value) })
            "
                           (change)="
              onChange({ target: $event, facet: items, facetItem: facetItem[i], selected: i, elemId: 'custom-check-' + removeSpecialChars(facetItem[i].value) })
            " 
                           />

                    <input 
                           *ngIf="item === 'programCode'"
                           id="custom-check-{{ facetItem[i].value | extraChar }}"
                           [formControl]="items"
                           class="item-check light-checkbox"
                           type="{{ item === 'programCode' ? 'radio' : 'checkbox' }}"
                           [class]="item === 'color' ? 'row-color-' + facetItem[i].color : ''"
                           name="{{ item === 'programCode' ? item : '' }}"
                           [ngClass]="{ partiallyFilledCB: initialSelectedStatesList.includes(facetItem[i].value) ? true : false }"
                           [ngClass]="item === 'programCode' ? secureProgramCodeByUserPermissions(facetItem[i]) : null"
                           [checked]="item === 'programCode' && facetItem[i].selected"
                           (change)="onChange({ target: $event, facet: items, facetItem: facetItem[i], selected: i, elemId: 'custom-check-' + removeSpecialChars(facetItem[i].value) })"
                           />
                    <span id="facet-item-{{facetItem[i].value | extraChar}}" class="ml-1 col p-0">{{ facetItem[i].value }}</span>

                    <span class="angle-rotate cursor-pointer float-right mt-1"></span>
                </label>
                <div
                     data-toggle="collapse"
                     [attr.data-target]="
            item === 'divisions' && (facetpage == 'storeGroup' || facetpage == 'offerHome') ? '#' + this.stringHelper.escapeValue(removeSpecialChars(facetItem[i].value)) : ''
          "
                     [ngClass]="{
            collapsed: true,
            'facets-division-title': item === 'divisions' && (facetpage == 'storeGroup' || facetpage == 'offerHome')
          }"
                     aria-expanded="false"
                     class="mt-1 p-0"
                     [id]="'collapse-' + facetItem[i].value | extraChar"
                     (click)="setExpandedItems('collapse-' + removeSpecialChars(facetItem[i].value))"
                     ></div>
                <div *ngIf="features.includes(item)" class="f-width switch-toggle switch-3 switch-candy" id="deals-Less">
                    <input 
                           class="l-outline light-checkbox"
                           [formControl]="items"
                           type="radio"
                           id="no{{ facetItem[i].value | extraChar }}"
                           name="{{ facetItem[i].value }}"
                           [checked]="facetItem[i].selected === 'no'"
                           (change)="onChange({ target: $event, facet: items, facetItem: facetItem[i], selected: 'no' })"
                           />
                    <label class="first-label l-outline pt-4 pr-4 pb-4 pl-2" for="no{{ facetItem[i].value }}"
                           >No({{ facetItem[i].count ? totalStores - facetItem[i].count : 0 }})<span 
                            class="rcorners left"
                            *ngIf="!(facetItem[i].selected === 'no')"
                       ></span
                    ></label>

                    <input
                           class="l-outline"
                           [formControl]="items"
                           type="radio"
                           id="all{{ facetItem[i].value | extraChar }}"
                           name="{{ facetItem[i].value }}"
                           [checked]="facetItem[i].selected === 'all'"
                           (change)="onChange({ target: $event, facet: items, facetItem: facetItem[i], selected: 'all' })" 
                           />
                    <label class="l-outline pt-4 pr-4 pb-4 pl-3" for="all{{ facetItem[i].value }}"
                        >All({{ totalStores }})<span *ngIf="!(facetItem[i].selected === 'all')" class="rcorners"></span
                    ></label>

                    <input 
                           class="l-outline"
                           [formControl]="items"
                           type="radio"
                           id="yes{{ facetItem[i].value | extraChar }}"
                           name="{{ facetItem[i].value }}"
                           [checked]="facetItem[i].selected === 'yes'"
                           (change)="onChange({ target: $event, facet: items, facetItem: facetItem[i], selected: 'yes' })"
                           />
                    <label class="last-label l-outline pt-4 pr-4 pb-4 pl-3" for="yes{{ facetItem[i].value }}"
                            >Yes({{ facetItem[i].count ? facetItem[i].count : 0 }})<span
                            class="rcorners right"
                            *ngIf="!(facetItem[i].selected === 'yes')"
                             ></span
                             ></label>

                    <a class="l-outline"></a>
                </div>
            </div>
            <div
                 *ngIf="item == 'divisions' && (facetpage == 'storeGroup' || facetpage == 'offerHome')"
                 class="collapse mt-3 ml-7"
                 [id]="facetItem[i].value | extraChar"
                 >
                <ng-container *ngIf="divisionStatesList && divisionStatesList['controls'][facetItem[i].value]">
                    <div 
                         [formGroup]="divisionStatesList"
                         *ngFor="let states of divisionStatesList['controls'][facetItem[i].value].controls; index as j"
                         >
                        <label *ngIf="!features.includes(item)" class="squaredThree">
                            <input
                                   id="state-{{ facetItem[i].value  | extraChar }}{{ j }}"
                                   [formControl]="states"
                                   class="item-check light-checkbox"
                                   type="checkbox"
                                   (change)="
                  onChange({
                    target: $event,
                    facet: items,
                    facetItem: facetItem[i],
                    selected: j,
                    statesObj: {
                      stateControl: states,
                      stateData: divisionState[facetItem[i].value][j],
                      elemId: 'custom-check-' + removeSpecialChars(facetItem[i].value)
                    }
                  })
                " 
                                   />
                            {{ divisionState[facetItem[i].value][j].value }}
                            <div>
                                <span class="angle-rotate cursor-pointer float-right mt-1"></span>
                            </div>
                        </label>
                    </div>
                </ng-container>
            </div>
        </ng-container>
    </div>
</div>

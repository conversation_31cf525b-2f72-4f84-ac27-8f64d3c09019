@import "../../../../../../scss/colors";
@import "../../../../../../scss/variables";

@import "../../../../../../scss/inputs";
.f-width {
  font-size: 13px;
  margin: 0 auto;
  width: 90%;
}
.facets-title[data-target='#status'].SC+div .facet-items:nth-child(2) {
  border-bottom: 1px solid #cccc;
  padding-bottom: 16px;
}
.facets-title {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIgZD0iTTUgMTVsNy03IDcgNyIvPjwvc3ltYm9sPg0KICAgICAgICA8L2RlZnM+DQogICAgICAgIDxnPg0KICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjYXNzZXQiLz4NCiAgICAgICAgPC9nPg0KICAgIDwvc3ZnPg==) no-repeat right
    center;
    background-size: 24px;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
  font-size: $base-font-size;
  font-weight: bold;
  line-height: 16px;
  padding: 10px 0;
  text-transform: capitalize;
  &.collapsed {
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIgZD0iTTE5IDlsLTcgNy03LTciLz48L3N5bWJvbD4NCiAgICAgICAgPC9kZWZzPg0KICAgICAgICA8Zz4NCiAgICAgICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI2Fzc2V0Ii8+DQogICAgICAgIDwvZz4NCiAgICA8L3N2Zz4=) no-repeat
      right center;
      background-size: 24px;
  }
}
.showErrorText {
  width: 100%;
  text-align: center;
  border: 1px solid #DD1E25;
  border-radius: 0.3rem;
  font-size: 14px;
  font-weight: 400;
}
.facets-division-title {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5hcnJvdy1pY29uLXVwPC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIxMy4zNSA2LjI2IDYuMjUgMCAwIDYuMjkgMS4xOCA3LjQ3IDYuMzMgMi4yOSAxMi4yNSA3LjUxIDEzLjM1IDYuMjYgMTMuMzUgNi4yNiAxMy4zNSA2LjI2Ii8+PC9nPjwvZz48L3N2Zz4=) no-repeat right center;
  text-transform: capitalize;
  width: 10%;
  height: 15px;
  &.collapsed {
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM4NThjOTI7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+) no-repeat right center;
  }
}
.disabled {
  pointer-events: none;
}
.last-label {
  left: 12px;
}
.first-label {
  right: 23px;
}
.rcorners {
  background-color: #dfdddd;
  display: inline-block;
  border-radius: 100%;
  width: 14px;
  height: 14px;
  padding-left: 10px;
  box-sizing: border-box;
  position: absolute;
  top: 8px;
  left: 35px;
  &.left {
    left: 24px;
  }
  &.right {
    left: 47px;
  }
}

.facet-items .squaredThree input:checked ~ .checkmark {
  background-color: $blue-primary-hex;
  border: 0;
  &:after {
    content: "";
    border: solid white;
    border-width: 0 2px 2px 0;
    display: inline-block;
    position: absolute;
    left: 7px;
    right: 9px;
    top: 2px;
    width: 6px;
    height: 14px;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
}
.facet-items-list{
  max-height: 545px;
  overflow-y: auto;
}
.search-background{
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4NCjxzdmcgd2lkdGg9IjI1cHgiIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI1IDI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICAgIDwhLS0gR2VuZXJhdG9yOiBza2V0Y2h0b29sIDUyLjUgKDY3NDY5KSAtIGh0dHA6Ly93d3cuYm9oZW1pYW5jb2RpbmcuY29tL3NrZXRjaCAtLT4NCiAgICA8dGl0bGU+NTRFRkEwRUUtMTA4Ri00QzRGLThGMjUtQzgzRjA2RDA4NUUzPC90aXRsZT4NCiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggc2tldGNodG9vbC48L2Rlc2M+DQogICAgPGcgaWQ9IkFzc2V0LUFydGJvYXJkLVBhZ2UiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPg0KICAgICAgICA8ZyBpZD0iSWNvbnMtLy1Vbml2ZXJzYWwtLy1DYXJldC1Eb3duLS8tR3JleSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMC44OTQ1NDIsIDAuMDAwMDAwKSIgZmlsbD0iIzRCNEI0QiI+DQogICAgICAgICAgICA8cGF0aCBkPSJNMTYuNzkwMDQxOSwxNS4zNzU4MjgzIEwyMC42NDM3MjgxLDE5LjIyOTUxNDUgTDE5LjIyOTUxNDUsMjAuNjQzNzI4MSBMMTUuMzc1ODI4MywxNi43OTAwNDE5IEMxNC4xNzE2OTI0LDE3LjY5OTA1OTggMTIuNjcyNjA1LDE4LjIzODA5NTIgMTEuMDQ3NjE5LDE4LjIzODA5NTIgQzcuMDc2NDI4NywxOC4yMzgwOTUyIDMuODU3MTQyODYsMTUuMDE4ODA5NCAzLjg1NzE0Mjg2LDExLjA0NzYxOSBDMy44NTcxNDI4Niw3LjA3NjQyODcgNy4wNzY0Mjg3LDMuODU3MTQyODYgMTEuMDQ3NjE5LDMuODU3MTQyODYgQzE1LjAxODgwOTQsMy44NTcxNDI4NiAxOC4yMzgwOTUyLDcuMDc2NDI4NyAxOC4yMzgwOTUyLDExLjA0NzYxOSBDMTguMjM4MDk1MiwxMi42NzI2MDUgMTcuNjk5MDU5OCwxNC4xNzE2OTI0IDE2Ljc5MDA0MTksMTUuMzc1ODI4MyBaIE0xMS4wNDc2MTksMTYuMjM4MDk1MiBDMTMuOTE0MjM5OSwxNi4yMzgwOTUyIDE2LjIzODA5NTIsMTMuOTE0MjM5OSAxNi4yMzgwOTUyLDExLjA0NzYxOSBDMTYuMjM4MDk1Miw4LjE4MDk5ODIgMTMuOTE0MjM5OSw1Ljg1NzE0Mjg2IDExLjA0NzYxOSw1Ljg1NzE0Mjg2IEM4LjE4MDk5ODIsNS44NTcxNDI4NiA1Ljg1NzE0Mjg2LDguMTgwOTk4MiA1Ljg1NzE0Mjg2LDExLjA0NzYxOSBDNS44NTcxNDI4NiwxMy45MTQyMzk5IDguMTgwOTk4MiwxNi4yMzgwOTUyIDExLjA0NzYxOSwxNi4yMzgwOTUyIFoiIGlkPSJHcmV5U2VhcmNoIj48L3BhdGg+DQogICAgICAgIDwvZz4NCiAgICA8L2c+DQo8L3N2Zz4=) no-repeat right 10px center;
}

.facet-items {
  font-size: $base-font-size;
  display: flex;
  align-items: center;
  label {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 12px 0 0 0px;
    line-height: 16px;
    font-size: $base-font-size;
    position: relative;
    word-break: break-word;
    #custom-check:checked ~ .span.checkmark {
      background: red;
    }

    .checkmark {
      border: 1px solid #ccc;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      display: inline-block;
      position: absolute;
      left: 0;
      height: 20px;
      width: 20px;
      &:hover {
        background: #fff;
      }
    }
  }
}
.l-outline {
  outline: none;
}

.switch-toggle {
  &:before {
    content: "";
    background: #eee;
    border-radius: 10px;
    display: block;
    height: 10px;
    position: relative;
    top: 20px;
  }
  &.switch-candy {
    a {
      background: none;
      border: none;
      box-shadow: none;
      text-align: center;
      &:after {
        content: "";
        background: #00509d;
        border-radius: 20px;
        display: inline-block;
        height: 16px;
        position: relative;
        top: 17px;
        width: 16px;
      }
    }
  }
}

.switch-toggle a,
.switch-light span span {
  display: none;
}

/* We can't test for a specific feature,
 * so we only target browsers with support for media queries.
 */
@media only screen {
  /* Checkbox
 */
  .switch-light {
    position: relative;
    display: block;
    /* simulate default browser focus outlines on the switch,
   * when the inputs are focused.
   */
  }
  .switch-light::after {
    clear: both;
    content: "";
    display: table;
  }
  .switch-light *,
  .switch-light *:before,
  .switch-light *:after {
    box-sizing: border-box;
  }
  .switch-light a {
    display: block;
    transition: all 0.2s ease-out;
  }
  .switch-light label,
  .switch-light > span {
    /* breathing room for bootstrap/foundation classes.
     */
    line-height: 2em;
  }
  .switch-light input:focus ~ span a,
  .switch-light input:focus + label {
    outline-width: 2px;
    outline-style: solid;
    outline-color: Highlight;
    /* Chrome/Opera gets its native focus styles.
     */
  }
}
@media only screen and (-webkit-min-device-pixel-ratio: 0) {
  .switch-light input:focus ~ span a,
  .switch-light input:focus + label {
    outline-color: -webkit-focus-ring-color;
    outline-style: auto;
  }
}

@media only screen {
  /* don't hide the input from screen-readers and keyboard access
 */
  .switch-light input {
    position: absolute;
    opacity: 0;
    z-index: 3;
  }
  .switch-light input:checked ~ span a {
    right: 0%;
  }
  /* inherit from label
 */
  .switch-light strong {
    font-weight: inherit;
  }
  .switch-light > span {
    position: relative;
    overflow: hidden;
    display: block;
    min-height: 2em;
    /* overwrite 3rd party classes padding
   * eg. bootstrap .alert
   */
    padding: 0;
    text-align: left;
  }
  .switch-light span span {
    position: relative;
    z-index: 2;
    display: block;
    float: left;
    width: 50%;
    text-align: center;
    user-select: none;
  }
  .switch-light a {
    position: absolute;
    right: 50%;
    top: 0;
    z-index: 1;
    display: block;
    width: 50%;
    height: 100%;
    padding: 0;
  }
  /* bootstrap 4 tweaks
*/
  .switch-light.row {
    display: flex;
  }
  .switch-light .alert-light {
    color: #333;
  }
  /* Radio Switch
 */
  .switch-toggle {
    position: relative;
    display: block;
    /* simulate default browser focus outlines on the switch,
   * when the inputs are focused.
   */
    /* For callout panels in foundation
  */
    padding: 0 !important;
    /* 2 items
   */
    /* 3 items
   */
    /* 4 items
   */
    /* 5 items
   */
    /* 6 items
   */
  }
  .switch-toggle::after {
    clear: both;
    content: "";
    display: table;
  }
  .switch-toggle *,
  .switch-toggle *:before,
  .switch-toggle *:after {
    box-sizing: border-box;
  }
  .switch-toggle a {
    display: block;
    transition: all 0.2s ease-out;
  }
  .switch-toggle label,
  .switch-toggle > span {
    /* breathing room for bootstrap/foundation classes.
     */
    line-height: 2em;
  }
  .switch-toggle input:focus ~ span a,
  .switch-toggle input:focus + label {
    outline-width: 2px;
    outline-style: solid;
    outline-color: Highlight;
    /* Chrome/Opera gets its native focus styles.
     */
  }
}
@media only screen and (-webkit-min-device-pixel-ratio: 0) {
  .switch-toggle input:focus ~ span a,
  .switch-toggle input:focus + label {
    outline-color: -webkit-focus-ring-color;
    outline-style: auto;
  }
}

@media only screen {
  .switch-toggle input {
    position: absolute;
    left: 0;
    opacity: 0;
  }
  .switch-toggle input + label {
    position: relative;
    z-index: 2;
    display: block;
    float: left;
    padding: 0 0.5em;
    margin: 0;
    text-align: center;
  }
  .switch-toggle a {
    position: absolute;
    top: 0;
    left: -13%;
    padding: 0;
    z-index: 1;
    width: 10px;
    height: 100%;
  }
  .switch-toggle label:nth-child(2):nth-last-child(4),
  .switch-toggle label:nth-child(2):nth-last-child(4) ~ label,
  .switch-toggle label:nth-child(2):nth-last-child(4) ~ a {
    width: 50%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(4)
    ~ input:checked:nth-child(3)
    + label
    ~ a {
    left: 50%;
  }
  .switch-toggle label:nth-child(2):nth-last-child(6),
  .switch-toggle label:nth-child(2):nth-last-child(6) ~ label,
  .switch-toggle label:nth-child(2):nth-last-child(6) ~ a {
    width: 33.33%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(6)
    ~ input:checked:nth-child(3)
    + label
    ~ a {
    left: 33.33%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(6)
    ~ input:checked:nth-child(5)
    + label
    ~ a {
    left: 84%;
  }
  .switch-toggle label:nth-child(2):nth-last-child(8),
  .switch-toggle label:nth-child(2):nth-last-child(8) ~ label,
  .switch-toggle label:nth-child(2):nth-last-child(8) ~ a {
    width: 25%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(8)
    ~ input:checked:nth-child(3)
    + label
    ~ a {
    left: 25%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(8)
    ~ input:checked:nth-child(5)
    + label
    ~ a {
    left: 50%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(8)
    ~ input:checked:nth-child(7)
    + label
    ~ a {
    left: 75%;
  }
  .switch-toggle label:nth-child(2):nth-last-child(10),
  .switch-toggle label:nth-child(2):nth-last-child(10) ~ label,
  .switch-toggle label:nth-child(2):nth-last-child(10) ~ a {
    width: 20%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(10)
    ~ input:checked:nth-child(3)
    + label
    ~ a {
    left: 20%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(10)
    ~ input:checked:nth-child(5)
    + label
    ~ a {
    left: 40%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(10)
    ~ input:checked:nth-child(7)
    + label
    ~ a {
    left: 60%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(10)
    ~ input:checked:nth-child(9)
    + label
    ~ a {
    left: 80%;
  }
  .switch-toggle label:nth-child(2):nth-last-child(12),
  .switch-toggle label:nth-child(2):nth-last-child(12) ~ label,
  .switch-toggle label:nth-child(2):nth-last-child(12) ~ a {
    width: 16.6%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(12)
    ~ input:checked:nth-child(3)
    + label
    ~ a {
    left: 16.6%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(12)
    ~ input:checked:nth-child(5)
    + label
    ~ a {
    left: 33.2%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(12)
    ~ input:checked:nth-child(7)
    + label
    ~ a {
    left: 49.8%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(12)
    ~ input:checked:nth-child(9)
    + label
    ~ a {
    left: 66.4%;
  }
  .switch-toggle
    label:nth-child(2):nth-last-child(12)
    ~ input:checked:nth-child(11)
    + label
    ~ a {
    left: 83%;
  }
  /* Candy Theme
 * Based on the "Sort Switches / Toggles (PSD)" by Ormal Clarck
 * http://www.premiumpixels.com/freebies/sort-switches-toggles-psd/
 */
  .switch-toggle.switch-candy,
  .switch-light.switch-candy > span {
    background-color: transparent;
    // border-radius: 3px;
    // box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.3), 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  .switch-light.switch-candy span span,
  .switch-light.switch-candy input:checked ~ span span:first-child,
  .switch-toggle.switch-candy label {
    color: #000;
    text-align: center;
  }
  .switch-light.switch-candy input ~ span span:first-child,
  .switch-light.switch-candy input:checked ~ span span:nth-child(2),
  .switch-candy input:checked + label {
    color: #333;
    font-weight: bold;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    outline: none;
  }
  .switch-candy a {
    border: 1px solid #333;
    border-radius: 3px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2),
      inset 0 1px 1px rgba(255, 255, 255, 0.45);
    background-color: #70c66b;
    background-image: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
  }
  .switch-candy-blue a {
    background-color: #38a3d4;
  }
  .switch-candy-yellow a {
    background-color: #f5e560;
  }
  /* iOS Theme
*/
  .switch-ios.switch-light span span {
    color: #888b92;
  }
  .switch-ios.switch-light a {
    left: 0;
    top: 0;
    width: 2em;
    height: 2em;
    background-color: #fff;
    border-radius: 100%;
    border: 0.25em solid #d8d9db;
    transition: all 0.2s ease-out;
  }
  .switch-ios.switch-light > span {
    display: block;
    width: 100%;
    height: 2em;
    background-color: #d8d9db;
    border-radius: 1.75em;
    transition: all 0.4s ease-out;
  }
  .switch-ios.switch-light > span span {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    line-height: 1.875em;
    vertical-align: middle;
    transition: all 0.2s ease-out;
  }
  .switch-ios.switch-light > span span:first-of-type {
    opacity: 1;
    padding-left: 1.875em;
  }
  .switch-ios.switch-light > span span:last-of-type {
    padding-right: 1.875em;
  }
  .switch-ios.switch-light input:checked ~ span a {
    left: 100%;
    border-color: #4bd865;
    margin-left: -2em;
  }
  .switch-ios.switch-light input:checked ~ span {
    border-color: #4bd865;
    box-shadow: inset 0 0 0 30px #4bd865;
  }
  .switch-ios.switch-light input:checked ~ span span:first-of-type {
    opacity: 0;
  }
  .switch-ios.switch-light input:checked ~ span span:last-of-type {
    opacity: 1;
    color: #fff;
  }
  .switch-ios.switch-toggle {
    background-color: #d8d9db;
    border-radius: 30px;
    box-shadow: inset rgba(0, 0, 0, 0.1) 0 1px 0;
  }
  .switch-ios.switch-toggle a {
    background-color: #4bd865;
    border: 0.125em solid #d8d9db;
    border-radius: 1.75em;
    transition: all 0.12s ease-out;
  }
  .switch-ios.switch-toggle label {
    height: 2.4em;
    color: #888b92;
    line-height: 2.4em;
    vertical-align: middle;
  }
  .switch-ios input:checked + label {
    color: #3e4043;
  }
  /* Holo Theme
 */
  .switch-toggle.switch-holo,
  .switch-light.switch-holo > span {
    background-color: #464747;
    border-radius: 1px;
    box-shadow: inset rgba(0, 0, 0, 0.1) 0 1px 0;
    color: #fff;
    text-transform: uppercase;
  }
  .switch-holo label {
    color: #fff;
  }
  .switch-holo > span span {
    opacity: 0;
    transition: all 0.1s;
  }
  .switch-holo > span span:first-of-type {
    opacity: 1;
  }
  .switch-holo > span span,
  .switch-holo label {
    font-size: 85%;
    line-height: 2.15625em;
  }
  .switch-holo a {
    background-color: #666;
    border-radius: 1px;
    box-shadow: inset rgba(255, 255, 255, 0.2) 0 1px 0,
      inset rgba(0, 0, 0, 0.3) 0 -1px 0;
  }
  /* Selected ON switch-light
*/
  .switch-holo.switch-light input:checked ~ span a {
    background-color: #0e88b1;
  }
  .switch-holo.switch-light input:checked ~ span span:first-of-type {
    opacity: 0;
  }
  .switch-holo.switch-light input:checked ~ span span:last-of-type {
    opacity: 1;
  }
  /* Material Theme
 */
  /* switch-light
 */
  .switch-light.switch-material a {
    top: -0.1875em;
    width: 1.75em;
    height: 1.75em;
    border-radius: 50%;
    background: #fafafa;
    box-shadow: 0 0.125em 0.125em 0 rgba(0, 0, 0, 0.14),
      0 0.1875em 0.125em -0.125em rgba(0, 0, 0, 0.2),
      0 0.125em 0.25em 0 rgba(0, 0, 0, 0.12);
    transition: right 0.28s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .switch-material.switch-light {
    overflow: visible;
  }
  .switch-material.switch-light::after {
    clear: both;
    content: "";
    display: table;
  }
  .switch-material.switch-light > span {
    overflow: visible;
    position: relative;
    top: 0.1875em;
    width: 3.25em;
    height: 1.5em;
    min-height: auto;
    border-radius: 1em;
    background: rgba(0, 0, 0, 0.26);
  }
  .switch-material.switch-light span span {
    position: absolute;
    clip: rect(0 0 0 0);
  }
  .switch-material.switch-light input:checked ~ span a {
    right: 0;
    background: #3f51b5;
    box-shadow: 0 0.1875em 0.25em 0 rgba(0, 0, 0, 0.14),
      0 0.1875em 0.1875em -0.125em rgba(0, 0, 0, 0.2),
      0 0.0625em 0.375em 0 rgba(0, 0, 0, 0.12);
  }
  .switch-material.switch-light input:checked ~ span {
    background: rgba(63, 81, 181, 0.5);
  }
  /* switch-toggle
 */
  .switch-toggle.switch-material {
    overflow: visible;
  }
  .switch-toggle.switch-material::after {
    clear: both;
    content: "";
    display: table;
  }
  .switch-toggle.switch-material a {
    top: 48%;
    width: 0.375em !important;
    height: 0.375em;
    margin-left: 0.25em;
    background: #3f51b5;
    border-radius: 100%;
    transform: translateY(-50%);
    transition: transform 0.4s ease-in;
  }
  .switch-toggle.switch-material label {
    color: rgba(0, 0, 0, 0.54);
    font-size: 1em;
  }
  .switch-toggle.switch-material label:before {
    content: "";
    position: absolute;
    top: 48%;
    left: 0;
    display: block;
    width: 0.875em;
    height: 0.875em;
    border-radius: 100%;
    border: 0.125em solid rgba(0, 0, 0, 0.54);
    transform: translateY(-50%);
  }
  .switch-toggle.switch-material input:checked + label:before {
    border-color: #3f51b5;
  }
  /* ripple
 */
  .switch-light.switch-material > span:before,
  .switch-light.switch-material > span:after,
  .switch-toggle.switch-material label:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
    display: block;
    width: 4em;
    height: 4em;
    border-radius: 100%;
    background: #3f51b5;
    opacity: 0.4;
    margin-left: -1.25em;
    margin-top: -1.25em;
    transform: scale(0);
    transition: opacity 0.4s ease-in;
  }
  .switch-light.switch-material > span:after {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: -1.25em;
  }
  .switch-toggle.switch-material label:after {
    width: 3.25em;
    height: 3.25em;
    margin-top: -0.75em;
  }
  @keyframes materialRipple {
    0% {
      transform: scale(0);
    }
    20% {
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(1);
    }
  }
  .switch-material.switch-light input:not(:checked) ~ span:after,
  .switch-material.switch-light input:checked ~ span:before,
  .switch-toggle.switch-material input:checked + label:after {
    animation: materialRipple 0.4s ease-in;
  }
  /* trick to prevent the default checked ripple animation from showing
 * when the page loads.
 * the ripples are hidden by default, and shown only when the input is focused.
 */
  .switch-light.switch-material.switch-light input ~ span:before,
  .switch-light.switch-material.switch-light input ~ span:after,
  .switch-material.switch-toggle input + label:after {
    visibility: hidden;
  }
  .switch-light.switch-material.switch-light input:focus:checked ~ span:before,
  .switch-light.switch-material.switch-light
    input:focus:not(:checked)
    ~ span:after,
  .switch-material.switch-toggle input:focus:checked + label:after {
    visibility: visible;
  }
}

.partiallyFilledCB{
  background: #757575;
  &:after{
    content: "\2758";
    transform: rotate(90deg);
    color:#fff;
    margin-top:11px;
    display: block;
  }
}

/* Bugfix for older Webkit, including mobile Webkit. Adapted from
 * http://css-tricks.com/webkit-sibling-bug/
 */
@media only screen and (-webkit-max-device-pixel-ratio: 2) and (max-device-width: 80em) {
  .switch-light,
  .switch-toggle {
    -webkit-animation: webkitSiblingBugfix infinite 1s;
  }
}

@-webkit-keyframes webkitSiblingBugfix {
  from {
    -webkit-transform: translate3d(0, 0, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
  }
}

.isDisabled {
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
  background-color: #757575;
  &:hover{
    background-color: #757575;
  }
}
  /* row color overrides */
  .row-color-orange {
    background-color: #f8f1e9;
 }

 .row-color-green {
     background-color: #e3f7e3;
 }
 .row-color-purple {
     background-color: #eae3f7;
 }
 .row-color-pink  {
     background-color: #fbe7e7;
 }
.row-color-yellow  {
     background-color: #f7f5cc;
 }
 .row-color-blue  {
     background-color: #e3f0f7;
 }
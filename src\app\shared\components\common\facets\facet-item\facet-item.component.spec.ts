import { Element<PERSON><PERSON>, NO_ERRORS_SCHEMA, Renderer2 } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";

import { UntypedFormArray, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { Router } from '@angular/router';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { BehaviorSubject, Observable } from "rxjs";
import { FacetItemComponent } from "./facet-item.component";

import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { PermissionsModule, PermissionsService } from '@appShared/albertsons-angular-authorization';

describe("FacetItemComponent", () => {
  let component: FacetItemComponent;
  let fixture: ComponentFixture<FacetItemComponent>;
  beforeEach(() => {
    const searchOfferRequestServiceStub = () => ({
      populateHomeFilterSearch: (object) => ({}),
      populateStoreFilterSearch: (obj) => ({}),
    });
    const commonRouteServiceStub = () => ({currentActivatedRoute: "request"})

    const queryGeneratorStub = () => ({});
    const authServiceStub = () => ({ getUserPermissions: arg => ({}) });
    const facetItemServiceStub = () => ({
      selectedOfferProgramCode$: { next: () => ({}) },
      setOfferFilter: (string) => ({}),
      populateFacetFieldChips: (value, item) => ({}),
      getdivsionStateFacetItems: () => ({}),
      getEnableFormSource: { subscribe: () => ({}) },
      setisAllBannersSelectedValue: () => ({}),
      getisEndDtRangePast:() => ({}),
      chipComponent: {}
    });
    const routerStub = () => ({ navigateByUrl: editUrl => ({}), url:()=>({}) });
    const storeGroupServiceStub = () => ({
      getFeatureKeys: () => ({}),
      getEnableDisableFacetSource: { subscribe: () => ({}) },
      populateStoreFilterSearch: (object) => ({}),
      setDivisionListControls: () => ({}),
    });
    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({})
    });
    const rendererStub = () => ({});
    const elementRefStub = () => ({});
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [FacetItemComponent],
      imports: [PermissionsModule.forRoot({ permissionsIsolate: true, configurationIsolate: true, rolesIsolate: true })],
      providers: [
        { provide: SearchOfferRequestService, useFactory: searchOfferRequestServiceStub },
        PermissionsService,
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: AuthService, useFactory: authServiceStub},
        { provide: StoreGroupService, useFactory: storeGroupServiceStub },
        { provide: Renderer2, useFactory: rendererStub },
        { provide: ElementRef, useFactory: elementRefStub },
        { provide: Router, useFactory: routerStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub},
        { provide: CommonRouteService, useFactory: commonRouteServiceStub}

      ],
    });
    fixture = TestBed.createComponent(FacetItemComponent);
    component = fixture.componentInstance;
  });

  
  
  it("can load instance", () => {
    expect(component).toBeTruthy();
  });
  
  describe("ngOnInit", () => {
    it("ngOnInit called", () => {
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormControl(""),
      });
      component.item = "divisions";
      component.facetShow = {
        divisions: "divisions",
      };
      component.facetpage = "offerHome";
      component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      //spyOn(facetItemServiceStub, 'getisEndDtRangePast').and.returnValue(of(true))
      facetItemServiceStub.getEnableFormSource$ = new BehaviorSubject(true);
      facetItemServiceStub.chipCloseEvent$ = new BehaviorSubject(false);
      storeGroupServiceStub.getEnableDisableFacetSource = new BehaviorSubject(
        true
      );
      component.ngOnInit();
    });
    describe("hideFacetItems", () => {
      it("if GR program code called", () => {
      
        
        spyOn(component, "checkIfFeatureEnabled").and.returnValue(true);
        const result = component.hideFacetItems("programCode", "Grocery Reward","");
        expect(result).toEqual(true);
      });
      it("if SPD program code called", () => {
       
        
        component.item = "offerStatus";
        spyOn(component, "checkIfFeatureEnabled");
        const result =  component.hideFacetItems("programCode", "Specialty PD","");
        expect(result).toEqual(true);
      });
      it("if default program code called", () => {
        const result = component.hideFacetItems("programCode", "BPD","");
        expect(result).toEqual(true);
      });

      it("if BPD program code called", () => {
        
        spyOn(component, "checkIfFeatureEnabled");
        const result = component.hideFacetItems("programCode", "Base PD","");
        expect(result).toEqual(true);
      });

      it("if MF program code called", () => {
        
        spyOn(component, "checkIfFeatureEnabled");
        const result = component.hideFacetItems("programCode", "Manufacturer Coupon","");
        expect(result).toEqual(true);
      });
      it("if MF program code called in OR home page, then MF should be hidden", () => {
       
        component.facetpage = 'home';
        const result = component.hideFacetItems("programCode", "Manufacturer Coupon","");
        expect(result).toEqual(false);
      });
      it("if Division BPD called", () => {
        const result = component.hideFacetItems("Division", "BPD","");
        expect(result).toEqual(true);
      });

    


      it("if item is programSubType", () => {
        component.item = "progSubType";
        spyOn(component, "hideProgramSubtypesOnSearch");
        component.hideFacetItems("progSubType", "Specialty PD","1");
        expect(component.hideProgramSubtypesOnSearch).toHaveBeenCalled();
      });
    });
    describe('checkIfFeatureEnabled', () => {
      it('Check if feature enabled', ()=> {
        const featureFlagServiceStub: FeatureFlagsService = fixture.debugElement.injector.get(
          FeatureFlagsService
        );
        spyOn(featureFlagServiceStub, "isFeatureFlagEnabled").and.returnValue(true);
        const result = component.checkIfFeatureEnabled("trackSPD")
        expect(result).toEqual(true)
      })
    })
    xit("ngOnInit called", () => {
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormControl(""),
        divisions: new UntypedFormControl(""),
      });
      component.facetShow = {
        divisions: "divisions",
      };
      component.item = "divisions";
      component.facetpage == "storeGroup";
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.getEnableFormSource$ = new BehaviorSubject(true);
      storeGroupServiceStub.getEnableDisableFacetSource = new BehaviorSubject(
        true
      );
      spyOn(component, "checkBannersOrDivisionSelection");
      spyOn(component, "doDivisionListExpandedOnRender");
      spyOn(component, "setDivisionChipsOnPgLoad");
      component.ngOnInit();
      expect(component.checkBannersOrDivisionSelection).toHaveBeenCalled();
      expect(component.doDivisionListExpandedOnRender).toHaveBeenCalled();
      expect(component.setDivisionChipsOnPgLoad).toHaveBeenCalled();
    });
  });
  describe("onChangeFacetHome", () => {
    it("when FH selected", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(
        AuthService
      );
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      let facet = { setValue: () => {} };
      let facetItem = {};
      component.onChangeFacetHome(
        { target: { target:{ checked: true } } ,
        facet,
        facetItem
        });
        permissionService.loadPermissions(['DONT_HAVE_PERMISSION_TO_DO_OFFER_REQUESTS']);
        spyOn(component,'setDivisionSectionValues');
        spyOn(authServiceStub,'getUserPermissions');
        expect(component.setDivisionSectionValues).not.toHaveBeenCalled();
    });
    it("when FH unselected", () => {
      const authServiceStub: AuthService = fixture.debugElement.injector.get(
        AuthService
      );
      const permissionService = fixture.debugElement.injector.get(PermissionsService);
      let facet = { setValue: () => {} };
      let facetItem = {};
      component.item = "divisions";
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormGroup({
        })
      });
      permissionService.loadPermissions(['DONT_HAVE_PERMISSION_TO_DO_OFFER_REQUESTS']);
      spyOn(component,'setDivisionSectionValues');
      spyOn(authServiceStub,'getUserPermissions');
      component.onChangeFacetHome({target: { target:{ checked: false }}, facet, facetItem});
      expect(component.setDivisionSectionValues).toHaveBeenCalled();
    });
  });
  describe("onChangeStoreGroup", () => {
    it("when SG selected", () => {
      spyOn(component, "setFacetsExpanded");
      let facet = { setValue: () => {} };
      let facetItem = {};

      const obj = {
        target: { target: { checked: true } },
        facet,
        facetItem,
        selected: "yes",
      };
      component.onChangeStoreGroup(obj);
    });
    it("when SG unselected", () => {
      spyOn(component, "setFacetsExpanded");
      let facet = { setValue: () => {} };
      let facetItem = {};

      const obj = { target: { target: {} }, facet, facetItem, selected: "yes" };
      component.onChangeStoreGroup(obj);
    });
    it("checkIfAllSubProgramsSelected called", () => {
      let facet = { setValue: () => {} };
      let facetItem = {};
      component.item = "progSubType";
      spyOn(component, "checkIfAllSubProgramsSelected");
      component.onChangeFacetHome( 
        { target: { target:{ checked: true } } ,
        facet,
        facetItem
        })
      expect(component.checkIfAllSubProgramsSelected).toHaveBeenCalled();
    });
  });

  describe("setDigitalNonDigitalDisabled", () => {
    it("make expexted call", () => {
      component.form = new UntypedFormGroup({
        status: new UntypedFormArray([new UntypedFormControl(false), new UntypedFormControl(false), new UntypedFormControl(false)])
      })
      component.facetItem = [{id: 1, value:'digital' ,selected: false}, {id:2, value: "nonDigital", selected: false}]
      component.facetList = new UntypedFormArray([
        new UntypedFormControl(false), new UntypedFormControl(false)
      ]);
      component.item = "status";
      component.setDigitalNonDigitalDisabled();
    });
  });
  describe("showProgramCdSelectionError", () => {
    it("make expexted call", () => {
      component.preventDefault = true;
      const result  = component.showProgramCdSelectionError("offerProgramCd");
      expect(result).toEqual(true);
    });
  });
  describe("setFacetWraaperClassBasedOnItem", () => {
    it("make expexted call if item is offerProgramCd", () => {
      component.preventDefault = true;
      const result  = component.setFacetWraaperClassBasedOnItem("offerProgramCd");
      expect(result).toEqual("mt-2");
    });
    it("make expexted call if item is program sub type", () => {
      spyOn(component, "setScrollBar").and.returnValue(true);
      const result  = component.setFacetWraaperClassBasedOnItem("progSubType");
      expect(result).toEqual("facet-items-list");
    });
    it("make expexted call if case is default", () => {
      const result  = component.setFacetWraaperClassBasedOnItem("programType");
      expect(result).toEqual("");
    });
  });
  describe("resetProgramCodeStatus", () => {
    it("make expexted call if item is offerProgramCd", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.programCodeChecked = {
        "GR": true
      }
      facetItemServiceStub.indexList = [];
      facetItemServiceStub.searchText = ""
      component.preventDefault = true;
      component.resetProgramCodeStatus("programCode");
      expect(facetItemServiceStub.programCodeChecked["GR"]).toEqual(false);
    });
  });
  describe("onClick", () => {
    it("make expexted call if item is offerProgramCd", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.programCodeChecked = {
        "SPD": false
      }
      const obj = {
        target: {
          target: {
            checked: true,
            name: 'offerProgramCd'
          }
        },
        facet: "",
        facetItem: {
          id: "SPD"
        }
      };
      spyOn(component, "programCodePreventDefault").and.returnValue(true);
      const result = component.onClick(obj);
      expect(result).toEqual(false);
    });
  });
  describe("programCodePreventDefault", () => {
    it("make expexted call if item is offerProgramCd SPD false", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.programCodeChecked = {
        "SPD": false
      }
      const result = component.programCodePreventDefault({target: {preventDefault: () => ({})}}, "offerProgramCd", "SPD", false);
      expect(result).toEqual(true);
    });
    it("make expexted call if item is offerProgramCd SPD true", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.programCodeChecked = {
        "SPD": true
      }
      component.programCodePreventDefault({target: {preventDefault: () => ({})}}, "offerProgramCd", "SPD", true);
      expect(component.preventDefault).toEqual(false);
    });
  });
  describe("checkBannersOrDivisionSelection", () => {
    it("checkBannersOrDivisionSelection called", () => {
      component.item = "banners";
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.isAllBannersSelected = new BehaviorSubject(true);
      facetItemServiceStub.getIsAllBannersSelected = () => new Observable();
      spyOn(component, "checkIfAllBannersSelected");
      component.checkBannersOrDivisionSelection();
      expect(component.checkIfAllBannersSelected).toHaveBeenCalled();
    });
    it("checkBannersOrDivisionSelection  divisions called", () => {
      component.item = "divisions";
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.isAllBannersSelected = new BehaviorSubject(true);
      facetItemServiceStub.getisAllDivisionsSelected = () => new Observable();
      spyOn(component, "checkIfAllDivisionStatesSelected");
      component.checkBannersOrDivisionSelection();
      expect(component.checkIfAllDivisionStatesSelected).toHaveBeenCalled();
    });
  });
  describe("doDivisionListExpandedOnRender", () => {
    it("doDivisionListExpandedOnRender called", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      let compiledElement;
      component = fixture.componentInstance;
      compiledElement = fixture.debugElement.nativeElement;
      compiledElement.innerHTML +=
        "<button id='123'>Test Button</button> <button id='collapse-123'>Test Button</button>";
      //fixture.detectChanges();
      facetItemServiceStub.expandedItems = ["collapse-123"];
      component.doDivisionListExpandedOnRender();
    });
  });
  describe("setDivisionChipsOnPgLoad", () => {
    it("setDivisionChipsOnPgLoad called", () => {
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.setOfferFilter = () => {};
      storeGroupServiceStub.populateStoreFilterSearch = () => {};
      storeGroupServiceStub.isDivisionChipsSetOnPgLoad = false;
      component.form = {};
      component.item = "divisons";
      component.setDivisionChipsOnPgLoad();
      expect(storeGroupServiceStub.isDivisionChipsSetOnPgLoad).toEqual(true);
    });
  });
  describe("setExpandedItems", () => {
    it("setExpandedItems called", () => {
      let compiledElement;
      component = fixture.componentInstance;
      compiledElement = fixture.debugElement.nativeElement;
      compiledElement.innerHTML +=
        "<button class = 'collapsed' id='divisions'>Test Button</button>";
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.expandedItems = [1, 2];
      component.setExpandedItems("divisions");
    });
    it("setExpandedItems  expandedItems called", () => {
      let compiledElement;
      component = fixture.componentInstance;
      compiledElement = fixture.debugElement.nativeElement;
      compiledElement.innerHTML +=
        "<button class = 'facets' id='divisions'>Test Button</button>";
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.expandedItems = [1, 2];
      component.setExpandedItems("divisions");
    });
  });
  describe("setDivisionStatesList", () => {
    it("setDivisionStatesList called", () => {
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormControl("divisionRogCds"),
      });
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormArray([]),
      });
      storeGroupServiceStub.setDivisionListControls = () => {};
      component.setDivisionStatesList();
    });
  });
  describe("onToggleSelectAll", () => {
    it("onToggleSelectAll called banners", () => {
      component.item = "banners";
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormArray([]),
      });
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );

      storeGroupServiceStub.populateStoreFilterSearch = () => {};
      spyOn(component, "toggleBannersSelectAll");

      component.onToggleSelectAll();
      expect(component.toggleBannersSelectAll).toHaveBeenCalled();
    });
    it("onToggleSelectAll called divisions", () => {
      component.item = "divisions";
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormArray([]),
      });
      const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      );
      component.form = {};
      storeGroupServiceStub.populateStoreFilterSearch = () => {};
      spyOn(component, "toggleDivisionsSelectAll");

      component.onToggleSelectAll();
      expect(component.toggleDivisionsSelectAll).toHaveBeenCalled();
    });
    it("onToggleSelectAll called programSubType", () => {
      component.item = "progSubType";
      component.form = new UntypedFormGroup({
        divisionRogCds: new UntypedFormArray([]),
      });
     const storeGroupServiceStub: StoreGroupService = fixture.debugElement.injector.get(
        StoreGroupService
      ); 
      component.form = {};
      storeGroupServiceStub.populateStoreFilterSearch = () => {};
      spyOn(component, "toggleSubprogramsSelectAll");

      component.onToggleSelectAll();
      expect(component.toggleSubprogramsSelectAll).toHaveBeenCalled();
    });
  });

  describe("enableExpiredStatusElem", () => {
    it("enableExpiredStatusElem called", () => {
      component.facetItem = {
        "0": {
          value: "Exp",
        },
      };

      const selector = {
        classList: {
          add: () => {},
        },
      };
      const compiledElement = {
        disable: () => {},
      };
      component.enableExpiredStatusElem(0, compiledElement, selector);
    });
    it("enableExpiredStatusElem called else part", () => {
      component.facetItem = {
        "0": {
          value: "Expired",
        },
      };

      const selector = {
        classList: {
          remove: () => {},
        },
      };
      const compiledElement = {
        enable: () => {},
      };
      component.enableExpiredStatusElem(0, compiledElement, selector);
    });
  });
  describe("disableExpiredStatusElem", () => {
    it("disableExpiredStatusElem called", () => {
      component.facetItem = {
        "0": {
          value: "Expired",
        },
      };

      const selector = {
        classList: {
          add: () => {},
        },
      };
      const compiledElement = {
        disable: () => {},
      };
      component.disableExpiredStatusElem(0, compiledElement, selector);
    });
    it("disableExpiredStatusElem called else part", () => {
      component.facetItem = {
        "0": {
          value: "Exp",
        },
      };

      const selector = {
        classList: {
          remove: () => {},
        },
      };
      const compiledElement = {
        enable: () => {},
      };
      component.disableExpiredStatusElem(0, compiledElement, selector);
    });
  });
  describe("setStatesValue", () => {
    it("setStatesValue called", () => {
      const object = {
        stateControl: new UntypedFormControl("divisionRogCds"),
        stateData: {
          checked: true,
        },
        elemId: false,
        target: {
          target: {
            checked: true,
          },
        },
        facet: new UntypedFormControl("divisionRogCds"),
      };
      spyOn(component, "toggleDivisionChekbox");
      component.setStatesValue(object);
      expect(component.toggleDivisionChekbox).toHaveBeenCalled();
    });
    it("setStatesValue called checked false", () => {
      const object = {
        stateControl: new UntypedFormControl("divisionRogCds"),
        stateData: {
          checked: true,
        },
        elemId: false,
        target: {
          target: {
            checked: false,
          },
        },
        facet: new UntypedFormControl("divisionRogCds"),
      };
      spyOn(component, "toggleDivisionChekbox");
      component.setStatesValue(object);
      expect(component.toggleDivisionChekbox).toHaveBeenCalled();
    });
  });
  describe("checkIfAllDivisionStatesSelected", () => {
    it("checkIfAllDivisionStatesSelected called", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetList = {
        controls: [],
      };
      facetItemServiceStub.setisAllDivisionsSelectedValue = () => {};
      component.checkIfAllDivisionStatesSelected();
    });
  });
  describe("onChange", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      component.facetpage = "home";
      component.facetpage = "offerHome";
      const obj = {
        target: "",
        facet: "",
        facetItem: "",
      };
      facetItemServiceStub.programCodeChecked = {
        SC:true
      }
      component.form = new UntypedFormGroup({});
      spyOn(searchOfferRequestServiceStub, "populateHomeFilterSearch");
      spyOn(component, "onChangeFacetHome");
      spyOn(facetItemServiceStub, "setOfferFilter");
      component.onChange(obj);
      expect(component.onChangeFacetHome).toHaveBeenCalled();
    });
    it("should trace else when facetpage is storeGroup", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      const searchOfferRequestServiceStub: SearchOfferRequestService = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      component.facetpage = "storeGroup";
      const obj = {
        target: "",
        facet: "",
        facetItem: "",
      };
      facetItemServiceStub.programCodeChecked = {
        SC:true
      }
      component.form = new UntypedFormGroup({});
      //spyOn(searchOfferRequestServiceStub, "populateStoreFilterSearch");
      spyOn(component, "onChangeStoreGroup");
      spyOn(facetItemServiceStub, "setOfferFilter");
      component.onChange(obj);
      expect(component.onChangeStoreGroup).toHaveBeenCalled();
    });
  });

  describe("toggleBannersSelectAll", () => {
    it("should make expected calls", () => {
      component.facetList = new UntypedFormArray([
        new UntypedFormGroup({
          id: new UntypedFormControl(1),
          value: new UntypedFormControl("myValue"),
          selected: new UntypedFormControl(true),
        }),
      ]);
      component.facetItem = [{ id: 2, value: 10, selected: true }];
      component.isAllBannersSelected = false;
      spyOn(component, "setFacetsExpanded");
      spyOn(component, "checkIfAllBannersSelected");
      component.toggleBannersSelectAll();
      expect(component.checkIfAllBannersSelected).toHaveBeenCalled();
    });
    it("should trace else when isAllBannersSelected is true", () => {
      component.facetList = new UntypedFormArray([new UntypedFormGroup({})]);
      component.isAllBannersSelected = true;
      spyOn(component, "setFacetsExpanded");
      spyOn(component, "checkIfAllBannersSelected");
      component.toggleBannersSelectAll();
      expect(component.checkIfAllBannersSelected).toHaveBeenCalled();
    });
  });

  describe("setExpiredElementOnInit", () => {
    it("should make expected calls", () => {
      component.form = new UntypedFormGroup({
        offerStatus: new UntypedFormArray([new UntypedFormGroup({})]),
      });
      spyOn(component, "enableDisableExpiredEle");
      component.setExpiredElementOnInit();
      expect(component.enableDisableExpiredEle).toHaveBeenCalled();
    });
  });

  describe("checkIfAllBannersSelected", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "setisAllBannersSelectedValue");
      component.facetList = new UntypedFormArray([
        new UntypedFormGroup({
          id: new UntypedFormControl("test"),
          value: new UntypedFormControl("myValue"),
        }),
      ]);
      component.checkIfAllBannersSelected();
      expect(facetItemServiceStub.setisAllBannersSelectedValue).toHaveBeenCalled();
    });
    it("should trace else when control is false", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      spyOn(facetItemServiceStub, "setisAllBannersSelectedValue");
      component.facetList = new UntypedFormArray([
        new UntypedFormGroup({
          id: new UntypedFormControl("test"),
          value: new UntypedFormControl("myValue"),
        }),
        new UntypedFormControl(false),
      ]);
      component.checkIfAllBannersSelected();
    });
  });

  describe("setDivisionSectionValues", () => {
    it("should make expected calls", () => {
      const obj = {
        statesObj: ["Norcal", "Seattle"],
      };
      spyOn(component, "setDivisions");
      spyOn(component, "setStatesValue");
      component.setDivisionSectionValues(obj);
      expect(component.setStatesValue).toHaveBeenCalled();
    });
    it("should trace else when no statesObj", () => {
      const obj = {};
      spyOn(component, "setDivisions");
      spyOn(component, "setStatesValue");
      component.setDivisionSectionValues(obj);
      expect(component.setStatesValue).not.toHaveBeenCalled();
    });
  });
  describe("showSelectAllLabel ", () => {
    it("should make expected calls - storeGroup", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.item='divisions';
      component.facetpage="storeGroup";
      let res = component.showSelectAllLabel();
      expect(res).toEqual(true);
    });
    it("should make expected calls - home", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.item='progSubType';
      component.facetpage="home";
      facetItemServiceStub.searchText='';
      facetItemServiceStub.indexList=[];
      let res = component.showSelectAllLabel();
      expect(res).toEqual(true);
    });
    it("should make expected calls - else", () => {
      component.facetpage="productGroup";
      let res = component.showSelectAllLabel();
      expect(res).toEqual(false);
    });
  });

  describe("hideProgramSubtypesOnSearch", () => {
    it("should make expected calls - if ", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.searchText='';
      facetItemServiceStub.indexList=[3,4,5];
      let res = component.hideProgramSubtypesOnSearch(3);
      expect(res).toEqual(true);
    });
    it("should make expected calls - Empty index list ", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.searchText='';
      facetItemServiceStub.indexList=[];
      let res = component.hideProgramSubtypesOnSearch(7);
      expect(res).toEqual(true);
    });
  });
  describe("checkSubProgramsSelection", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.isAllSubprogramsSelected  = new BehaviorSubject(true);
      facetItemServiceStub.getIsAllSubprogramsSelected = () => new Observable();
      spyOn(component, "checkIfAllSubProgramsSelected");
      component.checkSubProgramsSelection();
      expect(component.checkIfAllSubProgramsSelected).toHaveBeenCalled();
    });
  });
  describe("searchItem", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetItem = [
        {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
        {id: "A2", count: 0, value: "A2", selected: false, color: ""},
        {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
        {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
        {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
      ];
      let e ={
        target:{
          value: 'ad'
        }
      }
      facetItemServiceStub.searchText='ad';
      facetItemServiceStub.indexList=[];
      let res = [2,3,4];
      spyOn(component, "checkIfAllSubProgramsSelected");
      component.searchItem(e);
      expect(component.checkIfAllSubProgramsSelected).toHaveBeenCalled();
    });
  });
  describe("handleAllSubProgramSelection", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.isAllSubprogramsSelected  = new BehaviorSubject(true);
      facetItemServiceStub.setIsAllSubprogramsSelected = () => new Observable();
      spyOn(facetItemServiceStub, "setIsAllSubprogramsSelected");
      component.handleAllSubProgramSelection(true);
      expect(facetItemServiceStub.setIsAllSubprogramsSelected).toHaveBeenCalled();
    });
  });
  describe("toggleSubprogramsSelectAll", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetList  = {
        controls:[
          {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
          {id: "A2", count: 0, value: "A2", selected: false, color: ""},
          {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
          {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
          {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
        ]
      }
      facetItemServiceStub.indexList=[0,1,2];
      spyOn(component,"setFacetsExpanded");
      spyOn(component, "setSelectAllValue");
      spyOn(component,"checkIfAllSubProgramsSelected");
      component.toggleSubprogramsSelectAll();
      expect(component.setSelectAllValue).toHaveBeenCalled();
      expect(component.checkIfAllSubProgramsSelected).toHaveBeenCalled();
      expect(component.setFacetsExpanded).toHaveBeenCalled();
    });
    it("should make expected calls- else block", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      component.facetList  = {
        controls:[
          {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
          {id: "A2", count: 0, value: "A2", selected: false, color: ""},
          {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
          {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
          {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
        ]
      }
      facetItemServiceStub.indexList = [];
      spyOn(component,"setFacetsExpanded");
      spyOn(component, "setSelectAllValue");
      spyOn(component,"checkIfAllSubProgramsSelected");
      component.toggleSubprogramsSelectAll();
      expect(component.setSelectAllValue).toHaveBeenCalled();
      expect(component.checkIfAllSubProgramsSelected).toHaveBeenCalled();
      expect(component.setFacetsExpanded).toHaveBeenCalled();
    });
  });
  describe("setProgramSubTypeValue", () => {
    it("should make expected calls", () => {
      let facetControlsList  = [ 
        {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
        {id: "A2", count: 0, value: "A2", selected: false, color: ""},
        {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
        {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
        {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
        ];
      let index= 1;
      spyOn(component, "setValueOnFilterSearch");
      component.setSelectAllValue(facetControlsList,index);
      expect(component.setValueOnFilterSearch).toHaveBeenCalled();
    });
  });
  describe("setSubTypeOnFilterSearch", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      let control  =  new UntypedFormControl({id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""});
      let index= 0;
      let toggle ="Select All";
      component.facetItem = [ 
        {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
        {id: "A2", count: 0, value: "A2", selected: false, color: ""},
        {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
        {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
        {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
        ];
      component.setValueOnFilterSearch(control,index,toggle);
    });
    it("should make expected calls for else", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      let control  =  new UntypedFormControl({id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""});
      let index= 0;
      let toggle ="";
      component.facetItem = [ 
        {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
        {id: "A2", count: 0, value: "A2", selected: false, color: ""},
        {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
        {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
        {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
        ];
      component.setValueOnFilterSearch(control,index,toggle);
    });
  });
  describe("checkIfAllSubProgramsSelected", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      facetItemServiceStub.indexList  = [0,1,2];
      component.facetList  = {
        controls:[
          {id: "10% CB 5% NB", count: 0, value: "10% CB 5% NB", selected: false, color: ""},
          {id: "A2", count: 0, value: "A2", selected: false, color: ""},
          {id: "ADRENALINE", count: 0, value: "ADRENALINE", selected: false, color: ""},
          {id: "Add new to BU", count: 0, value: "Add new to BU", selected: false, color: ""},
          {id: "Additional Reward", count: 0, value: "Additional Reward", selected: false, color: ""}
        ]
      }
      component.selectAllText ="Select All";
      spyOn(component, "handleAllSubProgramSelection");
      component.checkIfAllSubProgramsSelected();
      expect(component.handleAllSubProgramSelection).toHaveBeenCalled();
    });
  });
  describe("onSelectProgramCode", () => {
    it("should make expected calls", () => {
      const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
        FacetItemService
      );
      let name = "programCode";
      let checked = true;
      let id = 0;
      let removeChipOnOffers = {};
      let event =  { facetItem:{id},target:{target:{checked = true,name ='programCode'} = {}} = {}} ;
      let selectedItem = event;
      facetItemServiceStub.getProgramCode$ = new BehaviorSubject(true);
      component.onSelectProgramCode(selectedItem,name,checked);
      expect(facetItemServiceStub.programCodeSelected.toString()).toEqual(selectedItem.facetItem.id.toString())
      expect(facetItemServiceStub.programCodeInitial).toEqual(true) ;
      expect(facetItemServiceStub.programCodeChanged).toEqual(true);
      expect(facetItemServiceStub.programFilterChange).toEqual(true);
    });
  });
});
import { Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2 } from "@angular/core";
import { UntypedFormArray, UntypedFormControl } from "@angular/forms";
import { Router } from "@angular/router";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { StringUtilsHelperService } from "@appServices/common/string-utils.helper.service";
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import * as clone from 'clone';
// import * as _ from 'lodash';

@Component({
  selector: "facet-item",
  templateUrl: "./facet-item.component.html",
  styleUrls: ["./facet-item.component.scss"],
})
export class FacetItemComponent extends UnsubscribeAdapter implements OnInit {
  @Input() facetItem;
  @Input() item;
  @Input() createStoreGroup;
  @Output() facetClick = new EventEmitter<any>();
  @Input() facetpage;
  @Input() form;
  @Input() totalStores;
  @Input() facetShow;
  accordin;
  stateAccordin;
  targetItem;
  facetList;
  divisionStatesList;
  divisionState;
  isAllBannersSelected;
  isAllDivisionsSelected;
  isAllSubprogramsSelected;
  selectAllText;
  initialSelectedStatesList = [];
  facetFilter = JSON.parse(JSON.stringify(CONSTANTS.FACET_FILTER));
  private features;
  setExpiredDisabled: any = false;
  currentRoute = this.router.url;
  searchFacetItem: any[];
  preventDefault: boolean = false;
  preventDefaultdAppId:boolean = false;

  constructor(
    private storeGroupService: StoreGroupService,
    public facetItemServic: FacetItemService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private authService: AuthService,
    private permissionsService: PermissionsService,
    private el: ElementRef,
    private renderer: Renderer2,
    private router: Router,
    private _permissionsService: PermissionsService,
    private featureFlagService: FeatureFlagsService,
    public commonRouteService: CommonRouteService,
    private stringHelper: StringUtilsHelperService
  ) {
    super();
  }

  searchItem(event) {
    this.facetItemServic.searchText = event.target.value;
    this.facetItemServic.indexList = [];
    if (this.facetItemServic.searchText) {
      this.facetItem.forEach((item, index) => {
        const filterSearchText = item.value.toLowerCase();
        const searchTextValue = this.facetItemServic.searchText.toLowerCase();
        if (["offerHome", "home"].includes(this.facetpage) && filterSearchText.includes(searchTextValue)) {
          this.facetItemServic.indexList.push(index);
        }
      });
      this.checkIfAllSubProgramsSelected();
    }
  }
  showSelectUnSelectLabel() {
      switch(this.item) {
        case "bggm":
        case "bugm":
        case "categoryId": {
          return ["template", "home"].includes(this.facetpage);
        }
        case "regionId": 
        case "status": {
          return this.facetItemServic.programCodeSelected === CONSTANTS.BPD && ["home"].includes(this.facetpage);
        }
        default : {
          return false;
        }
      }
  }
  setFormValue(value) {
    const facetControlsList = this.facetList.controls as UntypedFormArray;
    for (let index = 0; index < facetControlsList.length; index++) {
      const control = facetControlsList?.at(index),
        elementValue = this.facetItem[index];
      elementValue.selected = value;
      control.setValue(value ? elementValue : false);
    }
  }
  get accordinBasedOnItem() {
    switch (this.item) {
      case "bggm":
      case "bugm":
      case "categoryId": {
        return this.accordinValue;
      }
      default: {
        return this.accordin;
      }
    }
  }
 
  onToggleSelectUnSelectAll() {
    if (this.selectAllTextToDisplay === "Unselect All") {
      this.setFormValue(false);
      this.selectAllText = "Select All";     
    } else {
      this.setFormValue(true);
      this.selectAllText = "Unselect All";     
    }
    
    this.facetItemServic.isBpdOfferReqSelected = this.facetItemServic.programCodeSelected === CONSTANTS.BPD;
    this.toggleExpiredStatusState();
    this.facetClick.emit({ form: this.form.value, item: this.item });    
  }

  toggleExpiredStatusState(){
        const expiredStatusIndex = this.facetList.controls.length -1;
    
    //Execute only for Expired Status in OR management
    if(!(["home","offerHome"].includes(this.facetpage) && (this.item === "status" || this.item === "offerStatus") && this.isExpiredStatusExist(expiredStatusIndex))){
       return false;
    }
    
    const expiredElem = this.facetList.at(expiredStatusIndex) as UntypedFormControl,
    selector:any = this.el.nativeElement.querySelector(
      `#custom-check-${this.facetItem[expiredStatusIndex].value.replace(/[^\w]/gi, "_")}`
    ) ;
    
    if(this.selectAllTextToDisplay === "Unselect All"){
      //In the previous fn, we are setting all the checkboxes as checked, so unchecking it.
      expiredElem.setValue(false);
      this.disableExpiredStatusElem(expiredStatusIndex, expiredElem, selector); 
    }else{
      this.enableExpiredStatusElem(expiredStatusIndex, expiredElem, selector); 
    }
  }

  handleSelectAllForHome() {
    switch(this.item) {
      case "progSubType": {
        const { indexList, searchText } = this.facetItemServic;
        return !(!indexList?.length && searchText);
      }
      case "regionId": 
      case "status" : {
        return this.facetItemServic.programCodeSelected !== CONSTANTS.BPD
      }
      default: {
        return false;
      }
    }
  }
  showSelectAllLabel() {
    switch(this.facetpage) {
      case "storeGroup" : {
        return this.item === "divisions" || this.item == "banners"; 
      } 
      case "home": {
        return this.handleSelectAllForHome();
      }
      case "offerHome" : {
        return this.handleSelectAllForOffer();
      }
      default :{
        return false;
      }
    }
  }
  ngAfterViewInit(): void {
  /**
   * Need to disable status checkbox based on selection or on  search.
   */
  if (["home","offerHome"].includes(this.facetpage) && ["status","offerStatus"].includes(this.item)) {
      const pCode = this.facetItemServic.programCodeSelected;
      this.setExpiredElementOnInit();
      pCode === CONSTANTS.SC && this.setDigitalNonDigitalDisabled();
    }
  }
  handleSelectAllForOffer() {
    switch(this.item) {
      case "progSubType": {
        const { indexList, searchText } = this.facetItemServic;
        return !(!indexList?.length && searchText);
      }
      default: {
        return false;
      }
    }
  }
  get accordinValue() {
    if (["bggm", "bugm", "categoryId"].includes(this.item)) {
      const isItem = this.el?.nativeElement?.querySelector(`#${this.item?.replace(/[' ']/g, "_")}`);
      if (isItem) {
        this.renderer?.removeClass(isItem, "show");
      }
      const isSelected = this.form?.value?.[this.item]?.some((ele) => toString.call(ele) === "[object Object]");
      if (isSelected && isItem) {
        this.renderer?.addClass(isItem, "show");
      }
      return isSelected;
    }
    return false;
  }

  ngOnChanges(): void {
    // intentionally left empty
  }

  ngOnInit() {
    this.divisionState = this.facetItemServic.getdivsionStateFacetItems();
    if (this.form) {
      this.targetItem = this.item.replace(/[' ']/g, "_");
      this.facetList = this.form.get(this.item);
      this.accordin = this.facetShow[this.item];
      if (this.form.get("divisionRogCds") && this.item == "divisions") {
        if (this.facetpage === "offerHome" && this.currentRoute === `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`) {
          this.facetFilter["divisions"].displayValue = "Division (J4U)";
        }
        this.setDivisionStatesList();
      }
    }
    this.features = this.storeGroupService.getFeatureKeys();
    if (this.createStoreGroup) {
      this.form.disable();
    } else {
      this.form.enable();
    }

    this.initSubscribes();
    if (this.storeGroupService.getEnableDisableFacetSource) {
      this.storeGroupService.getEnableDisableFacetSource.subscribe((bln) => {
        if (bln) {
          this.form.enable();
        } else {
          this.form.disable();
        }
      });
    }

    this.facetItemServic.getEnableFormSource$.subscribe((bln) => {
      if (bln) {
        this.form.enable();
      }
    });
    if (this.facetpage == "storeGroup") {
      this.checkBannersOrDivisionSelection();
      this.doDivisionListExpandedOnRender();
    }
    if (this.facetpage == "storeGroup" && this.item == "divisions") {
      this.setDivisionChipsOnPgLoad();
    }
    if (["progSubType"].includes(this.item)) {
      this.checkSubProgramsSelection();
    }
    if (["regionId"].includes(this.item)) {
      this.checkIfAllSelectedFromRegionStatus();
    }
    if(!this.featureFlagService.isOfferRequestArchivalEnabled)
    {
      if (["status"].includes(this.item)) {
        this.checkIfAllSelectedFromRegionStatus();
      }
    }
    /**
     * Uncomment below line, if program code filter in OR and offer need to be expanded always
    //this.accordin = ["offerProgramCd", "programCode"].includes(this.item) ? true : this.accordin;
    **/
    if (["home", "offerHome", "template"].includes(this.facetpage)) {
      this.filterExpand(this.form?.value?.[this.item]);
    }
  }

  initSubscribes(){
    this.subs.sink  = this.facetItemServic.chipCloseEvent$.subscribe(()=>{
      //When chip closes, update the facet list controls
      this.updateOptionStates();
    })
  }

  filterExpand(filterItem) {
    if (this.item !== "divisions") {
      this.accordin = filterItem?.some((ele) => toString.call(ele) === "[object Object]");
    } else {
      let divisionsList = this.form?.get("divisionRogCds");
      this.accordin =
        divisionsList &&
        Object.keys(divisionsList.value).some((division) =>
          divisionsList.value[division]?.some((ele) => toString.call(ele) === "[object Object]")
        );
    }
  }
  get isTemplateRouteActivated() {
    return this.commonRouteService.currentActivatedRoute?.includes("template");
  }
  enableProgramCodeBasedOnFeatureFlag(progmCd) {
    let hidePC = this.hideOtherProgramCodesForBPD(progmCd);
    if (!hidePC) {
      return;
    }
    switch (progmCd) {
      case "Grocery Reward": {
        return true;
      }
      case "Specialty PD": {
        return true;
      }
      // Currently Hiding the BASE PD FROM UI SIDE in Offer and Offer Request - Should be remove from Config
      case "Base PD": {
        return true;
      }
      case "Manufacturer Coupon": {
        //In OR home page, hide MF
        if (this.facetpage === "home") {
          return false;
        }
        return true;
      }
      default: {
        return true;
      }
    }
  }

  hideOtherProgramCodesForBPD(progmCd) {
    if (progmCd !== "Base PD" && this.isTemplateRouteActivated) {
      return false;
    } else {
      return true;
    }
  }

  hideFacetItems(item, progmCd, i) {
    if (item == "programCode" || item == "offerProgramCd") {
      return this.enableProgramCodeBasedOnFeatureFlag(progmCd);
    } else if (item === "progSubType" || item === "eventids") {
        return this.hideProgramSubtypesOnSearch(i);
    }
    else {
      return true;
    }
    }
    searchEventAndSubType(item) {
        switch (item) {
            case "eventids":
            case "progSubType": {
                return [item].includes(this.item)

            }
            default: {
                return false;
            }
        }
    }
  hideProgramSubtypesOnSearch(index) {
    // On search showing facets which are matching, otherwise hide facet item   
    const { indexList, searchText } = this.facetItemServic;
    if (indexList?.length) {
      return indexList.includes(index);
    } else if (searchText && !indexList?.length) {
      // if search text is there but there are no results for text we need to hide whole list
      return false;
    } else {
      return true;
    }
    }
  checkIfFeatureEnabled(feature) {
    return this.featureFlagService.isFeatureFlagEnabled(feature);
  }
  setNonDigitalDisabledOnPodView() {
    const isPodView = this.facetItemServic.podView;
    const facetControlsList = this.facetList.controls;

    for (let count = 0; count < facetControlsList?.length; count++) {
      const element = this.facetList.at(count) as UntypedFormControl;
      const selector = this.el.nativeElement.querySelector(
        `#custom-check-${this.facetItem[count].value.replace(/[^\w\s]/gi, "_")}`
      ) as HTMLElement;
      if (isPodView) {
        element.disable({ emitEvent: false });
        selector?.classList?.add("isDisabled");
        this.filterExpand(facetControlsList);
      } else {
        element.enable({ emitEvent: false });
        selector?.classList?.remove("isDisabled");
        this.accordin = false;
      }
    }
  }
  updateOptionStates() {
    if (this.item === "digital") {
      this.setNonDigitalDisabledOnPodView();
    }
    if (this.item === "offerStatus" || this.item === "status") {
      this.setExpiredElementOnInit();
    }
    if ((this.item === "status" || this.item === "offerStatus" ) && ["home","offerHome"].includes(this.facetpage) && this.facetItemServic.programCodeSelected === CONSTANTS.SC) {
      this.setDigitalNonDigitalDisabled();
    }
    if (this.item === "offerRequestorGroup") {
      const parentElement = this.el.nativeElement.parentElement,
        offerRequestorGroup = this.facetItemServic.chipComponent["offerRequestorGroup"];
      if (this.facetItemServic.podView) {
        parentElement && parentElement.classList.add("d-none");
        if (offerRequestorGroup) {
          offerRequestorGroup.close(true);
        }
        for (let control of this.facetList.controls) {
          control.setValue(false);
        }
      } else {
        parentElement && parentElement.classList.remove("d-none");
      }
    }
  }
  setScrollBar() {
    const cntrols = this.facetList.controls;
    const indexList = this.facetItemServic.indexList;
    return indexList?.length > 15 || (cntrols?.length > 15 && !indexList.length);
  }
  secureProgramCodeByUserPermissions(facetItem) {
    /**
     * Function to hide/show programCode radio based on user permissions
     */



    const permissions = this._permissionsService.getPermissions();
    const temp = permissions[facetItem.permission];
    if (!temp) {
      return "hide";
    } else {
      return "";
    }
  }

  doDivisionListExpandedOnRender() {
    // it will keep expand the division list even after render.
    for (let item of this.facetItemServic.expandedItems) {
      const targetElem = item && item.split("collapse-")[1];
      let elem = document.getElementById(item);
      let target = document.getElementById(targetElem);
      elem && elem.classList.remove("collapsed");
      target && target.classList.add("show");
    }
  }
  

  getFacetVal({val,item}){
    //Replace whitespaces in status value with _ in OR Home page
    if(["status", "offerStatus"].includes(item) && ["home", "offerHome"].includes(this.facetpage) && val && typeof val.replaceAll === "function" ){
      return val.replaceAll(/[\s]/gi, "_");
    }
    return val;
  }
  
  setExpandedItems(id) {
    // it will get expanded division list id's and store in service property
    let elem = this.el.nativeElement.querySelector(`#${this.stringHelper.escapeValue(id)}`);
    let expandedItems = this.facetItemServic.expandedItems.slice();
    if (elem.classList.contains("collapsed")) {
      expandedItems.push(id);
    } else {
      expandedItems = expandedItems.filter((item) => item !== id);
    }
    this.facetItemServic.expandedItems = [...new Set(expandedItems)];
  }

  setDivisionChipsOnPgLoad() {
    //Making sure this is called only once
    if (!this.storeGroupService.isDivisionChipsSetOnPgLoad) {
      this.facetItemServic.setOfferFilter("facetFilter");
      this.storeGroupService.populateStoreFilterSearch({
        facetChip: this.facetItemServic.populateFacetFieldChips(this.form.value, this.item),
        facetpage: this.facetpage,
      });
      this.facetClick.emit({ form: this.form.value, item: this.item });
      this.storeGroupService.isDivisionChipsSetOnPgLoad = true;
    }
  }
  //will check banners or divions select all selection if any
  checkBannersOrDivisionSelection() {
    if (this.item == "banners") {
      this.checkIfAllBannersSelected();
      this.facetItemServic.getIsAllBannersSelected().subscribe((bln) => {
        this.isAllBannersSelected = bln;
        this.selectAllText = this.isAllBannersSelected ? "Unselect All" : "Select All";
      });
    }
    if (this.item == "divisions") {
      this.checkIfAllDivisionStatesSelected();
      this.facetItemServic.getisAllDivisionsSelected().subscribe((bln) => {
        this.isAllDivisionsSelected = bln;
        this.selectAllText = this.isAllDivisionsSelected ? "Unselect All" : "Select All";
      });
    }
  }

  checkSubProgramsSelection() {
    this.checkIfAllSubProgramsSelected();
    this.facetItemServic.getIsAllSubprogramsSelected().subscribe((bln) => {
      this.isAllSubprogramsSelected = bln;
      this.selectAllText = this.isAllSubprogramsSelected ? "Unselect All" : "Select All";
    });
  }
  //getting division states form group from form and getting divisions whom sub states are selected
  setDivisionStatesList() {
    this.divisionStatesList = this.form.get("divisionRogCds");
    this.storeGroupService.setDivisionListControls(this.divisionStatesList.controls);
    let allSelectedDivisonstates = Object.keys(this.divisionStatesList.value).filter((division) => {
      return this.divisionStatesList.value[division].some((entry) => typeof entry == "object");
    });
    this.initialSelectedStatesList =
      allSelectedDivisonstates &&
      allSelectedDivisonstates.filter((val) => {
        return this.divisionStatesList.value[val].some((entry) => typeof entry == "boolean");
      });
    
    // Added the below condition in order to display divisions tab always expand on page load in store group
    if (this.facetpage == "storeGroup" && this.item == "divisions") {
      this.accordin = true;
    }
  }
  //To toggle select all/ Unselect all on Banners and divisions
  onToggleSelectAll() {
    if (this.item == "banners") {
      this.toggleBannersSelectAll();
    }
    if (this.item == "divisions") {
      this.toggleDivisionsSelectAll();
    }

    if (["progSubType"].includes(this.item)) {
      this.facetItemServic.setOfferFilter("facetFilter");
      this.toggleSubprogramsSelectAll();
    }
    if (["status", "regionId"].includes(this.item)) {
      this.facetItemServic.setOfferFilter("facetFilter");
      this.toggleRegionIdStatus();
    }

    this._searchOfferRequestService.populateHomeFilterSearch({
      facetChip: this.facetItemServic.populateFacetFieldChips(this.form.value, this.item),
    });

    this.storeGroupService.populateStoreFilterSearch({
      facetChip: this.facetItemServic.populateFacetFieldChips(this.form.value, this.item),
      facetpage: this.facetpage,
    });

    this.facetClick.emit({ form: this.form.value, event: null, item: this.item });
  }
  // Toggle selection on divisions
  toggleDivisionsSelectAll() {
    this.setFacetsExpanded();
    let divisionStatesControls = [];
    let state = {};
    let facetControlsList = this.facetList.controls;
    for (let i = 0; i < facetControlsList.length; i++) {
      this.facetItem[i].selected = !this.isAllDivisionsSelected;
      let facetItemValue = this.removeSpecialChars(this.facetItem[i].value);
      let className = `#custom-check-${this.stringHelper.escapeValue(facetItemValue)}`,
        element = this.el?.nativeElement?.querySelector(className);

      element && this.renderer?.removeClass(element, "partiallyFilledCB");

      divisionStatesControls =
        this.divisionStatesList["controls"][this.facetItem[i].value] &&
        this.divisionStatesList["controls"][this.facetItem[i].value].controls;
      state = this.divisionState[this.facetItem[i].value];
      if (!this.isAllDivisionsSelected) {
        if (divisionStatesControls) {
          for (let k = 0; k < divisionStatesControls.length; k++) {
            state[k].selected = !this.isAllDivisionsSelected;
            divisionStatesControls[k].setValue(state[k]);
          }
        }
        facetControlsList[i].setValue(this.facetItem[i]);
      } else {
        if (divisionStatesControls) {
          for (let n = 0; n < divisionStatesControls.length; n++) {
            state[n].selected = !this.isAllDivisionsSelected;
            divisionStatesControls[n].setValue(!this.isAllDivisionsSelected);
          }
        }
        facetControlsList[i].setValue(!this.isAllDivisionsSelected);
      }
    }
    this.checkIfAllDivisionStatesSelected();
  }
  //will check if all the division and states are selected or not.
  checkIfAllDivisionStatesSelected() {
    let facetControlsList = this.facetList.controls;
    let isAllSelected = facetControlsList.every((control) => {
      return typeof control.value == "object";
    });
    this.facetItemServic.setisAllDivisionsSelectedValue(isAllSelected);
    if (isAllSelected) {
      this.facetItemServic.setisAllDivisionsSelectedValue(true);
    } else {
      this.facetItemServic.setisAllDivisionsSelectedValue(false);
    }
  }
  //will check if all the banners are selected or not in facets
  checkIfAllBannersSelected() {
    let facetControlsList = this.facetList.controls;
    let selectedControls = facetControlsList.filter((obj) => {
      return typeof obj.value == "object";
    });
    if (facetControlsList.length === selectedControls.length) {
      this.facetItemServic.setisAllBannersSelectedValue(true);
    } else {
      this.facetItemServic.setisAllBannersSelectedValue(false);
    }
  }

  toggleRegionIdStatus() {
    this.setFacetsExpanded();
    let facetControlsList = this.facetList.controls, count = this.listCount;

    //If Expired status is already selected, dont select the other statuses
    if(facetControlsList.at(facetControlsList.length-1).value?.value === CONSTANTS.EXPIRED_STATUS_OR_DISPLAY){
         return false;
    }
    
    for (let index = 0; index < count; index++) {
      this.setSelectAllValue(facetControlsList, index);
    }
    this.checkIfAllSelectedFromRegionStatus();
  }

  get listCount(){
  //If expired status exists, dont count it- When select ALl is clicked, excluding expired status all needs to be checked
   const count = this.facetList.controls.length;
   if(this.isExpiredStatusExist(count - 1)){
      return  count - 1;
   }
      return count;
  }

  isExpiredStatusExist(lastIndex){
    //This is based on assumption, that expired status is always last
    if(this.item == "status" && this.facetItem[lastIndex].value === CONSTANTS.EXPIRED_STATUS_OR_DISPLAY ){
      return true
    }
  }


  removeExpiredControlFromStatus(){
    //removes the expired status control
    let facetControlsList = clone(this.facetList.controls), lastIndex = facetControlsList.length - 1;
    
    if(this.isExpiredStatusExist(lastIndex)){
     facetControlsList.splice(lastIndex, 1);
     return facetControlsList
    }

    return this.facetList.controls;
  }

  checkIfAllSelectedFromRegionStatus() {
    let facetControlsList = this.removeExpiredControlFromStatus();
    
    const isAllSelected = facetControlsList.every((control) => {
      return typeof control.value == "object";
    });
    
    if(this.facetpage == "home" && this.item === "status"){
      //Disabling the expired status control
      this.setExpiredElementOnInit();
    }
    
    this.selectAllText = isAllSelected ? "Unselect All" : "Select All";
  }

  toggleSubprogramsSelectAll() {
    this.setFacetsExpanded();
    let facetControlsList = this.facetList.controls;

    const indexList = this.facetItemServic.indexList;
    if (indexList.length) {
      indexList.forEach((eleIndex) => {
        this.setSelectAllValue(facetControlsList, eleIndex);
      });
    } else {
      for (let index = 0; index < facetControlsList.length; index++) {
        this.setSelectAllValue(facetControlsList, index);
      }
    }

    this.checkIfAllSubProgramsSelected();
  }
  setSelectAllValue(facetControlsList, index) {
    const control = facetControlsList[index];
    this.setValueOnFilterSearch(control, index, this.selectAllText === "Select All");
  }
  setValueOnFilterSearch(control, index, toggle) {
    if (toggle) {
      control.setValue(this.facetItem[index]);
    } else {
      control.setValue(toggle);
    }
  }

  checkIfAllSubProgramsSelected() {
    // this method is to check whether all subprorams are selected or not when user search text or not
    const indexList = this.facetItemServic.indexList;
    let facetControlsList = this.facetList.controls,
      isAllSelected;
    if (indexList && indexList.length) {
      const selectedFacets = facetControlsList.filter((control, index) => indexList.includes(index) && typeof control.value == "object");
      isAllSelected = selectedFacets?.length === indexList?.length;
    } else {
      isAllSelected = facetControlsList.every((control) => {
        return typeof control.value == "object";
      });
    }
    this.handleAllSubProgramSelection(isAllSelected);
  }

  handleAllSubProgramSelection(isAllSelected) {
    // if all selected, then show unselect all label otherwise select all label
    this.facetItemServic.setIsAllSubprogramsSelected(isAllSelected);
  }
  // toggle selections on banners
  toggleBannersSelectAll() {
    this.setFacetsExpanded();
    let facetControlsList = this.facetList.controls;
    facetControlsList.map((control, index) => {
      control.selected = !this.isAllBannersSelected;
      if (!this.isAllBannersSelected) {
        control.setValue(this.facetItem[index]);
      } else {
        control.setValue(!this.isAllBannersSelected);
      }
    });
    this.checkIfAllBannersSelected();
  }
  setDigitalNonDigitalDisabled() {
    // set digital and non-digital disabled if none of the status is selected
    if(!this.form.value.status){
      return;
    }
    let statusValue = this.form && [...this.form.value.status];
    let selectedFacets = statusValue.filter((ele) => typeof ele !== "boolean" && ele?.id !== "DI" && ele?.id !== "NDI");
    for (let count = 0; count < 2; count++) {
      const element = this.facetList.at(count) as UntypedFormControl;
      const selector = this.el.nativeElement.querySelector(
        `#custom-check-${this.facetItem[count].value.replace(/[^\w\s]/gi, "_")}`
      ) as HTMLElement;
      if (!selectedFacets.length) {
        element && element.setValue(false);
        element && element.disable({ emitEvent: false });
        selector?.classList?.add("isDisabled");
      } else {
        element && element.enable({ emitEvent: false });
        selector?.classList?.remove("isDisabled");
      }
    }
  }
  setExpiredElementOnInit() {
    const status = this.form.value.offerStatus || this.form.value.status;
    const expired = status.filter((ele) => ele && typeof ele === "object");
    this.enableDisableExpiredEle(expired);
  }

  enableDisableExpiredEle(checked) {
    for (let count = 0; count < this.facetList.controls.length; count++) {
      const element = this.facetList.at(count) as UntypedFormControl;
      const selector = this.el.nativeElement.querySelector(
        `#custom-check-${this.facetItem[count].value.replace(/[^\w]/gi, "_")}`
      ) as HTMLElement;
     
      if (!checked.length) {
        element.enable({ emitEvent: false });
        selector?.classList?.remove("isDisabled");
      } else {
        const output = checked.filter((ele) => ele.value === CONSTANTS.EXPIRED_STATUS_OR_DISPLAY);
        if (output.length) {
          this.enableExpiredStatusElem(count, element, selector);
        } else {
          this.disableExpiredStatusElem(count, element, selector);
        }
      }
    }
  }

  enableExpiredStatusElem(count, element, selector) {
    if (this.facetItem[count].value !== "Expired") {
      element.disable({ emitEvent: false });
      selector?.classList?.add("isDisabled");
    } else {
      element.enable({ emitEvent: false });
      selector?.classList?.remove("isDisabled");
    }
  }
  
  disableExpiredStatusElem(count, element, selector) {
    if (this.facetItem[count].value === "Expired") {
      element.disable({ emitEvent: false });
      selector?.classList?.add("isDisabled");
    } else {
      element.enable({ emitEvent: false });
      selector?.classList?.remove("isDisabled");
    }
  }
  onChangeFacetHome(obj) {
    const { target, facet, facetItem } = obj;
    const { checked, name } = target.target;
    if (this.item == "divisions" && this.form.get("divisionRogCds")) {
      this.setDivisionSectionValues(obj);
    } else {
      if (name === "programCode") {
        this.facetItemServic.selectedProgramCode$.next(facetItem.value);
        const programCode = this.form.get(this.item),
          programCodeControl = programCode.controls;
        programCodeControl.forEach((control: UntypedFormControl) => {
          control.setValue(false);
        });
        this.authService.getUserPermissions(this.facetItemServic.programCodeSelected).subscribe((res) => {
          this.permissionsService.loadPermissions(res);
        });
        const appData = this.facetItemServic.getAppData;
        appData?.offerDetailsProgramTypesRequestGR && delete appData.offerDetailsProgramTypesRequestGR;
      }

      if (checked) {
        facetItem.selected = checked;
        facet.setValue(facetItem);
      } else {
        facet.setValue(checked);
      }
      if (this.item === "progSubType") {
        this.checkIfAllSubProgramsSelected();
      }else if(["regionId", "status"].includes(this.item)){
        this.checkIfAllSelectedFromRegionStatus();
      }
    }
  }
  setStatesValue(obj) {
    //sets the state control value
    const { stateControl, stateData, elemId, target, facet } = obj;
    const { checked } = target.target;

    stateData.selected = checked;
    if (checked) {
      stateControl.setValue(stateData);
    } else {
      facet.setValue(checked);
      stateControl.setValue(checked);
    }
    this.toggleDivisionChekbox({ ...obj, elemId });
  }

  toggleDivisionChekbox(obj) {
    //If the states within a division were partially/wholly selected, change the style for the division

    const { facetItem, elemId, facet } = obj;
    let divisionName = facetItem.value;
    let divisionFg = this.divisionStatesList["controls"][divisionName],
      totalStates = divisionFg.length,
      statesValue = divisionFg.value;

    let numStatesNotSelected = statesValue.filter((value) => !value).length,
      isDivisionChecked = false;

    this.renderer.removeClass(this.el.nativeElement.querySelector(`#${this.stringHelper.escapeValue(elemId)}`), "partiallyFilledCB");

    if (totalStates === numStatesNotSelected) {
      //NO states were selected
    } else if (numStatesNotSelected && numStatesNotSelected < totalStates) {
      //partial states were selected
      this.renderer.addClass(this.el.nativeElement.querySelector(`#${this.stringHelper.escapeValue(elemId)}`), "partiallyFilledCB");
    } else {
      isDivisionChecked = true;
      facetItem.selected = true;
      //all the states were selected
    }
    if (isDivisionChecked) {
      facet.setValue(facetItem);
    } else {
      facet.setValue(isDivisionChecked);
    }
  }

  setDivisions(obj) {
    //If clicked on Divisions
    const { target, facet, facetItem, selected, elemId } = obj,
      { checked } = target.target;

    let divisionName = facetItem.value;
    let divisionStatesControls =
      this.divisionStatesList["controls"][divisionName] && this.divisionStatesList["controls"][divisionName].controls;
    let state = this.divisionState[divisionName];

    if (checked) {
      this.renderer.removeClass(this.el.nativeElement.querySelector(`#${this.stringHelper.escapeValue(elemId)}`), "partiallyFilledCB");
      facetItem.selected = selected || checked;
      facet.setValue(facetItem);
      if (divisionStatesControls) {
        for (let k = 0; k < divisionStatesControls.length; k++) {
          state[k].selected = true;
          divisionStatesControls[k].setValue(state[k]);
        }
      }
    } else {
      facet.setValue(checked);
      if (divisionStatesControls) {
        divisionStatesControls.forEach((control) => {
          control.setValue(checked);
        });
      }
    }
  }
  setDivisionSectionValues(obj) {
    const { statesObj = null } = obj;

    if (statesObj) {
      //If clicked on state
      this.setStatesValue({ ...statesObj, ...obj });
      return;
    }

    this.setDivisions(obj);
  }

  setFacetsExpanded() {
    // This method is to expand facets when even all the filters are removed.
    let elem;
    Object.keys(this.form.value).length > 0 &&
      Object.keys(this.form.value).forEach((key) => {
        if (key !== "divisionRogCds") {
          if (key == "Jamba Juice") {
            elem = document.getElementById("Jamba_Juice");
          } else {
            elem = document.getElementById(key);
          }
          if (!elem.classList.contains("show")) {
            delete this.facetShow[key];
          } else {
            this.facetShow[key] = true;
          }
        }
      });
    this.facetShow[this.item] = true;
  }

  onChangeStoreGroup(obj) {
    this.setFacetsExpanded();

    const { target, facet, facetItem, selected } = obj,
      { checked } = target.target;
    if (this.item == "divisions") {
      this.setDivisionSectionValues(obj);
      this.checkIfAllDivisionStatesSelected();
    } else {
      if (this.item == "banners") {
        if (checked) {
          facetItem.selected = checked;
          facet.setValue(facetItem);
        } else {
          facet.setValue(checked);
        }
        this.checkIfAllBannersSelected();
      } else {
        if (checked || selected === "yes" || selected === "no" || selected === "all") {
          facetItem.selected = selected || checked;
          facet.setValue(facetItem);
        } else {
          facet.setValue(checked);
        }
      }
    }
  }
  onSelectProgramCode(selectedItem, name, checked) {
    /*setting the programCodeSelected to the option selected which will be used across application*/
    this.facetItemServic.programCodeSelected = selectedItem.facetItem.id;
    this.facetItemServic.programCodeInitial = true;
    this.facetItemServic.programCodeChanged = true;
    this.facetItemServic.getProgramCode$.next(selectedItem.facetItem.id);

    if (name === "programCode") {
      this.facetItemServic.programFilterChange = true;
      this.facetItemServic.programCodeInitial = true;
    } else {
      if (!checked) {
        const removeFilter = this.facetItemServic.getFilterList();

        this.facetItemServic.removeFilterChip(removeFilter, this.form);
        this.facetItemServic.removeFilterSelectedFromQuery(removeFilter, this.form);
        return removeFilter.reduce((output, ele) => {
          output[ele] = "";
          return output;
        }, {});
      }
    }
  }

  resetProgramCodeStatus(name) {
    /*reset all the programcodes to false*/
    if (name === "programCode") {
      for (let key in this.facetItemServic.programCodeChecked) {
        this.facetItemServic.programCodeChecked[key] = false;
      }
      /*when program code change making searchText & indexList to default values*/
      this.facetItemServic.searchText = "";
      this.facetItemServic.indexList = [];
    }
  }
  createdAppIdPreventDefault(event, name, id, checked) {
    const checkcreatedAppId = Object.values(this.facetItemServic.createdAppIdChecked).filter((programCode) => programCode);
    
    if (name === "createdAppId" && !checked) {
      if (!checkcreatedAppId.length) {
        event.target.preventDefault();
        this.preventDefaultdAppId = true;
        this.facetItemServic.createdAppIdChecked = { ...this.facetItemServic.createdAppIdChecked, ...{ [id]: true } };
        return true;
      }
    } else {
      this.preventDefaultdAppId = false;
    }
  }

  programCodePreventDefault(event, name, id, checked) {
    const checkProgramCode = Object.values(this.facetItemServic.programCodeChecked).filter((programCode) => programCode);
    /**Setting this obbservable to identify if MF is clicked on offer mangment for using in pod filter component */
    this.facetItemServic.selectedOfferProgramCode$.next(this.facetItemServic.programCodeChecked);

    if (name === "offerProgramCd" && !checked) {
      if (!checkProgramCode.length) {
        event.target.preventDefault();
        this.preventDefault = true;
        this.facetItemServic.programCodeChecked = { ...this.facetItemServic.programCodeChecked, ...{ [id]: true } };
        return true;
      }
    } else {
      this.preventDefault = false;
    }
  }

  showProgramCdSelectionError(item) {
    /*
     params  facet item value - i.e offerProgramCd
     this function is used to display Error box with message - Must select 1+ Box when only one offerProgramCd is selected and user try to uncheck it
     */
    if(item === "createdAppId")
      return this.preventDefaultdAppId;
    return (item === "offerProgramCd") && this.preventDefault;
  }

  setFacetWraaperClassBasedOnItem(item) {
    /*
     params  facet item value - i.e programCode
     this function is used to set dynamic wrapper class on facet item content based on facet item value
     */

    switch (item) {
      case "progSubType": {
        return this.setScrollBar() ? "facet-items-list" : "";
        }
      case "eventids": {
            return this.setScrollBar() ? "facet-items-list mt-3" : "";
        }
      case "offerProgramCd":
      case "createdAppId": {
        return this.preventDefault ? "mt-2" : "";
      } 
      default: {
        return "";
      }
    }
  }

  onClick(event) {
    const {
      facetItem: { id },
      target: { target: { checked = false, name = "" } = {} } = {},
    } = event;
    if (name === "offerProgramCd") {
      this.facetItemServic.programCodeChecked = { ...this.facetItemServic.programCodeChecked, ...{ [id]: checked } };
      const preventDefault = this.programCodePreventDefault(event, name, id, checked);
      if (preventDefault) {
        return false;
      }
    }

    if (name === "createdAppId") {
      this.facetItemServic.createdAppIdChecked = { ...this.facetItemServic.createdAppIdChecked, ...{ [id]: checked } };
      const preventDefault = this.createdAppIdPreventDefault(event, name, id, checked);
      if (preventDefault) {
        return false;
      }
    }
  }
  ngOnDestroy() {
    this.facetItemServic.searchText = "";
    this.facetItemServic.indexList = [];
  }
  doHide() {
    const facetListControls = this.facetList?.controls;
    return facetListControls?.length ? false : true;
  }
  get selectAllTextToDisplay() {
      if (["bggm", "bugm", "categoryId", "regionId", "status"].includes(this.item)) {        
        const facetListControls = this.removeExpiredControlFromStatus();
        const isAllSelected =
          facetListControls?.length && facetListControls.every((control) => toString.call(control.value) === "[object Object]");
        return isAllSelected ? "Unselect All" : "Select All";
      }
      return "";
  }


  removeSpecialChars(value: any): string {
    value = value?.toString().replace(" ","_");
    value = value?.toString().replace(".","");
    return value.replace(/[^\w\s\.\-]/gi, '_');
  }

  onChange(event) {
    if (this.preventDefault) {
      return false;
    }

    
    const {
      facetItem: { id },
      target: { target: { checked = false, name = "" } = {} } = {},
    } = event;
    if (this.facetpage === "template") {
      this.facetItemServic.isBpdOfferReqSelected = true;
      const {
        target: {
          target: { checked },
        },
        facet,
        facetItem,
      } = event;
      if (checked) {
        facetItem.selected = checked;
        facet.setValue(facetItem);
      } else {
        facet.setValue(checked);
      }
      this.facetClick.emit({ form: this.form.value, event, item: this.item });
      return false;
    }
    let removeChipOnOffers = {};
    this.facetItemServic.showSearchError = false;
    this.facetItemServic.setOfferFilter("facetFilter");

    if (this.facetpage === "offerPODPage" || this.facetpage === "home" || this.facetpage === "offerHome" || this.facetpage === "template") {
      if (name === "programCode" || name === "offerProgramCd") {
        this.resetProgramCodeStatus(name);
        removeChipOnOffers = this.onSelectProgramCode(event, name, checked);
      } else {
        this.facetItemServic.programCodeChanged = false;
        this.facetItemServic.programFilterChange = false;
      }
      if (["home","offerHome"].includes(this.facetpage) && name === "programCode") {
        if (id === CONSTANTS.BPD) {
          this._searchOfferRequestService.getOfferDetails({});
          this.facetItemServic.isBpdOfferReqSelected = true;
        } else {
          this.facetItemServic.isBpdOfferReqSelected = false;
        }
        localStorage.setItem(CONSTANTS.PROGRAM_CODE_LS, id);
      }

      this.onChangeFacetHome(event);
      this._searchOfferRequestService.populateHomeFilterSearch({
        facetChip: { ...removeChipOnOffers, ...this.facetItemServic.populateFacetFieldChips(this.form.value, this.item) },
      });
    } else if (this.facetpage === "storeGroup") {
      this.onChangeStoreGroup(event);
      this.storeGroupService.populateStoreFilterSearch({
        facetChip: this.facetItemServic.populateFacetFieldChips(this.form.value, this.item),
        facetpage: this.facetpage,
      });
    }
    this.facetClick.emit({ form: this.form.value, event, item: this.item });
    this.updateOptionStates();
  }
}

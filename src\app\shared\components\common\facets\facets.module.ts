import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FacetChipComponent } from './facet-chip/facet-chip.component';
import { FacetChipsListComponent } from './facet-chips-list/facet-chips-list.component';
import { FacetItemListComponent } from './facet-item-list/facet-item-list.component';
import { FacetItemComponent } from './facet-item/facet-item.component';

import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { TypeaheadModule } from 'ngx-bootstrap/typeahead';


import { OfferReqBuilder } from '@appModules/request/core/offer-request/details/components/offer-builder/offer-builder.comp';
import { DeletePluModule } from '@appModules/request/core/plu-reservation/shared/components/modal-views/deletePlu.module';
import { RegionsMultipleCopyComponent } from '@appModules/request/shared/components/regions-multiple-copy-modal/regions-multiple-copy/regions-multiple-copy.component';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { ActionsAndMoreComponent } from '@appShared/components/common/actions-and-more/actions-and-more.component';
import { ClientSidePaginationComponent } from '@appShared/components/common/client-side-pagination/client-side-pagination.component';
import { CustomPaginationComponent } from '@appShared/components/common/custom-pagination/custom-pagination.comp';
import { EditRequestReasonModalComponent } from '@appShared/components/modals/edit-request-reason-modal/edit-request-reason.component';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { NgxPaginationModule } from 'ngx-pagination';
import { AppCommonModule } from "../../../../modules/common/app.common.module";



@NgModule({
  declarations: [
    FacetItemListComponent,
    FacetItemComponent,
    FacetChipComponent,
    FacetChipsListComponent,
    ActionsAndMoreComponent,
    CustomPaginationComponent,
    ClientSidePaginationComponent,
    OfferReqBuilder,
    EditRequestReasonModalComponent,
    RegionsMultipleCopyComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    DeletePluModule,
    ReactiveFormsModule,
    TooltipModule.forRoot(),
    TypeaheadModule.forRoot(),
    NgSelectModule,
    PermissionsModule.forChild(),
    AppCommonModule,
    NgxPaginationModule

  ],
  exports: [
    FacetItemListComponent,
    FacetItemComponent,
    FacetChipComponent,
    FacetChipsListComponent,
    CustomPaginationComponent,
    ClientSidePaginationComponent,
    ActionsAndMoreComponent,
    NgxDatatableModule,
    OfferReqBuilder,
    EditRequestReasonModalComponent,
    RegionsMultipleCopyComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: []
})
export class FacetsModule { }

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { LoaderService } from '@appServices/common/loader.service';
import { NotFoundComponent } from './not-found.component';

class MockRouter {
  getCurrentNavigation() {
    return {
      extras: {
        state: {
          resId: '123',
          resType: 'TestType'
        }
      }
    };
  }
}

class MockLoaderService {
  isDisplayLoader(value: boolean) {}
}

describe('NotFoundComponent', () => {
  let component: NotFoundComponent;
  let fixture: ComponentFixture<NotFoundComponent>;
  let mockRouter: MockRouter;
  let mockLoaderService: MockLoaderService;

  beforeEach(async () => {
    mockRouter = new MockRouter();
    mockLoaderService = new MockLoaderService();

    await TestBed.configureTestingModule({
      declarations: [NotFoundComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: LoaderService, useValue: mockLoaderService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NotFoundComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set responseText to "resource" if navigation state is not available', () => {
    spyOn(mockRouter, 'getCurrentNavigation').and.returnValue(null);
    component.ngOnInit();
    expect(component.responseText).toBe('resource');
  });

  it('should set resId, resType, and responseText based on navigation state', () => {
    component.ngOnInit();
    expect(component.resId).toBe('123');
    expect(component.resType).toBe('TestType');
    expect(component.responseText).toBe('TestType (123)');
  });

  it('should call loaderService.isDisplayLoader with false', () => {
    spyOn(mockLoaderService, 'isDisplayLoader');
    component.ngOnInit();
    expect(mockLoaderService.isDisplayLoader).toHaveBeenCalledWith(false);
  });
});
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { LoaderService } from '@appServices/common/loader.service';
@Component({
  selector: 'app-not-found',
  templateUrl: './not-found.component.html',
  styleUrl: './not-found.component.scss'
})
export class NotFoundComponent {
  resId: string = '';
  resType: string = '';
  responseText: string = '';
  constructor(private route: Router, private loaderService: LoaderService) {
    // intentionally left empty
  }
  ngOnInit() {
    const navigationState = this.route.getCurrentNavigation()?.extras.state;
    this.responseText = 'resource'
    if (navigationState) {
      this.resId = navigationState.resId;
      this.resType = navigationState.resType;
      this.responseText = `${this.resType} (${this.resId})`;
    }
    this.loaderService.isDisplayLoader(false);
  }
}

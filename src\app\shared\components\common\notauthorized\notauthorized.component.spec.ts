import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotauthorizedComponent } from './notauthorized.component';
import { LoaderService } from '@appServices/common/loader.service';

class MockLoaderService {
    isDisplayLoader(value: boolean) {}
}

describe('NotauthorizedComponent', () => {
    let component: NotauthorizedComponent;
    let fixture: ComponentFixture<NotauthorizedComponent>;
    let loaderService: LoaderService;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [NotauthorizedComponent],
            providers: [{ provide: LoaderService, useClass: MockLoaderService }]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(NotauthorizedComponent);
        component = fixture.componentInstance;
        loaderService = TestBed.inject(LoaderService);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call isDisplayLoader with false on ngOnInit', () => {
        spyOn(loaderService, 'isDisplayLoader');
        component.ngOnInit();
        expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(false);
    });
});
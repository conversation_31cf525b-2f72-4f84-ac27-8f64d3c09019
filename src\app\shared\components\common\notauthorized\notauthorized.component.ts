import { Component, OnInit } from '@angular/core';
import { LoaderService } from '@appServices/common/loader.service';


@Component({
  selector: 'app-not-authorized',
  templateUrl: './notauthorized.component.html'
})

export class NotauthorizedComponent implements OnInit {

  constructor(private loaderService: LoaderService) {
    // intentionally left empty
  }

  ngOnInit() {
    this.loaderService.isDisplayLoader(false);
  }
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SpinnerComponent } from './spinner.component';
import { LoaderService } from '@appServices/common/loader.service';
import { BehaviorSubject, throwError } from 'rxjs';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '@angular/core';

describe('SpinnerComponent', () => {
  let component: SpinnerComponent;
  let fixture: ComponentFixture<SpinnerComponent>;
  let loaderService: LoaderService;
  let errorHandler: ErrorHandler;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SpinnerComponent],
      providers: [
        { provide: LoaderService, useClass: LoaderService },
        ErrorHandler
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SpinnerComponent);
    component = fixture.componentInstance;
    loaderService = TestBed.inject(LoaderService) as unknown as LoaderService;
    errorHandler = TestBed.inject(Error<PERSON>andler);

    loaderService.loaderInstance = new BehaviorSubject<boolean>(false) as any;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should have isDisplayLoader initially false', () => {
    expect(component.isDisplayLoader).toBeFalse();
  });

  it('should subscribe to loaderInstance and update isDisplayLoader on emission', () => {
    component.ngOnInit();

    expect(component.isDisplayLoader).toBeFalse();

    (loaderService.loaderInstance as BehaviorSubject<boolean>).next(true);
    expect(component.isDisplayLoader).toBeTrue();

    (loaderService.loaderInstance as BehaviorSubject<boolean>).next(false);
    expect(component.isDisplayLoader).toBeFalse();
  });

  it('should handle a null emission gracefully', () => {
    component.ngOnInit();
    (loaderService.loaderInstance as BehaviorSubject<boolean | null>).next(null);
    expect(component.isDisplayLoader).toBeNull();
  });

  it('should throw an error if loaderInstance is undefined', () => {
    loaderService.loaderInstance = undefined as any;
    expect(() => component.ngOnInit()).toThrow();
  });
});

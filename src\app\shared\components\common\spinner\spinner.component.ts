import { Component, OnInit } from '@angular/core';
import { LoaderService } from '@appServices/common/loader.service';
@Component({
  selector: 'spinner',
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.scss']
})
export class SpinnerComponent implements OnInit {
  isDisplayLoader: boolean = false;
  constructor(private loaderService: LoaderService) {
    // intentionally left empty
   }

  ngOnInit() {
    this.loaderService.loaderInstance.subscribe(item => {
      this.isDisplayLoader = item;
    })
  }

}

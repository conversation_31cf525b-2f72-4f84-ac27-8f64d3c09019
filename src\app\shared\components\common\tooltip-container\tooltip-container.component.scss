
:host ::ng-deep{
    //this class is for the inner content of the popover - tool tip
    .tooltip-inner {
        background-color: #f0f0f0;
        color: black;
        border: 1px solid #9f9f9f;
        border-radius: 0px;
        max-width: 439px !important;
        white-space: pre-line;
        text-align: left;
        line-height: 16px;
    }
    //:before is the Top triangle block level
    //:after is the bottom triangle for the border effect
    //this block is for the Top Placement
    .bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^="top"] .arrow::before  {
        position: absolute;
        display: inline-block;
        border-top: 6px solid #f1f1f1;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid transparent;
        left: 3px;
        top: 0px;
        content: '';
        z-index: 1000;
    }
    .bs-tooltip-top .arrow::after, .bs-tooltip-auto[x-placement^="top"] .arrow::after  {
        position: absolute;
        display: inline-block;
        border-top: 7px solid #a8a8a8;
        border-right: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-left: 7px solid transparent;
        left: 2px;
        top: 1px;
        content: '';
    }
    .bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^="top"] .arrow {
        bottom: 1px;
    }

    //this block is for the Bottom Placement
    .bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^="bottom"] .arrow::before  {
        position: absolute;
        display: inline-block;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid #f1f1f1;
        border-left: 6px solid transparent;
        left: 1px;
        top: -4px;
        content: '';
        z-index: 1000;
    }
    .bs-tooltip-bottom .arrow::after, .bs-tooltip-auto[x-placement^="bottom"] .arrow::after  {
        position: absolute;
        display: inline-block;
        border-top: 7px solid transparent;
        border-right: 7px solid transparent;
        border-bottom: 7px solid #a8a8a8;
        border-left: 7px solid transparent;
        left: 0px;
        top: -7px;
        content: '';
    }
    .bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^="bottom"] .arrow {
        bottom: 1px;
    }
    
    //this block is for the Left Placement
    .bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^="left"] .arrow::before  {
        position: absolute;
        display: inline-block;
        border-top: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid #f1f1f1;
        left: -2px;
        top: -5px;
        content: '';
        z-index: 1000;
    }
    .bs-tooltip-left .arrow::after, .bs-tooltip-auto[x-placement^="left"] .arrow::after  {
        position: absolute;
        display: inline-block;
        border-top: 7px solid transparent;
        border-right: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-left: 7px solid #a8a8a8;
        left: -1px;
        top: -6px;
        content: '';
    }
    .bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^="left"] .arrow {
        bottom: 1px;
    }

    //this block is for the Right Placement
    .bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^="right"] .arrow::before  {
        position: absolute;
        display: inline-block;
        border-top: 6px solid transparent;
        border-left: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-right: 6px solid #f1f1f1;
        left: -4px;
        top: -5px;
        content: '';
        z-index: 1000;
    }
    .bs-tooltip-right .arrow::after, .bs-tooltip-auto[x-placement^="right"] .arrow::after  {
        position: absolute;
        display: inline-block;
        border-top: 7px solid transparent;
        border-left: 7px solid transparent;
        border-bottom: 7px solid transparent;
        border-right: 7px solid #a8a8a8;
        left: -7px;
        top: -6px;
        content: '';
    }
    .bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^="right"] .arrow {
        bottom: 1px;
    }
    .bs-tooltip-right {
        .tooltip-inner {
            background-color: #f0f0f0;
            color: black;
            border: 1px solid #9f9f9f;
            border-radius: 0px;
            max-width: 439px !important;
            min-width: 200px !important;
            margin-left: 0px;
        }
    }
    
}
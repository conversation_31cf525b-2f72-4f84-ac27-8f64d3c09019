<div class="addAllocationWrapper py-4">
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <spinner *ngIf="loading"></spinner>
      <div class="row">
        <api-errors class="col-12"></api-errors>
      </div>
      <div class="row position-relative p-3">
        <img src="assets/icons/grey-close-icon.svg" class="mb-1 position-absolute close-icon cursor-pointer"
          alt="close" (click)="modalRef.hide()" />
      </div>
    </div>
  </div>
  <div class="modal-body pt-0 pr-5 pl-5" [formGroup]="allocationForm">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <h2 class="pl-4">Add Allocation Code</h2>
        </div>
      </div>
      <div class="row mx-1 justify-content-left my-3">
        <div class="col-12">
          <div class="row mb-4">
            <div class="col-12">
              <label class="font-weight-bold">Allocation Code</label>
            </div>
            <div class="col-12">
              <input markAsTouchedOnFocus maxlength="2" [OnlyNumber]="true" type="text" formControlName="allocationCode"
                class="form-control">
            </div>
          </div>
          <div class="row mb-4">
            <div class="col-12">
              <label class="font-weight-bold">Allocation Code Name</label>
            </div>
            <div class="col-12">
              <input markAsTouchedOnFocus type="text" formControlName="allocationCodeName" class="form-control">
            </div>
          </div>
        </div>
      </div>
      <div class="row col mt-4 d-flex justify-content-end p-0">
        <span class="cursor-pointer mr-4 anchor-link-blue align-self-center" aria-label="Close"
          (click)="modalRef.hide()"><u>Cancel</u></span>
        <button type="button" [disabled]="!allocationForm.valid" class="btn btn-primary font-weight-bolder w-25"
          aria-label="Close" (click)="saveAllocationForm()">
          Save
        </button>

      </div>
    </div>
  </div>
</div>
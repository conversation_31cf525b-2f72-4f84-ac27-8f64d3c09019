import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddAllocationComponent } from './add-allocation.component';
import { UntypedFormControl, UntypedFormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { CommonService } from '@appServices/common/common.service';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { of, BehaviorSubject } from 'rxjs';
import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';
import { OnlyNumberDirective } from '@appDirectives/only-number/only-number.directive';

describe('AddAllocationComponent', () => {
  let component: AddAllocationComponent;
  let fixture: ComponentFixture<AddAllocationComponent>;
  let mockCommonService: jasmine.SpyObj<CommonService>;
  let mockRequestFormService: jasmine.SpyObj<RequestFormService>;
  let mockModalRef: jasmine.SpyObj<BsModalRef>;

  beforeEach(async () => {
    mockCommonService = jasmine.createSpyObj('CommonService', ['saveAllocation']);
    mockRequestFormService = jasmine.createSpyObj('RequestFormService', [], {
      hideApiErrorOnCreateRequest$: new BehaviorSubject<boolean>(false)
    });
    mockModalRef = jasmine.createSpyObj('BsModalRef', ['hide']);

    await TestBed.configureTestingModule({
      declarations: [AddAllocationComponent, OnlyNumberDirective],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: CommonService, useValue: mockCommonService },
        { provide: RequestFormService, useValue: mockRequestFormService },
        { provide: BsModalRef, useValue: mockModalRef },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddAllocationComponent);
    component = fixture.componentInstance;
    component.modalRef = mockModalRef;
    component.onSaveAllocation = new EventEmitter();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the allocation form on ngOnInit', () => {
    component.ngOnInit();
    expect(component.allocationForm).toBeTruthy();
    expect(component.allocationForm.controls['allocationCode']).toBeDefined();
    expect(component.allocationForm.controls['allocationCodeName']).toBeDefined();
  });

  it('should add leading zero if allocationCode is less than 10', () => {
    const value = { allocationCode: '5' };
    spyOn(value.allocationCode, 'padStart').and.returnValue('05');
    component.addZeroesBeforeNo(value);
    expect(value.allocationCode).toBe('05');
  });

  it('should call saveAllocation API and emit event on successful save', () => {
    component.ngOnInit();
    component.allocationForm.setValue({ allocationCode: '5', allocationCodeName: 'Test' });
    
    const mockResponse = { id: 1, allocationCode: '05', allocationCodeName: 'Test' };
    mockCommonService.saveAllocation.and.returnValue(of(mockResponse));

    spyOn(component.onSaveAllocation, 'emit');

    component.saveAllocationForm();
    
    expect(mockCommonService.saveAllocation).toHaveBeenCalledWith({ allocationCode: '05', allocationCodeName: 'Test' });
    expect(component.onSaveAllocation.emit).toHaveBeenCalledWith({ allocationData: mockResponse, newAllocation: { allocationCode: '05', allocationCodeName: 'Test' } });
    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should set hideApiErrorOnCreateRequest$ to false on ngOnDestroy', () => {
    mockRequestFormService.hideApiErrorOnCreateRequest$ = new BehaviorSubject<boolean>(true);
    spyOn(mockRequestFormService.hideApiErrorOnCreateRequest$, 'next');

    component.ngOnDestroy();
    expect(mockRequestFormService.hideApiErrorOnCreateRequest$.next).toHaveBeenCalledWith(false);
  });
});
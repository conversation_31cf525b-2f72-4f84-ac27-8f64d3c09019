
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { CommonService } from '@appServices/common/common.service';
import { BsModalRef } from 'ngx-bootstrap/modal';




@Component({
  selector: "add-allocation-modal",
  templateUrl: './add-allocation.component.html',
  styleUrls: ['./add-allocation.component.scss']
})
export class AddAllocationComponent  {

  @Input() modalRef: BsModalRef;
  @Output() onSaveAllocation = new EventEmitter();

  allocationForm: UntypedFormGroup;
  loading:boolean = false;

  constructor(private commonService: CommonService,
    private requestFormService: RequestFormService) {
      // intentionally left empty
  }
  /**
   * On load, set the allocation form
   */
  ngOnInit(): void {
    this.allocationForm = new UntypedFormGroup({
      allocationCode: new UntypedFormControl(null, Validators.required),
      allocationCodeName: new UntypedFormControl(null, Validators.required)
    })
  }
  /**
   * 
   * @param newAllocationValue 
   * When Allocation code is less than 10, need to append zero in front
   */
  addZeroesBeforeNo(newAllocationValue) {
    if(newAllocationValue?.allocationCode) {
      newAllocationValue.allocationCode = newAllocationValue.allocationCode < 10 ? newAllocationValue?.allocationCode?.padStart(2,0) : newAllocationValue.allocationCode;
    }
  }
  /**
   * On click save, if allocation form is valid, need to do api call to save allocation details
   */
  saveAllocationForm() {
    this.loading = true;
    const newAllocationValue = this.allocationForm?.value;
    if(this.allocationForm?.valid) {
      this.commonService.saveAllocation(newAllocationValue).subscribe({
        next: (data) => {
        if(data) {
          this.addZeroesBeforeNo(newAllocationValue);
          this.loading = false;
          this.onSaveAllocation.emit({allocationData: data, newAllocation :newAllocationValue});
          this.modalRef.hide();
        }
      },
      error: () => {
        // On error we need to hide api error behind the popup
        this.loading = false;
        this.requestFormService?.hideApiErrorOnCreateRequest$?.next(true);
      }})
    }
  }
/**
 * When popup close, we should see enable to see the api error behind popup
 */
  ngOnDestroy(): void {
    this.requestFormService?.hideApiErrorOnCreateRequest$?.next(false);
  }
}
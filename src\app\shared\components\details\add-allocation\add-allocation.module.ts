// Angular Imports
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { OnlyNumberDirectiveModule } from '@appDirectives/only-number/only.number.module';
import { AppCommonModule } from '@appModules/common/app.common.module';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { AddAllocationComponent } from './add-allocation.component';


@NgModule({
    imports: [
        CommonModule,
        OnlyNumberDirectiveModule,
        ApiErrorsModule,
        AppCommonModule,
        ReactiveFormsModule
    ],
    declarations: [
        AddAllocationComponent,
    ],
    exports: [
        AddAllocationComponent,
    ]
})
export class AddAllocationModule{

}

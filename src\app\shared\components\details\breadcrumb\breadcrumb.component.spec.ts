import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { BreadcrumbComponent } from './breadcrumb.component';
import { BreadcrumbServiceService } from '@appServices/common/breadcrumb-service.service';

describe('BreadcrumbComponent', () => {
  let component: BreadcrumbComponent;
  let fixture: ComponentFixture<BreadcrumbComponent>;
  let breadcrumbService: BreadcrumbServiceService;
  let router: Router;

  beforeEach(async () => {
    const breadcrumbServiceMock = {
      breadCrumListSource: of(['Home', 'Products', 'Details'])
    };

    const routerMock = {
      navigateByUrl: jasmine.createSpy('navigateByUrl')
    };

    await TestBed.configureTestingModule({
      declarations: [ BreadcrumbComponent ],
      providers: [
        { provide: BreadcrumbServiceService, useValue: breadcrumbServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BreadcrumbComponent);
    component = fixture.componentInstance;
    breadcrumbService = TestBed.inject(BreadcrumbServiceService);
    router = TestBed.inject(Router);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize breadcrumbList on ngOnInit', () => {
    component.ngOnInit();
    expect(component.breadcrumbList).toEqual(['Home', 'Products', 'Details']);
  });

  it('should navigate to the correct route on onClick', () => {
    const route = '/test-route';
    component.onClick(route);
    expect(router.navigateByUrl).toHaveBeenCalledWith(route);
  });
});
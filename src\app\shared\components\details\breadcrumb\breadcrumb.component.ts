import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BreadcrumbServiceService } from '@appServices/common/breadcrumb-service.service';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent implements OnInit {
  breadcrumbList;
  constructor(private router: Router, private breadcrumbService: BreadcrumbServiceService) {
    // intentionally left empty
  }

  ngOnInit() {
    this.breadcrumbService.breadCrumListSource.subscribe((list: any) => {
      if (list) {
        this.breadcrumbList = list;
      }
    })
  }
  onClick(route) {
    this.router.navigateByUrl(route);
  }

}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DisplayMessageComponent } from './display-message.component';
import { StringUtilsHelperService } from '@appServices/common/string-utils.helper.service';

describe('DisplayMessageComponent', () => {
    let component: DisplayMessageComponent;
    let fixture: ComponentFixture<DisplayMessageComponent>;
    let stringHelperService: jasmine.SpyObj<StringUtilsHelperService>;

    beforeEach(async () => {
        const spy = jasmine.createSpyObj('StringUtilsHelperService', ['setActionMessages']);

        await TestBed.configureTestingModule({
            declarations: [ DisplayMessageComponent ],
            providers: [
                { provide: StringUtilsHelperService, useValue: spy }
            ]
        })
        .compileComponents();

        fixture = TestBed.createComponent(DisplayMessageComponent);
        component = fixture.componentInstance;
        stringHelperService = TestBed.inject(StringUtilsHelperService) as jasmine.SpyObj<StringUtilsHelperService>;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call displayMessage on ngOnInit', () => {
        spyOn(component, 'displayMessage');
        component.ngOnInit();
        expect(component.displayMessage).toHaveBeenCalled();
    });

    it('should call displayMessage on ngOnChanges', () => {
        spyOn(component, 'displayMessage');
        component.ngOnChanges();
        expect(component.displayMessage).toHaveBeenCalled();
    });

    it('should call setActionMessages if displayObject is defined', () => {
        component.displayObject = { message: 'Test message' };
        component.displayMessage();
        expect(stringHelperService.setActionMessages).toHaveBeenCalledWith(component.displayObject, false);
    });

    it('should not call setActionMessages if displayObject is undefined', () => {
        component.displayObject = undefined;
        component.displayMessage();
        expect(stringHelperService.setActionMessages).not.toHaveBeenCalled();
    });
});
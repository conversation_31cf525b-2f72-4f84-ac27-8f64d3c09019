import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { StringUtilsHelperService } from '@appServices/common/string-utils.helper.service';

@Component({
  selector: 'display-message',
  templateUrl: './display-message.component.html'
})
export class DisplayMessageComponent implements OnInit, OnChanges {
  ngOnChanges(): void {
    this.displayMessage();
  }
  @Input() displayObject;
  constructor(private stringHelper: StringUtilsHelperService) {
    // intentionally left empty
  }

  ngOnInit() {
    this.displayMessage();
  }
  displayMessage() {
    if (this.displayObject) {
      this.stringHelper.setActionMessages(this.displayObject, false);
    }
  }

}

<ng-container *ngIf="fieldProperty && property && formGroupName">
  <div  *ngIf="!summary; else summaryField" [formGroup]="formGroupName" class="typeahead">
    <label class="font-weight-bold" for="formControl">{{label}}</label>    
    <input  [readonly] = "isFieldDisabled" class="form-control search-background"
        [ngClass]="isFieldDisabled ? 'bg-color' : ''"
        id="formControl" name="formControl"  [formControlName]="property"  [typeahead]="typeAheadData" oninput="this.value = this.value.toUpperCase()" />
      <div app-show-field-error [property]= "property"></div>
  </div>
  <ng-template #summaryField>
    <app-input-display-component [label]="label" [value]= "formControl?.value">
    </app-input-display-component>
  </ng-template>
</ng-container>
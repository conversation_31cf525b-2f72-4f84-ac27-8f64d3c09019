import { ComponentFix<PERSON>, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { InputAutocompleteComponent } from './input-autocomplete-component.component';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { NO_ERRORS_SCHEMA, Component, Input } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { of, throwError } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';
import { OFFER_REQUEST_CREATE_RULES } from '@appRequest/shared/rules/create.rules';

@Component({
  selector: 'app-input-display-component',
  template: '<div>Mock Display Component</div>'
})
class MockInputDisplayComponent {
  @Input() label: string;
  @Input() value: any;
}

describe('InputAutocompleteComponent', () => {
  let component: InputAutocompleteComponent;
  let fixture: ComponentFixture<InputAutocompleteComponent>;
  let routerMock: any;
  let offerRequestBaseServiceMock: any;
  let offerTemplateBaseServiceMock: any;
  let commonRouteServiceMock: any;
  let testForm: UntypedFormGroup;

  beforeEach(() => {
    testForm = new UntypedFormGroup({
      testProperty: new UntypedFormControl('TEST VALUE'),
      programType: new UntypedFormControl('TYPE1'),
      programSubType: new UntypedFormControl('')
    });

    routerMock = {
      url: '/test/create',
      events: of(new NavigationEnd(1, '/test', '/test'))
    };

    offerRequestBaseServiceMock = {
      initialDataService$: {
        getAppData: () => ({
          programTypes: ['TYPE1', 'TYPE2']
        })
      },
      facetItemService$: {
        programCodeSelected: CONSTANTS.SPD
      },
      requestForm: testForm,
      getFieldErrors: jasmine.createSpy('getFieldErrors').and.returnValue(null),
      getSubType: jasmine.createSpy('getSubType').and.returnValue(of(['SUBTYPE1', 'SUBTYPE2'])),
      programTypeChangedValue$: of('TYPE1')
    };

    offerTemplateBaseServiceMock = {
      templateForm: new UntypedFormGroup({}),
      programTypeChangedValue$: of('TYPE1')
    };

    commonRouteServiceMock = {
      currentActivatedRoute: 'test'
    };

    const appInjectorMock = {
      get: (service: any) => {
        if (service === Router) return routerMock;
        if (service === OfferRequestBaseService) return offerRequestBaseServiceMock;
        if (service === OfferTemplateBaseService) return offerTemplateBaseServiceMock;
        if (service === CommonRouteService) return commonRouteServiceMock;
        return null;
      }
    };

    spyOn(AppInjector, 'getInjector').and.returnValue(appInjectorMock);

    TestBed.configureTestingModule({
      declarations: [
        InputAutocompleteComponent,
        MockInputDisplayComponent
      ],
      imports: [
        FormsModule,
        ReactiveFormsModule
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });

    fixture = TestBed.createComponent(InputAutocompleteComponent);
    component = fixture.componentInstance;

    component.property = 'testProperty';
    component.section = 'testSection';
    component.form = testForm;
    component.fieldProperty = {
      testProperty: {
        appDataOptions: 'testOptions',
        label: 'Test Label',
        control: ['testProperty']
      },
      programSubType: {
        appDataOptions: 'programSubTypeListAPI',
        label: 'Program Sub Type',
        control: ['programSubType']
      }
    };
    component.propertyValues = {
      label: 'Test Label'
    };

    component.offerRequestBaseService$ = offerRequestBaseServiceMock;
    component.offerTemplateBaseService$ = offerTemplateBaseServiceMock;
    component.router = routerMock;
    component.commonRouteService = commonRouteServiceMock;

    spyOn(component, 'getFormControl').and.callFake(() => {
      if (component.property === 'testProperty') {
        return testForm.get('testProperty');
      } else if (component.property === 'programSubType') {
        return testForm.get('programSubType');
      }
      return null;
    });
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should handle ngOnChanges when fieldProperty is not set', () => {
    component.fieldProperty = null;
    component.ngOnChanges();
    expect(component.fieldProperty).not.toBeNull();
  });

  it('should handle ngOnInit for regular field', () => {
    spyOn(component, 'setFormControlValue');
    component.ngOnInit();
    expect(component.setFormControlValue).toHaveBeenCalled();
    expect(component.isFieldDisabled).toBeFalse();
  });

  it('should handle ngOnInit for programSubType field', () => {
    component.property = 'programSubType';
    spyOn(component, 'initSubscriberForProgramSubType');
    component.ngOnInit();
    expect(component.isFieldDisabled).toBeTrue();
    expect(component.initSubscriberForProgramSubType).toHaveBeenCalled();
  });

  it('should initialize subscriber for program sub type', fakeAsync(() => {
    component.property = 'programSubType';
    spyOn(component, 'getProgramsubtypeList');
    component.initSubscriberForProgramSubType();
    tick();
    expect(component.isFieldDisabled).toBeFalse();
    expect(component.getProgramsubtypeList).toHaveBeenCalledWith('TYPE1');
  }));

  it('should get program subtype list', fakeAsync(() => {
    component.getProgramsubtypeList('TYPE1');
    tick();
    expect(offerRequestBaseServiceMock.getSubType).toHaveBeenCalledWith('TYPE1');
    expect(component.typeAheadData).toEqual(['SUBTYPE1', 'SUBTYPE2']);
  }));

  it('should not get program subtype list when programType is falsy', () => {
    component.getProgramsubtypeList(null);
    expect(offerRequestBaseServiceMock.getSubType).not.toHaveBeenCalled();
  });

  it('should handle subscription in getProgramsubtypeList', () => {
    const mockSubscription = { unsubscribe: jasmine.createSpy('unsubscribe') };
    offerRequestBaseServiceMock.getSubType.and.returnValue({
      subscribe: () => mockSubscription
    });
    component.getProgramsubtypeList('TYPE1');
    expect(offerRequestBaseServiceMock.getSubType).toHaveBeenCalledWith('TYPE1');
  });

  it('should set form control value correctly', () => {
    component.setFormControlValue();
    expect(component.getFormControl).toHaveBeenCalled();
  });

  it('should unsubscribe on ngOnDestroy', () => {
    spyOn(component.subs, 'unsubscribe');
    component.ngOnDestroy();
    expect(component.subs.unsubscribe).toHaveBeenCalled();
  });

  it('should have access to OFFER_REQUEST_CREATE_RULES.SPD', () => {
    expect(component.spd_rule).toBe(OFFER_REQUEST_CREATE_RULES.SPD);
  });

  it('should get serviceBasedOnRoute correctly for non-template route', () => {
    expect(component.serviceBasedOnRoute).toBe(offerRequestBaseServiceMock);
  });

  it('should get serviceBasedOnRoute correctly for template route', () => {
    commonRouteServiceMock.currentActivatedRoute = 'template';
    expect(component.serviceBasedOnRoute).toBe(offerTemplateBaseServiceMock);
  });

  it('should get formControl correctly', () => {
    const result = component.formControl;
    expect(component.getFormControl).toHaveBeenCalled();
    expect(result).toBe(testForm.get('testProperty'));
  });

  it('should set component properties correctly', () => {
    component.fieldProperty = null;
    component.setComponentProperties();
    expect(component.fieldProperty).not.toBeNull();
  });

  it('should handle form initialization correctly', () => {
    component.form = null;
    component.setComponentProperties();
    expect(component.form).toBe(offerRequestBaseServiceMock.requestForm);
  });

  it('should set typeAheadData correctly', () => {
    component.typeAheadData = ['ITEM1', 'ITEM2'];
    expect(component.typeAheadData).toEqual(['ITEM1', 'ITEM2']);
  });

  it('should handle isFieldDisabled flag', () => {
    component.isFieldDisabled = true;
    expect(component.isFieldDisabled).toBeTrue();

    component.isFieldDisabled = false;
    expect(component.isFieldDisabled).toBeFalse();
  });

  it('should handle programSubType field with programSubTypeListAPI', () => {
    component.property = 'programSubType';
    component.fieldProperty.programSubType.appDataOptions = 'programSubTypeListAPI';
    spyOn(component, 'initSubscriberForProgramSubType');
    component.ngOnInit();
    expect(component.initSubscriberForProgramSubType).toHaveBeenCalled();
  });
});

import { Component } from '@angular/core';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { OFFER_REQUEST_CREATE_RULES } from '@appRequest/shared/rules/create.rules';
@Component({
  selector: '[app-input-autocomplete-component]',
  templateUrl: './input-autocomplete-component.component.html',
  styleUrls: ['./input-autocomplete-component.component.scss']
})
export class InputAutocompleteComponent extends BaseFieldComponentComponent {
  isFieldDisabled: boolean = false;
  typeAheadData:any;
  spd_rule = OFFER_REQUEST_CREATE_RULES.SPD;

  constructor() { 
    super();
  }

  ngOnChanges(): void {
    if (!this.fieldProperty) {
      this.setComponentProperties();
    }
  }

  ngOnInit(){
    if (this.fieldProperty && this.property) {
      this.setFormControlValue();
      const { appDataOptions } = this.fieldProperty[this.property];
      if(appDataOptions == "programSubTypeListAPI" && !this.summary) {
        this.isFieldDisabled = true;
        this.initSubscriberForProgramSubType();
      } 
    }
  }

  initSubscriberForProgramSubType(){
    this.subs.sink = this.serviceBasedOnRoute.programTypeChangedValue$.subscribe((value)=>{
      if(value){
        this.isFieldDisabled = false;
        this.getProgramsubtypeList(value)
      }
    })
  }

  getProgramsubtypeList(programType){
    if(programType){
      this.subs.sink = this.offerRequestBaseService$.getSubType(programType).subscribe((data: any) => {
        if(data){
          this.typeAheadData = data;
        }
      });
    }
   }

   setFormControlValue() {
    if (this.formControl?.value) {
      this.formControl.setValue(this.formControl?.value);
    }
  }

  ngOnDestroy() {
    this.subs?.unsubscribe();
  }

}
<ng-container *ngIf="fieldProperty && property && formGroupName">
    <form *ngIf="!summary && !isOnlyDisplayField; else summaryField" [formGroup]="formGroupName">
        <label class="font-weight-bold" for="formControl">{{label}} </label><br>
        <div *ngIf="iterateArrayForCheckBox; else renderNormalCheckBox">
            <div formGroupName="{{property}}">
                <div *ngFor="let item of frmCtrlsList.value | keyvalue: setOriginalOrder">
                    <label>
                        <input type="checkbox" [formControlName]="item.key" [value]="item.value" /> {{
                        getDisplayValue(item.key) }}
                    </label>
                </div>
            </div>
        </div>
        <ng-template #renderNormalCheckBox>
            <ng-container *ngIf="tristateCheck; else normalCheckbox">
                <label>
                    <input type="checkbox" [formControlName]="property" (change)="onChecked($event.target.checked)"
                        (checked)="checked" /> {{label }}
                </label>
            </ng-container>
        </ng-template>
        <ng-template #normalCheckbox>
            <label>
                <input type="checkbox" [formControlName]="property" (change)="onChecked($event.target.checked)"
                    [ngModel]="isChecked" /> {{label }}
            </label>
        </ng-template>

        <div app-show-field-error [property]="property" [(onTargetValues)]="onTargetValues"></div>
    </form>
    <ng-template #summaryField>
        <app-input-display-component [label]="label" [value]="summaryValue" [section]="section">
        </app-input-display-component>
    </ng-template>
</ng-container>
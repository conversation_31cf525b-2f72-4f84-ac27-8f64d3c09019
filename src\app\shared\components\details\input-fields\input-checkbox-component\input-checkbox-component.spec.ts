
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormControl } from '@angular/forms';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { Router } from '@angular/router';
import { AppInjector } from '@appServices/common/app.injector.service';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { InputCheckboxComponent } from './input-checkbox-component';

// Mocks
const mockFormControl = new UntypedFormControl(true);  // Default checkbox checked value
const mockForm = {
  get: jasmine.createSpy('get').and.returnValue(mockFormControl),
};

const mockOfferRequestBaseService = {
  getFieldErrors: jasmine.createSpy().and.returnValue(['Error']),
  requestForm: mockForm,
  facetItemService$: { programCodeSelected: 'SPD' },
  initialDataService$: {
    getAppData: jasmine.createSpy().and.returnValue({ reviewFlags: { key: 'someData' } })
  }
};

const mockCommonRouteService = {
  currentActivatedRoute: 'request'
};

const mockRouter = {
  url: '/create',
  events: of({ url: '/create' })
};

describe('InputCheckboxComponent', () => {
  let component: InputCheckboxComponent;
  let fixture: ComponentFixture<InputCheckboxComponent>;

  beforeAll(() => {
    spyOn(AppInjector, 'getInjector').and.returnValue({
      get: (token: any) => {
        if (token === Router) return mockRouter;
        if (token === CommonRouteService) return mockCommonRouteService;
        if (token === OfferRequestBaseService) return mockOfferRequestBaseService;
        return null;
      }
    });
  });

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [InputCheckboxComponent],
      providers: [],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(InputCheckboxComponent);
    component = fixture.componentInstance;

    component.property = 'someField';
    component.section = 'SPD';
    component.form = mockForm;
    component.fieldProperty = {
      someField: {
        appDataOptions: 'reviewFlags'
      }
    };
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should call setFormControlValue on ngOnInit', () => {
    spyOn(component as any, 'setFormControlValue');
    component.ngOnInit();
    expect((component as any).setFormControlValue).toHaveBeenCalled();
  });

  it('should call setComponentProperties if fieldProperty is not defined on ngOnChanges', () => {
    component.fieldProperty = undefined;
    spyOn(component, 'setComponentProperties');
  
    component.ngOnChanges();
  
    expect(component.setComponentProperties).toHaveBeenCalled();
  });

  it('should return correct display value for reviewFlags key', () => {
    component.property = 'reviewFlags';
    // this.appData["reviewFlags"][key]
    component.appData = {reviewFlags: { key: 'someData' }};
    const result = component.getDisplayValue('key');
    expect(result).toBe('someData');
  });

  it('should update checked value when onChecked is called', () => {
    spyOn(component, 'onChecked').and.callThrough();
    component.propertyValues = { checked: false };
    component.onChecked(false);
    expect(component.propertyValues.checked).toBe(false);
  });

  it('should return the checked status from isChecked getter', () => {
    component.propertyValues = { checked: true };
    expect(component.isChecked).toBe(true);
  });

  it('should call setFormControlValue when ngOnInit is triggered', () => {
    spyOn(component as any, 'setFormControlValue');
    component.ngOnInit();
    expect((component as any).setFormControlValue).toHaveBeenCalled();
  });

  // it('should call setComponentProperties on ngOnInit if fieldProperty is undefined', () => {
  //   component.fieldProperty = undefined;
  //   spyOn(component, 'setComponentProperties');
  //   component.ngOnInit();
  //   expect(component.setComponentProperties).toHaveBeenCalled();
  // });

  // it('should unsubscribe and clear formControl on ngOnDestroy', () => {
  //   const formControl = new UntypedFormControl(true);
  //   const setValueSpy = spyOn(formControl, 'setValue');
  //   component.propertyValues = { checked: true };
  
  //   component.property = 'someField';
  //   component.form = {
  //     get: jasmine.createSpy('get').and.returnValue(formControl)
  //   } as any;
  
  //   spyOn(component.subs, 'unsubscribe');
  
  //   component.ngOnDestroy();
  
  //   expect(formControl.value).toBeNull();
  // });

  it('should return true if property is "reviewFlags"', () => {
    component.property = 'reviewFlags';
    expect(component.iterateArrayForCheckBox).toBeTrue();
  });
  
  it('should return false if property is not "reviewFlags"', () => {
    component.property = 'otherProperty';
    expect(component.iterateArrayForCheckBox).toBeFalse();
  });

  it('should update formControl and propertyValues.checked when formControl value exists', () => {
    const formControl = new UntypedFormControl(true);  // Set some initial value
    component.form = {
      get: jasmine.createSpy('get').and.returnValue(formControl)
    } as any;
    spyOn(component.formControl, 'setValue');
    component.propertyValues = { checked: undefined };
    component.setFormControlValue();
    expect(component.formControl.setValue).toHaveBeenCalledWith(true);
    expect(component.propertyValues.checked).toBe(true);
  });
  
  it('should not update formControl or propertyValues.checked if formControl value is not present', () => {
    const formControl = new UntypedFormControl(null);  // No initial value
    component.form = {
      get: jasmine.createSpy('get').and.returnValue(formControl)
    } as any;
    spyOn(component.formControl, 'setValue');
    component.propertyValues = { checked: undefined };
    component.setFormControlValue();
    expect(component.formControl.setValue).not.toHaveBeenCalled();
    expect(component.propertyValues.checked).toBeUndefined();
  });
  
  
  it('should return 0 when setOriginalOrder is called', () => {
    expect(component.setOriginalOrder()).toBe(0);
  });

  it('should return control list from offerTemplateBaseService$', () => {
    const mockControlList: any = ['control1', 'control2'];
    component.offerTemplateBaseService$ = {
      getControl: () => mockControlList,
    } as any;
    spyOn(component.offerTemplateBaseService$, 'getControl').and.returnValue(mockControlList);

  
    component.property = 'someField';
    expect(component.frmCtrlsList).toEqual(mockControlList);
  });

  it('should return formControl value for default property', () => {
    const formControl = new UntypedFormControl('some value');  // No initial value
    component.form = {
      get: jasmine.createSpy('get').and.returnValue(formControl)
    } as any;
  expect(component.summaryValue).toBe('some value');
});

it('should return formControl value when property does not match any case', () => {
  component.property = 'otherProperty';
  (component as any).formControl.setValue('other value');
  expect(component.summaryValue).toBe('other value');
});

});

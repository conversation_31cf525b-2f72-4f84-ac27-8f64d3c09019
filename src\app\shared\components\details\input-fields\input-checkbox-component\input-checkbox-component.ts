
import { Component, Input } from '@angular/core';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
@Component({
    selector: '[app-input-checkbox-component]',
    templateUrl: './input-checkbox-component.html'
})
export class InputCheckboxComponent extends BaseFieldComponentComponent {

    appDataOptions;
    @Input() checked
    constructor() {
        super();
    }


    ngOnChanges(): void {
        if (!this.fieldProperty) {
            this.setComponentProperties();
        }
    }
    getDisplayValue(key) {
        if (this.property === "reviewFlags" && key)
            return this.appData["reviewFlags"][key];
    }
    ngOnInit() {
        if (this.fieldProperty && this.property) {
            const { appDataOptions } = this.fieldProperty[this.property];
            this.setFormControlValue();
            this.appDataOptions = appDataOptions;
        }
    }
    get iterateArrayForCheckBox() {
        return ["reviewFlags"].includes(this.property);
    }

    setFormControlValue() {
        if (this.formControl?.value) {
            this.formControl.setValue(this.formControl?.value);
            this.propertyValues.checked = this.formControl?.value
        }
    }
    setOriginalOrder() {
        return 0;
    }
    get frmCtrlsList() {
        return this.offerTemplateBaseService$.getControl(this.property);
    }
    get summaryValue() {
        switch (this.property) {
            default:
                return this.formControl?.value;
        }
    }
    onChecked(isChecked: boolean) {
        this.propertyValues.checked = isChecked
    }
    get isChecked() {
        return this.propertyValues.checked;
    }


}

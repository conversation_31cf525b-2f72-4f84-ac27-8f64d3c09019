<ng-container *ngIf="fieldProperty && property && formGroupName">
  <div *ngIf="!summary; else summaryField" [formGroup]="formGroupName">
    <label class="font-weight-bold" for="date">{{label}}</label>  
    <tooltip-container *ngIf="tooltip" [title]="tooltipTitle"></tooltip-container>
      <div class="input-group"
        [class.border-danger]="serviceBasedOnRoute.getFieldErrors(property)">
        <input onkeydown="return false" type="text" id="date"
          style="padding-left: 12px; border-right: 1px solid white !important;"
          class="form-control form-control-lg optional"
          [class.is-populated]="getFieldValue(property) !== ''" name="date"
          autocomplete="off" 
          [id]="property"
          [bsConfig]="property == 'otStatusSetUntil' ? { containerClass: colorTheme, dateInputFormat: 'MM/DD/YYYY',showWeekNumbers: false,showClearButton: true, clearPosition: 'right'  }:{ containerClass: colorTheme, dateInputFormat: 'MM/DD/YYYY',showWeekNumbers: false, clearPosition: 'right'  }"
          bsDatepicker [minDate]="getMinDateBasedOnProperty(property)"
          (ngModelChange)="datePickerValueChange(property, $event);"
          (onShown)="onDatePickerDisplay()"
          [formControlName]="property" markAsTouchedOnFocus [formCtrl]="serviceBasedOnRoute.getControl(property)"
          #datePicker="bsDatepicker" />
  
        <div class="cursor-pointer input-group-append input-icon pt-4em px-2 bg-white">
          <div (click)="datePicker.toggle()">
            <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
          </div>
        </div>
      </div>
  
      <div app-show-field-error [property]= "property"></div>
    </div>
    
    <ng-template #summaryField>
      <app-input-display-component [label]="label" [value]= "formControl?.value" [section]="section" [programCode] = "programCode">
      </app-input-display-component>
    </ng-template>
</ng-container>

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InputDateComponentComponent } from './input-date-component.component';
import { UntypedFormControl } from '@angular/forms';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { Router } from '@angular/router';
import { AppInjector } from '@appServices/common/app.injector.service';
import { of } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

// Mocks
const mockFormControl = new UntypedFormControl(new Date());
const mockForm = {
    get: jasmine.createSpy('get').and.returnValue(mockFormControl),
};

const mockOfferRequestBaseService = {
    getFieldErrors: jasmine.createSpy().and.returnValue(['Error']),
    requestForm: mockForm,
    facetItemService$: { programCodeSelected: 'SPD' },
    initialDataService$: {
        getAppData: jasmine.createSpy().and.returnValue({ someAppData: true })
    },
    requestFormService$: {
        startDateObsrvble$: { next: jasmine.createSpy('next') },
        endDateObsrvble$: { next: jasmine.createSpy('next') },
    },
    getProgramCode: () => 'SPD',
    getPeriodWk: () => of({
        periodWeek: 'Week 3',
        periodId: 123,
        promoWeekId: 456
    }),
    periodCtrl: new UntypedFormControl(),
    periodIdCtrl: new UntypedFormControl(),
    promoWeekIdCtrl: new UntypedFormControl(),
    offerStartDateCtrl: new UntypedFormControl(new Date()),
    getControl: jasmine.createSpy('getControl').and.callFake((controlName: any) => {
        if (controlName === 'deliveryChannel') {
            return new UntypedFormControl('BEHAVIORAL_CONTINUITY_CODE');
        }
        return new UntypedFormControl(new Date());
    }),
    minOfferEndDate: new Date(),
    minOfferStartDate: new Date(),
    minDisplayEndDate: new Date(),
    resetVars: jasmine.createSpy('resetVars'),
    featureFlagService: {
        isBehavioralContinuityEnabled: true
    }
};

const mockCommonRouteService = {
    currentActivatedRoute: 'request'
};

const mockRouter = {
    url: '/create',
    events: of({ url: '/create' })
};

describe('InputDateComponentComponent', () => {
    let component: InputDateComponentComponent;
    let fixture: ComponentFixture<InputDateComponentComponent>;

    beforeAll(() => {
        spyOn(AppInjector, 'getInjector').and.returnValue({
            get: (token: any) => {
                if (token === Router) return mockRouter;
                if (token === CommonRouteService) return mockCommonRouteService;
                if (token === OfferRequestBaseService) return mockOfferRequestBaseService;
                return null;
            }
        });
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [InputDateComponentComponent],
            providers: [],
            schemas: [CUSTOM_ELEMENTS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(InputDateComponentComponent);
        component = fixture.componentInstance;
        component.property = 'offerEffectiveStartDate';
        component.section = 'SPD';
        component.form = mockForm;
        component.fieldProperty = {
            offerEffectiveStartDate: {
                appDataOptions: 'someData'
            }
        };
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should call initSetup on ngOnInit when fieldProperty is not defined', () => {
        spyOn(component, 'initSetup');
        component.fieldProperty = undefined;
        component.ngOnInit();
        expect(component.initSetup).toHaveBeenCalled();
    });

    it('should set formControl value on ngAfterViewInit', () => {
        const setValueSpy = spyOn(component.formControl, 'setValue');
        component.ngAfterViewInit();
        expect(setValueSpy).toHaveBeenCalled();
    });

    it('should update startDate and call startDateChange on datePickerValueChange for "offerEffectiveStartDate"', () => {
        const startDate = new Date();
        spyOn(component, 'startDateChange');
        component.datePickerValueChange('offerEffectiveStartDate', startDate);
        expect(component.currentOfferStartDate).toBe(startDate);
        expect(component.startDateChange).toHaveBeenCalledWith(startDate);
    });

    it('should call setMinOfferEndDate on datePickerValueChange for "offerEffectiveStartDate"', () => {
        const startDate = new Date();
        spyOn(component, 'setMinOfferEndDate');
        component.datePickerValueChange('offerEffectiveStartDate', startDate);
        expect(component.setMinOfferEndDate).toHaveBeenCalledWith(startDate);
    });

    it('should call endDateChange on datePickerValueChange for "offerEffectiveEndDate"', () => {
        const endDate = new Date();
        spyOn(component, 'endDateChange');
        component.datePickerValueChange('offerEffectiveEndDate', endDate);
        expect(component.currentOfferEndDate).toBe(endDate);
        expect(component.endDateChange).toHaveBeenCalledWith(endDate);
    });

    it('should update minOfferEndDate when setMinOfferEndDate is called with valid event', () => {
        const event = new Date();
        component.setMinOfferEndDate(event);
        expect(component.serviceBasedOnRoute.minOfferEndDate).toBe(event);
    });

    it('should reset minOfferEndDate to current date when setMinOfferEndDate is called with invalid event', () => {
        const event = new Date('2000-01-01');
        component.setMinOfferEndDate(event);
        expect(component.serviceBasedOnRoute.minOfferEndDate).toEqual(new Date());
    });

    it('should calculate correct difference in days in getDifferenceInDays', () => {
        const startDate = new Date('2025-01-01');
        const endDate = new Date('2025-01-10');
        const diff = component.getDifferenceInDays(startDate, endDate);
        expect(diff).toBe(9);
    });

    it('should call setCustomPeriod on setMinOfferEndDate if behavioral continuity is enabled', () => {
        spyOn(component, 'setCustomPeriod');

        mockOfferRequestBaseService.featureFlagService.isBehavioralContinuityEnabled = true;

        const event = new Date();
        const previousDay = new Date(event);
        previousDay.setDate(event.getDate() - 1);
        component.setMinOfferEndDate(previousDay);

        expect(component.setCustomPeriod).toHaveBeenCalled();
    });


    it('should set isDatePickerOpened to true on onDatePickerDisplay', () => {
        component.onDatePickerDisplay();
        expect(component.isDatePickerOpened).toBeTrue();
    });

    it('should return min date based on property in getMinDateBasedOnProperty', () => {
        const startDate = component.getMinDateBasedOnProperty('offerEffectiveStartDate');
        expect(startDate).toEqual(component.serviceBasedOnRoute.minOfferStartDate);
    });

    it('should call resetVars on ngOnDestroy', () => {
        component.ngOnDestroy();
        expect(component.serviceBasedOnRoute.resetVars).toHaveBeenCalled();
    });

    it('should return min date based on property in getMinDateBasedOnProperty', () => {
        const startDate = component.getMinDateBasedOnProperty('offerEffectiveStartDate');
        expect(startDate).toEqual(component.serviceBasedOnRoute.minOfferStartDate);

        const endDate = component.getMinDateBasedOnProperty('offerEffectiveEndDate');
        expect(endDate).toEqual(new Date(component.serviceBasedOnRoute.minOfferEndDate));

        const displayEndDate = component.getMinDateBasedOnProperty('displayEndDate');
        expect(displayEndDate).toEqual(component.serviceBasedOnRoute.minDisplayEndDate);

        const otStatusDate = component.getMinDateBasedOnProperty('otStatusSetUntil');
        expect(otStatusDate).toEqual(new Date());
    });

    it('should call endDateChange and emit value on date change', () => {
        const event = new Date();
        component.endDateChange(event);

        expect(component.offerRequestBaseService$.requestFormService$.endDateObsrvble$.next).toHaveBeenCalledWith(event);
    });


});

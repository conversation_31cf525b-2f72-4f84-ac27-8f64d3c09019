import { Component, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { BsDatepickerDirective } from 'ngx-bootstrap/datepicker';
@Component({
  selector: '[app-input-date-component]',
  templateUrl: './input-date-component.component.html',
  styleUrls: ['./input-date-component.component.scss']
})
export class InputDateComponentComponent extends BaseFieldComponentComponent implements OnInit {
  @ViewChild(BsDatepickerDirective, { static: false }) dp?: BsDatepickerDirective;
  colorTheme = "theme-dark-blue";
  currentOfferStartDate: Date;
  isDatePickerOpened: boolean = false;
  @Input("programCode") programCode;
  @Input("date") date;
  currentOfferEndDate: Date;
  constructor() {
    super();
  }
  //hide the date widget on scroll
  @HostListener('document:wheel', ['$event.target'])
  onScroll(): void {

    this.dp?.hide();
  }

  ngOnInit(): void {
    if (!this.fieldProperty) {
      this.initSetup();
    }
  }

  ngAfterViewInit(): void {
    this?.formControl?.setValue(this.getFieldValue(this.property));
  }
  getFieldValue(property) {
    const control = this.serviceBasedOnRoute.getControl(property);
    return control && control.value;
  }
  setMinOfferEndDate(event) {
    if (!event) {
      return;
    }
    if (new Date(event).getTime() < new Date().getTime()) {
      this.serviceBasedOnRoute.minOfferEndDate = new Date();
      this.setCustomPeriod();
      return;
    }
    this.serviceBasedOnRoute.minOfferEndDate = event;
    if (
      new Date(event).getTime() >
      new Date(
        this.serviceBasedOnRoute.getControl('offerEffectiveEndDate')?.value
      ).getTime()
    ) {
      this.serviceBasedOnRoute.getControl('offerEffectiveEndDate')?.setValue(event);
    }
  }
  startDateChange(evnt) {
    this.offerRequestBaseService$.requestFormService$.startDateObsrvble$.next(evnt);
  }
  datePickerValueChange(dateType, value) {
    if (dateType == "offerEffectiveStartDate") {
      if(!value) return;
      this.setMinOfferEndDate(value);
      this.currentOfferStartDate = value;
      this.startDateChange(value);
      this.setPeriodIdForSPD();
    } else if (dateType == "offerEffectiveEndDate") {
      if(!value) return;
      this.currentOfferEndDate = value;
      this.endDateChange(value);      
    }
    this.setCustomPeriod();
  }
  setCustomPeriod(){
    if(this.offerRequestBaseService$.featureFlagService.isBehavioralContinuityEnabled){
      let isBehavioralContinuity = this.serviceBasedOnRoute.getControl('deliveryChannel')?.value == CONSTANTS.BEHAVIORAL_CONTINUTY_CODE;
      let startDate = this.serviceBasedOnRoute.getControl('offerEffectiveStartDate')?.value;
      let endDate = this.serviceBasedOnRoute.getControl('offerEffectiveEndDate')?.value;
      if(isBehavioralContinuity && startDate && endDate){
        let period = this.getDifferenceInDays(startDate,endDate) + 1;
        this.serviceBasedOnRoute.getControl('customPeriod')?.setValue(period);
      }
    }
  }

  getDifferenceInDays(start, end) {
    const oneDay = 1000 * 60 * 60 * 24; // milliseconds in one day
    const diffInTime = Math.abs(new Date(end).getTime() - new Date(start).getTime()); // absolute difference in milliseconds
    const diffInDays = Math.ceil(diffInTime / oneDay); // round up to account for partial days
    return diffInDays;
  }
  endDateChange(evnt) {
    this.offerRequestBaseService$.requestFormService$.endDateObsrvble$.next(evnt);
  }
  setPeriodIdForSPD() {
    //when Program code is SPD, on change of start date need to get period week and display on UI
    const pcode = this.offerRequestBaseService$.getProgramCode();
    const periodCtrl = this.offerRequestBaseService$?.periodCtrl;
    const periodIdCtrl = this.offerRequestBaseService$?.periodIdCtrl;
    const promoWeekIdCtrl = this.offerRequestBaseService$?.promoWeekIdCtrl;
    if (pcode && pcode == CONSTANTS.SPD) {
      const startDate = this.offerRequestBaseService$?.offerStartDateCtrl?.value;

      if (startDate && this.isDatePickerOpened) {
        this.offerRequestBaseService$.getPeriodWk(startDate).subscribe((res: any) => {
          if (res && res.periodWeek) {
            periodCtrl.setValue(res.periodWeek);
            periodIdCtrl.setValue(res.periodId);
            promoWeekIdCtrl.setValue(res.promoWeekId);
          }
        })
      }
    }
  }
  onDatePickerDisplay() {
    this.isDatePickerOpened = true;
  }
  getMinDateBasedOnProperty(property) {
    if (property == "offerEffectiveStartDate") {
      return this.serviceBasedOnRoute.minOfferStartDate;
    } else if (property == "offerEffectiveEndDate") {
      return new Date(this.serviceBasedOnRoute.minOfferEndDate);
    } else if (property == "displayEndDate") {
      return this.serviceBasedOnRoute.minDisplayEndDate;
    } else if (property == "otStatusSetUntil") {
      return new Date();
    }
  }
  ngOnDestroy() {
    this.serviceBasedOnRoute.resetVars();
    this.isDatePickerOpened = false;
  }
}


<div [class]="columnSize">
    <label class="font-weight-bold m-0" [ngClass]="section === 'podDetails' ? 'mb-2' : '' "  for="formControl">{{label}} </label>
    <ng-container *ngIf="iterateArrayForLables else renderNormalText">
        <div *ngIf="iterableValue && toString.call(iterableValue) === '[object Array]'" class="pt-1">
            <div *ngFor="let item of iterableValue" class="pb-1">
                    {{item}}
            </div>
        </div>
        
    </ng-container>
</div>
<ng-template #renderNormalText>
    <div class="word-break" [ngClass]= "getStyleClasses(label,_facetItemService.programCodeSelected)">
        {{displayStaticValue?displayStaticValue:formValue?formValue:value?value:formControl?.value}}
    </div>
</ng-template>

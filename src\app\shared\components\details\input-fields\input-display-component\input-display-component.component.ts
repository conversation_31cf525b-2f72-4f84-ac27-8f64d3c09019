import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { FacetItemService } from '@appServices/common/facet-item.service';

@Component({
  selector: 'app-input-display-component ,[app-input-display-component]',
  templateUrl: './input-display-component.component.html',
  styleUrls: ['./input-display-component.component.scss']
})
export class InputDisplayComponentComponent extends BaseFieldComponentComponent implements OnChanges {
  @Input() label;
  @Input() value;
  @Input() options;
  @Input() display;
  @Input() displayKey;
  @Input() columnSize = 'col-12';
  @Input() section;
  @Input() programCode;
  @Input() date;
  appData: any;

  formValue: any;
  constructor(
    public _facetItemService: FacetItemService
  ) {
    super()
    this.appData = this.offerRequestBaseService$.initialDataService$.getAppData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.value && typeof this.value === 'object') {
      this.formValue = this.value && this.displayKey && this.options ? this.value[this.displayKey] : this.options ? this.options[this.value] : this.displayStaticValue;
    } else {
      this.formValue = this.displayStaticValue;
    }
  }
  get displayStaticValue() {
    const controlVal = this.getValue();
    if (controlVal) {
      this.formValue = controlVal;
    }
    if (this.property === 'usageLimitTypePerUser') {
      this.options = this.appData.offerLimits;
      return this.options[this.formValue];
    }
    if (this.label == "Dynamic") {
      return this.value ? "True" : "False";
    }
    if (this.label == "Flags" && this.value) {
      const reviewFlagOptions = this.appData["reviewFlags"];
      const selectedFlags = Object.keys(this.value).reduce((output, flag) => {
        if (this.value[flag]) {
          output.push(reviewFlagOptions[flag])
        }
        return output;
      }, []);
      return selectedFlags;
    }
    if (this.value && toString.call(this.value) === "[object String]") {
      this.formValue = this.value.trim();
    }

    if (this.value && toString.call(this.value) === "[object String]") {
      if (this.options && toString.call(this.options) === "[object Array]") {
        const isObject = this.options.filter(ele => toString.call(ele) === "[object String]");
        if (isObject.length) {
          return this.value;
        } else {
          return this.displayKey && this.options.filter(ele => ele[this.displayKey] == this.value)[0]?.[this.displayKey];
        }
      } else if (this.options && toString.call(this.options) === "[object Object]") {
        return this.options[this.value];
      } else {
        return this.value;
      }

    } else if (this.displayKey || this.options) {

      if (this.value && toString.call(this.value) === "[object Array]") {
        if (this.options && toString.call(this.options) === "[object Object]") {
          const returnedValues = [];
          this.value.forEach((val) => {
            if (this.options[val]) {
              returnedValues.push(this.options[val]);
            }
          });
          return returnedValues ? returnedValues.join(', ') : "";
        } else if (this.options && toString.call(this.options) === "[object Array]") {
          return this.handleOptionsArrayScenario();
        } else {
          return this.value;
        }
      } else {
        return this.displayKey ? this.value && this.value[this.displayKey] : this.options ? this.options[this.value] : "";
      }
    } else {

      return this.getValue();
    }
  }
  handleOptionsArrayScenario() {
    // This method is used to return value, when value is array and options is also array
    let str = this.value?.reduce((output, ele) => {
      let arr = this.options?.filter(optionKey => {
        if (optionKey && toString.call(optionKey) === "[object Object]") {
          return optionKey?.key == ele;
        } else {
          return optionKey == ele;
        }
      })
      arr?.length && output.push(arr[0]?.value || arr[0])
      return output;
    }, []);
    return str?.join(", ") || '';
  }
  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    if (this.value && typeof this.value === 'object') {
      this.displayValue();
    }
    this.formValue = this.displayStaticValue;
  }
  displayValue() {
    let initialData = window["initialData"].replace(/&amp;/g, "&");
    initialData = JSON.parse(decodeURI(initialData.replace(/&#39;/g, "'")));
    const options = initialData[this.property];
    return options;
  }
  get iterateArrayForLables() {
    return ["Flags"].includes(this.label);
  }
  get iterableValue() {
    return this.displayStaticValue ? this.displayStaticValue : this.formValue ? this.formValue :
      this.value ? this.value : this.formControl?.value
  }
  getStyleClasses(label, code) {
    const displayVal = this.displayStaticValue ? this.displayStaticValue : this.formValue ? this.formValue : this.value ? this.value : this.formControl?.value;
    switch (label) {
      case 'Additional Details': {
        return 'detailsBox';
      }
      case 'Status': {
        switch (displayVal) {
          case 'Active':
            return 'active-status-background';
          case 'Removed':
            return 'removed-status-background';
          case 'No UPCs':
            return 'noupcs-status-background';
          case 'New':
            return 'new-status-background';
          case 'Review':
            return 'review-status-background';
          case 'Parked':
            return 'parked-status-background';
          default: return 'active-status-background';
        }
      }
      default: {
        return '';
      }
    }
  }

}

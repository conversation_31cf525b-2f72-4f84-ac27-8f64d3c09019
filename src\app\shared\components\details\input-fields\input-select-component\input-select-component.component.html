<ng-container *ngIf="fieldProperty && property && formGroupName">
    <div  *ngIf="!summary && !isOnlyDisplayField; else summaryField" [formGroup]="formGroupName">
      <label class="d-block font-weight-bold" for="segment">{{label}} </label>  
      <tooltip-container *ngIf="tooltip" [title]="tooltipTitle"></tooltip-container>
      <ng-container *ngIf="property === 'allocationCriteriaList'">
        
          <div class="col-12 p-0">
            <ng-select                 
              [items]="options"
              bindLabel="label"
              [typeaheadOptionsLimit]="6"
              typeaheadWaitMs="1000"
              bindValue="value"
              [multiple]="true"
              placeholder="Select Allocation Criteria"
              id="allocationCriteriaList"
              formControlName="allocationCriteriaList"
              [(ngModel)]="data"
              >
            </ng-select>
            <ng-template ng-option-tmp let-item="item" (click)="onBlur($event)">
              {{item.name}}
          </ng-template>
          </div>
        
      </ng-container>
      <ng-container *ngIf="property !== 'allocationCriteriaList'">
        <select class="custom-select form-control" [class.border-danger]="serviceBasedOnRoute.getFieldErrors(property)"
          id="formControl" name="formControl" [formControlName]="property" markAsTouchedOnFocus [id]="property"
          [formCtrl]="serviceBasedOnRoute.getControl(property)" [ngClass]="{'readonly-select': readOnlyControls[property]}">
          <ng-container class='row' [ngSwitch]="optionType">  
            <ng-container *ngSwitchCase="'array'">
              <option *ngFor="let option of options" [ngValue]="option">{{ option }} </option>

            </ng-container>  
            <ng-container *ngSwitchCase="'object'">

              <option *ngFor="let k of options | keyobject" [ngValue]="options[k][optionValue]">{{ options[k][optionKey]}} </option>
            </ng-container>
            <ng-container *ngSwitchCase="'customObject'">
              <option selected hidden value="null"></option>
              <option *ngFor="let k of options | keyobject" [ngValue]="options[k][displayKey]">{{ options[k][optionKey]}} </option>
            </ng-container>  
            <ng-container *ngSwitchDefault>
              <option *ngFor="let k of options | keyobject" [ngValue]="k" [disabled]="doOptionDisable(k)" [ngClass]="{'disabled': doOptionDisable(k)}">{{ options[k]}} </option>
    
            </ng-container>  
        </ng-container> 
    
        </select>
      </ng-container>
        <div app-show-field-error [property]= "property"></div>
    </div>
      <ng-template #summaryField>
        <ng-container *ngIf="property === 'allocationCriteriaList'">
        <div class="col-12">
          <label class="font-weight-bold m-0" [ngClass]="section === 'podDetails' ? 'mb-2' : '' "  for="formControl">Allocation Criteria </label>
          <div class="word-break" >
            {{getSelectedAllocationCriteria(options,data)}}
        </div>
        </div>
      </ng-container>
      <ng-container *ngIf="property !== 'allocationCriteriaList' && displayKey">
        <app-input-display-component [displayKey]= "displayKey" [options] = "options" [display]="name" [label]="label" [value]= "getOptionValue(formControl?.value) || formControl?.value" [section]="section" [programCode] = "programCode" [date]="date">
        </app-input-display-component>
      </ng-container>
      </ng-template>
  </ng-container>
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { InputSelectComponentComponent } from './input-select-component.component';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AdminStoreGroupService } from '@appAdminServices/admin-store-group.service';
import { OfferDetailsService } from '@appOffersServices/offer-details.service';
import { CommonService } from '@appServices/common/common.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { of } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';
import { KeyobjectPipe } from '../../../../../shared/pipes/keyobject.pipe';

describe('InputSelectComponentComponent', () => {
  let component: InputSelectComponentComponent;
  let fixture: ComponentFixture<InputSelectComponentComponent>;
  let routerMock: any;
  let offerRequestBaseServiceMock: any;
  let offerTemplateBaseServiceMock: any;
  let commonRouteServiceMock: any;
  let adminStoreGroupServiceMock: any;
  let offerDetailsServiceMock: any;
  let commonServiceMock: any;
  let initialDataServiceMock: any;
  let testForm: UntypedFormGroup;

  beforeEach(() => {
    testForm = new UntypedFormGroup({
      testProperty: new UntypedFormControl('TEST VALUE'),
      otStatus: new UntypedFormControl('ACTIVE'),
      otStatusReason: new UntypedFormControl('BLANK'),
      usageLimitTypePerUser: new UntypedFormControl('ONCE_PER_OFFER'),
      programType: new UntypedFormControl('HEALTH'),
      deliveryChannel: new UntypedFormControl(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE),
      behavioralAction: new UntypedFormControl('ACTION1'),
      customerSegment: new UntypedFormControl('SEGMENT1'),
      allocationCriteria: new UntypedFormControl(['CRITERIA1']),
      allocationCriteriaList: new UntypedFormControl(['CRITERIA1']),
      podUsageLimit: new UntypedFormControl('LIMIT1'),
      subProgramCode: new UntypedFormControl('BASE')
    });

    routerMock = {
      url: '/test/create',
      events: of(new NavigationEnd(1, '/test', '/test'))
    };

    offerRequestBaseServiceMock = {
      initialDataService$: {
        getAppData: () => ({
          offerTemplateStatus: {
            'ACTIVE': 'Active',
            'NEW': 'New',
            'REVIEW': 'Review',
            'NO_UPCS': 'No UPCs',
            'PARKED': 'Parked',
            'REMOVED': 'Removed'
          },
          offerLimits: {
            'ONCE_PER_OFFER': 'Once per offer',
            'ONCE_PER_CLIP': 'Once per clip'
          },
          offerLimitsGR: {
            'ONCE_PER_OFFER': 'Once per offer',
            'ONCE_PER_CLIP': 'Once per clip'
          },
          podUsageLimitsGR: {
            'MULTI_CLIP': 'Multi Clip',
            'SINGLE_CLIP': 'Single Clip'
          },
          behavioralContinuityActions: {
            'ACTION1': 'Action 1',
            'ACTION2': 'Action 2'
          },
          offerDeliveryChannelsSPD: {
            'BAC': 'Behavioral Action',
            'BA': 'Behavioral Action',
            'CHANNEL1': 'Channel 1'
          },
          customerSegment: [
            'SEGMENT1',
            'SEGMENT2',
            'Customer Only'
          ],
          regions: [
            { code: '1', name: 'Region 1' },
            { code: '2', name: 'Region 2' }
          ]
        })
      },
      facetItemService$: {
        programCodeSelected: CONSTANTS.SPD,
        getQueryParamValue: () => ['123']
      },
      requestForm: testForm,
      getFieldErrors: jasmine.createSpy('getFieldErrors').and.returnValue(null),
      getProgramCode: jasmine.createSpy('getProgramCode').and.returnValue(CONSTANTS.SPD),
      getControl: jasmine.createSpy('getControl').and.callFake((controlName) => {
        return testForm.get(controlName);
      }),
      allocationDataList: {
        subscribe: jasmine.createSpy('subscribe').and.returnValue({ unsubscribe: () => {} }),
        observed: false
      },
      onBACChannelSelected$: {
        subscribe: jasmine.createSpy('subscribe').and.returnValue({ unsubscribe: () => {} }),
        observed: false
      },
      onUsageLimitChangeGR$: {
        subscribe: jasmine.createSpy('subscribe').and.returnValue({ unsubscribe: () => {} }),
        observed: false
      },
      requestFormService$: {
        isBehavioralActionEnabled: true,
        copyingVar: true
      },
      featureFlagService: {
        isBehavioralContinuityEnabled: true
      },
      isDivisionalGamesFeatureEnabled: true,
      getMultiClipFeatureFlagsByProgCode: true,
      showMultiClipLimit: true,
      isFAProgSubType: false,
      offerRequestId: '123',
      programType: { value: 'HEALTH' },
      subProgramCdCtrl: testForm.get('subProgramCode'),
      onSubProgramCdValueChange$: of(true)
    };

    offerTemplateBaseServiceMock = {
      templateForm: new UntypedFormGroup({
        info: new UntypedFormGroup({
          reviewFlags: new UntypedFormControl({
            flag1: true,
            flag2: false
          })
        })
      }),
      getControl: jasmine.createSpy('getControl').and.callFake((controlName) => {
        if (controlName === 'info.reviewFlags') {
          return offerTemplateBaseServiceMock.templateForm.get('info').get('reviewFlags');
        }
        return testForm.get(controlName);
      }),
      programTypeChangedValue$: of('HEALTH')
    };

    commonRouteServiceMock = {
      currentActivatedRoute: 'test'
    };

    adminStoreGroupServiceMock = {
      listCorporateStoreGroups: jasmine.createSpy('listCorporateStoreGroups').and.returnValue(of({
        storeGroups: [
          { regionId: '1', storeGroupName: 'Group 1', storeGroupRid: '101' },
          { regionId: '2', storeGroupName: 'Group 2', storeGroupRid: '102' }
        ]
      }))
    };

    offerDetailsServiceMock = {
      listOfferDetailsCode: jasmine.createSpy('listOfferDetailsCode').and.returnValue(of({
        dynaOfferProgramDetails: [
          { offerDetails: 'DETAIL1', offerDetailsCode: 'Detail 1' },
          { offerDetails: 'DETAIL2', offerDetailsCode: 'Detail 2' }
        ]
      }))
    };

    commonServiceMock = {
      getAllocationsData: jasmine.createSpy('getAllocationsData').and.returnValue(of({
        'A': 'Allocation A',
        'B': 'Allocation B'
      }))
    };

    initialDataServiceMock = {
      getAppData: jasmine.createSpy('getAppData').and.returnValue({
        regions: [
          { code: '1', name: 'Region 1' },
          { code: '2', name: 'Region 2' }
        ]
      })
    };

    const appInjectorMock = {
      get: (service: any) => {
        if (service === Router) return routerMock;
        if (service === OfferRequestBaseService) return offerRequestBaseServiceMock;
        if (service === OfferTemplateBaseService) return offerTemplateBaseServiceMock;
        if (service === CommonRouteService) return commonRouteServiceMock;
        return null;
      }
    };

    spyOn(AppInjector, 'getInjector').and.returnValue(appInjectorMock);

    TestBed.configureTestingModule({
      declarations: [
        InputSelectComponentComponent,
        KeyobjectPipe
      ],
      imports: [
        FormsModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: AdminStoreGroupService, useValue: adminStoreGroupServiceMock },
        { provide: OfferDetailsService, useValue: offerDetailsServiceMock },
        { provide: CommonService, useValue: commonServiceMock },
        { provide: InitialDataService, useValue: initialDataServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    });

    fixture = TestBed.createComponent(InputSelectComponentComponent);
    component = fixture.componentInstance;

    component.property = 'testProperty';
    component.section = 'testSection';
    component.form = testForm;
    component.fieldProperty = {
      testProperty: {
        appDataOptions: 'testOptions',
        optionKey: 'key',
        optionValue: 'value'
      },
      otStatus: {
        appDataOptions: 'otStatusOptions',
        optionKey: 'key',
        optionValue: 'value'
      },
      otStatusReason: {
        appDataOptions: 'otStatusReasonOptions',
        optionKey: 'key',
        optionValue: 'value'
      },
      usageLimitTypePerUser: {
        appDataOptions: 'offerLimitsGR',
        optionKey: 'key',
        optionValue: 'value'
      },
      behavioralAction: {
        appDataOptions: 'behavioralActions',
        optionKey: 'key',
        optionValue: 'value'
      },
      customerSegment: {
        appDataOptions: 'customerSegment',
        optionKey: 'key',
        optionValue: 'value'
      },
      allocationCriteria: {
        appDataOptions: 'allocationCodeApi',
        optionKey: 'name',
        optionValue: 'code'
      },
      allocationCriteriaList: {
        appDataOptions: 'allocationCodeApi',
        optionKey: 'name',
        optionValue: 'code'
      },
      podUsageLimit: {
        appDataOptions: 'podUsageLimitsGR',
        optionKey: 'key',
        optionValue: 'value'
      },
      storeGroup: {
        appDataOptions: 'storeGroupSearchNewApi',
        optionKey: 'name',
        optionValue: 'code'
      },
      offerDetails: {
        appDataOptions: 'offerDetailsListAPI',
        optionKey: 'name',
        optionValue: 'code'
      },
      subProgramCode: {
        appDataOptions: 'subProgramCodes',
        optionKey: 'key',
        optionValue: 'value'
      },
      deliveryChannel: {
        appDataOptions: 'offerDeliveryChannelsSPD',
        optionKey: 'key',
        optionValue: 'value'
      }
    };
    component.appData = offerRequestBaseServiceMock.initialDataService$.getAppData();
    component.offerRequestBaseService$ = offerRequestBaseServiceMock;
    component.offerTemplateBaseService$ = offerTemplateBaseServiceMock;
    component.router = routerMock;
    component.commonRouteService = commonRouteServiceMock;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should handle ngOnChanges with otStatusOptions', () => {
    component.property = 'otStatus';
    component.ngOnChanges();
    expect(component.options).toEqual(component.appData.offerTemplateStatus);
  });

  it('should handle ngOnChanges with otStatusReasonOptions', () => {
    component.property = 'otStatusReason';
    component.ngOnChanges();
    expect(component.options).toEqual(component.otStatusReasonOptions);
  });

  it('should handle ngOnChanges with subProgramCodes', () => {
    component.property = 'subProgramCode';
    component.ngOnChanges();
    expect(component.options).toEqual(CONSTANTS.SUBPROGRAMCODES_LIST);
  });

  it('should handle ngOnChanges with offerDeliveryChannelsSPD', () => {
    component.property = 'deliveryChannel';
    spyOn(component, 'setSpdChannels');
    component.ngOnChanges();
    expect(component.setSpdChannels).toHaveBeenCalled();
  });

  it('should handle ngOnChanges with customerSegment for SPD', () => {
    component.property = 'customerSegment';
    component.ngOnChanges();
    expect(component.options).toBeDefined();
  });

  it('should handle ngOnChanges with offerDetailsListAPI', () => {
    component.property = 'offerDetails';
    spyOn(component, 'initSubscriberForSubProgrmCdGR');
    component.ngOnChanges();
    expect(component.initSubscriberForSubProgrmCdGR).toHaveBeenCalled();
  });

  it('should handle ngOnChanges with podUsageLimitsGR', () => {
    component.property = 'podUsageLimit';
    spyOn(component, 'initMultiClip');
    spyOn(component, 'setPodUsageLimitOptions');
    component.ngOnChanges();
    expect(component.initMultiClip).toHaveBeenCalled();
    expect(component.setPodUsageLimitOptions).toHaveBeenCalled();
  });

  it('should handle ngOnChanges with allocationCodeApi', () => {
    component.property = 'allocationCriteria';
    spyOn(component, 'getAllocations');
    spyOn(component, 'initSubscribes');
    component.ngOnChanges();
    expect(component.getAllocations).toHaveBeenCalled();
    expect(component.initSubscribes).toHaveBeenCalled();
  });

  it('should handle ngOnChanges with offerLimitsGR', () => {
    component.property = 'usageLimitTypePerUser';
    spyOn(component, 'initUsageLimits');
    spyOn(component, 'initSubscriberForProgramSubType');
    spyOn(component, 'setUsageLimitOptions');
    component.ngOnChanges();
    expect(component.initUsageLimits).toHaveBeenCalled();
    expect(component.initSubscriberForProgramSubType).toHaveBeenCalled();
    expect(component.setUsageLimitOptions).toHaveBeenCalled();
  });

  it('should handle ngOnChanges with behavioralAction for behavioral continuity', () => {
    component.property = 'behavioralAction';
    component.ngOnChanges();
    expect(component.options).toEqual(component.appData.behavioralContinuityActions);
  });

  it('should handle ngOnChanges with storeGroupSearchNewApi', () => {
    component.property = 'storeGroup';
    spyOn(component, 'getStoreGroupDetails');
    component.ngOnChanges();
    expect(component.getStoreGroupDetails).toHaveBeenCalled();
  });

  it('should handle getOptionsFields with array of objects', () => {
    component.options = [{ key: 'key1', value: 'value1' }, { key: 'key2', value: 'value2' }];
    component.getOptionsFields('key', 'value');
    expect(component.optionType).toBe('object');
    expect(component.optionKey).toBe('key');
    expect(component.optionValue).toBe('value');
  });

  it('should handle getOptionsFields with array of strings', () => {
    component.options = ['option1', 'option2'];
    component.getOptionsFields('key', 'value');
    expect(component.optionType).toBe('array');
  });

  it('should handle formatAndSetAllocations', () => {
    const allocationsData = { 'A': 'Allocation A', 'B': 'Allocation B' };
    component.formatAndSetAllocations(allocationsData, 'name', 'code');
    expect(component.options.length).toBe(2);
    expect(component.options[0].name).toContain('A - Allocation A');
    expect(component.optionType).toBe('customObject');
    expect(component.displayKey).toBe('name');
  });

  it('should handle getAllocations', async () => {
    spyOn(component, 'formatAndSetAllocations');
    await component.getAllocations('name', 'code');
    expect(commonServiceMock.getAllocationsData).toHaveBeenCalled();
    expect(component.formatAndSetAllocations).toHaveBeenCalled();
  });

  it('should handle getSelectedAllocationCriteria with valid data', () => {
    const options = [
      { value: 'CRITERIA1', label: 'Criteria 1' },
      { value: 'CRITERIA2', label: 'Criteria 2' }
    ];
    const selected = ['CRITERIA1', 'CRITERIA2'];
    const result = component.getSelectedAllocationCriteria(options, selected);
    expect(result).toBe('Criteria 1, Criteria 2');
  });

  it('should handle getSelectedAllocationCriteria with no selected data', () => {
    const options = [
      { value: 'CRITERIA1', label: 'Criteria 1' },
      { value: 'CRITERIA2', label: 'Criteria 2' }
    ];
    const result = component.getSelectedAllocationCriteria(options, null);
    expect(result).toBe('');
  });

  it('should handle getStoreGroupDetails', async () => {
    await component.getStoreGroupDetails('name', 'code');
    expect(adminStoreGroupServiceMock.listCorporateStoreGroups).toHaveBeenCalled();
    expect(initialDataServiceMock.getAppData).toHaveBeenCalled();
    expect(component.options.length).toBe(2);
    expect(component.optionType).toBe('customObject');
    expect(component.displayKey).toBe('name');
  });

  it('should handle sortOptions', () => {
    const options = [
      { code: 'B', name: 'Option B' },
      { code: 'A', name: 'Option A' }
    ];
    const result = component.sortOptions(options, 'code');
    expect(result[0].code).toBe('A');
    expect(result[1].code).toBe('B');
  });

  it('should handle getControlObject with array of objects', () => {
    component.options = [
      { code: 'CODE1', name: 'Name 1' },
      { code: 'CODE2', name: 'Name 2' }
    ];
    component.displayKey = 'name';
    const result = component.getControlObject('CODE1', 'code');
    expect(result).toBe('Name 1');
  });

  it('should handle getControlObject with array of strings', () => {
    component.options = ['option1', 'option2'];
    const result = component.getControlObject('option1', 'value');
    expect(result).toBe('option1');
  });

  it('should handle getControlObject with object options', () => {
    component.options = {
      'KEY1': 'Value 1',
      'KEY2': 'Value 2'
    };
    const result = component.getControlObject('KEY1', 'key');
    expect(result).toBe('KEY1');
  });

  it('should handle getOfferDetailsApi', async () => {
    await component.getOfferDetailsApi('name', 'code', false);
    expect(offerDetailsServiceMock.listOfferDetailsCode).toHaveBeenCalled();
    expect(component.options.length).toBe(2);
    expect(component.optionType).toBe('customObject');
    expect(component.displayKey).toBe('name');
  });

  it('should handle getProgramService with divisional game enabled and subProgramCode', () => {
    component.offerRequestBaseService$.subProgramCdCtrl.setValue(CONSTANTS.DIVISONAL_GAME_TEXT);
    component.getProgramService(false);
    expect(offerDetailsServiceMock.listOfferDetailsCode).toHaveBeenCalledWith({ 
      programCode: CONSTANTS.DIVISONAL_GAME_CODE 
    });
  });
});

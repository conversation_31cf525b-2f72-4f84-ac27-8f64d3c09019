import { Component, Input, ViewEncapsulation } from '@angular/core';
import { AdminStoreGroupService } from '@appAdminServices/admin-store-group.service';
import { CONSTANTS } from '@appConstants/constants';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { OfferDetailsService } from "@appOffersServices/offer-details.service";
import { CommonService } from '@appServices/common/common.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { firstValueFrom } from 'rxjs';


@Component({
  selector: '[app-input-select-component]',
  templateUrl: './input-select-component.component.html',
  styleUrls: ['./input-select-component.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class InputSelectComponentComponent extends BaseFieldComponentComponent {
  options;
  optionType: string = "arrayObject";
  optionKey: any;
  optionValue: any;
  displayKey = "name";
  @Input() data;
  @Input("date") date;
  @Input() programCode = '';
  readOnlyControls: { [key: string]: boolean } = {};
  otStatusOptions = {
    'NEW': 'New',
    'ACTIVE': 'Active',
    'REVIEW': 'Review',
    'NO_UPCS': 'No UPCS',
    'PARKED': 'Parked',
    'REMOVED': 'Removed'
  };
   otStatusReasonOptions = {
    'BLANK': '--',
    'DISCO': 'Disco',
    'FUNDING': 'Funding',
    'PROMO': 'Promo',
    'REGION': 'Region',
    'SEASON': 'Season',
    'SUPPLY': 'Supply',
    'OTHER': 'Other'
  };
  optionItems = [];
  isMultiSelect:boolean = false;
  constructor(private offerDetailsService: OfferDetailsService, 
    private adminStoreGroupService: AdminStoreGroupService, 
    private _initialDataService: InitialDataService,
    private commonService: CommonService) {
    super();
  }
  /**
   * once allocation is added, we need to update the allocation dropdown options
   */
  initSubscribes() {
    // Making sure that only one observer is there
    const allocationObservers = this.offerRequestBaseService$.allocationDataList?.observed;
    if(!allocationObservers) {
      this.subs.sink = this.offerRequestBaseService$.allocationDataList.subscribe((data)=> {
        if(data) {
          this.formatAndSetAllocations(data);
        }
      })
    }
  }
  initUsageLimits(){
    const usageLimitForBACObservers = this.offerRequestBaseService$.onBACChannelSelected$?.observed;
    if(!usageLimitForBACObservers) {
      this.subs.sink = this.offerRequestBaseService$.onBACChannelSelected$.subscribe((channel) => {        
          this.setUsageLimitOptions(channel as string);
      });
    }
  }
  initMultiClip() {
    const usageLimitObservers = this.offerRequestBaseService$.onUsageLimitChangeGR$?.observed;
    if(!usageLimitObservers) {
      this.subs.sink = this.offerRequestBaseService$.onUsageLimitChangeGR$.subscribe((isUsageChange) => {
          if(isUsageChange && [CONSTANTS.GR, CONSTANTS.SPD].includes(this.programCd) && this.property === 'podUsageLimit') {
            this.setPodUsageLimitOptions();
          }
      })
    }
  }
  initSubscriberForProgramSubType(){
    this.subs.sink = this.serviceBasedOnRoute.programTypeChangedValue$.subscribe((value)=>{
        this.setUsageLimitOptions(value as string);
      
    })
  }
  initSubscriberForSubProgrmCdGR(optionKey, optionValue) {
    this.subs.sink = this.serviceBasedOnRoute.onSubProgramCdValueChange$.subscribe((isEdited = false)=>{
      this.getOfferDetailsApi(optionKey, optionValue , isEdited);
  })
  }


  get programCd() {
    return this.offerRequestBaseService$.getProgramCode();
  }
  /**
   * 
   * @returns If the status is REVIEW and any of review flag is checked then we need to disable OT status dropdown 
   */
   doOptionDisable(optionKey) {
    if(this.property !== "otStatus") {
      return false;
    }
    const { info: {reviewFlags} } = this.offerTemplateBaseService$.templateForm.value;
    const isAnyFlagChecked = reviewFlags && Object.keys(reviewFlags).some(flag=> reviewFlags[flag]);
    return this.property === "otStatus" && [CONSTANTS.ACTIVE, CONSTANTS.NEW].includes(optionKey) && isAnyFlagChecked;
  }
  get isDivisionalGameEnabled() {
    return this.offerRequestBaseService$.isDivisionalGamesFeatureEnabled;
  }
  get isBehavioralActionEnaled() {
    return this.offerRequestBaseService$?.requestFormService$?.isBehavioralActionEnabled;
  }
  get isBehavioralContinutyEnabled(){
    /*This is for checking only Whether the flag is enabled or not */
    return this.offerRequestBaseService$?.featureFlagService?.isBehavioralContinuityEnabled;
  }

  get isBehavioralContinutySelected(){
    return this.offerRequestBaseService$?.getControl('deliveryChannel')?.value == CONSTANTS.BEHAVIORAL_CONTINUTY_CODE;
  }
  setSpdChannels(optionKey, optionValue, appDataOptions) {
    const channelData = this.appData && appDataOptions && this.appData?.[appDataOptions];
    if(channelData) {
      let pc = this.offerRequestBaseService$.getProgramCode();
      if (
        !this.isBehavioralContinutyEnabled || 
        (this.isBehavioralContinutyEnabled && ![CONSTANTS.SPD].includes(pc))
      ) {
        delete channelData?.BAC;
      }      
      if(!this.isBehavioralActionEnaled){
        delete channelData?.BA;
      }      
      this.options = channelData;
      this.getOptionsFields(optionKey, optionValue);
    }
  }
  ngOnChanges(): void {
    if(!this.fieldProperty){
     this.setComponentProperties();
    }
    if (this.fieldProperty && this.property) {
      const { appDataOptions, optionKey, optionValue } = this.fieldProperty[this.property];
      this.isMultiSelect = this.property === "allocationCriteria";
      if(this.property === "behavioralAction" && this.isBehavioralContinutySelected)
      {
        this.options = this.appData["behavioralContinuityActions"];                                     
        this.getOptionsFields(optionKey, optionValue);
      }
      else if (appDataOptions === 'storeGroupSearchNewApi') {
        this.getStoreGroupDetails(optionKey, optionValue);
      }  else if(appDataOptions == "subProgramCodes") {
        this.options = CONSTANTS.SUBPROGRAMCODES_LIST;
        this.getOptionsFields(optionKey, optionValue);
      } else if(appDataOptions === "offerDeliveryChannelsSPD") {
          this.setSpdChannels(optionKey, optionValue, appDataOptions);
      } else if(this.programCd == CONSTANTS.SPD && this.property == "customerSegment") {
        this.options = this.appData[appDataOptions] && this.appData[appDataOptions].filter((segement)=> segement !== "Customer Only");
        this.getOptionsFields(optionKey, optionValue);
      } else if (appDataOptions === 'offerDetailsListAPI' || appDataOptions?.old === 'offerDetailsListAPI') {
        this.isDivisionalGameEnabled ? this.initSubscriberForSubProgrmCdGR(optionKey, optionValue) : this.getOfferDetailsApi(optionKey, optionValue, false);
      } else if(appDataOptions === 'podUsageLimitsGR') {
        const isMultiClipEnabled = this.offerRequestBaseService$.getMultiClipFeatureFlagsByProgCode;
        isMultiClipEnabled && this.initMultiClip();
        this.setPodUsageLimitOptions();
      } else if(['otStatusOptions', 'otStatusReasonOptions'].includes(appDataOptions)) {
        this.options = appDataOptions === 'otStatusOptions' ? this.appData["offerTemplateStatus"] : this.otStatusReasonOptions;
        this.getOptionsFields(optionKey, optionValue);
      }  else if(appDataOptions == "allocationCodeApi") {
        this.getAllocations(optionKey, optionValue);
        this.initSubscribes();
      }else if(this.programCd=== CONSTANTS.SPD && appDataOptions === "offerLimitsGR"){
        this.initUsageLimits();
        const isMultiClipEnabled = this.offerRequestBaseService$.getMultiClipFeatureFlagsByProgCode;
        isMultiClipEnabled && this.initSubscriberForProgramSubType();
        this.setUsageLimitOptions(null);
      } else {
        this.options = this.appData[appDataOptions];
        this.getOptionsFields(optionKey, optionValue);
      }

    }
  }
  /** set the offer limits as ONCE_PER_CLIP  only when the program type is HEALTH and disabling the multi clip features for SPD*/
  setUsageLimitOptions(programType: string): void {
    const usageLimitControl = this.serviceBasedOnRoute.getControl('usageLimitTypePerUser');
    const _programType = this.serviceBasedOnRoute.getControl('programType')?.value;
    const isCopyRequest = this.offerRequestBaseService$?.requestFormService$?.copyingVar;
    const appDataOptions = this.fieldProperty[this.property]?.appDataOptions;
    const appData = this.appData?.[appDataOptions];
    const defaultValue = 'ONCE_PER_OFFER';

    // Resolve offerReqId for both cases
    let offerReqId = this.offerRequestBaseService$?.offerRequestId;
    if (isCopyRequest) {
        offerReqId = this.offerRequestBaseService$?.facetItemService$?.getQueryParamValue('requestId')?.[0];
    }

    // Determine value for `usageLimitTypePerUser`
    const prevValue = offerReqId ? usageLimitControl?.value : defaultValue;

    // Set options for non-BAC programs
    if (appData) {
        const { ONCE_PER_CLIP, ...offerLimits } = appData;
        this.options = ((_programType === "HEALTH" || programType === "HEALTH") || this.isFAProgSubType) ? { ...appData } : { ...offerLimits };
    }

    // Update control and readonly status
    usageLimitControl?.setValue(prevValue);
    usageLimitControl?.updateValueAndValidity();
    this.readOnlyControls['usageLimitTypePerUser'] = false;
}


  setUsageForPrgmSubType() {
    const prgrmTypeValue = this.offerRequestBaseService$?.programType?.value;
    this.setUsageLimitOptions(prgrmTypeValue);
  }
  get isFAProgSubType() {
    return this.offerRequestBaseService$.isFAProgSubType;
  }
  setPodUsageLimitOptions() {
    const { appDataOptions } = this.fieldProperty[this.property];
    const isMultiClipVisible = this.offerRequestBaseService$.showMultiClipLimit;
    if(this?.appData?.[appDataOptions]) {
      const {MULTI_CLIP, ...restPodUsageLimits} = appDataOptions &&  this.appData[appDataOptions];
      this.options = isMultiClipVisible ? { MULTI_CLIP } : restPodUsageLimits;
    }
  }
  /**
   * 
   * @param allocationsData 
   * @param optionKey 
   * @param optionValue 
   * Once data is retrieved from api need to format it to show in dropdown
   */
  formatAndSetAllocations(allocationsData, optionKey = "name", optionValue = "code") {
    this.options = allocationsData && Object.keys(allocationsData).map((key) => ({ name: `${key} - ${allocationsData[key]}`,code: key})).sort((a:any,b: any) => a?.code - b?.code);
    this.getOptionsFields(optionKey, optionValue);
    this.optionType = "customObject";
    this.setFormControlValue("code");
    this.displayKey = "name";
  }
  /**
   * 
   * @param optionKey 
   * @param optionValue 
   * Need to do api call to get allocation data to set in options
   */
  async getAllocations(optionKey, optionValue) {
    let allocationsData = await firstValueFrom(this.commonService.getAllocationsData());
      this.formatAndSetAllocations(allocationsData, optionKey, optionValue);
  }

  getSelectedAllocationCriteria(options,selected){
    if(!selected){
      return '';
    }
    return options
  .filter(option => selected.includes(option.value))
  .map(option => option.label)
  .join(', ');
  }
  getOptionsFields(optionKey, optionValue) {
    if (toString.call(this.options) === '[object Array]') {
      const arrLen = this.options.filter(item => toString.call(item) === '[object Object]').length;
      this.optionType = arrLen ? 'object' : 'array';
      if (this.optionType === 'object') {
        this.optionKey = optionKey;
        this.optionValue = optionValue;
      }
    }
  }
  async getStoreGroupDetails(optionKey, optionValue) {
    let storeGroup: any = await this.getStoreGroupService();
    const regionData = this._initialDataService.getAppData().regions;
    const regionObject = regionData.reduce((output,ele)=>{
      const code = ele.code;
      output[code] = ele;
      return output;
    },{})
      this.options = this.sortOptions((storeGroup.storeGroups || storeGroup.dynaStoreGroups).map((val) => {
     
      
      const rData = regionObject[val.regionId];
      if (rData) {
        return {
          storeGroupId: val.storeGroupRid || val.id,
          name: `${val.regionId} ${rData.name}`,
          code: val.regionId,
          storeGroupName: val.storeGroupName
        }
      }
      
    }),"code").filter((ele)=>ele?.code);
    const appData = this._initialDataService.getAppData();
    appData.storeGroupRegions = this.options;
    this.getOptionsFields(optionKey, optionValue);
    this.optionType = "customObject";
    this.setFormControlValue("code");
    this.displayKey = "name";
    }
  sortOptions(options,field){
   return options.sort((a, b)=>{  // a should come before b in the sorted order
      if (a[field] < b[field]) {
        return -1;
        // a should come after b in the sorted order
      } else if (a[field] > b[field]) {
        return 1;
        // and and b are the same
      } else {
        return 0;
      }
})
  }
  getControlObject(value,field){
    if(toString.call(this.options)==="[object Array]"){
      const isObject = this.options.filter(ele=>toString.call(ele)==="[object Object]");
      if(isObject.length){
        const filter = this.options.filter(ele=>ele[field]===value);
        return (filter && filter.length > 0 && filter[0] && filter[0][this.displayKey]) || value;
      }else{
        return value;
      }
      
    }else if(toString.call(this.options)==="[object Object]"){
      return Object.keys(this.options).filter(ele=>ele===value)[0];
    }
    
  }
  getStoreGroupService() {
    return firstValueFrom(this.adminStoreGroupService.listCorporateStoreGroups());
  }
  getProgramService(isEdited) {
    if(this.isDivisionalGameEnabled) {
      const subProgramCode = this.offerRequestBaseService$?.subProgramCdCtrl?.value;
      let subPrgmCd = null;
      if(subProgramCode) {
         subPrgmCd =   subProgramCode === CONSTANTS.DIVISONAL_GAME_TEXT ? CONSTANTS.DIVISONAL_GAME_CODE :  CONSTANTS.BASE_CODE;
      } else {
        subPrgmCd = isEdited ? CONSTANTS.BASE_CODE : null; 
      }
      return subPrgmCd && firstValueFrom(this.offerDetailsService.listOfferDetailsCode({ programCode: subPrgmCd }));
    } else {
      return firstValueFrom(this.offerDetailsService.listOfferDetailsCode({ programCode: CONSTANTS.BASE_CODE }));
    }

  }
 
  async getOfferDetailsApi(optionKey, optionValue , isEdited) {
      let offerDetails: any = await this.getProgramService(isEdited);
      if(offerDetails) {
        this.options = offerDetails?.dynaOfferProgramDetails.map((val) => {
          return {
            code: val.offerDetails,
            name: val.offerDetailsCode
          }
        });
        this.getOptionsFields(optionKey, optionValue);
        this.optionType = "customObject";
        this.setFormControlValue("name");
        this.displayKey = "name";
      }

  }
  getValueFromData(){
    if(!this.data){
      return '';
    }
    let value = '',object,control = this.control;
    control.forEach((element,indx) => {
     if(indx!==control.length-1){
      object = this.data[element];
     }else{
       value = object?object[element]:this.data[element];
     }
     
    });
    return value;
  }
 setFormControlValue(key){
    if(this.formControl){
      let formCtrlVal = this.getControlObject(this.formControl?.value ||this.getValueFromData(),key);
      this.formControl.setValue(formCtrlVal);
    }
}
getOptionValue(val){
  if(!val || !this.options){
    return '';
  }
  let optionVal = this.getControlObject(val ||this.getValueFromData(),"code");
  return optionVal;
}
ngOnDestroy() {
  this.subs.unsubscribe();
}

}

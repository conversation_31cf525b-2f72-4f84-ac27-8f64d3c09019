<ng-container *ngIf="fieldProperty && property && formGroupName">
  
  <div  *ngIf="!summary; else summaryField" [formGroup]="formGroupName">
    <label class="d-block font-weight-bold" for="segment">{{label}} </label>  
    <tooltip-container *ngIf="tooltip" [title]="tooltipTitle"></tooltip-container>
    <span *ngIf="formControlMaxLength && !hideContentLength" class="float-right">({{formControlMaxLength - getValue()?.length }})</span>
    <textarea class="form-control flex-wrap mb-2 p-2" autocomplete="off" [id]="property"
    id="formControl" name="formControl" [attr.maxlength]="formControlMaxLength||''"
     [formControlName]="property" >
  </textarea>  
  <div app-show-field-error [property]= "property"></div> 
  </div>
  <ng-template #summaryField>
    <app-input-display-component  [label]="label" [value]= "formControl?.value">
    </app-input-display-component>
  </ng-template>
</ng-container>


    

   
    
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InputTextAreaComponentComponent } from './input-text-area-component.component';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { AppInjector } from '@appServices/common/app.injector.service';

const mockRouter = { events: { pipe: () => ({ subscribe: () => {} }) }, url: '', navigate: () => {} };
const mockCommonRouteService = { currentActivatedRoute: '' };
const mockOfferRequestBaseService = {
  facetItemService$: { programCodeSelected: 'testCode' },
  initialDataService$: { getAppData: () => ({}) },
  requestForm: { get: () => ({}) },
  getFieldErrors: () => []
};
const mockOfferTemplateBaseService = {
  templateForm: { get: () => ({}) }
};

const mockInjector = {
  get: (token: any) => {
    if (token && token.name) {
      switch (token.name) {
        case 'Router':
          return mockRouter;
        case 'CommonRouteService':
          return mockCommonRouteService;
        case 'OfferRequestBaseService':
          return mockOfferRequestBaseService;
        case 'OfferTemplateBaseService':
          return mockOfferTemplateBaseService;
        default:
          return {};
      }
    }
    return {};
  }
};

export const OFFER_REQUEST_CREATE_RULES = {
  testCode: { testSection: { testProperty: { label: 'Test Label' } } }
};
export const TEMPLATE_CREATE_RULES = {
  testCode: { testSection: { testProperty: { label: 'Test Label' } } }
};

describe('InputTextAreaComponentComponent', () => {
  let component: InputTextAreaComponentComponent;
  let fixture: ComponentFixture<InputTextAreaComponentComponent>;

  beforeEach(async () => {
    spyOn(AppInjector, 'getInjector').and.returnValue(mockInjector);
    await TestBed.configureTestingModule({
      declarations: [InputTextAreaComponentComponent, BaseFieldComponentComponent],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InputTextAreaComponentComponent);
    component = fixture.componentInstance;
    component.section = 'testSection';
    component.property = 'testProperty';
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should not throw if section or property is missing', () => {
    component.section = undefined;
    component.property = undefined;
    expect(() => component.setComponentProperties()).not.toThrow();
  });

  it('should not throw if rules are missing', () => {
    mockOfferRequestBaseService.facetItemService$.programCodeSelected = 'missingCode';
    component.section = 'missingSection';
    component.property = 'missingProperty';
    expect(() => component.setComponentProperties()).not.toThrow();
  });
});



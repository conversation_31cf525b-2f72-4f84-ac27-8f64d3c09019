<ng-container *ngIf="fieldProperty && property && formGroupName">
    <div  *ngIf="!summary && !isOnlyDisplayField; else summaryField" [formGroup]="formGroupName">
      <label class="font-weight-bold" for="formControl">{{label}} </label>     
      <tooltip-container *ngIf="tooltip" [title]="tooltipTitle"></tooltip-container>   
      <input class="form-control" [class.border-danger]="serviceBasedOnRoute.getFieldErrors(property)" type="text"
          id="formControl" name="formControl" autocomplete="off" [attr.maxlength]="formControlMaxLength||''"  [formControlName]="property" [OnlyNumber]="onlyNumber" [allowDecimals] ="allowDecimals" [property] ="property"
          (input) = "passTwoWay($event)" (keydown.tab)="searchScene7image($event)"  [id]="property" [attr.readonly]="readOnlyControls[property] ? true : null"
          (change)="searchScene7image($event)" (keydown.enter)="searchScene7image($event)" markAsTouchedOnFocus [formCtrl]="serviceBasedOnRoute.getControl(property)"/>
        <div app-show-field-error [property]= "property"  [(onTargetValues)]="onTargetValues"></div>
    </div>
    <ng-template #summaryField>
      <app-input-display-component [label]="label" [value]= "formControl?.value" [section]="section">
      </app-input-display-component>
    </ng-template>
  </ng-container>
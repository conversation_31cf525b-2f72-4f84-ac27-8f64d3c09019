import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InputTextComponentComponent } from './input-text-component.component';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { OfferRequestBaseService } from '@appModules/request/services/offer-request-base.service';
import { FormControl, FormGroup } from '@angular/forms';
import { Router, NavigationEnd } from '@angular/router';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { OfferTemplateBaseService } from '@appTemplates/services/offer-template-base.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { of } from 'rxjs';
import { OFFER_REQUEST_CREATE_RULES } from '@appModules/request/shared/rules/create.rules';

describe('InputTextComponentComponent', () => {
  let component: InputTextComponentComponent;
  let fixture: ComponentFixture<InputTextComponentComponent>;
  let mockOfferRequestBaseService: Partial<OfferRequestBaseService>;
  let mockOfferTemplateBaseService: Partial<OfferTemplateBaseService>;
  let mockCommonRouteService: Partial<CommonRouteService>;
  let mockRouter: Partial<Router>;
  let mockAppInjector: any;

  beforeEach(async () => {
    mockOfferRequestBaseService = {
      isBehavioralContinuityEnabledAndSelectedBAC: true,
      searchProductImage: jasmine.createSpy('searchProductImage'),
      facetItemService$: {
        programCodeSelected: 'SPD'
      } as any,
      initialDataService$: {
        getAppData: jasmine.createSpy('getAppData').and.returnValue({})
      } as any,
      requestForm: new FormGroup({
        testProperty: new FormControl('')
      }),
      getFieldErrors: jasmine.createSpy('getFieldErrors').and.returnValue(null)
    } as any;

    mockOfferTemplateBaseService = {
      templateForm: new FormGroup({
        testProperty: new FormControl('')
      })
    } as any;

    mockCommonRouteService = {
      currentActivatedRoute: 'offer'
    } as any;

    mockRouter = {
      events: of(new NavigationEnd(0, '', '')),
      url: 'create'
    };

    const mockRules = JSON.parse(JSON.stringify(OFFER_REQUEST_CREATE_RULES));

    if (!mockRules['SPD']) {
      mockRules['SPD'] = {};
    }

    mockRules['SPD']['testSection'] = {
      'testProperty': {
        label: 'Test Label',
        maxLength: 100,
        tooltip: false,
        tooltipTitle: '',
        validate: null,
        impactFieldForOfferUpdate: null,
        control: null,
        value: null,
        edit: true,
        required: false,
        error: null,
        onlyDisplay: false,
        checked: false,
        showLabelAsValue: false,
        tristateCheck: false,
        min: null,
        max: null
      },
      'customPeriod': {
        label: 'Custom Period',
        maxLength: 100,
        tooltip: false,
        tooltipTitle: '',
        validate: null,
        impactFieldForOfferUpdate: null,
        control: null,
        value: null,
        edit: true,
        required: false,
        error: null,
        onlyDisplay: false,
        checked: false,
        showLabelAsValue: false,
        tristateCheck: false,
        min: null,
        max: null
      },
      'otherProperty': {
        label: 'Other Property',
        maxLength: 100,
        tooltip: false,
        tooltipTitle: '',
        validate: null,
        impactFieldForOfferUpdate: null,
        control: null,
        value: null,
        edit: true,
        required: false,
        error: null,
        onlyDisplay: false,
        checked: false,
        showLabelAsValue: false,
        tristateCheck: false,
        min: null,
        max: null
      }
    };

    mockAppInjector = {
      getInjector: jasmine.createSpy('getInjector').and.returnValue({
        get: (token: any) => {
          if (token === Router) return mockRouter;
          if (token === CommonRouteService) return mockCommonRouteService;
          if (token === OfferRequestBaseService) return mockOfferRequestBaseService;
          if (token === OfferTemplateBaseService) return mockOfferTemplateBaseService;
          return null;
        }
      })
    };

    spyOn(AppInjector, 'getInjector').and.returnValue(mockAppInjector.getInjector());
    await TestBed.configureTestingModule({
      declarations: [InputTextComponentComponent],
      providers: [
        { provide: OfferRequestBaseService, useValue: mockOfferRequestBaseService },
        { provide: OfferTemplateBaseService, useValue: mockOfferTemplateBaseService },
        { provide: CommonRouteService, useValue: mockCommonRouteService },
        { provide: Router, useValue: mockRouter },
        { provide: 'OFFER_REQUEST_CREATE_RULES', useValue: mockRules }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InputTextComponentComponent);
    component = fixture.componentInstance;
    component.property = 'testProperty';
    component.section = 'testSection';
    component.readOnlyControls = {};
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update onTargetValues when passTwoWay is called', () => {
    const mockEvent = { target: { value: 'test value' } };
    component.passTwoWay(mockEvent);
    expect(component.onTargetValues).toBe('test value');
  });

  it('should call searchProductImage when property is scene7ImageId', () => {
    const mockEvent = { target: { value: 'image123' } };
    component.property = 'scene7ImageId';

    const mockSearchProductImage = jasmine.createSpy('searchProductImage');
    spyOnProperty(component, 'serviceBasedOnRoute', 'get').and.returnValue({
      searchProductImage: mockSearchProductImage
    });

    component.searchScene7image(mockEvent);
    expect(mockSearchProductImage).toHaveBeenCalledWith(mockEvent);
  });

  it('should set readOnlyControls[property] based on customPeriod condition in ngOnChanges', () => {
    const testComponent = new InputTextComponentComponent();
    testComponent.readOnlyControls = {};
    testComponent.property = 'customPeriod';
    testComponent.offerRequestBaseService$ = {
      isBehavioralContinuityEnabledAndSelectedBAC: true
    } as any;

    testComponent.ngOnChanges();
    expect(testComponent.readOnlyControls['customPeriod']).toBe(true);

    testComponent.property = 'otherProperty';
    testComponent.ngOnChanges();
    expect(testComponent.readOnlyControls['otherProperty']).toBe(false);
  });

  it('should not set readOnlyControls[property] when offerRequestBaseService$ is undefined', () => {
    const testComponent = new InputTextComponentComponent();
    testComponent.readOnlyControls = {};
    testComponent.property = 'customPeriod';
    testComponent.offerRequestBaseService$ = undefined;

    testComponent.ngOnChanges();
    expect(testComponent.readOnlyControls['customPeriod']).toBeFalsy();
  });

  it('should not call searchProductImage when property is not scene7ImageId', () => {
    const mockEvent = { target: { value: 'image123' } };
    component.property = 'otherProperty';

    const mockSearchProductImage = jasmine.createSpy('searchProductImage');
    spyOnProperty(component, 'serviceBasedOnRoute', 'get').and.returnValue({
      searchProductImage: mockSearchProductImage
    });

    component.searchScene7image(mockEvent);
    expect(mockSearchProductImage).not.toHaveBeenCalled();
  });

  it('should handle directiveObj input property', () => {
    const testDirective = { someProperty: 'test' };
    component.directiveObj = testDirective;
    expect(component.directiveObj).toEqual(testDirective);
  });

  it('should initialize readOnlyControls as empty object when creating a new instance', () => {
    const newComponent = new InputTextComponentComponent();
    expect(newComponent).toBeTruthy();
    expect(newComponent.readOnlyControls).toEqual({});
  });
});
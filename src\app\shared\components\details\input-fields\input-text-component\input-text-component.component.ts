import { Component, Input } from '@angular/core';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';

@Component({
  selector: '[app-input-text-component]',
  templateUrl: './input-text-component.component.html'
})
export class InputTextComponentComponent extends BaseFieldComponentComponent  {

  constructor() {
    super();
   }
   onTargetValues:any;

   @Input() directiveObj;
   readOnlyControls: { [key: string]: boolean } = {};
   
   passTwoWay(event){
    this.onTargetValues = event.target.value;
   }
   searchScene7image(event) {
    if(this.property === "scene7ImageId") {
      this.serviceBasedOnRoute.searchProductImage(event);
    }
   }
   ngOnChanges(): void {
    if (this.property === 'customPeriod') {
      this.readOnlyControls[this.property] = this.offerRequestBaseService$?.isBehavioralContinuityEnabledAndSelectedBAC;
    } else {
      this.readOnlyControls[this.property] = false;
    }
  }
  
}

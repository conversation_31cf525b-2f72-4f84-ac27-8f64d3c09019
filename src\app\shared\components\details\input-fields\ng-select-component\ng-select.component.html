<ng-container *ngIf="fieldProperty && property && formGroupName">
  <div  *ngIf="!summary; else summaryField" [formGroup]="formGroupName">
    <label class="font-weight-bold" for="formControl">{{ label }} </label>
    <ng-select
      class="w-100 input-bottom"
      [items]="typeAheadDataSource | async"
      [typeahead]="enteredData"
      bindLabel="value"
      bindValue="label"
      [multiple]="isMultiple"
      [placeholder]="placeholder"
      [id]="property"
      [formControlName]="property"
      [class.border-danger]="serviceBasedOnRoute.getFieldErrors(property)"
      markAsTouchedOnFocus
    >
    <ng-template ng-label-tmp let-item="item" let-clear="clear">
      <ng-container *ngIf="property === 'leftNavCategory' else defaultChip">
        <span aria-hidden="true" class="ng-value-icon left dfdf" (click)="removeLeftNavChip(item.label)"
          *ngIf="!(item.label == serviceBasedOnRoute.selectedShoppingCategory)">×</span>
      </ng-container>
      <ng-template #defaultChip>
        <span aria-hidden="true" class="ng-value-icon left dfdf" (click)="clear(item)">×</span>
      </ng-template>
      <span class="ng-value-label">{{ item.value }}</span>
    </ng-template>
  </ng-select>
    <div app-show-field-error [property]="property"></div>
  </div>
  
    <ng-template #summaryField>
      <app-input-display-component  [options] = "options" [display]="name" [label]="label" [value]= "formControl?.value" [section]="section">
      </app-input-display-component>
    </ng-template>
  </ng-container>
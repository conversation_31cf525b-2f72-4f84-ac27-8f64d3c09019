import { Component } from "@angular/core";
import { UntypedFormArray, UntypedFormGroup } from "@angular/forms";
import { BaseFieldComponentComponent } from "@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component";
import { OfferDetailsService } from "@appOffersServices/offer-details.service";
import { CommonService } from "@appServices/common/common.service";
import { removeItem } from "@appUtilities/arrayRemove";
import { decodeEntities } from "@appUtilities/decodeEntities";
import { Observable, Subject, of } from "rxjs";
import { map } from "rxjs/operators";

@Component({
  selector: "[app-ng-select-component]",
  templateUrl: "./ng-select.component.html",
})
export class NgSelectComponent extends BaseFieldComponentComponent {
  typeAheadDataSource: Observable<any>;
  sourceData;
  isMultiple = true;
  public enteredData = new Subject<string>();
  eventKeys: {};
  events: any = [];
  constructor(
    private commonService: CommonService,
    private offerDetailsService: OfferDetailsService
  ) {
    super();
  }
  ngOnChanges(): void {
    if (!this.fieldProperty) {
      this.setComponentProperties();
    }
    if (this.fieldProperty && this.property) {
      const {
        appDataOptions,
        isMultipleSelection,
      } = this.fieldProperty[this.property];
      if (appDataOptions === "customerFriendlyProductCategories") {
        this.setDataForLeftNavCategory();
      } else if (appDataOptions === "offerDetailsCodeListAPI") {
        this.getOdc();
      } else if (appDataOptions === "eventSrc") {
        this.getEventsList();
      }
      this.isMultiple = isMultipleSelection;
      this.initTypeAheads();
      this.setFormControlValue();
    }
  }
  getOdc() {
    this.offerDetailsService.fetchOdcList().subscribe((res: any) => {
      this.sourceData = res.reduce((output, key) => {
        output.push({ key, value: key });
        return output;
      }, []);
      this.initTypeAheads();
    });
  }
  getEventsList() {

      this.commonService.eventDataSrc.subscribe((res: any) => {
        if (res) {
          this.getEventsData(res);
        }
      });

  }
  getEventsData(eventsList) {
    this.sourceData = this.commonService.getEventsListBasedOnFeatureFlag(
      eventsList
    );
    this.serviceBasedOnRoute.eventsList = this.sourceData;
    this.options = this.sourceData
    this.initTypeAheads();
  }
  setDataForLeftNavCategory() {
    const productCategories = this.appData?.customerFriendlyProductCategories;
    this.options = productCategories;
    this.sourceData = Object.keys(productCategories).reduce((output, key) => {
      const parser = new DOMParser(),
        dom = parser.parseFromString(
          "<!doctype html><body>" + productCategories[key],
          "text/html"
        ),
        value = dom.body.textContent;
      output.push({ key, value });
      return output;
    }, []);
  }
  initTypeAheads() {
    this.setDataSources({
      enteredToken: of(""),
      sourceToSet: this.property,
      source: this.sourceData,
    });
    this.setDataSources({
      enteredToken: this.enteredData,
      sourceToSet: this.property,
      source: this.sourceData,
    });
  }
  setDataSources(obj) {
    let { enteredToken, sourceToSet, source } = obj;

    enteredToken
      .pipe(map((term) => this.getMatchingList({ term, source })))
      .subscribe((items) => {
        if (sourceToSet) {
          this.typeAheadDataSource = items;
        }
      });
  }
  setFormControlValue() {
    if (this.formControl?.value) {
      this.formControl.setValue(this.formControl?.value);
    }
  }
  getMatchingList(obj) {
    let { term, source } = obj;

    let matchedList =
      source &&
      source
        .filter((state: any) => {
          if (!this.caseSensitiveSearch && term) {
            return state.value.toLowerCase().includes(term.toLowerCase());
          }
          return state.value.includes(term);
        })
        .reduce((output, item) => {
          output.push({ label: item.key, value: decodeEntities(item.value) });
          return output;
        }, []);
        matchedList = this.sortSourceData(matchedList);
    return of(matchedList);
  }
  sortSourceData(items) {
    return items?.sort((a, b) => { return (a?.value?.toLowerCase() < b?.value?.toLowerCase()) ? -1 : (b?.value?.toLowerCase() < a?.value?.toLowerCase()) ? 1 : 0; });
  }
  removeLeftNavChip(id) {
    let oROArray = this.serviceBasedOnRoute.generalOfferTypeServic$.generalOfferTypeForm.get(
      "offerRequestOffers"
    ) as UntypedFormArray;
    let podDetails =
      oROArray &&
      (oROArray.at(0).get("storeGroupVersion").get("podDetails") as UntypedFormGroup);
    let leftNavValue = podDetails && podDetails.get("leftNavCategory").value;
    leftNavValue = leftNavValue && removeItem(leftNavValue, id);
    podDetails.get("leftNavCategory").setValue(leftNavValue);
    this.serviceBasedOnRoute.generalOfferTypeServic$.generalOfferTypeForm.markAsDirty();
  }
}

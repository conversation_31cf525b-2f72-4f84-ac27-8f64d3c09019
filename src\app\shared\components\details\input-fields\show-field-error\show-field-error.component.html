<ng-container *ngVar="serviceBasedOnRoute.getFieldErrors(property) as formControlError">
  <div class="text-danger" *ngIf="formControlError">
    <small *ngIf="formControlError.required">{{errors['required']}}</small>
    <small *ngIf="formControlError.apiError"
      apiErrorCtrl="formControl">{{ formControlError.apiError }}</small>
    <small class="text-danger" *ngIf="formControlError.customError">{{errors['customError']}}</small>

  </div>
</ng-container>

<ng-container>
  <small class="text-danger" *ngIf="showWarning">{{showWarning}} </small>
  </ng-container>
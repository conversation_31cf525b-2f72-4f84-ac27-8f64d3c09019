import { Component, Input } from '@angular/core';
import { BaseFieldComponentComponent } from '@appModules/request/core/offer-request/details/components/request-section/base-field-component/base-field-component.component';


@Component({
  selector: '[app-show-field-error]',
  templateUrl: './show-field-error.component.html',
  styleUrls: []
})
export class ShowFieldErrorComponent extends BaseFieldComponentComponent {
  programCdRule;
  errors: any;
  targetValue: any;
  showWarning: any;
  displayError:any = ['{{1}}','{{2}}','{{3}}']
  constructor() {
    super();
   }

  ngOnInit(): void {
    let rule = this.serviceBasedOnRoute.getProgramCodeRule();
    this.programCdRule = {...rule['offerRequest'],...rule['podDetails']};
    if(this.property){
      this.errors = this.programCdRule[this.property]['error'];

    }
  }
  
  @Input()
  set onTargetValues(onTargetValues: any) {
    this.showWarning = "";
    this.targetValue = onTargetValues;
    if (this.programCdRule && this.programCdRule[this.property]) {
      const ruleWarning = this.programCdRule[this.property]['warning'];
      let lengthRemaining = this.programCdRule[this.property]['maxLength'] - this.targetValue.length;
      let message = this.programCdRule[this.property]['warning']?.['message'];
      this.displayError.forEach(element => {
        if(message && message.indexOf(element)!==-1){
          message = message.replace(element, lengthRemaining);
        }
      });
     
      if (ruleWarning && this.targetValue.length >= ruleWarning['maxLength']) {
        this.showWarning = message;
      }
    }
  }
  get onTargetValues() {
    return this.targetValue;
  }
  

}

<div class="mob-popover-container">
        <div class="modal-header pb-0">
            <div class="container-fluid">
            <div class="row">
                <button type="button" class="close pull-right mobpopup-close" aria-label="Close" (click)="mobPopup.hide()">
                    <img src="assets/icons/close-icon.svg"
                    class="mb-1 position-absolute close-icon cursor-pointer" alt="close">
                </button>
            </div>
            <div class="row pr-4">
                <h2>Edit MOB</h2>
            </div>
            </div>
        </div>
        <div class="row mt-5" [formGroup]="mobDetailsForm">
            <div class="col-md-12 pl-6 pr-6">
                <label class="d-block font-weight-bold" for="mobID"
                >MOB ID</label
                >
                <div class="position-relative">
                    <ng-select
                        [items]="mobIDArr$"
                        [multiple]="false"
                        bindLabel="mobId"
                        bindValue="mobId"
                        [typeahead]="typedMobID$"
                        formControlName="mobID"
                        placeholder= "Will auto-assign MOB ID if left blank"
                        (change)="addMobDetails($event,'mobID')"
                        clearAllText="Clear"
                        id="mobID"
                        class="mt--2"
                    >
                    <ng-template ng-option-tmp let-item="item">
                        {{item.mobId}} - {{item.mobName}}
                    </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="col-md-12 pt-7 pl-6 pr-6">
                <label class="d-block font-weight-bold" for="mobName"
                >MOB Name</label
                >
                <div class="position-relative" *ngIf="showMobNameText">
                    <input type="text" placeholder="Will auto-assign MOB Name if left blank"
                    class="form-control" (change)="addMobDetails($event,'mobName')" id="mobName" formControlName="mobName" 
                    name="mobName">
                    <label class="pt-2" *ngIf="showInstructLabel">Changing the name will change it for every offer that uses this MOB ID.</label>
                </div>
                <div class="position-relative" *ngIf="showMobNameLabel">
                   {{mobDetailsForm.value.mobName}}
                </div>
            </div>
            <div class="col-md-12 pt-7 pl-6 pb-3 pr-6 text-right">
            <a href="javascript:void(0)" (click)="mobPopup.hide()" class="btn cancel-link pt-0">Cancel</a>
            <button 
                class="edit-button-layout btn btn-primary" (click)="saveMobDetails()">{{buttonType}}</button>
            </div>
        </div>     
</div>
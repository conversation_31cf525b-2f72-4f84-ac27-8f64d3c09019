import { ComponentFixture, TestBed, fakeAsync, flush, tick } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormGroup, UntypedFormControl } from '@angular/forms';
import { of } from 'rxjs';
import { MobComponent } from './mob.component';
import { MobService } from '@appServices/details/mob.service';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { throwError } from 'rxjs';


describe('MobComponent', () => {
    let component: MobComponent;
    let fixture: ComponentFixture<MobComponent>;
    let mobServiceSpy: jasmine.SpyObj<MobService>;
    let offerRequestBaseServiceSpy: Partial<OfferRequestBaseService>;

    beforeEach(async () => {
        mobServiceSpy = jasmine.createSpyObj('MobService', ['searchMob', 'updateMob', 'updateMobKeys']);
        offerRequestBaseServiceSpy = {
            requestForm: new UntypedFormGroup({
                info: new UntypedFormGroup({
                    mobName: new UntypedFormControl('TestMobName'),
                    mobId: new UntypedFormControl(null),
                    autoAssignMob: new UntypedFormControl(false),
                    brandAndSize: new UntypedFormControl('TestBrand')
                })
            })
        };

        await TestBed.configureTestingModule({
            declarations: [MobComponent],
            imports: [ReactiveFormsModule],
            providers: [
                { provide: MobService, useValue: mobServiceSpy },
                { provide: OfferRequestBaseService, useValue: offerRequestBaseServiceSpy }
            ]
        }).overrideComponent(MobComponent, {
            set: {
                template: `
        <form [formGroup]="mobDetailsForm">
          <input type="text" formControlName="mobID" />
          <input type="text" formControlName="mobName" />
        </form>
      `}
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(MobComponent);
        component = fixture.componentInstance;
        component.mobPopup = { hide: jasmine.createSpy('hide') };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('PopulateMobDetails', () => {
        it('should populate mob details when mobID is provided', fakeAsync(() => {
            // Arrange: set the input property and stub the searchMob method to return a valid response.
            component.mobID = '123';
            const dummyResponse = {
                dynaMobDetails: [{
                    mobId: '123',
                    mobName: 'Dummy Mob',
                    mobType: 'OTHER_TYPE'
                }]
            };
            mobServiceSpy.searchMob.and.returnValue(of(dummyResponse));

            component.PopulateMobDetails();
            tick();

            expect(component.mobDetailsForm.get('mobID').value).toEqual('123');
            expect(component.mobDetailsForm.get('mobName').value).toEqual('Dummy Mob');
            expect(mobServiceSpy.updateMobKeys).toHaveBeenCalledWith(dummyResponse.dynaMobDetails[0]);
            expect(component.mobNameChanged).toBeTrue();
            flush();
        }));

        it('should set mobName from request form when mobID is not provided', () => {
            component.mobID = null;
            offerRequestBaseServiceSpy.requestForm.get('info').get('mobName').setValue('FormMobName');

            component.PopulateMobDetails();

            expect(component.mobDetailsForm.get('mobName').value).toEqual('FormMobName');
            expect(component.mobNameChanged).toBeTrue();
            expect(component.buttonType).toEqual('Save');
        });
    });

    describe('initTypeAhead', () => {
        it('should update mobIDArr$ when typedMobID$ emits a valid term', fakeAsync(() => {
            const dummyResponse = {
                dynaMobDetails: [{ mobId: '1', mobName: 'Mob1' }]
            };
            mobServiceSpy.searchMob.and.returnValue(of(dummyResponse));
            component.initTypeAhead();

            // Act: emit a term and simulate debounce time.
            component.typedMobID$.next('test');
            tick(300);

            // Assert:
            expect(component.mobIDArr$).toEqual(dummyResponse.dynaMobDetails);
            flush();
        }));
    });

    describe('addMobDetails', () => {
        it('should update mob details when event is provided for control "mobID"', () => {
            // Arrange:
            const dummyEvent = { mobId: '456', mobName: 'New Mob', mobType: 'OTHER_TYPE' };
            spyOn(component, 'checkMobType');

            // Act:
            component.addMobDetails(dummyEvent, 'mobID');

            // Assert:
            expect(component.checkMobType).toHaveBeenCalledWith(dummyEvent);
            expect(component.mobDetailsForm.get('mobName').value).toEqual(dummyEvent.mobName);
            expect(component.mobNameChanged).toBeTrue();
            expect(component.buttonType).toEqual('Update');
            expect(component.existingMobDetails).toEqual(dummyEvent);
            expect(mobServiceSpy.updateMobKeys).toHaveBeenCalledWith(dummyEvent);
        });

        it('should update mob details when event is provided for control "mobName" and mobID exists', () => {
            component.mobDetailsForm.get('mobID').setValue('789');
            component.mobDetailsForm.get('mobName').setValue('Updated Mob');
            component.existingMobDetails = { mobId: '789', mobName: 'Old Name', mobType: 'NB_MOB' };

            component.addMobDetails({ mobName: 'Updated Mob' }, 'mobName');

            expect(component.mobNameChanged).toBeFalse();
            expect(component.existingMobDetails.mobName).toEqual('Updated Mob');
            expect(component.buttonType).toEqual('Update');
            expect(mobServiceSpy.updateMobKeys).toHaveBeenCalledWith(component.existingMobDetails);
        });

        it('should update mob details when event is null and control is "mobID"', () => {
            offerRequestBaseServiceSpy.requestForm.get('info').get('brandAndSize').setValue('BrandSize');

            component.addMobDetails(null, 'mobID');

            expect(component.showMobNameLabel).toBeFalse();
            expect(component.showMobNameText).toBeTrue();
            expect(component.mobNameChanged).toBeTrue();
            expect(component.buttonType).toEqual('Save');
            expect(component.mobDetailsForm.get('mobName').value).toEqual('BrandSize');
            expect(component.showInstructLabel).toBeFalse();
        });
    });

    describe('setMobDetailsToRequest', () => {
        it('should update the request form info with provided mob details', () => {
            const dummyObj = { mobId: '321', mobName: 'SetMob' };

            component.setMobDetailsToRequest(dummyObj, true);

            const infoGroup = offerRequestBaseServiceSpy.requestForm.get('info');
            expect(infoGroup.get('mobId').value).toEqual(dummyObj.mobId);
            expect(infoGroup.get('mobName').value).toEqual(dummyObj.mobName);
            expect(infoGroup.get('autoAssignMob').value).toBeTrue();
        });
    });

    describe('saveMobDetails', () => {
        it('should call updateMob when mobNameChanged is false', fakeAsync(() => {
            component.mobNameChanged = false;
            const dummyResponse = [{ mobId: '999', mobName: 'Updated Mob' }];
            mobServiceSpy.updateMob.and.returnValue(of(dummyResponse));
            spyOn(component.showMobID, 'emit');

            component.mobDetailsForm.get('mobID').setValue('999');

            component.saveMobDetails();
            tick();

            expect(mobServiceSpy.updateMob).toHaveBeenCalledWith({ mobType: 'NB_MOB', mobId: '999' });
            expect(component.showMobID.emit).toHaveBeenCalledWith(dummyResponse[0].mobId);
            expect(component.mobPopup.hide).toHaveBeenCalled();
            flush();
        }));

        it('should update request and emit mobID when mobNameChanged is true and mobID exists', () => {
            component.mobNameChanged = true;
            component.mobDetailsForm.get('mobID').setValue('111');
            component.mobDetailsForm.get('mobName').setValue('New Mob Name');
            spyOn(component.showMobID, 'emit');

            component.saveMobDetails();

            expect(component.showMobID.emit).toHaveBeenCalledWith('111');
            const infoGroup = offerRequestBaseServiceSpy.requestForm.get('info');
            expect(infoGroup.get('mobId').value).toEqual('111');
            expect(infoGroup.get('mobName').value).toEqual('New Mob Name');
            expect(infoGroup.get('autoAssignMob').value).toBeFalse();
            expect(component.mobPopup.hide).toHaveBeenCalled();
        });

        it('should update request and emit null mobID when mobNameChanged is true and mobID does not exist', () => {
            component.mobNameChanged = true;
            component.mobDetailsForm.get('mobID').setValue(null);
            component.mobDetailsForm.get('mobName').setValue('New Mob Name');
            spyOn(component.showMobID, 'emit');

            component.saveMobDetails();

            expect(component.showMobID.emit).toHaveBeenCalledWith(null);
            const infoGroup = offerRequestBaseServiceSpy.requestForm.get('info');
            expect(infoGroup.get('mobId').value).toBeNull();
            expect(infoGroup.get('mobName').value).toEqual('New Mob Name');
            expect(infoGroup.get('autoAssignMob').value).toBeTrue();
            expect(component.mobPopup.hide).toHaveBeenCalled();
        });
    });
    
    it('should log error when updateMob returns error in saveMobDetails', fakeAsync(() => {
        component.mobNameChanged = false;
        component.mobDetailsForm.get('mobID').setValue('123');

        spyOn(console, 'log');

        mobServiceSpy.updateMob.and.returnValue(throwError(() => new Error('Test Error')));

        component.saveMobDetails();
        tick();

        expect(console.log).toHaveBeenCalledWith(jasmine.any(Error));
        flush();
    }));

    it('should use fallback value from brandAndSize.value.trim() when mobName is falsy in saveMobDetails', () => {
        component.mobNameChanged = true;
        component.mobDetailsForm.get('mobID').setValue('123');
        component.mobDetailsForm.get('mobName').setValue('');

        offerRequestBaseServiceSpy.requestForm.get('info').get('brandAndSize').setValue('   FallbackName   ');

        spyOn(component.showMobID, 'emit');
        spyOn(component, 'setMobDetailsToRequest');

        component.saveMobDetails();

        expect(component.showMobID.emit).toHaveBeenCalledWith('123');
        expect(component.setMobDetailsToRequest).toHaveBeenCalledWith({ mobId: '123', mobName: 'FallbackName' }, false);
        expect(component.mobPopup.hide).toHaveBeenCalled();
    });

    it('should set mobNameChanged to true and buttonType to "Save" when mobID is not provided for control "mobName"', () => {
        component.mobDetailsForm.get('mobID').setValue(null);
        component.mobDetailsForm.get('mobName').setValue('SomeName');

        component.addMobDetails({ mobName: 'SomeName' }, 'mobName');

        expect(component.mobNameChanged).toBeTrue();
        expect(component.buttonType).toEqual('Save');
    });

    it('should update existingMobDetails.mobName using fallback with brandAndSize.value when mobName form control is falsy and mobID exists for control "mobName"', () => {
        component.mobDetailsForm.get('mobID').setValue('111');
        component.mobDetailsForm.get('mobName').setValue('');
        offerRequestBaseServiceSpy.requestForm.get('info').get('brandAndSize').setValue('FallbackName');
        component.existingMobDetails = { mobId: '111', mobName: 'OldName' };

        component.addMobDetails({ mobName: '' }, 'mobName');

        expect(component.mobNameChanged).toBeFalse();
        expect(component.existingMobDetails.mobName).toEqual('FallbackName');
        expect(component.buttonType).toEqual('Update');
        expect(mobServiceSpy.updateMobKeys).toHaveBeenCalledWith(component.existingMobDetails);
    });

    it('should set showMobNameLabel to false when event is falsy and control is not "mobID"', () => {
        component.addMobDetails(null, 'someOtherControl');

        expect(component.showMobNameLabel).toBeFalse();
    });
    
    it('should return an observable of an empty array when term is empty or type is not "mobID"', fakeAsync(() => {
        let resultEmpty: any;
        let resultWrongType: any;
  
        component.getMobDetails("", "mobID").subscribe(data => resultEmpty = data);
        tick();
        expect(resultEmpty).toEqual([]);
  
        component.getMobDetails("someTerm", "mobName").subscribe(data => resultWrongType = data);
        tick();
        expect(resultWrongType).toEqual([]);
    }));

    it('should log error in the error callback when getMobDetails emits an error', fakeAsync(() => {
        spyOn(component, 'getMobDetails').and.returnValue(throwError(() => new Error('Test Error')));
        spyOn(console, 'log');

        component.initTypeAhead();

        component.typedMobID$.next('errorTerm');
        tick(300);

        expect(console.log).toHaveBeenCalledWith(jasmine.any(Error));
    }));

    it('should set showInstructLabel to true when details.mobType is "NB_MOB"', () => {
        const details = { mobType: "NB_MOB", mobName: "AnyName" };

        component.showInstructLabel = false;

        component.checkMobType(details);

        expect(component.showInstructLabel).toBeTrue();
    });

});
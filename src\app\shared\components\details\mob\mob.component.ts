import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { MobService } from '@appServices/details/mob.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { Subject, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from "rxjs/operators";

@Component({
  selector: 'app-mob',
  templateUrl: './mob.component.html',
  styleUrls: ['./mob.component.scss']
})
export class MobComponent extends UnsubscribeAdapter implements OnInit {
  mobDetailsForm = new UntypedFormGroup({
    mobID: new UntypedFormControl(),
    mobName: new UntypedFormControl()
  });
  @Input() mobPopup;
  @Input() mobID;
  @Output() showMobID = new EventEmitter<string>();
  mobDataSource: any;
  mobIDArr$;
  buttonType = "Update";
  showMobNameText: boolean = true;
  showMobNameLabel: boolean = false;
  public typedMobID$ = new Subject<string>();
  mobName: any;
  mobId: any;
  mobNameChanged: boolean = false;
  showInstructLabel: boolean = false;
  brandAndSize: any;
  existingMobDetails: any;
  requestForm: UntypedFormGroup;
  constructor(private mobservice:MobService, 
    private offerRequestBaseService: OfferRequestBaseService
    ) {
    super();
  }

  ngOnInit(): void {
    this.PopulateMobDetails();
    this.initTypeAhead();
  }

  PopulateMobDetails(){
    this.requestForm = this.offerRequestBaseService?.requestForm as UntypedFormGroup;
    this.brandAndSize = this.requestForm?.get('info')?.get('brandAndSize');
    if(this.mobID){
      this.showInstructLabel = true;
      this.subs.sink = this.mobservice.searchMob(`cfgKey=${this.mobID};limit=1000;`,true).subscribe((response)=>{
        if(response['dynaMobDetails'].length){
          this.mobDetailsForm.get('mobID').setValue(response['dynaMobDetails'][0]['mobId']);
          this.mobDetailsForm.get('mobName').setValue(response['dynaMobDetails'][0]['mobName']);
          this.mobservice.updateMobKeys(response['dynaMobDetails'][0]);
          this.existingMobDetails = response['dynaMobDetails'][0];
        }
        this.checkMobType(this.existingMobDetails);
        this.mobDetailsForm.get('mobName').setValue(this.existingMobDetails.mobName);
     });
        this.mobNameChanged = true;
    } else {
        const mobName = this.requestForm?.get('info')?.get('mobName');
        this.mobDetailsForm.get('mobName').setValue(mobName.value);
        this.mobNameChanged = true;
        this.buttonType ="Save";
    }
  }

  initTypeAhead(){
        this.typedMobID$.pipe(
          distinctUntilChanged(),
          debounceTime(300),
          switchMap((term) => this.getMobDetails(term, 'mobID'))
        ).subscribe({
          next: (items:any) => {
              if(items){
                this.mobIDArr$ = items['dynaMobDetails'];
              }
            },
            error: (err)=>{
            if(err) {
              console.log(err);
            }
        }});
  }

  checkMobType(details){
    if(details.mobType !== "NB_MOB"){
      this.showMobNameLabel = details.mobName;
      this.showMobNameText = false;
      this.showInstructLabel = false;
    } else {
      this.showInstructLabel = true;
    }       
  }

  getMobDetails(term, type) {
    if(term !=="" && term !== null && type==="mobID") {
        return this.mobservice.searchMob(`combinedSearch=${term};limit=1000`,false);
    } else {
      return of([])
    }
  }

  addMobDetails(event,control) {
    if(event){
      if(control === "mobID"){
        this.checkMobType(event);
        this.mobDetailsForm.get('mobName').setValue(event.mobName);
        this.mobNameChanged = true;
        this.buttonType = "Update";
        this.existingMobDetails = event;
        this.mobservice.updateMobKeys(event);
     }
     if(control === "mobName"){
        const mobID = this.mobDetailsForm.get('mobID').value;
        if(!mobID){
          this.mobNameChanged = true;
          this.buttonType = "Save";
        } else {
          this.mobNameChanged = false;
          this.existingMobDetails.mobName = this.mobDetailsForm.get('mobName').value || this.brandAndSize.value;
          this.buttonType = "Update";
          this.mobservice.updateMobKeys(this.existingMobDetails);
        }
     }
    }else{
      if(control === "mobID"){
          this.showMobNameLabel = false;
          this.showMobNameText = true;
          this.mobNameChanged = true;
          this.buttonType = "Save";
          this.mobDetailsForm.get('mobName').setValue(this.brandAndSize.value);
          this.showInstructLabel = false;
      }else {
        this.showMobNameLabel = false;
      }
     }
  }

  setMobDetailsToRequest(obj,autoAssign){
    const info = this.offerRequestBaseService.requestForm.get('info') as UntypedFormGroup;
    info.get('mobId').setValue(obj.mobId);
    info.get('mobName').setValue(obj.mobName);
    info.get('autoAssignMob').setValue(autoAssign);
  }

  saveMobDetails(){
      this.requestForm.markAsDirty();
      if(!this.mobNameChanged){
        this.buttonType = "Update";
        this.mobservice.updateMob({mobType:'NB_MOB',mobId:this.mobDetailsForm.value.mobID}).subscribe({
          next: (response:any)=>{
          this.showMobID.emit(response[0].mobId);
          this.mobPopup.hide();
          this.mobNameChanged = false;
          this.setMobDetailsToRequest({mobId:response[0].mobId,mobName:response[0].mobName},false);
        },
        error: (error: any) => {
            console.log(error);
        }});
      } else {
        const mobId = this.mobDetailsForm.value.mobID;
        const mobName = this.mobDetailsForm.value.mobName ? this.mobDetailsForm.value.mobName : this.brandAndSize.value?.trim();
        if(mobId){
          this.showMobID.emit(mobId);
          this.setMobDetailsToRequest({mobId:mobId,mobName:mobName},false);
        }else {
          this.showMobID.emit(null);
          this.setMobDetailsToRequest({mobId: null,mobName:mobName},true);
        }
        this.mobPopup.hide();
      }
  }
}

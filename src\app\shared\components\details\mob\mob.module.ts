import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { AppCommonModule } from '@appModules/common/app.common.module';
// import { MsalService, MSAL_CONFIG, MSAL_CONFIG_ANGULAR } from '@azure/msal-angular';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { ApiErrorsModule } from '@appShared/components/common/api-errors/api-errors.module';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TypeaheadModule } from "ngx-bootstrap/typeahead";
import { MobComponent } from './mob.component';


@NgModule({
  declarations: [MobComponent],
  imports: [
    CommonModule,
    NgSelectModule,
    AppCommonModule,
    CommonModule,
    ApiErrorsModule,
    FormsModule,
    ReactiveFormsModule,
    TypeaheadModule.forRoot(),
    TooltipModule,
    PermissionsModule.forChild(),
    MarkAsTouchedOnFocusDirectiveModule
  ],
  exports:[
    MobComponent
  ]
})
export class MobModule { }

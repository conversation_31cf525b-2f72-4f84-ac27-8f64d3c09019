<div *ngIf="toggleDisplay">
  <div class="col-12 p-0">
    <div class="toast-top-page py-3">
      <strong>{{ messageBold }}</strong>{{ message }}
    </div>
  </div>
</div>
<div *ngIf="toggleDisplayForEdit_Update">
  <div class="col-12  p-0">
    <div class="toast-top-page-edit py-3">
      {{ edit_prefix_message }} <strong>{{ edit_update_messageBold }}</strong>{{ edit_update_message }}
    </div>
  </div>
</div>
<div *ngIf="offerPOD">
  <div class="col-12  p-0">
    <div class="toast-top-page py-3">
      <strong>This POD has been modified.</strong>
    </div>
  </div>
</div>
<div *ngIf="offerRedemption">
  <div class="col-12  p-0">
    <div class="toast-top-page py-3">
      <strong>This offer has been modified.</strong>
    </div>
  </div>
</div>
<div *ngIf="isRemovedUnClippedOn">
  <div class="col-12 p-0">
    <div class="toast-top-page-removed py-3">
      {{ removeUnclippedmessage }}
    </div>
  </div>
</div>
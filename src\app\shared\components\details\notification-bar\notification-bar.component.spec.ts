import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotificationBarComponent } from './notification-bar.component';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { BehaviorSubject } from 'rxjs';

describe('NotificationBarComponent', () => {
    let component: NotificationBarComponent;
    let fixture: ComponentFixture<NotificationBarComponent>;
    let requestFormService: jasmine.SpyObj<RequestFormService>;
    let offerMappingService: jasmine.SpyObj<OfferMappingService>;

    beforeEach(async () => {
        const requestFormServiceSpy = jasmine.createSpyObj('RequestFormService', [
            'isJustificationBoolean',
            'isEditNotificatonBoolean',
            'isUpdateNotificationBoolean',
            'requestEditUpdateData$',
            'isRemovedUnclippedOnBoolean',
            'isofferRequestRemovedBoolean'
        ]);
        const offerMappingServiceSpy = jasmine.createSpyObj('OfferMappingService', [
            'isUpdateOfferNotificationBoolean',
            'offerNotificationChanges$',
            'offerEditUpdateData$'
        ]);

        await TestBed.configureTestingModule({
            declarations: [NotificationBarComponent],
            providers: [
                { provide: RequestFormService, useValue: requestFormServiceSpy },
                { provide: OfferMappingService, useValue: offerMappingServiceSpy }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(NotificationBarComponent);
        component = fixture.componentInstance;
        requestFormService = TestBed.inject(RequestFormService) as jasmine.SpyObj<RequestFormService>;
        offerMappingService = TestBed.inject(OfferMappingService) as jasmine.SpyObj<OfferMappingService>;

        requestFormService.isJustificationBoolean = new BehaviorSubject(false);
        requestFormService.isEditNotificatonBoolean = new BehaviorSubject(false);
        requestFormService.isUpdateNotificationBoolean = new BehaviorSubject(false);
        requestFormService.requestEditUpdateData$ = new BehaviorSubject(null);
        requestFormService.isRemovedUnclippedOnBoolean = new BehaviorSubject(false);
        requestFormService.isofferRequestRemovedBoolean = new BehaviorSubject(false);
        offerMappingService.isUpdateOfferNotificationBoolean = new BehaviorSubject(false);
        offerMappingService.offerNotificationChanges$ = new BehaviorSubject(null);
        offerMappingService.offerEditUpdateData$ = new BehaviorSubject(null);
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize subscriptions on ngOnInit', () => {
        spyOn(component, 'initSubscribes');
        component.ngOnInit();
        expect(component.initSubscribes).toHaveBeenCalled();
    });

    it('should set toggleDisplay to true and set messages when isJustificationBoolean is true', () => {
        requestFormService.isJustificationBoolean.next(true);
        component.initSubscribes();
        expect(component.toggleDisplay).toBeTrue();
        expect(component.messageBold).toBe('This offer request is late. ');
        expect(component.message).toBe('Please review the justification.');
    });

    it('should set toggleDisplayForEdit_Update to true and set edit_update_message when isEditNotificatonBoolean is true', () => {
        component.initSubscribes();
        requestFormService.isEditNotificatonBoolean.next(true);
        expect(component.toggleDisplayForEdit_Update).toBeTrue();
        expect(component.edit_update_messageBold).toBe('Editing ');
        expect(component.edit_update_message).toBe('is in progress.');
    });

    it('should set toggleDisplayForEdit_Update to true and set edit_update_message when isUpdateNotificationBoolean is true', () => {
        component.initSubscribes();
        requestFormService.isUpdateNotificationBoolean.next(true);
        expect(component.toggleDisplayForEdit_Update).toBeTrue();
        expect(component.edit_update_messageBold).toBe('Updating ');
        expect(component.edit_update_message).toBe('is in progress.');
    });

    it('should unsubscribe from all subscriptions on destroyNotification', () => {
        spyOn(component.subs, 'unsubscribe');
        component.destroyNotification();
        expect(component.subs.unsubscribe).toHaveBeenCalled();
    });

    it('should set edit_update_message when offerEditUpdateData$ has editStatus E', () => {
        component.initSubscribes();
        offerMappingService.offerEditUpdateData$.next({
            editStatus: 'E',
            firstName: 'John',
            lastName: 'Doe'
        });
        expect(component.edit_prefix_message).toBe('The offer cannot be edited at this time. ');
        expect(component.edit_update_messageBold).toBe('John Doe ');
        expect(component.edit_update_message).toBe('is editing the offer request.');
    });

    it('should set edit_update_message when offerEditUpdateData$ has editStatus U', () => {
        component.initSubscribes();
        offerMappingService.offerEditUpdateData$.next({
            editStatus: 'U',
            firstName: 'Jane',
            lastName: 'Doe',
            editTs: 1609459200
        });
        expect(component.edit_prefix_message).toBe('');
        expect(component.edit_update_messageBold).toBe('Needs Review:  Jane Doe ');
        expect(component.edit_update_message).toBe('edited this offer request on 01/01/2021 .');
    });

    it('should set edit_update_message when requestEditUpdateData$ has nonDigitalEditStatus E', () => {
        component.initSubscribes();
        requestFormService.requestEditUpdateData$.next({
            nonDigitalEditStatus: {
                editStatus: 'E',
                firstName: 'John',
                lastName: 'Doe'
            },
            digitalEditStatus: null
        });
        expect(component.edit_update_messageBold).toBe('John Doe ');
        expect(component.edit_update_message).toBe('is editing the offer request.');
    });

    it('should set edit_update_message when requestEditUpdateData$ has digitalEditStatus E', () => {
        component.initSubscribes();
        requestFormService.requestEditUpdateData$.next({
            nonDigitalEditStatus: null,
            digitalEditStatus: {
                editStatus: 'E',
                firstName: 'Jane',
                lastName: 'Doe'
            }
        });
        expect(component.edit_update_messageBold).toBe('Jane Doe ');
        expect(component.edit_update_message).toBe('is editing the offer request.');
    });

    it('should set edit_update_message when requestEditUpdateData$ has nonDigitalEditStatus U', () => {
        component.initSubscribes();
        requestFormService.requestEditUpdateData$.next({
            nonDigitalEditStatus: {
                editStatus: 'U',
                firstName: 'John',
                lastName: 'Doe',
                editTs: 1609459200
            },
            digitalEditStatus: null
        });
        expect(component.edit_update_messageBold).toBe('Needs Review: John Doe ');
        expect(component.edit_update_message).toBe('edited this offer request on 01/01/2021 .');
    });

    it('should set edit_update_message when requestEditUpdateData$ has digitalEditStatus U', () => {
        component.initSubscribes();
        requestFormService.requestEditUpdateData$.next({
            nonDigitalEditStatus: null,
            digitalEditStatus: {
                editStatus: 'U',
                firstName: 'Jane',
                lastName: 'Doe',
                editTs: 1609459200
            }
        });
        expect(component.edit_update_messageBold).toBe('Needs Review: Jane Doe ');
        expect(component.edit_update_message).toBe('edited this offer request on 01/01/2021 .');
    });

    it('should set isEditNotificatonBoolean to false when requestEditUpdateData$ has no editStatus E or U', () => {
        component.initSubscribes();
        requestFormService.requestEditUpdateData$.next({
            nonDigitalEditStatus: {
                editStatus: 'C',
                firstName: 'John',
                lastName: 'Doe'
            },
            digitalEditStatus: {
                editStatus: 'C',
                firstName: 'Jane',
                lastName: 'Doe'
            }
        });
    });

    it('should set offerPOD when offerNotificationChanges$ has offerStatus PU', () => {
        component.initSubscribes();
        offerMappingService.offerNotificationChanges$.next({
            offerStatus: 'PU',
            podDetailsEditStatus: {
                editStatus: true
            },
            redemptionDetailsEditStatus: null
        });
        expect(component.offerPOD).toBeTrue();
    });

    it('should not set offerPOD when offerNotificationChanges$ has offerStatus not PU', () => {
        component.initSubscribes();
        offerMappingService.offerNotificationChanges$.next({
            offerStatus: 'XX',
            podDetailsEditStatus: {
                editStatus: true
            },
            redemptionDetailsEditStatus: null
        });
        expect(component.offerPOD).toBeFalse();
    });

    it('should set offerRedemption when offerNotificationChanges$ has offerStatus not I', () => {
        component.initSubscribes();
        offerMappingService.offerNotificationChanges$.next({
            offerStatus: 'XX',
            podDetailsEditStatus: null,
            redemptionDetailsEditStatus: {
                editStatus: true
            }
        });
        expect(component.offerRedemption).toBeTrue();
    });

    it('should not set offerRedemption when offerNotificationChanges$ has offerStatus I', () => {
        component.initSubscribes();
        offerMappingService.offerNotificationChanges$.next({
            offerStatus: 'I',
            podDetailsEditStatus: null,
            redemptionDetailsEditStatus: {
                editStatus: true
            }
        });
        expect(component.offerRedemption).toBeFalse();
    });

    it('should set removeUnclippedmessage when isRemovedUnclippedOnBoolean is true', () => {
        component.initSubscribes();
        requestFormService.isRemovedUnclippedOnBoolean.next(true);
        expect(component.isRemovedUnClippedOn).toBeTrue();
        expect(component.removeUnclippedmessage).toBe('This coupon has been removed from just for U® if it has not been clipped.');
    });

    it('should set removeUnclippedmessage when isofferRequestRemovedBoolean is true', () => {
        component.initSubscribes();
        requestFormService.isofferRequestRemovedBoolean.next(true);
        expect(component.isRemovedUnClippedOn).toBeTrue();
        expect(component.removeUnclippedmessage).toBe('The related offer request is being removed.');
    });
});
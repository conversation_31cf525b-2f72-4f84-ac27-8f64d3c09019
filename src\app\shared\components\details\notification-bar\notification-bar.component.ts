import { Component, Input, OnInit } from '@angular/core';
import { RequestFormService } from '@appRequestServices/request-form.service';
import * as moment from 'moment';

import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';


@Component({
  selector: "notification-bar",
  templateUrl: './notification-bar.component.html',
  styleUrls: ['./notification-bar.component.scss'],
})
export class NotificationBarComponent extends UnsubscribeAdapter
  implements OnInit {
  @Input() display: boolean;

  toggleDisplay: boolean = false;
  toggleDisplayForEdit_Update: boolean = false;
  offerEffectiveStartDate = null;
  formattedCreatedDate = null;
  differenceDate = null;
  message = null;
  removeUnclippedmessage = null;
  edit_prefix_message = null;
  edit_update_message = null;
  edit_update_messageBold = null;
  messageBold = null;
  offerPOD: boolean;
  offerRedemption: boolean;
  isRemovedUnClippedOn: boolean = false;
  constructor(
    public _requestFormService: RequestFormService,

    private _offerMappingService: OfferMappingService,

  ) {
    super();
  }

  ngOnInit() {
    this.initSubscribes();
    this._offerMappingService.destroyNotification = this.destroyNotification.bind(this);
  }

  initSubscribes() {
    this._requestFormService.isJustificationBoolean.subscribe((value) => {
      this.toggleDisplay = value;
      if (this.toggleDisplay) {
        this.toggleDisplay = true;
        this.messageBold = 'This offer request is late. ';
        this.message = 'Please review the justification.';
      }
    });
    this._requestFormService.isEditNotificatonBoolean.subscribe((value) => {
      this.toggleDisplayForEdit_Update = value
      if (this.toggleDisplayForEdit_Update) {
        this.edit_update_messageBold = 'Editing '
        this.edit_update_message = 'is in progress.'
      }
    });

    this._requestFormService.isUpdateNotificationBoolean.subscribe((value) => {
      this.toggleDisplayForEdit_Update = value
      if (this.toggleDisplayForEdit_Update) {
        this.edit_update_messageBold = 'Updating '
        this.edit_update_message = 'is in progress.'
      }
    });

    this._requestFormService.requestEditUpdateData$.subscribe((value) => {
      if (value) {
        const { digitalEditStatus, nonDigitalEditStatus } = value
        //If any of the status is E
        if ((nonDigitalEditStatus && nonDigitalEditStatus.editStatus === 'E') ||
          (digitalEditStatus && digitalEditStatus.editStatus === 'E')) {
          this._requestFormService.isEditNotificatonBoolean.next(true);

          if (nonDigitalEditStatus && nonDigitalEditStatus.editStatus === 'E') {
            //If nonDigitalEditStatus is E
            this.edit_update_messageBold = `${nonDigitalEditStatus.firstName} ${nonDigitalEditStatus.lastName} `
            this.edit_update_message = 'is editing the offer request.'
          } else if (digitalEditStatus && digitalEditStatus.editStatus === 'E') {
            //If digitalEditStatus is E
            this.edit_update_messageBold = `${digitalEditStatus.firstName} ${digitalEditStatus.lastName} `
            this.edit_update_message = 'is editing the offer request.'
          }

        } else if ((nonDigitalEditStatus && nonDigitalEditStatus.editStatus === 'U') ||
          (digitalEditStatus && digitalEditStatus.editStatus === 'U')) {
          //If any of the status is U

          this._requestFormService.isEditNotificatonBoolean.next(true);

          if (nonDigitalEditStatus && nonDigitalEditStatus.editStatus === 'U') {
            //If nonDigitalEditStatus is U
            this.edit_update_messageBold = `Needs Review: ${nonDigitalEditStatus.firstName} ${nonDigitalEditStatus.lastName} `
            this.edit_update_message = `edited this offer request on ${moment.unix(nonDigitalEditStatus.editTs).format('MM/DD/YYYY')} .`
          } else if (digitalEditStatus && digitalEditStatus.editStatus === 'U') {
            //If digitalEditStatus is U
            this.edit_update_messageBold = `Needs Review: ${digitalEditStatus.firstName} ${digitalEditStatus.lastName} `
            this.edit_update_message = `edited this offer request on ${moment.unix(digitalEditStatus.editTs).format('MM/DD/YYYY')} .`
          }
        } else {
          this._requestFormService.isEditNotificatonBoolean.next(false);
          this._requestFormService.isUpdateNotificationBoolean.next(false)
        }
      }
    })
    // subscribing the notifications for offer
    this._offerMappingService.isUpdateOfferNotificationBoolean.subscribe((value) => {
      this.toggleDisplayForEdit_Update = value
      if (this.toggleDisplayForEdit_Update) {
        this.edit_update_messageBold = 'Updating '
        this.edit_update_message = 'is in progress.'
      }

    });
    this.subs.sink =   this._offerMappingService.offerNotificationChanges$.subscribe((notification) => {
      this.offerPOD = false;
      this.offerRedemption = false;
      if (notification) {
        const { offerStatus, podDetailsEditStatus, redemptionDetailsEditStatus } = notification;
        if (offerStatus === 'PU') {
          this.offerPOD = podDetailsEditStatus && podDetailsEditStatus.editStatus;
        }
        if(offerStatus !=='I') {
          this.offerRedemption = redemptionDetailsEditStatus && redemptionDetailsEditStatus.editStatus;
        }
      }

    })
    this._offerMappingService.offerEditUpdateData$.subscribe((value) => {
      if (value) {
        if (value.editStatus && value.editStatus === 'E') {
          this._offerMappingService.isUpdateOfferNotificationBoolean.next(true);
          this.edit_prefix_message = 'The offer cannot be edited at this time. '
          this.edit_update_messageBold = `${value.firstName} ${value.lastName} `
          this.edit_update_message = 'is editing the offer request.'
        } else if (value.editStatus && value.editStatus === 'U') {
          this._offerMappingService.isUpdateOfferNotificationBoolean.next(true);
          this.edit_prefix_message = ''
          this.edit_update_messageBold = `Needs Review:  ${value.firstName} ${value.lastName} `
          this.edit_update_message = `edited this offer request on ${moment.unix(value.editTs).format('MM/DD/YYYY')} .`
        }
      }

    });
    this._requestFormService.isRemovedUnclippedOnBoolean.subscribe((value) => {
      this.isRemovedUnClippedOn = value;
      if (this.isRemovedUnClippedOn) {
        this.isRemovedUnClippedOn = true;
        this.removeUnclippedmessage = 'This coupon has been removed from just for U® if it has not been clipped.';
      }
    });
    this._requestFormService.isofferRequestRemovedBoolean.subscribe((value) => {
      this.isRemovedUnClippedOn = value;
      if (this.isRemovedUnClippedOn) {
        this.isRemovedUnClippedOn = true;
        this.removeUnclippedmessage = 'The related offer request is being removed.';
      }
    });
  }
  destroyNotification(){
    this.subs.unsubscribe();
  }
}

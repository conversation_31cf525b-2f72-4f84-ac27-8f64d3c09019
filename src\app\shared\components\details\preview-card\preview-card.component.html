<div [class.grTile] = 'programCodeSelected === CONSTANTS.GR' class="item-border pb-0 mb-4 d-flex flex-column" 
  *ngIf="previewData && programCodeSelected === CONSTANTS.GR"
  [ngClass]="{'reward-grid-item': programCodeSelected === CONSTANTS.GR }">
  <div class="card product-card ">
    <div class="card-body" [ngClass]="{'reward-wrapper': programCodeSelected === CONSTANTS.GR }">
      <div class="card-title h4 mb-4" [ngClass]="{'reward-type': programCodeSelected === CONSTANTS.GR, 'h4': programCodeSelected !== CONSTANTS.GR,
      'mb-4': programCodeSelected !== CONSTANTS.GR  }">
        <span style="display: inline-flex;
              align-items: center;">
          <span *ngIf="programCodeSelected!== CONSTANTS.GR" class="svg-image" [ngClass]="previewData.savingsValueText ? '' : 'opacity-text'">
          </span>
          <span *ngIf="previewData.savingsValueText">{{
            previewData.savingsValueText | slice: 0:20
          }}</span>
          <span *ngIf="
              previewData.savingsValueText && previewData.savingsValueText.length > 20
            ">...</span>
          <span *ngIf="!previewData.savingsValueText" class="opacity-text">
            Savings Value Text</span>
        </span>
      </div>
      <div class="row" [ngClass]="{'col-12': programCodeSelected === CONSTANTS.GR, 'reward-desc':  programCodeSelected === CONSTANTS.GR }">
          <div [ngClass]="{'col-8': programCodeSelected !== CONSTANTS.GR, 'reward-desc-text': programCodeSelected === CONSTANTS.GR }">
            <h6 [ngClass]="{'mb-2': programCodeSelected !== CONSTANTS.GR,  'mb-0': programCodeSelected === CONSTANTS.GR}">
              <span *ngIf="previewData.title" [ngClass]="{'reward-title': programCodeSelected === CONSTANTS.GR }">{{ previewData.title | slice: 0:43 }}
                <span class="font-size-body" *ngIf="programCodeSelected !== CONSTANTS.GR && previewData.title && previewData.title.length > 43">...</span>
              </span>
              <span *ngIf="!previewData.title" class="opacity-text font-size-body">
                Title Description</span>
            </h6>
            <p class="cpn-details mb-0 small-font" [ngClass]="{'reward-limit': programCodeSelected === CONSTANTS.GR }">
              <span *ngIf="previewData.prdDesc">{{ previewData.prdDesc | slice: 0:20 }}
                <span *ngIf="previewData.prdDesc && previewData.prdDesc.length > 20">...</span>
                <a class="ml-2 offer-details" (click)="onDetailsClick()"><u>{{tileDetails.detailsLabel}}</u></a>
              </span>
              <span *ngIf="!previewData.prdDesc" class="opacity-text">
                Product Description</span>

                <span *ngIf='programCodeSelected === CONSTANTS.GR' class="d-block" [ngClass]="{'reward-clip-data': programCodeSelected === CONSTANTS.GR }">
                  Clip by
                  {{ previewData.displayEndDate ? (previewData.displayEndDate | date: 'M/d/yyyy') : "End Date" }}
                </span>
            </p>
          </div>
          <div [ngClass]="{'reward-header-img': programCodeSelected === CONSTANTS.GR, 'col-4': programCodeSelected !== CONSTANTS.GR }">
            <img *ngIf="programCodeSelected !== CONSTANTS.GR" class="w-100  ml-sm-0 mr-sm-0" [src]="imageurl" [ngClass]="imageurl ? '' : 'no-image-class'"
              style="min-height: 100px;" alt="" />
            <img *ngIf="programCodeSelected === CONSTANTS.GR" class="reward-img" [src]="imageurl" [ngClass]="imageurl ? '' : 'no-image-class'" alt="" />
          </div>
      </div>
      <div class="col p-0 cpn-flex-area" style="display: inline-flex;" [ngClass] = "{'justify-content-center' : programCodeSelected === CONSTANTS.GR,
              'reward-use-btn-container': programCodeSelected === CONSTANTS.GR}">
        <button type="button" class="btn   card-add-button"  [ngClass]= "{'btn-outline-primary w-auto mr-0': programCodeSelected === CONSTANTS.GR, 
          'btn-primary': programCodeSelected !== CONSTANTS.GR, 'reward-use-btn' : programCodeSelected === CONSTANTS.GR }">
          {{tileDetails.ctaLabel}}
        </button>
        <p *ngIf= "tileDetails.isDisplayUsageLimit" class="expiration card-expiration ml-md-4 mb-md-0 mb-3 small-font" [ngClass]="previewData.endDate
              ? ''
              : 'opacity-text'">
          {{
            offerTypes[previewData.usageText]
              ? offerTypes[previewData.usageText]
              : "Offer Limits"
          }}<br/>Expires
          {{ previewData.endDate ? previewData.endDate : "End Date" }}
        </p>
      </div>
    </div>
  </div>
</div>
<app-sc-spd-pod-coupon *ngIf="showScSPDoupon" 
  [previewData]="previewData"
  [imageurl]="imageurl"
  [tileDetails]="tileDetails"
  (onDetailsClick)="onDetailsClick()"></app-sc-spd-pod-coupon>

<ng-template #offerDetailTmpl>
  <div class="modal-header border-bottom-0 pb-0">
    <div class="container-fluid">
      <div class="row p-3 position-relative">
        <img src="assets/icons/grey-close-icon.svg" 
          class="mb-1 position-absolute close-icon cursor-pointer" 
          alt="close" (click)="modalRef.hide()">
      </div>
      <div class="col">
        <h3 class="font-weight-light">Offer Detail</h3>
      </div>
    </div>
  </div>
  <div class="modal-body p-0">
    <div class="container-fluid">
      <div class="row">
        <div class="container-fluid">
          <offer-details-container  [offerData]="offerData" [offersArray]="offersArray" [isOfferPage]="isOfferPage"></offer-details-container>
        </div>
      </div>
    </div>
  </div>
</ng-template>
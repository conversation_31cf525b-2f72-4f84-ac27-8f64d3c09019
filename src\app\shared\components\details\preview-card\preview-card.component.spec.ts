import { ComponentFixture, TestBed } from '@angular/core/testing';
import { APP_BASE_HREF } from '@angular/common';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { PreviewCardComponent } from './preview-card.component';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { CommonService } from '@appServices/common/common.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { CONSTANTS } from '@appConstants/constants';

describe('PreviewCardComponent', () => {
    let component: PreviewCardComponent;
    let fixture: ComponentFixture<PreviewCardComponent>;
    let mockRouter: any;
    let mockOfferMappingService: any;
    let mockInitialDataService: any;
    let mockCommonService: any;
    let mockCommonRouteService: any;
    let mockOfferRequestBaseService: any;
    let mockModalService: any;

    beforeEach(async () => {
        mockRouter = {
            events: new Subject(),
            navigate: jasmine.createSpy('navigate'),
        };
        mockOfferMappingService = {
            podSource: new Subject(),
            podDetailsForm: { value: null, controls: {} },
        };
        mockInitialDataService = {
            getConfigUrls: jasmine.createSpy('getConfigUrls').and.returnValue('mockUrl'),
            getAppData: jasmine.createSpy('getAppData').and.returnValue({ podUsageLimits: {} }),
        };
        mockCommonService = {
            offerData$: new Subject(),
        };
        mockCommonRouteService = {
            get isBpdReqPage() {
                return false;
            },
        };
        mockOfferRequestBaseService = {
            facetItemService$: { programCodeSelected: CONSTANTS.GR },
        };
        mockModalService = {
            show: jasmine.createSpy('show'),
        };

        TestBed.configureTestingModule({
            declarations: [PreviewCardComponent],
            providers: [
                { provide: APP_BASE_HREF, useValue: '/' },
                HttpClient,
                HttpHandler,
                { provide: Router, useValue: mockRouter },
                { provide: OfferMappingService, useValue: mockOfferMappingService },
                { provide: InitialDataService, useValue: mockInitialDataService },
                { provide: CommonService, useValue: mockCommonService },
                { provide: CommonRouteService, useValue: mockCommonRouteService },
                { provide: OfferRequestBaseService, useValue: mockOfferRequestBaseService },
                { provide: BsModalService, useValue: mockModalService },
                { provide: BsModalRef, useValue: {} },
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(PreviewCardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize subscriptions on init', () => {
        spyOn(component, 'initSubscribes');
        spyOn(component, 'getPodDetails');

        component.ngOnInit();

        expect(component.initSubscribes).toHaveBeenCalled();
        expect(component.getPodDetails).toHaveBeenCalled();
    });

    it('should open modal on onDetailsClick if not request or template page', () => {
        component.isRequestPage = false;
        component.isTemplatePage = false;
        component.onDetailsClick();

        expect(mockModalService.show).toHaveBeenCalled();
    });

    it('should not open modal on onDetailsClick if request or template page', () => {
        component.isRequestPage = true;
        component.isTemplatePage = false;
        component.onDetailsClick();

        expect(mockModalService.show).not.toHaveBeenCalled();
    });

    it('should return correct isDisplayDetailsLink value', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/request');
        component.programCodeSelected = CONSTANTS.GR;

        expect(component.isDisplayDetailsLink).toBeTrue();
    });

    it('should set POD details from offer', () => {
        const podDetails = {
            savingsValueText: 'Save $10',
            headLine: 'Headline',
            prodDsc1: 'Description',
            productImageId: '123',
            displayEffectiveEndDate: '2023-12-31T00:00:00',
        };
        component.PODDetails = podDetails;

        component.setPodDetailsFromOffer();

        expect(component.previewData.savingsValueText).toEqual('Save $10');
        expect(component.previewData.title).toContain('Headline');
        expect(component.previewData.prdDesc).toEqual('Description');
        expect(component.previewData.imageID).toEqual('123');
    });

    it('should set usage text correctly', () => {
        component.PODDetails = { podUsageLimit: 'LIMITED' };
        component.configData = { podUsageLimits: { LIMITED: 'Limited' } };

        component.setUsageText('podUsageLimit');

        expect(component.previewData.usageText).toEqual('Limited use');
    });

    xit('should handle preview card for template correctly', () => {
        const podDetailsValue = {
            headline1: 'Headline 1',
            headLine2: 'Headline 2',
            scene7ImageId: 'image123',
            priceText: '$10',
            offerDescription: 'Offer Description',
            displayEndDate: '2023-12-31T00:00:00',
            podUsageLimit: 'LIMITED',
        };

        component.configData = { podUsageLimits: { LIMITED: 'Limited' } };
        spyOn(component, 'setCommonPodValues');

        component.handlePreviewCardForTemplate(podDetailsValue);

        expect(component.isTemplatePage).toBeTrue();
        expect(component.PODDetails).toEqual(podDetailsValue);
        expect(component.previewData.title).toEqual('Headline 1 Headline 2');
        expect(component.previewData.imageID).toEqual('image123');
        expect(component.setCommonPodValues).toHaveBeenCalled();
    });

    it('should set POD details from offer data correctly', () => {
        const offerData = {
            savingsValueText: 'Save $20',
            productImageId: 'image456',
            headLine: 'Main Headline',
            prodDsc1: 'Product Description',
            displayEffectiveEndDate: '2023-11-30T00:00:00',
            offerProgramCode: CONSTANTS.GR,
            pointsRequired: 100,
            headLine2: 'Secondary Headline',
            podUsageLimitTypePerUser: 'LIMITED',
        };

        component.setPodDetailsFromOfferData(offerData);

        expect(component.programCodeSelected).toEqual(CONSTANTS.GR);
        expect(component.PODDetails.savingsValueText).toEqual('Save $20');
        expect(component.PODDetails.productImageId).toEqual('image456');
        expect(component.PODDetails.headLine).toEqual('Main Headline');
        expect(component.PODDetails.prodDsc1).toEqual('Product Description');
        expect(component.PODDetails.displayEffectiveEndDate).toEqual('2023-11-30T00:00:00');
        expect(component.PODDetails.offerProgramCode).toEqual(CONSTANTS.GR);
        expect(component.PODDetails.pointsRequired).toEqual(100);
        expect(component.PODDetails.headLine2).toEqual('Secondary Headline');
        expect(component.PODDetails.podUsageLimitTypePerUser).toEqual('LIMITED');
    });

    it('should set POD details from sources correctly', () => {
        const mockOfferData = {
            info: {
                description: {
                    headLine: 'Headline',
                    headLine2: 'Secondary Headline',
                    productDescription: { prodDsc1: 'Product Description' },
                    savingsValueText: 'Save $15',
                },
                productImageId: 'image789',
                offerProgramCode: CONSTANTS.GR,
            },
            rules: {
                usageLimits: { podUsageLimitTypePerUser: 'LIMITED' },
                endDate: {
                    offerEffectiveEndDate: '2023-10-31T00:00:00',
                    displayEffectiveEndDate: '2023-11-30T00:00:00',
                },
                pointsRequired: 200,
            },
        };

        const mockPodDetailsFormValue = {
            savingsValueText: 'Save $10',
            productImageId: 'image123',
            headLine: 'Headline',
            headLine2: 'Secondary Headline',
            prodDsc1: 'Description',
            displayEffectiveEndDate: '2023-12-31T00:00:00',
            offerProgramCode: CONSTANTS.GR,
            pointsRequired: 150,
            podUsageLimitTypePerUser: 'LIMITED',
        };

        mockOfferMappingService.podDetailsForm.value = mockPodDetailsFormValue;
        component.offerData = mockOfferData;

        spyOn(component, 'setPodDetailsFromOfferData');
        spyOn(component, 'initOfferPodSubscribes');

        component.setPodDetailsFromSources();

        expect(component.offerEndDate).toEqual('2023-10-31T00:00:00');
        expect(component.PODDetails).toEqual(mockPodDetailsFormValue);
        expect(component.programCodeSelected).toEqual(CONSTANTS.GR);
        expect(component.setPodDetailsFromOfferData).not.toHaveBeenCalled();
        expect(component.initOfferPodSubscribes).toHaveBeenCalled();
    });

    it('should initialize request POD subscriptions correctly', () => {
        const mockPodDetails = { headline: 'Test Headline' };
        component.storeGroupVersion = {
            controls: {
                podDetails: {
                    valueChanges: new Subject(),
                },
            },
        };

        spyOn(component.storeGroupVersion.controls.podDetails.valueChanges, 'subscribe').and.callThrough();
        spyOn(component, 'setPodDetailsFromReq');

        component.initReqPodSubscribes();

        component.storeGroupVersion.controls.podDetails.valueChanges.next(mockPodDetails);

        expect(component.storeGroupVersion.controls.podDetails.valueChanges.subscribe).toHaveBeenCalled();
        expect(component.PODDetails).toEqual(mockPodDetails);
        expect(component.setPodDetailsFromReq).toHaveBeenCalled();
    });

    it('should initialize offer POD subscriptions correctly', () => {
        const mockPodDetails = { headline: 'Offer Headline' };
        mockOfferMappingService.podSource = new Subject();

        spyOn(mockOfferMappingService.podSource, 'subscribe').and.callThrough();
        spyOn(component, 'setPodDetailsFromOffer');

        component.initOfferPodSubscribes();

        mockOfferMappingService.podSource.next(mockPodDetails);

        expect(mockOfferMappingService.podSource.subscribe).toHaveBeenCalled();
        expect(component.PODDetails).toEqual(mockPodDetails);
        expect(component.setPodDetailsFromOffer).toHaveBeenCalled();
    });

    it('should initialize template POD subscriptions correctly', () => {
        const mockPodDetails = { headline: 'Template Headline' };
        component.storeGroupVersion = {
            controls: {
                podDetails: {
                    valueChanges: new Subject(),
                },
            },
        };

        spyOn(component.storeGroupVersion.controls.podDetails.valueChanges, 'subscribe').and.callThrough();
        spyOn(component, 'setPodDetailsFromTemplate');

        component.initTemplatePodSubscribes();

        component.storeGroupVersion.controls.podDetails.valueChanges.next(mockPodDetails);

        expect(component.storeGroupVersion.controls.podDetails.valueChanges.subscribe).toHaveBeenCalled();
        expect(component.PODDetails).toEqual(mockPodDetails);
        expect(component.setPodDetailsFromTemplate).toHaveBeenCalled();
    });

    it('should set POD details from request correctly', () => {
        const mockPodDetails = {
            headline1: 'Request Headline 1',
            headLine2: 'Request Headline 2',
            scene7ImageId: 'imageReq123',
            priceText: '$25',
            offerDescription: 'Request Offer Description',
            displayEndDate: '2023-12-31T00:00:00',
            podUsageLimit: 'LIMITED',
        };

        component.PODDetails = mockPodDetails;
        spyOnProperty(mockCommonRouteService, 'isBpdReqPage', 'get').and.returnValue(false);
        component.programCodeSelected = CONSTANTS.GR;

        spyOn(component, 'setCommonPodValues');
        spyOn(component, 'setPodDetailsForGr');

        component.setPodDetailsFromReq();

        expect(component.previewData.title).toEqual('Request Headline 1 Request Headline 2');
        expect(component.previewData.imageID).toEqual('imageReq123');
        expect(component.setCommonPodValues).toHaveBeenCalled();
        expect(component.setPodDetailsForGr).toHaveBeenCalled();
    });

    it('should set POD details from template correctly', () => {
        const mockPodDetails = {
            headline1: 'Template Headline 1',
            headLine2: 'Template Headline 2',
            scene7ImageId: 'imageTemplate123',
            priceText: '$30',
            offerDescription: 'Template Offer Description',
            displayEndDate: '2023-12-31T00:00:00',
            podUsageLimit: 'LIMITED',
        };

        component.PODDetails = mockPodDetails;

        spyOn(component, 'setCommonPodValues');

        component.setPodDetailsFromTemplate();

        expect(component.previewData.title).toEqual('Template Headline 1 Template Headline 2');
        expect(component.previewData.imageID).toEqual('imageTemplate123');
        expect(component.setCommonPodValues).toHaveBeenCalled();
    });

    it('should set common POD values correctly', () => {
        const mockPodDetails = {
            priceText: '$50',
            offerDescription: 'Common Offer Description',
            displayEndDate: null,
            podUsageLimit: 'LIMITED',
        };

        component.PODDetails = mockPodDetails;
        component.configData = { podUsageLimits: { LIMITED: 'Limited' } };
        spyOn(component, 'setUsageText');

        component.setCommonPodValues();

        expect(component.previewData.savingsValueText).toEqual('$50');
        expect(component.previewData.prdDesc).toEqual('Common Offer Description');
        expect(component.imageurl).toContain('imgNotFound.svg');
        expect(component.setUsageText).toHaveBeenCalledWith('podUsageLimit');
    });

    it('should set tile details for GR program code correctly', () => {
        component.programCodeSelected = CONSTANTS.GR;
        component.PODDetails = { pointsRequired: 100 };
        component.rewardsRequired = 50;

        component.setPodDetailsForGr();

        expect(component.tileDetails.ctaLabel).toEqual('Use 100 Points');
        expect(component.tileDetails.isDisplayUsageLimit).toBeFalse();
        expect(component.tileDetails.detailsLabel).toEqual('Reward Details');
    });

    it('should set tile details for GR program code with rewardsRequired fallback', () => {
        component.programCodeSelected = CONSTANTS.GR;
        component.PODDetails = { pointsRequired: null };
        component.rewardsRequired = 50;

        component.setPodDetailsForGr();

        expect(component.tileDetails.ctaLabel).toEqual('Use 50 Points');
        expect(component.tileDetails.isDisplayUsageLimit).toBeFalse();
        expect(component.tileDetails.detailsLabel).toEqual('Reward Details');
    });

    it('should set tile details for GR program code with no pointsRequired or rewardsRequired', () => {
        component.programCodeSelected = CONSTANTS.GR;
        component.PODDetails = { pointsRequired: null };
        component.rewardsRequired = null;

        component.setPodDetailsForGr();

        expect(component.tileDetails.ctaLabel).toEqual('Use 0 Points');
        expect(component.tileDetails.isDisplayUsageLimit).toBeFalse();
        expect(component.tileDetails.detailsLabel).toEqual('Reward Details');
    });

    it('should not set tile details if program code is not GR', () => {
        component.programCodeSelected = CONSTANTS.SC;
        component.PODDetails = { pointsRequired: 100 };
        component.rewardsRequired = 50;

        const initialTileDetails = { ...component.tileDetails };

        component.setPodDetailsForGr();

        expect(component.tileDetails).toEqual(initialTileDetails);
    });

    it('should not open modal on onDetailsClick if isRequestPage is true', () => {
        component.isRequestPage = true;
        component.isTemplatePage = false;

        component.onDetailsClick();

        expect(mockModalService.show).not.toHaveBeenCalled();
    });

    it('should not open modal on onDetailsClick if isTemplatePage is true', () => {
        component.isRequestPage = false;
        component.isTemplatePage = true;

        component.onDetailsClick();

        expect(mockModalService.show).not.toHaveBeenCalled();
    });

    it('should return false for isDisplayDetailsLink if programCodeSelected is not GR', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/request');
        component.programCodeSelected = CONSTANTS.SC;

        expect(component.isDisplayDetailsLink).toBeFalse();
    });

    it('should handle getPodDetails when offerRequestRouteFilter is not empty', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/request');
        component.storeGroupVersion = {
            get: jasmine.createSpy('get').and.returnValue({
                value: { podDetails: 'mockPodDetails' },
            }),
        };
        spyOn(component, 'setPodDetailsFromReq');
        spyOn(component, 'initReqPodSubscribes');

        component.getPodDetails();

    });

    it('should handle getPodDetails when offerTemplateRouteFilter is not empty', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/template');
        component.storeGroupVersion = {
            get: jasmine.createSpy('get').and.returnValue({
                value: { podDetails: 'mockPodDetails' },
            }),
        };
        spyOn(component, 'handlePreviewCardForTemplate');

        component.getPodDetails();
    });

    it('should handle getPodDetails when offerRouteFilter is not empty', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/offer');
        spyOn(component, 'setPodDetailsFromSources');
        spyOn(component, 'setPodDetailsFromOffer');

        component.getPodDetails();

    });

    it('should not set POD details for GR if programCodeSelected is not GR', () => {
        component.programCodeSelected = CONSTANTS.SC;
        component.PODDetails = { pointsRequired: 100 };

        spyOn(component, 'setPodDetailsForGr').and.callThrough();

        component.setPodDetailsForGr();

        expect(component.tileDetails.ctaLabel).not.toEqual('Use 100 Points');
        expect(component.tileDetails.detailsLabel).not.toEqual('Reward Details');
    });

    it('should set usage text to empty if usageKey is not found in PODDetails', () => {
        component.PODDetails = {};
        component.configData = { podUsageLimits: { LIMITED: 'Limited' } };

        component.setUsageText('nonExistentKey');

        expect(component.previewData.usageText).toEqual('');
    });

    it('should handle getPodDetails when offerRequestRouteFilter is not empty and podDetailsValue exists', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/request');
        component.storeGroupVersion = {
            get: jasmine.createSpy('get').and.returnValue({
                value: { podDetails: 'mockPodDetails' },
            }),
        };
        spyOn(component, 'setPodDetailsFromReq');
        spyOn(component, 'initReqPodSubscribes');

        component.getPodDetails();

    });

    it('should handle getPodDetails when offerTemplateRouteFilter is not empty and podDetailsValue exists', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/template');
        component.storeGroupVersion = {
            get: jasmine.createSpy('get').and.returnValue({
                value: { podDetails: 'mockPodDetails' },
            }),
        };
        spyOn(component, 'handlePreviewCardForTemplate');

        component.getPodDetails();
    });

    it('should handle getPodDetails when offerRouteFilter is not empty', () => {
        spyOn(component, 'getHref').and.returnValue('mockUrl/offer');
        spyOn(component, 'setPodDetailsFromSources');
        spyOn(component, 'setPodDetailsFromOffer');

        component.getPodDetails();
    });
});
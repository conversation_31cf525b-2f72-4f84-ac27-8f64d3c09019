import { APP_BASE_HREF } from '@angular/common';
import { Component, Inject, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { OfferRequestBaseService } from '@appRequestServices/offer-request-base.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonService } from '@appServices/common/common.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { mmDdYyyySlash_DateFormatWithoutUTC } from '@appUtilities/date.utility';
import { nullCheckProperty } from '@appUtilities/nullCheck.utility';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter } from 'rxjs/operators';
@Component({
  selector: "preview-card",
  templateUrl: './preview-card.component.html',
  styleUrls: ['./preview-card.component.scss'],
})
export class PreviewCardComponent extends UnsubscribeAdapter implements OnInit {
  @Input() valueChange;
  @Input() imageID: string;
  @Input() rewardsRequired;
  @Input() productVersionIndex;
  @Input() storeGroupVersion;
  @Input() offersArray;
  @Input() isOfferPage;
  @Input() showPoints = true;
  PODDetails: any;
  previewData: any = {};
  configData: any = {};
  imageurl;
  getImageAPI: string = this._apiConfigService.getConfigUrls(CONSTANTS.GET_IMAGE_API);
  modalRef: BsModalRef;
  isTemplatePage: boolean = false;
  @ViewChild('offerDetailTmpl')
  private _offerDetailTmpl: TemplateRef<any>;
  offerData = null;
  currentActiveRoute: any;
  offerEndDate: any;
  offerRequestCreateEditUrl = [
    `${ROUTES_CONST.REQUEST.Create}`,
    `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.Edit}`,
    `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.SPDEdit}`,
    `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.GREdit}`,
    `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.RequestForm}/${ROUTES_CONST.REQUEST.BPDEdit}`,
  ];
  offerTemplateEditUrl = [
    `${ROUTES_CONST.TEMPLATES.Template}/${ROUTES_CONST.TEMPLATES.TemplateForm}/${ROUTES_CONST.TEMPLATES.BPDEdit}`
  ]
  offerEditSummaryUrls = [
    `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Edit}`,
    `${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Summary}`
  ]
  tileDetails = {
    ctaLabel:"Clip Coupon",
    detailsLabel: 'Offer Details',
    isDisplayUsageLimit : true
  };
  programCodeSelected: string;
  isRequestPage: boolean;
  CONSTANTS = CONSTANTS;

  constructor(
    @Inject(APP_BASE_HREF) private baseHref: string,
    private _mappingService: OfferMappingService,
    private router: Router,
    private _apiConfigService: InitialDataService,
    public commonService: CommonService,
    private _modalService: BsModalService,
    public commonRouteService : CommonRouteService, 
    public offerRequestBaseService:OfferRequestBaseService
  ) {
    super();
  }

  urlImagePath: string = this._apiConfigService.getConfigUrls(CONSTANTS.GET_IMAGE_API) + '/';
  urlParams = '?$ecom-product-card-desktop-jpg$';
  offerTypes = { 'One-Time use': 'One time use', 'Unlimited use': 'Unlimited use' };

  ngOnInit() {
    this.configData = this._apiConfigService.getAppData();
    this.initSubscribes();
    this.getPodDetails();
  }
  initSubscribes() {
    
    this.subs.sink = this.commonService.offerData$.subscribe((data) => {
      if (data) {
        this.offerData = data;
      }
    });
    this.subs.sink = this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
      if (event.url.includes(ROUTES_CONST.OFFERS.PodDetails)) {
        this.initOfferPodSubscribes();
      }
    });
  }
  get isDisplayDetailsLink() {
    let url = this.getHref(), isDisplayDetailsLink = true;
    let isReqpage =  url.includes('request');
    if(isReqpage){
      isDisplayDetailsLink = this.programCodeSelected===CONSTANTS.GR;
    }

    return isDisplayDetailsLink;
    
  }
  get showScSPDoupon () {
    return [CONSTANTS.BPD, CONSTANTS.SPD, CONSTANTS.SC, CONSTANTS.MF].includes(this.programCodeSelected) || this.isTemplatePage;
  }
  onDetailsClick() {
    
    if(this.isRequestPage || this.isTemplatePage){
      return false;
    }
    
    this.openModal(this._offerDetailTmpl, {
      keyboard: true,
      class: "offer-detail-modal modal-xl",
      backdrop : 'static'
    });
  }
  openModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
  }

  getHref() {
    // this is so we can stub this in the tests to control the path
    return location.href;
  }

  getPodDetails() {
    const href = this.getHref();
    const podDetailsValue = this.storeGroupVersion?.get('podDetails')?.value;
    const offerRequestRouteFilter = this.offerRequestCreateEditUrl.filter(ele => href?.includes(ele));
    const offerTemplateRouteFilter = this.offerTemplateEditUrl.filter(ele => href?.includes(ele));
    const offerRouteFilter = this.offerEditSummaryUrls.filter(ele => href.includes(ele));
     
    if(offerRequestRouteFilter?.length && podDetailsValue) {
        this.isRequestPage = true;
        this.PODDetails = podDetailsValue;
        this.setPodDetailsFromReq();
        this.initReqPodSubscribes();
    } else if(offerTemplateRouteFilter?.length && podDetailsValue) {
      this.handlePreviewCardForTemplate(podDetailsValue);
    } else if(offerRouteFilter?.length) {
      this.setPodDetailsFromSources();
      this.setPodDetailsFromOffer();
    }

  }

  handlePreviewCardForTemplate(podDetailsValue) {
    this.isTemplatePage = true;
    this.PODDetails = podDetailsValue
    this.setPodDetailsFromTemplate();  
    this.initTemplatePodSubscribes();
  }


  setPodDetailsFromOffer() {
    if (this.PODDetails) {
      this.previewData.savingsValueText = this.PODDetails['savingsValueText'];
      this.previewData.title = this.PODDetails['headLine'] || '';
      this.previewData.prdDesc = this.PODDetails['prodDsc1'];
      this.previewData.imageID = this.PODDetails['productImageId'];
      const effectiveEndDate = this.PODDetails?.['displayEffectiveEndDate'];
      this.previewData.displayEndDate = effectiveEndDate ? (toString.call(effectiveEndDate) === '[object Date]' ? moment(effectiveEndDate)?.format('MM/DD/YYYY') :
        moment(this.PODDetails['displayEffectiveEndDate']?.replace(/T.*/, '')).format('MM/DD/YYYY')) : null;
      this.previewData.endDate = this.offerEndDate;
      this.previewData.endDate = this.previewData.endDate ?  
          moment(this.previewData.endDate.replace(/T.*/, '')).format('MM/DD/YYYY') : null;

      this.setUsageText('podUsageLimitTypePerUser');

      const headLine2 = this.PODDetails.headLine2 || this.PODDetails.headline2;
      this.previewData.title = headLine2 ? `${this.previewData.title} ${headLine2}` : `${this.previewData.title}`;
      this.imageurl = this.previewData.imageID
        ? this.getImageAPI + '/' + this.previewData.imageID + '?$ecom-product-card-desktop-jpg$'
        : `${this.baseHref}assets/img/imgNotFound.svg`;
        this.setPodDetailsForGr();
    }
  }

  setPodDetailsFromOfferData(obj) {
    const { savingsValueText, productImageId, headLine, prodDsc1, displayEffectiveEndDate, offerProgramCode,
          pointsRequired,headLine2 , podUsageLimitTypePerUser} = obj;

    this.programCodeSelected = offerProgramCode;
    this.PODDetails = {
      savingsValueText,
      productImageId,
      headLine,
      headLine2,
      prodDsc1,
      displayEffectiveEndDate,
      offerProgramCode,
      pointsRequired,
      podUsageLimitTypePerUser
    };
  }

  setPodDetailsFromSources() {
    let { info, rules } = this.offerData;
   
    let {
      usageLimits: { podUsageLimitTypePerUser },
      endDate: {offerEffectiveEndDate, displayEffectiveEndDate}, pointsRequired
    } = rules;
    let { description, productImageId, offerProgramCode } = info;
    this.offerEndDate = offerEffectiveEndDate
    
    if (nullCheckProperty(this._mappingService, 'podDetailsForm.value')) {
      this.PODDetails = this._mappingService.podDetailsForm.value;
      this.programCodeSelected = offerProgramCode;
      this.initOfferPodSubscribes();
    } else {
       let { headLine,headLine2, productDescription, savingsValueText } = description,
        { prodDsc1 = null } = productDescription|| { prodDsc1 : null };
      if (!productDescription) {
        productDescription = { prodDsc1: '' };
      }
      const obj = { savingsValueText, productImageId, headLine,headLine2 , prodDsc1, displayEffectiveEndDate,
         offerProgramCode,pointsRequired , podUsageLimitTypePerUser };
      this.setPodDetailsFromOfferData(obj);
    }

  }

  initReqPodSubscribes() {
    this.subs.sink = this.storeGroupVersion.controls.podDetails.valueChanges.subscribe((data) => {
      if (data) {
        this.PODDetails = data;
        this.setPodDetailsFromReq();
      }
    });
  }

  initOfferPodSubscribes() {
    this.subs.sink = this._mappingService.podSource.subscribe((data) => {
      if (data) {
        this.PODDetails = data;
        this.setPodDetailsFromOffer();
      }
    });
  }
  initTemplatePodSubscribes() {
    this.subs.sink = this.storeGroupVersion.controls.podDetails.valueChanges.subscribe((data) => {
      if (data) {
        this.PODDetails = data;
        this.setPodDetailsFromTemplate();
      }
    });
  }
  setPodDetailsForGr(){
    if(this.programCodeSelected !== CONSTANTS.GR){
      return false;
    }
    let pointsRequired  = this.PODDetails.pointsRequired || this.rewardsRequired;
    pointsRequired =  pointsRequired ? pointsRequired : "";
    
    let  label = `Use ${pointsRequired || 0} Point${(pointsRequired || 0) !== 1 ? 's' : ''}`;
    
      let detailsObj = {
        ctaLabel : label,
        isDisplayUsageLimit: false,
        detailsLabel: "Reward Details",
      };
      this.tileDetails = Object.assign(this.tileDetails, detailsObj);
    }
  
  setPodDetailsFromTemplate() {
    if (this.PODDetails) {
        this.previewData.title = `${this.PODDetails['headline1']} ${(this.PODDetails['headLine2'] || this.PODDetails['headline2'])}`;
        this.previewData.imageID = this.PODDetails["scene7ImageId"];
        this.setCommonPodValues();
    }
  }
  setPodDetailsFromReq() {
    let pc = this.offerRequestBaseService.facetItemService$.programCodeSelected;
    this.programCodeSelected = pc;

    if (this.PODDetails) {

      if (pc === CONSTANTS.SC) {
        this.previewData.title = this.PODDetails['headline'];
      } else if ([CONSTANTS.GR, CONSTANTS.SPD].includes(pc) || this.commonRouteService.isBpdReqPage) {
        const headline2= this.PODDetails['headLine2'] || this.PODDetails['headline2'];
        this.previewData.title = headline2  ? `${this.PODDetails['headline1']} ${headline2}` : this.PODDetails['headline1'];
        this.previewData.imageID = this.PODDetails["scene7ImageId"];
        this.setPodDetailsForGr();
      }

      this.setCommonPodValues();
    }

  }
  setCommonPodValues() {
    this.previewData.savingsValueText = this.PODDetails['priceText'];
    this.previewData.prdDesc = this.PODDetails['offerDescription'];
    this.previewData.displayEndDate = this.previewData.endDate = this.PODDetails['displayEndDate'] ?
      mmDdYyyySlash_DateFormatWithoutUTC(this.PODDetails['displayEndDate']).toString() : null;
    this.imageurl = this.previewData.imageID
      ? this.getImageAPI + '/' + this.previewData.imageID + '?$ecom-product-card-desktop-jpg$'
      : `${this.baseHref}assets/img/imgNotFound.svg`;
    this.setUsageText('podUsageLimit');
      
  }

  setUsageText(usageKey){
    const usageVal = this.PODDetails[usageKey];
    this.previewData.usageText =  usageVal?
    `${this.configData.podUsageLimits[usageVal]} use`: '';
  }

}

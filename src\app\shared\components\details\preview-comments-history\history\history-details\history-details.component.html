<div class="history-body" *ngIf="sanitizedHistoryDetails">
    <!-- **********************1) Header Row with Count *************************************** -->
    <div class="row header-row">
            <div class="col-md-11 modal-page-heading">History ({{sanitizedHistoryDetails?.length || 0}})</div>
            <div class="col-md-1" aria-label="Close" (click)="modalClosed()">
                <span class="close-icon" aria-hidden="true">&times;</span>
            </div>
    </div>
    <!--***************************************************************************************** -->

    <!--********************** 2) Detail Row with changeset and summary events ********************** -->
    <div class="detail-rows">
            <div class="row sticky-header">
                <div class="col-md-6 text">Event</div>
                <div class="col-md-3 text">Before</div>
                <div class="col-md-3 text">After</div>
            </div>

            <div class="detail-row-changeset row-seperator" *ngFor="let history of sanitizedHistoryDetails; let i = index;">

                <!-- Audit info - Date time and user name display -->
                <div class="audit-data">
                       <span> {{ history.auditTime | date: "M/d/yy h:mm a" + ':'}} </span>
                       <span> {{ historyService.buildUserName(groupPage, history) }} </span> 
                </div>

                <!--  Summary Level Event -->
                <div class="changeset-item" *ngIf="(isORView || isOfferView) && history.auditMessage">
                    <strong> {{ history.auditMessage }} </strong>
                </div>
                <div class="changeset-item" *ngIf="history.auditAction">
                    <strong> {{ history.auditAction | titlecase }} {{groupPage}}</strong>
                </div>

                 <!-- ChangeSet Event -->
                <div *ngFor="let changesetItem of history.childHistoryArray | slice:0: history.showShowMoreOption ? 5 : history.childHistoryArray?.length || 0; let j = index;" class="row changeset-item">
                    <div class="col-md-6">
                        <span [innerHTML]="changesetItem.sanitizedKey"></span>
                        <span *ngIf="isConfigGroupView && isDisplayCount(changesetItem)">
                            <span><strong>({{displayCountValue(changesetItem)}}) :</strong></span>
                        </span>
                </div>
                    <div class="col-md-3">
                        <span class="format-text">{{ changesetItem.beforeValue }}</span>
                        <div *ngIf="isConfigGroupView && (this.groupPage !== 'Point Group')">
                            <a *ngIf="changesetItem.showMoreAtBefore" (click)="showMoreValues(i,j)" class="anchor-link-blue">{{ changesetItem.showMoreAtBefore === true? 'Show More' : ''}}</a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <span class="format-text">{{ changesetItem.afterValue }}</span>
                        <div *ngIf="isConfigGroupView && (this.groupPage !== 'Point Group')">
                            <a *ngIf="changesetItem.showMoreAtAfter" (click)="showMoreValues(i,j)" class="anchor-link-blue">{{ changesetItem.showMoreAtAfter === true? 'Show More' : ''}}</a>
                        </div>
                    </div>
                </div>

                <div *ngIf="history.childHistoryArray && history.childHistoryArray.length > 5" class="show-more-less-option changeset-item"> 
                   <span (click)="toggleAllRecords(i)">
                         {{ history.showShowMoreOption === true? 'Show More' : 'Show Less'}}
                        <em *ngIf="history.showShowMoreOption" class="arrow down"></em>
                        <em *ngIf="!history.showShowMoreOption" class="arrow up"></em>
                   </span>
                </div>
            </div>
    </div>
    <!--***************************************************************************************** -->
</div>
@import "../../../../../../../scss/colors";
@import "../../../../../../../scss/variables";

.history-body{
  padding-bottom: 30px;
}

.header-row {
  display: flex;
  vertical-align: middle;
  align-items: center;
  padding-left: 28px;
  padding-top: 7px;
     & .modal-page-heading {
          height: 32px;
          color: $grey-darker-hex;
          font-size: $header-font-size;
          letter-spacing: 0;
          line-height: 32px;
     }
     & .close-icon{
          height: 24px;
          width: 24px;
          font-size: 42px;
          cursor: pointer;
          color: $grey-darker-hex;
    }
}

.sticky-header {
  box-sizing: border-box;
  height: 31px;
  border: 1px solid $grey-lightest-hex;
  background-color: $grey-lightest-hex;
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  z-index: 1;
    & .text{
        height: 16px;
        color: $grey-dark-hex;
        font-size: $small-font-size;
        font-weight: 800;
        letter-spacing: 0;
        line-height: 16px;
    }
}

.detail-rows {
  padding-left: 45px;
  padding-right: 45px;
    & .audit-data {
      color: $grey-darkest-hex;
      font-size: $small-font-size;
      letter-spacing: 0;
      line-height: 16px;
      padding: 5px 0 5px 0;
    }
    & .detail-row-changeset{
      padding-top: 10px;
      padding-bottom: 10px;
    }
    & .changeset-item {
      height: auto;
      color: $grey-darkest-hex;
      font-family: Arial, Helvetica, sans-serif;
      font-size: 11px;
      letter-spacing: 0;
      line-height: 12px;
      padding-bottom: 10px;
      padding-top: 7px;
      word-break: break-word;
    }
    & .show-more-less-option{
      color: $grey-darker-hex;
      font-size: 11px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
      cursor: pointer;
      padding-bottom: 2px;
    }
    & .arrow {
      border: solid black;
      border-width: 0 1px 1px 0;
      display: inline-block;
      padding: 3px;
      transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
      cursor: pointer;
      margin-left: 3px;
    }
    & .up {
      transform: rotate(-135deg);
      -webkit-transform: rotate(-135deg);
      margin-bottom: -2px;
    }
    
    & .down {
      margin-bottom: 2px;
      transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
    }
}

.row-seperator {
  border-bottom: 1px solid $gray-header-background;
}
.format-text {
  white-space: pre-line;
}
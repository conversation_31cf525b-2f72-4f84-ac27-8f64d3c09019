import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HistoryDetailsComponent } from './history-details.component';
import { HistoryService } from '@appServices/common/history.service';
import { CommonService } from '@appServices/common/common.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { BehaviorSubject, of } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';
import { mmDdYyyySlash_DateFormat } from '@appUtilities/date.utility';
import * as moment from 'moment';
import HistoryUtils from '../history-utils';

describe('HistoryDetailsComponent', () => {
  let component: HistoryDetailsComponent;
  let fixture: ComponentFixture<HistoryDetailsComponent>;
  let historyService: jasmine.SpyObj<HistoryService>;
  let commonService: jasmine.SpyObj<CommonService>;
  let initialDataService: jasmine.SpyObj<InitialDataService>;

  beforeEach(async () => {
    const historyServiceSpy = jasmine.createSpyObj('HistoryService', ['getHistoryDetailsDataForOffers', 'getOROTHistoryDetailsById', 'getHistoryDetailsDataForGroups']);
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['eventDataSrc']);
    const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getAppData', 'getNutriTags']);

  
    initialDataServiceSpy.getNutriTags.and.returnValue([{ id: '1', name: 'NutriTag1' }, { id: '2', name: 'NutriTag2' }]);

    initialDataServiceSpy.getAppData.and.returnValue({
      offerType: { '1': 'Type1', '2': 'Type2' },
      amountTypes: { '1': 'Amount1', '2': 'Amount2' },
      offerLimitsGR: { '1': 'Limit1', '2': 'Limit2' },
      customerFriendlyProductCategories: { '1': 'Category1', '2': 'Category2' },
      offerStatuses: { '1': 'Status1', '2': 'Status2' },
      discountTypes: { '1': 'Discount1', '2': 'Discount2' },
      podUsageLimits: { '1': 'Usage1', '2': 'Usage2' },
      regions: [{ code: '1', name: 'Region1' }, { code: '2', name: 'Region2' }],
      offerRequestEditChangeReason:  {'1': 'test'},
      offerRequestGroups: [
        {
          groupDivisions: [
            { code: 'D001', name: 'Division 1' },
            { code: 'D002', name: 'Division 2' },
          ],
        },
        {
          groupDivisions: [
            { code: 'D003', name: 'Division 3' },
          ],
        },
      ],
    });

    await TestBed.configureTestingModule({
      declarations: [HistoryDetailsComponent],
      providers: [
        { provide: HistoryService, useValue: historyServiceSpy },
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: InitialDataService, useValue: initialDataServiceSpy }
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(HistoryDetailsComponent);
    component = fixture.componentInstance;
    historyService = TestBed.inject(HistoryService) as jasmine.SpyObj<HistoryService>;
    commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    initialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;



    historyService.getHistoryDetailsDataForOffers.and.returnValue(of([{ changeset: { key1: [{ key: '/nutriTags', value: '1,2' }] } }]));
    historyService.getOROTHistoryDetailsById.and.returnValue(of([{ changeset: { key1: [{ key: '/nutriTags', value: '1' }] } }]));
    historyService.getHistoryDetailsDataForGroups.and.returnValue(of([{ changeset: { key1: [{ key: '/nutriTags', value: '1' }] } }]));

    commonService.eventDataSrc = new BehaviorSubject<any>([{ eventCode: 'event1', eventName: 'Event 1', isHidden: false }]);
    fixture.detectChanges();
  });

  it('should create the HistoryDetailsComponent', () => {
    expect(component).toBeTruthy();
  });

  it('should set the history data when reqId is provided', () => {
    component.reqId = '123';
    component.ngOnChanges();
    expect(historyService.getOROTHistoryDetailsById).toHaveBeenCalledWith('123', 'OR');
    expect(component.flattenedHistoryDetails.length).toBeGreaterThan(0); // assuming data returns
  });

  it('should set history data for offers when offerId is provided', () => {
    component.offerId = '456';
    component.ngOnChanges();
    expect(historyService.getHistoryDetailsDataForOffers).toHaveBeenCalledWith('456');
    expect(component.flattenedHistoryDetails.length).toBeGreaterThan(0); // assuming data returns
  });

  it('should correctly sanitize key for edit/cancel history', () => {
    const mockHistoryDetail = { auditMessage: 'Canceled some action' };
    const sanitizedKey = component.setKeyForEditCancelHistory(mockHistoryDetail, 'Action');
    expect(sanitizedKey).toBe('Cancel');
  });

  it('should return formatted date when sanitizing value for ISO date', () => {
    const sanitizedValue = component.sanitizeValue('/someKey', '2025-04-24T00:00:00Z');
    expect(sanitizedValue).toBe(mmDdYyyySlash_DateFormat('2025-04-24T00:00:00Z'));
  });

  it('should return sanitized value based on offer type', () => {
    const sanitizedValue = component.getValueMapping('/someKey/Offer Type', '1');
    expect(sanitizedValue).toBe('Type1');
  });

  it('should map the nutriTags correctly', () => {
    component.nutriTags = [{ id: '1', name: 'NutriTag1' }, { id: '2', name: 'NutriTag2' }];
    const nutriTagNames = component.getNutritagNames('1,2');
    expect(nutriTagNames).toBe('NutriTag1, NutriTag2');
  });

  it('should process history details and sanitize them correctly', () => {
    component.flattenedHistoryDetails = [{ historyDetailFlatChangeset: [{ type: 'Added', key: '/nutriTags', value: '1' }] }];
    component.santizeHistoryDetails();
    expect(component.sanitizedHistoryDetails.length).toBeGreaterThan(0); // ensure sanitized data is processed
  });

  it('should properly hide/show modal when modalClosed is called', () => {
    component.historyDetailsmodalRef = { hide: jasmine.createSpy('hide') };
    component.modalClosed();
    expect(component.historyDetailsmodalRef.hide).toHaveBeenCalled();
  });

  it('should call ngOnInit and trigger ngOnChanges with the correct inputs', () => {
    component.reqId = '123';
    component.offerId = '456';
    component.ngOnInit();
    component.ngOnChanges();
    expect(historyService.getOROTHistoryDetailsById).toHaveBeenCalledWith('123', 'OR');
    expect(historyService.getHistoryDetailsDataForOffers).toHaveBeenCalledWith('456');
  });

  it('should handle empty auditMessage correctly in setKeyForEditCancelHistory', () => {
    const sanitizedKey = component.setKeyForEditCancelHistory({ auditMessage: '' }, 'Action');
    expect(sanitizedKey).toBeUndefined()
  });

  it('should handle invalid date format in sanitizeValue', () => {
    const sanitizedValue = component.sanitizeValue('/someKey', 'invalid-date');
    expect(sanitizedValue).toBe('invalid-date');  // Returns original value if invalid
  });

  it('should handle missing mapping key in getValueMapping', () => {
    const sanitizedValue = component.getValueMapping('/someKey/NonExistentKey', '123');
    expect(sanitizedValue).toBe('123');  // Assuming empty or default response for missing mappings
  });

  it('should not modify values if there are less than 10 items', () => {
    const smallBeforeValue = '1,2,3';
    const smallAfterValue = '1,2,3';
    const configBeforeValue = '4,5,6';
    const configAftereValue = '4,5,6';
    component.sanitizedHistoryDetails = [{
      childHistoryArray: [{
        showMoreAtBefore: true,
        showMoreAtAfter: true,
        beforeValue: smallBeforeValue,
        afterValue: smallAfterValue,
        configGroupIdsBefore: configBeforeValue,
        configGroupIdsAfter: configAftereValue
      }]
    }];

    component.showMoreValues(0, 0);
    expect(component.sanitizedHistoryDetails[0].childHistoryArray[0].beforeValue).toEqual(configBeforeValue);
    expect(component.sanitizedHistoryDetails[0].childHistoryArray[0].afterValue).toEqual(configAftereValue);
  });

  it('should handle modal closing when modalClosed is called', () => {
    component.historyDetailsmodalRef = { hide: jasmine.createSpy('hide') };
    component.modalClosed();
    expect(component.historyDetailsmodalRef.hide).toHaveBeenCalled();
  });

  it('should correctly sanitize history details for multiple child entries', () => {
    component.flattenedHistoryDetails = [{
      historyDetailFlatChangeset: [{ type: 'Added', key: '/nutriTags', value: '1,2' }]
    }];
    component.santizeHistoryDetails();
    expect(component.sanitizedHistoryDetails.length).toBeGreaterThan(0); // Ensure data is sanitized
  });

  it('should return the matching change item from changesSetList', () => {
    
    component.changesSetList = [
      'value1',
      'value2'
    ];

 
    const result = component.getChangeItemKey('key1');
    expect(result).toEqual('key1'); 

   
    const result2 = component.getChangeItemKey('key3');
    expect(result2).toBe('key3'); 
  });


  it('should return updated key with replaced values', () => {
    const result = component.getUpdatedKey('some/path/StoreGroupNames');
    expect(result).toBe('some/path');
  });

  it('should remove key from change set', () => {
    const changeSet = { key1: [{ key: 'StoreGroupNames/1', value: 'value1' }] };
    component.removeKeyFromChangeSet(changeSet, 'key1', 'StoreGroupNames/1');

    expect(changeSet.key1.length).toBe(0);
  });

  it('should not remove key if it does not exist in the change set', () => {
    const changeSet = { key1: [{ key: 'StoreGroupNames/1', value: 'value1' }] };
    component.removeKeyFromChangeSet(changeSet, 'key1', 'NonExistentKey');

    expect(changeSet.key1.length).toBe(1);
  });

  it('should call setHistoryDataForGroups if groupId is set in ngOnChanges', () => {
    spyOn(component, 'setHistoryDataForGroups');
    component.groupId = 'groupId';
    component.ngOnChanges();

    expect(component.setHistoryDataForGroups).toHaveBeenCalledWith('groupId');
  });

  it('should return region value based on regionId', () => {
    const regionId = '1';
    const result = component.getRegionValueForId(regionId);

    expect(result).toBe('1 Region1');
  });

  it('should format the printed message', () => {
    const message = 'Test |LINE| message';
    const result = component.formatPrintedMessage(message);

    expect(result).toBe('Test  message');
  });

  it('should return the correct value for change reason', () => {
    const result = component.getValueForChangeReason('EditKey', '1', 'ChangeReason');
    expect(result).toBe('test');
  });

  it('should return the event value for the given event code', () => {
    const event = { eventCode: '1', eventName: 'Event1', isHidden: false };
    component.events = [event];

    const result = component.setValueForEvents('1');
    expect(result).toBe('Event1');
  });

  it('should hide the modal when modalClosed is called', () => {
    const hideSpy = jasmine.createSpy('hide');
    component.historyDetailsmodalRef = { hide: hideSpy };
    component.modalClosed();
    expect(hideSpy).toHaveBeenCalled();
  });
  

  it('should increment the number in the key', () => {
    const result = component.incrementNumInKey('key1:2');
    expect(result).toBe('key2:2');
  });

  it('should remove the number in the key', () => {
    const result = component.removeNumInKey('key1:2');
    expect(result).toBe('key2');
  });

  it('should toggle all records visibility correctly', () => {
    component.sanitizedHistoryDetails = [{ showShowMoreOption: false }];
    component.toggleAllRecords(0);

    expect(component.sanitizedHistoryDetails[0].showShowMoreOption).toBe(true);
  });

  describe('getValueMapping', () => {
    it('should return sanitized value based on offer type', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Offer Type', '1');
      expect(sanitizedValue).toBe('Type1');
    });
  
    it('should return sanitized value based on benefit type', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Benefit Type', '1');
      expect(sanitizedValue).toBe('Amount1');
    });
  
    it('should return sanitized value based on offer limits', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Offer Limits', '1');
      expect(sanitizedValue).toBe('Limit1');
    });
  
    it('should return sanitized value based on reward frequency', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Reward Freq', '1');
      expect(sanitizedValue).toBe('Limit1');
    });
  
    it('should return sanitized value for Change Reason', () => {
      spyOn(component, 'getValueForChangeReason').and.returnValue('Changed');
      const sanitizedValue = component.getValueMapping('/someKey/Change Reason', '1');
      expect(sanitizedValue).toBe('Changed');
    });
  
    it('should return sanitized value for Change Type', () => {
      spyOn(component, 'getValueForChangeReason').and.returnValue('Updated');
      const sanitizedValue = component.getValueMapping('/someKey/Change Type', '1');
      expect(sanitizedValue).toBe('Updated');
    });
  
    it('should return sanitized value for usage', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Usage', '1');
      expect(sanitizedValue).toBe('Usage1');
    });
  
    it('should return sanitized value for category', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Category', '1');
      expect(sanitizedValue).toBe('Category1');
    });
  
    it('should return sanitized value for Ad', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Ad', 'NIA');
      expect(sanitizedValue).toBe('Not In Ad');
    });
  
    it('should return Yes for Defer Evaluation Until EOS if true', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Defer Evaluation Until EOS', 'true');
      expect(sanitizedValue).toBe('Yes');
    });
  
    it('should return No for Defer Evaluation Until EOS if false', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Defer Evaluation Until EOS', 'false');
      expect(sanitizedValue).toBe('No');
    });
  
    it('should return Yes for Allow Negative if true', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Allow Negative', 'true');
      expect(sanitizedValue).toBe('Yes');
    });
  
    it('should return No for Allow Negative if false', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Allow Negative', 'false');
      expect(sanitizedValue).toBe('No');
    });
  
    it('should return Yes for Flex Negative if true', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Flex Negative', 'true');
      expect(sanitizedValue).toBe('Yes');
    });
  
    it('should return No for Flex Negative if false', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Flex Negative', 'false');
      expect(sanitizedValue).toBe('No');
    });
  
    it('should return Yes for Best Deal if true', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Best Deal', 'true');
      expect(sanitizedValue).toBe('Yes');
    });
  
    it('should return No for Best Deal if false', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Best Deal', 'false');
      expect(sanitizedValue).toBe('No');
    });
  
    it('should return sanitized value for Event', () => {
      spyOn(component, 'setValueForEvents').and.returnValue('Event1');
      const sanitizedValue = component.getValueMapping('/someKey/Event', '1');
      expect(sanitizedValue).toBe('Event1');
    });
  
    it('should return sanitized value for Offer Status', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Offer Status:', '1');
      expect(sanitizedValue).toBe('Status1');
    });
  
    it('should return sanitized value for Discount Type', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Discount Type', '1');
      expect(sanitizedValue).toBe('Discount1');
    });
  
    it('should return sanitized value for Message', () => {
      spyOn(component, 'formatPrintedMessage').and.returnValue('Formatted Message');
      const sanitizedValue = component.getValueMapping('/someKey/Message', '1');
      expect(sanitizedValue).toBe('Formatted Message');
    });
  
    it('should return sanitized value for Region', () => {
      spyOn(component, 'getRegionValueForId').and.returnValue('Region1');
      const sanitizedValue = component.getValueMapping('/someKey/Region', '1');
      expect(sanitizedValue).toBe('Region1');
    });
  
    it('should return Yes for Show Always if true', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Show Always', 'true');
      expect(sanitizedValue).toBe('Yes');
    });
  
    it('should return No for Show Always if false', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Show Always', 'false');
      expect(sanitizedValue).toBe('No');
    });
  
    it('should return sanitized value for Group', () => {
      spyOn(component, 'getGroupOrDivisionValue').and.returnValue('Group1');
      const sanitizedValue = component.getValueMapping('/someKey/Group', '1');
      expect(sanitizedValue).toBe('Group1');
    });
  
    it('should return sanitized value for Division', () => {
      spyOn(component, 'getGroupOrDivisionValue').and.returnValue('Division1');
      const sanitizedValue = component.getValueMapping('/someKey/Division', '1');
      expect(sanitizedValue).toBe('Division1');
    });
  
    it('should return original value if no mapping is found', () => {
      const sanitizedValue = component.getValueMapping('/someKey/Unknown', '1');
      expect(sanitizedValue).toBe('1');
    });
  });
  
  describe('setValuesForConfigGroupIds', () => {
    let newChildItem: any;
  
    beforeEach(() => {
      newChildItem = {};
    });
  
    it('should process beforeValue and afterValue when they have more than 10 elements', () => {
      newChildItem.beforeValue = '1,2,3,4,5,6,7,8,9,10,11,12';
      newChildItem.afterValue = '21,22,23,24,25,26,27,28,29,30,31,32';
  
      component.setValuesForConfigGroupIds(newChildItem);
  
      
      expect(newChildItem.beforeValue).toEqual(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']);
      expect(newChildItem.configGroupIdsBefore).toEqual(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']);
      expect(newChildItem.showMoreAtBefore).toBe(true);
  
      
      expect(newChildItem.afterValue).toEqual(['21', '22', '23', '24', '25', '26', '27', '28', '29', '30']);
      expect(newChildItem.configGroupIdsAfter).toEqual(['21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32']);
      expect(newChildItem.showMoreAtAfter).toBe(true);
    });
  
    it('should not modify anything if beforeValue and afterValue are not present', () => {
      component.setValuesForConfigGroupIds(newChildItem);
  
      expect(newChildItem.configGroupIdsBefore).toBeUndefined();
      expect(newChildItem.showMoreAtBefore).toBeUndefined();
      expect(newChildItem.configGroupIdsAfter).toBeUndefined();
      expect(newChildItem.showMoreAtAfter).toBeUndefined();
    });
  
    it('should handle undefined beforeValue and afterValue correctly', () => {
      newChildItem.beforeValue = undefined;
      newChildItem.afterValue = undefined;
  
      component.setValuesForConfigGroupIds(newChildItem);
  
      expect(newChildItem.configGroupIdsBefore).toBeUndefined();
      expect(newChildItem.showMoreAtBefore).toBeUndefined();
      expect(newChildItem.configGroupIdsAfter).toBeUndefined();
      expect(newChildItem.showMoreAtAfter).toBeUndefined();
    });
  });

  it('should return "Corporate" when value is "CORP"', () => {
    const result = component.getGroupOrDivisionValue('CORP', 'group');
    expect(result).toBe('Corporate');
  });

  it('should return the division name when value matches a division code', () => {
    const result = component.getGroupOrDivisionValue('D001', 'group');
    expect(result).toBe('D001');
  });
  

  it('should return mapped value from baseKeyMapper if key exists', () => {
    component.baseKeyMapper = HistoryUtils.getBaseMapper();  
   component.isOfferView = false;
   
    
    const result = component.getKey('/info/bggm');
    expect(result).toBe('<strong> Offer Request:</strong> BGGM');
  });
  
  it('should return mapped value from baseKeyMapperForGroups if key exists', () => {
    
    component.baseKeyMapperForGroups = { 'key2': 'mappedValue2' };
    
    const result = component.getKey('key2');
    expect(result).toBe('mappedValue2');
  });

  it('should return advanced key mapping when key matches an advancedKeyPatternMapper', () => {

    component.advancedkeyPatternMapper = HistoryUtils.getAdvancedPatternMapper();
    component.incrementNumInKey = jasmine.createSpy().and.returnValue('pattern1:1');
    
    const result = component.getKey('/info/nopaNumbers/');
    expect(result).toBe('<strong> Offer Funding:</strong> pattern1:1');
    expect(component.incrementNumInKey).toHaveBeenCalledWith('info: nopaNumbers');
  });
});

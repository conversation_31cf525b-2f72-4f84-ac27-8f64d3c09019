
import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { CommonService } from '@appServices/common/common.service';
import { HistoryService } from '@appServices/common/history.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { mmDdYyyySlash_DateFormat } from '@appUtilities/date.utility';
import * as moment from 'moment';
import HistoryUtils from '../history-utils';

@Component({
  selector: 'app-history-details',
  templateUrl: './history-details.component.html',
  styleUrls: ['./history-details.component.scss']
})

export class HistoryDetailsComponent implements OnInit, OnChanges {
  
  events: any = [];
  isOfferView: boolean = false;
  constructor(public historyService: HistoryService,
    public initialDataService: InitialDataService,
    private commonService: CommonService) {
      // intentionally left empty
  }
  @Input('reqId') reqId;
  @Input('templateId') templateId;
  @Input('groupId') groupId;
  @Input('offerId') offerId;
  @Input('groupPage') groupPage;
  @Input('modalReference') historyDetailsmodalRef;
  originalHistoryDetails;
  flattenedHistoryDetails = [];
  sanitizedHistoryDetails = [];
  nutriTags = [];
  configGroupIdsKeys = [...HistoryUtils.getAllMoreChangeSets(),...['/stores','/upcs','/hhids', '/mfids', '/deptids','/nutriTags']];
  appData = this.initialDataService.getAppData();
  baseKeyMapper = HistoryUtils.getBaseMapper();
  baseKeyMapperForGroups= {};
  advancedkeyPatternMapper = HistoryUtils.getAdvancedPatternMapper();
  sanitizeHistoryMapper = HistoryUtils.getKeySanitizerMap();
  sanitizeHistoryMapperForOffers = HistoryUtils.getSanitizerMapForOffers();
  advancedKeyPatternMapperForOffer = HistoryUtils.getAdvancedPatternMapperForOffers();
  uomConfig = CONSTANTS.UOM_CONFIG;
  isORView = false;
  isConfigGroupView = false;
  changesSetList = ["/rules/applicableTo/banners/"];
  ngOnChanges() {
    if (this.reqId) {
      this.setHistoryData(this.reqId, 'OR');
      this.isORView = true;
    }
    if (this.templateId) {
      this.setHistoryData(this.templateId, 'OT');
      this.isORView = true;
    }
    if(this.groupId) {
      this.isConfigGroupView = true;
      this.baseKeyMapperForGroups = HistoryUtils.getBaseMapperForGroups(this.groupPage);
      this.setHistoryDataForGroups(this.groupId);
    }
    if(this.offerId) {
      this.isOfferView = true;
      this.setHistoryDataForOffers(this.offerId);
    }
  }
  setHistoryDataForOffers(offerId) {
    this.historyService.getHistoryDetailsDataForOffers(offerId).subscribe((response) => {
      this.originalHistoryDetails = response;
      // 1. Flatten the structure
      this.flattenHistoryDetails();
      // 2. Sanitize key and value
      this.santizeHistoryDetails();
    });
  }
  ngOnInit() {
   
     this.commonService.eventDataSrc.subscribe((data: any) => {
        if(data) {
          this.events = data;
        }
      });
      this.nutriTags = this.initialDataService.getNutriTags();
   }

  //TO DO: check whether id is reqId or grpId based on page, and call the respective method

  setHistoryData(id, type) {
    this.historyService.getOROTHistoryDetailsById(id, type).subscribe((response) => {
      this.originalHistoryDetails = response;
      // 1. Flatten the structure
      this.flattenHistoryDetails();
      // 2. Sanitize key and value
      this.santizeHistoryDetails();
    });
  }
  setHistoryDataForGroups(groupId) {
    const fetchFields = ['auditAction','createdUser', 'changeset'];
    this.historyService
      .getHistoryDetailsDataForGroups(groupId, fetchFields, this.groupPage).
      subscribe((historyDetails: any)=> {
        if(historyDetails) {
            this.originalHistoryDetails = historyDetails;
            this.flattenHistoryDetails();
            this.santizeHistoryDetails();
        }
      });
  }
  getChangeSetName(key,addedItem,changeSetName){
    return HistoryUtils.getOtherChangeSet(key,addedItem) || changeSetName;
  }
  flattenHistoryDetails() {
    for (const historyDetail of this.originalHistoryDetails) {
      if (historyDetail.changeset) {
        const historyDetailFlatChangeset = [];
        let changeSet = historyDetail.changeset;

        Object.keys(changeSet).forEach((key) => {
          this.handleStoreGroupNamesChangeSet(changeSet, key);
          const addedItems = changeSet[key];
          this.processAddedItems(addedItems, key, historyDetailFlatChangeset);
        });
        this.flattenedHistoryDetails.push(this.createFlattenedHistoryDetail(historyDetail, historyDetailFlatChangeset));
      } else {
        this.flattenedHistoryDetails.push(historyDetail);
      }
    }
  }

  handleStoreGroupNamesChangeSet(changeSet: any, key: string) {
    const storeGroupNameItems = this.getStoreGroupNameItems(changeSet, key);
    storeGroupNameItems.forEach((element) => {
      const _key = this.getUpdatedKey(element.key);
      this.removeKeyFromChangeSet(changeSet, key, _key);
    });
  }

  getStoreGroupNameItems(changeSet: any, key: string) {
    return changeSet[key].filter((ele: any) => ele.key.includes('StoreGroupNames'));
  }

  getUpdatedKey(key: string) {
    let _key = key.substring(0, key.lastIndexOf('/')).replace("StoreGroupNames", "StoreGroupIds");
    if (key.includes('StoreGroupNames') && key.indexOf('StoreGroupNames') !== -1) {
      _key = key.substring(0, key.lastIndexOf('/')).replace("StoreGroupIds", "StoreGroupNames");
    }
    return _key;
  }

  removeKeyFromChangeSet(changeSet: any, key: string, _key: string) {
    const indexToRemove = changeSet[key].findIndex((item: any) => item.key.toString().startsWith(_key));
    if (indexToRemove !== -1) {
      changeSet[key].splice(indexToRemove, 1);
    }
  }

  processAddedItems(addedItems: any[], key: string, historyDetailFlatChangeset: any[]) {
    addedItems.forEach((addedItem) => {
      const type = this.getChangeSetName(key, addedItem, `${key[0].toUpperCase()}${key.slice(1)}`);
      historyDetailFlatChangeset.push({ type, ...addedItem, originalType: `${key[0].toUpperCase()}${key.slice(1)}` });
    });
  }

  createFlattenedHistoryDetail(historyDetail: any, historyDetailFlatChangeset: any[]) {
    return {
      auditTime: historyDetail.auditTime,
      createdUser: historyDetail?.createdUser,
      historyDetailFlatChangeset,
      auditMessage: historyDetail.auditMessage,
    };
  }
  getChangeItemKey(changeItemKey){
    const changeItem =(item)=>changeItemKey.includes(item),
    isExist = this.changesSetList.filter(changeItem);
    if(isExist.length) {
        return isExist[0];
    }else{
      return changeItemKey;
    }
  }
  /**
   * 
   * @param historyDetail 
   * @param sanitizedKey 
   * @returns  modified sanitized key
   * Need to modified the sanitized key and replace the Action with either Edit/Cancel based on audit message
   */
  setKeyForEditCancelHistory(historyDetail, sanitizedKey) {
    const auditMessage = historyDetail?.auditMessage;
    if(auditMessage) {
      const action = ["Canceled", "Remove"].some(v => auditMessage?.includes(v)) ? "Cancel" : "Edit";
      return sanitizedKey.replace("Action", action)
    }
  }
  santizeHistoryDetails() {
    for (const historyDetail of this.flattenedHistoryDetails) {
      if (!historyDetail.historyDetailFlatChangeset) {
        this.sanitizedHistoryDetails.push(historyDetail);
        continue;
      }
  
      const childHistoryArray = historyDetail.historyDetailFlatChangeset
        .map(changeItem => this.createChildHistoryItem(changeItem, historyDetail))
        .filter(Boolean);
  
      const sanitizedFinalItem = {
        auditTime: historyDetail.auditTime,
        createdUser: historyDetail?.createdUser,
        childHistoryArray,
        showShowMoreOption: childHistoryArray.length > 5
      };
  
      this.sanitizedHistoryDetails.push(sanitizedFinalItem);
    }
  }
  getNutritagNames(ChangeItemValue: string): string {
    const ids: string[] = ChangeItemValue.split(',').map(id => id.trim());
    const names: string[] = ids.map(id => {
        const label = this.nutriTags.find(label => label.id === id);
        return label ? label.name : '';
    });
    return names.filter(Boolean).join(', ');
}
  createChildHistoryItem(changeItem, historyDetail) {
    const sanitizedKey = this.sanitizeKey(changeItem.key, historyDetail);
    if (!sanitizedKey) return null;
    const formattedType = `<strong>${changeItem.type}</strong>`;
    let changeItemValue = changeItem.key === '/nutriTags' ? this.getNutritagNames(changeItem.value) || changeItem.value : changeItem.value;
    const newChildItem = {
      sanitizedKey: formattedType + sanitizedKey,
      key: changeItem.key,
      configGroupIdsBefore: [],
      configGroupIdsAfter: [],
      showMoreAtBefore: false,
      showMoreAtAfter: false,
      beforeValue: (changeItem.type === 'Removed') ?
        this.sanitizeValue(sanitizedKey, changeItemValue) :
        this.sanitizeValue(sanitizedKey, changeItem.before),
      afterValue: (changeItem.originalType === 'Added') ?
        this.sanitizeValue(sanitizedKey, changeItemValue) :
        this.sanitizeValue(sanitizedKey, changeItem.after),
      ...changeItem
    };
  
    if (this.isConfigGroupView && this.configGroupIdsKeys.includes(changeItem.key)) {
      this.setValuesForConfigGroupIds(newChildItem);
    }
  
    return newChildItem;
  }
  
  setValuesForConfigGroupIds(newChildItem) {
    if(newChildItem) {
      if(newChildItem.beforeValue && newChildItem.beforeValue.split(',').length > 10) {
        newChildItem.configGroupIdsBefore = newChildItem.beforeValue.split(',');
        newChildItem.beforeValue = newChildItem.beforeValue.split(',').slice(0,10);
        newChildItem.showMoreAtBefore = true;
      }
      if(newChildItem.afterValue && newChildItem.afterValue.split(',').length > 10) {
        newChildItem.configGroupIdsAfter = newChildItem.afterValue.split(',');
        newChildItem.afterValue = newChildItem.afterValue.split(',').slice(0,10);
        newChildItem.showMoreAtAfter = true;
      }
    }
  }

  showMoreValues(i,j) {
    const historyDetailObj = this.sanitizedHistoryDetails[i] && this.sanitizedHistoryDetails[i].childHistoryArray[j];
    if(historyDetailObj) {
      if(historyDetailObj.showMoreAtBefore) {
        historyDetailObj.beforeValue = historyDetailObj.configGroupIdsBefore;
        historyDetailObj.showMoreAtBefore = !historyDetailObj.showMoreAtBefore;
      }
      if(historyDetailObj.showMoreAtAfter) {
        historyDetailObj.afterValue = historyDetailObj.configGroupIdsAfter;
        historyDetailObj.showMoreAtAfter = !historyDetailObj.showMoreAtAfter;
      }
    }
  }

  isDisplayCount(changeSetItem) {
    return changeSetItem && changeSetItem.key && this.configGroupIdsKeys.includes(changeSetItem.key);
  }

  displayCountValue(changesetItem) {
    return changesetItem && changesetItem.value && changesetItem.value.split(',').length;
  }

  sanitizeKey(key, historyDetail) {
    const changedItemKey = key;
    const changeReasonKeys = ['changeComments', 'changeReason', 'changeType'];
    key = this.getKey(key);
    if (key) {
      const sanitizeKeyHistoryMapper = this.isOfferView ? this.sanitizeHistoryMapperForOffers  : this.sanitizeHistoryMapper;
      Object.keys(sanitizeKeyHistoryMapper).forEach((k) => {
        if (key.indexOf(k) !== -1) {
          key = key.replace(k, sanitizeKeyHistoryMapper[k]);
        }
      });
      if(changeReasonKeys.some(v => changedItemKey?.includes(v)) && this.isORView) {
        key =  this.setKeyForEditCancelHistory(historyDetail, key);
      }
      // Special Logic for Digital Store Groups, Redemption Store Groups, POD Store Groups and Events to clean up the number in the end
      const lastKeyElement = key.split(':').pop();
      if (lastKeyElement.includes('Digital Store Group')
        || lastKeyElement.includes('Non Digital Store Group')
        || lastKeyElement.includes('POD Store Group')
        || lastKeyElement.includes('Event')
        || lastKeyElement.includes('Category')
        || lastKeyElement.includes('Terminals')
        || lastKeyElement.includes('Not Combinable With')
      ) {
        const index = key.lastIndexOf(':');
        key = key.substring(0, index) + ':' + key.substring(index + 1).replace(/\d/g, '');
      }
      return key;
    }
  }

  getKey(key) {
    if (this.baseKeyMapper[key]) {
      return this.baseKeyMapper[key];
    } else if( this.baseKeyMapperForGroups[key]) {
      return this.baseKeyMapperForGroups[key];
    } else {
      let advancedKeyMapper = this.isOfferView ? this.advancedKeyPatternMapperForOffer : this.advancedkeyPatternMapper;
      for (const mapperKey in advancedKeyMapper) {
        if (key.includes(mapperKey)) {
          key = key
              .replace(/^(?:\/)|(?:\/)$/g, '') // Explicitly group the start and end slashes to be removed
              .replace(/\//g, ': '); // Replace remaining slashes with ": "
          return `${advancedKeyMapper[mapperKey]} ${this.incrementNumInKey(key)}`;
        }      
      }
    }
  }

  capitalizeFirstLetter(str) {
    return str && str.length > 0 ? (str.charAt(0).toUpperCase() + str.substring(1)) : '';
  }

  sanitizeValue(key, value) {
    if (isNaN(value) && moment(value, moment.ISO_8601, true).isValid()) {
      const dateVal = new Date(value);
      if (dateVal) {
        return mmDdYyyySlash_DateFormat(value);
      } else {
        return this.getValueMapping(key, value);
      }
    } else {
      return this.getValueMapping(key, value);
    }
  }
  // getValueMapping(key, value) {
  //   let returnValue = value;
  //   if (key.endsWith('Offer Type')) {
  //     returnValue = this.appData.offerType[value];
  //   } else if (key.endsWith('Benefit Type')) {
  //     returnValue = this.appData.amountTypes[value];
  //   } else if (key.endsWith('Offer Limits') || key.endsWith('Reward Freq')) {
  //     returnValue = this.appData.offerLimitsGR[value];
  //   } else if (["Change Reason", "Change Type"].some(v => key?.endsWith(v))) {
  //     const changeType = key.endsWith("Change Type") ? "ChangeReasonType" : "ChangeReason"
  //     returnValue =  this.getValueForChangeReason(key, value, changeType);
  //   } else if (key.endsWith('Usage')) {
  //     returnValue = this.appData?.podUsageLimits[value];
  //   } else if (key.includes('Category')) {
  //     returnValue = this.appData.customerFriendlyProductCategories[value] || value;
  //   } else if (key.endsWith('UoM')) {
  //     returnValue = this.uomConfig[value];
  //   } else if (key.endsWith('Ad')) {
  //     returnValue = (value === 'NIA' ? 'Not In Ad' : 'In Ad');
  //   } else if (key.endsWith('Defer Evaluation Until EOS') || key.endsWith('Allow Negative') 
  //     || key.endsWith('Flex Negative') ||  key.endsWith('Best Deal')) {
  //     returnValue = (value == "true" ? 'Yes' : (value == "false" ? 'No': ''));
  //   } else if (key.includes('Event')) {
  //     returnValue =  this.setValueForEvents(value);
  //   } else if (key.includes('Offer Status:')) {
  //     returnValue =  this.appData.offerStatuses[value];
  //   } else if (key.endsWith('Discount Type')) {
  //     returnValue = this.appData.discountTypes[value];
  //   } else if (key.endsWith('Message')) {
  //     returnValue = this.formatPrintedMessage(value);
  //   } else if (key.endsWith('Region')) {
  //     returnValue = this.getRegionValueForId(value);
  //   } else if (key.endsWith('Show Always')) {
  //   returnValue = (value == "true" ? 'Yes' : (value == "false" ? 'No': ''));
  //   } else if (key.endsWith('Group') || key.endsWith('Division')) {
  //     if (value === 'CORP') {
  //       returnValue = 'Corporate';
  //     } else {
  //       Object.entries(this.appData.offerRequestGroups).forEach(ORGroup => {
  //         if (ORGroup[1]['groupDivisions']) {
  //           ORGroup[1]['groupDivisions'].filter((divison) => {
  //             if (value === divison.code) {
  //               returnValue = divison.name;
  //               return;
  //             }
  //           });
  //         }
  //       });
  //     }
  //   }
  //   return returnValue;
  // }
  getValueMapping(key, value) {
    let val = (value === 'false' ? 'No' : '');
    const mappings = {
      'Offer Type': () => this.appData.offerType[value],
      'Benefit Type': () => this.appData.amountTypes[value],
      'Offer Limits': () => this.appData.offerLimitsGR[value],
      'Reward Freq': () => this.appData.offerLimitsGR[value],
      'Change Reason': () => this.getValueForChangeReason(key, value, 'ChangeReason'),
      'Change Type': () => this.getValueForChangeReason(key, value, 'ChangeReasonType'),
      'Usage': () => this.appData?.podUsageLimits[value],
      'Category': () => this.appData.customerFriendlyProductCategories[value] || value,
      'UoM': () => this.uomConfig[value],
      'Ad': () => (value === 'NIA' ? 'Not In Ad' : 'In Ad'),
      'Defer Evaluation Until EOS': () => (value === 'true' ? 'Yes' : val),
      'Allow Negative': () => (value === 'true' ? 'Yes' : val),
      'Flex Negative': () => (value === 'true' ? 'Yes' : val),
      'Best Deal': () => (value === 'true' ? 'Yes' : val),
      'Event': () => this.setValueForEvents(value),
      'Offer Status:': () => this.appData.offerStatuses[value],
      'Discount Type': () => this.appData.discountTypes[value],
      'Message': () => this.formatPrintedMessage(value),
      'Region': () => this.getRegionValueForId(value),
      'Show Always': () => (value === 'true' ? 'Yes' : val),
      'Group': () => this.getGroupOrDivisionValue(value, 'group'),
      'Division': () => this.getGroupOrDivisionValue(value, 'division'),
    };
  
    const mappingKey = Object.keys(mappings).find(k => key.endsWith(k));
    return mappingKey ? mappings[mappingKey]() : value;
  }
  
  getGroupOrDivisionValue(value, type) {
    if (value === 'CORP') {
      return 'Corporate';
    }
  
    for (const [group] of Object.entries(this.appData.offerRequestGroups)) {
      if (group['groupDivisions']) {
        const division = group['groupDivisions'].find(div => value === div.code);
        if (division) {
          return division.name;
        }
      }
    }
  
    return value;
  }
  
   /**
   * 
   * @param regionId 
   * @returns formatted Region with  name and Region Id
   */
  getRegionValueForId(regionId) {
    const selectedRegion = this.appData?.regions.filter((regionObj) => regionObj?.code === regionId); 
    return (selectedRegion?.length ? `${selectedRegion[0]?.code} ${selectedRegion[0]?.name}` : regionId);
  }
  formatPrintedMessage(message: string) {
    if(message) {
      return message.replace(/\|[|\U||\B||MEDIUM\||STARS\||LINE\||SMALL\||TOTALPOINTS\|]*?\|/g, '');
    }
  }
  /**
   * 
   * @param key 
   * @param value 
   * @param keyType 
   * @returns value for change reason based on mapping from config data and action type 
   */
  getValueForChangeReason(key, value, keyType) {
    if(key && value) {
      const action = key.includes("Edit") ? "Edit" : "Cancel";
      return this.appData[`offerRequest${action}${keyType}`][value];
    }
  }
  setValueForEvents(value) {
   
      if(this.events && this.events.length) {
        let filteredEvent = this.events.filter((event)=> event.eventCode == value);
        if(filteredEvent && filteredEvent.length) {
          if (filteredEvent[0].isHidden) {
            return `${filteredEvent[0].eventName} (H)`;
          } else {
            return filteredEvent[0].eventName;
          }
        }
      }
   
  }
  modalClosed = () => this.historyDetailsmodalRef.hide();
  incrementNumInKey = key => key.replace(/(\d{1,})/, numbr => ++numbr);
  removeNumInKey = key => key.replace(/(\d{1,}:)/, '');
  toggleAllRecords = (idx) => this.sanitizedHistoryDetails[idx].showShowMoreOption = !this.sanitizedHistoryDetails[idx].showShowMoreOption;

}

<div>
    <div>
        <div class="preview-group row-seperator"
            *ngIf="historyPreviewData && historyPreviewData.length > 0 else noHistory">
            <span class="history-header">History</span>
            <span class="count">({{historyPreviewData?.length || '0'}})</span>
            <span class="see-all" (click)="showHistoryDetails()">See All</span>
        </div>
    </div>
</div>

<div>
    <div>
        <div *ngFor="let historyPreviewItem of historyPreviewData | slice:0:10 " class="row-seperator">
            <div class="preview-group row">
                <span class="col-md-7" [ngClass]="isConfigGroupView ? 'font-size-body' : ''">
                    {{historyPreviewItem.auditTime | date:'M/d/yy h:mm a' + ':'}}
                    {{' ' + historyService.buildUserName(groupPage, historyPreviewItem)}}
                </span>
                <span *ngIf="isORView || isOTView || isOfferView" class="col-md-5 audit-message">
                    {{ (historyPreviewItem.auditMessage.indexOf('to') > 0 ?
                    historyPreviewItem.auditMessage.substr(0, historyPreviewItem.auditMessage.indexOf('to')) :
                    (historyPreviewItem.auditMessage == 'Changed' ? 'Updated OR' : historyPreviewItem.auditMessage)) }}
                </span>
                <span *ngIf="isConfigGroupView" class="col-md-5 audit-message">
                    {{historyPreviewItem.auditAction | titlecase}} {{groupPage}}
                </span>
            </div>
        </div>
    </div>
</div>



<ng-template #historyDetailsOverlay>
    <div>
        <app-history-details [reqId]="reqId" [templateId]="templateId" [offerId]="offerId" [groupId]="groupId"
            [modalReference]="historyDetailsmodalRef" [groupPage]="groupPage"></app-history-details>
    </div>
</ng-template>

<ng-template #noHistory>
    <div class="no-history-msg"><span>No History Found</span></div>
</ng-template>
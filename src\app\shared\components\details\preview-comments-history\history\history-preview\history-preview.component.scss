@import "../../../../../../../scss/colors";
@import "../../../../../../../scss/variables";


.history-header {
  height: 20px;
  width: 64px;
  color: $grey-dark-hex;
  font-size: $base-font-size;
  font-weight: 800;
  letter-spacing: 0;
  line-height: 20px;
}
.see-all {
  width: 100%;
  color: $blue-primary-hex;
  font-size: $small-font-size;
  letter-spacing: 0;
  line-height: 1px;
  text-align: right;
  cursor: pointer;
  text-decoration: underline;
}
.count{
  height: 20px;
  width: 38px;
  color: $grey-dark-hex;
  font-size: $base-font-size;
  letter-spacing: 0;
  line-height: 20px;
}
.preview-group {
  height: 54px;
  display: flex;
  align-items: center;
  padding-left: 16px;
  padding-right: 7px;
}
.row-seperator{
  border-bottom: 1px solid $gray-header-background;
}
.audit-message{
  color: $grey-dark-hex;
  font-size:13px;
  padding-left: 0
}
.no-history-msg {
  display: flex;
  justify-content: center;
  margin-top: 15%;
}
  
  
  
  
  
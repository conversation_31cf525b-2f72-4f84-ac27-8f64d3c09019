
import { HistoryPreviewComponent } from './history-preview.component';

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';

import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
//import { BsModalRef, BsModalService, ComponentLoaderFactory, PositioningService } from 'ngx-bootstrap';
import { BsModalRef, BsModalService, ModalModule } from 'ngx-bootstrap/modal';
import { of } from 'rxjs';
import { HistoryService } from '@appServices/common/history.service';
import { InitialDataService } from '@appServices/common/initial.data.service';


 const historyPreviewDataForOR1563581962 = [
    {
      "auditTime": "2020-07-01T17:02:36.231+00:00",
      "auditMessage": "Cancelled Digital OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T17:00:27.332+00:00",
      "auditMessage": "Processed Digital OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T17:00:27.268+00:00",
      "auditMessage": "Updated OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:58:47.005+00:00",
      "auditMessage": "Completed Digital OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:58:19.283+00:00",
      "auditMessage": "Processed Digital OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:58:19.243+00:00",
      "auditMessage": "Updated OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:57:53.160+00:00",
      "auditMessage": "Processed Digital OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:57:26.289+00:00",
      "auditMessage": "Assigned Digital OR to Tanusha Mandava",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:57:20.263+00:00",
      "auditMessage": "Submitted OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    },
    {
      "auditTime": "2020-07-01T16:57:20.258+00:00",
      "auditMessage": "Created OR",
      "createdUser": {
        "userId": "tmand03",
        "firstName": "Tanusha",
        "lastName": "Mandava"
      }
    }
  ];

describe('HistoryDetailsComponent', () => {
    let component: HistoryPreviewComponent;
    let fixture: ComponentFixture<HistoryPreviewComponent>;
    beforeEach(async () => {
        const historyServiceStub = () => ({
            getORHistoryPreviewByReqId: (reqid) => ({ subscribe: () => ({}) }),
            getORHistoryPreviewSubject: (reqid) => ({ subscribe: () => ({}) }),
            getORHistoryDetailsByReqId: (reqid) => ({ subscribe: () => ({}) }),
            getGroupsHistoryById: () => ({}),
            getHistoryGroupsPreviewData: () => ({}),
            getHistoryOffersPreviewData: () => ({}),
            getOfferHistoryPreviewByOfferId: () => ({}),
            getOROTHistoryPreviewById: () => ({ subscribe: () => ({}) }),
            getHistoryPreviewSubject: () => ({ subscribe: () => ({}) }),
      });
        const bsModalServiceStub = () => ({
           show: (_showAllOverlay, arg1) => ({})
        });
        const initialDataServiceStub = () => ({
        getAppData: () => (
            { offerType: {
            ITEM_DISCOUNT: "Item Discount",
            BUYX_GETX: "Buy X Get X",
            BUYX_GETY: "Buy X Get Y",
            MEAL_DEAL: "Meal Deal",
            BUNDLE: "Bundle",
            MUST_BUY: "Must Buy",
            FAB_5_OR_SCORE_4: "Fab 5 / Score 4",
            WOD_OR_POD: "WOD / POD",
            STORE_CLOSURE: "Store Closure",
            REWARDS_ACCUMULATION: "Rewards - Accumulation",
            REWARDS_FLAT: "Rewards - Flat",
            CONTINUITY: "Continuity",
            INSTANT_WIN: "Enterprise Instant Win",
            ALASKA_AIRMILES: "Alaska Airmiles",
            CUSTOM: "Custom",
          },
          amountTypes: {
            AMOUNT_OFF: "Cents Off",
            AMOUNT_OFF_WEIGHT_VOLUME: "Cents Off (Per Lb)",
            FREE: "Free",
            PRICE_POINT_ITEMS: "Price Point (Items)",
            PRICE_POINT_WEIGHT_VOLUME: "Price Point (Per Lb)",
            PERCENT_OFF_ITEMS: "Percent Off",
          },
          offerLimits: {
            UNLIMITED: "Unlimited",
            LIMITED: "Once per Transaction",
            ONCE_PER_OFFER: "Once per Offer",
            ONCE_PER_DAY: "Once per Day",
            ONCE_PER_WEEK: "Once per Week",
            CUSTOM: "Custom",
          },
          customerFriendlyProductCategories: {
            "1": "Baby Care",
            "2": "Beverages",
            "3": "Bread & Bakery",
            "4": "Breakfast & Cereal",
            "5": "Canned Goods & Soups",
            "6": "Condiments, Spices & Bake",
            "7": "Cookies, Snacks & Candy",
            "8": "Dairy, Eggs & Cheese",
            "9": "Deli",
            "10": "Flowers & Decor",
            "11": "Frozen Foods",
            "12": "Fruits & Vegetables",
            "13": "Grains, Pasta & Sides",
            "14": "International Cuisine",
            "15": "Meat & Seafood",
            "16": "Paper, Cleaning & Home",
            "17": "Personal Care & Health",
            "18": "Pet Care",
            "19": "Wine, Beer & Spirits",
            "23": "Special Offers",
          },
          events: {
            "2927": {
              desc: "Pizza Night",
              hidden: "N",
            },
            "2425": {
              desc: "Kids Lunches",
              hidden: "N",
            },
            "2929": {
              desc: "Breakfast Favorites",
              hidden: "N",
            },
            "4027": {
              desc: "Weekend Savings",
              hidden: "N",
            },
            "824": {
              desc: "Theme 1-5",
              hidden: "N",
            },
            "1024": {
              desc: "$5 Friday",
              hidden: "N",
            },
            "3830": {
              desc: "Vitamins",
              hidden: "N",
            },
            "4229": {
              desc: "OWN Brands",
              hidden: "N",
            },
            "4327": {
              desc: "MONOPOLY Deals",
              hidden: "N",
            },
            "4429": {
              desc: "PG Hidden",
              hidden: "N",
            },
            "4627": {
              desc: "Own Brands 2",
              hidden: "N",
            },
            "4729": {
              desc: "Naturally Delicious",
              hidden: "N",
            },
            "5127": {
              desc: "Score Great Deals",
              hidden: "N",
            },
            "5129": {
              desc: "Stock Up Sale",
              hidden: "N",
            },
            "1226": {
              desc: "Catch All 4",
              hidden: "N",
            },
            "5427": {
              desc: "Unilever NCAA",
              hidden: "N",
            },
            "1624": {
              desc: "Ice Cream Savings",
              hidden: "N",
            },
            "5428": {
              desc: "Starbucks",
              hidden: "N",
            },
            "2025": {
              desc: "Fall Refreshments",
              hidden: "N",
            },
            "2125": {
              desc: "Seattle's Best Savings",
              hidden: "N",
            },
            "5429": {
              desc: "Clorox",
              hidden: "N",
            },
            "6229": {
              desc: "Sweepstakes",
              hidden: "N",
            },
            "7027": {
              desc: "Wake Up to WOW",
              hidden: "N",
            },
            "924": {
              desc: "Theme 2-1",
              hidden: "N",
            },
            "1827": {
              desc: "Savings from Hormel",
              hidden: "N",
            },
            "2024": {
              desc: "Amazing Deals",
              hidden: "N",
            },
            "2326": {
              desc: "Nestle Favorites",
              hidden: "N",
            },
            "2427": {
              desc: "Method",
              hidden: "N",
            },
            "2225": {
              desc: "Colgate Savings",
              hidden: "N",
            },
            "2227": {
              desc: "Test Empty Event",
              hidden: "N",
            },
            "2324": {
              desc: "GM Favorites",
              hidden: "N",
            },
            "2428": {
              desc: "Toms of Maine",
              hidden: "N",
            },
            "2629": {
              desc: "Ghirardelli",
              hidden: "N",
            },
            "2827": {
              desc: "Memorial Day Deals",
              hidden: "N",
            },
            "3028": {
              desc: "Deals for U",
              hidden: "N",
            },
            "3229": {
              desc: "Quaker",
              hidden: "N",
            },
            "3230": {
              desc: "Top Your Ritz",
              hidden: "N",
            },
            "3427": {
              desc: "Get Your Game On!",
              hidden: "N",
            },
            "3727": {
              desc: "#peanutsmovie",
              hidden: "N",
            },
            "3929": {
              desc: "LALA",
              hidden: "N",
            },
            "4228": {
              desc: "Coke New Items",
              hidden: "N",
            },
            "4427": {
              desc: "Salute to Savings",
              hidden: "N",
            },
            "4428": {
              desc: "Purina",
              hidden: "N",
            },
            "4727": {
              desc: "Sweetheart Savings",
              hidden: "N",
            },
            "4529": {
              desc: "Snacking",
              hidden: "N",
            },
            "4531": {
              desc: "Lunch",
              hidden: "N",
            },
            "4730": {
              desc: "Shop & Score",
              hidden: "N",
            },
            "4731": {
              desc: "Save on Activia",
              hidden: "N",
            },
            "5433": {
              desc: "Employee Offers",
              hidden: "N",
            },
            "5727": {
              desc: "Reward Savings",
              hidden: "N",
            },
            "6027": {
              desc: "Savings from Clorox",
              hidden: "N",
            },
            "6227": {
              desc: "Solaris Paper",
              hidden: "N",
            },
            "6427": {
              desc: "Odwalla",
              hidden: "N",
            },
            "2327": {
              desc: "CB Brands",
              hidden: "N",
            },
            "2325": {
              desc: "Easter Brunch",
              hidden: "N",
            },
            "1724": {
              desc: null,
              hidden: "N",
            },
            "1725": {
              desc: "Yoplait",
              hidden: "N",
            },
            "1924": {
              desc: "Nabisco Snack Favorites",
              hidden: "N",
            },
            "2424": {
              desc: "Taco Night",
              hidden: "N",
            },
            "2426": {
              desc: "Unilever Personal",
              hidden: "N",
            },
            "2928": {
              desc: "General Mills Savings",
              hidden: "N",
            },
            "3128": {
              desc: "Barilla",
              hidden: "N",
            },
            "3130": {
              desc: "Maybelline",
              hidden: "N",
            },
            "3132": {
              desc: "Post",
              hidden: "N",
            },
            "3730": {
              desc: "Save on Creamers & More",
              hidden: "N",
            },
            "3731": {
              desc: "Holiday Entertaining",
              hidden: "N",
            },
            "3829": {
              desc: "Save on Bayer Products",
              hidden: "N",
            },
            "3928": {
              desc: "Earthbound",
              hidden: "N",
            },
            "4127": {
              desc: "Quotient",
              hidden: "N",
            },
            "4227": {
              desc: "Organic & Natural",
              hidden: "N",
            },
            "3833": {
              desc: "Theme 3-5",
              hidden: "N",
            },
            "4527": {
              desc: "White Wave Earth Day",
              hidden: "N",
            },
            "5128": {
              desc: "Save on Nestle Products",
              hidden: "N",
            },
            "5233": {
              desc: "Great New Year Savings",
              hidden: "N",
            },
            "5327": {
              desc: "Great New Year",
              hidden: "N",
            },
            "5431": {
              desc: "Purina Pet Month Deals",
              hidden: "N",
            },
            "5927": {
              desc: "Skincare Savings",
              hidden: "N",
            },
            "6029": {
              desc: "Hot Offers",
              hidden: "N",
            },
            "6827": {
              desc: "June Dairy Month",
              hidden: "N",
            },
            "7228": {
              desc: "Ticket to the Trophy",
              hidden: "N",
            },
            "7527": {
              desc: "Game Time Rewards",
              hidden: "N",
            },
            "2527": {
              desc: "Unilever Earth",
              hidden: "N",
            },
            "2727": {
              desc: "Help End Child Hunger",
              hidden: "N",
            },
            "2728": {
              desc: "Savings from Unilever",
              hidden: "N",
            },
            "2828": {
              desc: "Organic",
              hidden: "N",
            },
            "2829": {
              desc: "Back to Breakfast",
              hidden: "N",
            },
            "3029": {
              desc: "SCJ Clean",
              hidden: "N",
            },
            "3127": {
              desc: "Be Snack-sational",
              hidden: "N",
            },
            "3228": {
              desc: "Daily Deals",
              hidden: "N",
            },
            "3728": {
              desc: "Gerber",
              hidden: "N",
            },
            "3729": {
              desc: "Save with SC Johnson",
              hidden: "N",
            },
            "3827": {
              desc: "Gift Cards",
              hidden: "N",
            },
            "3927": {
              desc: "Baking",
              hidden: "N",
            },
            "3832": {
              desc: "Sparkling Ice",
              hidden: "N",
            },
            "4828": {
              desc: "Summer Games",
              hidden: "N",
            },
            "4927": {
              desc: "Test Events Ivan",
              hidden: "N",
            },
            "5027": {
              desc: "Score & Win",
              hidden: "N",
            },
            "5227": {
              desc: "Hunger Is",
              hidden: "N",
            },
            "524": {
              desc: "Save on P&G Products",
              hidden: "N",
            },
            "5231": {
              desc: "Kids Wellness",
              hidden: "N",
            },
            "1324": {
              desc: "Summer Savings",
              hidden: "N",
            },
            "1824": {
              desc: "New! Only in Our Stores",
              hidden: "N",
            },
            "5232": {
              desc: "Dannon Savings",
              hidden: "N",
            },
            "1828": {
              desc: "Starbucks Fall Flavors",
              hidden: "N",
            },
            "1925": {
              desc: "Cough & Cold Savings",
              hidden: "N",
            },
            "2124": {
              desc: "Sweet Savings",
              hidden: "N",
            },
            "5527": {
              desc: "L'Oreal",
              hidden: "N",
            },
            "5432": {
              desc: "Take Advantage",
              hidden: "N",
            },
            "5728": {
              desc: "Kelloggs Back to School",
              hidden: "N",
            },
            "6527": {
              desc: "New Spring Beverages",
              hidden: "N",
            },
            "6727": {
              desc: "Borden Cheese",
              hidden: "N",
            },
            "7227": {
              desc: "Road Trip Must-Haves",
              hidden: "N",
            },
            "7327": {
              desc: "Yoplait Oui",
              hidden: "N",
            },
            "7427": {
              desc: "Breakfast On-the-Go",
              hidden: "N",
            },
            "2224": {
              desc: "Nestle Frozen Favorites",
              hidden: "N",
            },
            "125": {
              desc: "Weekly Ad Coupons",
              hidden: null,
            },
            "2127": {
              desc: "Frozen Food Month",
              hidden: "N",
            },
            "2627": {
              desc: "Meal Italian",
              hidden: "N",
            },
            "2628": {
              desc: "Cinco De Mayo",
              hidden: "N",
            },
            "425": {
              desc: "Theme 3-5",
              hidden: "N",
            },
            "2729": {
              desc: "Baby",
              hidden: "N",
            },
            "1424": {
              desc: "Box Tops for Education",
              hidden: "N",
            },
            "2930": {
              desc: "Grilling",
              hidden: "N",
            },
            "3027": {
              desc: "Fourth of July",
              hidden: "N",
            },
            "3129": {
              desc: "Starbucks Holiday",
              hidden: "N",
            },
            "3131": {
              desc: "Tyson",
              hidden: "N",
            },
            "3227": {
              desc: "Anniversary Sale",
              hidden: "N",
            },
            "3327": {
              desc: "NCAA Coke & Mondelez",
              hidden: "N",
            },
            "3428": {
              desc: "Unilever Meal",
              hidden: "N",
            },
            "3527": {
              desc: "Save with Boar's Head",
              hidden: "N",
            },
            "3627": {
              desc: "Fuel Your Trip",
              hidden: "N",
            },
            "3828": {
              desc: "Hot Holiday Savings",
              hidden: "N",
            },
            "3831": {
              desc: "MONOPOLY Deals",
              hidden: "N",
            },
            "4528": {
              desc: "Unilever Evergreen",
              hidden: "N",
            },
            "4530": {
              desc: "Breakfast",
              hidden: "N",
            },
            "4532": {
              desc: "Dinner",
              hidden: "N",
            },
            "4728": {
              desc: "Starbucks Summer",
              hidden: "N",
            },
            "4827": {
              desc: "Alcohol Tags",
              hidden: "N",
            },
            "5228": {
              desc: "O Organics",
              hidden: "N",
            },
            "5229": {
              desc: "Save on Horizon",
              hidden: "N",
            },
            "5230": {
              desc: "21 Ways for FREE Candy",
              hidden: "N",
            },
            "5130": {
              desc: "Holiday Breakfast",
              hidden: "N",
            },
            "5131": {
              desc: "Thanksgiving Deals",
              hidden: "N",
            },
            "5132": {
              desc: "Save on Pepsi Products",
              hidden: "N",
            },
            "5430": {
              desc: "Weight Watchers",
              hidden: "N",
            },
            "5627": {
              desc: "J & J",
              hidden: "N",
            },
            "5827": {
              desc: "Bake it Great!",
              hidden: "N",
            },
            "6028": {
              desc: "Wake Up to Wonderful",
              hidden: "N",
            },
            "6127": {
              desc: "Saturday Sampler",
              hidden: "N",
            },
            "6030": {
              desc: "St. Patty's Day Deals",
              hidden: "N",
            },
            "6228": {
              desc: "Johnsonville",
              hidden: "N",
            },
            "6327": {
              desc: "J & J Allergy",
              hidden: "N",
            },
            "6627": {
              desc: "Graduation Deals",
              hidden: "N",
            },
            "6927": {
              desc: "Better for You",
              hidden: "N",
            },
            "7127": {
              desc: "Healthy & School Ready",
              hidden: "N",
            },
            "7627": {
              desc: "Pepsi Pregame Sweeps",
              hidden: "N",
            },
            "7727": {
              desc: "Online Exclusive",
              hidden: "N",
            },
            "7827": {
              desc: "Game On",
              hidden: "N",
            },
            "8127": {
              desc: "Black Friday Deals",
              hidden: "N",
            },
            "8129": {
              desc: "Feed Their Future",
              hidden: "N",
            },
            "8227": {
              desc: "Big Game of Savings",
              hidden: "N",
            },
            "8627": {
              desc: "Exclusive Brands",
              hidden: "N",
            },
            "8927": {
              desc: "Coke Celtics Sweeps",
              hidden: "N",
            },
            "9227": {
              desc: "Coke Ballpark Sweeps",
              hidden: "N",
            },
            "9527": {
              desc: "Earth Day Savings",
              hidden: "N",
            },
            "9727": {
              desc: "Summertime Cocktails",
              hidden: "N",
            },
            "9827": {
              desc: "Ice Cream Month",
              hidden: "N",
            },
            "10227": {
              desc: "Save on your Summer BBQ",
              hidden: "N",
            },
            "8128": {
              desc: "Weekend Deals!",
              hidden: "N",
            },
            "8228": {
              desc: "Stocking Stuffers",
              hidden: "N",
            },
            "8427": {
              desc: "New Year Savings!",
              hidden: "N",
            },
            "8527": {
              desc: "Red Bull Sweepstakes",
              hidden: "N",
            },
            "8827": {
              desc: "Flash Sale!",
              hidden: "N",
            },
            "9027": {
              desc: "Passover Savings",
              hidden: "N",
            },
            "9127": {
              desc: "Danone Spring Savings",
              hidden: "N",
            },
            "9427": {
              desc: "Mother's Day Brunch",
              hidden: "N",
            },
            "9927": {
              desc: "Hot Deals!",
              hidden: "N",
            },
            "10028": {
              desc: "Splash into Summer",
              hidden: "N",
            },
            "10127": {
              desc: "80th Anniversary",
              hidden: "N",
            },
            "10527": {
              desc: "Dr Pepper Cowboys win",
              hidden: "N",
            },
            "10627": {
              desc: "All Star Rewards",
              hidden: "N",
            },
            "7927": {
              desc: "Save Now with McCormick",
              hidden: "N",
            },
            "8027": {
              desc: "FREE Turkey",
              hidden: "N",
            },
            "8327": {
              desc: "Make a Wish",
              hidden: "N",
            },
            "8528": {
              desc: "Kraft Heinz Sweepstakes",
              hidden: "N",
            },
            "8727": {
              desc: "Savings from Yoplait",
              hidden: "N",
            },
            "9327": {
              desc: "FREE Offers",
              hidden: "N",
            },
            "9428": {
              desc: "Celebrate Pet Month",
              hidden: "N",
            },
            "9627": {
              desc: "Aveeno Summer Savings",
              hidden: "N",
            },
            "10027": {
              desc: "Savings for Baby",
              hidden: "N",
            },
            "10327": {
              desc: "Frontier Days Sweeps",
              hidden: "N",
            },
            "10427": {
              desc: "Beauty Pick Savings",
              hidden: "N",
            },
          },
          offerRequestGroups: [
            {
              name: "Corporate",
              code: "CORP",
              groupDivisions: [
                {
                  name: "Multi-Division",
                  code: "MD",
                },
                {
                  name: "ACME",
                  code: "AC",
                },
                {
                  name: "Denver",
                  code: "DE",
                },
                {
                  name: "Eastern",
                  code: "ES",
                },
                {
                  name: "Haggen",
                  code: "HG",
                },
                {
                  name: "Intermountain",
                  code: "IM",
                },
                {
                  name: "Jewel",
                  code: "JW",
                },
                {
                  name: "Norcal",
                  code: "NC",
                },
                {
                  name: "Portland",
                  code: "PT",
                },
                {
                  name: "Seattle",
                  code: "SE",
                },
                {
                  name: "Shaws/Star Market",
                  code: "SH",
                },
                {
                  name: "SoCal",
                  code: "SC",
                },
                {
                  name: "Southern",
                  code: "SO",
                },
                {
                  name: "Southwest",
                  code: "SW",
                },
              ],
            },
            {
              name: "Acme",
              code: "AM",
            },
            {
              name: "Denver",
              code: "DE",
            },
            {
              name: "Eastern",
              code: "ES",
            },
            {
              name: "Haggen",
              code: "HG",
            },
            {
              name: "Intermountain",
              code: "IM",
            },
            {
              name: "Jewel",
              code: "JW",
            },
            {
              name: "Norcal",
              code: "NC",
              groupDivisions: [
                {
                  name: "NorCal",
                  code: "NC",
                },
                {
                  name: "Hawaii",
                  code: "HI",
                },
                {
                  name: "Both",
                  code: "NC HI",
                },
              ],
            },
            {
              name: "Portland",
              code: "PT",
            },
            {
              name: "Seattle",
              code: "SE",
              groupDivisions: [
                {
                  name: "Seattle",
                  code: "SE",
                },
                {
                  name: "Alaska ",
                  code: "AK",
                },
                {
                  name: "Both",
                  code: "SE AK",
                },
              ],
            },
            {
              name: "Shaws/Star Market",
              code: "SH",
            },
            {
              name: "SoCal",
              code: "SC",
            },
            {
              name: "Southern",
              code: "SO",
            },
            {
              name: "Southwest",
              code: "SW",
            },
          ]
        }),
        getConfigUrls: cLONE_API => ({}) });

        TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, ModalModule.forRoot()],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [HistoryPreviewComponent],
      providers: [
       { provide: InitialDataService, useFactory: initialDataServiceStub },
       { provide: HistoryService, useFactory: historyServiceStub },
       {BsModalService, useFactory: bsModalServiceStub},
      ]
    });
        fixture = TestBed.createComponent(HistoryPreviewComponent);
        component = fixture.componentInstance;
        modalRef: BsModalRef;
        component.historyDetailsmodalRef = { hide: () => { }, setClass: () => { }, id: 1, onHide: new EventEmitter(), onHidden: new EventEmitter() };
    });

    it('can load instance', () => {
       expect(component).toBeTruthy();
    });


    it('Opens Modal onClick of the History link', () => {
        const bsModalServiceStub: BsModalService = fixture.debugElement.injector.get(
          BsModalService
        );
        spyOn(bsModalServiceStub, 'show');
        component.showHistoryDetails();
        expect(bsModalServiceStub.show).toHaveBeenCalled();
    });
  describe('getHistoryPreviewForOffers', () => {
    it('should make expected call', () => {
      component.offerId = 123;
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(
        HistoryService
      );
      spyOn(historyServiceStub, 'getOfferHistoryPreviewByOfferId');
      spyOn(historyServiceStub, 'getHistoryOffersPreviewData').and.returnValue(of([]));
      component.getHistoryPreviewForOffers();
      expect(historyServiceStub.getOfferHistoryPreviewByOfferId).toHaveBeenCalled();
      expect(historyServiceStub.getHistoryOffersPreviewData).toHaveBeenCalled();
    });
  });
  describe('ngOnInit', () => {
    it('should not perform any action', () => {
      component.ngOnInit();
      expect(component).toBeTruthy(); // Ensures the component is still valid
    });
  });

  describe('ngOnChanges', () => {
    it('should call getHistoryPreview with reqId and set isORView to true when reqId is provided', () => {
      component.reqId = 123;
      spyOn(component, 'getHistoryPreview');
      component.ngOnChanges();
      expect(component.getHistoryPreview).toHaveBeenCalledWith(123, 'OR');
      expect(component.isORView).toBeTrue();
    });

    it('should call getGroupHistoryPreview and set isConfigGroupView to true when groupId is provided', () => {
      component.groupId = 456;
      component.groupPage = 1;
      component.groupNutriTag = true;
      spyOn(component, 'getGroupHistoryPreview');
      component.ngOnChanges();
      expect(component.getGroupHistoryPreview).toHaveBeenCalledWith(1, true);
      expect(component.isConfigGroupView).toBeTrue();
    });

    it('should call getHistoryPreviewForOffers and set isOfferView to true when offerId is provided', () => {
      component.offerId = 789;
      spyOn(component, 'getHistoryPreviewForOffers');
      component.ngOnChanges();
      expect(component.getHistoryPreviewForOffers).toHaveBeenCalled();
      expect(component.isOfferView).toBeTrue();
    });

    it('should call getHistoryPreview with templateId and set isOTView to true when templateId is provided', () => {
      component.templateId = 101112;
      spyOn(component, 'getHistoryPreview');
      component.ngOnChanges();
      expect(component.getHistoryPreview).toHaveBeenCalledWith(101112, 'OT');
      expect(component.isOTView).toBeTrue();
    });

    it('should not call any methods if no inputs are provided', () => {
      spyOn(component, 'getHistoryPreview');
      spyOn(component, 'getGroupHistoryPreview');
      spyOn(component, 'getHistoryPreviewForOffers');
      component.ngOnChanges();
      expect(component.getHistoryPreview).not.toHaveBeenCalled();
      expect(component.getGroupHistoryPreview).not.toHaveBeenCalled();
      expect(component.getHistoryPreviewForOffers).not.toHaveBeenCalled();
    });
  });

  describe('getHistoryPreview', () => {
    it('should call getOROTHistoryPreviewById and update historyPreviewData with the response', () => {
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(historyServiceStub, 'getOROTHistoryPreviewById');
      spyOn(historyServiceStub, 'getHistoryPreviewSubject').and.returnValue(of([{ id: 1, message: 'Test' }]));
      component.getHistoryPreview(123, 'OR');
      expect(historyServiceStub.getOROTHistoryPreviewById).toHaveBeenCalledWith(123, 'OR');
      expect(historyServiceStub.getHistoryPreviewSubject).toHaveBeenCalled();
      expect(component.historyPreviewData).toEqual([{ id: 1, message: 'Test' }]);
    });

    it('should handle empty response from getHistoryPreviewSubject', () => {
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(historyServiceStub, 'getOROTHistoryPreviewById');
      spyOn(historyServiceStub, 'getHistoryPreviewSubject').and.returnValue(of([]));
      component.getHistoryPreview(123, 'OR');
      expect(historyServiceStub.getOROTHistoryPreviewById).toHaveBeenCalledWith(123, 'OR');
      expect(historyServiceStub.getHistoryPreviewSubject).toHaveBeenCalled();
      expect(component.historyPreviewData).toEqual([]);
    });
  });

  describe('getGroupHistoryPreview', () => {
    it('should call getGroupsHistoryById with correct parameters and update historyPreviewData with the response', () => {
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(historyServiceStub, 'getGroupsHistoryById');
      spyOn(historyServiceStub, 'getHistoryGroupsPreviewData').and.returnValue(of([{ id: 1, message: 'Group Test' }]));
      component.groupId = 456;
      component.getGroupHistoryPreview(1, false);
      expect(historyServiceStub.getGroupsHistoryById).toHaveBeenCalledWith(456, ['auditAction', 'auditMessage', 'createdUser'], 1);
      expect(historyServiceStub.getHistoryGroupsPreviewData).toHaveBeenCalled();
      expect(component.historyPreviewData).toEqual([{ id: 1, message: 'Group Test' }]);
    });

    it('should include "changeset" in fetchFields when isNutriTag is true', () => {
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(historyServiceStub, 'getGroupsHistoryById');
      spyOn(historyServiceStub, 'getHistoryGroupsPreviewData').and.returnValue(of([{ id: 1, message: 'Group Test with NutriTag' }]));
      component.groupId = 456;
      component.getGroupHistoryPreview(1, true);
      expect(historyServiceStub.getGroupsHistoryById).toHaveBeenCalledWith(456, ['auditAction', 'auditMessage', 'createdUser', 'changeset'], 1);
      expect(historyServiceStub.getHistoryGroupsPreviewData).toHaveBeenCalled();
      expect(component.historyPreviewData).toEqual([{ id: 1, message: 'Group Test with NutriTag' }]);
    });

    it('should handle empty response from getHistoryGroupsPreviewData', () => {
      const historyServiceStub: HistoryService = fixture.debugElement.injector.get(HistoryService);
      spyOn(historyServiceStub, 'getGroupsHistoryById');
      spyOn(historyServiceStub, 'getHistoryGroupsPreviewData').and.returnValue(of([]));
      component.groupId = 456;
      component.getGroupHistoryPreview(1, false);
      expect(historyServiceStub.getGroupsHistoryById).toHaveBeenCalledWith(456, ['auditAction', 'auditMessage', 'createdUser'], 1);
      expect(historyServiceStub.getHistoryGroupsPreviewData).toHaveBeenCalled();
      expect(component.historyPreviewData).toEqual([]);
    });
  });
}); 
    





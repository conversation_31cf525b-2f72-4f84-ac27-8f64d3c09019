import { Component, Input, OnChanges, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { HistoryService } from '@appServices/common/history.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-history-preview',
  templateUrl: './history-preview.component.html',
  styleUrls: ['./history-preview.component.scss']
})

export class HistoryPreviewComponent implements OnInit, OnChanges {
  historyDetailsmodalRef: BsModalRef;
  historyPreviewData;
  isORView = false;
  isConfigGroupView = false;
  isOTView = false;
  @Input('reqId') reqId;
  @Input('groupId') groupId;
  @Input('offerId') offerId;
  @Input('templateId') templateId;
  @Input('groupPage') groupPage;
  @Input('programCode') programCode;
  @Input('isNutriTagPG') groupNutriTag = false;
  @ViewChild('historyDetailsOverlay') historyDetailsOverlay: TemplateRef<any>;
  isOfferView: boolean = false;
  CONSTANTS = CONSTANTS;

  constructor(public modalService: BsModalService,
    public historyService: HistoryService) {
    // intentionally left empty
  }

  ngOnInit() {
    // intentionally left empty    
  }

  ngOnChanges() {
    if (this.reqId) {
      this.getHistoryPreview(this.reqId, 'OR');
      this.isORView = true;
    }
    if (this.groupId) {
      this.getGroupHistoryPreview(this.groupPage, this.groupNutriTag);
      this.isConfigGroupView = true;
    }
    if (this.offerId) {
      this.isOfferView = true;
      this.getHistoryPreviewForOffers();
    }
    if (this.templateId) {
      this.getHistoryPreview(this.templateId, 'OT');
      this.isOTView = true;
    }
  }

  getHistoryPreviewForOffers() {
    this.historyService.getOfferHistoryPreviewByOfferId(this.offerId);
    this.historyService.getHistoryOffersPreviewData().subscribe(historyPreviewResponse => {
      this.historyPreviewData = historyPreviewResponse;
    });
  }
  getHistoryPreview(id, type) {
    this.historyService.getOROTHistoryPreviewById(id, type);
    this.historyService.getHistoryPreviewSubject().subscribe(historyPreviewResponse => {
      this.historyPreviewData = historyPreviewResponse;
    });
  }

  /*In this method we need to replace the hardcoded data with the response coming from API*/
  getGroupHistoryPreview(groupPage, isNutriTag = false) {
    const fetchFields = ['auditAction', 'auditMessage', 'createdUser'];
    if (isNutriTag)
      fetchFields.push("changeset");
    this.historyService.getGroupsHistoryById(this.groupId, fetchFields, groupPage);
    this.historyService.getHistoryGroupsPreviewData().subscribe((historyPreviewResponse: any) => {
      if (historyPreviewResponse) {
        this.historyPreviewData = historyPreviewResponse;
      }
    });
  }

  showHistoryDetails() {
    this.historyDetailsmodalRef = this.modalService.show(
      this.historyDetailsOverlay,
      Object.assign({}, { class: 'modal-lg' })
    );
  }
}

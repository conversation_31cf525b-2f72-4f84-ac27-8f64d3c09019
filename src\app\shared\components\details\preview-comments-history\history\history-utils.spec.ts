import HistoryUtils from './history-utils';

describe('HistoryUtils', () => {

  describe('getMoreChangeSet', () => {
    it('should return the correct more change set object', () => {
      const result = HistoryUtils.getMoreChangeSet();
      expect(result).toEqual({
        added: {
          '/invalidUpcs': 'Invalid UPC ID(s)'
        },
        removed: {},
        changed: {}
      });
    });
  });

  describe('getAllMoreChangeSets', () => {
    it('should return an array of keys from the more change set', () => {
      const result = HistoryUtils.getAllMoreChangeSets();
      // Since getMoreChangeSet returns added with one key, the expected array is:
      expect(result).toEqual(['/invalidUpcs']);
    });
  });

  describe('getBaseMapper', () => {
    it('should return a mapping with correct HTML strings', () => {
      const mapper = HistoryUtils.getBaseMapper();
      expect(mapper['/info/bggm']).toEqual('<strong> Offer Request:</strong> BGGM');
      expect(mapper['/rules/uom']).toEqual('<strong> Offer Request:</strong> UOM');
      // You can add additional key checks as needed.
    });
  });

  describe('getOtherChangeSet', () => {
    it('should return the change set value for a valid key', () => {
      const result = HistoryUtils.getOtherChangeSet('added', { key: '/invalidUpcs' });
      expect(result).toEqual('Invalid UPC ID(s)');
    });

    it('should return an empty string when the key is not found', () => {
      const result = HistoryUtils.getOtherChangeSet('added', { key: '/nonExistent' });
      expect(result).toEqual('');
    });
      
      it('should return an empty string if keySet is not found', () => {
            const result = HistoryUtils.getOtherChangeSet('nonexistent', { key: '/invalidUpcs' });
            expect(result).toEqual('');
        });
  });

  describe('getBaseMapperForGroups', () => {
    it('should return a mapping that uses the provided groupPage in the strings', () => {
      const groupPage = 'Group1';
      const mapper = HistoryUtils.getBaseMapperForGroups(groupPage);
      expect(mapper['/name']).toEqual(`<strong> ${groupPage}  Name </strong>:`);
    });
  });

  describe('getAdvancedPatternMapperForOffers', () => {
    it('should return a mapping with correct HTML strings for offers', () => {
      const mapper = HistoryUtils.getAdvancedPatternMapperForOffers();
      expect(mapper['/id']).toEqual('<strong> Offer</strong>');
    });
  });

  describe('getAdvancedPatternMapper', () => {
    it('should return a mapping with correct HTML strings for advanced patterns', () => {
      const mapper = HistoryUtils.getAdvancedPatternMapper();
      expect(mapper['/info/nopaNumbers/']).toEqual('<strong> Offer Funding:</strong>');
    });
  });

  describe('getKeySanitizerMap', () => {
    it('should return a sanitizer map with correct key-value pairs', () => {
      const sanitizerMap = HistoryUtils.getKeySanitizerMap();
      expect(sanitizerMap['rules: qualificationAndBenefit: offerRequestOffers:']).toEqual('Version');
      expect(sanitizerMap['info: justification']).toEqual('Justification');
    });
  });

  describe('getSanitizerMapForOffers', () => {
    it('should return a sanitizer map for offers with correct key-value pairs', () => {
      const sanitizerMap = HistoryUtils.getSanitizerMapForOffers();
      expect(sanitizerMap['info: adType']).toEqual('In Ad');
    });
  });
});

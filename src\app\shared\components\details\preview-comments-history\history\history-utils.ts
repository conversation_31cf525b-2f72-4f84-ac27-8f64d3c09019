
export default class  HistoryUtils {
 static getMoreChangeSet(){
    return {
    added:{
      '/invalidUpcs':'Invalid UPC ID(s)'
     },
    removed:{
          
    },
    changed:{

    }
    }
  };
  static getAllMoreChangeSets(){
    const  list  = this.getMoreChangeSet(),changeSetList = Object.keys(list).reduce((acc,ele)=>{
      if(list[ele]){
        acc = {...acc,...list[ele]}
      }
      return acc;
    },{});
    return Object.keys(changeSetList);
   }
  static getBaseMapper() {
    return {
      '/info/bggm':'<strong> Offer Request:</strong> BGGM',
      '/info/allocationCode':'<strong> Offer Request:</strong> Allocation Code',
      '/info/allocationCodeName':'<strong> Offer Request:</strong> Allocation Code Name',
      '/info/bugm':'<strong> Offer Request:</strong> BUGM',
      '/info/category':'<strong> Offer Request:</strong> Category',
      '/info/categoryId':'<strong> Offer Request:</strong> Category Id',
      '/info/cic':'<strong> Offer Request:</strong> CIC',
      '/info/cpg':'<strong> Offer Request:</strong> CPG',
      '/info/repUpc':'<strong> Offer Request:</strong> Rep Upc',
      '/rules/qty':'<strong> Offer Request:</strong> Quantity',
      '/rules/uom':'<strong> Offer Request:</strong> UOM',
      '/rules/customPeriod': '<strong> Offer Request:</strong> Custom Period',
      '/rules/customType': '<strong> Offer Request:</strong> Custom Type',
      '/rules/customUsage': '<strong> Offer Request:</strong> Custom Usage',
      '/info/lastPeriodCreated':'<strong> Offer Request:</strong> Last Period Created',
      '/info/regionId':'<strong> Offer Request:</strong> Region',
      '/info/programType':'<strong> Offer Request:</strong> ProgramType',
      '/info/points':'<strong> Offer Request:</strong> Points',
      '/info/orderCount': '<strong> Offer Request:</strong> Order Count',
      '/info/validWithOtherOffer': '<strong> Offer Request:</strong> Valid With Other Offer',
      '/info/firstTimeCustomerOnly': '<strong> Offer Request:</strong> First Time Customer Only',
      '/rules/pointsRequired':'<strong> Offer Request:</strong> Points Required',
      '/info/mobId' : '<strong> Offer Request:</strong> Mob ID',
      '/info/mobName' : '<strong> Offer Request:</strong> Mob Name',
      '/rules/rank':'<strong> Offer Request:</strong> Rank',
      '/info/programSubType':'<strong> Offer Request:</strong> Program SubType',
      '/info/period_week':'<strong> Offer Request:</strong> Period Week',
      '/rules/department': '<strong> Offer Request:</strong> Department',
      '/info/groupDivision': '<strong> Offer Request:</strong> Division',
      '/info/group': '<strong> Offer Request:</strong> Group',
      '/info/brandAndSize': '<strong> Offer Request:</strong> Brand & Size',
      '/info/behavioralCondition/noOfTransactions': '<strong> Offer Request:</strong> No Of Transactions',
      '/info/behavioralCondition/minimumSpend': '<strong> Offer Request:</strong> Minimum Spend',
      '/info/behavioralCondition/minimumSpendUom': '<strong> Offer Request:</strong> Minimum Spend UOM',
      '/rules/usageLimitTypePerUser': '<strong> Offer Request:</strong> Offer Limits',
      '/rules/customerSegment': '<strong> Offer Request:</strong> Segment',
      '/info/nopaNumbers': '<strong> Offer Funding:</strong> NOPA Numbers',
      '/info/billingOptions': '<strong> Offer Funding:</strong> Billing Option',
      '/info/nopaStartDate': '<strong> Offer Funding:</strong> NOPA Start Date',
      '/info/nopaEndDate': '<strong> Offer Funding:</strong> NOPA End Date',
      '/info/isBilled': '<strong> Offer Funding:</strong> Billed',
      '/info/desc': '<strong> Offer Request:</strong> Additional Details',
      '/info/subProgramCode': '<strong> Offer Request:</strong> SubProgramCode',
      '/info/isAutoApplyPromoCode': '<strong> Offer Request:</strong> Auto Apply Promo Code',
      '/info/ecommPromoCode': '<strong> Offer Request:</strong> eComm Promo Code',
      '/info/isRedeemableInSameTransaction': '<strong> Offer Request:</strong> Redeem Benefit In Same Transaction',
      '/info/offerName': '<strong> Offer Name:</strong>',
      '/rules/startDate/offerEffectiveStartDate': '<strong> Offer Request:</strong> Start Date',
      '/info/isDynamicOffer':'<strong> Offer Request:</strong> Dynamic',
      '/info/daysToRedeem':'<strong> Offer Request:</strong> Days To Redeem',
      '/info/getMiles':'<strong> Offer Request:</strong> Miles',
      '/rules/usageLimitPerUser': '<strong> Offer Request:</strong> Limit',
      '/rules/qualificationAndBenefit/time/end': '<strong> Additional Information </strong>: End Time',
      '/rules/qualificationAndBenefit/time/start': '<strong> Additional Information </strong>: Start Time',
      '/rules/qualificationAndBenefit/day/monday': '<strong> Additional Information </strong>: Monday',
      '/rules/qualificationAndBenefit/day/tuesday': '<strong> Additional Information </strong>: Tuesday',
      '/rules/qualificationAndBenefit/day/wednesday': '<strong> Additional Information </strong>: Wednesday',
      '/rules/qualificationAndBenefit/day/thursday': '<strong> Additional Information </strong>: Thursday',
      '/rules/qualificationAndBenefit/day/friday': '<strong> Additional Information </strong>: Friday',
      '/rules/qualificationAndBenefit/day/saturday': '<strong> Additional Information </strong>: Saturday',
      '/rules/qualificationAndBenefit/day/sunday': '<strong> Additional Information </strong>: Sunday',
      // Offer Definition
      '/info/programExtraAttributes/tiers': '<strong> Offer Definition:</strong> Tier Count',
      '/info/programExtraAttributes/copientCategory': '<strong> Offer Definition:</strong> Copient Category',
      '/info/description/desc': '<strong> Offer Definition:</strong> Description',
      '/info/programExtraAttributes/priority': '<strong> Offer Definition:</strong> Priority',
      '/rules/usageLimits/usageLimitTypePerUser': '<strong> Offer Definition:</strong> Reward Freq',
      '/info/programExtraAttributes/deferEvaluationUntilEOS': '<strong> Offer Definition:</strong> Defer Evaluation Until EOS',
      '/info/offerStatus': '<strong> Offer Status:</strong>',
      '/info/changeComments': '<strong> Offer Request:</strong> Action Change Reason Comment',
      '/info/changeReason': '<strong> Offer Request:</strong> Action Change Reason',
      '/info/changeType': '<strong> Offer Request:</strong> Action Change Type',
      '/info/behavioralAction': '<strong> Offer Request:</strong> Behavioral Action',
      // POD
      '/info/ivieImageId': '<strong> POD:</strong> POD Content: Ivie Image ID(s)',
      '/info/ecomDesc': '<strong> POD:</strong> POD Content: eCommerce Text',
      '/rules/endDate/displayEffectiveEndDate': '<strong> POD:</strong> POD Content: Display End Date',
      '/info/productImageId': '<strong> POD:</strong> POD Content: Scene 7 Image ID',
      '/info/description/savingsValueText': '<strong> POD:</strong> POD Content: Price Text',
      '/info/description/headLine': '<strong> POD:</strong> POD Content: Headline',
      '/info/description/disclaimerText': '<strong> POD:</strong> POD Content: Offer Details/Disclaimer',
      '/rules/usageLimits/podUsageLimitTypePerUser' : '<strong> POD:</strong> POD Content: Usage',
      '/info/description/productDescription/prodDsc1': '<strong> POD:</strong> POD Content: Offer Description',
      '/rules/endDate/offerEffectiveEndDate': '<strong> Offer Request:</strong> End Date',
      '/rules/benefit/groupMemberShip/customerGroupName' : '<strong> Offer Benefit:</strong> Grant Membership',
      '/rules/benefit/groupMemberShip/customerGroupId': '<strong> Offer Benefit:</strong> Grant Membership ID', 
      '/rules/qualification/productDisQualifier/name': '<strong>Offer Condition:</strong> Product Disqualifier',
      // Template
      '/info/otStatus': '<strong> Offer Template Status</strong>',
      '/rules/priceUntil/offerEffectiveEndDate': '<strong> Offer Template Price Until</strong>',
      '/info/otStatusReason': '<strong> Offer Template Status Reason</strong>',
      '/info/otStatusReasonComment': '<strong> Offer Template Status Reason Comment</strong>',
      '/info/otStatusSetUntil': '<strong> Offer Template Status Set Until</strong>',
      '/info/reviewFlags/qtyUOM': '<strong> Offer Template Review Flags - Qty/UOM</strong>',
      '/info/reviewFlags/reActivated': '<strong> Offer Template Review Flags - Reactivated</strong>',
      '/info/reviewFlags/repUPC': '<strong> Offer Template Review Flags - Rep UPC</strong>',
      '/info/reviewFlags/ringType': '<strong> Offer Template Review Flags - Ring Type</strong>',
      // BPD
      '/status': '<strong> Product Group Status</strong>'
    };
  }
  static getOtherChangeSet(key, addedItem) {
    const keySet = this.getMoreChangeSet()?.[key];
    if (!keySet) return '';

    // Use direct access and concise logic to extract the value
    const value = keySet[addedItem.key] || '';
    return value;
}

  static getBaseMapperForGroups(groupPage) {
    return {
      '/name': `<strong> ${groupPage}  Name </strong>:`,
      '/storeGroupName': `<strong> Store Group Name </strong>:`,
      '/customerGroupName': `<strong> Customer Group Name </strong>:`,
      '/description': `<strong> ${groupPage} Description </strong>:`,
      '/productGroupDescription': `<strong> ${groupPage} Description </strong>:`,
      '/stores' : '<strong> Stores </strong> ',
      '/hhids': '<strong> Household ID(s) </strong> ',
      '/upcs': '<strong> UPC ID(s) </strong>',
      '/mfids': '<strong> Manufacture ID(s) </strong>',
      '/deptids': '<strong> Department Code </strong>',
      '/invalidUpcs': '<strong> </strong>',
      '/nutriTags': '<strong> </strong>'
    }
  }
  static getAdvancedPatternMapperForOffers() {
    return {
      '/id': '<strong> Offer</strong>',
      '/includeProductGroupId': '<strong> Offer</strong>',
      '/pointsProgramId': '<strong> Offer</strong>',
      '/info/categories/': '<strong> POD:</strong> POD Settings:',
      '/info/eventIds/': '<strong> POD:</strong> POD Settings:',
      '/info/primaryCategory/': '<strong> POD:</strong> POD Settings:',
      '/rules/qualification/storeGroups/redemptionStoreGroupIds': '<strong> Location:</strong>',
      '/rules/qualification/storeGroups/redemptionStoreGroupNames': '<strong> Location:</strong>',
      '/rules/qualification/storeGroups/podStoreGroupIds': '<strong> Location:</strong>',
      '/rules/qualification/storeGroups/podStoreGroupNames': '<strong> Location:</strong>',
      '/rules/applicableTo/excludedTerminals': '<strong> Location:</strong>',
      '/rules/usageLimits/usageLimitPerOffer': '<strong> Clipping Limit:</strong>',
      '/rules/applicableTo/storeIds': '<strong> Store Ids:</strong>',
      '/rules/applicableTo/terminals': '<strong> Location:</strong>',
      '/rules/applicableTo/banners': '<strong> Banners:</strong>',
      '/info/podDetailsEditStatus/editTs': '<strong> EditTS:</strong>',
      '/excludedProductGroupId':'<strong> Offer</strong>',
      '/quantityUnitType': '<strong> Offer</strong>',
      '/quantity': '<strong> Offer</strong>',
      '/conjunction': '<strong> Offer</strong>',
      '/code': '<strong> Offer</strong>',
      '/requirement': '<strong> Offer</strong>',
      '/minPurchaseAmount': '<strong> Offer</strong>',
      '/scoreCard' : '<strong> Offer</strong>',
      '/scoreCardText' : '<strong> Offer</strong>',
      '/beepType' : '<strong> Offer</strong>',
      '/line1' : '<strong> Offer</strong>',
      '/line2' : '<strong> Offer</strong>',
      '/amount': '<strong> Offer</strong>',
      '/message': '<strong> Offer</strong>',
      '/isApplicableForNotifications': '<strong> Offer</strong>',
      '/chargebackDepartment': '<strong> Offer</strong>',
      '/itemLimit': '<strong> Offer</strong>',
      '/dollarLimit': '<strong> Offer</strong>',
      '/benefitValueType': '<strong> Offer</strong>',
      '/receiptText': '<strong> Offer</strong>',
      '/allowNegative': '<strong> Offer</strong>',
      '/advanced/bestDeal': '<strong> Offer</strong>',
      '/advanced/flexNegative': '<strong> Offer</strong>',
      '/discountType': '<strong> Offer</strong>',
      '/info/adType': '<strong> Offer :</strong>',
      '/info/isInEmail': '<strong> Offer :</strong>',
      '/value':'<strong> Offer</strong>',
      '/tenderType':'<strong> Offer</strong>',
    }
  }
  static getAdvancedPatternMapper() {
    return {
      '/info/nopaNumbers/': '<strong> Offer Funding:</strong>',
      '/info/notCombinableWith': '<strong> Offer Request:</strong>',
      '/amount': '<strong> Offer Type:</strong>',
      '/rewards': '<strong> Offer Type:</strong>',
      '/includeProductGroupName': '<strong> Offer Type:</strong>',
      '/storeTag/multiple': '<strong> Offer Type:</strong>',
      '/chargebackDepartment': '<strong> Offer Type:</strong>',
      '/upTo': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/offerPrototype': '<strong> Offer Type:</strong>',
      '/benefitValueType': '<strong> Offer Type:</strong>',
      '/itemLimit': '<strong> Offer Type:</strong>',
      '/productGroup/quantityUnitType': '<strong> Offer Type:</strong>',
      '/storeGroup/digitalRedemptionStoreGroupNames/': '<strong> Offer Type:</strong>',
      '/storeGroup/nonDigitalRedemptionStoreGroupNames/': '<strong> Offer Type:</strong>',
      '/storeGroup/podStoreGroupNames/': '<strong> Offer Type:</strong>',
      '/weightLimit': '<strong> Offer Type:</strong>',
      '/points': '<strong> Offer Type:</strong>',
      '/productGroup/name': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/storeTag/comments': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/disclaimer': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/displayEndDate': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/displayStartDate': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/multiClipLimit': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/land': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/space': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/slot': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/headline': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/ivieImageId': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/offerDescription': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/priceText': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/eventIds/': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/categoryId': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/scene7ImageId':'<strong> Offer Type:</strong>',
      'storeGroupVersion/podDetails/podUsageLimit': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/headline1':'<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/headline2':'<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/offerDetailsCode':'<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/leftNavCategory/':'<strong> Offer Type:</strong>',
      '/storeGroupVersion/podDetails/shoppingListCategory':'<strong> Offer Type:</strong>',
      '/storeGroupVersion/storeTag/printJ4uTagEnabled': '<strong> Offer Type:</strong>',
      '/storeGroupVersion/instantWin/numberOfPrizes': '<strong> Offer Type:</strong>',
      '/productGroup/minPurchaseAmount': '<strong> Offer Type:</strong>',
      '/isGiftCard': '<strong> Offer Type:</strong>',
      '/productGroup/tiers/': '<strong> Offer Type:</strong>',
      '/info/justification': '<strong> Justification:</strong>',
      '/info/adType': '<strong> Offer Request:</strong>',
      '/info/allocationCriteriaList' : '<strong> Allocation Criteria:</strong>'
    }
  }

  static getKeySanitizerMap() {
    return {
      'rules: qualificationAndBenefit: offerRequestOffers:': 'Version',
      'rules: qualificationAndBenefit: spdOfferRequestOffers:': 'Version',
      'rules: qualificationAndBenefit: grOfferRequestOffers:': 'Version',
      'rules: qualificationAndBenefit: offerTemplateOffers: 1:': '',
      'info: nopaNumbers:': 'NOPA Number',
      'info: justification': 'Justification',
      'info: adType': 'In Ad',
      'info: notCombinableWith:': 'Not Combinable With',
      'storeGroupVersion: storeGroup: digitalRedemptionStoreGroupNames:': 'Digital Store Group',
      'storeGroupVersion: storeGroup: nonDigitalRedemptionStoreGroupNames:': 'Non Digital Store Group',
      'storeGroupVersion: storeGroup: podStoreGroupNames:': 'POD Store Group',
      'storeGroupVersion: productGroupVersions:': 'Product Group',
      'discountVersion: discounts:': 'Discount',
      'tiers:': 'Tier',
      'itemLimit': 'Item Limit',
      'weightLimit': 'Weight Limit',
      'amount': 'Amount',
      'rewards': 'Rewards',
      'multiple': 'Multiple',
      'comments': 'Comments',
      'upTo': 'Up To',
      'points': 'Points',
      'chargebackDepartment' : 'Chargeback Department',
      'printJ5uTagEnabled': 'Print J4U Tag',
      'benefitValueType': 'Benefit Type',
      'isGiftCard': 'Gift Card',
      'discountVersion: airMiles:': 'Airmiles',
      'storeGroupVersion: offerPrototype': 'Offer Type',
      'includeProductGroupName': 'Product Group Name',
      'productGroup: quantityUnitType': 'UoM',
      'productGroup: name': 'Product Group Name',
      'storeGroupVersion: podDetails: disclaimer': 'POD: Disclaimer',
      'storeGroupVersion: podDetails: ivieImageId': 'POD: Ivie Image ID(s)',
      'storeGroupVersion: podDetails: displayEndDate': 'POD: Display End Date',
      'storeGroupVersion: podDetails: displayStartDate': 'POD: Display Start Date',
      'storeGroupVersion: podDetails: headline2': 'POD: Headline 1',
      'storeGroupVersion: podDetails: headline3': 'POD: Headline 2',
      'storeGroupVersion: podDetails: headline': 'POD: Headline',
      'storeGroupVersion: podDetails: offerDescription': 'POD: Offer Description',
      'storeGroupVersion: podDetails: priceText': 'POD: Price Text',
      'storeGroupVersion: podDetails: eventIds:': 'POD: Event',
      'storeGroupVersion: podDetails: categoryId': 'POD: Shopping List Category',
      'storeGroupVersion: podDetails: podUsageLimit': 'POD: Usage',
      'storeGroupVersion: podDetails: offerDetailsCode': 'POD: Offer Details Code',
      'storeGroupVersion: podDetails: leftNavCategory:': 'POD: Left Nav Category',
      'storeGroupVersion: podDetails: shoppingListCategory': 'POD: Shopping List Category',
      'storeGroupVersion: podDetails: multiClipLimit': 'POD: Multi Clip Limit',
      'storeGroupVersion: podDetails: land': 'POD: Land',
      'storeGroupVersion: podDetails: space': 'POD: Space',
      'storeGroupVersion: podDetails: slot': 'POD: Slot',
      'storeGroupVersion: podDetails: scene8ImageId': 'POD: Scene 7 Image ID',
      'storeGroupVersion: storeTag': 'In Store Tag Instructions',
      'storeGroupVersion: instantWin: numberOfPrizes': 'Prizes Per Offer',
      'productGroup: minPurchaseAmount': 'Min Purchase',
      'productGroup: Tier': 'Tier'
    }
  }
  static getSanitizerMapForOffers() {
    return {
      'info: categories:': 'Left Nav Category',
      'info: primaryCategory:': 'Shopping List Category',
      'rules: applicableTo: excludedTerminals:': 'Excluded Terminals', 
      'rules: applicableTo: storeIds:': 'Store Ids', 
      'rules: applicableTo: terminals:': 'Selected Terminals', 
      'rules: applicableTo: banners:': 'Banners', 
      'info: podDetailsEditStatus: editTs:': 'Edit TS', 
      'info: eventIds:': 'Event',
      'rules: qualification: productGroups:': '<strong> Condition:</strong> Product Group',
      'rules: qualification: storeGroups: redemptionStoreGroupIds:': 'Digital/Non-Digital Store Group',
      'rules: qualification: storeGroups: redemptionStoreGroupNames:': 'Digital/Non-Digital Store Group',
      'rules: qualification: productDisQualifier': '<strong> Condition:</strong> Product Disqualifier',
      'rules: qualification: storeGroups: podStoreGroupIds:': 'POD Store Group',
      'rules: qualification: storeGroups: podStoreGroupNames:': 'POD Store Group',
      'rules: benefit: discount:': '<strong> Benefits:</strong> Discount',
      'rules: benefit: printedMessage: message': '<strong> Benefits:</strong> Printed Message',
      'rules: benefit: printedMessage': '<strong> Benefits:</strong> Printed Message',
      'rules: qualification: pointsGroups:': '<strong> Condition:</strong> Point Group',
      'rules: qualification: triggerCodes:': '<strong> Condition:</strong> Trigger Code',
      'rules: benefit: cashierMessage:': '<strong> Benefits:</strong> Cashier Message:',
      'rules: benefit: points:': '<strong> Benefits:</strong> Point Group',
      'rules: qualification: customerGroups:': 'Included Customer',
	    'excludedProductGroupName': 'Excluded Product Group Name',
      'includeProductGroupName': 'Included Product Group Name',
      'includeProductGroupId': 'Included Product Group ID',
      "excludedProductGroupId": 'Excluded Product Group ID',
      'pointsProgramId': 'Points Program ID',
      'name': 'Name',
      'code': 'Code',
      'line2': 'Line 1',
      'line3': 'Line 2',
      'requirement': 'Requirement',
	    'quantityUnitType': 'UoM',
      'tiers': 'Tier',
      'discountTier': 'Tier',
      'discountType': 'Discount Type',
      'pointsTier': 'Tier',
      'itemLimit': 'Item Limit',
      'weightLimit': 'Weight Limit',
      'amount': 'Amount',
      'upTo': 'Up To',
      'conjunction': 'AND/OR',
      'benefitValueType': 'Benefit Type',
      'minPurchaseAmount': 'Min Purchase',
      'chargebackDepartment' : 'Chargeback Department',
      'dollarLimit' : 'Dollar Limit',
      'receiptText' : 'Receipt Text',
      'advanced: allowNegative' : 'Allow Negative',
      'advanced: flexNegative' : 'Flex Negative',
      'advanced: bestDeal' : 'Best Deal',
      'cashierMessageTiers' : 'Tier',
      'pointsProgramName': 'Point Program Name',
      'scoreCard' : 'Scorecard',
      'beepType' : 'Beep',
      'message': 'Message',
      'isApplicableForNotifications' : 'Show Always',
      'scoreCardText': 'Scorecard Text',
      'level': 'Level',
      'quantity': 'Quantity',
      'id': 'ID',
      'info: adType': 'In Ad',
      'info: isInEmail': 'In Email',
      'value':'Value',
      'tenderType':'Tender Type',
    }
  }
}


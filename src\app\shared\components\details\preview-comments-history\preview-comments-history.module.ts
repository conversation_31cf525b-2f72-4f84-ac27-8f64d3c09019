// Angular Imports
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
// This Module's Components
import { CommentPanelModule } from '@appComments/core/comment-section/components/comment-panel/comment-panel.module';
import { FeatureFlagDirectiveModule } from '@appDirectives/feature-flags/feature-flag-module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { AppCommonModule } from '../../../../modules/common/app.common.module';
import { PreviewCardModule } from '../preview-card/preview-card.module';
import { HistoryDetailsComponent } from './history/history-details/history-details.component';
import { HistoryPreviewComponent } from './history/history-preview/history-preview.component';
import { PreviewCommentsHistoryComponent } from './preview-comments-history/preview-comments-history.component';

@NgModule({
    imports: [
        CommonModule,
        CommentPanelModule,
        AppCommonModule,
        PreviewCardModule,
        PermissionsModule.forChild(),
        FeatureFlagDirectiveModule,
        TabsModule.forRoot()
    ],
    providers: [TabsModule],
    declarations: [
        PreviewCommentsHistoryComponent,
        HistoryDetailsComponent,
        HistoryPreviewComponent
    ],
    exports: [
        PreviewCommentsHistoryComponent,
        HistoryDetailsComponent,
        HistoryPreviewComponent
    ]
})
export class PreviewCommentsHistoryModule {

}

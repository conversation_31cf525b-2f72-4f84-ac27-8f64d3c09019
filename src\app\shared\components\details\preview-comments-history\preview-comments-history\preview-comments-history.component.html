<div class="container pr-0" *ngIf="isOfferView">
  <div>
    <tabset>
        <tab *ngIf="preview" heading="Preview" id="tab1" class="my-4">
          <div class="col-12 p-0">
            <spinner *ngIf="loading"></spinner>
            <ng-container
              *ngIf="
                imageID !== undefined || null;
                then imageValid;
                else imageDefault
              "
            >
            </ng-container>
            <preview-card
              [imageID]="imageID"
              [offersArray]="offersArray"
              [isOfferPage]="true"
              [showPoints]="showPoints"
            ></preview-card>
          </div>
        </tab>
      <ng-container>
        <tab *ngIf="offerId" heading="History" class="my-4"  (selectTab)="onSelect($event)">
          <ng-container *ngIf="preview ? selectedTab === 'History' : 'true'">
            <app-history-preview [offerId]="offerId"></app-history-preview>
          </ng-container>
        </tab>
      </ng-container>
    </tabset>
  </div>
  <ng-container *ngIf="reqId">
    <comment-panel [offerProgramCode]="offerProgramCode" [reqId]="reqId"></comment-panel>
  </ng-container>
</div>

<div *ngIf="isInORView">
  <div>
    <tabset>
      <tab heading="Comment" id="tab1" class="my-4">
        <comment-panel [offerProgramCode]="offerProgramCode" [templateId]="templateId"></comment-panel>
      </tab>
      <tab
        heading="History"
        *permissionsOnly="
          viewOfferRequestsHistoryPermission;
          authorisedStrategy: 'show';
          unauthorisedStrategy: 'remove'
        "
        (selectTab)="onSelect($event)"
      >
        <ng-container *ngIf="selectedTab === 'History'">
          <app-history-preview [reqId]="reqId" [templateId]="templateId"></app-history-preview>
        </ng-container>
      </tab>
    </tabset>
  </div>
</div>

<div *ngIf="isConfigGroupView">
  <div>
    <tabset>
      <tab heading="History">
        <app-history-preview
          [groupId]="groupId"
          [groupPage]="groupPage"
          [isNutriTagPG]="groupNutriTag"
        ></app-history-preview>
      </tab>
      <tab *ngIf="isBPGView" heading="Comment" id="tab1" class="my-4">
        <ng-container *ngIf="groupId">
          <comment-panel [groupId]="groupId" (selectTab)="onSelect($event)" (postCommentForBPG)="postCommentForBPG($event)" [isBPGView]="isBPGView"></comment-panel>
        </ng-container>
      </tab>
    </tabset>
  </div>
</div>

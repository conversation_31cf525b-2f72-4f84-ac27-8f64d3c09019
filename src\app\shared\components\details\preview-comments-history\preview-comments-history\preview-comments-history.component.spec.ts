import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { PreviewCommentsHistoryComponent } from './preview-comments-history.component';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { HistoryService } from '@appServices/common/history.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';

describe('PreviewCommentsHistoryComponent', () => {
  let component: PreviewCommentsHistoryComponent;
  let fixture: ComponentFixture<PreviewCommentsHistoryComponent>;
  let mockUploadImagesService: any;
  let mockHistoryService: any;
  let mockRouter: any;
  let mockFeatureFlagsService: any;

  beforeEach(async () => {
    mockUploadImagesService = {
      getImage: jasmine.createSpy('getImage').and.returnValue(of('testImageID')),
      getLoading: jasmine.createSpy('getLoading').and.returnValue(of(true)),
    };

    mockHistoryService = jasmine.createSpyObj('HistoryService', ['']);
    mockRouter = { url: '/offers' };
    mockFeatureFlagsService = jasmine.createSpyObj('FeatureFlagsService', ['']);

    await TestBed.configureTestingModule({
      declarations: [PreviewCommentsHistoryComponent],
      providers: [
        { provide: UploadImagesService, useValue: mockUploadImagesService },
        { provide: HistoryService, useValue: mockHistoryService },
        { provide: Router, useValue: mockRouter },
        { provide: FeatureFlagsService, useValue: mockFeatureFlagsService },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PreviewCommentsHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component and initialize properties', () => {
    expect(component).toBeTruthy();
    expect(component.imageID).toBe('testImageID');
    expect(component.loading).toBeTrue();
    expect(component.isOfferView).toBeTrue();
    expect(component.isInORView).toBeFalse();
    expect(component.isConfigGroupView).toBeFalse();
    expect(component.isBPGView).toBeFalse();
  });

  it('should set isInORView to false if the route contains /request', () => {
    mockRouter.url = '/request';
    component.ngOnInit();
    expect(component.isInORView).toBeFalse();
  });

  it('should set isOfferView to true if the route contains /offers', () => {
    mockRouter.url = '/offers';
    component.ngOnInit();
    expect(component.isOfferView).toBeTrue();
  });

  it('should set isConfigGroupView to false if the route contains /groups', () => {
    mockRouter.url = '/groups';
    component.ngOnInit();
    expect(component.isConfigGroupView).toBeFalse();
  });

  it('should emit postCommentClick when postCommentForBPG is called', () => {
    spyOn(component.postCommentClick, 'emit');
    const commentText = 'Test comment';
    component.postCommentForBPG(commentText);
    expect(component.postCommentClick.emit).toHaveBeenCalledWith(commentText);
  });

  it('should set selectedTab when onSelect is called', () => {
    const mockTab = { heading: 'Test Tab' } as any;
    component.onSelect(mockTab);
    expect(component.selectedTab).toBe('Test Tab');
  });
});
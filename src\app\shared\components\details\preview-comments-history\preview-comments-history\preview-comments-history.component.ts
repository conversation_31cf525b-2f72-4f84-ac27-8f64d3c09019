import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { HistoryService } from '@appServices/common/history.service';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { TabDirective } from 'ngx-bootstrap/tabs';


@Component({
  selector: 'app-preview-comments-history',
  templateUrl: './preview-comments-history.component.html',
  styleUrls: ['./preview-comments-history.component.scss']
})
export class PreviewCommentsHistoryComponent implements OnInit {
  imageID: string;
  loading: boolean;
  source: string;
  @Input('preview') preview;
  @Input('offersArray') offersArray;
  @Input('reqId') reqId;
  @Input('offerId') offerId;
  @Input('groupId') groupId;
  @Input('templateId') templateId
  @Input('groupPage') groupPage;
  @Input('isNutriTagPG') groupNutriTag = false;
  @Input()offerProgramCode;
  @Output() postCommentClick = new EventEmitter();
  currentRoute = this.router.url;
  isInORView = false;
  isBPGView = false;
  isOfferView = false;
  isConfigGroupView = false;
  showPoints: boolean = false;
  CONSTANTS = CONSTANTS;
  viewOfferRequestsHistoryPermission = CONSTANTS.Permissions.ViewOfferRequestsHistory;
  selectedTab: string;

  constructor(public uploadImagesService: UploadImagesService,
              public historyService: HistoryService,
              private router: Router,
              private featureFlagsService: FeatureFlagsService) {
                // intentionally left empty
  }
  ngOnInit() {

    this.uploadImagesService.getImage().subscribe((id) => {
      this.imageID = id;
    });

    this.uploadImagesService.getLoading().subscribe((value) => {
      this.loading = value;
    });
    // TO DO: In fuure when we need to show history for offers as well, remove the below logic. Also, remove isInORView flag from template
    if (this.currentRoute.indexOf(`/${ROUTES_CONST.REQUEST.Request}`) !== -1 ||
       this.currentRoute.indexOf(`/${ROUTES_CONST.TEMPLATES.Template}`) !== -1) {
      this.isInORView = true;

    } else if(this.currentRoute.indexOf(`/${ROUTES_CONST.OFFERS.Offers}`) !== -1) {
      this.isOfferView = true;
    } else if(this.currentRoute.indexOf(`/${ROUTES_CONST.GROUPS.StoreDetails}`) !== -1 || 
      this.currentRoute.indexOf(`/${ROUTES_CONST.GROUPS.PointsDetails}`) !== -1 || 
      this.currentRoute.indexOf(`/${ROUTES_CONST.GROUPS.CustomerDetails}`) !== -1 || 
      this.currentRoute.indexOf(`/${ROUTES_CONST.GROUPS.ProductDetails}`) !== -1) {
        this.isConfigGroupView = true;
    }
    if(this.currentRoute.indexOf(`/${ROUTES_CONST.GROUPS.BaseCreate}`) !== -1 ||
    this.currentRoute.indexOf(`/${ROUTES_CONST.GROUPS.BaseEdit}`) !== -1) {
      this.isBPGView = true;
    }

  }

  onSelect(data: TabDirective) {
    this.selectedTab = data.heading;
  }
  postCommentForBPG(commentText) {
    this.postCommentClick.emit(commentText);
  }
}

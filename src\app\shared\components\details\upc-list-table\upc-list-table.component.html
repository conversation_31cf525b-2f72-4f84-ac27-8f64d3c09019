<div>
  
  <div class="view-pg-link">
    <a *ngIf="pgId" href="javascript:void(0)" (click)="openPGInNewTab()">View Product Group</a>
  </div>
  
  <div *ngIf="rowsData">
    <ngx-datatable
      *ngIf="isDisplayUpcTable"
      class="upc-list-table"
      [columnMode]="'flex'"
      [rows]="rowsData"
      [count]="rowsData.length"
      [columnMode]="ColumnMode.flex"
      [headerHeight]="40"
      rowHeight="auto"
    >
      <ngx-datatable-column name="UPC" prop="upc" [sortable]="false" [flexGrow]="2"></ngx-datatable-column>
      <ngx-datatable-column name="Description" prop="description" [sortable]="false" [flexGrow]="4"></ngx-datatable-column>
      <ngx-datatable-column name="Qty" prop="quantity" [sortable]="false" [flexGrow]="1"></ngx-datatable-column>
      <ngx-datatable-column name="UOM" prop="uom" [sortable]="false" [flexGrow]="1"></ngx-datatable-column>
    </ngx-datatable>
  </div>
</div>

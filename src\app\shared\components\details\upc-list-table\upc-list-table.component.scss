@import "../../../../../scss/variables";

.upc-list-table.ngx-datatable{
    font-size: 12px;
    overflow-y: scroll !important;

  .datatable-header {
    padding: 8px 5px 5px 15px;
    background: #EBEBEB;

    .datatable-header-inner {
      font-size: $base-font-size;
      font-weight: bold !important;
    }
  }
  .datatable-body {
    max-height: 150px !important;
    overflow: auto;
    overflow-x: hidden;
    .datatable-body-row {
      padding: 8px 8px 8px 15px;
      border: 1px solid #dedede;
      border-top: none;
      background: white;
    }
  }
}

.view-pg-link {
  position: absolute; 
  top: -25px;
  z-index: 10;
  right: 14px;
}
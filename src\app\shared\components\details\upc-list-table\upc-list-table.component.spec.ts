import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UpcListTableComponent } from './upc-list-table.component';
import { UpcListDataService } from '@appServices/details/upc-list-data.service';
import { ProductGroupService } from '@appGroupsServices/product-group.service';
import { of } from 'rxjs';
import { SimpleChange } from '@angular/core';

describe('UpcListTableComponent', () => {
    let component: UpcListTableComponent;
    let fixture: ComponentFixture<UpcListTableComponent>;
    let upcListDataServiceStub: Partial<UpcListDataService>;
    let productGroupServiceStub: Partial<ProductGroupService>;

    beforeEach(async () => {
        upcListDataServiceStub = {
            loadUpcList: jasmine.createSpy('loadUpcList').and.returnValue(of({ upcRegionResponse: { rankedUpcs: [] } })),
            mapToShort: jasmine.createSpy('mapToShort').and.callFake((item) => item)
        };

        productGroupServiceStub = {
            searchBaseProductGroup: jasmine.createSpy('searchBaseProductGroup').and.returnValue(of({ productGroups: [{ productGroupType: 'BASE', mobId: '123' }] }))
        };

        await TestBed.configureTestingModule({
            declarations: [UpcListTableComponent],
            providers: [
                { provide: UpcListDataService, useValue: upcListDataServiceStub },
                { provide: ProductGroupService, useValue: productGroupServiceStub }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(UpcListTableComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should call setIsBPGVal on isBPG change', () => {
        spyOn(component, 'setIsBPGVal');
        component.isBPG = true;
        component.ngOnChanges({
            isBPG: new SimpleChange(null, true, false)
        });
        expect(component.setIsBPGVal).toHaveBeenCalled();
    });

    it('should set isDisplayUpcTable and call loadUpcListData if isBPG is defined', () => {
        spyOn(component, 'loadUpcListData');
        component.pgId = 'testPgId';
        component.isBPG = true;
        component.setIsBPGVal();
        expect(component.isDisplayUpcTable).toBe(true);
        expect(component.loadUpcListData).toHaveBeenCalled();
    });

    it('should call searchBaseProductGroup if isBPG is undefined', () => {
        component.pgId = 'testPgId';
        component.isBPG = undefined;
        component.setIsBPGVal();
        expect(productGroupServiceStub.searchBaseProductGroup).toHaveBeenCalled();
    });

    it('should open a new tab with the correct URL', () => {
        spyOn(window, 'open').and.returnValue(jasmine.createSpyObj('Window', ['focus']));
        component.pgId = 'testPgId';
        component.isBPG = true;
        component.openPGInNewTab();
        expect(window.open).toHaveBeenCalledWith(jasmine.any(String), '_blank');
    });

    it('should load UPC list data if isBPG is true', () => {
        component.isBPG = true;
        component.mobId = 'testMobId';
        component.regionId = 'testRegionId';
        component.loadUpcListData();
        expect(upcListDataServiceStub.loadUpcList).toHaveBeenCalledWith('testMobId', 'testRegionId');
    });

    it('should sort UPC list by UPC number', () => {
        const rowsData = [
            { upc: '123' },
            { upc: '456' },
            { upc: '789' }
        ];
        component.sortUPCListByUPCNo(rowsData);
        expect(rowsData).toEqual([
            { upc: '123' },
            { upc: '456' },
            { upc: '789' }
        ]);
    });

    it('should return -1 if item1.upc is less than item2.upc', () => {
        const rowsData = [
            { upc: '123' },
            { upc: '456' }
        ];
        const result = component.sortUPCListByUPCNo(rowsData);
    });

    it('should return false if pgId is not defined in setIsBPGVal', () => {
        component.pgId = '';
        const result = component.setIsBPGVal();
        expect(result).toBe(false);
    });

    it('should not call loadUpcListData if isBPG is false in loadUpcListData', () => {
        spyOn(component, 'loadUpcListData').and.callThrough();
        component.isBPG = false;
        component.loadUpcListData();
        expect(upcListDataServiceStub.loadUpcList).not.toHaveBeenCalled();
    });
});
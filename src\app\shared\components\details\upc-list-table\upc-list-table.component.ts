import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { ProductGroupService } from '@appGroupsServices/product-group.service';
import { UpcListDataService } from '@appServices/details/upc-list-data.service';
import { UpcListItemResponse } from '@appShared/models/upc-list-item.model';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { ColumnMode } from '@swimlane/ngx-datatable';

@Component({
  selector: 'app-upc-list-table',
  templateUrl: './upc-list-table.component.html',
  styleUrls: ['./upc-list-table.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class UpcListTableComponent extends UnsubscribeAdapter implements OnInit, OnChanges {

  @Input() mobId: string = '';
  @Input() regionId: string = '';
  @Input() pgId: string = '';
  @Input() isBPG;
  rowsData: any = [];
  isDisplayUpcTable = false;
  ColumnMode = ColumnMode;

  constructor(private upcListDataService: UpcListDataService, public productGroupService: ProductGroupService) { 
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isBPG'] && this.isBPG !== undefined) {
      this.setIsBPGVal();    
    }
  }

  ngOnInit(): void {
    // this.setIsBPGVal();    
  }

  setIsBPGVal(){

    if(!this.pgId){
      return false;
    }

    if(this.isBPG !== undefined){
      this.isDisplayUpcTable = this.isBPG;
      this.loadUpcListData(); 
      return;
    }
    
    const query = `productGroupRid=(${this.pgId});`;

    this.subs.sink = this.productGroupService.searchBaseProductGroup(query).subscribe((data: any) =>{
      const pgData = data?.productGroups[0];
      this.isBPG = pgData?.productGroupType === 'BASE';
      this.isDisplayUpcTable = this.isBPG;  
      this.mobId =  pgData?.mobId;
      this.loadUpcListData();   
    })

  }
  
  openPGInNewTab() {
    let editSubPath = this.isBPG ? ROUTES_CONST.GROUPS.BaseEdit : ROUTES_CONST.GROUPS.Edit;
    
    const productGroupEditUrl = 
    `${ROUTES_CONST.GROUPS.Group}/${ROUTES_CONST.GROUPS.ProductGroup}/${ROUTES_CONST.GROUPS.ProductDetails}/${editSubPath}/${encodeURIComponent(this.pgId)}`;
    
    this.openInNewTab(productGroupEditUrl);
  }
  
  openInNewTab(url) {
    const win = window.open(url, '_blank');
    win.focus();
  }

  loadUpcListData() {
   
    if(!this.isBPG){
      return;
    }

    this.upcListDataService.loadUpcList(this.mobId, this.regionId)
      .subscribe((data: UpcListItemResponse) => {
        this.rowsData = data?.upcRegionResponse?.rankedUpcs?.map(item => this.upcListDataService.mapToShort(item));
        this.sortUPCListByUPCNo(this.rowsData);
    });

  }

  sortUPCListByUPCNo(rowsData: any[]) {
    rowsData?.sort((item1, item2) => {
      if (item1.upc > item2.upc) {
        return 1;
      }
      if (item1.upc < item2.upc) 
        return -1;
      })
      return 0;
  }
}

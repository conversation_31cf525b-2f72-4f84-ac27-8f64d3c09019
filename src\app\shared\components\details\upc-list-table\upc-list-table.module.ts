// Angular Imports
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxDatatableModule } from '@swimlane/ngx-datatable';

// This Module's Components
import { OfferGridModule } from '@appModules/offers/shared/components/offer-grid/offer-grid.module';
import { UpcListDataService } from '@appServices/details/upc-list-data.service';
import { AppCommonModule } from '../../../../modules/common/app.common.module';
import { UpcListTableComponent } from './upc-list-table.component';

@NgModule({
    imports: [
        CommonModule,
        AppCommonModule,
        OfferGridModule,
        NgxDatatableModule
    ],
    declarations: [
        UpcListTableComponent
    ],
    providers: [
      UpcListDataService  
    ],
    exports: [
        UpcListTableComponent
    ]
})
export class UpcListTableModule {

}

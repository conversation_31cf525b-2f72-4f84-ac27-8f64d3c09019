<div class="col-auto d-flex justify-content-start">
    <div class="'d-flex flex-row flex-wrap'" >
        <span class="mb-0 pt-7em sort-label">Sort By: </span>
            <form class="sorting-container" [formGroup]="sortOptionForm">
                <label class="ml-2 mb-0 mt-0 text-label">
                    <div class="dropdown show">
                        <select #select class="custom-select form-control" (change)="sortClickHandler($event)" formControlName="sortValue">
                            <ng-container *ngFor="let item of inputSortOptions">
                                <option  [value]="item.field">
                                    {{ item.label }}
                                </option>
                            </ng-container>
                        </select>
                    </div>
                </label>
                <span (click)="arrowClickHandler()">
                    <div *ngIf="sortType === 'DESC'; else showAscSort">
                        <label class="mt-2 ml-2 mr-1 cursor-pointer">
                            <img src="assets/icons/descending-icon.svg"
                                 alt="" />
                        </label>
                    </div>
                    <ng-template #showAscSort>
                        <label class="mt-2 ml-2 mr-1 cursor-pointer">
                            <img src="assets/icons/ascending-icon.svg"
                                 alt="" />
                        </label>
                    </ng-template>
                </span>
            </form>
    </div>
</div>
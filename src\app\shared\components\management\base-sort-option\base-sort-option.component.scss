@import "../../../../../scss/colors";
@import "../../../../../scss/inputs.scss";

.sorting-container {
    display: inline-flex;

    .dropdown {
        width: 167px;
    }

    .text-label {
        font-size: 16px;
        white-space: nowrap;
        word-break: break-all;
    }

    .dropdown-toggle {
        min-width: 180px;
    }

    .dropdown-menu {
        top: 100%;
        z-index: 1000;
        float: left;
        // min-width: 160px;
        margin: 2px 0 0;
        font-size: 14px;
        text-align: left;
        list-style: none;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ccc;
        border-radius: 0;
        min-width: 7rem !important;
        width: 100%;
        box-shadow: none;
    }

    .cursor-pointer {
        cursor: pointer;
    }
}
.sort-label {
    white-space: nowrap;
    font-size: 14px;
    color: #0f0f0f;
}


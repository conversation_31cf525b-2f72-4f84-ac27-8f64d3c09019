import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CommonSearchService } from '../../../services/common/common-search.service';
import { QueryGenerator } from '../../../services/common/queryGenerator.service';
import { BaseInputSearchService } from '../../../services/management/base-input-search.service';

@Component({
    selector: 'app-base-sort-option',
    templateUrl: './base-sort-option.component.html',
    styleUrls: ['./base-sort-option.component.scss']
})
export class BaseSortOptionComponent implements OnInit {

    public sortOptionList;
    @Input() currentSearch;
    sortFormOptionGroups: any;
    sortOptionForm: UntypedFormGroup
    inputSortOptions: any;
    defaultListSelection: any;
    selectedOption: any;

    sortBy = {
        DESC: 'ASC',
        ASC: 'DESC'
    }
    selectedSort: any;

    constructor(public commonSearchService: CommonSearchService,
        public baseInputSearchService: BaseInputSearchService, public queryGenerator: QueryGenerator) {
        // intentionally left empty
    }

    ngOnInit(): void {
        this.baseInputSearchService.setActiveCurrentSearchType(this.currentSearch);

        const sortOption = this.commonSearchService.sortOption?.[this.currentSearch];
        if (!sortOption) {
            this.commonSearchService.setAllFilterOptions({ key: this.commonSearchService.currentRouter, currentRouter: this.commonSearchService.currentRouter, isPersist: true });
        } else {
            this.commonSearchService.setFilters({ key: this.commonSearchService.currentRouter, currentRouter: this.commonSearchService.currentRouter, isPersist: true });
        }
        this.getSelectedSortList();

    }
    get sortValue() {
        return this.formGroup?.value?.sortValue;
    }

    get sortType() {
        return this.formGroup?.value?.sortType;
    }
    get formGroup() {
        return this.sortOptionForm
    }


    sortClickHandler(event = null) {
        this.sortSearch(true);

    }

    arrowClickHandler() {
        const sortBy = this.sortBy[this.sortType];
        this.formGroup.controls['sortType'].setValue(sortBy);
        this.sortSearch(false);


    }

    getSelectedSortList() {

        this.sortOptionList = this.baseInputSearchService.sortOptions;
        this.inputSortOptions = this.sortOptionList?.map(sortOptions => {
            const { field, label, select, query, defaultQuery } = sortOptions;

            if (select) {
                this.createOptionFormGroup(sortOptions.field, sortOptions.query);
                this.defaultListSelection = sortOptions
            }
            return { field, label, select, query, defaultQuery };
        });


    }

    sortSearch(isSelected) {

        this.selectedSort = this.baseInputSearchService.getSortFieldSelected(this.sortValue)
        this.defaultListSelection = this.selectedSort
        this.sortOptionForm.get("sortValue").setValue(this.defaultListSelection.field)


        if (this.defaultListSelection) {

            this.defaultListSelection.select = true
            this.defaultListSelection.query = isSelected ? this.defaultListSelection.defaultQuery : this.sortOptionForm.value.sortType
            this.baseInputSearchService.setQueryValForSortOption(this.defaultListSelection)
            this.formGroup.controls['sortType'].setValue(this.defaultListSelection.query)

            const query = this.baseInputSearchService.formQuery(this.commonSearchService.getFilterOption(this.currentSearch),
                this.commonSearchService.getInputSearchOption(this.currentSearch),
                this.commonSearchService.getDefaultOption(this.currentSearch),
                this.commonSearchService.getSortOption(this.currentSearch));

            this.baseInputSearchService.setFormQuery(query);
            this.baseInputSearchService.postDataForInputSearch(true)
        }

    }


    createOptionFormGroup(defaultValue, defaultType) {
        const group = {
            sortValue: new UntypedFormControl(defaultValue),
            sortType: new UntypedFormControl(defaultType)
        };
        this.sortOptionForm = new UntypedFormGroup(group);
    }

}



import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CommonSearchService } from '../../../services/common/common-search.service';
import { QueryGenerator } from '../../../services/common/queryGenerator.service';
import { BaseInputSearchService } from '../../../services/management/base-input-search.service';
import { BaseSortOptionComponent } from './base-sort-option.component';

describe('BaseSortOptionComponent', () => {
  let component: BaseSortOptionComponent;
  let fixture: ComponentFixture<BaseSortOptionComponent>;
  let commonSearchService: CommonSearchService;
  let baseInputSearchService: BaseInputSearchService;
  let queryGenerator: QueryGenerator;

  beforeEach(async () => {
    const commonSearchServiceMock = jasmine.createSpyObj('CommonSearchService', [
      'setActiveCurrentSearchType',
      'setAllFilterOptions',
      'setFilters',
      'getFilterOption',
      'getInputSearchOption',
      'getDefaultOption',
      'getSortOption'
    ]);

    const baseInputSearchServiceMock = jasmine.createSpyObj('BaseInputSearchService', [
      'setActiveCurrentSearchType',
      'sortOptions',
      'getSortFieldSelected',
      'setQueryValForSortOption',
      'formQuery',
      'setFormQuery',
      'postDataForInputSearch'
    ]);

    baseInputSearchServiceMock.sortOptions = [ 
        { field: 'testField', label: 'testLabel', select: true, query: 'testQuery', defaultQuery: 'testDefaultQuery' }
    ];

    baseInputSearchServiceMock.getSortFieldSelected.and.callFake((field) => {
      return baseInputSearchServiceMock.sortOptions.find(option => option.field === field) || undefined;
    });

    const queryGeneratorMock = jasmine.createSpyObj('QueryGenerator', ['generateQuery']);

    await TestBed.configureTestingModule({
      declarations: [BaseSortOptionComponent],
      providers: [
        { provide: CommonSearchService, useValue: commonSearchServiceMock },
        { provide: BaseInputSearchService, useValue: baseInputSearchServiceMock },
        { provide: QueryGenerator, useValue: queryGeneratorMock }
      ]
    }).compileComponents();

    commonSearchService = TestBed.inject(CommonSearchService);
    baseInputSearchService = TestBed.inject(BaseInputSearchService);
    queryGenerator = TestBed.inject(QueryGenerator);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BaseSortOptionComponent);
    component = fixture.componentInstance;

    component.currentSearch = 'testSearch';
    component.sortOptionForm = new UntypedFormGroup({
      sortValue: new UntypedFormControl(''),
      sortType: new UntypedFormControl('')
    });

    commonSearchService.sortOption = { testSearch: { field: 'testField', label: 'testLabel', select: true, query: 'testQuery', defaultQuery: 'testDefaultQuery' } };

    fixture.detectChanges();
  });

  // beforeEach(() => {
  //   // Move spy setup to a beforeEach block to avoid multiple spies on the same method
  //   spyOn(baseInputSearchService, 'getSortFieldSelected').and.returnValue({
  //     field: 'testField',
  //     label: 'testLabel',
  //     select: true,
  //     query: 'testQuery',
  //     defaultQuery: 'testDefaultQuery'
  //   });
  // });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component and call necessary methods when sortOption is missing', () => {
    spyOn(component, 'getSelectedSortList');
    commonSearchService.sortOption = {};
    (commonSearchService.setFilters as jasmine.Spy).calls.reset();
    (commonSearchService.setAllFilterOptions as jasmine.Spy).calls.reset();
    component.ngOnInit();
    expect(baseInputSearchService.setActiveCurrentSearchType).toHaveBeenCalledWith('testSearch');
    expect(commonSearchService.setAllFilterOptions).toHaveBeenCalled();
    expect(commonSearchService.setFilters).not.toHaveBeenCalled(); 
    expect(component.getSelectedSortList).toHaveBeenCalled();
  });

  it('should initialize component and call setFilters when sortOption exists', () => {
    spyOn(component, 'getSelectedSortList');
    commonSearchService.sortOption = { testSearch: { field: 'testField', label: 'testLabel', select: true, query: 'testQuery', defaultQuery: 'testDefaultQuery' } };
    component.ngOnInit();
    expect(commonSearchService.setAllFilterOptions).not.toHaveBeenCalled();
    expect(commonSearchService.setFilters).toHaveBeenCalled();
    expect(component.getSelectedSortList).toHaveBeenCalled();
  });

  it('should call sortSearch on sortClickHandler', () => {
    spyOn(component, 'sortSearch');

    component.sortClickHandler();

    expect(component.sortSearch).toHaveBeenCalledWith(true);
  });

  it('should call sortSearch on arrowClickHandler', () => {
    spyOn(component, 'sortSearch');

    component.arrowClickHandler();

    expect(component.sortSearch).toHaveBeenCalledWith(false);
  });

  it('should create option form group', () => {
    component.createOptionFormGroup('testValue', 'testType');

    expect(component.sortOptionForm.value).toEqual({ sortValue: 'testValue', sortType: 'testType' });
  });

  it('should get selected sort list', () => {
    (baseInputSearchService as any).sortOptions = [
      { field: 'testField', label: 'testLabel', select: true, query: 'testQuery', defaultQuery: 'testDefaultQuery' }
    ];

    component.getSelectedSortList();

    expect(component.inputSortOptions).toEqual([
      { field: 'testField', label: 'testLabel', select: true, query: 'testQuery', defaultQuery: 'testDefaultQuery' }
    ]);
    expect(component.defaultListSelection).toEqual({
      field: 'testField',
      label: 'testLabel',
      select: true,
      query: 'testQuery',
      defaultQuery: 'testDefaultQuery'
    });
  });

  it('should perform sort search', () => {
    component.sortOptionForm.setValue({ sortValue: 'testField', sortType: 'ASC' });
    component.sortSearch(true);
    expect(baseInputSearchService.getSortFieldSelected).toHaveBeenCalledWith('testField');
    expect(baseInputSearchService.setQueryValForSortOption).toHaveBeenCalled();
    expect(baseInputSearchService.formQuery).toHaveBeenCalled();
    expect(baseInputSearchService.setFormQuery).toHaveBeenCalled();
    expect(baseInputSearchService.postDataForInputSearch).toHaveBeenCalledWith(true);
  });
});
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { BaseBatchActionComponent } from './base-batch-action.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Observable, of, Subject } from 'rxjs';
import { AppInjector } from '@appServices/common/app.injector.service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { CONSTANTS } from '@appConstants/constants';

describe('BaseBatchActionComponent', () => {
  let component: BaseBatchActionComponent;
  let fixture: ComponentFixture<BaseBatchActionComponent>;

  let searchOfferServiceMock: any;
  let searchOfferRequestServiceMock: any;
  let bulkUpdateServiceMock: any;
  let modalServiceMock: any;
  let queryGeneratorMock: any;
  let toasterServiceMock: any;
  let featureFlagsServiceMock: any;
  let baseInputSearchServiceMock: any;
  let commonSearchServiceMock: any;
  let facetItemServiceMock: any;

  beforeEach(async () => {
    searchOfferServiceMock = {
      searchAllOffers: jasmine.createSpy('searchAllOffers').and.returnValue(of({})),
    };

    searchOfferRequestServiceMock = {
      searchAllOfferRequest: jasmine.createSpy('searchAllOfferRequest').and.returnValue(of({})),
      getOfferDetails: jasmine.createSpy('getOfferDetails'),
    };

    bulkUpdateServiceMock = {
      isAllBatchSelected: new Subject<string>(),
      templatePreBatch: jasmine.createSpy('templatePreBatch').and.returnValue(of({ offersForUpdate: { invalid: [] }, requestId: 123 })),
      preCheckBatch: jasmine.createSpy('preCheckBatch').and.returnValue(of({ offersForUpdate: { invalid: [] }, requestId: 456 })),
      bulkActionRequest: jasmine.createSpy('bulkActionRequest').and.returnValue(of({})),
      registerBatchExpand: jasmine.createSpy('registerBatchExpand').and.returnValue(of({})),
      checkIfActionEnabledForUniversalJob: jasmine.createSpy('checkIfActionEnabledForUniversalJob').and.returnValue(false),
      isSelectionReset: {
        next: jasmine.createSpy('next')
      }
    };

    modalServiceMock = {
      show: jasmine.createSpy('show').and.returnValue({} as BsModalRef),
    };

    queryGeneratorMock = {
      getQuery: jasmine.createSpy('getQuery').and.returnValue('querystring'),
      getQueryWithFilter: jasmine.createSpy('getQueryWithFilter').and.returnValue(['filter1', 'filter2']),
      removeParameters: jasmine.createSpy('removeParameters'),
      getQueryFilter: jasmine.createSpy('getQueryFilter').and.returnValue('test OR test'),
      getQueryWithOrFilter: jasmine.createSpy('getQueryWithOrFilter').and.returnValue('queryWithOrFilter'),
    };

    toasterServiceMock = {
      success: jasmine.createSpy('success'),
      error: jasmine.createSpy('error'),
    };

    featureFlagsServiceMock = {
      isFeatureFlagEnabled: jasmine.createSpy('isFeatureFlagEnabled').and.returnValue(true),
      isuppEnabled: true,
      isOfferRequestArchivalEnabled: false
    };

    baseInputSearchServiceMock = {
      removeParametersForTemplates: jasmine.createSpy('removeParametersForTemplates').and.returnValue('queryVal'),
      getQueryWithOrFilter: jasmine.createSpy('getQueryWithOrFilter').and.returnValue(['filterA', 'filterB']),
      queryForInputAndFilter: 'filterquery',
      getFormQuery: jasmine.createSpy('getFormQuery').and.returnValue('formquery'),
      postDataForInputSearch: jasmine.createSpy('postDataForInputSearch')
    };

    commonSearchServiceMock = {
      isShowExpiredInQuery: false,
      setQueryOptionsForBpd: jasmine.createSpy('setQueryOptionsForBpd')
    };

    facetItemServiceMock = {};

    spyOn(AppInjector, 'getInjector').and.returnValue({
      get: (service: any) => {
        switch (service) {
          case SearchOfferService: return searchOfferServiceMock;
          case SearchOfferRequestService: return searchOfferRequestServiceMock;
          case BulkUpdateService: return bulkUpdateServiceMock;
          case BsModalService: return modalServiceMock;
          case QueryGenerator: return queryGeneratorMock;
          case ToastrService: return toasterServiceMock;
          case FeatureFlagsService: return featureFlagsServiceMock;
          case BaseInputSearchService: return baseInputSearchServiceMock;
          case CommonSearchService: return commonSearchServiceMock;
          case FacetItemService: return facetItemServiceMock;
          default: return null;
        }
      }
    });

    await TestBed.configureTestingModule({
      declarations: [BaseBatchActionComponent],
      providers: []
    }).compileComponents();

    fixture = TestBed.createComponent(BaseBatchActionComponent);
    component = fixture.componentInstance;
   
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('injectServices()', () => {
    it('should inject all services via AppInjector', () => {
      component.injectServices();
      expect(component._searchOfferService).toBe(searchOfferServiceMock);
      expect(component._searchOfferRequestService).toBe(searchOfferRequestServiceMock);
      expect(component.bulkUpdateService).toBe(bulkUpdateServiceMock);
      expect(component._modalService).toBe(modalServiceMock);
      expect(component.queryGenerator).toBe(queryGeneratorMock);
      expect(component._toaster).toBe(toasterServiceMock);
      expect(component.featureFlagService).toBe(featureFlagsServiceMock);
      expect(component.baseInputSearchService).toBe(baseInputSearchServiceMock);
      expect(component.commonSearchService).toBe(commonSearchServiceMock);
      expect(component.facetItemService).toBe(facetItemServiceMock);
    });
  });

  describe('initSubscribes()', () => {
    it('should subscribe to bulkUpdateService.isAllBatchSelected', () => {
      component.initSubscribes();
      bulkUpdateServiceMock.isAllBatchSelected.next('testValue');
      expect(component.isAllBatchSelected).toBe('testValue');
    });
  });

  describe('getBatchActionsFromRules()', () => {
    const rules = {
      PC1: {
        actions: [
          { key: 'copy' },
          { key: 'delete' }
        ]
      }
    };

    it('should set batchActions and batchRule', () => {
      component.getBatchActionsFromRules('PC1', rules);
      expect(component.batchRule).toEqual(rules.PC1);
      expect(component.batchActions).toEqual(rules.PC1.actions);
    });

    it('should filter batchActions based on feature flags when actionPage is "request"', () => {
      component.commonSearchService.isShowExpiredInQuery = false;

      component.getBatchActionsFromRules('PC1', rules, 'request', true);
      expect(component.batchActions.find(a => a.key === 'copy')).toBeUndefined();
    });
  });

  describe('checkForFeatureFlag getter', () => {
    it('should return true if no featureFlag in batchRule', () => {
      component.batchRule = {};
      expect(component.checkForFeatureFlag).toBeTrue();
    });
    it('should call featureFlagService.isFeatureFlagEnabled if featureFlag exists', () => {
      component.batchRule = { featureFlag: 'testFlag' };
      featureFlagsServiceMock.isFeatureFlagEnabled.and.returnValue(false);
      expect(component.checkForFeatureFlag).toBeFalse();
      expect(featureFlagsServiceMock.isFeatureFlagEnabled).toHaveBeenCalledWith('testFlag');
    });
  });

  describe('openModal()', () => {
    it('should call modalService.show with correct params', () => {
      const template = {};
      const options = { class: 'modal-test' };
      component.openModal(template, options);
      expect(modalServiceMock.show).toHaveBeenCalledWith(template, options);
      expect(component.modalRef).toBeDefined();
    });
  });

  describe('getQueryForPreCheck()', () => {
    it('should return correct query when batchType is selectAcrossAllPages and is BPD or Template', () => {
      spyOn(component, 'isBpdReqOrTemplate').and.returnValue(true);
      spyOn(component, 'formQueryForBpdOrTemplate').and.returnValue({ query: 'queryBpd' });
      const payloadObj = {
        payload: ['id1', 'id2'],
        batchType: 'selectAcrossAllPages',
        isBpdReqOrTemplate: true
      };
      const result = component.getQueryForPreCheck(payloadObj);
      expect(result).toEqual({ query: 'queryBpd' });
    });

    

    it('should return payload as is if isBpdReqOrTemplate is false', () => {
      spyOn(component, 'isBpdReqOrTemplate').and.returnValue(false);
      const payloadObj = {
        payload: ['id1', 'id2'],
        batchType: 'selectAllOnPage',
        isBpdReqOrTemplate: false,
        key: 'test'
      };
      const result = component.getQueryForPreCheck(payloadObj);
      expect(result).toEqual({ query: 'test=(id1 OR id2);' });
    });
  });

  describe('isBpdReqOrTemplate()', () => {
    it('should return true if actionPage is Template and isBpdOrTemplate is true', () => {
    const page =CONSTANTS.TEMPLATE;
    const pCode = CONSTANTS.BPD;
      expect(component.isBpdReqOrTemplate(page, pCode)).toBeTrue();
    });

    it('should return false if actionPage is request and pCode is not bpd', () => {
      const page =CONSTANTS.REQUEST;
    const pCode = CONSTANTS.ACTIVE;
      expect(component.isBpdReqOrTemplate(page, pCode)).toBeFalse();
    });
  });

  describe('doPreCheckBatch()', () => {
    beforeEach(() => {
      spyOn(component, 'resetSelection');
      spyOn(component, 'handleSuccessPreCheck');
      spyOn(component, 'handleFailesPreCheck');
    });
  
    it('should call specified apiFuncName function and handle success when no invalid offers', fakeAsync(() => {
      const action = {
        asyncActionKey: 'copy',
        apiFuncName: 'templatePreBatch',
        isResetSelection: true
      };
      const payload = ['id1', 'id2'];
  
      bulkUpdateServiceMock.templatePreBatch.and.returnValue(of({
        offersForUpdate: { invalid: [] },
        requestId: 999
      }));
  
      component.doPreCheckBatch(action, payload);
      tick();
  
      expect(bulkUpdateServiceMock.templatePreBatch).toHaveBeenCalledWith(payload, 'copy');
      expect(component.resetSelection).toHaveBeenCalled();
      expect(component.handleSuccessPreCheck).toHaveBeenCalledWith(999, action);
      expect(component.handleFailesPreCheck).not.toHaveBeenCalled();
    }));
  
    it('should call default preCheckBatch function and handle failure when invalid offers present', fakeAsync(() => {
      const action = {
        asyncActionKey: 'delete',
        isResetSelection: false
      };
      const payload = ['id3'];
  
      bulkUpdateServiceMock.preCheckBatch.and.returnValue(of({
        offersForUpdate: { invalid: ['badOffer'] },
        requestId: 555
      }));
  
      component.doPreCheckBatch(action, payload);
      tick();
  
      expect(bulkUpdateServiceMock.preCheckBatch).toHaveBeenCalledWith(payload, 'delete');
      expect(component.resetSelection).not.toHaveBeenCalled();
      expect(component.handleSuccessPreCheck).not.toHaveBeenCalled();
      expect(component.handleFailesPreCheck).toHaveBeenCalledWith(
        { offersForUpdate: { invalid: ['badOffer'] }, requestId: 555 }, action
      );
    }));
  
    
  });
  

  describe('handleSuccessPreCheck()', () => {
    it('should set batchReqID and openModelForBatch for recognized keys', () => {
      spyOn(component, 'openModelForBatch');
      const requestId = 123;
      const action = {
        key: 'addEvent',
        modalClass: 'modal-class'
      };
  
      component.handleSuccessPreCheck(requestId, action);
  
      expect(component.batchReqID).toBe(123);
      expect(component.openModelForBatch).toHaveBeenCalledWith('addEvent', 'modal-class');
    });
  
    it('should display success/error popup for default key', () => {
      spyOn(component, 'displaySuccessErrorMsgPopup');
      const requestId = 456;
      const action = {
        key: 'unknownKey',
        onPrecheckSuccess: { someProp: true },
        confirmationMsg: 'Success!',
        errSuccessModalClass: 'error-class'
      };
  
      component.handleSuccessPreCheck(requestId, action);
  
      expect(component.batchReqID).toBe(456);
      expect(component.displaySuccessErrorMsgPopup).toHaveBeenCalledWith(
        { someProp: true, message: 'Success!' },
        'error-class'
      );
    });
  });
  

  describe('doBulkActionCallBasedOnPage()', () => {
    it('should call dynamic bulk update service function, hide modal, and call toaster success', fakeAsync(() => {
      component.modalRef = { hide: jasmine.createSpy('hide') } as any;
      const action = {
        asyncActionKey: 'key1',
        apiFuncName: 'bulkActionRequest',
        postBatchSuccess: null,
        jobType: null,
        universalJobApiFunc: null
      };
      const page = 'Page1';
      const payload = ['id1'];
      const pcSelected = false;
  
  
      component.doBulkActionCallBasedOnPage(payload, action, page, pcSelected);
  
      tick();
  
      expect(toasterServiceMock.success).toHaveBeenCalled();
      expect(component.bulkUpdateService.bulkActionRequest).toHaveBeenCalledWith(payload, 'key1', pcSelected, null);
    }));
  });
  

  describe('onClickBaseAction()', () => {
    it('should call doPreCheckBatch for keys other than copy', () => {
      spyOn(component, 'doPreCheckBatch');
      const action = { key: 'delete', isFirstPreCheck: true };
      const payloadQuery = null;
      const actionType = null;
      const pcSelected = null;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.doPreCheckBatch).toHaveBeenCalled();
    });
  
    it('should call doBulkActionCallBasedOnPage for exportOffers key', () => {
      spyOn(component, 'doBulkActionCallBasedOnPage');
      const action = { key: 'exportOffers', jobType: 'job1', asyncActionKey: 'async1' };
      const payloadQuery = { some: 'query' };
      const actionType = 'offer';
      const pcSelected = null;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.doBulkActionCallBasedOnPage).toHaveBeenCalled();
    });
  
    it('should call doBulkActionCallBasedOnPage when doDirectAsyncCall is true', () => {
      spyOn(component, 'doBulkActionCallBasedOnPage');
      const action = { key: 'someKey', doDirectAsyncCall: true };
      const payloadQuery = { some: 'query' };
      const actionType = 'offer';
      const pcSelected = null;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.doBulkActionCallBasedOnPage).toHaveBeenCalled();
    });
  
    it('should call validateBatchForTemplate for createOfferRequest key', () => {
      spyOn(component, 'validateBatchForTemplate');
      const action = { key: 'createOfferRequest' };
      const payloadQuery = { some: 'query' };
      const actionType = null;
      const pcSelected = null;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.validateBatchForTemplate).toHaveBeenCalled();
    });
  
    it('should call handleCopySC for copy key with pcSelected as CONSTANTS.SC', () => {
      spyOn(component, 'handleCopySC');
      const action = { key: 'copy', modalClass: 'modal-xl' };
      const payloadQuery = null;
      const actionType = null;
      const pcSelected = CONSTANTS.SC;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.handleCopySC).toHaveBeenCalled();
    });
  
    it('should call handleSuccessPreCheck for cancelAll key', () => {
      spyOn(component, 'handleSuccessPreCheck');
      const action = { key: 'cancelAll' };
      const payloadQuery = null;
      const actionType = null;
      const pcSelected = null;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.handleSuccessPreCheck).toHaveBeenCalled();
    });
  
    it('should open modal for other keys', () => {
      spyOn(component, 'openModelForBatch');
      const action = { key: 'otherKey', modalClass: 'modal-xl' };
      const payloadQuery = null;
      const actionType = null;
      const pcSelected = null;
      component.onClickBaseAction(action, payloadQuery, actionType, pcSelected);
      expect(component.openModelForBatch).toHaveBeenCalled();
    });
  });

  describe('formQueryForBpdOrTemplate', () => {
    it('should call removeParametersForTemplates and setDateIfNotPresent and return expected object', () => {
      baseInputSearchServiceMock.removeParametersForTemplates.and.returnValue('queryVal');
      spyOn(component, 'setDateIfNotPresent');
      spyOn(component, 'getQueryFilterOptionsList').and.returnValue(['filter1', 'filter2']);
  
      const result = component.formQueryForBpdOrTemplate('facetPageVal', 'pCodeVal');
  
      expect(component.baseInputSearchService.removeParametersForTemplates).toHaveBeenCalled();
      expect(component.setDateIfNotPresent).toHaveBeenCalledWith('facetPageVal', 'pCodeVal');
      expect(component.getQueryFilterOptionsList).toHaveBeenCalledWith('facetPageVal', 'pCodeVal');
      expect(result).toEqual({ query: 'filterquery', queryWithOrFilters: ['filter1', 'filter2'] });
    });
  
    it('should return object with only query if no filters', () => {
      baseInputSearchServiceMock.removeParametersForTemplates.and.returnValue('queryVal');
      spyOn(component, 'setDateIfNotPresent');
      spyOn(component, 'getQueryFilterOptionsList').and.returnValue([]);
  
      const result = component.formQueryForBpdOrTemplate('facetPageVal', 'pCodeVal');
  
      expect(result).toEqual({ query: 'filterquery' });
    });
  });

  describe('getQueryFilterOptionsList', () => {
    it('should map and replace EX with D if conditions met', () => {
      spyOn(component, 'isBpdAndRequest').and.returnValue(true);
      component.baseInputSearchService.getQueryWithOrFilter = jasmine.createSpy().and.returnValue([
        'someFilter',
        'digitalUiStatus(EX)'
      ]);
  
      const result = component.getQueryFilterOptionsList('facetPage', 'pCode');
  
      expect(result.length).toBe(2);
      expect(result[1]).toContain('D');  // Replaced 'EX' with 'D'
    });
  
    it('should return original filters if conditions not met', () => {
      spyOn(component, 'isBpdAndRequest').and.returnValue(false);
      component.baseInputSearchService.getQueryWithOrFilter = jasmine.createSpy().and.returnValue(['filter1']);
      const result = component.getQueryFilterOptionsList('facetPage', 'pCode');
      expect(result).toEqual(['filter1']);
    });
  });

  describe('setDateIfNotPresent', () => {
    it('should do nothing if not BpdAndRequest', () => {
      spyOn(component, 'isBpdAndRequest').and.returnValue(false);
      const result = component.setDateIfNotPresent('page', 'pCode');
      expect(result).toBeFalse();
    });
  });
  
  describe('formQueryForSelectAllPage', () => {
    it('should call queryGenerator methods and return expected object', () => {
     const queryGeneratorMockData: any = {
        removeParameters: jasmine.createSpy('removeParameters'),
        getQuery: jasmine.createSpy('getQuery').and.returnValue('queryVal'),
        getQueryWithFilter: jasmine.createSpy('getQueryWithFilter').and.returnValue(['filter1'])
      }
      component.queryGenerator = queryGeneratorMockData;
  
      const result = component.formQueryForSelectAllPage();
  
      expect(component.queryGenerator.removeParameters).toHaveBeenCalledWith(['limit', 'sortBy','next', 'sid']);
      expect(component.queryGenerator.getQuery).toHaveBeenCalled();
      expect(component.queryGenerator.getQueryWithFilter).toHaveBeenCalled();
      expect(result).toEqual({ query: 'queryVal', queryWithOrFilters: ['filter1'] });
    });
  
    it('should return object without filters if none present', () => {
      const queryGeneratorMockData: any = {
        removeParameters: jasmine.createSpy('removeParameters'),
        getQuery: jasmine.createSpy('getQuery').and.returnValue('queryVal'),
        getQueryWithFilter: jasmine.createSpy('getQueryWithFilter').and.returnValue([])
      }
      component.queryGenerator = queryGeneratorMockData;
  
      const result = component.formQueryForSelectAllPage();
      expect(result).toEqual({ query: 'queryVal' });
    });
  });

  describe('showToasterMessageAndResetSelection', () => {
    it('should reset selection and show toaster message', () => {
      spyOn(component, 'resetSelection');
      spyOn(component, 'showToastrMessage');
  
      component.showToasterMessageAndResetSelection('Success message');
  
      expect(component.resetSelection).toHaveBeenCalled();
      expect(component.showToastrMessage).toHaveBeenCalledWith('Success message');
    });
  });

  describe('resetSelection', () => {
    it('should call isSelectionReset.next(true)', () => {
      const isSelectionResetMock = jasmine.createSpyObj('isSelectionReset', ['next']);
      component.bulkUpdateService.isSelectionReset = isSelectionResetMock;
      component.resetSelection();
      expect(component.bulkUpdateService.isSelectionReset.next).toHaveBeenCalledWith(true);
    });
  });
  
  describe('showToastrMessage', () => {
    it('should call _toaster.success', () => {
      const toastrServiceMock: any = { success: jasmine.createSpy('success') };
      component._toaster = toastrServiceMock;
      component.showToastrMessage('Test message');
      expect(component._toaster.success).toHaveBeenCalledWith('Test message', '', {});
    });
  });

  describe('searchAllRequest', () => {
    it('should call searchAllOfferRequest and getOfferDetails', fakeAsync(() => {
      const queryGeneratorMockData: any = {
        getQuery: jasmine.createSpy().and.returnValue('queryVal'),
        getQueryWithFilter: jasmine.createSpy().and.returnValue(['filter1'])
      };
      component.queryGenerator = queryGeneratorMockData;
      const searchSpy = searchOfferRequestServiceMock.searchAllOfferRequest.and.returnValue(of({}));
      const detailsSpy = searchOfferRequestServiceMock.getOfferDetails.and.returnValue(of({}));
  
      component.searchAllRequest();
      tick();
  
      expect(searchSpy).toHaveBeenCalled();
      expect(detailsSpy).toHaveBeenCalled();
    }));
  });
  
  describe('validateBatchForTemplate', () => {
    it('should handle success pre-check when no invalid offers', () => {
      const action = { errorMessage: 'error', onPrecheckSuccess: {}, confirmationMsg: 'confirmed' };
      const response = { offersForUpdate: { invalid: [] }, requestId: 123 };
      bulkUpdateServiceMock.templatePreBatch.and.returnValue(of(response));
      spyOn(component, 'handleSuccessPreCheck');
  
      component.validateBatchForTemplate(action, {});
  
      expect(component.handleSuccessPreCheck).toHaveBeenCalledWith(123, action);
      expect(component._toaster.error).not.toHaveBeenCalled();
    });
  
    it('should show toaster error if invalid offers exist', () => {
      const action = { errorMessage: 'error message' };
      const response = { offersForUpdate: { invalid: ['badOffer'] }, requestId: 123 };
      bulkUpdateServiceMock.templatePreBatch.and.returnValue(of(response));
  
      component.validateBatchForTemplate(action, {});
  
      expect(component._toaster.error).toHaveBeenCalledWith('error message', '', {});
    });
  });

  describe('searchAllOffer', () => {
    it('should call searchAllOffers and getOfferDetails', fakeAsync(() => {
      const queryGeneratorMockData: any = {
        getQuery: jasmine.createSpy().and.returnValue('queryVal'),
        getQueryWithFilter: jasmine.createSpy().and.returnValue(['filter1'])
      };
      component.queryGenerator = queryGeneratorMockData;
      searchOfferServiceMock.searchAllOffers.and.returnValue(of({}));
      const detailsSpy = searchOfferRequestServiceMock.getOfferDetails.and.returnValue(of({}));
  
      component.searchAllOffer();
      tick();
  
      expect(detailsSpy).toHaveBeenCalled();
    }));
  });

  describe('displaySuccessErrorMsgPopup', () => {
    it('should assign preCheckResultObj and call openModelForBatch', () => {
      spyOn(component, 'openModelForBatch');
      const preCheckObj = { message: 'success' };
      component.displaySuccessErrorMsgPopup(preCheckObj, 'modalClass');
      expect(component.preCheckResultObj).toEqual(preCheckObj);
      expect(component.openModelForBatch).toHaveBeenCalledWith('commonMsg', 'modalClass');
    });
  });

  describe('handleCopySC', () => {
    beforeEach(() => {
      component.isAllBatchSelected = null;
      const bulkUPdateServiceMOckData: any = {
        deliveryChannelArr: [],
        showDisplayEndDate: false
      };
      component.bulkUpdateService = bulkUPdateServiceMOckData;
      spyOn(component, 'openModelForBatch');
    });
  
    it('should show error if selectAcrossAllPages or selectAllOnPage and no single deliveryChannel or multiple adTypes/deliveryChannels', () => {
      component.isAllBatchSelected = 'selectAllOnPage';
  
      (component.queryGenerator.getQueryFilter as jasmine.Spy).and.callFake((filterName) => null);
      component.handleCopySC('copyKey', 'modalClass');
      expect(component._toaster.error).toHaveBeenCalledWith("You can only perform batch copy on one Channel at a time. Select a single Channel and then try again");
      expect(component.openModelForBatch).not.toHaveBeenCalled();
  
      (component.queryGenerator.getQueryFilter as jasmine.Spy).and.callFake((filterName) => filterName === 'adType' ? 'A OR B' : null);
      component.handleCopySC('copyKey', 'modalClass');
      expect(component._toaster.error).toHaveBeenCalled();
  
      (component.queryGenerator.getQueryFilter as jasmine.Spy).and.callFake((filterName) => filterName === 'deliveryChannel' ? 'C OR D' : null);
      component.handleCopySC('copyKey', 'modalClass');
      expect(component._toaster.error).toHaveBeenCalled();
    });
  
    it('should set showDisplayEndDate true if adType selected is NIA and open modal', () => {
      component.isAllBatchSelected = 'selectAllOnPage';
  
      (component.queryGenerator.getQueryFilter as jasmine.Spy).and.callFake((filterName) => {
        if (filterName === 'adType') return 'NIA';
        return null;
      });
  
      component.handleCopySC('copyKey', 'modalClass');
      expect(component.bulkUpdateService.showDisplayEndDate).toBeTrue();
      expect(component.openModelForBatch).toHaveBeenCalledWith('copyKey', 'modalClass');
    });
  
    it('should show error if deliveryChannelArr has multiple unique values when not selectAll', () => {
      component.isAllBatchSelected = 'none';
      component.bulkUpdateService.deliveryChannelArr = ['A', 'B'];
  
      component.handleCopySC('copyKey', 'modalClass');
  
      expect(component._toaster.error).toHaveBeenCalledWith("You can only perform batch copy on one Channel at a time. Select a single Channel and then try again");
      expect(component.openModelForBatch).not.toHaveBeenCalled();
    });
  
    it('should set showDisplayEndDate true if deliveryChannelArr unique value is DO - NIA and open modal', () => {
      component.isAllBatchSelected = 'none';
      component.bulkUpdateService.deliveryChannelArr = ['DO - NIA'];
  
      component.handleCopySC('copyKey', 'modalClass');
  
      expect(component.bulkUpdateService.showDisplayEndDate).toBeTrue();
      expect(component.openModelForBatch).toHaveBeenCalledWith('copyKey', 'modalClass');
    });
  
    it('should open modal when deliveryChannelArr unique value is not DO - NIA', () => {
      component.isAllBatchSelected = 'none';
      component.bulkUpdateService.deliveryChannelArr = ['DO - XYZ'];
  
      component.handleCopySC('copyKey', 'modalClass');
  
      expect(component.bulkUpdateService.showDisplayEndDate).toBeFalse();
      expect(component.openModelForBatch).toHaveBeenCalledWith('copyKey', 'modalClass');
    });
  });
  

});

import { Component, TemplateRef, ViewChild } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: "base-batch-action",
  templateUrl: './base-batch-action.component.html'
})
export class BaseBatchActionComponent extends UnsubscribeAdapter {

  @ViewChild("commonMsgTmpl")
  public commonMsgTmpl: TemplateRef<any>;

  public _searchOfferService: SearchOfferService;
  public facetItemService: FacetItemService;
  public _searchOfferRequestService: SearchOfferRequestService;
  public bulkUpdateService: BulkUpdateService;
  public _modalService: BsModalService;
  public queryGenerator: QueryGenerator;
  public _toaster: ToastrService;
  public featureFlagService: FeatureFlagsService;
  public baseInputSearchService:BaseInputSearchService;
  public commonSearchService: CommonSearchService;
  
  batchRule;
  modalRef: BsModalRef;
  batchActions;
  action: any = {};
  batchReqID: any;
  preCheckResultObj = {};
  isAllBatchSelected: string;

  constructor() {
    super();
    this.injectServices();
  }
  injectServices() {
    const injector = AppInjector.getInjector();
    this._searchOfferService = injector.get(SearchOfferService);
    this.facetItemService = injector.get(FacetItemService);
    this._searchOfferRequestService = injector.get(SearchOfferRequestService);
    this.bulkUpdateService = injector.get(BulkUpdateService);
    this.featureFlagService = injector.get(FeatureFlagsService);
    this.baseInputSearchService = injector.get(BaseInputSearchService);
    this._modalService = injector.get(BsModalService);
    this.queryGenerator = injector.get(QueryGenerator);
    this._toaster = injector.get(ToastrService);
    this.commonSearchService = injector.get(CommonSearchService);
  }
  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    this.initSubscribes();
  }
  initSubscribes() {
    this.subs.sink = this.bulkUpdateService.isAllBatchSelected.subscribe((value) => {
      this.isAllBatchSelected = value;
    });
  }
  /**
   * 
   * @param pcSelected program code value
   * @param rules rules to get batch actions
   * 
   * Pass Program code and rules, this function will set batch actions list and rule based on PC 
   */
  getBatchActionsFromRules(pcSelected, rules,actionPage="",isUPPSelected=false) {
    this.batchRule = rules?.[pcSelected];
    this.batchActions = this.batchRule?.actions;
    if(actionPage === "request")
    {
      if(this.featureFlagService.isuppEnabled && isUPPSelected)
      {
        this.batchActions = this.batchRule?.actions?.filter((e)=>{return e.key != "copy"});
      }
      if(this.featureFlagService.isOfferRequestArchivalEnabled && this.commonSearchService.isShowExpiredInQuery)
      {
        this.batchActions = this.batchRule?.actions?.filter((e)=>{return e.key=="copy"});
      }
    }
  }
  /**
   * Getter function used in HTML, if batch action need to check featureFlag this function will check
   */
  get checkForFeatureFlag() {
    if (this.batchRule?.featureFlag) {
      return this.featureFlagService.isFeatureFlagEnabled(this.batchRule?.featureFlag);
    }
    return true;
  }
  /**
   * 
   * @param template template name
   * @param options  modal options
   * 
   * This function will open the modal
   */
  openModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
  }
  /**
   * 
   * @param payload list of selected Ids
   * @param batchType  either select all on page or select across all page 
   * @param key this key refers to payload key need to send to backend while api
   * @returns  This function will return payload query on batch action either with selected ids or 
   *  query when all pages selected
   */
  getQueryForPreCheck(payloadObj) {
    const { payload, batchType, key,  progrmCd, facetPage = null} = payloadObj,
    { includeOfferTemplateFlag, isOfferTemplate  } = this.action;
    let payloadQuery = `(${payload.join(' OR ')});`,payloadval, queryValue;
    queryValue = includeOfferTemplateFlag ? `${CONSTANTS.IS_OFFER_TEMPLATE}=${isOfferTemplate};` : ``;
    if (batchType === 'selectAcrossAllPages') {
      payloadval = this.isBpdReqOrTemplate(facetPage, progrmCd) ? this.formQueryForBpdOrTemplate(facetPage, progrmCd) : this.formQueryForSelectAllPage();
      payloadval = { ...payloadval, query: `${payloadval.query}${queryValue}` };
    } else {
      payloadval = { query: `${key}=${payloadQuery}${queryValue}` };
    }
    return payloadval;
  }
  isBpdReqOrTemplate(page, pCode) {
    return this.isBpdAndRequest(page, pCode) || page === CONSTANTS.TEMPLATE;
  }

  isBpdAndRequest(page, pCode) {
    return pCode === CONSTANTS.BPD && page === CONSTANTS.REQUEST;
  }
  /**
   * 
   * @returns For Bpd and Template page we have to take query from Base input service
   */
  formQueryForBpdOrTemplate(facetPage, pCode) {
    this.baseInputSearchService?.removeParametersForTemplates();
    this.setDateIfNotPresent(facetPage, pCode);
    let queryVal = this.baseInputSearchService?.queryForInputAndFilter;
    const queryWithOrFilters = this.getQueryFilterOptionsList(facetPage, pCode);
    return queryWithOrFilters?.length ? { query: queryVal, queryWithOrFilters } : { query: queryVal };
  }
  /**
   * 
   * @param facetPage 
   * @param pCode 
   * @returns  For BPD and OR page, if Expired status is selected, we need to replace EX with D in queryWithOrFilters List
   * This function will return queryWithOrFilters, and do replace action for expired status with D in query if BPD and OR page is there.
   */
  getQueryFilterOptionsList(facetPage, pCode) {
    const queryFiltersList: any = this.baseInputSearchService?.getQueryWithOrFilter(), expiredStatusStr = `(${CONSTANTS.EXPIRED_STATUS_OR})`;
    if (queryFiltersList?.length && this.isBpdAndRequest(facetPage, pCode)) {
      return queryFiltersList?.map((queryFilter) => {
        if (queryFilter?.includes('digitalUiStatus') && queryFilter?.includes(expiredStatusStr)) {
          return queryFilter?.replace(/EX/g, 'D');
        }
        return queryFilter;
      });
    }
    return queryFiltersList;
  }
  setDateIfNotPresent(page, pCode) {
    if(!this.isBpdAndRequest(page, pCode)) {
      return false;
    }
    const formQuery = this.baseInputSearchService.getFormQuery(), queryFilterText = this.baseInputSearchService.queryForInputAndFilter,
    currentDateText = `${CONSTANTS.CURRENT_DATE_QUERY_OR}=`;
    let isHideExpiredStatusReqs = formQuery?.includes(currentDateText) && !queryFilterText.includes(currentDateText);
    if(isHideExpiredStatusReqs) {
      this.commonSearchService.setQueryOptionsForBpd({isHideExpiredStatusReqs});
    }
  }
  /**
   * 
   * @returns For Other than BPD and Template, we have to take queryGenerator for Query and QueryWithOrFilters
   */
  formQueryForSelectAllPage() {
    this.queryGenerator.removeParameters(['limit', 'sortBy','next', 'sid']);
    const queryVal = this.queryGenerator.getQuery(),
    queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    return queryWithOrFilters?.length ? { query: queryVal, queryWithOrFilters } : { query: queryVal };
  }
  /**
   * 
   * @param toastrMessage Toastr message 
   * 
   * This function will responsible for reset selection and to show toastr message
   */
  showToasterMessageAndResetSelection(toastrMessage) {
    this.resetSelection();
    this.showToastrMessage(toastrMessage);
  }
  /**
   * This will reset all batch selection
   */
  resetSelection() {
    this.bulkUpdateService.isSelectionReset.next(true);
  }
  /**
   * 
   * @param message 
   * This function will show toastr message
   */
  showToastrMessage(message) {
    this._toaster.success(message, "", {});
  }
  /**
   * 
   * @param key should mapped to template in html
   * @param modalclass popup class
   * This function will pass the template key and modal class to open modal dynamically
   */
  openModelForBatch(key, modalclass) {
    this.openModal(this[`${key}Tmpl`], {
      'backdrop': 'static',
      keyboard: true,
      static: true,
      class: `${modalclass}`,
    });
  }
  /**
   * This function will responsible to search all offer request
   */
  searchAllRequest() {
    this._searchOfferRequestService
      .searchAllOfferRequest(this.queryGenerator.getQuery(), false, this.queryGenerator.getQueryWithFilter())
      .subscribe((result: any) => {
        result.pagination = true;
        this._searchOfferRequestService.getOfferDetails(result);
      });
  }
    /**
   * This function will responsible to search all offers
   */
  validateBatchForTemplate(action, payloadval){
    const { errorMessage } = action;
    this.bulkUpdateService.templatePreBatch(payloadval).subscribe((response: any) => {
      const { offersForUpdate: { invalid } = {invalid: null}, requestId=null } = response;
        if (!invalid?.length) {
          //this.resetSelection();
          this.handleSuccessPreCheck(requestId, action);
        } else {
          this._toaster.error(errorMessage, "", {});
        }
      })
  }
  searchAllOffer() {
    const queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    this._searchOfferService.searchAllOffers(this.queryGenerator.getQuery(), false, queryWithOrFilters).subscribe((result: any) => {
        result.pagination = true;
        this._searchOfferRequestService.getOfferDetails(result);
      });
  }
  /**
   * 
   * @param action action object with properties from rule
   * @param payloadval payload query
   * This function will be responsible to do precheck call to get whether selection is valid for batch actions
   */
  doPreCheckBatch(action, payloadval) {
    const { asyncActionKey,apiFuncName =null,isResetSelection = true } = action,
    funcname = apiFuncName || 'preCheckBatch';
    this.bulkUpdateService[funcname](payloadval, asyncActionKey).subscribe((response: any) => {
        const { offersForUpdate: { invalid = null } = {invalid : null}, requestId } = response;
        if (!invalid?.length) {
          isResetSelection && this.resetSelection();
          this.handleSuccessPreCheck(requestId, action);
        } else {
          this.handleFailesPreCheck(response, action)
        }
      })
  }
  /**
   * 
   * @param preCheckObj 
   * @param modalClass 
   * 
   * This function will open modal with related message when pre check success or fail.
   */
  displaySuccessErrorMsgPopup(preCheckObj, modalClass) {
    this.preCheckResultObj = preCheckObj;
    this.openModelForBatch("commonMsg", modalClass);
  }
  
  /**
   * 
   * @param payload payload query
   * @param action action object from rule
   * @param page on which page doing batch action
   * @param pcSelected 
   * This function is responsible to do bulk async call based on page - TRY TO CREATE FUNCTIONS WITH PATTERN BELOW
   */
  doBulkActionCallBasedOnPage(payload, action, page, pcSelected) {
    const { asyncActionKey, apiFuncName = null, postBatchSuccess = null, jobType = null, universalJobApiFunc = null } = action;
    const isUniversalJobEnabledForAction = this.checkIfUseUniversalJobForAction();
    let funcName = isUniversalJobEnabledForAction && universalJobApiFunc ? universalJobApiFunc : (apiFuncName || `bulkAction${page}`);
    this.bulkUpdateService[funcName](payload, asyncActionKey, pcSelected, jobType).subscribe((response: any) => {
      const searchFunc =  postBatchSuccess || `searchAll${page}` ;
      this.updateRecords(searchFunc);
    })
  }
  checkIfUseUniversalJobForAction() {
    const {  checkUniversalJobFeatureFlag, universalJobFlagValue } = this.action;
    if(checkUniversalJobFeatureFlag && universalJobFlagValue) {
      return this.bulkUpdateService.checkIfActionEnabledForUniversalJob(universalJobFlagValue)
    }
    return false;
  }
  updateRecords(funcName) {
    const {postBatchSuccess = null, toastrMessage} = this.action;
    this.showToasterMessageAndResetSelection(toastrMessage);
    funcName = (!funcName && postBatchSuccess) ? postBatchSuccess : funcName;
    if(funcName) {
      this?.[funcName]?.();
    }
  }
  getAllBpdRequest() {
    this.baseInputSearchService.postDataForInputSearch();
  }

  doBulkActionForExpandPeriod(query, action, page, pcSelected){
    const { asyncActionKey, toastrMessage,postBatchSuccess = null,jobType } = action;

    let payLoad = {
      jobType: jobType,
      jobSubType: asyncActionKey,
      programCodeType: CONSTANTS.BPD,
      searchQuery: query
    }
    this.bulkUpdateService.registerBatchExpand(payLoad).subscribe((response: any) => {
      this.showToasterMessageAndResetSelection(toastrMessage);
      const searchFunc =  postBatchSuccess || `searchAll${page}` ;
      this[searchFunc]();
    })
    
  }
  getAllTemplates() {
    this.baseInputSearchService.postDataForInputSearch();
  }
  /**
   * 
   * @param requestId On precheck success there is request id 
   * @param action action obj from rule
   * On precheck success either modal will open or will show common message (success/error) on popup
   */
  handleSuccessPreCheck(requestId, action) {
    const { modalClass, errSuccessModalClass, onPrecheckSuccess, confirmationMsg, key } = action;
    this.batchReqID = requestId;
    switch (key) {
      case 'addEvent':
      case 'batchTesting':
      case 'inAd':
      case 'inEmail':
      case 'createOfferRequest':
      case  'expandPeriod':
      case "terminalUpdate": {
        this.openModelForBatch(key, modalClass);
        break;
      }
      default: {
        const obj = { ...onPrecheckSuccess, message: confirmationMsg }
        this.displaySuccessErrorMsgPopup(obj, errSuccessModalClass);
      }
    }
  }
  /**
   * 
   * @param response response from precheck api
   * @param action action object from rule
   * This function will be responsible for handle scenarios when precheck request fails
   */
  handleFailesPreCheck(response, action) {
    const { errSuccessModalClass, errorMessage, onPrecheckError } = action;
    const { offersForUpdate: { invalid = [], valid = [] } } = response;
    const preCheckResultObj = {
      ...onPrecheckError,
      message: errorMessage,
      totalOffers: invalid.length + valid.length,
      validOffersList: valid,
      invalidOffersList: invalid
    }
    this.displaySuccessErrorMsgPopup(preCheckResultObj, errSuccessModalClass)
  }
  get doSkipPrecheckIfUJ() {
    const isUniversalJobEnabledForAction = this.checkIfUseUniversalJobForAction(),
    {doDirectActionOnUJMigrate = false } = this.action;
    return isUniversalJobEnabledForAction && doDirectActionOnUJMigrate;
  }
/**
   * common functionalty for Request/offer/template for clickaction
   */
  onClickBaseAction(action,payloadQuery,actionType,pcSelected){
    if (action) {
      if(this.checkIfUseUniversalJobForAction())
      {
        action.isFirstPreCheck = false;
      }      
      const { isFirstPreCheck, doDirectAsyncCall, key, modalClass="modal-xl" } = action;
      if(key === "exportOffers" || key === "exportOffersToEdit"){
        let searchQuery = payloadQuery;
        let newPayload = {
          jobType : action.jobType,
          jobSubType : action.asyncActionKey,
          programCodeType : "NA",
          searchQuery
        }
        this.doBulkActionCallBasedOnPage(newPayload, action, actionType, pcSelected);
      }
      else if (doDirectAsyncCall || this.doSkipPrecheckIfUJ) {
        this.doBulkActionCallBasedOnPage(payloadQuery, action, actionType, pcSelected);
      } else if (isFirstPreCheck) {
        this.doPreCheckBatch(action, payloadQuery);
      } else if (key === "createOfferRequest") {
        this.validateBatchForTemplate(action, payloadQuery);
      } else if (key === "copy" && pcSelected === CONSTANTS.SC){
        this.handleCopySC(key,modalClass) 
      } else {
        if(key === "cancelAll" || key === "cancelUnclipped" || key === "cancelIR"){
          this.handleSuccessPreCheck(key, action);
        }
        else {
          this.openModelForBatch(key, modalClass);
        }
      }
    }
  }

  handleCopySC(key,modalClass){

    if(['selectAcrossAllPages', 'selectAllOnPage'].includes(this.isAllBatchSelected)){
      let deliveryChannelSelected = this.queryGenerator.getQueryFilter('deliveryChannel')?.split(' OR ');
      let adTypeSelected = this.queryGenerator.getQueryFilter('adType')?.split(' OR ');
      if((!deliveryChannelSelected && !adTypeSelected) || adTypeSelected?.length>1 || deliveryChannelSelected?.length>1){
        this._toaster.error("You can only perform batch copy on one Channel at a time. Select a single Channel and then try again");
      }else{
        this.bulkUpdateService.showDisplayEndDate = adTypeSelected?.[0] == 'NIA'
        this.openModelForBatch(key, modalClass);
      }
    }else{
    if(Array.from(new Set(this.bulkUpdateService.deliveryChannelArr))?.length>1){
      this._toaster.error("You can only perform batch copy on one Channel at a time. Select a single Channel and then try again")
    }else{
      this.bulkUpdateService.showDisplayEndDate = Array.from(new Set(this.bulkUpdateService?.deliveryChannelArr))?.[0]=='DO - NIA';
      this.openModelForBatch(key, modalClass);
      }
    }
  }
}

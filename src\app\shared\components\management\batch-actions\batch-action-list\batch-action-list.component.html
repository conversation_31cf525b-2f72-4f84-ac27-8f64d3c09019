<ng-container *ngFor="let action of batchActions">
    <ng-container [ngTemplateOutlet]="(!action.isHavingChilds) ? renderParent : renderChilds"
        [ngTemplateOutletContext]="{ action: action}"></ng-container>
</ng-container>

<ng-template #renderParent let-action="action">
    <span *ngIf="checkFeatureFlag(action)">
        <a class="dropdown-item assign-user-bulk" [id]="action.key" (click)="onClickAction(action);" [class.isDisabled]="disableAction(action)"
            *permissionsOnly="action.permissionAllowed; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'"><label
                class="m-0 text-label">{{action.displayName}}</label></a>
    </span>
</ng-template>

<ng-template #renderChilds let-action="action">
    <span *ngIf="checkFeatureFlag(action)">
        <a class="dropdown-item update-offer-dates-bulk" [class.isDisabled]="disableAction(action)"  [id]="action.key"
            *permissionsOnly="action.permissionAllowed; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'">
            <label class="m-0 text-label cancelAction collapsed" data-toggle="collapse" data-target="#cancelOffers"
                aria-expanded="false" aria-controls="cancelOffers">{{action.displayName}}</label>
        </a>
        <div id="cancelOffers" class="collapse" *ngFor="let childAction of action.childActions">
            <span class="dropdown-item pl-6" (click)="onClickAction(childAction)">{{childAction.displayName}}</span>
        </div>
    </span>
</ng-template>
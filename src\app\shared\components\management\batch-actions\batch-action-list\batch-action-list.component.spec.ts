import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef } from '@angular/core';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { BatchActionsListComponent } from './batch-action-list.component';
import { of, BehaviorSubject } from 'rxjs';

describe('BatchActionsListComponent', () => {
  let component: BatchActionsListComponent;
  let fixture: ComponentFixture<BatchActionsListComponent>;
  let featureFlagsServiceStub: Partial<FeatureFlagsService>;
  let bulkUpdateServiceStub: Partial<BulkUpdateService>;
  let queryGeneratorStub: Partial<QueryGenerator>;
  let cdrStub: Partial<ChangeDetectorRef>;

  beforeEach(async () => {
    featureFlagsServiceStub = {
      isFeatureFlagEnabled: jasmine.createSpy('isFeatureFlagEnabled').and.returnValue(true),
      isUJActionEnabled: jasmine.createSpy('isUJActionEnabled').and.returnValue(true)
    };

    bulkUpdateServiceStub = {
      offerIdsListSelected$: new BehaviorSubject<any[]>([]),
      bulkSelectionForOffers: new BehaviorSubject<string>(''),
      offersIdArr: [],
      requestIdArr: [],
      isSelectAcrossAllPages: false,
      isAllBatchSelected: new BehaviorSubject<string>('')
    };

    queryGeneratorStub = {
      getQueryFilter: jasmine.createSpy('getQueryFilter').and.returnValue(''),
      getInputValue: jasmine.createSpy('getInputValue').and.returnValue('programCode1 OR programCode2')
    };

    cdrStub = {
      detectChanges: jasmine.createSpy('detectChanges')
    };

    await TestBed.configureTestingModule({
      declarations: [BatchActionsListComponent],
      providers: [
        { provide: FeatureFlagsService, useValue: featureFlagsServiceStub },
        { provide: BulkUpdateService, useValue: bulkUpdateServiceStub },
        { provide: QueryGenerator, useValue: queryGeneratorStub },
        { provide: ChangeDetectorRef, useValue: cdrStub }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BatchActionsListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component', () => {
    component.ngOnInit();
    expect(bulkUpdateServiceStub.offerIdsListSelected$).toBeTruthy();
    expect(bulkUpdateServiceStub.bulkSelectionForOffers).toBeTruthy();
  });

  it('should emit action on onClickAction', () => {
    spyOn(component.onClickBatchAction, 'emit');
    const action = { key: 'testAction' };
    component.onClickAction(action);
    expect(component.onClickBatchAction.emit).toHaveBeenCalledWith(action);
  });

  it('should check feature flag', () => {
    const action = { featureFlag: 'testFlag', checkUniversalJobFeatureFlag: true, universalJobFlagValue: 'testValue' };
    const result = component.checkFeatureFlag(action);
    expect(result).toBeTrue();
    expect(featureFlagsServiceStub.isFeatureFlagEnabled).toHaveBeenCalledWith('testFlag');
    expect(featureFlagsServiceStub.isUJActionEnabled).toHaveBeenCalledWith('testValue');
  });

  it('should check universal job feature flag only', () => {
    const action = { checkUniversalJobFeatureFlag: true, universalJobFlagValue: 'testValue' };
    const result = component.checkFeatureFlag(action);
    expect(result).toBeTrue();
    expect(featureFlagsServiceStub.isUJActionEnabled).toHaveBeenCalledWith('testValue');
  });

  it('should check feature flag only', () => {
    const action = { featureFlag: 'testFlag' };
    const result = component.checkFeatureFlag(action);
    expect(result).toBeTrue();
    expect(featureFlagsServiceStub.isFeatureFlagEnabled).toHaveBeenCalledWith('testFlag');
  });

  it('should return true when no feature flag or universal job feature flag is provided', () => {
    const action = {};
    const result = component.checkFeatureFlag(action);
    expect(result).toBeTrue();
  });

  it('should disable action for exportOffers when selectedBatchFilter is selectAcrossAllPages', () => {
    const action = { key: 'exportOffers' };
    component.selectedBatchFilter = 'selectAcrossAllPages';
    const result = component.disableAction(action);
    expect(result).toBeFalse();
  });

  it('should disable action for exportOffers when offersIdArr is empty', () => {
    const action = { key: 'exportOffers' };
    component.selectedBatchFilter = '';
    bulkUpdateServiceStub.offersIdArr = [];
    const result = component.disableAction(action);
    expect(result).toBeTrue();
  });

  it('should disable action for cancelIR when deliveryChannelSelected is IR and atleastOneOfferSelected is false', () => {
    const action = { key: 'cancelIR' };
    (queryGeneratorStub.getQueryFilter as jasmine.Spy).and.returnValue('IR');
    bulkUpdateServiceStub.offersIdArr = [];
    bulkUpdateServiceStub.isSelectAcrossAllPages = false;
    const result = component.disableAction(action);
    expect(result).toBeTrue();
  });

  it('should not disable action for cancelIR when deliveryChannelSelected is IR and atleastOneOfferSelected is true', () => {
    const action = { key: 'cancelIR' };
    (queryGeneratorStub.getQueryFilter as jasmine.Spy).and.returnValue('IR');
    bulkUpdateServiceStub.offersIdArr = [1];
    bulkUpdateServiceStub.isSelectAcrossAllPages = false;
    const result = component.disableAction(action);
    expect(result).toBeTruthy();
  });

  it('should disable action for other actions when onlyOnePCSelected is true and atleastOneOfferSelected is false', () => {
    const action = { key: 'otherAction' };
    component.pgCodeCount = 1;
    bulkUpdateServiceStub.offersIdArr = [];
    const result = component.disableAction(action);
    expect(result).toBeTrue();
  });

  it('should not disable action for other actions when isSelectAcrossAllPages is true and onlyOnePCSelected is false', () => {
    const action = { key: 'otherAction' };
    component.pgCodeCount = 2;
    bulkUpdateServiceStub.isAllBatchSelected.next('selectAcrossAllPages');
    const result = component.disableAction(action);
    expect(result).toBeFalsy();
  });

  
  it('should disable action', () => {
    const action = { key: 'exportOffers' };
    component.selectedBatchFilter = 'selectAcrossAllPages';
    const result = component.disableAction(action);
    expect(result).toBeFalse();
  });

  it('should check program code count', () => {
    const action = { programCodeCount: [1, 2, 3] };
    component.pgCodeCount = 2;
    const result = component.checkProgramCodeCount(action);
    expect(result).toBeTrue();
  });

  it('should unsubscribe on destroy', () => {
    spyOn(component.subs, 'unsubscribe');
    component.ngOnDestroy();
    expect(component.subs.unsubscribe).toHaveBeenCalled();
  });
});
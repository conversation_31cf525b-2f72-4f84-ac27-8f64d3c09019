import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';

@Component({
  selector: "batch-action-list",
  templateUrl: './batch-action-list.component.html',
  styleUrls: ['./batch-action-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BatchActionsListComponent extends UnsubscribeAdapter implements OnInit, OnDestroy {

  @Input() batchActions;
  @Input() isPopupDisabled;
  @Input() featureFlagCheck;
  @Input() pgCodeCount;
  @Output() onClickBatchAction = new EventEmitter<any>();
  selectedBatchFilter: string = "";
  constructor(private featureFlagService: FeatureFlagsService, private bulkUpdateService: BulkUpdateService, private cdr: ChangeDetectorRef, private queryGenerator: QueryGenerator) {
    super();
  }
  ngOnInit() {
    this.subs.sink = this.bulkUpdateService.offerIdsListSelected$.subscribe(
      (data) => {
        this.bulkUpdateService.offersIdArr = data;
        this.subs.sink = this.bulkUpdateService.bulkSelectionForOffers.subscribe((value) => {
          this.selectedBatchFilter = value;
          this.cdr.detectChanges();
        })

      }
    );
  }

  onClickAction(action) {
    this.onClickBatchAction.emit(action);
  }

  checkFeatureFlag(action) {
    if(action?.featureFlag && action?.checkUniversalJobFeatureFlag){
      if(action?.featureFlagDisplay){
        return this.featureFlagService.isFeatureFlagEnabled(action?.featureFlag);
      }
      return this.featureFlagService.isFeatureFlagEnabled(action?.featureFlag) && this.featureFlagService.isUJActionEnabled(action?.universalJobFlagValue);
    }
      
    //first check for UJ feature flags
    if (action?.checkUniversalJobFeatureFlag) {
      return this.featureFlagService.isUJActionEnabled(action?.universalJobFlagValue);
    }
    if(action?.featureFlag)
      return this.featureFlagService.isFeatureFlagEnabled(action?.featureFlag);
    return true;
  }

  disableAction(action) {
    let isOfferRequest = !(window.location.href.indexOf("offers/management") > -1);
    this.pgCodeCount = this.queryGenerator.getInputValue("offerProgramCd")?.split(' OR ')?.length;
    if (action.key === "exportOffers" || action.key === "exportOffersToEdit") {
      return this.selectedBatchFilter === 'selectAcrossAllPages' 
        ? false 
        : this.bulkUpdateService.offersIdArr.length === 0;
    }
    else if(action.key === "cancelIR"){
      
      let deliveryChannelSelected = this.queryGenerator.getQueryFilter('deliveryChannel')?.split(' OR ');
      let atleastOneOfferSelected = (this.bulkUpdateService.offersIdArr.length > 0);
      return !(!isOfferRequest && deliveryChannelSelected?.length == 1 && deliveryChannelSelected?.[0] === "IR" && (atleastOneOfferSelected || this.bulkUpdateService.isSelectAcrossAllPages )) 
    }
    else{
      let onlyOnePCSelected = isOfferRequest ? true : (this.pgCodeCount < 2);
      let atleastOneOfferSelected = isOfferRequest ? (this.bulkUpdateService.requestIdArr.length > 0)  :(this.bulkUpdateService.offersIdArr.length > 0);
      let canDisable = !(onlyOnePCSelected && atleastOneOfferSelected ); 
      let isSelectAcrossAllPages = isOfferRequest ? this.bulkUpdateService.isAllBatchSelected.value === "selectAcrossAllPages" : this.selectedBatchFilter === 'selectAcrossAllPages';

      let retVal = isSelectAcrossAllPages ? !onlyOnePCSelected : canDisable;
      return retVal;
    }  
  }

  checkProgramCodeCount(action) {
    if (action?.programCodeCount) {
      return action?.programCodeCount.includes(this.pgCodeCount);
    }
    return true;
  }

  ngOnDestroy(): void {
    this.subs?.unsubscribe();
  }
}
# BATCH RULES KEYS EXPLANATION - 

1. Create batch rules file with below properties - 

a. displayName  - Use this to display what will be displayed to user (Batch Action Name)

b. key: use this to identify batch action operating on. 
       -> create your template reference variable with key Name. Example - deployTmpl, submitTmpl, publishTmpl

c. permissionAllowed - Permissions to have to access the Batch action.

d. modalClas - If your batch action require to open model - Give the class here

e. doDirectAsyncCall - boolean value. either true/false. 
                     -> If on click batch action  we need to do direct batch async call. Make this true else false
                     -> Example - Submit, Process, Export, Deploy+Publish.

f. confirmationMsg - This key is required when you want to show any confirmation message to user on popup.
                   -> Example - On precheck success we ask for confirmation.
                   -> `Are you sure you want to Deploy the selected offer(s)? We will send you an email once the offer(s) are Deployed.`

g. isFirstPreCheck - If your batch action require first pre check call, then make this true else false.
                  -> Example - Update Terminals, Update for Testing, Publish, Deploy

h. childActions - If your batch actions have child actions, update the action in this array
                -> Example - Batch Cancel in MF

i. isHavingChilds - if your batch action have childs, make this as true.

j. asyncActionKey - This key is payload key we need to send while doing bulk process action.

k. toastrMessage - Toastr message to display after batch success

l. errSuccessModalClass - on precheck success / failure - if we want to show some message in popup- for that give class

m. onPrecheckSuccess -  on precheck success if you are showing popup - what buttons you want to show on that popup.
                     -> Ok button  -Do showOK value to true
                     -> more button - Do showMore value to true

n. onPrecheckError - on precheck failure if you are showing popup - what buttons you want to show on that popup.
                     -> Ok button  -Do showOK value to true
                     -> more button - Do showMore value to true

o. errorMessage - on precheck fail what message you want to show to user on popup will come here.
                -> Example - "You have one or more offer that has failed our status check."


# Batch details -

 Components - 

 1. Base Batch action component - This is the component having common code releated to batch actions.

 PLEASE EXTEND YOUR BATCH FILE WITH BASE BATCH ACTION COMPONENT.

 Example - Request Batch action component extending Base Batch action component 

 2. Batch Action List component - This is component responsible to display batch actions to UI based on program code

 INCLUDE THIS COMPONENT IN YOUR BATCH ACTION FILE SO THAT BATCH ACTIONS CAN BE DISPLAYED

 Example - Request Batch action component includes  Batch Action List component.

 3. Load dynamic batch component - In this component components will load dynamically based on route.


  ****************************************************************************************************

# Some Basic steps - 

  Create batch rules file with program code and mention components in rules you want to load dynamically through load dynamic batch component.

  Create your batch component extend it with base batch action component./

  In your batch component includes batch action list component so that batch actions will be displayed on UI.

  Try to give key in rules file as it to use as template reference variale to refer any view.
  Example - Given the key as podUpdate - 
  Now on click of batch action you need to open popup - so to give template reference in html file use - podUpdateTmpl

  Try to write function name with same name pattern.
  Example - on batch success - we need to search offer -
  create method like searchAllOffer, searcAllhRequest, searchAllTemplate

  If you are creating any batch related component - Include it in Load Dynamic batch module file.


# REFER REQUEST SECTION, OFFER SECTION BATCH COMPONENT, RULES FILE FOR MORE UNDERSTANDING

  



import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { BatchComponentInstanceService } from '@appServices/management/batch-component-instance-service';
import { Router, NavigationEnd } from '@angular/router';
import { ComponentFactoryResolver,ComponentFactory,  ViewContainerRef } from '@angular/core';
import { of } from 'rxjs';
import { filter } from 'rxjs/operators';
import { LoadDynamicBatchComponent } from './load-dynamic-batch-component';
import { REQUEST_BATCH_RULES } from '@appModules/request/shared/rules/OR-batch-rules';
import { TEMPLATE_BATCH_RULES } from '@appModules/templates/core/offer-template/management/shared/rules/template-batch-rules';
import { OFFER_BATCH_RULES } from '@appOffers/shared/rules/offer-batch-rules';


describe('LoadDynamicBatchComponent', () => {
    let component: LoadDynamicBatchComponent;
    let fixture: ComponentFixture<LoadDynamicBatchComponent>;
    let facetItemServiceSpy: jasmine.SpyObj<FacetItemService>;
    let routerSpy: any;
    let batchComponentInstanceServiceSpy: jasmine.SpyObj<BatchComponentInstanceService>;
    let resolverSpy: jasmine.SpyObj<ComponentFactoryResolver>;
    let fakeViewContainerRef: jasmine.SpyObj<ViewContainerRef>;

    beforeEach(async () => {
        facetItemServiceSpy = jasmine.createSpyObj('FacetItemService', [], {
            programCodeSelected: 'reqCode',
            programCodeChecked: { reqCode: true },
            templateProgramCodeSelected: 'tempCode'
        });

        routerSpy = {
            url: '/request',
            events: of(new NavigationEnd(1, '/request', '/request')).pipe(filter((e: any) => e instanceof NavigationEnd))
        };

        batchComponentInstanceServiceSpy = jasmine.createSpyObj('BatchComponentInstanceService', ['getComponent']);
        resolverSpy = jasmine.createSpyObj('ComponentFactoryResolver', ['resolveComponentFactory']);

        fakeViewContainerRef = jasmine.createSpyObj('ViewContainerRef', ['clear', 'createComponent']);

        await TestBed.configureTestingModule({
            declarations: [LoadDynamicBatchComponent],
            providers: [
                { provide: FacetItemService, useValue: facetItemServiceSpy },
                { provide: Router, useValue: routerSpy },
                { provide: BatchComponentInstanceService, useValue: batchComponentInstanceServiceSpy }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(LoadDynamicBatchComponent);
        component = fixture.componentInstance;
        component.mainContent = fakeViewContainerRef;
        fixture.detectChanges();
    });

    describe('Initialization', () => {
        it('should initialize currentActiveRoute from router.url, clear mainContent, and call loadBatchComponent on ngOnInit', () => {
            spyOn(component, 'loadBatchComponent');
            component.ngOnInit();
            expect(component.currentActiveRoute).toBe(routerSpy.url);
            expect(fakeViewContainerRef.clear).toHaveBeenCalled();
            expect(component.loadBatchComponent).toHaveBeenCalled();
        });
    });

    describe('Getters', () => {
        it('should return programCdSelectedForRequest from facetItemService', () => {
            expect(component.programCdSelectedForRequest).toBe(facetItemServiceSpy.programCodeSelected);
        });
        it('should return programCdSelectedForOffer based on programCodeChecked', () => {
            expect(component.programCdSelectedForOffer).toBe('reqCode');
        });
        it('should return templateProgramCd from facetItemService', () => {
            expect(component.templateProgramCd).toBe(facetItemServiceSpy.templateProgramCodeSelected);
        });
    });

    describe('setBatchComponentsInputs', () => {
        it('should set inputs on the dynamic component instance when batchInputsObj is provided', () => {
            const dummyComponentRef: any = { instance: {} };
            component.batchInputsObj = { foo: 'bar', baz: 42 };
            component.setBatchComponentsInputs(dummyComponentRef);
            expect(dummyComponentRef.instance.foo).toBe('bar');
            expect(dummyComponentRef.instance.baz).toBe(42);
        });
        it('should do nothing if batchInputsObj is not provided', () => {
            const dummyComponentRef = { instance: {} };
            component.batchInputsObj = null;
            expect(() => component.setBatchComponentsInputs(dummyComponentRef)).not.toThrow();
            expect(dummyComponentRef.instance).toEqual({});
        });
    });

    describe('createBatchComponent', () => {
        it('should create a dynamic batch component, set its inputs, assign currentComponent to mainContent, and return the created component', () => {
            const dummyName = 'DummyComponent';

            class DummyComponent { }

            batchComponentInstanceServiceSpy.getComponent.and.returnValue(DummyComponent);

            const fakeComponentFactory = {} as ComponentFactory<any>;
            resolverSpy.resolveComponentFactory.and.returnValue(fakeComponentFactory);

            const fakeComponentRef: any = { instance: {} };
            fakeViewContainerRef.createComponent.and.returnValue(fakeComponentRef);

            spyOn(component, 'setBatchComponentsInputs');

            const result = component.createBatchComponent(dummyName);
        });
    });

    describe('componentData getter', () => {
        // Initialization: by default, if currentComponent is not set, it should be undefined.
        it('should initialize with undefined when currentComponent is not set', () => {
            // Ensure currentComponent is undefined (or the expected default).
            component.currentComponent = undefined;
            expect(component.componentData).toBeUndefined();
        });

        // Public methods: simulate a public method (or direct assignment) that sets currentComponent.
        it('should return the value set by a public method (or assignment) on currentComponent', () => {
            const dummyValue = { instance: 'dummy' };
            component.currentComponent = dummyValue;
            expect(component.componentData).toEqual(dummyValue);
        });

        it('should return null when currentComponent is explicitly set to null', () => {
            component.currentComponent = null;
            expect(component.componentData).toBeNull();
        });

        it('should not throw an error when currentComponent is undefined', () => {
            component.currentComponent = undefined;
            expect(() => component.componentData).not.toThrow();
        });
    });
    
    describe('loadBatchComponent', () => {
        const dummyRequestRules = { reqCode: { components: ['DummyRequestComponent'] } };
        const dummyOfferRules = { reqCode: { components: ['DummyOfferComponent'] } };
        const dummyTemplateRules = { tempCode: { components: ['DummyTemplateComponent'] } };

        it('should load request components when currentActiveRoute includes "/request"', () => {
            component.currentActiveRoute = '/request/some/path';
            (REQUEST_BATCH_RULES as any).reqCode = dummyRequestRules.reqCode;
            spyOn(component, 'createBatchComponent');
    
            component.loadBatchComponent();
    
            expect(component.createBatchComponent).toHaveBeenCalledWith('DummyRequestComponent');
        });

        it('should load offer components when currentActiveRoute includes "/offer"', () => {
            component.currentActiveRoute = '/offer/some/path';
            (OFFER_BATCH_RULES as any).reqCode = dummyOfferRules.reqCode;
            spyOn(component, 'createBatchComponent');
    
            component.loadBatchComponent();
    
            expect(component.createBatchComponent).toHaveBeenCalledWith('DummyOfferComponent');
        });

        it('should load template components when currentActiveRoute includes "/template"', () => {
            component.currentActiveRoute = '/template/some/path';
            (TEMPLATE_BATCH_RULES as any).tempCode = dummyTemplateRules.tempCode;
            spyOn(component, 'createBatchComponent');
    
            component.loadBatchComponent();
    
            expect(component.createBatchComponent).toHaveBeenCalledWith('DummyTemplateComponent');
        });

        it('should not throw an error if no matching rules are found', () => {
            component.currentActiveRoute = '/unknown';
            expect(() => component.loadBatchComponent()).not.toThrow();
        });
    });

});
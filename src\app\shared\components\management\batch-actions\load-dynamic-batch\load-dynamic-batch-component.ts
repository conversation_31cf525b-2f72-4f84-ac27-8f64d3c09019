import {Component, EnvironmentInjector, Input, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
  import { NavigationEnd, Router } from '@angular/router';
  import { REQUEST_BATCH_RULES } from '@appModules/request/shared/rules/OR-batch-rules';
  import { TEMPLATE_BATCH_RULES } from '@appModules/templates/core/offer-template/management/shared/rules/template-batch-rules';
  import { OFFER_BATCH_RULES } from '@appOffers/shared/rules/offer-batch-rules';
  import { FacetItemService } from '@appServices/common/facet-item.service';
  import { BatchComponentInstanceService } from '@appServices/management/batch-component-instance-service';
  import { filter } from 'rxjs/operators';
  
  @Component({
    selector: 'load-dynamic-batch',
    templateUrl: './load-dynamic-batch-component.html',
    styleUrls: []
  })
  export class LoadDynamicBatchComponent implements OnInit {
    @Input("batchInputsObj") batchInputsObj;
    @Input("isPopupDisabled") isPopupDisabled;
    componentName: string;
    currentActiveRoute = '';
    currentComponent = null;
    pcSelected;
  
    @ViewChild('mainContent', { read: ViewContainerRef, static: true }) mainContent: ViewContainerRef;
      constructor(private facetItemService: FacetItemService, private router: Router,
      private componentInstance: BatchComponentInstanceService,
      private environmentInjector: EnvironmentInjector) {
      router?.events?.pipe(filter(event => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => {
          this.currentActiveRoute = event.url;
        });
    }  
    ngOnInit(): void {
      this.currentActiveRoute = this.router.url;
      this.mainContent?.clear();
      this.loadBatchComponent();
    }  
    get componentData() {
      return this.currentComponent;
    }  
    get programCdSelectedForRequest() {
      return this.facetItemService.programCodeSelected;
    }  
    get programCdSelectedForOffer() {
      const pcChecked = this.facetItemService.programCodeChecked;
      return pcChecked && Object.keys(pcChecked).find((code) => pcChecked[code]);
    }  
    get templateProgramCd() {
      return this.facetItemService.templateProgramCodeSelected;
    }  
    setBatchComponentsInputs(component: any) {
      if (!this.batchInputsObj) return;    
      Object.keys(this.batchInputsObj).forEach((key) => {
        if (component.instance) {
          component.instance[key] = this.batchInputsObj[key];
        } else {
          component[key] = this.batchInputsObj[key];
        }
      });
    }    
  
    loadBatchComponent() {
      let rules, components, pcSelected;
      if (this.currentActiveRoute?.includes("/request")) {
        rules = REQUEST_BATCH_RULES;
        pcSelected = this.programCdSelectedForRequest;
      } else if (this.currentActiveRoute?.includes("/offer")) {
        rules = OFFER_BATCH_RULES;
        pcSelected = this.programCdSelectedForOffer;
      } else if (this.currentActiveRoute?.includes("/template")) {
        rules = TEMPLATE_BATCH_RULES;
        pcSelected = this.templateProgramCd;
      }
      components = rules?.[pcSelected]?.components;
      components?.forEach((name) => {
        this.createBatchComponent(name);
      });
    }
    createBatchComponent(name) {
      let component = this.componentInstance.getComponent(name);
      const componentRef = this.mainContent?.createComponent(component, { environmentInjector: this.environmentInjector});
      this.setBatchComponentsInputs(componentRef?.instance);
      this.currentComponent = this.mainContent;
      return componentRef;
    }
  }  
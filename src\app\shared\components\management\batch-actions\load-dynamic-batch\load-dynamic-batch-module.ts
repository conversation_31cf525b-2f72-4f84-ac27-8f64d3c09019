import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MarkAsTouchedOnFocusDirectiveModule } from "@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module";
import { VarDirectiveModule } from "@appDirectives/var/var.module";
import { AppCommonModule } from "@appModules/common/app.common.module";
import { BulkAddEventComponent } from "@appModules/offers/shared/components/batch-actions/bulk-add-event/bulk-add-event.component";
import { BulkInAdEmailComponent } from "@appModules/offers/shared/components/batch-actions/bulk-in-ad-email/bulk-in-ad-email.component";
import { BulkPodUpdateComponent } from "@appModules/offers/shared/components/batch-actions/bulk-pod-update/bulk-pod-update.component";
import { BulkTerminalsUpdateComponent } from "@appModules/offers/shared/components/batch-actions/bulk-terminals-update/bulk-terminal-update.component";
import { BulkUpdateTestingComponent } from "@appModules/offers/shared/components/batch-actions/bulk-update-testing/bulk-update-testing.component";
import { OfferBatchActionsComponent } from "@appModules/offers/shared/components/batch-actions/offer-batch-action/offer-batch-action.component";
import { BatchOfferRequestCopy } from "@appModules/request/shared/components/batch-actions/batch-OR-copy/batch-request-copy.component";
import { BpdBatchRequestCopyComponent } from "@appModules/request/shared/components/batch-actions/bpd-batch-request-copy/bpd-batch-request-copy.component";
import { BulkExpandModalComponent } from "@appModules/request/shared/components/batch-actions/bulk-expand-period/bulk-expand-modal.component";
import { BulkOfferBuilderComponent } from "@appModules/request/shared/components/batch-actions/bulk-offer-builder/bulk-offer-builder.component";
import { RequestBatchActionComponent } from "@appModules/request/shared/components/batch-actions/request-batch-action/request-batch-action.component";
import { OfferDatePickerComponent } from "@appModules/request/shared/components/offer-date-picker/offer-date-picker.component";
import { CreateOfferRequestComponent } from "@appModules/templates/shared/components/batch-create-request/create-offer-request.component";
import { BatchUpdateStatusComponent } from "@appModules/templates/shared/components/batch-update-status/batch-update-status.component";
import { TemplateBatchActionComponent } from "@appModules/templates/shared/components/template-batch-action/template-batch-action.component";
import { OffersExportComponent } from "@appOffers/shared/components/batch-actions/offers-export/offers-export.component";
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { ApiErrorsModule } from "@appShared/components/common/api-errors/api-errors.module";
import { BaseBatchActionComponent } from "@appShared/components/management/batch-actions/base-batch-action/base-batch-action.component";
import { BatchActionsListComponent } from "@appShared/components/management/batch-actions/batch-action-list/batch-action-list.component";
import { BulkDeleteModalComponent } from "@appShared/components/modals/bulk-delete-modal/bulk-delete-modal.compinent";
import { ModalSuccessComponent } from "@appShared/components/modals/modal-success/modal-success.component";
import { NgxConfirmBoxModule } from '@appShared/ngx-confirm-box';
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { BsDatepickerModule } from "ngx-bootstrap/datepicker";
import { PopoverModule } from "ngx-bootstrap/popover";
import { TooltipModule } from "ngx-bootstrap/tooltip";
import { TypeaheadModule } from "ngx-bootstrap/typeahead";
import { LoadDynamicBatchComponent } from "./load-dynamic-batch-component";

@NgModule({
    imports: [
        NgxDatatableModule,
        TypeaheadModule,
        AppCommonModule,
        CommonModule,
        TooltipModule.forRoot(),
        BsDatepickerModule.forRoot(),
        PopoverModule.forRoot(),
        FormsModule,
        ReactiveFormsModule,
        VarDirectiveModule,
        MarkAsTouchedOnFocusDirectiveModule,
        NgxConfirmBoxModule,
        NgSelectModule,
        NgOptionHighlightModule,
        ApiErrorsModule,
        PermissionsModule
    ],
    declarations: [
        LoadDynamicBatchComponent,BulkOfferBuilderComponent, RequestBatchActionComponent,BaseBatchActionComponent,
        OfferDatePickerComponent, BulkDeleteModalComponent,BulkExpandModalComponent, BatchActionsListComponent, OfferBatchActionsComponent, 
        BulkUpdateTestingComponent, BulkPodUpdateComponent,CreateOfferRequestComponent, BulkTerminalsUpdateComponent,BulkInAdEmailComponent,
        BulkAddEventComponent,ModalSuccessComponent, BatchOfferRequestCopy, TemplateBatchActionComponent, BatchUpdateStatusComponent,
        BpdBatchRequestCopyComponent, OffersExportComponent

    ],
    exports: [
        LoadDynamicBatchComponent,BulkOfferBuilderComponent, RequestBatchActionComponent,BaseBatchActionComponent,
        OfferDatePickerComponent, BulkDeleteModalComponent, BulkExpandModalComponent, BatchActionsListComponent, OfferBatchActionsComponent, 
        BulkUpdateTestingComponent,BulkPodUpdateComponent,CreateOfferRequestComponent, BulkTerminalsUpdateComponent,BulkInAdEmailComponent, 
        BulkAddEventComponent, ModalSuccessComponent, BatchOfferRequestCopy, TemplateBatchActionComponent, BatchUpdateStatusComponent,
        BpdBatchRequestCopyComponent,OffersExportComponent

    ]
})
export class LoadDynamicBatchModule {

}
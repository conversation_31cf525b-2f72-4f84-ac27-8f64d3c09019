import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AppCommonModule } from '@appModules/common/app.common.module';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TypeaheadModule } from 'ngx-bootstrap/typeahead';
import { PodFilterComponent } from '../pod-filter/pod-filter.component';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MarkAsTouchedOnFocusDirectiveModule } from '@appDirectives/markAsTouched-onFocus/markAsTouched-onFocus.module';
import { VarDirectiveModule } from '@appDirectives/var/var.module';
import { NgOptionHighlightModule } from '@appShared/@ng-select/ng-option-highlight/ng-option-highlight.module';
import { NgSelectModule } from '@appShared/@ng-select/ng-select/ng-select.module';
import { PermissionsModule } from '@appShared/albertsons-angular-authorization';
import { ApiErrorsModule } from "@appShared/components/common/api-errors/api-errors.module";
import { LoadDynamicBatchModule } from '@appShared/components/management/batch-actions/load-dynamic-batch/load-dynamic-batch-module';
import { InputSearchComponent } from '@appShared/components/management/input-search/input-search.component';
import { MultiSelectCheckboxComponent } from '@appShared/components/management/input-search/multi-select-checkbox/multi-select-checkbox.component';
import { SortOptionComponent } from '@appShared/components/management/sort-option/sort-option.component';
import { NgxConfirmBoxModule } from '@appShared/ngx-confirm-box';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { BaseSortOptionComponent } from '../base-sort-option/base-sort-option.component';
import { PaginationComponent } from '../pagination/pagination.component';


@NgModule({
    declarations: [MultiSelectCheckboxComponent, PodFilterComponent, PaginationComponent, InputSearchComponent, SortOptionComponent, BaseSortOptionComponent ],
  imports: [
    LoadDynamicBatchModule,
    NgxDatatableModule,
    TypeaheadModule,
    AppCommonModule,
    CommonModule,
    TooltipModule.forRoot(),
    BsDatepickerModule.forRoot(),
    PopoverModule.forRoot(),
    FormsModule,
    ReactiveFormsModule,
    VarDirectiveModule,
    MarkAsTouchedOnFocusDirectiveModule,
    NgxConfirmBoxModule,
    NgSelectModule,
    NgOptionHighlightModule,
    ApiErrorsModule,
    PermissionsModule
  ],
  exports: [
    PodFilterComponent,
    InputSearchComponent,
    PaginationComponent,
    FormsModule,
    ReactiveFormsModule,
    LoadDynamicBatchModule,
    SortOptionComponent,
    MultiSelectCheckboxComponent,
    BaseSortOptionComponent
  ]
})
export class FilterHeaderModule { }

<form [formGroup]="inputFormGroup" class="input-search-wrapper">
    <div class="position-relative">
      <div class="d-flex">
        <div class="search-bar input-group row">
          <div class="d-flex col-auto p-0 ">
            <div class="d-flex mr-2 mt-2 w-auto justify-content-center align-items-center">
              <label class="mb-20"> Search:</label>
            </div>
            <select id="inputSelected" class="custom-select form-control category-name" formControlName="inputSelected" (change)="optionSelectedHandler($event)">
              <ng-container *ngFor="let option of searchOptions">
                <option [id]="option.field" *ngIf="!option.isHidden" [value]="option.field">
                  {{option.label}}
                </option>
              </ng-container>
            </select>
            
          </div>
          <ng-container *ngIf="selectedOption">
            <ng-container *ngFor=" let inputGroup of inputGroups.controls; let i = index;">
              <ng-container *ngIf="!selectedOption.elements[i].hide" [ngSwitch]="selectedOption.elements[i].type">
                <div [ngClass] = "selectedOption.elements[i].hide?'hide':''" *ngIf="inputGroups"   class = "mr-1 pr-1"
                [formGroup]="inputGroups.controls[i]" [ngClass]="selectedOption.elements[i].type == 'select' ? '' : getClass(selectedOption)">
                  <div *ngSwitchCase="'typeahead'" class="row">
                    <div class="col">
                      <ng-select
                          bindLabel="name"
                          bindValue="id"
                          [items]="typeaheadArrayList"
                          [multiple]="false"
                          [typeahead]="typeahead$"
                          [formControlName]="selectedOption.elements[i].field"
                          clearAllText="Clear"
                          [class.border-danger]="getFieldErrors(selectedOption.elements[i].field)"
                          markAsTouchedOnFocus
                          [formCtrl]="getControl(selectedOption.elements[i].field)"
                          (change)="searchClickHandler()"
                          class="mt--2"
                      >
                      </ng-select>
                    </div>
                  </div>
                  <div *ngSwitchCase="'date'">
                    <input
                    onkeydown="return false"
                    type="text"
                    class="form-control form-control-lg dt-field optional border-0"
                    id="endDate"
                    name="endDate"
                    autocomplete="off"
                    [placeholder]="selectedOption.elements[i].placeholder  || ''"
                    [formControlName]="selectedOption.elements[i].field"
                    #endDatePicker="bsDatepicker"
                    [minDate]="rangeEndDate"
                    [bsConfig]="{
                      containerClass: colorTheme,
                      dateInputFormat: 'MM/DD/YYYY',
                      showWeekNumbers: false
                    }"
                    [class.border-danger]="getFieldErrors(selectedOption.elements[i].field)"
                    bsDatepicker
                    markAsTouchedOnFocus
                    [formCtrl]="getControl(selectedOption.elements[i].field)"
                  />
                  <div class="input-group-append mr-0">
                    <div class="input-group-text input-text-icon pad-5 border-0" (click)="endDatePicker.toggle()">
                      <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
                    </div>
                  </div>
                  </div>
                  <div *ngSwitchCase="'select'" class="ml-3 mr-1 row">
                    <div>
                      <select class="custom-select form-control  custom-width category-name" 
                      [formControlName]="selectedOption.elements[i].field" 
                      [ngClass]="actionLogWidth ? 'bpdLogWidth' : ''"
                      (change)="optionTypeSelectSelected($event,selectedOption.elements[i])">
                        <option *ngFor="let item of selectedOption.elements[i].options" [value]="item.bindValue">
                          {{ item.bindLabel }}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div *ngSwitchDefault class="row">
                    <div class="col d-flex">
                      <input 
                      (paste)="onPasteSearch($event,selectedOption)"
                      (keyup)="onKeypress($event,selectedOption)"
                       [formControlName]="selectedOption.elements[i].field" 
                       type="text"
                       [class.border-danger]="getFieldErrors(selectedOption.elements[i].field)"
                        markAsTouchedOnFocus
                       [formCtrl]="getControl(selectedOption.elements[i].field)"
                        [placeholder]="selectedOption.elements[i].placeholder ||''" 
                        class="textarea  form-control search-field br-0  pr-1"
                         />
                         <div class="input-group-append bl-0"  (click)="searchClickHandler()">
                          <span class="resize-icon search-date-icon bl-0"><img alt="" src="assets/icons/Grey-Search.svg" /></span>
                        </div>
                    </div>
                  </div>
                </div>
                              
                    <ng-container [ngClass]="{'offset-3': !inputGroupsLevel}" class="text-danger bg-block" *ngIf="getFieldErrors(selectedOption.elements[i].field)">{{selectedOption.elements[i].error}}
                       </ng-container>
               
              </ng-container>
            </ng-container>
          </ng-container>
          
         
          <ng-container *ngIf="inputGroupsLevel">
            <ng-container *ngFor=" let inputGroupLevel of inputGroupsLevel.controls; let i = index;">
              <ng-container [formGroup]="inputGroupsLevel.controls[i]" [ngSwitch]="inputGroupsLevelOptions.elements[i].type" class="col">
                  <div *ngSwitchCase="'typeahead'">
                      <ng-select
                          bindLabel="name"
                          bindValue="id"
                          [items]="typeaheadArrayList"
                          [multiple]="false"
                          [typeahead]="typeahead$"
                          [formControlName]="inputGroupsLevelOptions.elements[i].field"
                          [class.border-danger]="getFieldErrors(inputGroupsLevelOptions.elements[i].field)"
                          markAsTouchedOnFocus
                          [formCtrl]="getControl(inputGroupsLevelOptions.elements[i].field)"
                          clearAllText="Clear"
                          (change)="searchClickHandler()"
                          class="mt--2"
                      >
                      </ng-select>
                  </div>
                  <ng-container *ngSwitchCase="'date'">
                    <div  class="col-2" *ngIf="!inputGroupsLevelOptions.elements[i].hide">
                      <div  class="input-group" [class.border-danger]="getFieldErrors(inputGroupsLevelOptions.elements[i].field)">

                        <input
                        onkeydown="return false"
                        type="text"
                        style="padding-left: 12px; border-right: 1px solid white !important;"
                        class="form-control form-control-lg  optional"
                        [id]="inputGroupsLevelOptions.elements[i].field"
                        [name]="inputGroupsLevelOptions.elements[i].field"
                        autocomplete="off"
                        [formControlName]="inputGroupsLevelOptions.elements[i].field"
                        #bsDatepicker="bsDatepicker"
                        [minDate]="getMinDateBasedOnProperty(inputGroupsLevelOptions.elements[i].field)"
                        (bsValueChange)="dateChangeHandler($event)"
                        [bsConfig]="{ containerClass: colorTheme, dateInputFormat: 'MM/DD/YYYY',showWeekNumbers: false }"
                        bsDatepicker
                        
                        markAsTouchedOnFocus
                        [formCtrl]="getControl(inputGroupsLevelOptions.elements[i].field)"
                        [placeholder]="inputGroupsLevelOptions.elements[i].placeholder  || ''"
                      />
                      
                      <div  [ngClass] = "inputGroupsLevelOptions.elements[i].hide?'hide':''" 
                      class="cursor-pointer input-group-append input-icon pt-4em px-2 bg-white">
                        <div  (click)="bsDatepicker.toggle()">
                          <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
                        </div>
                      </div>
                      </div>
                      <ng-container>
                        <small class="text-danger" *ngIf="getFieldErrors(inputGroupsLevelOptions.elements[i].field)">
                          {{inputGroupsLevelOptions.elements[i].error}} </small>
                        </ng-container>
                    </div>
                    
                  </ng-container>
                  <div *ngSwitchCase="'select'" class="mr-1">
                      <select class="custom-select form-control col custom-width category-name"
                          [class.border-danger]="getFieldErrors(inputGroupsLevelOptions.elements[i].field)"
                          markAsTouchedOnFocus
                          [formCtrl]="getControl(inputGroupsLevelOptions.elements[i].field)"
                           [formControlName]="inputGroupsLevelOptions.elements[i].field" 
                           (change)="inputGroupsLevelSelected(selectedOption.elements[i])">
                          <option *ngFor="let item of inputGroupsLevelOptions.elements[i].options" 
                          [value]="item.bindValue" [disabled]="item.disabled">
                            {{ item.bindLabel }}
                          </option>
                        </select>
                        <ng-container>
                          <small class="text-danger d-block" *ngIf="getFieldErrors(inputGroupsLevelOptions.elements[i].field)">
                            {{inputGroupsLevelOptions.elements[i].error}} </small>
                          </ng-container>
                  </div>
                  <div *ngSwitchDefault>
                      <input 
                      [class.border-danger]="getFieldErrors(inputGroupsLevelOptions.elements[i].field)"
                      markAsTouchedOnFocus
                      [formCtrl]="getControl(inputGroupsLevelOptions.elements[i].field)"
                       [formControlName]="inputGroupsLevelOptions.elements[i].field"
                        type="text"
                         [placeholder]="inputGroupsLevelOptions.elements[i].placeholder || ''" 
                         class="textarea  form-control search-field col-8 ml-1 pr-1"
                         />
                  </div>
                </ng-container>
                
            </ng-container>
            
          </ng-container>
    
          <ng-container *ngIf="selectedOption?.filterByUser">
            <div class="d-flex col-auto p-0" >
              <label class="mb-20">
                <input type="checkbox" id="input-filter-check" class="list-item-input" (click)="onCheckChanged($event.target.checked);searchClickHandler();"
                     /> User Only
              </label>
            </div>
          </ng-container>

          <button id="input-search-btn" *ngIf="selectedOption?.searchButton" class="btn btn-primary" (click)="searchClickHandler()">Search</button>         
        
        </div>
        
      </div>
      <app-base-saved-search *ngIf="!isHideSavedSearch" [currentSearchType]="currentSearchType" [inputFormGroup]="inputFormGroup"></app-base-saved-search>
       <div *ngIf="showSearchError">
        {{showSearchError}}
      </div>
    </div>
  </form>
  
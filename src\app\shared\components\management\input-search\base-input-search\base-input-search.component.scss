.custom-width{
    width: 160px;
}
.date-wrapper {
    border: 1px solid #DEDEDE !important;
}
.custom-dropdown-width {
  max-width: 24.3% !important;
}
.category-name{
  min-width: 165px

}
.bpdLogWidth {
  width: 200px;
}
.ml-10{
    margin-left: 10px;
}
.selct-width{
    width: 100%;
}
.mb-20{
  margin-bottom: 20px
}
.bl-0 {
    border-left: none !important ;
}
.br-0{
border-right: none !important;
}
.line {
    box-sizing: border-box;
    border: 1px solid #D9DDE0;
  }
  .search-date-icon {
    border: 1px solid #DEDEDE;
    padding: 7px 10px 10px 10px;
    cursor: pointer;
}
.padding-top-5 {
    padding-top: 5px;
  }
  
    .cursor-pointer {
      cursor: pointer;
    }
    .saved-searches {
      font-size: 12px;
      padding-top: 9px;
      font-weight: bold;
      color: #00529f !important;
      text-decoration: underline;
    }
    .dropdown-toggle:not(.nav-link) {
      border: none !important;
    }
    button {
      border: none;
      // height: 37px;
    }
   
    .request-suggestion {
      padding-top: 3px;
      width: 100%;
      button {
        font-size: 14px;
        height: 30px;
        padding: 0px 14px;
      }
    }
    .search-results {
      ul li {
        border: none;
        // &:nth-child(even) {
        //     background: #F0F4F7;
        // }
        &:hover {
          background: #f0f4f7;
        }
      }
    }
    .custom-list-style {
      padding: 5px 25px;
      font-size: 14px;
    }
    .search-save {
      width: 457px;
      padding: 0px;
      border-radius: 0
    }
    .custom-btn-saved:hover {
      background-color: #00529f;
      color: #fff;
    }
    .saved-search-rectangle {
      border-radius: 2px 2px 0 0;
      background-color: #FFFFFF;
      width: 100%;
      text-align: left;
      &:hover {
        background: #F0F0F0;
      }
    }
    .line {
      box-sizing: border-box;
      border: 1px solid #D9DDE0;
    }
    .border-danger.input-group {
      border: 1px solid #cf202f !important;
      input,
      .input-group-append {
        border: 0 !important;
      }
    }

  .search-bar.input-group.row label.mb-20 {
      color: #000;
  }
  .search-bar.input-group.row {
      color: #cf202f;
      font-size: 13px;
      float: right;
      width: 100%;
      flex-wrap: nowrap;
  }
import { ComponentFixture, TestBed, fakeAsync, tick, flush } from '@angular/core/testing';
import { BaseInputSearchComponent } from './base-input-search.component';
import { UntypedFormBuilder, UntypedFormGroup, UntypedFormControl, UntypedFormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { of, Subject, BehaviorSubject } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA, Injector } from '@angular/core';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CommonService } from '@appServices/common/common.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { LoaderService } from '@appServices/common/loader.service';
import { BaseSavedSearchService } from '@appServices/management/base-saved-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CONSTANTS } from '@appConstants/constants';

import * as resetFormControlErrorUtil from '@appUtilities/resetFormControlError';
import * as updateTreeValidityUtil from '@appUtilities/updateValueValidatityForTree';

// Mock services
class MockBaseInputSearchService {
  inputOptions = [
    {
      field: 'mockFieldWithSubLevels',
      label: 'Mock Field With Sub Levels',
      defaultSelect: true,
      elements: [
        {
          field: 'mainSelect',
          type: 'select',
          options: [
            { label: 'Default Sub Option', bindValue: 'defaultSubVal', defaultSelect: true, elements: [{ field: 'subLevelElement', value: 'subDefault' }] }
          ]
        }
      ]
    },
    { field: 'periodWeekField', label: 'Period Week Field', defaultSelect: false, elements: [{ field: 'periodElement', value: '' }], dropDownOptionsFromApi: { methodName: 'getPeriodOptions', optionLevel: 'selectedOption' } },
    { field: 'featureFlaggedField', label: 'Feature Flagged Field', defaultSelect: false, featureFlag: 'testFeatureFlag', elements: [{ field: 'featureElement', value: '' }] },
    { field: 'linkedField', label: 'Linked Field', isLinkedObj: true, query: [], linkedWith: 'sourceField', elements: [] },
    { field: 'sourceField', label: 'Source Field', elements: [], linkedTo: 'linkedField', linkValue: 'linkedValue123' },
    { field: 'productGroupType', label: 'Product Group Type', defaultSelect: false, elements: [{ field: 'pgType', value: 'BASE' }] }
  ];  inputFormGrpValue: any;
  setActiveCurrentSearchType = jasmine.createSpy('setActiveCurrentSearchType');
  getInputFieldSelected = jasmine.createSpy('getInputFieldSelected').and.returnValue({
    field: 'testField',
    label: 'Test Field',
    showChip: 'Test Chip Value'
  });
  setChipForField = jasmine.createSpy('setChipForField');
  generateQueryForOptions = jasmine.createSpy('generateQueryForOptions').and.returnValue(false);
  getActiveCurrentSearchType = jasmine.createSpy('getActiveCurrentSearchType').and.returnValue('testSearchType');
  getDataForInputSearch = jasmine.createSpy('getDataForInputSearch').and.returnValue(of({ data: 'searchData' }));
  setFormQuery = jasmine.createSpy('setFormQuery');
  formQuery = jasmine.createSpy('formQuery').and.returnValue('query');
  getInputSearchOption = jasmine.createSpy('getInputSearchOption').and.returnValue({});
  getDefaultOption = jasmine.createSpy('getDefaultOption').and.returnValue({});
  getSortOption = jasmine.createSpy('getSortOption').and.returnValue({});
  getLastPeriodOptions = jasmine.createSpy('getLastPeriodOptions').and.returnValue(of([{ periodWeek: '202501', periodId: 1 }, { periodWeek: '202502', periodId: 2 }]));
  postDataForInputSearch = jasmine.createSpy('postDataForInputSearch');
  setQueryWithOrFilter = jasmine.createSpy('setQueryWithOrFilter'); 
  queryWithOrFilter = 'queryWithOrFilter';
  inputSearchChip = jasmine.createSpy('inputSearchChip').and.returnValue({ field: 'chipValue' });
  currentRouter = 'mockRouter';
  mockRouter = new BehaviorSubject<any>(null);
}

class MockSearchUsersService {
  getUsers = jasmine.createSpy('getUsers').and.returnValue(of([{ firstName: 'John', lastName: 'Doe', userId: 'john.doe' }]));
}

class MockCommonSearchService {
  isFiltered = false;
  isStoreIdUserSearchEnabled = false;
  fetchDefaultOptions = jasmine.createSpy('fetchDefaultOptions');
  getFilterOption = jasmine.createSpy('getFilterOption').and.returnValue({});
  getInputSearchChip = jasmine.createSpy('getInputSearchChip').and.returnValue({});
  setInputSearchChip = jasmine.createSpy('setInputSearchChip');
  inputSearchChip = { sourceField: 'someValue' }; // For linked options test
  getInputSearchOption = jasmine.createSpy('getInputSearchOption').and.returnValue({});
  getDefaultOption = jasmine.createSpy('getDefaultOption').and.returnValue({});
  getSortOption = jasmine.createSpy('getSortOption').and.returnValue({});
}

class MockCommonService {
  getPeriodWeeks = jasmine.createSpy('getPeriodWeeks').and.returnValue(of(['202501', '202502']));
}

class MockCommonRouteService {
  currentActivatedRoute = 'mockRoute';
  currentRoute = 'mockRoute';
  debugFlagEnabledRoutes = [];
  router = { navigate: jasmine.createSpy('navigate') } as any;
  acivatedRoute = {};
  currentRouter = 'mockRoute';
  routerPage = '';
  isDebugTrueExists = false;
  isBpdReqPage = false;
  getCurrentRoute = jasmine.createSpy('getCurrentRoute').and.returnValue('mockRoute');
  getCurrentActivatedRoute = jasmine.createSpy('getCurrentActivatedRoute').and.returnValue('mockActivatedRoute');
  getCurrentRouteName = jasmine.createSpy('getCurrentRouteName');
  getCurrentRoutePath = jasmine.createSpy('getCurrentRoutePath');
  getCurrentRouteParams = jasmine.createSpy('getCurrentRouteParams');
}

class MockLoaderService {}
class MockBaseSavedSearchService {}
class MockFacetItemService {}
class MockFeatureFlagsService {
  isFeatureFlagEnabled = jasmine.createSpy('isFeatureFlagEnabled').and.returnValue(true);
}

describe('BaseInputSearchComponent', () => {
  let component: BaseInputSearchComponent;
  let fixture: ComponentFixture<BaseInputSearchComponent>;
  let mockAppInjector: jasmine.SpyObj<Injector>;
  let mockBaseInputSearchService: MockBaseInputSearchService;
  let mockSearchUsersService: MockSearchUsersService;
  let mockCommonSearchService: MockCommonSearchService;
  let mockCommonService: MockCommonService;
  let mockCommonRouteService: MockCommonRouteService;
  let mockLoaderService: MockLoaderService;
  let mockBaseSavedSearchService: MockBaseSavedSearchService;
  let mockFacetItemService: MockFacetItemService;
  let mockFeatureFlagsService: MockFeatureFlagsService;
  let fb: UntypedFormBuilder;

  beforeEach(async () => {
    // Create fresh mock instances before each test
    mockBaseInputSearchService = new MockBaseInputSearchService();
    mockSearchUsersService = new MockSearchUsersService();
    mockCommonSearchService = new MockCommonSearchService();
    mockCommonService = new MockCommonService();
    mockCommonRouteService = new MockCommonRouteService();
    mockLoaderService = new MockLoaderService();
    mockBaseSavedSearchService = new MockBaseSavedSearchService();
    mockFacetItemService = new MockFacetItemService();
    mockFeatureFlagsService = new MockFeatureFlagsService();

    // Mock AppInjector
    mockAppInjector = jasmine.createSpyObj('Injector', ['get']);
    spyOn(AppInjector, 'getInjector').and.returnValue(mockAppInjector);

    mockAppInjector.get.and.callFake((token: any) => {
      if (token === UntypedFormBuilder) {
        return new UntypedFormBuilder();
      }
      if (token === BaseInputSearchService) return mockBaseInputSearchService;
      if (token === SearchUsersService) return mockSearchUsersService;
      if (token === CommonSearchService) return mockCommonSearchService;
      if (token === CommonService) return mockCommonService;
      if (token === CommonRouteService) return mockCommonRouteService;
      if (token === LoaderService) return mockLoaderService;
      if (token === BaseSavedSearchService) return mockBaseSavedSearchService;
      if (token === FacetItemService) return mockFacetItemService;
      if (token === FeatureFlagsService) return mockFeatureFlagsService;
      return undefined;
    });

    await TestBed.configureTestingModule({
      declarations: [BaseInputSearchComponent],
      imports: [ReactiveFormsModule],
      providers: [
        UntypedFormBuilder,
        { provide: BaseInputSearchService, useValue: mockBaseInputSearchService },
        { provide: SearchUsersService, useValue: mockSearchUsersService },
        { provide: CommonSearchService, useValue: mockCommonSearchService },
        { provide: CommonService, useValue: mockCommonService },
        { provide: CommonRouteService, useValue: mockCommonRouteService },
        { provide: LoaderService, useValue: mockLoaderService },
        { provide: BaseSavedSearchService, useValue: mockBaseSavedSearchService },
        { provide: FacetItemService, useValue: mockFacetItemService },
        { provide: FeatureFlagsService, useValue: mockFeatureFlagsService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(BaseInputSearchComponent);
    component = fixture.componentInstance;
    fb = TestBed.inject(UntypedFormBuilder);

    // Set @Input properties
    component.currentSearchType = 'testSearchType';
    component.isHideSavedSearch = false;

    // Initialize component properties
    component.isCommaNotAllowed = ['upc', 'hhid', 'cic'];
    component.showSearchError = '';
    component.searchError = {
      upc: 'You can only search on one UPC or PLU at a time. Please clear UPC/PLU filter',
      hhid: 'You can only search on one HHID at a time. Please clear HHID filter'
    };
    component.inputSearchOptions = mockBaseInputSearchService.inputOptions;

    // Initialize form for all tests to avoid null errors
    component.inputFormGroup = fb.group({
      inputSelected: [mockBaseInputSearchService.inputOptions[0].field],
      savedSearchName: [''],
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
    expect(component.currentSearchType).toBe('testSearchType');
    expect(component.isHideSavedSearch).toBe(false);
  });

  it('should initialize with the correct search type', () => {
    spyOn(component, 'getInitialOptionsList').and.callThrough();

    component.ngOnInit();

    expect(component.getInitialOptionsList).toHaveBeenCalled();
    expect(mockBaseInputSearchService.setActiveCurrentSearchType).toHaveBeenCalledWith('testSearchType');
  });

  it('should include feature flagged options when flag is enabled', () => {
    mockFeatureFlagsService.isFeatureFlagEnabled.and.returnValue(true);

    component.ngOnInit();

    const featureFlaggedOption = component.searchOptions?.find(opt => opt.field === 'featureFlaggedField');
    expect(featureFlaggedOption).toBeDefined();
  });

  it('should exclude feature flagged options when flag is disabled', () => {
    mockFeatureFlagsService.isFeatureFlagEnabled.and.returnValue(false);

    component.ngOnInit();

    const featureFlaggedOption = component.searchOptions?.find(opt => opt.field === 'featureFlaggedField');
    expect(featureFlaggedOption).toBeUndefined();
  });

  it('should exclude multiple feature flagged options when flags are disabled', () => {
    const testOptions = [
      { field: 'regular', label: 'Regular Field', defaultSelect: false, elements: [] },
      { field: 'feature1', label: 'Feature 1', defaultSelect: false, featureFlag: 'flag1', elements: [] },
      { field: 'feature2', label: 'Feature 2', defaultSelect: false, featureFlag: 'flag2', elements: [] }
    ];

    mockBaseInputSearchService.inputOptions = testOptions;

    mockFeatureFlagsService.isFeatureFlagEnabled.and.callFake(flag => {
      return false;
    });

    component.getInitialOptionsList();

    expect(component.searchOptions.length).toBe(1);
    expect(component.searchOptions[0].field).toBe('regular');
  });

  it('should handle getPeriodWeeks with valid term', () => {
    const result = component.getPeriodWeeks('202501');

    expect(mockCommonService.getPeriodWeeks).toHaveBeenCalledWith('202501');
    expect(result.subscribe).toBeDefined();
  });

  it('should handle getPeriodWeeks with null term', () => {
    const result = component.getPeriodWeeks(null);

    expect(mockCommonService.getPeriodWeeks).not.toHaveBeenCalled();
    expect(result).toBeDefined();
  });

  it('should handle removeWordAfterSpace correctly', () => {
    expect(component.removeWordAfterSpace('one two')).toBe('one');
    expect(component.removeWordAfterSpace('one two three')).toBe('one two');
    expect(component.removeWordAfterSpace('single')).toBe('single');
    expect(component.removeWordAfterSpace('')).toBe('');
    expect(component.removeWordAfterSpace(null)).toBe(null);
  });

  it('should handle getUsers with valid term', () => {
    const result = component.getUsers('John');

    expect(mockSearchUsersService.getUsers).toHaveBeenCalledWith('John');
    expect(result.subscribe).toBeDefined();
  });

  it('should handle getUsers with null term', () => {
    const result = component.getUsers(null);

    expect(mockSearchUsersService.getUsers).not.toHaveBeenCalled();
    expect(result).toBeDefined();
  });

  it('should return the correct typeaheadApi function for various fields', () => {
    component.optionListSelected = { field: 'createUserId' };
    expect(typeof component.getTypeAheadApis()).toBe('function');

    component.optionListSelected = { field: 'periodWeek' };
    expect(typeof component.getTypeAheadApis()).toBe('function');

    component.optionListSelected = { field: 'nonExistingField' };
    expect(component.getTypeAheadApis()).toBeUndefined();
  });

  it('should handle typeahead subscriptions correctly', fakeAsync(() => {
    spyOn(component, 'setTypeAheadList').and.stub();
    const mockApiFn = jasmine.createSpy().and.returnValue(of(['test item']));
    spyOn(component, 'getTypeAheadApis').and.returnValue(mockApiFn);

    component.initSubscribes();
    component.typeahead$.next('test');
    tick(300);

    expect(mockApiFn).toHaveBeenCalledWith('test');
    expect(component.setTypeAheadList).toHaveBeenCalledWith(['test item']);

    component.typeahead$.next('test');
    tick(300);
    expect(mockApiFn).toHaveBeenCalledTimes(1);

    component.typeahead$.next('new test');
    tick(300);
    expect(mockApiFn).toHaveBeenCalledTimes(2);

    flush();
  }));

  it('should set users list for typeahead correctly', () => {
    component.setUsersListForTypeAhead([
      { firstName: 'John', lastName: 'Doe', userId: 'jdoe' },
      { firstName: 'Jane', lastName: '', userId: 'jane' },
      { firstName: '', lastName: 'Smith', userId: 'smith' }
    ]);

    expect(component.typeaheadArrayList).toEqual([
      { name: 'John Doe', id: 'jdoe' },
      { name: 'Jane ', id: 'jane' },
      { name: ' Smith', id: 'smith' }
    ]);
  });

  it('should set period list for typeahead correctly', () => {
    component.setPeriodListForTypeahead(['202501', '202502']);

    expect(component.typeaheadArrayList).toEqual([
      { name: '202501', id: '202501' },
      { name: '202502', id: '202502' }
    ]);
  });

  it('should handle setTypeAheadList for different field types', () => {
    component.optionListSelected = { field: 'createUserId' };
    const mockUsers = [{ firstName: 'Test', lastName: 'User', userId: 'tuser' }];
    spyOn(component, 'setUsersListForTypeAhead');

    component.setTypeAheadList(mockUsers);
    expect(component.setUsersListForTypeAhead).toHaveBeenCalledWith(mockUsers);

    component.optionListSelected = { field: 'periodWeek' };
    const mockPeriods = ['202501', '202502'];
    spyOn(component, 'setPeriodListForTypeahead');

    component.setTypeAheadList(mockPeriods);
    expect(component.setPeriodListForTypeahead).toHaveBeenCalledWith(mockPeriods);
  });

  it('should get class based on column class or page type', () => {
    expect(component.getClass({ columnClass: 'test-class' })).toBe('test-class');

    mockCommonRouteService.currentRouter = CONSTANTS.TEMPLATE + '/some-path';
    expect(component.getClass({})).toBe('col-6 pr-6');

    mockCommonRouteService.currentRouter = 'other/path';
    expect(component.getClass({})).toBe('col-5');
  });

  it('should correctly identify action log pages', () => {
    mockCommonRouteService.currentRouter = CONSTANTS.ACTION_LOG + '/details';
    expect(component.actionLogWidth).toBeTrue();

    mockCommonRouteService.currentRouter = CONSTANTS.IMPORT_LOG_BPD;
    expect(component.actionLogWidth).toBeTrue();

    mockCommonRouteService.currentRouter = 'other/path';
    expect(component.actionLogWidth).toBeFalse();
  });

  it('should handle keypress events correctly', () => {
    spyOn(component, 'searchClickHandler').and.stub();

    const enterEvent = new KeyboardEvent('keypress');
    Object.defineProperty(enterEvent, 'which', { value: 13 });
    spyOn(enterEvent, 'preventDefault');

    component.onKeypress(enterEvent, {});
    expect(component.searchClickHandler).toHaveBeenCalled();
    expect(enterEvent.preventDefault).toHaveBeenCalled();

    const otherEvent = new KeyboardEvent('keypress');
    Object.defineProperty(otherEvent, 'which', { value: 65 });
    spyOn(otherEvent, 'preventDefault');

    component.onKeypress(otherEvent, {});
    expect(otherEvent.preventDefault).not.toHaveBeenCalled();
  });

  it('should handle onPasteSearch by calling onKeypress', () => {
    spyOn(component, 'onKeypress');
    const event = new Event('paste');
    const option = { field: 'test' };

    component.onPasteSearch(event, option);
    expect(component.onKeypress).toHaveBeenCalledWith(event, option);
  });

  it('should check if comma is allowed in input fields', () => {
    component.isCommaNotAllowed = ['upc', 'hhid'];
    component.showSearchError = '';

    const result1 = component.isCheckedCommaAllowed('upc', [{ upc: '123,456' }]);
    expect(result1).toBeTrue();
    expect(component.showSearchError).toBe(component.searchError.upc);

    component.showSearchError = '';
    const result2 = component.isCheckedCommaAllowed('other', [{ other: '123,456' }]);
    expect(result2).toBeUndefined();
    expect(component.showSearchError).toBe('');
  });

  it('should handle getMinDateBasedOnProperty', () => {
    const testDate = new Date(2025, 0, 1);
    component.rangeEndDate = testDate;

    expect(component.getMinDateBasedOnProperty('To')).toBe(testDate);
    expect(component.getMinDateBasedOnProperty('From')).toBeUndefined();
  });

  it('should handle dateChangeHandler correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['Range'] })]),
      inputGroupsLevel: fb.array([fb.group({ To: [new Date(2025, 0, 1)] })])
    });

    const newDate = new Date(2025, 0, 15);
    component.dateChangeHandler(newDate);

    expect(component.rangeEndDate).toEqual(newDate);
    expect(component.inputFormGroup.get('inputGroupsLevel').get('0').get('To').value).toEqual(newDate);
  });

  it('should handle dateChangeHandler with date comparison', () => {
    const futureDate = new Date(2026, 0, 1);
    const newDate = new Date(2025, 6, 1);

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['Range'] })]),
      inputGroupsLevel: fb.array([fb.group({ To: [futureDate] })])
    });

    component.dateChangeHandler(newDate);

    expect(component.rangeEndDate).toEqual(newDate);
    expect(component.inputFormGroup.get('inputGroupsLevel').get('0').get('To').value)
      .toEqual(futureDate);
  });

  it('should handle dateChangeHandler with non-range input groups', () => {
    const newDate = new Date(2025, 0, 15);

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['NotRange'] })]),
      inputGroupsLevel: fb.array([fb.group({ To: [new Date(2025, 0, 1)] })])
    });

    component.dateChangeHandler(newDate);

    expect(component.rangeEndDate).toEqual(newDate);
    expect(component.inputFormGroup.get('inputGroupsLevel').get('0').get('To').value)
      .toEqual(new Date(2025, 0, 1));
  });

  it('should handle dateChangeHandler with null input groups or level', () => {
    const newDate = new Date(2025, 0, 15);

    component.inputFormGroup = fb.group({
      inputSelected: ['testField']
    });

    expect(() => {
      component.dateChangeHandler(newDate);
    }).not.toThrow();

    expect(component.rangeEndDate).toEqual(newDate);
  });

  it('should handle getErrorsForField correctly', () => {
    const control1 = new UntypedFormControl();
    control1.setErrors({ required: true });
    control1.markAsUntouched();

    const result1 = component.getErrorsForField(control1);
    expect(result1).toBeTrue();
    expect(control1.errors).toEqual({ required: true });

    const control2 = new UntypedFormControl();
    control2.setErrors({ required: true });
    control2.markAsTouched();

    const result2 = component.getErrorsForField(control2);
    expect(result2).toBeFalse();
    expect(control2.errors).toBeNull();

    const control3 = new UntypedFormControl();

    const result3 = component.getErrorsForField(control3);
    expect(result3).toBeFalse();
  });

  it('should handle setFieldValidator to add validators', () => {
    const formArray = fb.array([
      fb.group({ test1: [''], test2: [''] }),
      fb.group({ test3: [''] })
    ]);

    component.setFieldValidator(formArray);

    expect(formArray.at(0).get('test1').validator).toBeDefined();
    expect(formArray.at(0).get('test2').validator).toBeDefined();
    expect(formArray.at(1).get('test3').validator).toBeDefined();
  });

  it('should handle onCheckChanged correctly', () => {
    component.onCheckChanged(true);
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeTrue();

    component.onCheckChanged(false);
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeFalse();
  });

  it('should handle getUniqueListBy correctly', () => {
    const testArray = [
      { id: 1, name: 'Test 1' },
      { id: 2, name: 'Test 2' },
      { id: 1, name: 'Test 3' }
    ];

    const result = component.getUniqueListBy(testArray, 'id');

    expect(result.length).toBe(2);
    const resultIds = result.map(item => (item as any).id);
    expect(resultIds).toContain(1);
    expect(resultIds).toContain(2);
  });

  it('should handle setValidators with null inputGroupsLevel', () => {
    const inputGroupsArr = fb.array([fb.group({ field: [''] })]);
    spyOn(component, 'setFieldValidator');

    component.setValidators(inputGroupsArr, null);

    expect(component.setFieldValidator).toHaveBeenCalledWith(inputGroupsArr);
    expect(component.setFieldValidator).toHaveBeenCalledTimes(1);
  });

  it('should handle setValidators with both arrays', () => {
    const inputGroupsArr = fb.array([fb.group({ field1: [''] })]);
    const inputGroupsLevelArr = fb.array([fb.group({ field2: [''] })]);
    spyOn(component, 'setFieldValidator');

    component.setValidators(inputGroupsArr, inputGroupsLevelArr);

    expect(component.setFieldValidator).toHaveBeenCalledWith(inputGroupsArr);
    expect(component.setFieldValidator).toHaveBeenCalledWith(inputGroupsLevelArr);
    expect(component.setFieldValidator).toHaveBeenCalledTimes(2);
  });

  it('should validate form correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['field'],
      inputGroups: fb.array([fb.group({ test: ['value'] })]),
      inputGroupsLevel: fb.array([])
    });

    spyOn(component, 'setValidators');

    const result = component.isFormValid();

    expect(result).toBeTrue();
    expect(component.setValidators).toHaveBeenCalled();
  });

  it('should handle searchClickHandler with invalid form', () => {
    spyOn(component, 'isFormValid').and.returnValue(false);
    const result = component.searchClickHandler();
    expect(result).toBeTrue();
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler when comma is not allowed', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(true);

    const result = component.searchClickHandler();
    expect(result).toBeTrue();
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with no input value', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);

    component.inputFormGroup = fb.group({
      inputSelected: ['mockField'],
      inputGroups: fb.array([fb.group({ mockField: [''] })])
    });

    const result = component.searchClickHandler();
    expect(result).toBeFalse();
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with valid inputs', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();

    component.inputFormGroup = fb.group({
      inputSelected: ['mockField'],
      inputGroups: fb.array([fb.group({ mockField: ['value'] })])
    });

    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];

    const result = component.searchClickHandler();

    expect(mockBaseInputSearchService.getInputFieldSelected).toHaveBeenCalled();
    expect(mockBaseInputSearchService.generateQueryForOptions).toHaveBeenCalled();
    expect(mockCommonSearchService.fetchDefaultOptions).toHaveBeenCalled();
    expect(mockCommonSearchService.setInputSearchChip).toHaveBeenCalled();
    expect(mockBaseInputSearchService.postDataForInputSearch).toHaveBeenCalled();
  });

  it('should handle window.location.reload if used', () => {
    const reloadSpy = jasmine.createSpy('reload');

    if (Object.getOwnPropertyDescriptor(window.location, 'reload')?.configurable !== false) {
      Object.defineProperty(window.location, 'reload', {
        configurable: true,
        value: reloadSpy
      });
    }

    expect(component).toBeTruthy();
  });

  it('should handle edge cases in component methods', () => {
    expect(component.removeWordAfterSpace('')).toBe('');
    expect(component.removeWordAfterSpace(null)).toBe(null);
    expect(component.removeWordAfterSpace('single')).toBe('single');
    expect(component.removeWordAfterSpace('one two')).toBe('one');
    expect(component.removeWordAfterSpace('one two three')).toBe('one two');
  });

  it('should handle getDataForInputSearch correctly', () => {
    const mockResponse = { data: 'testData' };
    mockBaseInputSearchService.getDataForInputSearch.and.returnValue(of(mockResponse));
    mockBaseInputSearchService.currentRouter = 'testRouter';
    mockBaseInputSearchService['testRouter'] = new BehaviorSubject(null);
    spyOn(mockBaseInputSearchService['testRouter'], 'next');

    component.getDataForInputSearch();

    expect(mockBaseInputSearchService.getDataForInputSearch).toHaveBeenCalled();
    expect(mockBaseInputSearchService['testRouter'].next).toHaveBeenCalledWith(mockResponse);
  });

  it('should handle getPeriodOptions correctly', () => {
    const mockResponse = [{ periodWeek: '202501', periodId: 1 }];
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of(mockResponse));
    spyOn(component, 'setOptions');

    const selectedOption = { elements: [{ options: [] }] };
    component.getPeriodOptions(selectedOption);

    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).toHaveBeenCalledWith(selectedOption, mockResponse);
  });

  it('should handle setOptions correctly', () => {
    const selectedOption = {
      elements: [{
        options: [{ defaultSelect: true, label: 'Default' }]
      }]
    };
    const response = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 }
    ];

    component.setOptions(selectedOption, response);

    expect(selectedOption.elements[0].options.length).toBe(3);
    expect(selectedOption.elements[0].options[0].defaultSelect).toBe(true);
  });

  it('should handle setOptionsFromApi correctly', () => {
    spyOn(component, 'getPeriodOptions');
    component['selectedOption'] = 'testOption';
    component.setOptionsFromApi('getPeriodOptions', 'selectedOption');
    expect(component.getPeriodOptions).toHaveBeenCalledWith('testOption');

    expect(() => component.setOptionsFromApi('nonExistentMethod', 'someOption')).not.toThrow();
    expect(() => component.setOptionsFromApi(null, 'selectedOption')).not.toThrow();
    expect(() => component.setOptionsFromApi('getPeriodOptions', null)).not.toThrow();
    expect(() => component.setOptionsFromApi(null, null)).not.toThrow();
  });

  it('should handle getTypeAheadApis for all supported field types', () => {
    const userFields = ['createUserId', 'createdBy', 'updatedBy', 'updatedByUser', 'createdByUser', 'updatedUserId'];

    userFields.forEach(field => {
      component.optionListSelected = { field };
      const api = component.getTypeAheadApis();
      expect(api).toBeDefined();
      expect(typeof api).toBe('function');
    });

    const periodFields = ['periodWeek', 'lastPeriodCreated'];

    periodFields.forEach(field => {
      component.optionListSelected = { field };
      const api = component.getTypeAheadApis();
      expect(api).toBeDefined();
      expect(typeof api).toBe('function');
    });

    component.optionListSelected = { field: 'nonExistingField' };
    expect(component.getTypeAheadApis()).toBeUndefined();
  });

  it('should handle feature-flagged options with dynamic flag values', () => {
    mockBaseInputSearchService.inputOptions = [
      { field: 'regular', label: 'Regular Field', defaultSelect: false, elements: [] },
      { field: 'feature1', label: 'Feature 1', defaultSelect: false, featureFlag: 'flag1', elements: [] },
      { field: 'feature2', label: 'Feature 2', defaultSelect: false, featureFlag: 'flag2', elements: [] }
    ];

    mockFeatureFlagsService.isFeatureFlagEnabled.and.callFake(flag => {
      return flag === 'flag1';
    });

    component.searchOptions = undefined;
    component.getInitialOptionsList();

    expect(component.searchOptions.length).toBe(2);
    expect(component.searchOptions.some(opt => opt.field === 'regular')).toBeTrue();
    expect(component.searchOptions.some(opt => opt.field === 'feature1')).toBeTrue();
    expect(component.searchOptions.some(opt => opt.field === 'feature2')).toBeFalse();
  });

  it('should handle form control operations correctly', () => {
    component.inputFormGroup = fb.group({
      inputGroups: fb.array([fb.group({ test: ['value'] })]),
      inputGroupsLevel: fb.array([fb.group({ test2: ['value2'] })])
    });

    spyOn(component, 'setFieldValidator');
    component.setValidators(
      component.inputFormGroup.get('inputGroups') as UntypedFormArray,
      component.inputFormGroup.get('inputGroupsLevel') as UntypedFormArray
    );

    expect(component.setFieldValidator).toHaveBeenCalledTimes(2);
  });

  it('should handle comprehensive component functionality', () => {
    mockBaseInputSearchService.inputOptions = [
      {
        field: 'option1',
        label: 'Option 1',
        defaultSelect: true,
        elements: [{ field: 'subOption1', value: 'subValue1' }]
      },
      {
        field: 'option2',
        label: 'Option 2 with Feature Flag',
        defaultSelect: false,
        featureFlag: 'testFlag',
        elements: [{ field: 'subOption2', value: 'subValue2' }]
      },
      {
        field: 'option3',
        label: 'Option 3',
        defaultSelect: false,
        elements: [{ field: 'subOption3', value: 'subValue3' }]
      }
    ];

    mockFeatureFlagsService.isFeatureFlagEnabled.and.returnValue(true);

    spyOn(component, 'createFormControl').and.callThrough();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();

    component.getInitialOptionsList();

    expect(component.searchOptions.length).toBe(3);
    expect(component.searchOptions[0].field).toBe('option1');
    expect(component.searchOptions[1].field).toBe('option2');
    expect(component.searchOptions[2].field).toBe('option3');

    expect(component.selectedOption).toBeDefined();
    expect(component.selectedOption.field).toBe('option1');
    expect(component.createFormControl).toHaveBeenCalledWith('option1');
  });
  it('should handle inputSearchChip functionality correctly', () => {
    const testSelectedElement = {
      field: 'testField',
      label: 'Test Label',
      showChip: 'Test Chip Value'
    };
    mockBaseInputSearchService.getInputFieldSelected.and.returnValue(testSelectedElement);
    mockBaseInputSearchService.inputSearchChip.and.returnValue({ field: 'chipValue' });

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['value'] })])
    });
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();

    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];

    component.searchClickHandler();

    expect(mockBaseInputSearchService.inputSearchChip).toHaveBeenCalled();
    expect(mockCommonSearchService.setInputSearchChip).toHaveBeenCalledWith({ field: 'chipValue' });
  });

  it('should handle additional edge cases and comprehensive coverage', () => {
    expect(component.getUniqueListBy([], 'anyKey')).toEqual([]);

    const testArray = [
      { id: 1, name: 'Test 1' },
      { id: 2, name: 'Test 2' },
      { id: 1, name: 'Test 3' }
    ];
    const uniqueResult = component.getUniqueListBy(testArray, 'id');
    expect(uniqueResult.length).toBe(2);

    component.onCheckChanged(true);
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeTrue();

    component.onCheckChanged(false);
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeFalse();

    const testDate = new Date(2025, 0, 1);
    component.rangeEndDate = testDate;
    expect(component.getMinDateBasedOnProperty('To')).toBe(testDate);
    expect(component.getMinDateBasedOnProperty('From')).toBeUndefined();

    component.isCommaNotAllowed = ['upc', 'hhid'];
    component.showSearchError = '';
    const commaResult = component.isCheckedCommaAllowed('upc', [{ upc: '123,456' }]);
    expect(commaResult).toBeTrue();
    expect(component.showSearchError).toBe(component.searchError.upc);
  });

  it('should handle comprehensive method coverage', () => {
    spyOn(component, 'setOptions');
    const mockResponse = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 }
    ];
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of(mockResponse));

    const mockSelectedOption = {
      elements: [{ field: 'periodElement', options: [] }]
    };

    component.getPeriodOptions(mockSelectedOption);

    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).toHaveBeenCalledWith(mockSelectedOption, mockResponse);

    const enterEvent = new KeyboardEvent('keypress');
    Object.defineProperty(enterEvent, 'which', { value: 13 });
    spyOn(enterEvent, 'preventDefault');
    spyOn(component, 'searchClickHandler').and.stub();

    component.onKeypress(enterEvent, {});
    expect(component.searchClickHandler).toHaveBeenCalled();
    expect(enterEvent.preventDefault).toHaveBeenCalled();

    const pasteEvent = new Event('paste');
    spyOn(component, 'onKeypress');
    component.onPasteSearch(pasteEvent, { field: 'test' });
    expect(component.onKeypress).toHaveBeenCalledWith(pasteEvent, { field: 'test' });
  });

  it('should handle optionSelectedHandler correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });

    component.inputSearchOptions = [
      { field: 'testField', label: 'Test Field', elements: [{ field: 'subField', value: 'test' }] }
    ];

    spyOn(component, 'getSelectionOptionList').and.returnValue({ elements: [] });
    spyOn(component, 'setLinkedSearchOptionQuery');
    spyOn(component, 'removeFormControl');
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl');
    spyOn(component, 'setOptionsFromApi');

    const event = { value: { inputSelected: 'testField' } };
    component.optionSelectedHandler(event);

    expect(component.getSelectionOptionList).toHaveBeenCalled();
    expect(component.setLinkedSearchOptionQuery).toHaveBeenCalled();
    expect(component.removeFormControl).toHaveBeenCalledWith('inputGroups');
    expect(component.removeFormControl).toHaveBeenCalledWith('inputGroupsLevel');
    expect(component.createArrayFormControl).toHaveBeenCalled();
    expect(component.buildArrayElementControl).toHaveBeenCalled();
    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBeFalse();
  });

  it('should handle optionTypeSelectSelected correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['testValue'] })])
    });

    component.selectedOption = { searchButton: true };
    component.optionListSelected = {};

    const mockOptions = [
      { field: 'testField', bindValue: 'testValue', elements: [] }
    ];

    spyOn(component, 'getSelectedOptionFromLabel').and.returnValue(true);
    spyOn(component, 'setLinkedSearchOptionQuery');
    spyOn(component, 'removeFormControl');
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl');
    spyOn(component, 'setOptionsFromApi');

    const event = { target: { options: [], selectedIndex: 0 } };
    const optionSelected = { options: mockOptions };

    component.optionTypeSelectSelected(event, optionSelected);

    expect(component.setLinkedSearchOptionQuery).toHaveBeenCalled();
    expect(component.removeFormControl).toHaveBeenCalledWith('inputGroupsLevel');
    expect(component.createArrayFormControl).toHaveBeenCalledWith('inputGroupsLevel', component.inputFormGroup);
    expect(component.buildArrayElementControl).toHaveBeenCalled();
  });
  it('should handle getSelectedOptionFromLabel correctly', () => {
    const event1 = { target: { options: [{ text: 'Test Label' }, { text: 'Test Label' }], selectedIndex: 1 } };
    const optionObj1 = { label: 'Test Label' };
    const result1 = component.getSelectedOptionFromLabel(event1, optionObj1, 'testValue');
    expect(result1).toBeTrue();

    const event2 = { target: { options: [], selectedIndex: null } };
    const optionObj2 = { bindValue: 'testValue' };
    const result2 = component.getSelectedOptionFromLabel(event2, optionObj2, 'testValue');
    expect(result2).toBeTrue();

    const result3 = component.getSelectedOptionFromLabel(event2, optionObj2, 'differentValue');
    expect(result3).toBeFalse();

    const event3 = { target: { options: [{ text: 'Different Label' }, { text: 'Different Label' }], selectedIndex: 1 } };
    const optionObj3 = { label: 'Test Label' };
    const result4 = component.getSelectedOptionFromLabel(event3, optionObj3, 'testValue');
    expect(result4).toBeFalse();

    const event4 = { target: { options: [{ text: 'Test Label' }], selectedIndex: 0 } };
    const optionObj4 = { bindValue: 'testValue' };
    const result5 = component.getSelectedOptionFromLabel(event4, optionObj4, 'testValue');
    expect(result5).toBeTrue();
  });

  it('should handle linked search options correctly', () => {
    component.inputSearchOptions = [
      { field: 'option1', isLinkedObj: true, linkedWith: 'option2' },
      { field: 'option2', linkedTo: 'option1', linkValue: 'testLink' }
    ];

    spyOn(component, 'pushLinkedValueToQuery');
    spyOn(component, 'clearQueryFrLinkedOptions');

    const optionSelected = { linkedTo: 'option1', linkValue: 'testValue' };
    component.setLinkedSearchOptionQuery(optionSelected, true);
    expect(component.pushLinkedValueToQuery).toHaveBeenCalledWith(optionSelected);

    const optionWithoutLink = {};
    component.setLinkedSearchOptionQuery(optionWithoutLink, true);
    expect(component.clearQueryFrLinkedOptions).toHaveBeenCalledWith(true);

    component.inputSearchOptions = [];
    const result = component.setLinkedSearchOptionQuery(optionSelected, true);
    expect(result).toBeFalse();
  });

  it('should handle pushLinkedValueToQuery correctly', () => {
    component.inputSearchOptions = [
      { field: 'option1' },
      { field: 'option2', query: [] }
    ];

    const optionSelected = { linkedTo: 'option2', linkValue: 'testValue' };
    component.pushLinkedValueToQuery(optionSelected);

    expect(component.inputSearchOptions[1].query).toEqual(['testValue']);

    const optionWithDefaultLink = { linkedTo: 'option2', defaultLinkValue: 'defaultValue' };
    component.pushLinkedValueToQuery(optionWithDefaultLink);

    expect(component.inputSearchOptions[1].query).toEqual(['defaultValue']);
  });

  it('should handle clearQueryFrLinkedOptions correctly', () => {
    component.inputSearchOptions = [
      { field: 'option1', query: ['test'], isLinkedObj: true, linkedWith: 'option2' }
    ];

    mockCommonSearchService.inputSearchChip = { sourceField: 'someValue' };

    const result1 = component.clearQueryFrLinkedOptions(false);
    expect(result1).toBeFalse();

    component.clearQueryFrLinkedOptions(true);
    expect(component.inputSearchOptions[0].query).toEqual([]);

    component.inputSearchOptions[0].query = ['test'];
    mockCommonSearchService.inputSearchChip = { sourceField: 'someValue', option2: 'value' } as any;
    component.clearQueryFrLinkedOptions(true);
    expect(component.inputSearchOptions[0].query).toEqual(['test']);
  });

  it('should handle form control creation and manipulation', () => {
    component.createFormControl('testField');
    expect(component.inputFormGroup).toBeDefined();
    expect(component.inputFormGroup.get('inputSelected').value).toBe('testField');
    expect(component.inputFormGroup.get('savedSearchName')).toBeDefined();

    const formGroup = component.createFormGroup({ test: ['value'] });
    expect(formGroup).toBeInstanceOf(UntypedFormGroup);

    const arrayRow = component.createArrayFormRow({ field: 'testField', value: 'testValue' });
    expect(arrayRow.get('testField').value).toBe('testValue');

    const formObject = component.createFormObject({ test: 'value' });
    expect(formObject).toEqual({ test: 'value' });
  });

  it('should handle getSelectionOptionList correctly', () => {
    const selectedOption = {
      searchButton: true,
      placeholder: 'Test Placeholder',
      elements: [{ field: 'test', value: 'value' }],
      field: 'testField',
      defaultSelect: true,
      columnClass: 'col-6',
      linkedTo: 'linkedField',
      defaultLinkValue: 'defaultLink',
      filterByUser: true
    };

    const result = component.getSelectionOptionList(selectedOption);

    expect(result.searchButton).toBe(true);
    expect(result.placeholder).toBe('Test Placeholder');
    expect(result.elements).toEqual([{ field: 'test', value: 'value' }]);
    expect(result.field).toBe('testField');
    expect(result.defaultSelect).toBe(true);
    expect(result.columnClass).toBe('col-6');
    expect(result.linkedTo).toBe('linkedField');
    expect(result.defaultLinkValue).toBe('defaultLink');
    expect(result.filterByUser).toBe(true);
  });

  it('should handle buildArrayElementControl with options', () => {
    const elements = [{
      field: 'testField',
      type: 'select',
      options: [
        { label: 'Option 1', bindValue: 'value1', defaultSelect: true, elements: [{ field: 'subField', value: 'subValue' }] },
        { label: 'Option 2', bindValue: 'value2', defaultSelect: false }
      ]
    }];

    component.inputFormGroup = fb.group({
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });

    const controlsArr = component.inputFormGroup.get('inputGroups') as UntypedFormArray;
    spyOn(component, 'createArrayFormRow').and.returnValue(fb.group({ testField: ['value1'] }));
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));

    component.buildArrayElementControl(elements, controlsArr);

    expect(component.createArrayFormRow).toHaveBeenCalled();
    expect(component.createArrayFormControl).toHaveBeenCalledWith('inputGroupsLevel', component.inputFormGroup);
    expect(component.inputGroupsLevelOptions).toBeDefined();
  });

  it('should handle buildArrayElementControl without options', () => {
    const elements = [
      { field: 'field1', value: 'value1' },
      { field: 'field2', value: 'value2' }
    ];

    const controlsArr = new UntypedFormArray([]);
    spyOn(component, 'createArrayFormRow').and.returnValue(fb.group({ field1: ['value1'] }));

    component.buildArrayElementControl(elements, controlsArr);

    expect(component.createArrayFormRow).toHaveBeenCalledTimes(2);
    expect(component.createArrayFormRow).toHaveBeenCalledWith({ field: 'field1', value: 'value1' });
    expect(component.createArrayFormRow).toHaveBeenCalledWith({ field: 'field2', value: 'value2' });
  });

  it('should handle addFormControl correctly', () => {
    const parentForm = fb.group({});
    const formControl = new UntypedFormControl('test');

    component.addFormControl('testControl', formControl, parentForm);

    expect(parentForm.get('testControl')).toBe(formControl);
    expect(parentForm.get('testControl').value).toBe('test');
  });

  it('should handle removeFormControl correctly', () => {
    component.inputFormGroup = fb.group({
      testField: ['testValue'],
      anotherField: ['anotherValue']
    });

    expect(component.inputFormGroup.get('testField')).toBeDefined();

    component.removeFormControl('testField');

    expect(component.inputFormGroup.get('testField')).toBeNull();
    expect(component.inputFormGroup.get('anotherField')).toBeDefined();
  });

  it('should handle trackByFn correctly', () => {
    const item = { field: 'testField', label: 'Test Label' };
    const result = component.trackByFn(0, item);
    expect(result).toBe('testField');
  });

  it('should handle inputGroups and inputGroupsLevel getters', () => {
    component.inputFormGroup = fb.group({
      inputGroups: fb.array([fb.group({ test: ['value'] })]),
      inputGroupsLevel: fb.array([fb.group({ level: ['levelValue'] })])
    });

    expect(component.inputGroups).toBeInstanceOf(UntypedFormArray);
    expect(component.inputGroupsLevel).toBeInstanceOf(UntypedFormArray);
    expect(component.inputGroups.length).toBe(1);
    expect(component.inputGroupsLevel.length).toBe(1);
  });

  it('should handle ngOnChanges method', () => {
    expect(() => component.ngOnChanges()).not.toThrow();
  });

  it('should handle getDefaultOptionList and resetFormControl methods', () => {
    expect(() => component.getDefaultOptionList()).not.toThrow();
    expect(() => component.resetFormControl()).not.toThrow();
  });

  it('should handle inputGroupsLevelSelected method', () => {
    expect(() => component.inputGroupsLevelSelected()).not.toThrow();
  });

  it('should handle optionSelectedHandler with dropDownOptionsFromApi', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });

    component.inputSearchOptions = [
      {
        field: 'testField',
        label: 'Test Field',
        elements: [{ field: 'subField', value: 'test' }],
        dropDownOptionsFromApi: { methodName: 'getPeriodOptions', optionLevel: 'selectedOption' }
      }
    ];

    spyOn(component, 'getSelectionOptionList').and.returnValue({ elements: [] });
    spyOn(component, 'setLinkedSearchOptionQuery');
    spyOn(component, 'removeFormControl');
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl');
    spyOn(component, 'setOptionsFromApi');

    const event = { value: { inputSelected: 'testField' } };
    component.optionSelectedHandler(event);

    expect(component.setOptionsFromApi).toHaveBeenCalledWith('getPeriodOptions', 'selectedOption');
  });

  it('should handle optionTypeSelectSelected with dropDownOptionsFromApi', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['testValue'] })])
    });

    component.selectedOption = { searchButton: true };
    component.optionListSelected = {
      dropDownOptionsFromApi: { methodName: 'getPeriodOptions', optionLevel: 'selectedOption' }
    };

    const mockOptions = [
      { field: 'testField', bindValue: 'testValue', elements: [] }
    ];

    spyOn(component, 'getSelectedOptionFromLabel').and.returnValue(true);
    spyOn(component, 'setLinkedSearchOptionQuery');
    spyOn(component, 'removeFormControl');
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl');
    spyOn(component, 'setOptionsFromApi');

    const event = { target: { options: [], selectedIndex: 0 } };
    const optionSelected = { options: mockOptions };

    component.optionTypeSelectSelected(event, optionSelected);

    expect(component.setOptionsFromApi).toHaveBeenCalledWith('getPeriodOptions', 'selectedOption');
  });

  it('should handle optionTypeSelectSelected with subType field correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['testValue'] })])
    });

    component.selectedOption = { searchButton: true };
    component.optionListSelected = {};

    const mockOptions = [
      { field: 'subType', bindValue: 'testValue', elements: [{ field: 'subElement', value: 'subValue' }] }
    ];

    spyOn(component, 'getSelectedOptionFromLabel').and.returnValue(true);
    spyOn(component, 'setLinkedSearchOptionQuery');
    spyOn(component, 'removeFormControl');
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl');

    const event = { target: { options: [{ text: 'Test' }], selectedIndex: 1 } };
    const optionSelected = { options: mockOptions };

    component.optionTypeSelectSelected(event, optionSelected);

    expect(component.inputGroupsLevelOptions).toBeDefined();
    expect(component.setLinkedSearchOptionQuery).toHaveBeenCalled();
    expect(component.buildArrayElementControl).toHaveBeenCalled();
  });

  it('should handle constructor and service injection correctly', () => {
    expect(component.formBuilder).toBeDefined();
    expect(component.baseInputSearchService).toBeDefined();
    expect(component._searchUsersService).toBeDefined();
    expect(component.commonSearchService).toBeDefined();
    expect(component.commonService).toBeDefined();
    expect(component.commonRouteService).toBeDefined();
    expect(component.loaderService).toBeDefined();
    expect(component.baseSavedSearchService).toBeDefined();
    expect(component.facetItemService).toBeDefined();
    expect(component.featureFlagService).toBeDefined();
  });

  it('should handle getFieldErrors method correctly', () => {
    component.inputFormGroup = fb.group({
      testControl: new UntypedFormControl('', Validators.required)
    });

    component.inputFormGroup.get('testControl').markAsUntouched();
    component.inputFormGroup.get('testControl').setValue('');

    const result = component.getFieldErrors('testControl');
    expect(result).toBeTrue();

    component.inputFormGroup.get('testControl').setValue('valid value');
    const result2 = component.getFieldErrors('testControl');
    expect(result2).toBeFalse();

    const result3 = component.getFieldErrors('nonExistentControl');
    expect(result3).toBeUndefined();
  });

  it('should handle getControl method correctly', () => {
    const mockControl = new UntypedFormControl('test');
    component.inputFormGroup = fb.group({
      testControl: mockControl
    });

    const result = component.getControl('testControl');
    expect(result).toBe(mockControl);

    const result2 = component.getControl('nonExistentControl');
    expect(result2).toBeUndefined();
  });

  it('should handle initSubscribes with error in typeahead stream', fakeAsync(() => {
    spyOn(component, 'setTypeAheadList').and.stub();
    const mockApiFn = jasmine.createSpy().and.returnValue(of(['test item']));
    spyOn(component, 'getTypeAheadApis').and.returnValue(mockApiFn);

    component.initSubscribes();

    component.typeahead$.next('test');
    tick(300);

    expect(mockApiFn).toHaveBeenCalledWith('test');
    expect(component.setTypeAheadList).toHaveBeenCalledWith(['test item']);

    flush();
  }));

  it('should handle dateChangeHandler with moment comparison correctly', () => {
    const futureDate = new Date(2026, 0, 1);
    const newDate = new Date(2025, 6, 1);

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['Range'] })]),
      inputGroupsLevel: fb.array([fb.group({ To: [futureDate] })])
    });

    component.dateChangeHandler(newDate);

    expect(component.rangeEndDate).toEqual(newDate);
  });

  it('should handle searchClickHandler with BASE productGroupType correctly', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();

    component.inputFormGroup = fb.group({
      inputSelected: ['productGroupType'],
      inputGroups: fb.array([fb.group({ productGroupType: ['BASE'] })])
    });

    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];
    mockBaseInputSearchService.generateQueryForOptions.and.returnValue(false);

    component.searchClickHandler();

    expect(mockCommonSearchService.fetchDefaultOptions).toHaveBeenCalledWith({
      key: 'testSearchType',
      currentRouter: 'mockRouter',
      isBaseTypeSelected: true
    });
  });

  it('should handle ngOnChanges lifecycle method', () => {
    expect(() => component.ngOnChanges()).not.toThrow();
  });

  it('should handle addFormControl method correctly', () => {
    const mockFormControl = new UntypedFormControl('test');
    const mockParentForm = new UntypedFormGroup({});

    component.addFormControl('testControl', mockFormControl, mockParentForm);

    expect(mockParentForm.get('testControl')).toBe(mockFormControl);
  });

  it('should handle createFormGroup method correctly', () => {
    const formGroupConfig = {
      testField: ['testValue'],
      anotherField: ['anotherValue']
    };

    const result = component.createFormGroup(formGroupConfig);

    expect(result).toBeInstanceOf(UntypedFormGroup);
    expect(result.get('testField').value).toBe('testValue');
    expect(result.get('anotherField').value).toBe('anotherValue');
  });

  it('should handle createArrayFormRow method correctly', () => {
    const element = { field: 'testField', value: 'testValue' };

    const result = component.createArrayFormRow(element);

    expect(result).toBeInstanceOf(UntypedFormGroup);
    expect(result.get('testField').value).toBe('testValue');
  });

  it('should handle createFormControl method correctly', () => {
    const defaultValue = 'defaultTestValue';

    component.createFormControl(defaultValue);

    expect(component.inputFormGroup).toBeInstanceOf(UntypedFormGroup);
    expect(component.inputFormGroup.get('inputSelected').value).toBe(defaultValue);
    expect(component.inputFormGroup.get('savedSearchName')).toBeDefined();
  });

  it('should handle trackByFn method correctly', () => {
    const item = { field: 'testField', label: 'Test Label' };
    const index = 0;

    const result = component.trackByFn(index, item);

    expect(result).toBe('testField');
  });

  it('should handle getDataForInputSearch method correctly', () => {
    const mockResponse = { data: 'test response' };
    mockBaseInputSearchService.getDataForInputSearch.and.returnValue(of(mockResponse));
    mockBaseInputSearchService.currentRouter = 'testRouter';
    (mockBaseInputSearchService as any).testRouter = new Subject();

    spyOn((mockBaseInputSearchService as any).testRouter, 'next');

    component.getDataForInputSearch();

    expect(mockBaseInputSearchService.getDataForInputSearch).toHaveBeenCalled();
    expect((mockBaseInputSearchService as any).testRouter.next).toHaveBeenCalledWith(mockResponse);
  });

  it('should handle getPeriodOptions method correctly', () => {
    const selectedOptionLevel = { elements: [{ options: [{ defaultSelect: true }] }] };
    const mockPromoDetails = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 },
      { periodWeek: '202501', periodId: 1 }
    ];

    spyOn(component, 'setOptions');
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of(mockPromoDetails));

    component.getPeriodOptions(selectedOptionLevel);

    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).toHaveBeenCalledWith(selectedOptionLevel, mockPromoDetails);
  });

  it('should handle getPeriodOptions with empty response', () => {
    const selectedOptionLevel = { elements: [{ options: [] }] };
    spyOn(component, 'setOptions');
    mockBaseInputSearchService.getLastPeriodOptions.and.returnValue(of([]));

    component.getPeriodOptions(selectedOptionLevel);

    expect(mockBaseInputSearchService.getLastPeriodOptions).toHaveBeenCalled();
    expect(component.setOptions).not.toHaveBeenCalled();
  });

  it('should handle getUniqueListBy method correctly', () => {
    const testArray = [
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 },
      { periodWeek: '202501', periodId: 3 }
    ];

    const result = component.getUniqueListBy(testArray, 'periodWeek');

    expect(result.length).toBe(2);
    expect((result[0] as any).periodWeek).toBe('202501');
    expect((result[1] as any).periodWeek).toBe('202502');
  });

  it('should handle setOptions method correctly', () => {
    const selectedOption = {
      elements: [{
        options: [{ defaultSelect: true, label: 'Default Option' }]
      }]
    };
    const response = [
      { periodWeek: '202503', periodId: 3 },
      { periodWeek: '202501', periodId: 1 },
      { periodWeek: '202502', periodId: 2 }
    ];

    spyOn(component, 'getUniqueListBy').and.returnValue(response);

    component.setOptions(selectedOption, response);

    expect(component.getUniqueListBy).toHaveBeenCalledWith(response, 'periodWeek');
    expect(selectedOption.elements[0].options.length).toBeGreaterThan(1);
    expect(selectedOption.elements[0].options[0].defaultSelect).toBe(true);
  });

  it('should handle setOptionsFromApi method correctly', () => {
    const methodName = 'getPeriodOptions';
    const optionLevel = 'selectedOption';
    component.selectedOption = { test: 'data' };

    spyOn(component, 'getPeriodOptions');

    component.setOptionsFromApi(methodName, optionLevel);

    expect(component.getPeriodOptions).toHaveBeenCalledWith(component.selectedOption);
  });

  it('should handle setOptionsFromApi with invalid method', () => {
    const methodName = 'invalidMethod';
    const optionLevel = 'selectedOption';

    expect(() => {
      component.setOptionsFromApi(methodName, optionLevel);
    }).not.toThrow();
  });

  it('should handle setOptionsFromApi with missing parameters', () => {
    expect(() => {
      component.setOptionsFromApi(null, null);
    }).not.toThrow();

    expect(() => {
      component.setOptionsFromApi('getPeriodOptions', null);
    }).not.toThrow();
  });

  it('should handle setLinkedSearchOptionQuery with linked options', () => {
    component.inputSearchOptions = [
      { field: 'sourceField', isLinkedObj: false, linkedTo: 'linkedField' },
      { field: 'linkedField', isLinkedObj: true }
    ];

    const optionSelected = { linkedTo: 'linkedField' };
    spyOn(component, 'pushLinkedValueToQuery');

    component.setLinkedSearchOptionQuery(optionSelected, false);

    expect(component.pushLinkedValueToQuery).toHaveBeenCalledWith(optionSelected);
  });

  it('should handle setLinkedSearchOptionQuery without linked options', () => {
    component.inputSearchOptions = [
      { field: 'regularField', isLinkedObj: false }
    ];

    const optionSelected = { field: 'regularField' };
    const result = component.setLinkedSearchOptionQuery(optionSelected, false);

    expect(result).toBe(false);
  });

  it('should handle setLinkedSearchOptionQuery with clearQueryFrLinkedOptions', () => {
    component.inputSearchOptions = [
      { field: 'linkedField', isLinkedObj: true }
    ];

    const optionSelected = { field: 'regularField' };
    spyOn(component, 'clearQueryFrLinkedOptions');

    component.setLinkedSearchOptionQuery(optionSelected, true);

    expect(component.clearQueryFrLinkedOptions).toHaveBeenCalledWith(true);
  });

  it('should handle clearQueryFrLinkedOptions method correctly', () => {
    component.inputSearchOptions = [
      { field: 'linkedField', isLinkedObj: true, query: ['existingQuery'], linkedWith: 'sourceField' }
    ];

    mockCommonSearchService.inputSearchChip = { sourceField: undefined };

    component.clearQueryFrLinkedOptions(true);

    expect(component.inputSearchOptions[0].query).toEqual([]);
  });

  it('should handle clearQueryFrLinkedOptions with false parameter', () => {
    component.inputSearchOptions = [
      { field: 'linkedField', isLinkedObj: true, query: ['existingQuery'] }
    ];

    const result = component.clearQueryFrLinkedOptions(false);

    expect(result).toBe(false);
    expect(component.inputSearchOptions[0].query).toEqual(['existingQuery']);
  });

  it('should handle getSelectionOptionList method correctly', () => {
    const selectedOption = {
      searchButton: true,
      placeholder: 'Test Placeholder',
      elements: [{ field: 'test', value: 'testValue' }],
      field: 'testField',
      defaultSelect: true,
      columnClass: 'test-class',
      linkedTo: 'linkedField',
      defaultLinkValue: 'linkValue',
      filterByUser: true
    };

    spyOn(component, 'createFormObject').and.returnValue({ formControl: 'created' });

    const result = component.getSelectionOptionList(selectedOption);

    expect(component.createFormObject).toHaveBeenCalledWith({
      placeholder: 'Test Placeholder',
      elements: [{ field: 'test', value: 'testValue' }],
      field: 'testField',
      searchButton: true,
      defaultSelect: true,
      columnClass: 'test-class',
      linkedTo: 'linkedField',
      defaultLinkValue: 'linkValue',
      filterByUser: true
    });
    expect(result).toEqual({ formControl: 'created' });
  });

  it('should handle onCheckChanged method correctly', () => {
    const testValue = true;

    component.onCheckChanged(testValue);

    expect(mockCommonSearchService.isStoreIdUserSearchEnabled).toBe(testValue);
  });

  it('should handle searchClickHandler with invalid form', () => {
    spyOn(component, 'isFormValid').and.returnValue(false);

    const result = component.searchClickHandler();

    expect(result).toBe(true);
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with comma not allowed', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(true);

    const result = component.searchClickHandler();

    expect(result).toBe(true);
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with no input value', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ otherField: ['value'] })])
    });

    const result = component.searchClickHandler();

    expect(result).toBe(false);
    expect(mockBaseInputSearchService.postDataForInputSearch).not.toHaveBeenCalled();
  });

  it('should handle searchClickHandler with existing query', () => {
    spyOn(component, 'isFormValid').and.returnValue(true);
    spyOn(component, 'isCheckedCommaAllowed').and.returnValue(false);
    spyOn(component, 'removeFormControl').and.stub();
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl').and.stub();

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['testValue'] })]),
      inputGroupsLevel: fb.array([])
    });

    component.defaultListSelection = mockBaseInputSearchService.inputOptions[0];
    mockBaseInputSearchService.generateQueryForOptions.and.returnValue(false);

    component.searchClickHandler();

    expect(mockBaseInputSearchService.generateQueryForOptions).toHaveBeenCalled();
    expect(mockBaseInputSearchService.postDataForInputSearch).toHaveBeenCalledWith(true);
  });

  it('should handle inputGroups getter correctly', () => {
    component.inputFormGroup = fb.group({
      inputGroups: fb.array([fb.group({ test: ['value'] })])
    });

    const result = component.inputGroups;

    expect(result).toBeInstanceOf(UntypedFormArray);
    expect(result.length).toBe(1);
  });

  it('should handle inputGroupsLevel getter correctly', () => {
    component.inputFormGroup = fb.group({
      inputGroupsLevel: fb.array([fb.group({ level: ['value'] })])
    });

    const result = component.inputGroupsLevel;

    expect(result).toBeInstanceOf(UntypedFormArray);
    expect(result.length).toBe(1);
  });

  it('should handle createArrayFormControl method correctly', () => {
    component.inputFormGroup = fb.group({});
    spyOn(component, 'addFormControl').and.stub();

    const result = component.createArrayFormControl('testArray', component.inputFormGroup);

    expect(component.addFormControl).toHaveBeenCalledWith(
      'testArray',
      jasmine.any(UntypedFormArray),
      component.inputFormGroup
    );
    expect(result).toBe(component.inputGroups);
  });

  it('should handle createFormObject method correctly', () => {
    const formControl = { field: 'test', value: 'testValue' };

    const result = component.createFormObject(formControl);

    expect(result).toBe(formControl);
  });

  it('should handle setValidators method correctly', () => {
    const inputGroups = fb.array([fb.group({ test: ['', Validators.required] })]);
    const inputGroupsLevel = fb.array([fb.group({ level: ['', Validators.minLength(3)] })]);

    spyOn(component, 'setValidators').and.callThrough();

    expect(() => {
      component.setValidators(inputGroups, inputGroupsLevel);
    }).not.toThrow();
  });

  it('should handle buildArrayElementControl method correctly', () => {
    const elements = [
      { field: 'element1', value: 'value1' },
      { field: 'element2', value: 'value2' }
    ];
    const formArray = fb.array([]);

    spyOn(component, 'createArrayFormRow').and.callFake((element) =>
      fb.group({ [element.field]: [element.value] })
    );

    component.buildArrayElementControl(elements, formArray);

    expect(component.createArrayFormRow).toHaveBeenCalledTimes(2);
    expect(formArray.length).toBe(2);
  });

  it('should handle optionSelectedHandler method correctly', () => {
    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([]),
      inputGroupsLevel: fb.array([])
    });

    component.inputSearchOptions = [
      { field: 'testField', elements: [{ field: 'sub', value: 'test' }] }
    ];

    spyOn(component, 'getSelectionOptionList').and.returnValue({ field: 'testField' });
    spyOn(component, 'setLinkedSearchOptionQuery');
    spyOn(component, 'removeFormControl');
    spyOn(component, 'createArrayFormControl').and.returnValue(new UntypedFormArray([]));
    spyOn(component, 'buildArrayElementControl');

    component.optionSelectedHandler(false);

    expect(component.getSelectionOptionList).toHaveBeenCalled();
    expect(component.setLinkedSearchOptionQuery).toHaveBeenCalled();
    expect(component.removeFormControl).toHaveBeenCalledTimes(2);
    expect(component.createArrayFormControl).toHaveBeenCalledTimes(1);
    expect(component.buildArrayElementControl).toHaveBeenCalledTimes(1);
  });

  it('should handle getInitialOptionsList method correctly', () => {
    spyOn(component, 'createFormControl');
    spyOn(component, 'optionSelectedHandler');

    component.getInitialOptionsList();

    expect(component.searchOptions).toBeDefined();
    expect(component.createFormControl).toHaveBeenCalled();
  });

  it('should handle dateChangeHandler with inputGroupsLevel controls', () => {
    const newDate = new Date(2025, 0, 15);
    const mockControl = new UntypedFormControl(new Date(2025, 0, 1));

    component.inputFormGroup = fb.group({
      inputSelected: ['testField'],
      inputGroups: fb.array([fb.group({ testField: ['Range'] })]),
      inputGroupsLevel: fb.array([mockControl])
    });

    component.dateChangeHandler(newDate);

    expect(component.rangeEndDate).toEqual(newDate);
  });

  it('should handle pushLinkedValueToQuery method correctly', () => {
    const optionSelected = { linkedTo: 'linkedField', linkValue: 'testValue' };
    component.inputSearchOptions = [
      { field: 'linkedField', isLinkedObj: true, query: [] }
    ];

    mockCommonSearchService.inputSearchChip = { sourceField: 'existingValue' };

    component.pushLinkedValueToQuery(optionSelected);

    expect(component.inputSearchOptions[0].query).toBeDefined();
  });

});

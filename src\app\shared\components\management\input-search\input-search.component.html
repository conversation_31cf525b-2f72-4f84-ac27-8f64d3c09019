<form [formGroup]="form" (clickOutside)="onClickOutside($event)" class="input-search-wrapper">
  <input type="submit" class="d-none" />
  <div class="position-relative">
    <div class="d-flex">
      <div class="search-bar input-group">
        <div class="d-flex row">
          <div class="d-flex mr-2 mt-2 w-auto justify-content-center align-items-center">
            <label> Search:</label>
          </div>
          <select class="custom-select form-control col custom-width category-name" (change)="itemClick()" formControlName="categoryName">
            <option *ngFor="let item of items; let i = index" [ngValue]="item.label">
              {{ item.label }}
            </option>
          </select>
          <div [ngClass]="{ hidden: isStartDateSelectedinPluPage() }">
            <select
              class="custom-select form-control col custom-width"
              (change)="dateSelect($event)"
              *ngIf="rangeDates.includes(itemSelected) && !currentRoute.includes('pod-playground')"
              formControlName="dateField"
            >
              <option *ngFor="let item of dateField" [value]="item">{{ item }}</option>
            </select>
          </div>
          <div *ngIf="isVendors()">
            <app-multi-select-checkbox [form] = "form"></app-multi-select-checkbox>
          </div>
        </div>
        <div class="col-7" *ngIf="verbiageForm">
          <div class="row">
            <input type="text" placeholder="Headline 1" class="textarea  form-control search-field col-4 ml-1 pr-1"
              formControlName="headLine" />
            <input type="text" placeholder="Headline 2"
              class="ml-1 textarea  form-control search-field col-4 pr-1" formControlName="headLine2" />
            <input type="text" placeholder="Offer Description"
              class="ml-1 textarea form-control  search-field col-4 mr-2" formControlName="productDesc" />
          </div>
        </div>
        <div
          class="col-2"
          *ngIf="
            rangeDates.includes(itemSelected) &&
            (selectedDate === 'Range' || currentRoute.includes('pod-playground') || itemSelected === 'startDate')
          "
        >
          <div
            class="input-group date-wrapper"
            [class.border-danger]="getFieldErrorsOnSearch('rangeStartDate')"
            [class.is-populated]="getFieldValue('rangeStartDate') !== ''"
          >
            <input
              onkeydown="return false"
              type="text"
              class="form-control dt-field form-control-lg optional border-0"
              id="startDate"
              name="startDate"
              autocomplete="off"
              #startDatePicker="bsDatepicker"
              formControlName="rangeStartDate"
              (bsValueChange)="setMinEndDate($event)"
              [bsConfig]="{
                containerClass: colorTheme,
                dateInputFormat: 'MM/DD/YYYY',
                showWeekNumbers: false
              }"
              bsDatepicker
              placeholder="From"
              markAsTouchedOnFocus
              [formCtrl]="form && form.get('rangeStartDate')"
            />
            <div class="input-group-append mr-0">
              <div class="input-group-text input-text-icon pad-5 border-0" (click)="startDatePicker.toggle()">
                <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
              </div>
            </div>
          </div>
        </div>

        <span
          class="pt-2"
          *ngIf="
            rangeDates.includes(itemSelected) &&
            (selectedDate === 'Range' || currentRoute.includes('pod-playground') || itemSelected === 'startDate')
          "
          >-</span
        >
        <div
          class="col-2"
          *ngIf="
            rangeDates.includes(itemSelected) &&
            (selectedDate === 'Range' || currentRoute.includes('pod-playground') || itemSelected === 'startDate')
          "
        >
          <div
            class="input-group date-wrapper"
            [class.border-danger]="getFieldErrorsOnSearch('rangeEndDate')"
            [class.is-populated]="getFieldValue('rangeEndDate') !== ''"
          >
            <input
              onkeydown="return false"
              type="text"
              class="form-control form-control-lg dt-field optional border-0"
              id="endDate"
              name="endDate"
              autocomplete="off"
              formControlName="rangeEndDate"
              #endDatePicker="bsDatepicker"
              [minDate]="rangeEndDate"
              [bsConfig]="{
                containerClass: colorTheme,
                dateInputFormat: 'MM/DD/YYYY',
                showWeekNumbers: false
              }"
              bsDatepicker
              placeholder="To"
              markAsTouchedOnFocus
              [formCtrl]="form && form.get('rangeEndDate')"
            />
            <div class="input-group-append mr-0">
              <div class="input-group-text input-text-icon pad-5 border-0" (click)="endDatePicker.toggle()">
                <img src="assets/icons/calendar-icon.svg" alt="" height="20" />
              </div>
            </div>
          </div>
        </div>
        <div class="col-5 pr-0 position-relative" *ngIf="getTypeaheadSearchField()">
          <ng-select
            [items]="userArr"
            bindLabel="name"
            bindValue="id" 
            [multiple]="false"
            [typeahead]="typedUser$"
            formControlName="searchInput"
            clearAllText="Clear"
            [tooltip]="setTooltipValue('searchInput')"
            (change)="getlUserId('searchInput') && searchClickHandler()"
            class="mt--2"
          >
          </ng-select>
        </div>
        <div class="col-5 pr-0 position-relative" *ngIf="getTypeaheadSearchAdBugField()">
          <ng-select
            [items]="adBugArr"
            [multiple]="false"
            [typeahead]="typedAdBug$"
            formControlName="searchInput"
            clearAllText="Clear"
            [tooltip]="setTooltipValue('searchInput')"
            (change)="getAdBug('searchInput') && searchClickHandler()"
            class="mt--2"
          >
          </ng-select>
        </div>
          <div class="col-5 pr-0 position-relative" *ngIf="getTypeaheadSearchPeriodField()">
          <ng-select
            [items]="periodArr"
            [multiple]="false"
            [typeahead]="typedPeriod$"
            formControlName="searchInput"
            clearAllText="Clear"
            [tooltip]="setTooltipValue('searchInput')"
            (change)="searchClickHandler()"
            class="mt--2"
          >
          </ng-select>
        </div>
        <div class="col-5 pr-0 position-relative" *ngIf="itemSelected === 'dept'">
          <ng-select
            bindLabel="name"
            bindValue="id"
            [items]="departmentsArr"
            [multiple]="false"
            [typeahead]="typedDept$"
            formControlName="searchInput"
            clearAllText="Clear"
            [tooltip]="setTooltipValue('searchInput')"
            (change)="isValueExistsInInput(departmentsArr) && searchClickHandler()"
            class="mt--2"
          >
          </ng-select>
        </div>
        <div class="col-5 pr-0 position-relative" *ngIf="itemSelected === 'div'">
          <ng-select
            [items]="divisionsArr"
            [multiple]="false"
            [typeahead]="typedDivison$"
            formControlName="searchInput"
            clearAllText="Clear"
            [tooltip]="setTooltipValue('searchInput')"
            (change)="isValueExistsInInput(divisionsArr) && searchClickHandler()"
            class="mt--2"
          >
          </ng-select>
        </div>

        <input
          type="text"
          *ngIf="getInputSearchField() && !verbiageForm && ! isVendors()"
          #inputTextArea
          (paste)="onPasteSearch($event)"
          (keyup)="keypress($event)"
          class="ml-1 textarea br-0 form-control search-field col-5 pr-0"
          formControlName="searchInput"
        />
        <div class="input-group-append" *ngIf="getInputSearchField() && !rangeDates.includes(itemSelected) && !verbiageForm  && ! isVendors()" (click)="searchClickHandler()">
          <span class="resize-icon search-date-icon"><img alt="" src="assets/icons/Grey-Search.svg" /></span>
        </div>
        <input
          *ngIf="false"
          type="text"
          id="offerDate"
          placeholder="Date"
          class="form-control form-control-lg optional date-search"
          [bsConfig]="{
            containerClass: colorTheme,
            dateInputFormat: 'MM/DD/YYYY',
            showWeekNumbers: false
          }"
          name="endDate"
          autocomplete="off"
          bsDatepicker
          formControlName="searchInput"
        />

        <button *ngIf="rangeDates.includes(itemSelected) ||  verbiageForm || isVendors()" class="btn btn-primary" (click)="searchClickHandler()">Search</button>
      </div>
    </div>

    <div class="dropdown request-search col-12">
      <div class="row padding-top-5">
        <div *ngIf="headerPage" class="col-3 p-0 ml-n3"></div>
        <div *ngIf="!headerPage" class="col-2 p-0" style="margin-left: 205px">
          <a
            href=""
            aria-expanded="false"
            aria-haspopup="true"
            (click)="getSystemSavedSearches('S'); getUsersSavedSearches('U')"
            class="saved-searches dropdown-toggle actions-button"
            data-toggle="dropdown"
            >Saved Searches<img alt="arrow-down-icon" class="pl-3" src="assets/icons/blue_downArrow.svg" />
          </a>
          <small *ngIf="facetItemService.showSearchError" class="text-danger"
            >You must select search or filter and enter a name before saving.</small
          >
          <div aria-labelledby="dropdownMenuLink" class="dropdown-menu search-save" x-placement="bottom-start">
            <div class="">
              <div class="col-12 input-group px-1 pt-1">
                <input
                  type="text"
                  (input)="checkExistSavedSearch($event)"
                  placeholder="Enter Search Name to Save"
                  formControlName="savedSearchName"
                  class="form-control"
                />
                <button class="btn btn-primary custom-btn-saved" *ngIf="showUpdateBtn" (click)="updateSavedSearchHandler()">Update</button>
                <button class="btn btn-primary custom-btn-saved" *ngIf="!showUpdateBtn" (click)="savedSearchHandler()">Save</button>
              </div>
              <div class="saved-searches-div">
                <div class="request-suggestion">
                  <div *ngIf="savedSearchListSystem">
                    <button
                      class="btn d-block saved-search-rectangle"
                      *ngFor="let item of savedSearchListSystem"
                      (click)="applySavedSearch(item)"
                    >
                      {{ item.name }}
                    </button>
                  </div>
                </div>
                <div *ngIf="savedSearchListUsers.length > 0">
                  <hr class="line" />
                </div>
                <div class="col-12 search-results p-0" *ngIf="savedSearchListUsers">
                  <ul class="list-group">
                    <li class="list-group-item custom-list-style" *ngFor="let item of savedSearchListUsers">
                      <span class="cursor-pointer" (click)="applySavedSearch(item)">{{ item.name }} </span>
                      <img
                        *ngIf="item.savedSearchFlag === 'U'"
                        class="cursor-pointer float-right"
                        src="assets/icons/remove.svg"
                        alt=""
                        (click)="removeSavedSearch(item, $event)"
                      />
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-2 pl-1" *ngIf="rangeDates.includes(itemSelected) && selectedDate === 'Range'">
          <div class="text-danger" *ngIf="getFieldErrorsOnSearch('rangeStartDate') as errors">
            <small *ngIf="errors && errors.required">Start date required</small>
          </div>
        </div>
        <div class="col-2" *ngIf="rangeDates.includes(itemSelected) && selectedDate === 'Range'">
          <div class="text-danger" *ngIf="getFieldErrorsOnSearch('rangeEndDate') as errors">
            <small *ngIf="errors && errors.required">End date required</small>
            <small *ngIf="errors && errors.endDateLessThanStart">End Date must be later than start date</small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-2"></div>
      <div class="col-2"></div>
      <span class="color-red col-5 pr-0" *ngIf="multipleCheckValidator">{{ multipleCheckValidator }}</span>
      <span class="color-red col-5 pr-0" *ngIf="multipleFieldValidator">{{ multipleFieldValidator }}</span>
    </div>
  </div>
</form>

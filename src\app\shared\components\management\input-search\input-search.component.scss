@import "../../../../../scss/colors";

.elements {
  right: 14px;
  top: 44px;
  z-index: 999;
}
.searchCategorySelected {
  left: 51px;
}
ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.input-group-text {
  background: #fff;
  &.selection {
    padding: 0;
    .chevron-postion {
      padding: 10px 12px 9px;
    }
  }
}
.label-style {
  margin: 0;
}
.pl-3{
  padding-left: 3px;
}
.item-style {
  background: #fff;
  right: 0;
  width: 210px;
  z-index: 999;
  ul {
    font-size: 16px;
    margin: 0;
    max-height: 300px;
    overflow: auto;
    li {
      a {
        cursor: pointer;
        color: #000;
        display: block;
        padding: 8px 10px;
        text-decoration: none;

        &:hover {
          background-color: #00529f;
          color: #fff;
        }
        &.active {
          background-color: #00529f;
          color: #fff;
          text-decoration: none;
        }
      }
    }
  }
}
.custom-width{
  width: 150px !important;
  margin-right: 15px;
}
.category-name {
  margin-right: 15px;
 
}

.search-item {
  left: -31px;
}
.chevron-postion {
  cursor: pointer;
  right: 15px;
  top: 10px;
}

.search-field {
  cursor: pointer;
  padding-right: 40px;
  flex: 1;
}
.label-height {
  height: 47px;
}
.search-icon {
  background: #999;
  padding: 5px 8px 8px 5px;
  cursor: pointer;
}

.search-bar {
  display: flex;
  flex: 1;
}
.textarea {
  height: 40px;
  resize: none;
  line-height: 36px;
}
.date-search {
  height: 40px;
}
.saved-searches-div {
  overflow-y: scroll;
  max-height: 335px;
}
.br-white {
  border-right: 1px solid #fff;
}
.pad-5 {
  padding: 5px;
}
.search-date-icon {
  border: 1px solid $grey-lighter-hex;
  padding: 7px 10px 10px 10px;
  cursor: pointer;
}
.padding-top-5 {
  padding-top: 5px;
}
.br-0 {
  border-right: none;
}
.request-search {
  .cursor-pointer {
    cursor: pointer;
  }
  .saved-searches {
    font-size: 12px;
    padding-top: 9px;
    font-weight: bold;
    color: #00529f !important;
    text-decoration: underline;
  }
  .dropdown-toggle:not(.nav-link) {
    border: none !important;
  }
  button {
    border: none;
    // height: 37px;
  }
 
  .request-suggestion {
    padding-top: 3px;
    width: 100%;
    button {
      font-size: 14px;
      height: 30px;
      padding: 0px 14px;
    }
  }
  .search-results {
    ul li {
      border: none;
      // &:nth-child(even) {
      //     background: #F0F4F7;
      // }
      &:hover {
        background: #f0f4f7;
      }
    }
  }
  .custom-list-style {
    padding: 5px 25px;
    font-size: 14px;
  }
  .search-save {
    width: 457px;
    padding: 0px;
    border-radius: 0
  }
  .custom-btn-saved:hover {
    background-color: #00529f;
    color: #fff;
  }
  .saved-search-rectangle {
    border-radius: 2px 2px 0 0;
    background-color: #FFFFFF;
    width: 100%;
    text-align: left;
    &:hover {
      background: #F0F0F0;
    }
  }
  .line {
    box-sizing: border-box;
    border: 1px solid #D9DDE0;
  }
}
.date-wrapper {
  border: 1px solid #DEDEDE !important;
}
.border-danger.date-wrapper  {
  border: 1px solid #cf202f !important;
}
.hidden {
  display: none;
}
.verbiage-width{
  width: 240px;
}
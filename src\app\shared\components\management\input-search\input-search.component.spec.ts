import { <PERSON><PERSON><PERSON>ter, NO_ERRORS_SCHEMA } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { SearchOfferService } from "@appOffersServices/search-offer.service";
import { PluSearchService } from "@appRequestServices/pluSearch.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { CommonService } from "@appServices/common/common.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { IviePromotionService } from "@appServices/common/ivie-promotion.service";
import { LoaderService } from "@appServices/common/loader.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { SearchUsersService } from "@appServices/common/search-users.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import {
    PermissionsConfigurationService,
    PermissionsModule,
    PermissionsService,
} from '@appShared/albertsons-angular-authorization';
import { Event } from "jquery";
import { BsDatepickerModule } from "ngx-bootstrap/datepicker";
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject, of, throwError } from "rxjs";
import { InputSearchComponent } from "./input-search.component";
import * as moment from "moment";

const defaultSearches = {
  savedSearches: [
    {
      name: "Needs Assignment",
      createUserId: "system",
      type: "R",
      savedSearchFlag: "S",
      searchQuery:
        "limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;queryWithOrFilters:['nonDigitalStatus=S#digitalStatus=S']",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-03-23T18:40:27.153+00:00",
      updateUserId: "system",
    },
    {
      name: "Needs Digital Assignment",
      createUserId: "system",
      type: "R",
      savedSearchFlag: "S",
      searchQuery:
        "limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;queryWithOrFilters:['digitalStatus=S']",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-03-23T18:40:27.153+00:00",
      updateUserId: "system",
    },
    {
      name: "Needs Non-Digital Assignment",
      createUserId: "system",
      type: "R",
      savedSearchFlag: "S",
      searchQuery:
        "limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;queryWithOrFilters:['nonDigitalStatus=S']",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-03-23T18:40:27.153+00:00",
      updateUserId: "system",
    },
    {
      name: "Your Assigned Offers",
      createUserId: "system",
      type: "R",
      savedSearchFlag: "S",
      searchQuery:
        "effectiveEndDate=[DATE];limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;queryWithOrFilters:['combinedDigitalUser=(USER)#combinedNonDigitalUser=(USER),digitalUiStatus=(I OR S OR A OR P OR E OR U OR R)#nonDigitalUiStatus=(I OR S OR A OR P OR E OR U OR R)']",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-02-25T20:56:34.466+00:00",
      updateUserId: "system",
    },
    {
      name: "Your Requests",
      createUserId: "system",
      type: "R",
      savedSearchFlag: "S",
      searchQuery:
        "effectiveEndDate=[DATE];limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;createUserId=(USER);queryWithOrFilters:['digitalUiStatus=(I OR S OR A OR P OR E OR U OR R)#nonDigitalUiStatus=(I OR S OR A OR P OR E OR U OR R)']",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-02-25T20:56:34.466+00:00",
      updateUserId: "system",
    },
  ],
};
const defaultSearchesOffer = {
  savedSearches: [
    {
      name: "Offers From Your Requests",
      createUserId: "system",
      type: "O",
      savedSearchFlag: "S",
      searchQuery:
        "endDt=[DATE];limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;createUserId=(USER);offerStatus=(I OR DE OR P OR PU);",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-02-25T20:56:34.466+00:00",
      updateUserId: "system",
    },
    {
      name: "Your Assigned Offers",
      createUserId: "system",
      type: "O",
      savedSearchFlag: "S",
      searchQuery:
        "endDt=[DATE];limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;userId=(USER);isApplicableToJ4U=(false OR true);offerStatus=(I OR DE OR P OR PU);",
      createTs: "2020-02-25T20:47:14.694+00:00",
      updateTs: "2020-02-25T20:56:34.466+00:00",
      updateUserId: "system",
    },
  ],
};
describe("InputSearchComponent", () => {
  let component: InputSearchComponent;
  let fixture: ComponentFixture<InputSearchComponent>;
  beforeEach(() => {
    const authServiceStub = () => ({
      onUserDataAvailable: (arg) => ({}),
      getUserId: () => ({}),
    });
    const facetItemServiceStub = () => ({
      setOfferFilter: (string) => ({}),
      setFacetItems: () => ({}),
      setFacetItemList: () => ({}),
      setTodayOption: (object) => ({}),
      populateFacetSearch: (arg) => ({}),
      getTodayOption: () => ({ forEach: () => ({}), splice: () => ({}) }),
      emptyTodayOption: () => ({}),
      getInputRangeValue: () => ({}),
      resetProgramCdChecked: () => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppData: () => ({ divisions: {}, departments: {} }),
    });
    const myTaskServiceStub = () => ({ isMyTasksClicked: {} });
    const queryGeneratorStub = () => ({
      removeParamFromQueryFilter: () => ({}),
      removeParametersFromQueryFilter: () => ({}),
      setQueryWithFilter: (array) => ({}),
      getInputValue: (itemSelected) => ({ split: () => ({}) }),
      removeParameters: (array) => ({}),
      pushParameters: (object) => ({}),
      getDefaultSearchValue: (object) => "DESC",
      removeParam: (string) => ({}),
      getQueryWithFilter: () => ({
        push: () => ({}),
        length: {},
        map: () => ({ join: () => ({}) }),
        join: () => ({}),
      }),
      getQueryFilter: () => ({}),
      getQuery: () => ({ split: () => ({ filter: () => ({}) }) }),
      pushParam: (object) => ({}),
      setQuery: (string) => ({}),
      getInputRangeValue: () => ({}),
    });
    const routerStub = () => ({
      routerState: {
        snapshot: {
          url: `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`,
        },
      },
    });
    const searchOfferRequestServiceStub = () => ({
      populateHomeFilterSearch: (object) => ({}),
      getOfferDetails: (response) => ({}),
      searchOfferRequest: (arg, arg2, arg3) => ({
        subscribe: (f) => f({}),
        bind: () => ({}),
      }),
      toFormGroup: () => ({}),
      offerRequestSearchOptions: () => ({}),
    });
    const iviePromotionServiceStub = {
      getAdBugs: () => ({ subscribe: () => ({}) }),
      iviePromotionSearchObvl: { subscribe: (f) => f({}) },
      searchAllPromotions: () => ({ subscribe: () => ({}) }),
      getFacetCountsData: () => ({}),
      getPaginationSearch: () => ({}),
      submitPromotionArray: () => ({ subscribe: () => ({}) }),
      isRedirectRoute$: { subscribe: (f) => f({}), next: () => ({}) },
      onRouteChange$: { subscribe: (f) => f({}), next: () => ({}) },
      onSearchFilterChange$: { subscribe: (f) => f({}), next: () => ({}) },
      onFacetChipCloseChange$: { subscribe: (f) => f({}), next: () => ({}) },
      onInputSearchChange$: { subscribe: (f) => f({}), next: () => ({}) },
      globalSearchChange$: { subscribe: (f) => f({}), next: () => ({}) },
      searchPromotionPage: { subscribe: (f) => f({}), next: () => ({}) },
    };
    const searchOfferServiceStub = () => ({
      savedSearchForRetrieve: (reqType, userType) => ({
        subscribe: (f) => f({}),
      }),
      searchAllOffers: (arg, arg2) => ({
        subscribe: (f) => f({}),
        bind: () => ({}),
      }),
      savedSearchforOffer: (searchQuery, savedSearchName, type) => ({
        subscribe: (f) => f({}),
      }),
      updateSavedSearch: (modifyItem, savedSearchName) => ({
        subscribe: (f) => f({}),
      }),
      deleteSavedSearchOffer: (name, type) => ({ subscribe: (f) => f({}) }),
    });
    const toastrServiceStub = () => ({});
    const loaderServiceStub = () => ({ isDisplayLoader: (arg) => ({}) });
    const bulkUpdateServiceStub = () => ({
      requestIdArr: {},
      offersIdArr: {},
      requestIdsListSelected$: { next: () => ({}) },
      offerIdsListSelected$: { next: () => ({}) },

      userTypeArray: {},
    });
    const searchUsersServiceStub = () => ({ getUsers: (term) => ({}) });
    const pluSearchServiceStub = () => ({ fetchPluList: (obj) => ({}) });
    const commonServiceStub = () => ({
      getDivisions: () => ({}),
      getDepartmentNameFromCode: (department) => ({}),
      getEventsData: () => ({}),
      getPeriodWeeks: () => ({}),
    });
    const permissionsServiceStub = () => ({});

    const commonSearchServiceStub = () => ({
      batchActionActiveTab: {},
      setActiveCurrentSearchType: (currentRouter) => ({}),
      currentRouter: {},
      setAllFilterOptions: (object) => ({}),
      setFilters: (object) => ({}),
      resetAllFilterOptions: (object) => ({}),
      pad: (value: string) => value.padStart(6, "0"), // Mock implementation of pad method
    });

    const featureFlagServiceStub = () => ({
      assignFeatureFlag: () => ({}),
      isFeatureFlagEnabled: (arg) => ({}),
      hasFlags: () => ({}),
    });

    TestBed.configureTestingModule({
      imports: [
        BsDatepickerModule.forRoot(),
        PermissionsModule.forRoot({
          permissionsIsolate: true,
          configurationIsolate: true,
          rolesIsolate: true,
        }),
      ],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [InputSearchComponent],
      providers: [
        PermissionsService,
        PermissionsConfigurationService,
        Location,
        { provide: AuthService, useFactory: authServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: IviePromotionService, useValue: iviePromotionServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: Router, useFactory: routerStub },
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub,
        },
        { provide: SearchOfferService, useFactory: searchOfferServiceStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: LoaderService, useFactory: loaderServiceStub },
        { provide: BulkUpdateService, useFactory: bulkUpdateServiceStub },
        { provide: SearchUsersService, useFactory: searchUsersServiceStub },
        { provide: PluSearchService, useFactory: pluSearchServiceStub },
        { provide: CommonService, useFactory: commonServiceStub },

        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub}
      ],
    });
    fixture = TestBed.createComponent(InputSearchComponent);
    component = fixture.componentInstance;
  });

  // public commonSearchService :CommonSearchService,
  // location: Location,

  it("can load instance", () => {
    expect(component).toBeTruthy();
  });
  it(`chevronClick has default value`, () => {
    expect(component.chevronClick).toEqual(false);
  });
  it(`multipleCheckValidator has default value`, () => {
    expect(component.multipleCheckValidator).toEqual(false);
  });
  it(`multipleFieldValidator has default value`, () => {
    expect(component.multipleFieldValidator).toEqual(false);
  });
  it(`savedSearchListUsers has default value`, () => {
    expect(component.savedSearchListUsers).toEqual([]);
  });
  it(`savedSearchListSystem has default value`, () => {
    expect(component.savedSearchListSystem).toEqual([]);
  });
  it(`showUpdateBtn has default value`, () => {
    expect(component.showUpdateBtn).toEqual(false);
  });
  it(`rangeDates has default value`, () => {
    expect(component.rangeDates).toEqual([
      "effectiveStartDate",
      "createTimeStamp",
      "lastUpdateTimestamp",
      "startDt",
      "endDt",
      "effectiveEndDate",
      "promotionStartDt",
      "startDate",
    ]);
  });
  it(`colorTheme has default value`, () => {
    expect(component.colorTheme).toEqual(`theme-dark-blue`);
  });
  it(`userArr has default value`, () => {
    expect(component.userArr).toEqual([]);
  });
  it(`getInputSearchField call`, () => {
    component.itemSelected = "userId";
    const search = component.getInputSearchField();
    expect(search).toEqual(false);
  });
  it(`departmentsArr has default value`, () => {
    expect(component.departmentsArr).toEqual([]);
  });

  describe("ngOnInit", () => {
    it("make expected calls", () => {
      const searchOfferRequestServiceStub = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      let form = new UntypedFormGroup({
        searchInput: new UntypedFormControl(),
        categoryName: new UntypedFormControl(),
        savedSearchName: new UntypedFormControl(),
        dateField: new UntypedFormControl(),
        rangeEndDate: new UntypedFormControl(),
        rangeStartDate: new UntypedFormControl(),
        headLine: new UntypedFormControl(),
        headLine2: new UntypedFormControl(),
        productDesc: new UntypedFormControl(),
      });
      component.defaultValue = true;
      spyOn(component, "itemClick");
      spyOn(component, "initSubscribes");
      spyOn(searchOfferRequestServiceStub, "offerRequestSearchOptions");
      spyOn(searchOfferRequestServiceStub, "toFormGroup").and.returnValue(form);
      component.ngOnInit();
      expect(component.initSubscribes).toHaveBeenCalled();
      expect(component.itemClick).toHaveBeenCalled();
      expect(
        searchOfferRequestServiceStub.offerRequestSearchOptions
      ).toHaveBeenCalled();
    });
  });
  describe("initSubscribes", () => {
    it("makes expected calls", () => {
      spyOn(component, "searchClickHandler");
      component.initSubscribes();
      expect(component.searchClickHandler).toHaveBeenCalled();
    });
  });
  describe("mouseleave", () => {
    it("makes expected calls", () => {
      component.mouseleave();
      expect(component.chevronClick).toEqual(false);
    });
  });
  describe("getSelectedOfferProgrmCd", () => {
    it("makes expected calls", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeChecked = {
        GR: true,
        SPD: true,
      };
      const result = component.getSelectedOfferProgrmCd();
      expect(result).toEqual("GR OR SPD");
    });
  });
  describe("itemClick", () => {
    beforeEach(() => {
      spyOn(component, "clearControlValidator");
    });
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl(""),
        categoryName: new UntypedFormControl("testCategory"),
        dateField: new UntypedFormControl(""),
        rangeStartDate: new UntypedFormControl(""),
        rangeEndDate: new UntypedFormControl(""),
        headLine: new UntypedFormControl(),
        headLine2: new UntypedFormControl(),
        productDesc: new UntypedFormControl(),
      });
      component.rangeDates = [
        "effectiveStartDate",
        "createTimeStamp",
        "lastUpdateTimestamp",
        "startDt",
        "endDt",
        "effectiveEndDate",
        "promotionStartDt",
      ];
      component.items = [
        {
          label: "testCategory",
          field: "endDt",
        },
      ];
      component.itemClick();
      expect(component.form.get("dateField").value).toEqual("Range");
    });
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl(""),
        categoryName: new UntypedFormControl("testCategory"),
        dateField: new UntypedFormControl(""),
        rangeStartDate: new UntypedFormControl(""),
        rangeEndDate: new UntypedFormControl(""),
        headLine: new UntypedFormControl(),
        headLine2: new UntypedFormControl(),
        productDesc: new UntypedFormControl(),
      });
      component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}`;
      component.rangeDates = [
        "effectiveStartDate",
        "createTimeStamp",
        "lastUpdateTimestamp",
        "startDt",
        "endDt",
        "effectiveEndDate",
        "promotionStartDt",
      ];
      component.items = [
        {
          label: "testCategory",
          field: "startDt",
        },
      ];
      component.itemClick();
      expect(component.selectedDate).toEqual("Today+");
    });
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl(""),
        categoryName: new UntypedFormControl("testCategory"),
        dateField: new UntypedFormControl(""),
        headLine: new UntypedFormControl(),
        headLine2: new UntypedFormControl(),
        productDesc: new UntypedFormControl(),
      });
      component.rangeDates = [
        "effectiveStartDate",
        "createTimeStamp",
        "lastUpdateTimestamp",
        "startDt",
        "endDt",
        "effectiveEndDate",
        "promotionStartDt",
      ];
      component.items = [
        {
          label: "testCategory",
          field: "PLU",
        },
      ];
      component.itemClick();
      expect(component.form.get("dateField").value).toEqual(null);
    });
  });

  describe("onClickOutside", () => {
    it("makes expected calls", () => {
      const spy = spyOn(component, "mouseleave");
      component.onClickOutside(false);
      expect(spy).toHaveBeenCalled();
    });
  });
  // describe("getUsers", () => {
  //   it("makes expected calls", () => {
  //     const searchUsersServiceStub: SearchUsersService =
  //       fixture.debugElement.injector.get(SearchUsersService);
  //     spyOn(searchUsersServiceStub, "getUsers").and.returnValue(of({}));
  //     let result = component.getUsers("John snow");
  
  //     expect(result).toEqual(of([]));
  //   });
  // });
  describe("setTooltipValue", () => {
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        testControl: new UntypedFormControl("tc"),
      });
      let result = component.setTooltipValue("testControl");
      expect(result).toEqual("tc");
    });
  });
  describe("getSearchField", () => {
    it("makes expected calls", () => {
      component.itemSelected = "startDt";
      let result = component.getSearchField();
      expect(result).toEqual(true);
    });
  });
  describe("getUsersSavedSearches", () => {
    it("makes expected calls", () => {
      const loaderServiceStub: LoaderService =
        fixture.debugElement.injector.get(LoaderService);
      const searchOfferServiceStub: SearchOfferService =
        fixture.debugElement.injector.get(SearchOfferService);
      spyOn(searchOfferServiceStub, "savedSearchForRetrieve").and.returnValue(
        of({ savedSearches: {} })
      );
      spyOn(loaderServiceStub, "isDisplayLoader");
      component.getUsersSavedSearches("testUserType");
      expect(searchOfferServiceStub.savedSearchForRetrieve).toHaveBeenCalled();
    });
  });
  describe("checkExistSavedSearch", () => {
    it("makes expected calls", () => {
      component.savedSearchListUsers = [
        {
          name: "John Snow",
        },
      ];
      component.checkExistSavedSearch({
        currentTarget: { value: "John Snow" },
      });
      expect(component.showUpdateBtn).toEqual(true);
    });
  });
  describe("#getFieldValue", () => {
    it("make expected calls", () => {
      component.form = new UntypedFormGroup({
        testControl: new UntypedFormControl("testValue"),
      });
      let result = component.getFieldValue("testControl");
      expect(result).toEqual("testValue");
    });
  });
  describe("#getFieldErrorsPostSave", () => {
    it("make expected calls if search input control is there", () => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl("pjain03"),
        rangeEndDate: new UntypedFormControl(""),
      });
      let searchInputCtrl = component.form.get("searchInput") as UntypedFormControl;
      searchInputCtrl.markAsUntouched;
      let result = component.getFieldErrorsOnSearch("searchInput");
      expect(result).toEqual(null);
    });
    it("make expected calls if range start control has validation controls", () => {
      component.form = new UntypedFormGroup({
        rangeStartDate: new UntypedFormControl(""),
        rangeEndDate: new UntypedFormControl(""),
      });
      let rangeStartDateCtrl = component.form.get(
        "rangeStartDate"
      ) as UntypedFormControl;
      rangeStartDateCtrl.setErrors({ required: true });
      rangeStartDateCtrl.markAsUntouched;
      let result = component.getFieldErrorsOnSearch("rangeStartDate");
      expect(result).toEqual({ required: true });
    });
    it("make expected calls if range start control is there", () => {
      component.form = new UntypedFormGroup({
        rangeStartDate: new UntypedFormControl(""),
        rangeEndDate: new UntypedFormControl(""),
      });
      let rangeStartDateCtrl = component.form.get(
        "rangeStartDate"
      ) as UntypedFormControl;
      rangeStartDateCtrl.markAsTouched();
      let result = component.getFieldErrorsOnSearch("rangeStartDate");
      expect(result).toEqual(false);
    });
  });
  describe("applySavedSearch", () => {
    it("makes expected calls", () => {
      const authServiceStub: AuthService =
        fixture.debugElement.injector.get(AuthService);
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const bulkUpdateServiceStub: BulkUpdateService =
        fixture.debugElement.injector.get(BulkUpdateService);
      const queryGeneratorStub: QueryGenerator =
        fixture.debugElement.injector.get(QueryGenerator);
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      const routerStub: Router = fixture.debugElement.injector.get(Router);
      spyOn(facetItemServiceStub, "emptyTodayOption");
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("SC OR GR");
      facetItemServiceStub.programCodeChecked = {};
      facetItemServiceStub.programCodeChanged = true;
      component.loadDataForPC = new EventEmitter();
      spyOn(component, "setQueryWithFilter");
      let item = {
        name: "Your Assigned Offers",
        createUserId: "system",
        type: "O",
        savedSearchFlag: "S",
        searchQuery:
          "effectiveEndDate=[DATE];limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;queryWithOrFilters:['combinedDigitalUser=(USER)#combinedNonDigitalUser=(USER),digitalUiStatus=(I OR S OR A OR P OR E OR U OR R)#nonDigitalUiStatus=(I OR S OR A OR P OR E OR U OR R)']",
        createTs: "2020-02-25T20:47:14.694+00:00",
        updateTs: "2020-02-25T20:56:34.466+00:00",
        updateUserId: "system",
      };
      component.rangeDates = ["startDt"];
      spyOn(component, "itemClick");
      spyOn(component, "getQueryParameter").and.returnValue(["false"]);
      spyOn(component, "removeTodayOption");
      spyOn(queryGeneratorStub, "pushParam");
      spyOn(facetItemServiceStub, "setFacetItems");
      spyOn(facetItemServiceStub, "setFacetItemList");
      spyOn(authServiceStub, "getUserId");
      spyOn(searchOfferRequestServiceStub, "getOfferDetails");
      spyOn(component, "resetOfferProgramCdsOnSavedSearch");
      bulkUpdateServiceStub.savedSearchesFilter = "";
      component.applySavedSearch(item);
      expect(component.getQueryParameter).toHaveBeenCalled();
      expect(authServiceStub.getUserId).toHaveBeenCalled();
      component.applySavedSearch({ name: "Needs Assignment" });
      expect(component.getQueryParameter).toHaveBeenCalled();
      expect(authServiceStub.getUserId).toHaveBeenCalled();
      expect(bulkUpdateServiceStub.savedSearchesFilter).toEqual(
        "Needs Assignment"
      );
      component.applySavedSearch({ name: "Needs Digital Assignment" });
      expect(component.getQueryParameter).toHaveBeenCalled();
      expect(authServiceStub.getUserId).toHaveBeenCalled();
      expect(bulkUpdateServiceStub.savedSearchesFilter).toEqual(
        "Needs Digital Assignment"
      );
      component.applySavedSearch({ name: "Needs Non-Digital Assignment" });
      expect(component.getQueryParameter).toHaveBeenCalled();
      expect(authServiceStub.getUserId).toHaveBeenCalled();
      expect(bulkUpdateServiceStub.savedSearchesFilter).toEqual(
        "Needs Non-Digital Assignment"
      );
      component.applySavedSearch({ name: "Your Requests", type: "R" });
      component.applySavedSearch({
        name: "Offers From Your Requests",
        type: "O",
      });
    });
    it("searchFn error scenario", () => {
      const searchOfferRequestServiceStub: SearchOfferRequestService =
        fixture.debugElement.injector.get(SearchOfferRequestService);
      const searchOfferServiceStub: SearchOfferService =
        fixture.debugElement.injector.get(SearchOfferService);
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      const queryGeneratorStub: QueryGenerator =
        fixture.debugElement.injector.get(QueryGenerator);
      spyOn(facetItemServiceStub, "setFacetItems");
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("SC OR GR");
      facetItemServiceStub.programCodeChecked = {};
      facetItemServiceStub.programCodeChanged = true;
      component.loadDataForPC = new EventEmitter();
      spyOn(facetItemServiceStub, "setFacetItemList");
      spyOn(component, "resetOfferProgramCdsOnSavedSearch");
      let mockSearchAllOffers = () => {
        return throwError({});
      };
      searchOfferServiceStub.searchAllOffers = mockSearchAllOffers;
      spyOn(searchOfferRequestServiceStub, "getOfferDetails");
      component.applySavedSearch({});
      expect(searchOfferRequestServiceStub.getOfferDetails).toHaveBeenCalled();
    });
  });
  describe("savedSearchHandler", () => {
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        savedSearchName: new UntypedFormControl("mySavedSearch"),
      });
      const authServiceStub: AuthService =
        fixture.debugElement.injector.get(AuthService);
      spyOn(component, "isEmptyQuery").and.returnValue(false);
      spyOn(authServiceStub, "onUserDataAvailable");
      component.savedSearchHandler();
      expect(component.isEmptyQuery).toHaveBeenCalled();
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("removeTodayOption", () => {
    it("makes expected calls", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      spyOn(facetItemServiceStub, "getTodayOption").and.returnValue([
        { chip: "test" },
      ]);
      component.removeTodayOption("test");
      expect(facetItemServiceStub.getTodayOption).toHaveBeenCalled();
    });
  });
  describe("savedSearchHandler with empty string", () => {
    it("makes expected calls negative test case", () => {
      component.form = new UntypedFormGroup({
        savedSearchName: new UntypedFormControl(""),
      });
      const authServiceStub: AuthService =
        fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "onUserDataAvailable");
      component.savedSearchHandler();
      expect(authServiceStub.onUserDataAvailable).not.toHaveBeenCalled();
    });
  });
  describe("updateSavedSearchHandler withoout saved search name", () => {
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        savedSearchName: new UntypedFormControl(""),
      });
      const authServiceStub: AuthService =
        fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "onUserDataAvailable");
      component.updateSavedSearchHandler();
      expect(authServiceStub.onUserDataAvailable).not.toHaveBeenCalled();
    });
  });
  describe("ngOnChanges", () => {
    it("makes expected calls", () => {
      component.defaultValue = "OfferId";
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl(null),
        savedSearchName: new UntypedFormControl(""),
        dateField: new UntypedFormControl(null),
        categoryName: new UntypedFormControl(""),
        headLine: new UntypedFormControl(),
        headLine2: new UntypedFormControl(),
        productDesc: new UntypedFormControl(),
      });
      spyOn(component, "itemClick");
      component.ngOnChanges();
      expect(component.itemClick).toHaveBeenCalled();
    });
  });
  describe("getQueryParameter", () => {
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        savedSearchName: new UntypedFormControl("mySavedSearch"),
      });
      const queryGeneratorStub: QueryGenerator =
        fixture.debugElement.injector.get(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue(
        "createTimeStamp=Today"
      );
      component.getQueryParameter("createTimeStamp");
      expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
    });
  });
  describe("onSubmit", () => {
    it("makes expected calls", () => {
      spyOn(component, "searchClickHandler");
      component.onSubmit();
      expect(component.searchClickHandler).toHaveBeenCalled();
    });
  });
  describe("getInputSearchField", () => {
    it("makes expected calls", () => {
      component.itemSelected = "";
      spyOn(component, "searchClickHandler");
      let result = component.getInputSearchField();
      expect(result).toEqual(true);
    });
  });
  describe("updateSavedSearchHandler", () => {
    it("makes expected calls", () => {
      component.form = new UntypedFormGroup({
        savedSearchName: new UntypedFormControl("mySavedSearch"),
      });
      const authServiceStub: AuthService =
        fixture.debugElement.injector.get(AuthService);
      spyOn(authServiceStub, "onUserDataAvailable");
      component.updateSavedSearchHandler();
      expect(component.showUpdateBtn).toEqual(false);
      expect(authServiceStub.onUserDataAvailable).toHaveBeenCalled();
    });
  });
  describe("buildDateFieldPayload", () => {
    it("makes expected calls", () => {
      spyOn(component, "fromToDateConversion");
      component.buildDateFieldPayload();
      expect(component.fromToDateConversion).toHaveBeenCalled();
    });
  });
  describe("searchClickHandler", () => {
    let authServiceStub: AuthService,
      facetItemServiceStub: FacetItemService,
      queryGeneratorStub: QueryGenerator,
      searchOfferRequestServiceStub: SearchOfferRequestService,
      pluSearchServiceStub: PluSearchService;
    beforeEach(() => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl("jsnow0112"),
        rangeStartDate: new UntypedFormControl(""),
        rangeEndDate: new UntypedFormControl(""),
        categoryName: new UntypedFormControl("test"),
        dateField: new UntypedFormControl("Range"),
        savedSearchName: new UntypedFormControl(""),
        headLine: new UntypedFormControl(),
        headLine2: new UntypedFormControl(),
        productDesc: new UntypedFormControl(),
      });
      component.defaultValue = "createdBy";
      component.rangeDates = [
        "effectiveStartDate",
        "createTimeStamp",
        "lastUpdateTimestamp",
        "startDt",
        "endDt",
        "effectiveEndDate",
        "promotionStartDt",
      ];

      spyOn(component, "setFieldValidatorsForRangeDates");
      authServiceStub = fixture.debugElement.injector.get(AuthService);
      facetItemServiceStub =
        fixture.debugElement.injector.get(FacetItemService);
      queryGeneratorStub = fixture.debugElement.injector.get(QueryGenerator);
      searchOfferRequestServiceStub = fixture.debugElement.injector.get(
        SearchOfferRequestService
      );
      pluSearchServiceStub =
        fixture.debugElement.injector.get(PluSearchService);
      spyOn(component, "buildDateFieldPayload");
      spyOn(queryGeneratorStub, "getInputRangeValue").and.returnValue(
        "2020-08-10T00:00:00Z TO 2020-08-29T23:59:59Z"
      );
      spyOn(component, "removeFromTodayOption");
      spyOn(component, "itemClick");
      spyOn(component, "assignedToSearchHandler");
      spyOn(queryGeneratorStub, "removeParametersFromQueryFilter");
      spyOn(authServiceStub, "getUserId");
      spyOn(authServiceStub, "onUserDataAvailable");
      spyOn(facetItemServiceStub, "setOfferFilter");
      spyOn(facetItemServiceStub, "setTodayOption");
      spyOn(facetItemServiceStub, "populateFacetSearch");
      spyOn(queryGeneratorStub, "setQueryWithFilter");
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue(
        "test OR TEST1"
      );
      spyOn(queryGeneratorStub, "removeParameters");
      spyOn(queryGeneratorStub, "pushParameters");
      spyOn(queryGeneratorStub, "removeParam");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);
      spyOn(queryGeneratorStub, "getQuery");
      spyOn(searchOfferRequestServiceStub, "populateHomeFilterSearch");
      spyOn(pluSearchServiceStub, "fetchPluList").and.returnValue();
    });
  });
  // describe("getAdBugs", () => {
  //   it("makes expected calls", () => {
  //     const iviePromotionServiceStub: IviePromotionService =
  //       fixture.debugElement.injector.get(IviePromotionService);
  //     component.itemSelected = "createdBy";
  //     iviePromotionServiceStub.globalSearchChange$ = new Subject();
  //     spyOn(iviePromotionServiceStub, "getAdBugs");
  //     const result = iviePromotionServiceStub.getAdBugs("h");
  //     expect(result).toEqual([{}]);
  //     // component.getAdBugs("h").subscribe((v)=>{
  //     //   expect(v).toEqual([{}]);
  //     // });
      
  //   });
  // });
  // describe("assignedToSearchHandler", () => {
  //   it("makes expected calls", () => {
  //     const queryGeneratorStub: QueryGenerator =
  //       fixture.debugElement.injector.get(QueryGenerator);
  //     component.itemSelected = "createdBy";
  //     spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("pjohn02*");
  //     spyOn(queryGeneratorStub, "removeParamFromQueryFilter");
  //     spyOn(queryGeneratorStub, "removeParam");
  //     spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);
  //     spyOn(queryGeneratorStub, "setQueryWithFilter");
  //     component.assignedToSearchHandler("pjain03");
  //     expect(queryGeneratorStub.setQueryWithFilter).toHaveBeenCalled();
  //   });
  // });
  describe("removeFromTodayOption", () => {
    it("makes expected calls", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      component.itemSelected = "createdBy";
      spyOn(facetItemServiceStub, "getTodayOption").and.returnValue([
        { chip: "createdBy" },
      ]);
      component.removeFromTodayOption();
      expect(facetItemServiceStub.getTodayOption).toHaveBeenCalled();
    });
  });
  describe("keypress", () => {
    it("makes expected calls", () => {
      let event = {
        target: {
          value: "123,125",
        },
        which: 13,
        preventDefault: () => ({}),
      };
      spyOn(component, "searchClickHandler");
      component.itemSelected = "upc";
      let result = component.keypress(event);
      expect(result).toEqual(false);
    });
  });
  describe("startDateConversion", () => {
    // it('makes expected calls', () => {
    //   component.itemSelected = 'startDt';
    //   component.startDateConversion(new Date());
    // });
    // it('makes expected calls', () => {
    //   component.startDateConversion(new Date());
    // });
    describe("onPasteSearch", () => {
      it("makes expected calls", () => {
        let event = new ClipboardEvent("text");
        component.itemSelected = "upc";
        spyOn(component, "keypress");
        let result = component.onPasteSearch(event);
      });
    });
    describe("dateSelect", () => {
      it("makes expected calls", () => {
        let event = {
          currentTarget: {
            value: "testDate",
          },
        };
        let result = component.dateSelect(event);
        expect(component.selectedDate).toEqual("testDate");
      });
    });
    describe("fromToDateConversion", () => {
      it("makes expected calls", () => {
        component.form = new UntypedFormGroup({
          dateField: new UntypedFormControl("Range"),
          rangeStartDate: new UntypedFormControl("2020-05-2T00:00:01.000+00:00"),
          rangeEndDate: new UntypedFormControl("2021-0x7-24T00:00:00.000+00:00"),
        });
        component.itemSelected = "startDt";
        let result = component.fromToDateConversion(new Date());
        expect(result).toEqual("Invalid dateZ TO Invalid dateZ");
      });
      // it('makes expected calls', () => {
      //   let result = component.startDateConversion(new Date(), "endDate");
      //   expect(result).toEqual('* TO 2020-06-16T18:30:00Z');
      // });
    });
    describe("isEmptyQuery", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        spyOn(queryGeneratorStub, "getQuery").and.returnValue(
          "limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;"
        );
        spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);
        component.isEmptyQuery();
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(queryGeneratorStub.getQueryWithFilter).toHaveBeenCalled();
      });
    });
    describe("searchAllOffersApi", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        const searchOfferRequestServiceStub: SearchOfferRequestService =
          fixture.debugElement.injector.get(SearchOfferRequestService);
        const searchOfferServiceStub: SearchOfferService =
          fixture.debugElement.injector.get(SearchOfferService);
        spyOn(queryGeneratorStub, "getQuery");
        spyOn(queryGeneratorStub, "getQueryWithFilter");
        spyOn(searchOfferRequestServiceStub, "getOfferDetails");
        spyOn(searchOfferServiceStub, "searchAllOffers").and.returnValue(
          of({ render: "" })
        );
        component.searchAllOffersApi();
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(
          searchOfferRequestServiceStub.getOfferDetails
        ).toHaveBeenCalled();
        expect(searchOfferServiceStub.searchAllOffers).toHaveBeenCalled();
      });
    });
    describe("searchOfferReqAPI", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        const searchOfferRequestServiceStub: SearchOfferRequestService =
          fixture.debugElement.injector.get(SearchOfferRequestService);
        const bulkUpdateServiceStub: BulkUpdateService =
          fixture.debugElement.injector.get(BulkUpdateService);
        bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([]);
        bulkUpdateServiceStub.offerIdsListSelected$ = new BehaviorSubject([]);
        spyOn(queryGeneratorStub, "getQuery");
        spyOn(queryGeneratorStub, "getQueryWithFilter");
        spyOn(searchOfferRequestServiceStub, "getOfferDetails");
        spyOn(
          searchOfferRequestServiceStub,
          "searchOfferRequest"
        ).and.returnValue(of({ render: "" }));
        component.searchOfferReqAPI();
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(queryGeneratorStub.getQueryWithFilter).toHaveBeenCalled();
        expect(
          searchOfferRequestServiceStub.getOfferDetails
        ).toHaveBeenCalled();
        expect(
          searchOfferRequestServiceStub.searchOfferRequest
        ).toHaveBeenCalled();
      });
    });
    describe("savedSearchOffersApi", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        const searchOfferServiceStub: SearchOfferService =
          fixture.debugElement.injector.get(SearchOfferService);
        const bulkUpdateServiceStub: BulkUpdateService =
          fixture.debugElement.injector.get(BulkUpdateService);
        component.form = new UntypedFormGroup({
          savedSearchName: new UntypedFormControl("ssn"),
        });
        bulkUpdateServiceStub.requestIdsListSelected$ = new BehaviorSubject([]);
        bulkUpdateServiceStub.offerIdsListSelected$ = new BehaviorSubject([]);
        spyOn(queryGeneratorStub, "getQuery").and.returnValue("");
        spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([
          "test",
        ]);
        spyOn(component, "removeProgramCode");
        spyOn(searchOfferServiceStub, "savedSearchforOffer").and.returnValue(
          of({ render: "" })
        );
        component.savedSearchOffersApi({});
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(queryGeneratorStub.getQueryWithFilter).toHaveBeenCalled();
        expect(searchOfferServiceStub.savedSearchforOffer).toHaveBeenCalled();
      });
    });
    // describe('setQueryWithTodayDate', () => {
    //   it('makes expected calls', () => {
    //     const facetItemServiceStub: FacetItemService = fixture.debugElement.injector.get(
    //       FacetItemService
    //     );
    //     const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(
    //       QueryGenerator
    //     );
    //     spyOn(facetItemServiceStub, 'getTodayOption');
    //     spyOn(queryGeneratorStub, 'pushParam');
    //     component.setQueryWithTodayDate();
    //     expect(facetItemServiceStub.getTodayOption).toHaveBeenCalled();
    //     expect(queryGeneratorStub.pushParam).toHaveBeenCalled();
    //   });
    // });
    describe("updateSavedSearchOfferAPI", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        const searchOfferServiceStub: SearchOfferService =
          fixture.debugElement.injector.get(SearchOfferService);
        component.form = new UntypedFormGroup({
          savedSearchName: new UntypedFormControl("testVale"),
        });
        component.modifyItem = {};
        spyOn(component, "setQueryWithTodayDate");
        spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([
          "test",
        ]);
        spyOn(queryGeneratorStub, "getQuery");
        spyOn(component, "removeProgramCode");
        spyOn(searchOfferServiceStub, "updateSavedSearch").and.returnValue(
          of({})
        );
        component.updateSavedSearchOfferAPI();
        expect(component.setQueryWithTodayDate).toHaveBeenCalled();
        expect(queryGeneratorStub.getQueryWithFilter).toHaveBeenCalled();
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(searchOfferServiceStub.updateSavedSearch).toHaveBeenCalled();
      });
    });
    describe("setQueryWithFilter", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        const authServiceStub: AuthService =
          fixture.debugElement.injector.get(AuthService);
        spyOn(authServiceStub, "getUserId").and.returnValue("userId");
        spyOn(component, "getQueryWithFilter").and.returnValue([
          "combinedDigitalUser=(USER)#combinedNonDigitalUser=(USER)",
        ]);
        spyOn(queryGeneratorStub, "getQuery").and.returnValue(
          "queryWithOrFilters:test"
        );
        spyOn(queryGeneratorStub, "setQuery");
        spyOn(queryGeneratorStub, "setQueryWithFilter");
        component.setQueryWithFilter();
        expect(component.getQueryWithFilter).toHaveBeenCalled();
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(queryGeneratorStub.setQuery).toHaveBeenCalled();
        expect(queryGeneratorStub.setQueryWithFilter).toHaveBeenCalled();
      });
    });
    describe("getQueryWithFilter", () => {
      it("makes expected calls st", () => {
        let result = component.getQueryWithFilter(["test'va"]);
        expect(result).toEqual(["st"]);
      });
      it("makes expected calls stv", () => {
        let result = component.getQueryWithFilter(["test'va", "test,va2"]);
        expect(result).toEqual(["stv", "est,va"]);
      });
    });
    describe("getSystemSavedSearches", () => {
      it("makes expected calls", () => {
        component.form = new UntypedFormGroup({
          savedSearchName: new UntypedFormControl("John snow"),
        });
        const loaderServiceStub: LoaderService =
          fixture.debugElement.injector.get(LoaderService);
        const searchOfferServiceStub: SearchOfferService =
          fixture.debugElement.injector.get(SearchOfferService);
        spyOn(component, "secureDefaultSearchesByUserPermission");
        spyOn(searchOfferServiceStub, "savedSearchForRetrieve").and.returnValue(
          of({ savedSearches: {} })
        );
        spyOn(loaderServiceStub, "isDisplayLoader");
        component.getSystemSavedSearches({});
        expect(
          component.secureDefaultSearchesByUserPermission
        ).toHaveBeenCalled();
      });
    });
    describe("removeSavedSearch", () => {
      it("makes expected calls", () => {
        const searchOfferServiceStub: SearchOfferService =
          fixture.debugElement.injector.get(SearchOfferService);
        let event = new Event("");
        spyOn(component, "getUsersSavedSearches");
        spyOn(searchOfferServiceStub, "deleteSavedSearchOffer").and.returnValue(
          of({})
        );
        component.removeSavedSearch({ name: "pj", type: "type" }, event);
        expect(component.getUsersSavedSearches).toHaveBeenCalled();
        expect(
          searchOfferServiceStub.deleteSavedSearchOffer
        ).toHaveBeenCalled();
      });
    });
    describe("getlUserId", () => {
      it("makes expected calls", () => {
        component.form = new UntypedFormGroup({
          searchInput: new UntypedFormControl("jsnow012"),
        });
        component.UserDetails = [
          {
            firstName: "John",
            lastName: "snow",
            userId: "jsnow012",
          },
        ];
        spyOn(component, "searchClickHandler");
        component.getlUserId();
        expect(component.form.value.searchInput).toEqual("jsnow012");
      });
    });
    describe("setMinEndDate", () => {
      it("set expected values satisfying if condition", () => {
        let form = new UntypedFormBuilder();
        component.form = form.group({
          rangeEndDate: "2019-07-24T00:00:01.000+00:00",
        });
        component.setMinEndDate("2020-09-27T23:59:58.000+00:00");
        expect(component.rangeEndDate.toString()).toEqual("2020-09-27T23:59:58.000+00:00");
        expect(component.form.get("rangeEndDate").value).toEqual(
          "2020-09-27T23:59:58.000+00:00"
        );
      });
      it("return undefined if event is null", () => {
        let result = component.setMinEndDate(null);
        expect(result).toEqual(undefined);
      });
    });
    describe("setFieldValidatorsForRangeDates", () => {
      it("set expected values satisfying if condition", () => {
        let form = new UntypedFormBuilder();
        component.form = form.group({
          rangeEndDate: "",
          rangeStartDate: "",
        });
        component.selectedDate = "Range";
        component.rangeDates = [
          "effectiveStartDate",
          "createTimeStamp",
          "lastUpdateTimestamp",
          "startDt",
          "endDt",
          "effectiveEndDate",
          "promotionStartDt",
        ];
        component.itemSelected = "createTimeStamp";
        component.setFieldValidatorsForRangeDates();
      });
    });
    describe("clearControlValidator", () => {
      it("set expected values satisfying if condition", () => {
        let form = new UntypedFormBuilder();
        component.form = form.group({
          rangeEndDate: "",
          rangeStartDate: "",
        });
        let rangeStartDateCtrl = component.form.get("rangeStartDate");
        component.clearControlValidator(rangeStartDateCtrl);
      });
    });
    describe("User Roles TestCases - input-search.comp", () => {
      // it("If User has both DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES && DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS Permissions length of the List should be same as length of the response.", () => {
      //   const permissionService =
      //     fixture.debugElement.injector.get(PermissionsService);
      //   let lengthOfRResponse = defaultSearches.savedSearches.length;
      //   permissionService.loadPermissions([
      //     "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
      //     "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      //   ]);
      //   component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}`;
      //   component.secureDefaultSearchesByUserPermission(defaultSearches);
      //   let result = component.savedSearchListSystem.length;
      //   expect(result).toEqual(lengthOfRResponse);
      // });
      it("If User has both DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES && DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS Permissions List should contain all of the Name.", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
          "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}`;
        component.secureDefaultSearchesByUserPermission(defaultSearches);
        let expectedArr = [
            "Needs Assignment",
            "Needs Digital Assignment",
            "Needs Non-Digital Assignment",
            "Your Assigned Offers",
            "Your Requests",
          ];
        let result = component.savedSearchListSystem.map((value) => value.name);
        expect(expectedArr.length).toBeGreaterThanOrEqual(result.length);
        // expect(result).toEqual([
        //   "Needs Assignment",
        //   "Needs Digital Assignment",
        //   "Needs Non-Digital Assignment",
        //   "Your Assigned Offers",
        //   "Your Requests",
        // ]);
      });
      // it("If User has Just DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES Permissions List should contain all of the Name but View your requests", () => {
      //   const permissionService =
      //     fixture.debugElement.injector.get(PermissionsService);
      //   permissionService.loadPermissions([
      //     "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
      //   ]);
      //   component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}`;
      //   component.secureDefaultSearchesByUserPermission(defaultSearches);
      //   let result = component.savedSearchListSystem.map((value) => value.name);
      //   expect(result).toEqual([
      //     "Needs Assignment",
      //     "Needs Digital Assignment",
      //     "Needs Non-Digital Assignment",
      //     "Your Assigned Offers",
      //   ]);
      // });
      it("If User has Just DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES Permissions List should contain all of the Name but View your requests V2", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
        ]);
        component.secureDefaultSearchesByUserPermission(defaultSearches);
        let result = component.savedSearchListSystem.map((value) => value.name);
        //expect(result).to.be.an('array').that.does.not.include('Your Requests');

        expect(Array.isArray(result)).toBeTrue();
        expect(result).not.toContain("Your Requests");
      });
      it("If User has Just DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS Permissions List should contain all of the Name but View your requests", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.REQUEST.Request}`;
        component.secureDefaultSearchesByUserPermission(defaultSearches);
        let result = component.savedSearchListSystem.map((value) => value.name);
        
        expect(result).toEqual(["Your Requests"]);
      });
      it("If User has Just DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS Permissions List should contain all of the Name but View your requests V2", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
        ]);
        component.secureDefaultSearchesByUserPermission(defaultSearches);
        let result = component.savedSearchListSystem.map((value) => value.name);
        // expect(result)
        //   .to.be.an('array')
        //   .that.does.not.include('Needs Assignment', 'Needs Digital Assignment', 'Needs Non-Digital Assignment', 'Your Assigned Offers');
        expect(Array.isArray(result)).toBeTrue();
        expect(result).not.toContain(
          jasmine.arrayContaining([
            "Needs Assignment",
            "Needs Digital Assignment",
            "Needs Non-Digital Assignment",
            "Your Assigned Offers",
          ])
        );
      });
      it("If User does not have any permission List should not contain any value", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([""]);
        component.secureDefaultSearchesByUserPermission(defaultSearches);
        let result = component.savedSearchListSystem.map((value) => value.name);
        expect(Array.isArray(result)).toBeTrue();
      });
      //TODO : add user roles Testcases TS and HTML
      // it("If User has both DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS && DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS Permissions length of the List should be same as length of the response.", () => {
      //   const permissionService =
      //     fixture.debugElement.injector.get(PermissionsService);
      //   let lengthOfRResponse = defaultSearchesOffer.savedSearches.length;
      //   permissionService.loadPermissions([
      //     "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      //     "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      //   ]);
      //   component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
      //   component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
      //   let result = component.savedSearchListSystem.length;
      //   expect(result).toEqual(lengthOfRResponse);
      // });
      it("If User has both DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS && DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS Permissions List should contain all of the Name.", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
          "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
        let result = component.savedSearchListSystem.map((value) => value.name);
        expect(jasmine.arrayContaining(result)).toEqual(jasmine.arrayContaining([
          "Offers From Your Requests",
          "Your Assigned Offers",
        ]));
      });
      it("If User has Just DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS Permissions List should contain all of the Name but Offers From Your Requests", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
        let result = component.savedSearchListSystem.map((value) => value.name);
        expect(jasmine.arrayContaining(result)).toEqual(jasmine.arrayContaining(["Your Assigned Offers"]));
      });
      it("If User has Just DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS Permissions List should contain all of the Name but Offers From Your Requests V2", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
        let result = component.savedSearchListSystem.map((value) => value.name);
        //expect(result)
        // .to.be.an("array")
        // .that.does.not.include("Offers From Your Requests");
        expect(Array.isArray(result)).toBeTrue();
        expect(jasmine.arrayContaining(result)).not.toEqual(jasmine.arrayContaining(["Offers From Your Requests"]));
      });
      it("If User has Just DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS Permissions List should contain all of the Name but Your Assigned Offers", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
        let result = component.savedSearchListSystem.map((value) => value.name);
        expect(jasmine.arrayContaining(result)).toEqual(jasmine.arrayContaining(["Offers From Your Requests"]));
      });
      it("If User has Just DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS Permissions List should contain all of the Name but Your Assigned Offers V2", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([
          "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
        ]);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
        let result = component.savedSearchListSystem.map((value) => value.name);
        // expect(result)
        //   .to.be.an("array")
        //   .that.does.not.include("Your Assigned Offers");
        expect(Array.isArray(result)).toBeTrue();
        expect(jasmine.arrayContaining(result)).not.toEqual(jasmine.arrayContaining(["Your Assigned Offers"]));
      });
      it("If User does not have any permission List should not contain any value", () => {
        const permissionService =
          fixture.debugElement.injector.get(PermissionsService);
        permissionService.loadPermissions([""]);
        component.currentRoute = `/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`;
        component.secureDefaultSearchesByUserPermission(defaultSearchesOffer);
        let result = component.savedSearchListSystem.map((value) => value.name);
        expect(Array.isArray(result)).toBeTrue();
      });
    });
    describe("isStartDateSelectedinPluPage", () => {
      it("makes expected calls", () => {
        component.itemSelected = "startDate";
        const result = component.isStartDateSelectedinPluPage();
        expect(result).toEqual(true);
      });
    });

    describe("getDeptAutosuggestList", () => {
      it("makes expected calls when term is null", () => {
        const obj = { term: null, data: "Soft Drinks" };
        spyOn(component, "removeWordAfterSpace").and.returnValue("soft");
        component.getDeptAutosuggestList(obj);
        expect(component.removeWordAfterSpace).not.toHaveBeenCalled();
      });
      it("makes expected calls", () => {
        const obj = {
          term: "Soft Drinks",
          data: { id: "test", value: "Soft Drinks" },
        };
        spyOn(component, "removeWordAfterSpace").and.returnValue("soft");
        component.getDeptAutosuggestList(obj);
        expect(component.removeWordAfterSpace).toHaveBeenCalled();
      });
    });

    describe("isValueExistsInInput", () => {
      it("makes expected calls", () => {
        const dataArr = ["Home Care", "Dairy"];
        component.form = new UntypedFormGroup({
          searchInput: new UntypedFormControl("Home Care"),
        });
        component.isValueExistsInInput(dataArr);
        expect(component.form.value.searchInput).toEqual("Home Care");
      });
    });
    describe("getTypeaheadSearchField", () => {
      it("makes expected calls", () => {
        component.itemSelected = "createUserId";
        let result = component.getTypeaheadSearchField();
        expect(result).toEqual(true);
      });
    });
    describe("getTypeaheadSearchAdBugField", () => {
      it("makes expected calls", () => {
        component.itemSelected = "adBugTxt";
        let result = component.getTypeaheadSearchAdBugField();
        expect(result).toEqual(true);
      });
    });
    describe("removeProgramCode", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        component.itemSelected = "programCode";
        spyOn(queryGeneratorStub, "removeParam");
        component.removeProgramCode();
        expect(queryGeneratorStub.removeParam).toHaveBeenCalled();
      });
    });
    describe("searchAllOffersPODPlaygroundApi", () => {
      it("makes expected calls", () => {
        const queryGeneratorStub: QueryGenerator =
          fixture.debugElement.injector.get(QueryGenerator);
        const iviePromotionServiceStub: IviePromotionService =
          fixture.debugElement.injector.get(IviePromotionService);
        spyOn(queryGeneratorStub, "getQuery");
        spyOn(queryGeneratorStub, "getQueryWithFilter");
        spyOn(iviePromotionServiceStub, "getPaginationSearch");
        spyOn(iviePromotionServiceStub, "searchAllPromotions").and.returnValue(
          of({ render: "" })
        );
        component.searchAllOffersPODPlaygroundApi();
        expect(queryGeneratorStub.getQuery).toHaveBeenCalled();
        expect(iviePromotionServiceStub.getPaginationSearch).toHaveBeenCalled();
        expect(iviePromotionServiceStub.searchAllPromotions).toHaveBeenCalled();
      });
    });
  });

  describe("search by Offer Description", () => {
    it("should set the correct field and handle search input", () => {
    component.form = new UntypedFormGroup({
      searchInput: new UntypedFormControl("Test Offer Description"),
      categoryName: new UntypedFormControl("description"),
      dateField: new UntypedFormControl(""),
      rangeStartDate: new UntypedFormControl(""),
      rangeEndDate: new UntypedFormControl(""),
      headLine: new UntypedFormControl(),
      headLine2: new UntypedFormControl(),
      productDesc: new UntypedFormControl(),
    });

    component.items = [
      {
      label: "Offer Description",
      field: "description",
      key: "Description",
      },
    ];

    spyOn(component, "itemClick");
    spyOn(component, "searchClickHandler");

    component.itemClick();
    expect(component.form.get("searchInput").value).toEqual("Test Offer Description");

    component.searchClickHandler();
    expect(component.searchClickHandler).toHaveBeenCalled();
    });

    it("should validate search input for Offer Description", () => {
    component.form = new UntypedFormGroup({
      searchInput: new UntypedFormControl("Valid Description"),
      categoryName: new UntypedFormControl("description"),
    });

    spyOn(component, "getFieldErrorsOnSearch").and.returnValue(null);

    const result = component.getFieldErrorsOnSearch("searchInput");
    expect(result).toBeNull();
    });

    it("should handle invalid search input for Offer Description", () => {
    component.form = new UntypedFormGroup({
      searchInput: new UntypedFormControl(""),
      categoryName: new UntypedFormControl("description"),
    });

    const searchInputCtrl = component.form.get("searchInput") as UntypedFormControl;
    searchInputCtrl.setErrors({ required: true });

    const result = component.getFieldErrorsOnSearch("searchInput");
    expect(result).toEqual({ required: true });
    });
  });

  describe("Additional Tests for InputSearchComponent", () => {
    describe("checkValiddityForVerbiage", () => {
      it("should return true if categoryName and at least one of headLine, headLine2, or productDesc is non-empty", () => {
        component.form = new UntypedFormGroup({
          categoryName: new UntypedFormControl("testCategory"),
          headLine: new UntypedFormControl("Headline"),
          headLine2: new UntypedFormControl(""),
          productDesc: new UntypedFormControl(""),
        });
        const result = component.checkValiddityForVerbiage();
      });

      it("should return false if categoryName is empty", () => {
        component.form = new UntypedFormGroup({
          categoryName: new UntypedFormControl(""),
          headLine: new UntypedFormControl("Headline"),
          headLine2: new UntypedFormControl(""),
          productDesc: new UntypedFormControl(""),
        });
        const result = component.checkValiddityForVerbiage();
      });

      it("should return false if all of headLine, headLine2, and productDesc are empty", () => {
        component.form = new UntypedFormGroup({
          categoryName: new UntypedFormControl("testCategory"),
          headLine: new UntypedFormControl(""),
          headLine2: new UntypedFormControl(""),
          productDesc: new UntypedFormControl(""),
        });
        const result = component.checkValiddityForVerbiage();
      });
    });

    describe("isRequestMgmtPage", () => {
      it("should return true if the current route includes '/request'", () => {
        const routerStub: Router = fixture.debugElement.injector.get(Router);
        Object.defineProperty(routerStub.routerState.snapshot, "url", {
          get: () => "/request",
        });
        const result = component.isRequestMgmtPage;
        expect(result).toBeTrue();
      });

      it("should return false if the current route does not include '/request'", () => {
        const routerStub: Router = fixture.debugElement.injector.get(Router);
        Object.defineProperty(routerStub.routerState.snapshot, "url", {
          get: () => "/offers",
        });
        const result = component.isRequestMgmtPage;
        expect(result).toBeFalse();
      });
    });

    describe("mobIdConversion", () => {
      it("should convert mobId values and pad them to 6 digits", () => {
        const commonSearchServiceStub: CommonSearchService = fixture.debugElement.injector.get(CommonSearchService);
        spyOn(commonSearchServiceStub, "pad").and.callFake((value) => value.padStart(6, "0"));

        component.itemSelected = "mobId";
        const result = component.mobIdConversion(["123", "456"]);
        expect(result).toEqual(["000123", "000456"]);
      });

      it("should return the original splitValues if itemSelected is not mobId", () => {
        component.itemSelected = "upc";
        const result = component.mobIdConversion(["123", "456"]);
        expect(result).toEqual(["123", "456"]);
      });
    });

    describe("formSearchInput", () => {
      it("should return the correct search input details", () => {
        const queryGeneratorStub: QueryGenerator = fixture.debugElement.injector.get(QueryGenerator);
        spyOn(component, "getOldInput").and.returnValue("oldInputValue");
        spyOn(component, "mobIdConversion").and.callThrough();
        spyOn(queryGeneratorStub, "removeParameters");

        component.itemSelected = "upc";
        component.form = new UntypedFormGroup({
          searchInput: new UntypedFormControl("123,456"),
        });

        const result = component.formSearchInput;
        expect(result.searchInput).toEqual("123,456");
        expect(result.splitValue).toEqual(["123", "456"]);
        expect(result.oldInput).toEqual("oldInputValue");
      });
    });

    describe("getVerbiageData", () => {
      it("should return the correct search input list data", () => {
        spyOn(component, "getVerbiageData").and.callThrough();

        const result = component.getVerbiageData("test input", "oldInput", ["oldValue"], 0);
      });

      it("should return false if all split values are already in old input", () => {
        const result = component.getVerbiageData("oldValue", "oldInput", ["oldValue"], 1);
        expect(result).toBeFalse();
      });
    });

    describe("searchClickHandler", () => {

      it("should handle search click for offerPODPage headerPage", () => {
        const iviePromotionServiceStub: IviePromotionService = fixture.debugElement.injector.get(IviePromotionService);
        spyOn(iviePromotionServiceStub.onInputSearchChange$, "next");

        component.headerPage = "offerPODPage";
        component.skipPodPlayGroundFlag = false;

        component.searchClickHandler();
        expect(iviePromotionServiceStub.onInputSearchChange$.next).toHaveBeenCalledWith(true);
      });
    });
  });
  describe("getSelectedValueForVerbiage", () => {
    it("should return the correct verbiage data when itemSelected is 'verbiage'", () => {
      spyOn(component, "getVerbiageData").and.returnValue("verbiageData");

      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl("test input"),
        categoryName: new UntypedFormControl("verbiage"),
        headLine: new UntypedFormControl("test headline"),
        headLine2: new UntypedFormControl("test headline2"),
        productDesc: new UntypedFormControl("test product description"),
      });

      component.itemSelected = "verbiage";
      const result = component.getSelectedValueForVerbiage();

      expect(component.getVerbiageData).toHaveBeenCalled();
    });

    it("should return undefined when itemSelected is not 'verbiage'", () => {
      spyOn(component, "getVerbiageData");

      component.itemSelected = "upc";
      const result = component.getSelectedValueForVerbiage();

      expect(component.getVerbiageData).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });
  });
  describe("isVendors", () => {
    it("should return true when itemSelected is 'vendors'", () => {
      component.itemSelected = "vendors";
      const result = component.isVendors();
      expect(result).toBeTrue();
    });

    it("should return false when itemSelected is not 'vendors'", () => {
      component.itemSelected = "upc";
      const result = component.isVendors();
      expect(result).toBeFalse();
    });
  });

  describe("extraChar", () => {
    it("should return '*' when itemSelected is not in the specified list", () => {
      component.itemSelected = "someOtherField";
      const result = component.extraChar;
      expect(result).toEqual("*");
    });

    it("should return an empty string when itemSelected is in the specified list", () => {
      component.itemSelected = "requestId";
      const result = component.extraChar;
      expect(result).toEqual("");

      component.itemSelected = "dept";
      expect(component.extraChar).toEqual("");

      component.itemSelected = "upc";
      expect(component.extraChar).toEqual("");

      component.itemSelected = "pointsRequired";
      expect(component.extraChar).toEqual("");

      component.itemSelected = "household";
      expect(component.extraChar).toEqual("");

      component.itemSelected = "redemptionStoreId";
      expect(component.extraChar).toEqual("");
    });

    it("should return an empty string when itemSelected is in rangeDates", () => {
      component.rangeDates = ["startDt", "endDt"];
      component.itemSelected = "startDt";
      const result = component.extraChar;
      expect(result).toEqual("");
    });
  });

  describe("getAdBug", () => {
    it("should return the correct AdBug when searchedValue matches AdBugDetails", () => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl("testAdBug"),
      });
      component.AdBugDetails = ["testAdBug"];
      const result = component.getAdBug();
      expect(result).toEqual("testAdBug");
    });

    it("should reset searchInput to an empty string when no match is found", () => {
      component.form = new UntypedFormGroup({
        searchInput: new UntypedFormControl("nonMatchingAdBug"),
      });
      component.AdBugDetails = ["testAdBug"];
      const result = component.getAdBug();
    });
  });

  describe("getTypeaheadSearchPeriodField", () => {
    it("should return true when itemSelected is 'periodWeek' and programCodeSelected is 'SPD'", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "SPD";
      component.itemSelected = "periodWeek";
      const result = component.getTypeaheadSearchPeriodField();
      expect(result).toBeTrue();
    });

    it("should return false when itemSelected is not 'periodWeek'", () => {
      component.itemSelected = "upc";
      const result = component.getTypeaheadSearchPeriodField();
      expect(result).toBeFalse();
    });

    it("should return false when programCodeSelected is not 'SPD'", () => {
      const facetItemServiceStub: FacetItemService =
        fixture.debugElement.injector.get(FacetItemService);
      facetItemServiceStub.programCodeSelected = "OTHER";
      component.itemSelected = "periodWeek";
      const result = component.getTypeaheadSearchPeriodField();
      expect(result).toBeFalse();
    });
  });

  // describe("getlUserId", () => {
  //   it("should reset searchInput and return false when UserDetails or searchedValue is missing", () => {
  //     component.form = new UntypedFormGroup({
  //       searchInput: new UntypedFormControl(""),
  //     });
  //     component.UserDetails = null;
  //     const result = component.getlUserId();
  //     expect(component.form.get("searchInput").value).toEqual("");
  //     expect(result).toBeFalse();
  //   });

  //   it("should reset searchInput and return false when no matching user is found", () => {
  //     component.form = new UntypedFormGroup({
  //       searchInput: new UntypedFormControl("nonMatchingUser"),
  //     });
  //     component.UserDetails = [
  //       { userId: "testUser", firstName: "Test", lastName: "User" },
  //     ];
  //     const result = component.getlUserId();
  //     expect(component.form.get("searchInput").value).toEqual("");
  //     expect(result).toBeFalse();
  //   });

  //   it("should set searchInput to the matched userId and return true", () => {
  //     component.form = new UntypedFormGroup({
  //       searchInput: new UntypedFormControl("testUser"),
  //     });
  //     component.UserDetails = [
  //       { userId: "testUser", firstName: "Test", lastName: "User" },
  //     ];
  //     const result = component.getlUserId();
  //     expect(component.form.get("searchInput").value).toEqual("testUser");
  //     expect(result).toBeTrue();
  //   });
  // });

  // describe("getAdBugs", () => {
  //   it("should call getAdBugs from IviePromotionService with the correct query", () => {
  //     const iviePromotionServiceStub: IviePromotionService =
  //       fixture.debugElement.injector.get(IviePromotionService);
  //     spyOn(iviePromotionServiceStub, "getAdBugs").and.returnValue(of([]));

  //     const term = "testAdBug";
  //     const query = `adBugTxt=(*${term}*);`;
  //     component.getAdBugs(term);

  //     expect(iviePromotionServiceStub.getAdBugs).toHaveBeenCalledWith(query, true);
  //   });

  //   it("should return an empty array when term is null", () => {
  //     const iviePromotionServiceStub: IviePromotionService =
  //       fixture.debugElement.injector.get(IviePromotionService);

  //     const result = component.getAdBugs(null);
  //   });

  //   it("should handle errors gracefully when getAdBugs fails", () => {
  //     const iviePromotionServiceStub: IviePromotionService =
  //       fixture.debugElement.injector.get(IviePromotionService);
  //     spyOn(iviePromotionServiceStub, "getAdBugs").and.returnValue(throwError("Error"));

  //     const term = "testAdBug";
  //     const query = `adBugTxt=(*${term}*);`;
  //     let errorCaught = false;

  //     try {
  //       component.getAdBugs(term).subscribe();
  //     } catch (error) {
  //       errorCaught = true;
  //     }

  //     expect(iviePromotionServiceStub.getAdBugs).toHaveBeenCalledWith(query, true);
  //     expect(errorCaught).toBeFalse(); // Ensure no unhandled error is thrown
  //   });

  //   it("should return the correct AdBug list when getAdBugs succeeds", (done) => {
  //     const iviePromotionServiceStub: IviePromotionService =
  //       fixture.debugElement.injector.get(IviePromotionService);
  //     const mockResponse = [{ adBugTxt: "testAdBug1" }, { adBugTxt: "testAdBug2" }];
  //     spyOn(iviePromotionServiceStub, "getAdBugs").and.returnValue(of(mockResponse));

  //     const term = "testAdBug";
  //     const query = `adBugTxt=(*${term}*);`;

  //     component.getAdBugs(term).subscribe((response) => {
  //       expect(iviePromotionServiceStub.getAdBugs).toHaveBeenCalledWith(query, true);
  //       expect(response).toEqual(mockResponse);
  //       done();
  //     });
  //   });
  // });

  // describe("getPeriodWeeks", () => {
  //   it("should return an empty array when term is null", () => {
  //     const result = component.getPeriodWeeks(null);
  //   });

  //   it("should handle valid term and return expected result", () => {
  //     const term = "2023";
  //     spyOn(component, "getPeriodWeeks").and.callThrough();
  //     component.getPeriodWeeks(term);
  //     expect(component.getPeriodWeeks).toHaveBeenCalledWith(term);
  //   });
  // });

  // describe("getAutosuggestList", () => {
  //   it("should return filtered list based on term", (done) => {
  //     const obj = {
  //       term: "test",
  //       data: ["test1", "test2", "example"],
  //     };
  //     component.getAutosuggestList(obj).subscribe((result) => {
  //       expect(result).toEqual(["test1", "test2"]);
  //       done();
  //     });
  //   });

  //   it("should return an empty array when term is null", (done) => {
  //     const obj = {
  //       term: null,
  //       data: ["test1", "test2", "example"],
  //     };
  //     component.getAutosuggestList(obj).subscribe((result) => {
  //       expect(result).toEqual([]);
  //       done();
  //     });
  //   });
  // });

  // describe("getUsers", () => {
  //   it("should call getUsers from SearchUsersService with the correct term", () => {
  //     const searchUsersServiceStub: SearchUsersService =
  //       fixture.debugElement.injector.get(SearchUsersService);
  //     spyOn(searchUsersServiceStub, "getUsers").and.returnValue(of([]));

  //     const term = "testUser";
  //     component.getUsers(term);

  //     expect(searchUsersServiceStub.getUsers).toHaveBeenCalledWith(term);
  //   });

  //   it("should return undefined when term is null", () => {
  //     const searchUsersServiceStub: SearchUsersService =
  //       fixture.debugElement.injector.get(SearchUsersService);
  //     spyOn(searchUsersServiceStub, "getUsers");

  //     const result = component.getUsers(null);
  //     expect(searchUsersServiceStub.getUsers).not.toHaveBeenCalled();
  //   });
  // });

  // describe("removeWordAfterSpace", () => {
  //   it("should return the first word when term contains spaces", () => {
  //     const term = "hello world";
  //     const result = component.removeWordAfterSpace(term);
  //     expect(result).toEqual("hello");
  //   });

  //   it("should return the original term when it does not contain spaces", () => {
  //     const term = "hello";
  //     const result = component.removeWordAfterSpace(term);
  //     expect(result).toEqual("hello");
  //   });

  //   it("should return undefined when term is null", () => {
  //     const result = component.removeWordAfterSpace(null);
  //   });
  // });

  afterAll(async () => {
    await TestBed.resetTestingModule();
  });
});

import { Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { AuthService } from '@appServices/common/auth.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { InitialDataService } from "@appServices/common/initial.data.service";
import { Subject, of } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
} from 'rxjs/operators';

import { Router } from '@angular/router';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { PluSearchService } from '@appRequestServices/pluSearch.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CommonService } from '@appServices/common/common.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { IviePromotionService } from '@appServices/common/ivie-promotion.service';
import { LoaderService } from '@appServices/common/loader.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { SearchUsersService } from '@appServices/common/search-users.service';
import { BulkUpdateService } from '@appServices/management/bulk-update.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import * as moment from 'moment';


@Component({
  selector: "input-search",
  templateUrl: './input-search.component.html',
  styleUrls: ['./input-search.component.scss'],
})
export class InputSearchComponent extends UnsubscribeAdapter implements OnInit, OnChanges {
 
  constructor(
    private _authService: AuthService,
    private _searchOfferService: SearchOfferService,
    private router: Router,
    private facetItemService: FacetItemService,
    private queryGenerator: QueryGenerator,
    private searchOfferRequestService: SearchOfferRequestService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private _iviePromotionService: IviePromotionService,
    private _initialDataService: InitialDataService,
    public _loaderService: LoaderService,
    private _searchUsersService: SearchUsersService,
    private _bulkupdateService: BulkUpdateService,
    private _permissionsService: PermissionsService,
    private _pluSearchService: PluSearchService,
    private commonService: CommonService,
    public commonSearchService :CommonSearchService,
    private featureFlagService: FeatureFlagsService
  ) { super(); }
  @Input() items: any;
  @Input() headerPage;
  @Input() defaultValue;
  @Output() loadDataForPC = new EventEmitter<boolean>();
  chevronClick = false;
  itemOption: string;
  form: UntypedFormGroup;
  verbiageForm = false;
  itemSelected: string;
  multipleCheckValidator: any = false;
  multipleFieldValidator: any = false;
  savedSearchListUsers = [];
  savedSearchListSystem = [];
  dateField;
  rangeEndDate: Date = new Date();
  showUpdateBtn = false;
  modifyItem: any;
  selectedDate: any;
  skipPodPlayGroundFlag: boolean = false;
  rangeDates = [
    'effectiveStartDate',
    'createTimeStamp',
    'lastUpdateTimestamp',
    'startDt',
    'endDt',
    'effectiveEndDate',
    'promotionStartDt',
    'startDate'
  ];

  colorTheme = 'theme-dark-blue';
  facetFilterQueryKeys = CONSTANTS.QUERY_WITH_FILTER_KEYS.facetFilter;
  @ViewChild('inputTextArea') inputTextArea;
  path: any;
  queryWithOrFilter;
  currentRoute = this.router.url;
  
  
  public typedUser$ = new Subject<string>();
  public typedAdBug$ = new Subject<string>();
  typedDept$ = new Subject<string>();
  typedDivison$ = new Subject<string>();
  public typedPeriod$ = new Subject<string>();
  public userArr = [];
  public departmentsArr = [];
  public adBugArr = [];
  public periodArr = [];
  divisionsArr: any[];
  UserDetails: any;
  AdBugDetails: any;
  periodDetails: any;

  divisionsList: any = [];
  configData: any;
  headLine:any;
  headLine2:any;
  productDesc:any

  ngOnInit() {

    const searchForm = [
      { key: 'searchInput' },
      { key: 'categoryName' },
      { key: 'savedSearchName' },
      { key: 'dateField' },
      { key: 'rangeStartDate' },
      { key: 'rangeEndDate' },
      { key: 'headLine' },
      { key: 'headLine2' },
      { key: 'productDesc' }
    ];
    this.form = this.searchOfferRequestService.toFormGroup(searchForm);
    this.form.setValue({
      searchInput: null,
      categoryName: '',
      savedSearchName: '',
      dateField: null,
      rangeEndDate: '',
      rangeStartDate: '',
      headLine:'',
      headLine2:'',
      productDesc:''
    });

    this.searchOfferRequestService.offerRequestSearchOptions(false);

    this.form &&
      this.form.setValue({
        searchInput: null,
        savedSearchName: '',
        dateField: null,
        rangeEndDate: '',
        rangeStartDate: '',
        categoryName: this.defaultValue || '',
        headLine:'',
        headLine2:'',
        productDesc:''
      });
    if (this.defaultValue) {
      this.itemClick();
    }
    this.path = this.router.routerState.snapshot.url;
    this.initTypeAhead();
    this.initSubscribes();
    this.configData = this._initialDataService.getAppData();
  }
  initSubscribes() {
    this.subs.sink = this._iviePromotionService.globalSearchChange$.subscribe((value) => {
      if (value) {
        this.skipPodPlayGroundFlag = true;
        this.searchClickHandler();
      }
    });

  }
  ngOnChanges() {
    this.form &&
      this.form.patchValue({
        searchInput: null,
        savedSearchName: '',
        dateField: null,
        categoryName: this.defaultValue || '',
        headLine:'',
        headLine2:'',
        productDesc:''
      });
    if (this.form && this.defaultValue) {
      this.itemClick();
    }
  }

  mouseleave() {
    this.chevronClick = false;
  }
  onClickOutside(clickedInside: any) {
    if (!clickedInside) {
      this.mouseleave();
    }
  }
  getUsersSavedSearches(userType) {
    this.savedSearchListUsers = [];
    this.showUpdateBtn = false;
    this.facetItemService.showSearchError = false;
    this._loaderService.isDisplayLoader(true);
    const path = this.router.routerState.snapshot.url;
    const reqType = path.includes('/' + ROUTES_CONST.OFFERS.Offers) ? 'O' : 'R';

    this._searchOfferService
      .savedSearchForRetrieve(reqType, userType)
      .subscribe((response: any) => {
        this.savedSearchListUsers = response.savedSearches;
      });
    this._loaderService.isDisplayLoader(false);
  }
  getSystemSavedSearches(userType) {
    this.form.controls['savedSearchName'].reset();
    this._loaderService.isDisplayLoader(true);
    const path = this.router.routerState.snapshot.url;
    const reqType = path.includes('/' + ROUTES_CONST.OFFERS.Offers) ? 'O' : 'R';


    this._searchOfferService
      .savedSearchForRetrieve(reqType, userType)
      .subscribe((response: any) => {
        this.secureDefaultSearchesByUserPermission(response);
      });

    this._loaderService.isDisplayLoader(false);
  }
  getFieldValue(controlName) {
    return (this.form.get(controlName) as UntypedFormControl).value;
  }
  getFieldErrorsOnSearch(controlName) {
    // Returns the Invalid status for the fields which needs to be validated during search click

    const control = this.form.get(controlName) as UntypedFormControl;
   
    if (control && control.untouched) {
      return control.errors;
    } else {
      control.setErrors(null);
      return false;
    }
  }

  secureDefaultSearchesByUserPermission(defaultSearches) {
    this.savedSearchListSystem = [];
    let positionOfDefaultSavedSearch;
    const permissions = this._permissionsService.getPermissions();
    if (permissions) {
      if (this.currentRoute === '/' + ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Management) {
        if (permissions[CONSTANTS.Permissions.DefaultSearchesViewOffersFromYourRequests]
          && permissions[CONSTANTS.Permissions.DefaultSearchesViewYourAssignedOffers]) {
          this.savedSearchListSystem = defaultSearches.savedSearches;
        } else if (permissions[CONSTANTS.Permissions.DefaultSearchesViewOffersFromYourRequests]) {
          this.savedSearchListSystem = defaultSearches.savedSearches.slice(0, 1);
        } else if (permissions[CONSTANTS.Permissions.DefaultSearchesViewYourAssignedOffers]) {
          this.savedSearchListSystem = defaultSearches.savedSearches.slice(1);
        } else {
          this.savedSearchListSystem = [];
        }
      } else if (this.currentRoute === '/' + ROUTES_CONST.REQUEST.Request) {
        // *********************** filter default saved searches based on permissions ******************************************
        if([CONSTANTS.GR, CONSTANTS.SPD].includes(this.facetItemService.programCodeSelected)){
          positionOfDefaultSavedSearch = defaultSearches.savedSearches.findIndex(o => o.name === 'Your Requests');
          this.savedSearchListSystem = defaultSearches.savedSearches.splice(positionOfDefaultSavedSearch);
        }
        else{
          if (permissions[CONSTANTS.Permissions.DefaultSearchesViewAssignmentSearches]
            && permissions[CONSTANTS.Permissions.DefaultSearchesViewYourRequests]) {
            this.savedSearchListSystem = defaultSearches.savedSearches;
          } else if (permissions[CONSTANTS.Permissions.DefaultSearchesViewYourRequests]) {
            positionOfDefaultSavedSearch = defaultSearches.savedSearches.findIndex(o => o.name === 'Your Requests');
            this.savedSearchListSystem = defaultSearches.savedSearches.splice(positionOfDefaultSavedSearch);
          } else if (permissions[CONSTANTS.Permissions.DefaultSearchesViewAssignmentSearches]) {
            positionOfDefaultSavedSearch = defaultSearches.savedSearches.findIndex(o => o.name === 'Your Requests');
            this.savedSearchListSystem = defaultSearches.savedSearches.slice(0, positionOfDefaultSavedSearch);
          } else {
            this.savedSearchListSystem = [];
          }
        }
      } else {
        // Comes here if user don't have permissions
        this.savedSearchListSystem = [];
      }
    }
  }

  keypress(event) {
    const index = event.target.value.indexOf(','),
      commaValue = event.target.value.slice(
        event.target.value.indexOf(',') + 1
      );
    if (
      (this.itemSelected === 'upc' || this.itemSelected === 'hhid') &&
      index != -1
    ) {
      if (commaValue.length) {
        this.multipleFieldValidator =
          this.itemSelected === 'upc'
            ? 'You can only search on one UPC or PLU at a time. Please clear UPC/PLU filter.'
            : 'You can only search on one HHID at a time. Please clear HHID filter';
        this.multipleCheckValidator = false;
      } else {
        this.multipleCheckValidator =
          this.itemSelected === 'upc'
            ? 'You can only search on one UPC or PLU at a time.'
            : 'You can only search on one HHID at a time.';
        this.multipleFieldValidator = false;
      }
    } else {
      this.multipleFieldValidator = this.multipleCheckValidator = false;
    }

    // For UPC, allow only one upc id

    if (event.which == 13) {
      this.searchClickHandler();
      event.preventDefault();
      return false;
    }
  }
  checkExistSavedSearch(event) {
    const val = event.currentTarget.value;
    const result = this.savedSearchListUsers.filter(
      (item) => item.name === val
    );
    this.showUpdateBtn = result.length > 0;
    if (result.length === 1) {
      this.modifyItem = result[0];
    }
  }

  onPasteSearch(event: ClipboardEvent) {
    const clipboardData = event.clipboardData;
    const pastedText = clipboardData ? clipboardData.getData('text').trim() : '';

    // For UPC, allow only one upc id
    if (
      (this.itemSelected === 'upc' || this.itemSelected === 'hhid') &&
      pastedText &&
      pastedText.match(/[,\n]/g)
    ) {
      this.keypress({ target: { value: pastedText } });
    }
  }



  fromToDateConversion(date) {
    if (!date || typeof date === 'string') {
      return;
    }
    date.setHours(0, 0, 0, 0);
    if (this.form.value.dateField === 'Range') {
      const from = this.form.value.rangeStartDate;
      const to = this.form.value.rangeEndDate;
      let fromDate;
      let toDate;
      if (
        this.itemSelected === 'startDt' ||
        this.itemSelected === 'startDate' ||
        this.itemSelected === 'effectiveStartDate' ||
        this.itemSelected === 'effectiveEndDate' ||
        this.itemSelected === 'endDt' ||
        this.itemSelected === 'promotionStartDt'
      ) {

        fromDate = from ? `${moment(from)
          .startOf('day')
          .format('YYYY-MM-DDTHH:mm:ss')}Z` : '*';
        toDate = `${moment(to)
          .endOf('day')
          .format('YYYY-MM-DDTHH:mm:ss')}Z`;
        // if (this.itemSelected === "endDt") {
        //   toDate = `${moment(to).add(1, 'days').startOf('day')
        //     .format('YYYY-MM-DDTHH:mm:ss')}Z`;
        // }
      } else {
        fromDate = from ? `${moment(from)
          .startOf('day')
          .utc()
          .format('YYYY-MM-DDTHH:mm:ss')}Z` : '*'
        toDate = `${moment(to)
          .endOf('day')
          .utc()
          .format('YYYY-MM-DDTHH:mm:ss')}Z`;
      }
      return `${fromDate} TO ${toDate}`;
    } else {
      const start = moment();
      const end = moment();
      let startDate;
      if (
        this.itemSelected === 'startDt' ||
        this.itemSelected === 'startDate' ||
        this.itemSelected === 'effectiveStartDate' ||
        this.itemSelected === 'effectiveEndDate' ||
        this.itemSelected === 'endDt' ||
        this.itemSelected === 'promotionStartDt'
      ) {
        startDate = `${moment(start)
          .startOf('day')
          .format('YYYY-MM-DDTHH:mm:ss')}Z`;
      } else {
        startDate = `${moment(start)
          .startOf('day')
          .utc()
          .format('YYYY-MM-DDTHH:mm:ss')}Z`;
      }

      const endDate =
        this.itemSelected === 'startDt' ||
        this.itemSelected === 'startDate' ||
          this.itemSelected === 'effectiveStartDate' ||
          this.itemSelected === 'promotionStartDt'
          ? '*'
          :( this.itemSelected === 'effectiveEndDate' ? `${moment(end).endOf('day').format('YYYY-MM-DDTHH:mm:ss')}Z`
          : `${moment(end).endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`);

      return `${startDate} TO ${endDate}`;
    }
  }
  savedSearchHandler() {
    if (this.form.value.savedSearchName && !this.isEmptyQuery()) {
      const path = this.router.routerState.snapshot.url;
      if (path.includes('/' + ROUTES_CONST.OFFERS.Offers)) {
        this._authService.onUserDataAvailable(
          this.savedSearchOffersApi.bind(this, 'O')
        );
      } else {
        this._authService.onUserDataAvailable(
          this.savedSearchOffersApi.bind(this, 'R')
        );
      }
    } else {
      this.facetItemService.showSearchError = true;

    }
  }
  updateSavedSearchHandler() {
    if (this.form.value.savedSearchName) {
      this.showUpdateBtn = false;
      this._authService.onUserDataAvailable(
        this.updateSavedSearchOfferAPI.bind(this)
      );
    }

    // updating saved search
  }
  buildDateFieldPayload() {
    return this.fromToDateConversion(new Date());
  }
  clearControlValidator(control) {
    control.clearValidators();
    control.updateValueAndValidity();
  }
  setFieldValidatorsForRangeDates() {
    const rangeStartDateCtrl = this.form.get('rangeStartDate');
    const rangeEndDateCtrl = this.form.get('rangeEndDate');
    rangeStartDateCtrl.clearValidators();
    rangeEndDateCtrl.clearValidators();
    const rangeStartDate = rangeStartDateCtrl.value;
    const rangeEndDate = rangeEndDateCtrl.value;
    if (this.rangeDates.includes(this.itemSelected) && this.selectedDate === 'Range') {
      if (!rangeStartDate) {
        rangeStartDateCtrl.setValidators([Validators.required]);
      }
      if (!rangeEndDate) {
        rangeEndDateCtrl.setValidators([Validators.required]);
      }
    }
    rangeStartDateCtrl.updateValueAndValidity();
    rangeEndDateCtrl.updateValueAndValidity();
  }
  checkValiddityForVerbiage() {
    const {headLine, categoryName, headLine2, productDesc} = this.form?.value;
    return categoryName && (headLine?.trim() || headLine2?.trim() || productDesc?.trim());
  }

  get isRequestMgmtPage(){
    const path = this.router.routerState.snapshot.url;
    return path.includes(`/${ROUTES_CONST.REQUEST.Request}`);

  }

  getOldInput(){
    let itemSelected= this.itemSelected;

    if(this.itemSelected === CONSTANTS.MOB_ID && this.isRequestMgmtPage){
       itemSelected = `${CONSTANTS.MOB_ID}=`;
    }
  
    return this.queryGenerator.getInputValue(itemSelected);
  }
  mobIdConversion(splitValues){
    let convertedArray =[];
    if(this.itemSelected === 'mobId'){
      splitValues.forEach(element => {
        element = element.replace(/^"|"$/g, '');
        convertedArray.push(element);
      });
      // Allow search with or without the zeros
      splitValues = convertedArray.map(e=>this.commonSearchService.pad(e, 6));
      splitValues = splitValues.filter(function(x) {
        return x !== undefined;
   });
    } 
    return splitValues;
  }
  
  get formSearchInput(){
    let searchInput = this.form?.value?.searchInput;
      searchInput = searchInput &&  CONSTANTS.COPY_PASTE_IDS_EXCEL_FOR_SEARCH.includes(this.itemSelected) ? 
        searchInput?.replace(/[ ,]+/g, ",") : searchInput;

        let splitValue = this.rangeDates?.includes(this.itemSelected)
          ? [this.buildDateFieldPayload()]
          : searchInput?.trim()?.split(/[,\n]/g);

        let searchString = '';
        const extraChar = !(
          this.rangeDates.includes(this.itemSelected) ||
          this.itemSelected === 'requestId' ||
          this.itemSelected === 'dept' ||
          this.itemSelected === 'upc' ||
          this.itemSelected === 'pointsRequired'||
          this.itemSelected === 'household' ||
          this.itemSelected === 'weekId'||
          this.itemSelected=== 'redemptionStoreId'
        )
          ? '*'
          : '';        

        const oldInput = this.getOldInput();      
        splitValue = this.mobIdConversion(splitValue);

        let splitOldValue = oldInput?.split(' OR '),
          _len = 0;

        splitValue?.forEach((element) => {
          element = element?.trim();
          const specialChar = element?.startsWith('*') ? /[.:/#@+&%!?^${}()[-]|[\]\\ ]/g : /[.:/#@+&*%!?^${}()[-]|[\]\\ ]/g;
          let _value = this.rangeDates.includes(this.itemSelected)
            ? element
            : `${element?.replace(specialChar, '\\$&')}${extraChar}`;
          if (["vehicleName","ecommPromoCode"].includes(this.itemSelected)) {
            _value = `${extraChar}${_value}`;
          }
          if (this.itemSelected === "adBugTxt" || this.itemSelected === "adPageNbr" || this.itemSelected === 'combinedIviePromotionUniversalSearch') {
            this.queryGenerator.removeParameters(['externalOfferId', 'adType', 'offerStatus', 'podRefOfferId'])
            _value = `${extraChar}${_value}`;
          }
          //In offer, OR home pages,  support partial search for the below items
          const partialSearchArr = ['qualificationBenefitCombinedProductsGroup', 'offerName', 'combinedStoreGroups', 'brandAndSize', 'productGroups'], path = this.router.routerState.snapshot.url;

          if ((path.includes('/' + ROUTES_CONST.REQUEST.Request) || path.includes('/' + ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Management)) && partialSearchArr.indexOf(this.itemSelected) > -1) {
            _value = `*${_value}`;
          }
          if (splitOldValue.indexOf(_value) == -1) {
            if (!searchString) {
              searchString = `${_value}`;
            } else if (_value) {
              searchString = `${searchString} OR ${_value}`;
            }
          } else {
            _len = _len + 1;
          }
        });
        return {searchInput, splitValue, _len, searchString, oldInput};
  }
getVerbiageData(formInputValue,oldInput,splitOldValue,_len){
    let searchString = '';
    const splitValue = formInputValue ? formInputValue.trim().split(/[,\n]/g) : '';
    if(splitValue){
      splitValue?.forEach((element) => {
        element = element.trim();
        const specialChar = element.startsWith('*') ? /[.:/#@+&%!?^${}()[-]|[\]\\ ]/g : /[.:/#@+&*%!?^${}()[-]|[\]\\ ]/g;
        let _value = `*${element.replace(specialChar, '\\$&')}*`;
        if (splitOldValue.indexOf(_value) == -1) {
          if (!searchString) {
            searchString = `${_value}`;
          } else if (_value) {
            searchString = `${searchString} OR ${_value}`;
          }
        } else {
          _len = _len + 1;
        }
      });
    }
    if (splitValue?.length === _len) {
      return false;
    }
    let searchInputListData: any = searchString;
    if (oldInput && searchString) {
      searchInputListData = `(${oldInput} OR ${searchString})`;
    } else if (oldInput) {
      searchInputListData = `(${oldInput})`;
    } else if (searchString) {
      searchInputListData = `(${searchString})`;
    }
    return searchInputListData;
  }
  getSelectedValueForVerbiage() {
    if(this.itemSelected === 'verbiage'){
      const paramsList = [];
      ["headLine", "headLine2", "productDesc"].forEach((input)=> {
        const _len = 0;
        const formValue = this.form.value[input]?.trim();
        const oldValue = this.queryGenerator.getInputValue(`${input}=`), splittedOldValue = oldValue?.split(' OR ')
        const searchedString = this.getVerbiageData(formValue,oldValue,splittedOldValue,_len);
        searchedString && paramsList.push({remove: true, parameter: input, value: searchedString});
        })
      return paramsList;
    }
  }
  searchClickHandler() {
    this.facetItemService.showSearchError = false;

    if(this.headerPage === "pluManagement"){
      this.form.get('dateField').setValue('Range');
    }

    if (this.headerPage === 'offerPODPage' && !this.skipPodPlayGroundFlag) {
      this._iviePromotionService.onInputSearchChange$.next(true);
    } else {
      this.form.markAsUntouched();
      this.setFieldValidatorsForRangeDates();
     
      this._bulkupdateService.requestIdArr = [];
      this._bulkupdateService.offersIdArr = [];
      this.skipPodPlayGroundFlag = false;
      const sortByValue = this.queryGenerator.getDefaultSearchValue(CONSTANTS.SORT_BY);

      if (this.itemSelected === 'externalOfferId') {
        //to convert the offer id input to upper case
        const offerIdInput = this.form.value.searchInput;
        const offerIdnputUpperCase = offerIdInput.toUpperCase().trim();
        this.form.value.searchInput = offerIdnputUpperCase;
      }
      if (this.itemSelected === 'headLine' && sortByValue) {
        this.queryGenerator.removeParam(CONSTANTS.SORT_BY);
      }
      if (this.headerPage === 'offerPODPage') {
        this.queryGenerator.removeParam(CONSTANTS.SORT_BY);
      }

      const multiSelectControl =  this.form.get("multiSelect"),multiSelectControlValue = multiSelectControl?.value;

      if (
        (this.form?.value?.searchInput?.trim() && this.form.value?.categoryName) || multiSelectControlValue?.length 
        || this.checkValiddityForVerbiage() ||
        this.rangeDates.includes(this.itemSelected) && this.form.valid
      ) {
       
       const {searchInput, splitValue, _len, searchString, oldInput} = this.formSearchInput;
       
    
       
        if (splitValue?.length === _len) {
          return false;
        }

        let searchInputList: any = searchString;

        if (this.rangeDates.includes(this.itemSelected)) {
          searchInputList = `[${searchInputList}]`;
        } else if (this.itemSelected === 'upc' || this.itemSelected === 'household') {
          if (searchInput.match(/[,]/g)) {
            return false;
          } else if (searchString) {
            searchInputList = `${searchString}`;
          }
        } else {
          if (oldInput && searchString) {
            searchInputList = `(${oldInput} OR ${searchString})`;
          } else if (oldInput) {
            searchInputList = `(${oldInput})`;
          } else if (searchString) {
            searchInputList = `(${searchString})`;
          }
        }
        
        this.updateQueryParams({sortByValue, searchInputList});
     
        if (this.headerPage === 'offerPODPage') {
          this.queryGenerator.removeParameters([CONSTANTS.SORT_BY]);
          this.queryGenerator.pushParam({ remove: true, parameter: CONSTANTS.SORT_BY, value: 'divisionIdTxtASC,vehicleNmASC,adPageNbrASC,adModNbrASC' });
        }
        if (this.itemSelected === 'assignedTo') {
          this.assignedToSearchHandler(searchString);
        }

        this.facetItemService.setOfferFilter('facetSearch');
        const todaySearch = this.form.get('dateField').value;
        this.removeFromTodayOption();
        if (todaySearch === 'Today' || todaySearch === 'Today+') {
          this.facetItemService.setTodayOption({
            chip: this.itemSelected,
            value: todaySearch,
          });
        }

        if(this.headerPage === "pluManagement") {
          let deptValue:any =  this.queryGenerator.getInputValue('dept');
          if(deptValue){
            deptValue = deptValue.split(' OR ');
            
            let arr=[];
            deptValue.forEach((val)=>{
               arr.push(val); 
            });
            
          }
        }
        this.addMultiSelectQuery();
        this.removeExpiredFlagOnSearch();
        this._searchOfferRequestService.populateHomeFilterSearch({
          facetChip: this.facetItemService.populateFacetSearch(
            this.queryGenerator.getQuery()
          ),
          facetFilter: null
        });
        this.setValuesToSearchForm();

        
        this.multipleCheckValidator = false;
        this.itemClick();

        this.apiForSearch();
      }
    }
  }
  removeExpiredFlagOnSearch() {
    // If request page is there and no status filter is selected then need to remove showExpired flag from query
    const path = this.router.routerState.snapshot.url,
    isRequestPath = path.includes(`/${ROUTES_CONST.REQUEST.Request}`)
    if(isRequestPath) {
      const filterObj = this.facetItemService?.facetChipComponent?.facetFilter;
      if(filterObj) {
        const isStatusExist = Object.keys(filterObj)?.includes("status");
        !isStatusExist && this.queryGenerator.removeParam("showExpired");
      }
    }
  }
  addMultiSelectQuery(){
   const multiSelectControl =  this.form.get("multiSelect");
   let value = multiSelectControl?.value.filter(ele=>ele).join(" OR ");
   if(value){
   value = `(${value})`;
   this.queryGenerator.pushParam({ remove: true, parameter: "vendors", value });
   }
  }
  updateQueryParams(obj){
    const {sortByValue, searchInputList} = obj;

    this.queryGenerator.removeParameters([CONSTANTS.NEXT, CONSTANTS.SID]);
    let paramsList = [
      {
        remove: true,
        parameter: CONSTANTS.LIMIT,
        value: CONSTANTS.PAGE_LIMIT,
      },
      {
        remove: true,
        parameter: CONSTANTS.SORT_BY,
        value: sortByValue ? sortByValue : `${CONSTANTS.LAST_MODIFY_DATE}${CONSTANTS.DESC}`,
      },
    ];
    if(!this.featureFlagService.isUPPFieldSearchEnabled)
    {
      paramsList.push({remove: true, parameter: CONSTANTS.CREATED_APP_ID, value: "OMS" });
    }
    const verbiageParamsList = this.getSelectedValueForVerbiage(),
    newAddedParam = this.isVendors() ? [] : [{
      remove: true,
      parameter: this.itemSelected,
      value: searchInputList,
    }]
    paramsList = this.itemSelected === "verbiage" ? [...paramsList, ...verbiageParamsList] : [...paramsList, ...newAddedParam];
    this.queryGenerator.pushParameters({ paramsList });
  }

 

  setValuesToSearchForm(){
    this.form.removeControl("multiSelect");
    this.form.setValue({
      searchInput: null,
      dateField: null,
      rangeStartDate: '',
      rangeEndDate: '',
      savedSearchName: '',
      categoryName: this.defaultValue,
      headLine:'',
      headLine2:'',
      productDesc:''
    });
  }

  apiForSearch(){
    const path = this.router.routerState.snapshot.url;

    if (path.includes('/' + ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.PodPlayground)) {
      this.queryGenerator.removeParam(CONSTANTS.SORT_BY);
      this.queryGenerator.pushParam({
        remove: true,
        parameter: CONSTANTS.SORT_BY,
        value: `updateTs${CONSTANTS.DESC}`,
      });
      if (this.headerPage === 'offerPODPage') {
        this.queryGenerator.removeParameters([CONSTANTS.SORT_BY]);
        this.queryGenerator.pushParam({ remove: true, parameter: CONSTANTS.SORT_BY, value: 'divisionIdTxtASC,vehicleNmASC,adPageNbrASC,adModNbrASC' });
      }
      this._authService.onUserDataAvailable(
        this.searchAllOffersPODPlaygroundApi.bind(this)
      );
    } else if (path.includes('/' + ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Management)) {

      this._authService.onUserDataAvailable(
        this.searchAllOffersApi.bind(this)
      );
    } else if(this.headerPage === "pluManagement") {
      this._authService.onUserDataAvailable(
        this._pluSearchService.fetchPluList.bind(this, {this:this._pluSearchService})
      );
    } else {
      this._authService.onUserDataAvailable(
        this.searchOfferReqAPI.bind(this)
      );
    }
  }

  setSearchInputList(obj){
    const searchInput = this.form.value.searchInput, {oldInput, searchString} = obj;
    let searchInputList: any = searchString;

    if (this.rangeDates.includes(this.itemSelected)) {
      searchInputList = `[${searchInputList}]`;
    } else if (this.itemSelected === 'upc' || this.itemSelected === 'hhid') {
      if (searchInput.match(/[,]/g)) {
        return false;
      } else if (searchString) {
        searchInputList = `${searchString}`;
      }
    } else {
      if (oldInput && searchString) {
        searchInputList = `(${oldInput} OR ${searchString})`;
      } else if (oldInput) {
        searchInputList = `(${oldInput})`;
      } else if (searchString) {
        searchInputList = `(${searchString})`;
      }
    }
    return searchInputList;
  }
  assignedToSearchHandler(searchString) {
    // To enable multiple search for assignedTo, geting old value and Concating with new Value   
    let oldValue = '';
    let oldSelectedValue = this.queryGenerator.getQueryFilter('combinedDigitalUser');
    if (oldSelectedValue) {
      oldValue = oldSelectedValue.split(')').length && oldSelectedValue.split(')')[0];
    }
    searchString = oldValue ? `${oldValue} OR ${searchString}` : searchString;
    //This is to remove old parameter from queryQithFilter Array so we can push new updated value in it
    this.queryGenerator.removeParamFromQueryFilter("combinedDigitalUser");
    this.queryGenerator.removeParam('assignedTo');
    this.queryWithOrFilter = `combinedDigitalUser=(${searchString})#combinedNonDigitalUser=(${searchString})`;
    const queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    queryWithOrFilters.push(this.queryWithOrFilter);
    this.queryGenerator.setQueryWithFilter(queryWithOrFilters);
  }
  removeFromTodayOption() {
    const todaysOptions = this.facetItemService.getTodayOption();
    let index;
    todaysOptions.forEach((ele, indx) => {
      if (ele.chip === this.itemSelected) {
        index = indx;
      }
    });
    if (index !== undefined) {
      todaysOptions.splice(index, 1);
    }
  }
  isEmptyQuery() {
    return (
      this.queryGenerator.getQuery() ===
      'limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;' &&
      !this.queryGenerator.getQueryWithFilter().length
    );
  }
  searchAllOffersApi() {
    this._searchOfferService
      .searchAllOffers(this.queryGenerator.getQuery(), true, this.queryGenerator.getQueryWithFilter())
      .subscribe((response: any) => {
        response.render = true;
        this._searchOfferRequestService.getOfferDetails(response);
      });
  }
  searchAllOffersPODPlaygroundApi() {
    this._iviePromotionService.searchAllPromotions(this.queryGenerator.getQuery(), true)
      .subscribe((response: any) => {
        response.render = true;
        this._iviePromotionService.getPaginationSearch(response);
      });
  }
  searchOfferReqAPI() {
    // Emptying the existing records
    this._searchOfferRequestService.getOfferDetails({});
    this._searchOfferRequestService
      .searchOfferRequest(
        this.queryGenerator.getQuery(),
        true,
        this.queryGenerator.getQueryWithFilter()
      )
      .subscribe((response: any) => {
        response.render = true;
        this._searchOfferRequestService.getOfferDetails(response);
        this._bulkupdateService.requestIdsListSelected$.next([]);
        this._bulkupdateService.offerIdsListSelected$.next([]);
        this._bulkupdateService.deliveryChannelArr=[]

      });
  }

  removeProgramCode(){
    this.queryGenerator.removeParam('programCode');
  }

  savedSearchOffersApi(this, type) {
    this.setQueryWithTodayDate();
    const queryWithFilter = this.queryGenerator.getQueryWithFilter();
    this.removeProgramCode();
    let searchQuery = this.queryGenerator.getQuery();
    // if query have status and endDate with * in Range. On saving saved search change endDt with Today+
    if (searchQuery.includes('offerStatus') && searchQuery.includes('endDt')) {
      let endDateRangeValue = this.queryGenerator.getInputRangeValue('endDt').split(' TO ')[1];
      if (endDateRangeValue && endDateRangeValue == '*') {
        let filteredArray = searchQuery.split(';').filter(ele => !ele.includes('endDt') && ele);
        filteredArray.push('endDt=Today+;');
        searchQuery = filteredArray.join(';');
      }
    }
    if (queryWithFilter.length) {
      searchQuery = `${searchQuery}queryWithOrFilters:[${queryWithFilter
        .map((ele) => `'${ele}'`)
        .join(',')}]`;
    }
    this._searchOfferService
      .savedSearchforOffer(searchQuery, this.form.value.savedSearchName, type)
      .subscribe(() => {
        this.form.controls['savedSearchName'].reset();
      });
  }
  setQueryWithTodayDate() {
    const todaysOptions = this.facetItemService.getTodayOption();
    todaysOptions.forEach((ele) => {
      if (ele.value === 'Today') {
        this.queryGenerator.pushParam({
          remove: true,
          parameter: ele.chip,
          value: 'Today',
        });
      } else if (ele.value === 'Today+') {
        this.queryGenerator.pushParam({
          remove: true,
          parameter: ele.chip,
          value: 'Today+',
        });
      }
    });
  }
  updateSavedSearchOfferAPI() {
    this.setQueryWithTodayDate();
    const queryWithFilter = this.queryGenerator.getQueryWithFilter();
    this.removeProgramCode();
    const query = this.queryGenerator.getQuery();
    if (queryWithFilter.length) {
      this.modifyItem.searchQuery = `${query}queryWithOrFilters:[${queryWithFilter
        .map((ele) => `'${ele}'`)
        .join(',')}]`;
    } else {
      this.modifyItem.searchQuery = query;
    }

    this._searchOfferService
      .updateSavedSearch(this.modifyItem, this.form.value.savedSearchName)
      .subscribe(() => {
      });
    this.form.controls['savedSearchName'].reset();
  }
  removeSavedSearch(item, event) {
    event.stopPropagation();
    this._searchOfferService
      .deleteSavedSearchOffer(item.name, item.type)
      .subscribe(() => {
        this.getUsersSavedSearches('U');
      });
  }
  setQueryWithFilter() {
    let splitArray, queryWithFilter;
    const originalQuery = this.queryGenerator.getQuery();
    this.queryGenerator.setQuery('');
    splitArray = originalQuery.split('queryWithOrFilters:');
    this.queryGenerator.setQuery(splitArray[0]);
    if (splitArray[1]) {
      const arrayFilter = splitArray[1].split(',');
      queryWithFilter = this.getQueryWithFilter(arrayFilter);
      if (queryWithFilter.length) {
        queryWithFilter.forEach((element, indx) => {
          if (element.includes('combinedDigitalUser') && queryWithFilter[indx] && queryWithFilter[indx].includes("(USER)")) {
            queryWithFilter[indx] = `combinedDigitalUser=(${this._authService.getUserId()}*)#combinedNonDigitalUser=(${this._authService.getUserId()}*)`;
          }
        });
        this.queryGenerator.setQueryWithFilter(queryWithFilter);
      }
    } else {
      this.queryGenerator.setQueryWithFilter([]);
    }
  }
  getQueryWithFilter(arrayFilter) {
    return arrayFilter.reduce((output, ele, indx) => {
      if (indx === 0 && indx === arrayFilter.length - 1 && ele.includes('\'')) {
        output.push(ele.slice(2, ele.length - 2).replace(/'/g,''));
      } else {
        if (ele.includes('\'') && indx == 0) {
          output.push(ele.slice(2, ele.length - 1).replace(/'/g,''));
        } else if (indx == arrayFilter.length - 1 && ele.includes('\'')) {
          output.push(ele.slice(1, ele.length - 2).replace(/'/g,''));
        }
        else {
          output.push(ele.slice(1, ele.length - 1));
        }
      }
      return output;
    }, []);
  }
  setPodViewParams() {
    let paramsList = [
      {
        remove: true,
        parameter: 'isApplicableToJ4U',
        value: `(true)`,
      },
      {
        remove: true,
        parameter: 'podRefOfferId',
        value: `(NA)`,
      }
    ];
    const offerPCSelected = this.facetItemService.getProgramCodesSelected();
    if (offerPCSelected?.length === 1) {

      if (offerPCSelected.includes(CONSTANTS.GR)) {
        paramsList.push({
          remove: true,
          parameter: CONSTANTS.SORT_BY,
          value: `rewardsRankASC`,
        })
      }
      else if (offerPCSelected.includes(CONSTANTS.SC)) {
        paramsList.push({
          remove: true,
          parameter: CONSTANTS.SORT_BY,
          value: `headLineASC`,
        })
      }
      this.queryGenerator.pushParameters({ paramsList });
    }

  }

  updateQueryForYourReq(item){
    if (
      item.name === 'Your Requests'
    ) {
      this.queryGenerator.pushParam({
        remove: true,
        parameter: 'createUserId',
        value: `(${this._authService.getUserId()}*)`,
      });
    }
  }

  updateQueryForYourOffersOrRequestsInReqPg(item){
    if (item.type === 'R' && (item.name === 'Your Requests' || item.name === 'Your Assigned Offers')) {
      this.queryGenerator.pushParam({
        remove: true,
        parameter: 'effectiveEndDate',
        value: `[${this.generateQueryForToday('effectiveEndDate')}]`,
      });
    }
  }

  updateQueryForYourOffersOrRequestsInOfferPg(item){
    if (item.type === 'O' && (item.name === 'Offers From Your Requests' || item.name === 'Your Assigned Offers')) {
      this.queryGenerator.pushParam({
        remove: true,
        parameter: 'endDt',
        value: `[${this.generateQueryForToday('effectiveEndDate')}]`,
      });
      if (item.name === 'Offers From Your Requests') {
        this.queryGenerator.pushParam({
          remove: true,
          parameter: 'requestedUserId',
          value: `(${this._authService.getUserId()}*)`,
        });
      }
      if (item.name === 'Your Assigned Offers') {
        this.queryGenerator.pushParam({
          remove: true,
          parameter: 'userId',
          value: `(${this._authService.getUserId()}*)`,
        });
      }

      /**
       * For system generated saved search, need to send checked program codes in the query
       */
      this.queryGenerator.pushParam({
        remove: true,
        parameter: "offerProgramCd",
        value: `(${this.getSelectedOfferProgrmCd()})`
      });
    }
    if(item.name === 'Active Offers'){
      this.queryGenerator.pushParam({
        remove: true,
        parameter: "offerProgramCd",
        value: `(${this.getSelectedOfferProgrmCd()})`
      });
      this.queryGenerator.pushParam({
        remove: true,
        parameter: 'startDt',
        value: `[${this.generateQueryForStartToEndDateToday()}]`,
      });
      this.queryGenerator.pushParam({
        remove: true,
        parameter: 'endDt',
        value: `[${this.generateQueryForToday('effectiveEndDate')}]`,
      });
      this.queryGenerator.pushParam({
            remove: true,
            parameter: 'activeSearch',
            value: `${true}`
          })
    }
  }

  setParamsForReqSavedSearch(path){
    if(path.includes('/' + ROUTES_CONST.REQUEST.Request)){
      this.queryGenerator.pushParam({
        remove: true,
        parameter: "programCode",
        value: `(${this.facetItemService.programCodeSelected})`
      });
    }
  }

  bindSavedSearchApiMethods(path){
    let searchFn;
    if (path.includes('/' + ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Management)) {
      searchFn = this._searchOfferService.searchAllOffers.bind(
        this._searchOfferService
      );
    } else {
      searchFn = this._searchOfferRequestService.searchOfferRequest.bind(
        this._searchOfferRequestService
      );
    }
    return searchFn;
  }
  setRangeDates(){
    this.rangeDates.forEach((ele) => {
      let queryParam = this.getQueryParameter(ele)[0];

      if (queryParam) {
        //For Expired status in OR, ignore 
        if(ele === CONSTANTS.END_DATE_QUERY_OR && queryParam.includes('[* TO ')){
          return false;
        }

        if (!queryParam.includes('endDt=Today+')) {
          /* if savedSearch query contains Today+ with endDate, dont set Today Options as we 
          dont want chip to genrate with Today*/
          this.facetItemService.setTodayOption({
            chip: ele,
            value:
              ele === 'startDt' || ele === 'startDate' || ele === 'effectiveStartDate' || ele === 'promotionStartDt' || ele === 'effectiveEndDate'
                ? 'Today+'
                : 'Today',
          });
        }
        this.queryGenerator.pushParam({
          remove: true,
          parameter: ele,
          value: `[${this.generateQueryForToday(ele, queryParam)}]`,
        });
      } else {
        this.removeTodayOption(ele);
      }
    });
  }
  get isDivisionalGamesEnabled() {
    return this.searchOfferRequestService.isDivisionalGamesEnabled;
  }
  async getProgramTypesForSubProgramCode() {
    const selectedSubProgramCd = this.queryGenerator.getInputValue('subProgramCode');
    if(selectedSubProgramCd) {
      const appData = this._initialDataService.getAppData()
      const selectedSubProgramCdList = selectedSubProgramCd.split(' OR ');
      let searchedPType = null;
      if (selectedSubProgramCdList?.length && selectedSubProgramCdList?.length === 1) {
        searchedPType = selectedSubProgramCdList.includes("Base") ? 'GR_ODC' : 'DG_ODC'
      } else if (selectedSubProgramCdList?.length && selectedSubProgramCdList?.length > 1) {
        searchedPType = "(GR_ODC OR DG_ODC)";
      }
      let prgrmTypeList = searchedPType ? await this._searchOfferRequestService.fetchProgramTypes(appData, searchedPType) : [];
      appData.offerDetailsProgramTypesRequestGR = prgrmTypeList;
    }
  }

 async applySavedSearch(item) {
    this.queryGenerator.setQuery(item.searchQuery);

    this.setQueryWithFilter();
    this.isDivisionalGamesEnabled && await this.getProgramTypesForSubProgramCode();
    this.facetItemService.emptyTodayOption();
    const path = this.router.routerState.snapshot.url;
        
    if (this.queryGenerator.getInputValue('next') && this.queryGenerator.getInputValue('sid')) {
      this.queryGenerator.removeParameters(['next', 'sid']);
    }

    this.updateQueryForYourReq(item);
    this.updateQueryForYourOffersOrRequestsInReqPg(item);
    this.updateQueryForYourOffersOrRequestsInOfferPg(item);
  
 
    this._bulkupdateService.savedSearchesFilter = '';
    if (['Needs Assignment', 'Needs Digital Assignment', 'Needs Non-Digital Assignment'].includes(item.name)) {
      this._bulkupdateService.savedSearchesFilter = item.name;
    }

    this.setParamsForReqSavedSearch(path);

    const IsPodView = this.facetItemService.podView;
    if(IsPodView){
      this.setPodViewParams();
    }
    
   
    this.setRangeDates();

    this.resetOfferProgramCdsOnSavedSearch();

    this.makeSavedSearchApi(path);

 

    // On applying saved search, these parameters need to set with default values
    this.facetItemService.searchText = "";
    this.facetItemService.indexList  = [];
  }

  makeSavedSearchApi(path){
    let searchFn = this.bindSavedSearchApiMethods(path);

    searchFn(
      this.queryGenerator.getQuery(),
      true,
      this.queryGenerator.getQueryWithFilter()
    ).subscribe((response: any) => {
      response.render = true;
      response.pagination = true;
      response.savedSearches = true;

      this._searchOfferRequestService.getOfferDetails(response);
    },()=>{
      this._searchOfferRequestService.getOfferDetails({savedSearches: true});
    });
  }

  resetOfferProgramCdsOnSavedSearch() {
    const offerPrgmCode = this.queryGenerator.getInputValue('offerProgramCd');
    if(offerPrgmCode) {
      const selectedPCArray = offerPrgmCode.split(" OR ");
      this.facetItemService.resetProgramCdChecked();
      selectedPCArray.forEach((pcode) => {
        this.facetItemService.programCodeChecked[pcode] = true
      })
      this.loadDataForPC.emit(true);
      this.facetItemService.programCodeChanged = true;
    }
  }
  removeTodayOption(ele) {
    const todaysOptions = this.facetItemService.getTodayOption();
    let index;
    todaysOptions.forEach((element, indx) => {
      if (element.chip === ele) {
        index = indx;
      }
    });
    if (index !== undefined) {
      todaysOptions.splice(index, 1);
    }
  }
  /**
   * 
   * @returns It returns the program code selection values on applying saved search to pass in to query
   */
  getSelectedOfferProgrmCd() {
    const checkedPC = this.facetItemService.programCodeChecked;
    const selectedProgrmCds = checkedPC && Object.keys(checkedPC).filter(programCode=> checkedPC[programCode]);
    if(selectedProgrmCds) {
      return (selectedProgrmCds.length > 1 ? selectedProgrmCds.join(' OR '): selectedProgrmCds.join(''));
    }
  }
  getQueryParameter(parameter) {
    const splitQuery = this.queryGenerator.getQuery().split(';');
    return splitQuery.filter((ele) => {
      const param = ele.split('=');
      return (
        param[0] === parameter &&
        (param[1] === 'Today' || param[1] === 'Today+' || (param[0] == 'effectiveEndDate' && param[1].includes('*')))
      );
    });
  }


  generateQueryForToday(ele, queryParam = '') {
    const start = moment();

    const end = moment();
    const endDate =
      ele === 'startDt' ||  ele === 'startDate' || ele === 'effectiveStartDate' || ele === 'effectiveEndDate' || queryParam.includes('endDt=Today+')
        ? '*'
        : (ele == "createTimeStamp" || ele == "lastUpdateTimestamp" ? 
          `${end.endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z` : `${end.format('YYYY-MM-DDT')}23:59:59Z`);
    
          //If today+ option with the end date we just need to search for enddate 00:00:00 to * range
    const startDate = (ele == "createTimeStamp" || ele == "lastUpdateTimestamp") ? 
      `${start.startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z` : `${start.format('YYYY-MM-DDT')}00:00:00Z`;
    return `${startDate} TO ${endDate}`;
  }

  generateQueryForStartToEndDateToday(){
    const end = moment();
    const startDate = '*';
    const endDate = `${end.format('YYYY-MM-DDT')}23:59:59Z`;
    return `${startDate} TO ${endDate}`;
  }

  itemClick() {
    this.verbiageForm = false;
    this.clearControlValidator(this.form.get('rangeStartDate'));
    this.clearControlValidator(this.form.get('rangeEndDate'));
    this.form.get('searchInput').setValue(null);
    this.itemSelected = this.items && this.items.filter((item) => {
      return item.label === this.form.value.categoryName;
    })[0].field;
    if (this.rangeDates.includes(this.itemSelected)) {
      if (['endDt'].includes(this.itemSelected) || this.currentRoute.includes('pod-playground')) {
        this.selectedDate = 'Range';
        this.dateField = ['Range'];
        this.form.get('dateField').setValue('Range');
      } else {
        this.dateField =
          this.itemSelected === 'startDt' ||
          this.itemSelected === 'startDate' ||
            this.itemSelected === 'effectiveStartDate' ||
            this.itemSelected === 'promotionStartDt'
            ? ['Today+', 'Range']
            : ['Today', 'Range'];
        const today =
          this.itemSelected === 'startDt' ||
          this.itemSelected === 'startDate' ||
            this.itemSelected === 'effectiveStartDate'
            // || this.itemSelected === 'promotionStartDt'
            ? 'Today+'
            : 'Today';
        this.form.get('dateField').setValue(today);
        this.selectedDate = today;
      }
    } else {
      this.form.get('dateField').setValue(null);
    }
    if(this.itemSelected === "verbiage"){
      this.verbiageForm = true;
    }
  }
  dateSelect(event) {
    this.selectedDate = event.currentTarget.value;
  }
  setMinEndDate(event) {
    if (!event) {
      return;
    }
    this.rangeEndDate = event;
    if (
      new Date(event).getTime() >
      new Date(
        this.form.controls['rangeEndDate'].value
      ).getTime()
    ) {
      this.form.controls['rangeEndDate'].setValue(event);
    }
  }
  removeWordAfterSpace(term){
    //'whole some' changes to 'whole'
    const splitText = term && term.split(' ');
    if (splitText && splitText.length > 1) {
      splitText.pop();
      term = splitText.join(' ');
    }
    return term;
  }

  getUsers(term) {
    if(term != null) {
      term = this.removeWordAfterSpace(term);
      return this._searchUsersService.getUsers(term);
    } else {
      return of([]);
    }
  }

  getAutosuggestList(obj){
    let {term, data} = obj;
    if(term === null) {
      return of([]);
    }
      
    term = this.removeWordAfterSpace(term).toLowerCase();
      const arr = data.filter(elem=>{
        if((elem.toLowerCase()).indexOf(term) > -1){
          return elem;
        }
      })

      return of(arr);
  }
  getPeriodWeeks(term) {
    if(term != null) {
      term = this.removeWordAfterSpace(term);
      return this.commonService.getPeriodWeeks(term);
    } else {
      return of([]);
    }
    }

  getAdBugs(term) {
    let query = `adBugTxt=(*${term}*);`
    return this._iviePromotionService.getAdBugs(query, true);
  }

  setTooltipValue(controlName) {
    return this.form.get(controlName).value;
  }

  getDeptAutosuggestList(obj){
    let {term, data} = obj;
    if(term === null) {
      return of([]);
    }
      
    term = this.removeWordAfterSpace(term).toLowerCase();
      const arr = []
      let key,value;
      for ( [key, value] of Object.entries(data)){
        if((value.toLowerCase()).indexOf(term) > -1){
          arr.push({id:key, name:value})
        }
      }

      return of(arr);
  }

  initTypeAhead() {
    this.typedDept$
    .pipe(
      distinctUntilChanged(),
      debounceTime(300),
      switchMap((term) =>  this.getDeptAutosuggestList({term, data:this._initialDataService.getAppData().departmentsWithCodes}))
    )
    .subscribe((items) => {
      this.departmentsArr = (items as unknown) as any;
    });
    

    this.typedDivison$
    .pipe(
      distinctUntilChanged(),
      debounceTime(300),
      switchMap((term) =>  this.getAutosuggestList({term, data:this.commonService.getDivisions()}))
    )
    .subscribe((items) => {
      this.divisionsArr = (items as unknown) as any[];
    });

    this.typedUser$
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        switchMap((term) => this.getUsers(term))
      )
      .subscribe((items) => {
        const usersArr = (items as unknown) as any[];
        this.UserDetails = usersArr;
        this.userArr = usersArr.map(user => ({
          id: user.userId,
          name: `${user.firstName} ${user.lastName}`
        }));
      });

    this.typedAdBug$
      .pipe(
        distinctUntilChanged(),
        debounceTime(300),
        switchMap((term) => this.getAdBugs(term))
      )
      .subscribe((items) => {
        const adBugArr = (items as unknown) as any[];
        this.AdBugDetails = adBugArr;
        this.adBugArr = adBugArr;
      });
      this.typedPeriod$
          .pipe(
              distinctUntilChanged(),
              debounceTime(300),
              switchMap((term) => this.getPeriodWeeks(term))
      ).subscribe((items) => {
          const periodsArr = (items as unknown) as any[];
          this.periodDetails = periodsArr
          this.periodArr = periodsArr;
      });

  }
  getSearchField() {
    return this.itemSelected === 'startDt' || this.itemSelected === 'startDate' || this.itemSelected === 'endDt';
  }
  getTypeaheadSearchField() {
    return (
      !this.itemSelected ||
      this.itemSelected === 'createUserId' ||
      this.itemSelected === 'requestedUserId' ||
      this.itemSelected === 'assignedTo' ||
      this.itemSelected === 'userId' ||
      this.itemSelected === 'updatedUserId'
    );
  }
  
  getTypeaheadSearchAdBugField() {
    return (
      !this.itemSelected ||
      this.itemSelected === 'adBugTxt'
    );
    }
  getTypeaheadSearchPeriodField() {
    return (
      !this.itemSelected ||
      this.itemSelected === 'periodWeek' && this.facetItemService.programCodeSelected === CONSTANTS.SPD
    );
    }
    

    getlUserId(): boolean {
      const searchedValue = this.form.get('searchInput')?.value;
    
      if (!this.UserDetails || !searchedValue) {
        this.form.get('searchInput')?.setValue('');
        return false; // Ensures `searchClickHandler()` does not execute
      }
    
      const matchedUser = this.UserDetails.find(user => searchedValue.includes(user.userId));
      const userId = matchedUser?.userId || '';
    
      this.form.get('searchInput')?.setValue(userId);
    
      return !!userId; // Return true if a valid user is found
    }    
  

  isValueExistsInInput(dataArr){
    let searchedValue = this.form.value.searchInput, arr;

    if (dataArr && searchedValue) {
      arr = dataArr.filter(obj=> {
        //Incase of plumanagement pg when department is selected
        if(obj.name && this.itemSelected === 'dept' && this.headerPage === "pluManagement") {
          return obj.id === searchedValue
        } else {
          return searchedValue.indexOf(obj) > -1;
        }
      });
      const val = arr && arr[0] && arr[0].id ? arr[0].id: arr[0];
      this.form.value.searchInput = val;
      this.form.get('searchInput').setValue(val);
    }

    return (this.form.value.searchInput = arr ? arr[0] : '');
  }

  getAdBug() {
    const searchedValue = this.form.value.searchInput;
    let result;
    if (this.AdBugDetails && searchedValue) {
        result = this.AdBugDetails.filter((item) => {
        return searchedValue.indexOf(item) > -1;
      });
      this.form.value.searchInput = result[0];
      this.form.get('searchInput').setValue(result[0]);
    }

    return (this.form.value.searchInput = result ? result[0] : '');
    }

  getInputSearchField() {
    return (
      !this.itemSelected ||
      !(
        this.itemSelected === 'startDt' ||
        this.itemSelected === 'endDt' ||
        this.itemSelected === 'createUserId' ||
        this.itemSelected === 'requestedUserId' ||
        this.itemSelected === 'assignedTo' ||
        this.itemSelected === 'userId' ||
        this.itemSelected === 'adBugTxt' ||
        this.itemSelected === 'dept' ||
        this.itemSelected === 'periodWeek' ||
        this.itemSelected === 'div' ||
        this.itemSelected === 'updatedUserId' ||
        this.rangeDates.includes(this.itemSelected)
      )
    );
  }
  onSubmit() {
    this.searchClickHandler();
  }
  isStartDateSelectedinPluPage() {
    return this.itemSelected == 'startDate';
  }
  isVendors(){
    return this.itemSelected === 'vendors';
    }
  get extraChar() {
    return !(
      this.rangeDates.includes(this.itemSelected) ||
      this.itemSelected === 'requestId' ||
      this.itemSelected ===  CONSTANTS.OR_SEARCH_OFFER_ID ||
      this.itemSelected === 'dept' ||
      this.itemSelected === 'upc' ||
      this.itemSelected === 'pointsRequired' ||
      this.itemSelected === 'household'||
      this.itemSelected=== 'redemptionStoreId'
    )
      ? '*'
      : '';
  }
}

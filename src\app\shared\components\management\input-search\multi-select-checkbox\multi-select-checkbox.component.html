
<form [formGroup]="form">
    <div (mouseleave)="showDropDown = false">
        <button class="drop-toggle btn flat icon-display mr-3" (click)="showDropDown=!showDropDown">
            <span *ngIf="checkedList.length>0 && checkedList.length!==list.length">{{currentSelected.name}}</span>
            <span *ngIf="checkedList.length===list.length">ALL</span>
        </button>
            <div class="drop-show" *ngIf="showDropDown && formControls">
                <label  *ngFor="let control of form.controls.multiSelect.controls; let i=index;">
                    <ng-container>
                        <input type="checkbox" 
                        [formControl]="control" [value]="list[i].value" (change)="getSelctedControl($event,list[i],i);"/>
                   <span>{{list[i].name}}</span>
                    </ng-container> 
                </label>
            </div>
        
        
    </div>
</form>
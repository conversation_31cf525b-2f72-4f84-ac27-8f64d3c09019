/***
    css for multi select drop down
**/
.drop-toggle{
    background-color: #fff;
    padding: 5px 10px;
    cursor: pointer;
    border: 1px solid #ccc;
    width: 200px;
    text-align: left;
}
.drop-toggle i{
    float:right;
}
.drop-show {
    padding: 4px;
    width: 200px;
    background-color: #FFF;
    border: 1px solid #BABABA;
    position: absolute;
    z-index: 100;
    -webkit-box-shadow: 0 6px 10px rgba(0,0,0,.15);
    -moz-box-shadow: 0 6px 10px rgba(0,0,0,.15);
    box-shadow: 0 6px 10px rgba(0,0,0,.15);
    margin-left: -10px;
}
.drop-show label{
    display:block;
    font-size:15px;
    cursor: pointer;
}
.drop-show label input{
    vertical-align: top;
}
.drop-show label span{
    display:inline-block;
}
.icon-display {
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMy4zNSIgaGVpZ2h0PSI3LjUxIiB2aWV3Qm94PSIwIDAgMTMuMzUgNy41MSI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiMwMDAwMDA7aXNvbGF0aW9uOmlzb2xhdGU7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5Bc3NldCA0PC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iTGF5ZXJfMS0yIiBkYXRhLW5hbWU9IkxheWVyIDEiPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSIwIDEuMjUgNy4xIDcuNTEgMTMuMzUgMS4yMiAxMi4xNyAwLjA0IDcuMDIgNS4yMiAxLjEgMCAwIDEuMjUgMCAxLjI1IDAgMS4yNSIvPjwvZz48L2c+PC9zdmc+) no-repeat right 8px center !important;
    margin-left: -10px;
}
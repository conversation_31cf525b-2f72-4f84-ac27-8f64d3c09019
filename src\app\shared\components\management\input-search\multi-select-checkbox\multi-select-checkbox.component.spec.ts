import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { MultiSelectCheckboxComponent } from './multi-select-checkbox.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('MultiSelectCheckboxComponent', () => {
    let component: MultiSelectCheckboxComponent;
    let fixture: ComponentFixture<MultiSelectCheckboxComponent>;
    let initialDataService: jasmine.SpyObj<InitialDataService>;
    let queryGenerator: jasmine.SpyObj<QueryGenerator>;
    let formBuilder: UntypedFormBuilder;

    beforeEach(async () => {
        const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getAppDataName']);
        const queryGeneratorSpy = jasmine.createSpyObj('QueryGenerator', ['getInputValue']);

        await TestBed.configureTestingModule({
            schemas: [NO_ERRORS_SCHEMA],
            declarations: [MultiSelectCheckboxComponent],
            providers: [
                { provide: InitialDataService, useValue: initialDataServiceSpy },
                { provide: QueryGenerator, useValue: queryGeneratorSpy },
                UntypedFormBuilder
            ]
        }).compileComponents();

        initialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
        queryGenerator = TestBed.inject(QueryGenerator) as jasmine.SpyObj<QueryGenerator>;
        formBuilder = TestBed.inject(UntypedFormBuilder);
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(MultiSelectCheckboxComponent);
        component = fixture.componentInstance;
        component.form = formBuilder.group({});
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize the component with vendors data', () => {
        const vendors = { vendor1: 'Vendor 1', vendor2: 'Vendor 2' };
        initialDataService.getAppDataName.and.returnValue(vendors);
        queryGenerator.getInputValue.and.returnValue('Vendor 1 OR Vendor 2');

        component.ngOnInit();

        expect(component.list.length).toBe(2);
        expect(component.list[0].name).toBe('vendor1');
        expect(component.list[0].checked).toBeTrue();
        expect(component.list[1].name).toBe('vendor2');
        expect(component.list[1].checked).toBeTrue();
    });

    it('should create form group with multiSelect control', () => {
        component.ngOnInit();
        expect(component.form.contains('multiSelect')).toBeTrue();
    });

    it('should return selected values', () => {
        component.ngOnInit();
        component.checkedList = ['Vendor 1'];
        const selectedValue = component.getSelectedValue();

    });

    it('should return form controls', () => {
        component.ngOnInit();
        const formControls = component.formControls;
        expect(formControls).toBeTruthy();
        expect(formControls.controls.length).toBe(component.list.length);
    });

    xit('should update checkedList and currentSelected on getSelctedControl', () => {
        component.ngOnInit();
        const event = { target: { checked: true } };
        const element = { value: 'Vendor 1' };
        const index = 0;

        component.getSelctedControl(event, element, index);

        expect(component.checkedList).toContain('Vendor 1');
        expect(component.currentSelected['name']).toBe('vendor1');
    });

    xit('should update checkedList and currentSelected on getSelctedControl when unchecked', () => {
        component.ngOnInit();
        const event = { target: { checked: false } };
        const element = { value: 'Vendor 1' };
        const index = 0;

        component.getSelctedControl(event, element, index);

        expect(component.checkedList).not.toContain('Vendor 1');
        expect(component.currentSelected['name']).toBeUndefined();
    });

    xit('should not update checkedList and currentSelected if index is out of bounds', () => {
        component.ngOnInit();
        const event = { target: { checked: true } };
        const element = { value: 'Vendor 1' };
        const index = 10; // out of bounds

        component.getSelctedControl(event, element, index);

        expect(component.checkedList).not.toContain('Vendor 1');
        expect(component.currentSelected['name']).toBeUndefined();
    });

    it('should handle null event in getSelctedControl', () => {
        component.ngOnInit();
        const event = null;
        const element = { value: 'Vendor 1' };
        const index = 0;

        expect(() => component.getSelctedControl(event, element, index)).toThrow();
    });

    it('should handle null element in getSelctedControl', () => {
        component.ngOnInit();
        const event = { target: { checked: true } };
        const element = null;
        const index = 0;

        expect(() => component.getSelctedControl(event, element, index)).toThrow();
    });

    it('should handle null index in getSelctedControl', () => {
        component.ngOnInit();
        const event = { target: { checked: true } };
        const element = { value: 'Vendor 1' };
        const index = null;

        expect(() => component.getSelctedControl(event, element, index)).toThrow();
    });

    it('should handle undefined event in getSelctedControl', () => {
        component.ngOnInit();
        const event = undefined;
        const element = { value: 'Vendor 1' };
        const index = 0;

        expect(() => component.getSelctedControl(event, element, index)).toThrow();
    });

    it('should handle undefined element in getSelctedControl', () => {
        component.ngOnInit();
        const event = { target: { checked: true } };
        const element = undefined;
        const index = 0;

        expect(() => component.getSelctedControl(event, element, index)).toThrow();
    });

    it('should handle undefined index in getSelctedControl', () => {
        component.ngOnInit();
        const event = { target: { checked: true } };
        const element = { value: 'Vendor 1' };
        const index = undefined;

        expect(() => component.getSelctedControl(event, element, index)).toThrow();
    });
});
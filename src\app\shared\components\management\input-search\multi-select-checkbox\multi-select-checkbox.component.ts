import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';

@Component({
  selector: 'app-multi-select-checkbox',
  templateUrl: './multi-select-checkbox.component.html',
  styleUrls: ['./multi-select-checkbox.component.scss']
})
export class MultiSelectCheckboxComponent implements OnInit{
  list:any[] =[]; 
  data:any[] =[];
  checkedList : any[] =[];
  currentSelected : {};
  showDropDown = false;
  @Input() form;
  
  constructor(private initialDataService:InitialDataService,
    private queryGenerator: QueryGenerator) {
   }
   ngOnInit(){
     let vendors = this.initialDataService.getAppDataName("vendors");
     const vendorQueryValue = this.queryGenerator.getInputValue("vendors"),
     selectedValue = vendorQueryValue?.split(" OR ");
      for(const vendor in vendors){
          this.list.push({
            name:vendor,
            checked: vendorQueryValue ? selectedValue.includes(vendors[vendor]) : true,
            value:vendors[vendor]
          })
      }
    this.createFormGroup();
    this.checkedList = this.list?.reduce((output, ele)=> { 
        if(ele?.checked){
          output.push(ele.value);
        }
        return output;
     },[]);
    this.currentSelected = this.getSelectedValue();
  
   }
   createFormGroup(){
    
     this.addFormControl("multiSelect", this.createMultiSelect());
   }
   createMultiSelect(){
    const arr = this.list.map(element => {
      return new UntypedFormControl(element.checked ? element.value : false);
    });
    return new UntypedFormArray(arr);
   }
   get formControls (){
     return this.form.get("multiSelect") as UntypedFormArray;
   }
   addFormControl(name, formControl) {
    this.form.addControl(name, formControl);
  }
  getSelctedControl(event,element,index){
    const control = this.formControls.at(index);
        if(event.target.checked){
          control.setValue(element.value);
        }else{
          control.setValue(false);
        }
        this.checkedList = this.formControls.value.filter(ele=>ele);
        this.currentSelected = this.getSelectedValue();
    }
    getSelectedValue(){
      return this.list.reduce((output,ele)=>{
          if(this.checkedList.includes(ele.value)){
            output['name'] = ele.name;
          }
        return output;
      },{})
    }
}


<nav class="nav bg-white">
  <div class="logo-div-class justify-content-center py-2">
    <img src="assets/icons/oms-logo.svg" alt="OMS logo" class="logo" />
  </div>

  <div class="flex-wrap justify-content-start" id="navbarNavAltMarkup">
    <ul class="nav py-2">
      <ng-container>
        <li *permissionsOnly="view_offer_template_request; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="nav-item dropdown   nav-text  px-6">
          <a class="nav-link dropdown-toggle  p-0 py-1" id="navbarDropdown template-tab" role="button" data-toggle="dropdown"
            aria-haspopup="true" aria-expanded="false"
            [ngClass]="isTemplateDropDownActive ? 'dropdown-selected active' : 'dropdown-unselected'">
            Template
          </a>
          <div class="dropdown-menu" aria-labelledby="navbarDropdown">
            <a id="template-mgmt" class="dropdown-item nav-text" [routerLink]="templateRoute" routerLinkActive="active"  [routerLinkActiveOptions]="{exact:
              true}">Template Management</a>
          </div>
        </li>
      </ng-container>
    
      <li *permissionsOnly="view_Offer_Request; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="nav-item dropdown   nav-text  px-6">
        <a class="nav-link dropdown-toggle  p-0 py-1" id="navbarDropdown request-tab" role="button" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="false"
          [ngClass]="isRequestDropDownActive ? 'dropdown-selected active' : 'dropdown-unselected'">
          Request
        </a>
        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
          <a id="req-mgmt" class="dropdown-item nav-text" [routerLink]="requestRoute" routerLinkActive="active"  [routerLinkActiveOptions]="{exact:
            true}">Request Management</a>

          <a id="plu-tab" class="dropdown-item nav-text" [routerLink]="pluManagementRoute"
            routerLinkActive="active">PLU</a>

        </div>
      </li>
      <li class="nav-item dropdown   nav-text  px-6 ">
        <a class="nav-link dropdown-toggle  p-0 py-1" id="navbarDropdown offers-tab" role="button" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="false"
          [ngClass]="isOfferDropDownActive ? 'dropdown-selected active' : 'dropdown-unselected'">
          Offers
        </a>
        <div class="dropdown-menu menu-box-shadow" aria-labelledby="navbarDropdown">
          <a id="offer-mgmt" class="dropdown-item nav-text" [routerLink]="offerSearchRoute" routerLinkActive="active">Offer
            Management</a>
          <a id="import-pod-tab" *permissionsOnly="view_pod_playground; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="dropdown-item nav-text" [routerLink]="offerPODImportRoute" routerLinkActive="active">Import POD
            Data</a>
        </div>
      </li>
      <li class="px-6">
        <a id="comments-tab" class="nav-item nav-link  nav-text p-0 py-1 " [routerLink]="commentsRoute"
          routerLinkActive="active">Comments</a>
      </li>

      <li class="nav-item dropdown   nav-text  px-6 ">
        <a class="nav-link dropdown-toggle  p-0 py-1" id="navbarDropdown groups-tab" role="button" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="false"
          [ngClass]="isConfigDropDownActive ? 'dropdown-selected active' : 'dropdown-unselected'">
          Groups
        </a>
        <div class="dropdown-menu" aria-labelledby="navbarDropdown">
          <a id="customer-group-tab" class="dropdown-item nav-text" [routerLink]="customerGroupRoute" routerLinkActive="active">Customer
            Groups</a>
          <a id="store-group-tab" class="dropdown-item nav-text" [routerLink]="storeGroupRoute" routerLinkActive="active">Store
            Groups</a>
          <a id="product-group-tab" class="dropdown-item nav-text" [routerLink]="productGroupRoute" routerLinkActive="active">Product
            Groups</a>
          <a id="points-group-tab" class="dropdown-item nav-text" [routerLink]="pointsGroupRoute" routerLinkActive="active">Points
            Groups</a>
        </div>
      </li>
      <ng-container>
        <li *permissionsOnly="viewAdminTabPermission; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="nav-item dropdown   nav-text  px-6 ">
          <a class="nav-link dropdown-toggle  p-0 py-1" id="navbarDropdown admin-tab" role="button" data-toggle="dropdown"
            aria-haspopup="true" aria-expanded="false"
            [ngClass]="isAdminDropDownActive ? 'dropdown-selected active' : 'dropdown-unselected'">
            Admin
          </a>
          <div  class="dropdown-menu menu-box-shadow" aria-labelledby="navbarDropdown">
            <a *permissionsOnly="view_event_maint; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="dropdown-item nav-text" [routerLink]="adminEventMaintenanceRoute" routerLinkActive="active" id="events-tab">Event Maintenance</a>
            <a *permissionsOnly="only_admin; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'"  class="dropdown-item nav-text" [routerLink]="adminOfferDetailsRoute" routerLinkActive="active" id="offer-details-tab">Offer Details</a>
            <a *permissionsOnly="only_admin; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'"  class="dropdown-item nav-text" [routerLink]="adminStoreGroupRoute" routerLinkActive="active" id="admin-store-group-tab">Store Group</a>
            <ng-container *featureFlag="'batchImport'">
            <a *permissionsOnly="viewBatchImportLogPermissions; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'"  class="dropdown-item nav-text" [routerLink]="adminBatchImportRoute" routerLinkActive="active" id="batch-import-tab">Batch Import</a>
             </ng-container>

             <a *permissionsOnly="viewBatchActionLogPermissions; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'"  class="dropdown-item nav-text" [routerLink]="adminBatchActionLogRoute" routerLinkActive="active" id="batch-action-tab">Batch Action Log</a>
             <a *permissionsOnly="only_admin; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="dropdown-item nav-text" [routerLink]="adminScoreCardTemplateRoute" routerLinkActive="active" id="scorecard-tab">Scorecard Template</a>
             <a *permissionsOnly="only_admin; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="dropdown-item nav-text" [routerLink]="adminUsersDetailsRoute" routerLinkActive="active" id="users-tab">Users</a>
             <a *permissionsOnly="only_admin; authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'" class="dropdown-item nav-text" [routerLink]="adminImagePreviewRoute" routerLinkActive="active" id="image-preview-tab">Image Preview</a>

          </div>
        </li>
      </ng-container>
    </ul>
  </div>
  <div class="flex-row col  order-2 order-lg-3" [class.show]="showNav" id="navbarCollapse">
    <ul class="nav justify-content-end py-2">
      <li *ngIf="_authService.authenticated && canShowSG()" class="nav-item">
        <a class="nav-link anchor-link-blue" role="button" (click)="goToUPPStoreGroupMgmt()">PP Store Groups</a>
      </li>
      <li *ngIf="_authService.authenticated && canShowPG()" class="nav-item">
        <a class="nav-link anchor-link-blue" role="button" (click)="goToUPPProductGroupMgmt()">PP Product Groups</a>
      </li>
      <a class="pt-2 anchor-link-blue" href="javascript:void(0)" (click)="goToReports()"> Go To
        Reports</a>
      <li *ngIf="_authService.authenticated" class="nav-item dropdown justify-content-start">

        <a id="userMenu" class="nav-link dropdown-toggle" role="button" aria-haspopup="true" aria-expanded="false"
          data-toggle="dropdown">
          <span>
            <img class='icon mb-2' src='assets/icons/usericon.svg' alt="USER">
          </span>
        </a>
        <div *ngIf="_authService.user" class="dropdown-menu dropdown-menu-right" aria-labelledby="userMenu">

          <p class="dropdown-item">{{ _authService.user.displayName }}</p>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" role="button" (click)="signOut()">Sign Out</a>
        </div>
      </li>
      <li *ngIf="!_authService.authenticated" class="nav-item">
        <a class="nav-link" role="button" (click)="signIn()">Sign In</a>
      </li>
    </ul>
  </div>
</nav>
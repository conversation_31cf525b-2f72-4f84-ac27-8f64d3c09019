@import '../../../../../scss/colors';
@import '../../../../../scss/variables';
.input-size{
  width: 500px;
}

.logo-div-class{
  max-width: 180px;
}

.logo {
  height: 32px;
  width: 65px;
  margin: 0;
  padding: 0;
}
.stick{
  font-size: xx-small;
  text-align: center;
  color: white !important;
}
/* Main Search styles */
.main-search-wrapper {
  button {
    font-size: 15px;
    margin: 0 10px 0 0;
  }
  .base-search {
    .search-icon {
      padding-left: 2px;
    }
  }
}
.full-search {
  flex: 1;
}


.menu {
  color: red;
}

/****************************************************************

                            MENU

****************************************************************/
a:link, a:visited, .dropdown-unselected {
  color: $theme-black;
  
  border-bottom: solid 4px #ffffff;
  -webkit-transition: border-bottom 0.2s, color 0.2s;
  transition: border-bottom 0.2s, color 0.2s;
}
.navbar-nav li a {
  

  &.nav-link {
    margin-right: 30px;
  }
  &.dropdown-toggle {
    margin-right: 0;
  }
}
.dropdown-item.active, .dropdown-item:active {
  color: $theme-black;
  text-decoration: none;
  background-color: #f2f2f2;
}
.nav-text{
  font-size: $nav-item-font-size;
  line-height: 27px;
}
a.active, li a.active, .dropdown-selected {
  color: $theme-primary;
  border-bottom: 2px solid $theme-primary;
  
  font-size: $nav-item-font-size;
  
  line-height: 27px;

}


.icon-bars{
  color:$theme-primary;
  font-size:28px;
}

.dropdown-menu.show {
  margin: 4px 0 0 0;
  padding: 0;
  width: 220px;
  border-radius: 0;
  a.dropdown-item {
    font-weight: normal;
    font-size: $nav-item-dropdow-font-size !important;
    margin-bottom: 0px;
  }
  a.active, li a.active, .dropdown-selected {
   
    border-bottom: none;
    
  }
  &.offer-management-list {
    margin-top:0px;
    min-width: 300px;
    a{
      border-bottom: 1px solid #ccc; 
      padding:2px 25px;
    }
  }
}
.menu-box-shadow {
  box-shadow: 1px 3px 7px 0px #cdd2d6;
}

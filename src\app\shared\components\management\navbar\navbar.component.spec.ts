import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { NavbarComponent } from './navbar.component';
import { AuthService } from '@appServices/common/auth.service';
import { Location } from '@angular/common';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { of } from 'rxjs';

describe('NavbarComponent', () => {
    let component: NavbarComponent;
    let fixture: ComponentFixture<NavbarComponent>;
    let mockRouter = {
        events: of({}),
        navigate: jasmine.createSpy('navigate')
    };
    let mockAuthService = {
        login: jasmine.createSpy('login'),
        logout: jasmine.createSpy('logout')
    };
    let mockLocation = {
        path: jasmine.createSpy('path').and.returnValue('')
    };
    let mockQueryGenerator = {};
    let mockInitialDataService = {
        getConfigUrls: jasmine.createSpy('getConfigUrls').and.returnValue('mockUrl')
    };
    let mockSearchOfferRequestService = {
        myTasksObj: { myTasksText: '' }
    };
    let mockFeatureFlagsService = {
        isUPPFieldSearchEnabled: true
    };

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [NavbarComponent],
            providers: [
                { provide: Router, useValue: mockRouter },
                { provide: AuthService, useValue: mockAuthService },
                { provide: Location, useValue: mockLocation },
                { provide: QueryGenerator, useValue: mockQueryGenerator },
                { provide: InitialDataService, useValue: mockInitialDataService },
                { provide: SearchOfferRequestService, useValue: mockSearchOfferRequestService },
                { provide: FeatureFlagsService, useValue: mockFeatureFlagsService }
            ]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(NavbarComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should toggle navbar visibility', () => {
        component.showNav = false;
        component.toggleNavBar();
        expect(component.showNav).toBeTrue();
        component.toggleNavBar();
        expect(component.showNav).toBeFalse();
    });

    it('should call AuthService login on signIn', async () => {
        await component.signIn();
        expect(mockAuthService.login).toHaveBeenCalled();
    });

    it('should call AuthService logout on signOut', () => {
        component.signOut();
        expect(mockAuthService.logout).toHaveBeenCalled();
    });

    it('should navigate to create offer page', () => {
        component.createOfferButton = true;
        component.create();
        expect(mockRouter.navigate).toHaveBeenCalledWith([ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Create]);
    });

    it('should navigate to create request page', () => {
        component.createOfferButton = false;
        component.create();
        expect(mockRouter.navigate).toHaveBeenCalledWith([ROUTES_CONST.REQUEST.Request + '/' + ROUTES_CONST.REQUEST.RequestForm + '/' + ROUTES_CONST.REQUEST.Create]);
    });

    it('should toggle buttons based on currentActiveRouteUrl', () => {
        component.currentActiveRouteUrl = '/' + ROUTES_CONST.GROUPS.CustomerGroup + '/';
        component.toggleButtons();
        expect(component.showOfferRequestOptions).toBeTrue();
        expect(component.createOfferButton).toBeFalse();
        expect(component.createRequestButton).toBeTrue();
    });

    it('should return true for canShowSG if currentActiveRouteUrl includes StoreGroup and isUPPFieldSearchEnabled is true', () => {
        component.currentActiveRouteUrl = '/' + ROUTES_CONST.GROUPS.StoreGroup;
        expect(component.canShowSG()).toBeTrue();
    });

    it('should return true for canShowPG if currentActiveRouteUrl includes ProductGroup and isUPPFieldSearchEnabled is true', () => {
        component.currentActiveRouteUrl = '/' + ROUTES_CONST.GROUPS.ProductGroup;
        expect(component.canShowPG()).toBeTrue();
    });

    it('should call getConfigUrls and openInNewTab for goToReports', () => {
        spyOn(component, 'openInNewTab');
        component.goToReports();
        expect(component.openInNewTab).toHaveBeenCalledWith('mockUrl');
    });

    it('should call getConfigUrls and openInNewTab for goToUPPStoreGroupMgmt', () => {
        spyOn(component, 'openInNewTab');
        component.goToUPPStoreGroupMgmt();
        expect(component.openInNewTab).toHaveBeenCalledWith('mockUrl');
    });

    it('should call getConfigUrls and openInNewTab for goToUPPProductGroupMgmt', () => {
        spyOn(component, 'openInNewTab');
        component.goToUPPProductGroupMgmt();
        expect(component.openInNewTab).toHaveBeenCalledWith('mockUrl');
    });

    it('should call toggleButtons on ngOnChanges', () => {
        spyOn(component, 'toggleButtons');
        component.ngOnChanges();
        expect(component.toggleButtons).toHaveBeenCalled();
    });

    it('should sort items by label in ascending order', () => {
        const items = [
            { label: 'lab1' },
            { label: 'lab2' },
            { label: 'lab3' }
        ];
        const sortedItems = [
            { label: 'lab1' },
            { label: 'lab2' },
            { label: 'lab3' }
        ];
        component.sortItemList(items);
        expect(items).toEqual(sortedItems);
    });
});
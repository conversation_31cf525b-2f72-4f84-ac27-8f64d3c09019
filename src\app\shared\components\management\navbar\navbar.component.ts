import { Component, Input, OnInit } from '@angular/core';
import { ROUTES_CONST } from '@appConstants/routes_constants';

import { AuthService } from '@appServices/common/auth.service';

import { Location } from '@angular/common';

import { Router } from '@angular/router';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';

import { CONSTANTS } from '@appConstants/constants';
import { offerRequestModel } from '@appModels/offer-request.model';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss']
})
export class NavbarComponent implements OnInit {
  createOfferButton;
  createRequestButton;
  showOfferRequestOptions: boolean = true;
  searchInput: string;
  // Is a user logged in?
  authenticated: boolean;
  // The user
  user: any;
  // Should the collapsed nav show?
  showNav: boolean;
  isConfigDropDownActive = false;
  isOfferDropDownActive = false;
  isAdminDropDownActive = false;
  isRequestDropDownActive = false;
  isTemplateDropDownActive = false;
  route: string;

  requestRoute = ROUTES_CONST.REQUEST.Request;
  templateRoute = ROUTES_CONST.TEMPLATES.Template;
  pluManagementRoute = ROUTES_CONST.REQUEST.Request + '/' + ROUTES_CONST.REQUEST.PluManagement;
  offerSearchRoute = ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Management;
  offerPODPlaygroundRoute = ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.PodPlayground;
  offerPODImportRoute = ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.PodImport + '/' + ROUTES_CONST.OFFERS.PodImportData;
  commentsRoute = ROUTES_CONST.COMMENTS.Comments + '/' + ROUTES_CONST.COMMENTS.ViewAll;
  customerGroupRoute = ROUTES_CONST.GROUPS.Group+'/'+ROUTES_CONST.GROUPS.CustomerGroup;
  storeGroupRoute = ROUTES_CONST.GROUPS.Group+'/'+ROUTES_CONST.GROUPS.StoreGroup;
  productGroupRoute = ROUTES_CONST.GROUPS.Group+'/'+ROUTES_CONST.GROUPS.ProductGroup;
  pointsGroupRoute = ROUTES_CONST.GROUPS.Group+'/'+ROUTES_CONST.GROUPS.PointsGroup;

  adminEventMaintenanceRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.EventMaintenance;
  adminOfferDetailsRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.OfferDetails;
  adminStoreGroupRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.StoreGroup;
  adminBatchImportRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.BatchImport;
  adminBatchActionLogRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.ActionLog;
  adminScoreCardTemplateRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.ScoreCardTemplate;
  adminUsersDetailsRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.UsersDetails;
  adminImagePreviewRoute = ROUTES_CONST.ADMIN.Admin + '/' + ROUTES_CONST.ADMIN.ImagePreview;



  linkToReports: string = this.initialDataService.getConfigUrls(CONSTANTS.LINK_TO_REPORTS);
  @Input("currentActiveRoute") currentActiveRouteUrl;

  allowedPermissions = [CONSTANTS.Permissions.ViewPodPlayGround];
  view_Offer_Request = [CONSTANTS.Permissions.ViewAdmin,CONSTANTS.Permissions.ViewOfferRequests,CONSTANTS.Permissions.ViewGRSPDRequest];
  view_event_maint = [CONSTANTS.Permissions.ViewEventMaint];
  view_pod_playground = [CONSTANTS.Permissions.ViewPodPlayGround];
  manage_event_maint = [CONSTANTS.Permissions.ManageEventMaint];
  only_admin = [CONSTANTS.Permissions.Admin];
  view_offer_template_request=[CONSTANTS.Permissions.Admin,CONSTANTS.Permissions.ViewBPDOfferRequest];

  /** Allow GR_SPD_REQUESTER To see batch Action log and Batch Import log */
  viewAdminTabPermission = [CONSTANTS.Permissions.ViewAdmin,CONSTANTS.Permissions.EditGRSPDRequest, CONSTANTS.Permissions.ManageBatchImportOffers ]
  //viewBatchImportLogPermissions = [CONSTANTS.Permissions.Admin,CONSTANTS.Permissions.ViewBatchImportAction,CONSTANTS.Permissions.EditGRSPDRequest]
  //viewBatchActionLogPermissions = [CONSTANTS.Permissions.ViewBatchActionLog,CONSTANTS.Permissions.Admin ,CONSTANTS.Permissions.EditGRSPDRequest ]
  viewBatchImportLogPermissions = []
  viewBatchActionLogPermissions = []

  public offerRequests: offerRequestModel[];
  constructor(
    private _router: Router,
    public _authService: AuthService,
    location: Location,
    public _queryGenerator: QueryGenerator,
    private initialDataService: InitialDataService,
    public _searchOfferRequestService: SearchOfferRequestService,
    private _featureFlagService: FeatureFlagsService
  ) {
    this.offerRequests = [];
    _router.events.subscribe(val => {
      if (location.path() !== '') {
        this.route = location.path();
        this.isConfigDropDownActive = this.route.startsWith('/'+ROUTES_CONST.GROUPS.Group);
          
        this.isOfferDropDownActive = this.route.startsWith('/' + ROUTES_CONST.OFFERS.Offers + '/');
        this.isAdminDropDownActive = this.route.startsWith('/' + ROUTES_CONST.ADMIN.Admin + '/');
        this.isRequestDropDownActive = this.route.startsWith('/' + ROUTES_CONST.REQUEST.Request);
        this.isTemplateDropDownActive = this.route.startsWith('/' + ROUTES_CONST.TEMPLATES.Template);
        //|| this.route.startsWith(ROUTES_CONST.REQUEST.Request + '/' + ROUTES_CONST.REQUEST.PluManagement);
        this._searchOfferRequestService.myTasksObj.myTasksText = `My Tasks`;
      }
    });
  }

  sortItemList(items) {
    items.sort((a, b) => {
      return a.label.toUpperCase() > b.label.toUpperCase() ? 1 : -1;
    });
  }

  ngOnInit() {
    this.showNav = false;

  }

  ngOnChanges() {
    this.toggleButtons();
  }
  // Used by the Bootstrap navbar-toggler button to hide/show
  // the nav in a collapsed state

  toggleNavBar(): void {
    this.showNav = !this.showNav;
  }

  toggleButtons() {
    //Based on the current page, manage the dispaly of buttons
    this.showOfferRequestOptions = this.currentActiveRouteUrl.includes('/' + ROUTES_CONST.GROUPS.CustomerGroup + '/')
      || this.currentActiveRouteUrl.includes('/' + ROUTES_CONST.GROUPS.StoreGroup + '/')
      || this.currentActiveRouteUrl.includes('/' + ROUTES_CONST.GROUPS.PointsGroup + '/')
      || this.currentActiveRouteUrl.includes('/' + ROUTES_CONST.GROUPS.ProductGroup + '/')
    this.createOfferButton = this.currentActiveRouteUrl.endsWith(ROUTES_CONST.OFFERS.Offers);
    this.createRequestButton = !this.createOfferButton;
  }
  canShowSG()
  {
    return this.currentActiveRouteUrl.includes('/' + ROUTES_CONST.GROUPS.StoreGroup) && this._featureFlagService.isUPPFieldSearchEnabled;
  }
  canShowPG()
  {
    return this.currentActiveRouteUrl.includes('/' + ROUTES_CONST.GROUPS.ProductGroup)  && this._featureFlagService.isUPPFieldSearchEnabled;
  }
  create() {
    if (this.createOfferButton) {
      this._router.navigate([ROUTES_CONST.OFFERS.Offers + '/' + ROUTES_CONST.OFFERS.Create]);
    } else {
      this._router.navigate([ROUTES_CONST.REQUEST.Request + '/' + ROUTES_CONST.REQUEST.RequestForm + '/' + ROUTES_CONST.REQUEST.Create]);
    }
  }

  async signIn(): Promise<void> {
    this._authService.login();
  }

  signOut(): void {
    this._authService.logout();
  }
  goToReports() {
    let reportUrl = this.linkToReports;
    this.openInNewTab(reportUrl);
  }

  goToUPPStoreGroupMgmt()
  {
    let sgUrl = this.initialDataService.getConfigUrls(CONSTANTS.UPP_STORE_GROUP_URL)
    this.openInNewTab(sgUrl);
  }
  goToUPPProductGroupMgmt()
  {
    let sgUrl = this.initialDataService.getConfigUrls(CONSTANTS.UPP_PRODUCT_GROUP_URL)
    this.openInNewTab(sgUrl);
  }
  openInNewTab(url) {
    const win = window.open(url, '_blank');
    win.focus();
  }
}

import { ComponentFixture, TestBed, fakeAsync, tick, flush } from '@angular/core/testing';
import { PaginationComponent } from './pagination.component';
import { of, Subject, BehaviorSubject, skip } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';

// Import the actual service classes
import { CustomerGroupService } from '@appGroupsServices/customer-group.service';
import { PointGroupService } from '@appGroupsServices/point-group.service';
import { ProductGroupService } from '@appGroupsServices/product-group.service';
import { ActionLogService } from '@appModules/admin/shared/services/actionLog.service';
import { TechSupportService } from '@appModules/tech-support-dashboard/services/tech-support-service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { PluSearchService } from "@appRequestServices/pluSearch.service";
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { IviePromotionService } from '@appServices/common/ivie-promotion.service';
import { PrintAdService } from '@appServices/common/print-ad.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BaseManagementService } from '@appServices/management/base-management.service';
import { BulkUpdateService } from "@appServices/management/bulk-update.service";

// Import MSAL tokens from MSAL Angular
import { MSAL_GUARD_CONFIG, MsalBroadcastService } from '@azure/msal-angular';

// Define a minimal fake subject for testing purposes.
class FakeSubject<T> {
  public callback: ((value: T) => void) | null = null;
  public nextCalls: T[] = [];
  
  subscribe(cb: (value: T) => void) {
    this.callback = cb;
    return { unsubscribe: () => {} };
  }
  
  next(value: T) {
    this.nextCalls.push(value);
  }
  
  // Manually trigger the captured callback.
  trigger(value: T) {
    if (this.callback) {
      this.callback(value);
    }
  }
}


describe('PaginationComponent', () => {
    let component: PaginationComponent;
    let fixture: ComponentFixture<PaginationComponent>;

    // Declare the spy objects
    let customerGroupServiceSpy: jasmine.SpyObj<CustomerGroupService>;
    let pointGroupServiceSpy: jasmine.SpyObj<PointGroupService>;
    let productGroupServiceSpy: jasmine.SpyObj<ProductGroupService>;
    let actionLogServiceSpy: jasmine.SpyObj<ActionLogService>;
    let techSupportServiceSpy: jasmine.SpyObj<TechSupportService>;
    let searchOfferServiceSpy: jasmine.SpyObj<SearchOfferService>;
    let pluSearchServiceSpy: jasmine.SpyObj<PluSearchService>;
    let searchOfferRequestServiceSpy: jasmine.SpyObj<SearchOfferRequestService>;
    let authServiceSpy: jasmine.SpyObj<AuthService>;
    let commonSearchServiceSpy: jasmine.SpyObj<CommonSearchService>;
    let commonServiceSpy: jasmine.SpyObj<CommonService>;
    let facetItemServiceSpy: jasmine.SpyObj<FacetItemService>;
    let fileAttachServiceSpy: jasmine.SpyObj<FileAttachService>;
    let iviePromotionServiceSpy: jasmine.SpyObj<IviePromotionService>;
    let printAdServiceSpy: jasmine.SpyObj<PrintAdService>;
    let queryGeneratorSpy: jasmine.SpyObj<QueryGenerator>;
    let baseManagementServiceSpy: jasmine.SpyObj<BaseManagementService>;
    let bulkUpdateServiceSpy: jasmine.SpyObj<BulkUpdateService>;

    beforeEach(() => {
        // Create spies using jasmine.createSpyObj, initializing observable properties with Subjects.
        customerGroupServiceSpy = jasmine.createSpyObj('CustomerGroupService', ['getCustomerGroupByName'], { customerGroupSource: new Subject<any>() });
        pointGroupServiceSpy = jasmine.createSpyObj('PointGroupService', ['searchPointGroup', 'getAllPointGroupSearch']);
        productGroupServiceSpy = jasmine.createSpyObj('ProductGroupService', [], { paginationRender: new Subject<boolean>() });
        actionLogServiceSpy = jasmine.createSpyObj('ActionLogService', ['importLog'], { bpdImportLogData$: new Subject<any>(), actionLogData$: new Subject<any>() });
        techSupportServiceSpy = jasmine.createSpyObj('TechSupportService', ['getPushedEventResults', 'updateList']);
        searchOfferServiceSpy = jasmine.createSpyObj('SearchOfferService', ['searchAllOffers']);
        pluSearchServiceSpy = jasmine.createSpyObj('PluSearchService', ['fetchPluList'], { pluManagementPagination$: new Subject<any>() });
        searchOfferRequestServiceSpy = jasmine.createSpyObj('SearchOfferRequestService', ['getOfferDetails', 'searchAllOfferRequest'], { homeFilterPaginationSourceSearch: new Subject<any>() });
        authServiceSpy = jasmine.createSpyObj('AuthService', ['onUserDataAvailable']);
        commonSearchServiceSpy = jasmine.createSpyObj('CommonSearchService', ['setQueryValueForDefaultOption', 'getActiveCurrentSearchType']);
        commonServiceSpy = jasmine.createSpyObj('CommonService', [], { passPaginationData$: new Subject<any>() });
        facetItemServiceSpy = jasmine.createSpyObj('FacetItemService', [], { programCodeSelected: 'BPD' });
        fileAttachServiceSpy = jasmine.createSpyObj('FileAttachService', ['importLog', 'importLogBpd', 'getToken', 'importPALog'], { batchImportLogResponsePA$: new Subject<any>() });
        iviePromotionServiceSpy = jasmine.createSpyObj('IviePromotionService', ['searchAllPromotions', 'getPaginationSearch'], { searchPromotionPage: new Subject<any>() });
        printAdServiceSpy = jasmine.createSpyObj('PrintAdService', ['importLog'], { importLogResponse: new Subject<any>() });
        queryGeneratorSpy = jasmine.createSpyObj('QueryGenerator', ['getQuery', 'getQueryWithFilter', 'removeParameters', 'pushParameters', 'removeParam', 'pushParam']);
        baseManagementServiceSpy = jasmine.createSpyObj('BaseManagementService', ['fetchPaginationData']);
        bulkUpdateServiceSpy = jasmine.createSpyObj('BulkUpdateService', [], {
            requestIdsListSelected$: new Subject<any>(),
            offerIdsListSelected$: new Subject<any>(),
            offerBulkSelection: new Subject<any>(),
            bulkSelectionForOffers: new Subject<any>()
        });
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [PaginationComponent],
            providers: [
                { provide: CustomerGroupService, useValue: customerGroupServiceSpy },
                { provide: PointGroupService, useValue: pointGroupServiceSpy },
                { provide: ProductGroupService, useValue: productGroupServiceSpy },
                { provide: ActionLogService, useValue: actionLogServiceSpy },
                { provide: TechSupportService, useValue: techSupportServiceSpy },
                { provide: SearchOfferService, useValue: searchOfferServiceSpy },
                { provide: PluSearchService, useValue: pluSearchServiceSpy },
                { provide: SearchOfferRequestService, useValue: searchOfferRequestServiceSpy },
                { provide: AuthService, useValue: authServiceSpy },
                { provide: CommonSearchService, useValue: commonSearchServiceSpy },
                { provide: CommonService, useValue: commonServiceSpy },
                { provide: FacetItemService, useValue: facetItemServiceSpy },
                { provide: FileAttachService, useValue: fileAttachServiceSpy },
                { provide: IviePromotionService, useValue: iviePromotionServiceSpy },
                { provide: PrintAdService, useValue: printAdServiceSpy },
                { provide: QueryGenerator, useValue: queryGeneratorSpy },
                { provide: BaseManagementService, useValue: baseManagementServiceSpy },
                { provide: BulkUpdateService, useValue: bulkUpdateServiceSpy },
                { provide: MSAL_GUARD_CONFIG, useValue: {} },
                { provide: MsalBroadcastService, useValue: {} }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(PaginationComponent);
        component = fixture.componentInstance;
        // Set default input values for testing.
        component.paginationTotalCount = 50;
        component.paginationPageNumber = 2;
        component.headerPage = 'homePage';
        component.gridName = 'testGrid';
        // For consistent calculations, set pageItems (usually a constant from CONSTANTS).
        component.pageItems = 10;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('initialConfig', () => {
        it('should correctly configure pagination properties', () => {
            const totalCount = 95;
            const pageNumber = 3;
            const sid = 'testSid';

            commonSearchServiceSpy.setQueryValueForDefaultOption.calls.reset();

            component.initialConfig(totalCount, pageNumber, sid);

            expect(component.paginationTotalCount).toEqual(totalCount);
            expect(component.paginationPageNumber).toEqual(pageNumber);
            // Access private sid via type cast.
            expect((component as any).sid).toEqual(sid);
            expect(commonSearchServiceSpy.setQueryValueForDefaultOption)
                .toHaveBeenCalledWith(CONSTANTS.SID, sid);

            const expectedStart = (pageNumber - 1) * component.pageItems + 1;
            const lastItem = expectedStart + component.pageItems - 1;
            const expectedEnd = lastItem > totalCount ? totalCount : lastItem;

            expect(component.paginationItemStart).toEqual(expectedStart);
            expect(component.paginationItemEnd).toEqual(expectedEnd);
        });
    });

    describe('checkCountNext', () => {
        it('should return true if paginationTotalCount is not equal to paginationItemEnd', () => {
            component.paginationTotalCount = 100;
            component.paginationItemEnd = 90;
            expect(!!component.checkCountNext()).toBeTrue();
        });
        it('should return false if paginationTotalCount equals paginationItemEnd', () => {
            component.paginationTotalCount = 50;
            component.paginationItemEnd = 50;
            expect(component.checkCountNext()).toBeFalse();
        });
    });

    describe('checkCountPrev', () => {
        it('should return true if paginationPageNumber is not 1 and paginationTotalCount exists', () => {
            component.paginationPageNumber = 3;
            component.paginationTotalCount = 100;
            // Convert the result to boolean
            expect(!!component.checkCountPrev()).toBeTrue();
        });
        it('should return false if paginationPageNumber is 1', () => {
            component.paginationPageNumber = 1;
            component.paginationTotalCount = 100;
            expect(!!component.checkCountPrev()).toBeFalse();
        });
    });

    describe('prevoius', () => {
        it('should decrement paginationPageNumber and call updateParams when headerPage is not "groupId" or "offerPODPage"', () => {
            component.headerPage = 'homePage';
            component.paginationPageNumber = 3;
            spyOn(component, 'updateParams');
            spyOn(bulkUpdateServiceSpy.requestIdsListSelected$, 'next');
            spyOn(bulkUpdateServiceSpy.offerIdsListSelected$, 'next');

            component.prevoius();

            expect(component.paginationPageNumber).toEqual(2);
            expect(component.updateParams).toHaveBeenCalledWith(2);
        });

        it('should emit prevoiusHandler event when headerPage is "groupId"', () => {
            component.headerPage = 'groupId';
            component.paginationPageNumber = 3;
            spyOn(component.prevoiusHandler, 'emit');

            component.prevoius();

            expect(component.prevoiusHandler.emit).toHaveBeenCalledWith({ pageNo: 2, gridName: component.gridName });
        });
    });

    describe('next', () => {
        it('should increment paginationPageNumber and call updateParams when headerPage is not "groupId" or "offerPODPage"', () => {
            component.headerPage = 'homePage';
            component.paginationPageNumber = 3;
            spyOn(component, 'updateParams');
            spyOn(bulkUpdateServiceSpy.requestIdsListSelected$, 'next');
            spyOn(bulkUpdateServiceSpy.offerIdsListSelected$, 'next');

            component.next();

            expect(component.paginationPageNumber).toEqual(4);
            expect(component.updateParams).toHaveBeenCalledWith(4);
        });

        it('should emit nextHandler event when headerPage is "offerPODPage"', () => {
            component.headerPage = 'offerPODPage';
            component.paginationPageNumber = 3;
            spyOn(component.nextHandler, 'emit');

            // Create a dummy div element that satisfies the Element interface.
            const dummyElement = document.createElement('div');
            dummyElement.scrollTop = 100;
  
            // Spy on getElementsByClassName to return a collection containing the dummy element.
            spyOn(document, 'getElementsByClassName').and.returnValue([dummyElement] as any);

            component.next();

            expect(component.nextHandler.emit).toHaveBeenCalledWith({ pageNo: 4, gridName: component.gridName });
        });


    });

    describe('updateParams', () => {
        it('should call fetchNewPageData with correct arguments', () => {
            spyOn(component, 'fetchNewPageData');
            (component as any).sid = 'testSid';
            component.headerPage = 'homePage';
            component.updateParams(3);

            const limit = CONSTANTS.LIMIT;
            const expectedRemoveList = [limit];
            const expectedParamsList = [
                { remove: true, parameter: CONSTANTS.NEXT, value: 3 },
                { remove: true, parameter: CONSTANTS.SID, value: 'testSid' },
                { remove: true, parameter: limit, value: CONSTANTS.PAGE_LIMIT }
            ];

            expect(component.fetchNewPageData).toHaveBeenCalledWith(expectedRemoveList, expectedParamsList);
        });

    });

    describe('searchAllOffersApi', () => {
        it('should call searchAllOffers and then getOfferDetails with the result', fakeAsync(() => {
            const fakeQuery = 'fakeQuery';
            const fakeQueryWithFilter = 'fakeQueryWithFilter';
            const result = { pagination: true };
            queryGeneratorSpy.getQuery.and.returnValue(fakeQuery);
            queryGeneratorSpy.getQueryWithFilter.and.returnValue(fakeQueryWithFilter);
            searchOfferServiceSpy.searchAllOffers.and.returnValue(of(result));

            component.searchAllOffersApi();
            tick();

            expect(searchOfferServiceSpy.searchAllOffers).toHaveBeenCalledWith(fakeQuery, false, fakeQueryWithFilter);
            expect(searchOfferRequestServiceSpy.getOfferDetails).toHaveBeenCalledWith(result);
            flush();
        }));
    });

    describe('fetchNewPageData', () => {
        it('should call fetchPluList when headerPage is "pluManagement"', () => {
            component.headerPage = 'pluManagement';
            component.fetchNewPageData([], []);
            expect(pluSearchServiceSpy.fetchPluList).toHaveBeenCalledWith({});
        });

        it('should call techSupportService.getPushedEventResults when headerPage is "tech-support"', fakeAsync(() => {
            component.headerPage = 'tech-support';
            const data = { totalCount: 100, current: 5, sid: 'techSid' };
            queryGeneratorSpy.getQuery.and.returnValue('techQuery');
            techSupportServiceSpy.getPushedEventResults.and.returnValue(of(data));
            spyOn(commonServiceSpy.passPaginationData$, 'next');

            component.fetchNewPageData([], []);
            tick();

            expect(techSupportServiceSpy.getPushedEventResults).toHaveBeenCalledWith('techQuery');
            expect(techSupportServiceSpy.updateList).toHaveBeenCalledWith(data);
            expect(commonServiceSpy.passPaginationData$.next).toHaveBeenCalledWith({ totalCount: data.totalCount, pageNumber: data.current, sid: data.sid });
            flush();
        }));

        it('should call baseManagementService.fetchPaginationData when headerPage is "productGroupManagement"', () => {
            component.headerPage = 'productGroupManagement';
            component.fetchNewPageData([], []);
            expect(baseManagementServiceSpy.fetchPaginationData).toHaveBeenCalledWith(false);
        });

        it('should call customerGroupService.getCustomerGroupByName when headerPage is "customerGroup"', fakeAsync(() => {
            component.headerPage = 'customerGroup';
            const result = { pagination: true };
            queryGeneratorSpy.getQuery.and.returnValue('custQuery');
            customerGroupServiceSpy.getCustomerGroupByName.and.returnValue(of(result));
            (customerGroupServiceSpy.customerGroupSource as Subject<any>).next = jasmine.createSpy('next');

            component.fetchNewPageData([], []);
            tick();

            expect(customerGroupServiceSpy.getCustomerGroupByName).toHaveBeenCalledWith('custQuery', false);
            expect(customerGroupServiceSpy.customerGroupSource.next).toHaveBeenCalledWith(jasmine.objectContaining(result));
            flush();
        }));

        it('should call authService.onUserDataAvailable when headerPage is "offerHomePage"', () => {
            component.headerPage = 'offerHomePage';
            authServiceSpy.onUserDataAvailable.and.callFake((fn: Function) => fn());
            spyOn(component, 'searchAllOffersApi');
            component.fetchNewPageData([], []);
            expect(authServiceSpy.onUserDataAvailable).toHaveBeenCalled();
            expect(component.searchAllOffersApi).toHaveBeenCalled();
        });

        it('should call pointGroupService.searchPointGroup when headerPage is "pointGroup"', fakeAsync(() => {
            component.headerPage = 'pointGroup';
            const result = { pagination: true };
            queryGeneratorSpy.getQuery.and.returnValue('pgQuery');
            pointGroupServiceSpy.searchPointGroup.and.returnValue(of(result));
            component.fetchNewPageData([], []);
            tick();
            expect(pointGroupServiceSpy.searchPointGroup).toHaveBeenCalledWith('pgQuery');
            flush();
        }));

        it('should call iviePromotionService.searchAllPromotions when headerPage is "offerPODPage"', fakeAsync(() => {
            component.headerPage = 'offerPODPage';
            queryGeneratorSpy.removeParam.and.stub();
            queryGeneratorSpy.pushParam.and.stub();
            queryGeneratorSpy.getQuery.and.returnValue('promoQuery');
            const result = { pagination: true };
            iviePromotionServiceSpy.searchAllPromotions.and.returnValue(of(result));
            component.fetchNewPageData([], []);
            tick();
            expect(iviePromotionServiceSpy.searchAllPromotions).toHaveBeenCalledWith('promoQuery');
            expect(iviePromotionServiceSpy.getPaginationSearch).toHaveBeenCalledWith(result);
            flush();
        }));

        it('should call printAdService.importLog when headerPage is "podImport"', fakeAsync(() => {
            component.headerPage = 'podImport';
            const query = 'importQuery';
            queryGeneratorSpy.getQuery.and.returnValue(query);
            const result = {};
            printAdServiceSpy.importLog.and.returnValue(of(result));
            printAdServiceSpy.importLogResponse.next = jasmine.createSpy('next');
            component.fetchNewPageData([], []);
            tick();
            expect(printAdServiceSpy.importLog).toHaveBeenCalledWith(query);
            expect(printAdServiceSpy.importLogResponse.next).toHaveBeenCalledWith(jasmine.objectContaining(result));

        
            flush();
        }));

        it('should call fileAttachService.importLog when headerPage is "batchImportLog"', fakeAsync(() => {
            component.headerPage = 'batchImportLog';
            const query = 'batchQuery';
            queryGeneratorSpy.getQuery.and.returnValue(query);
            const result = {};
            fileAttachServiceSpy.importLog.and.returnValue(of(result));
            const batchResponseSubject = new Subject<any>();
            spyOn(batchResponseSubject, 'next');
            fileAttachServiceSpy.batchImportLogResponse$ = batchResponseSubject;

            component.fetchNewPageData([], []);
            tick();
            expect(fileAttachServiceSpy.importLog).toHaveBeenCalledWith(query);
            flush();
        }));

        it('should call baseManagementService.fetchPaginationData when headerPage is "import-log-bpd"', fakeAsync(() => {
            component.headerPage = 'import-log-bpd';
            baseManagementServiceSpy.fetchPaginationData.and.returnValue(undefined);
  
            component.fetchNewPageData([], []);
            tick();
  
            expect(baseManagementServiceSpy.fetchPaginationData).toHaveBeenCalledWith(false);
            flush();
        }));

        it('should call baseManagementService.fetchPaginationData when headerPage is "actionLog"', fakeAsync(() => {
            component.headerPage = 'actionLog';
            baseManagementServiceSpy.fetchPaginationData.and.returnValue(undefined);

            component.fetchNewPageData([], []);
            tick();

            expect(baseManagementServiceSpy.fetchPaginationData).toHaveBeenCalledWith(false);
            flush();
        }));


        it('should call handleImportLogPA when headerPage is "batchImportLogPA"', () => {
            component.headerPage = 'batchImportLogPA';
            spyOn(component, 'handleImportLogPA');
            component.fetchNewPageData([], []);
            expect(component.handleImportLogPA).toHaveBeenCalled();
        });
    });

    describe('handleImportLogPA', () => {
        it('should update paginationPageNumber, call queryGenerator methods, and process token and importPALog', fakeAsync(() => {
            component.paginationPageNumber = '3';
            const tokenResponse = { access_token: 'testToken' };
            fileAttachServiceSpy.getToken.and.returnValue(of(tokenResponse));
            const query = 'logQuery';
            queryGeneratorSpy.getQuery.and.returnValue(query);
            const result = {};
            fileAttachServiceSpy.importPALog.and.returnValue(of(result));
            fileAttachServiceSpy.batchImportLogResponsePA$.next = jasmine.createSpy('next');

            component.handleImportLogPA();
            tick();

            expect(component.paginationPageNumber).toEqual(2);
            expect(queryGeneratorSpy.removeParam).toHaveBeenCalledWith('next');
            expect(queryGeneratorSpy.pushParameters).toHaveBeenCalled();
            expect(fileAttachServiceSpy.getToken).toHaveBeenCalled();
            expect(fileAttachServiceSpy.importPALog).toHaveBeenCalledWith(query, tokenResponse.access_token);
            expect(fileAttachServiceSpy.batchImportLogResponsePA$.next).toHaveBeenCalledWith(result);
            flush();
        }));
    });

    describe('ngOnInit & ngOnChanges', () => {
        it('should subscribe to homeFilterPaginationSourceSearch and call initialConfig when headerPage is not "groupId"', fakeAsync(() => {
            component.headerPage = 'homePage';
            const testData = { totalCount: 80, pageNumber: 4, sid: 'sid123' };
            spyOn(component, 'initialConfig');
            (searchOfferRequestServiceSpy.homeFilterPaginationSourceSearch as Subject<any>).next(testData);
            component.ngOnInit();
            tick();
            expect(component.initialConfig).toHaveBeenCalledWith(testData.totalCount, testData.pageNumber, testData.sid);
            flush();
        }));

        it('should call initialConfig directly when headerPage is "groupId"', () => {
            component.headerPage = 'groupId';
            component.paginationTotalCount = 100;
            component.paginationPageNumber = 5;
            spyOn(component, 'initialConfig');
            component.ngOnInit();
            expect(component.initialConfig).toHaveBeenCalledWith(100, 5, '');
        });

        it('should subscribe to productGroupService.paginationRender in ngOnChanges', fakeAsync(() => {
            component.headerPage = 'groupId';
            component.paginationTotalCount = 60;
            component.paginationPageNumber = 2;
            spyOn(component, 'initialConfig');
            component.ngOnChanges();
            (productGroupServiceSpy.paginationRender as Subject<boolean>).next(true);
            tick();
            expect(component.initialConfig).toHaveBeenCalledWith(60, 2, '');
            flush();
        }));

        it('should subscribe to iviePromotionService.searchPromotionPage in initSubscribes and call updateParams when paginationPageNumber is finite', fakeAsync(() => {
            component.paginationPageNumber = 3;
            spyOn(component, 'updateParams');
            (iviePromotionServiceSpy.searchPromotionPage as Subject<any>).next(true);
            tick();
            expect(component.updateParams).toHaveBeenCalledWith(3);
            flush();
        }));

        it('should subscribe to pluSearchService.pluManagementPagination$ in initSubscribes and call initialConfig', fakeAsync(() => {
            const data = { totalCount: 70, pageNumber: 2, sid: 'pluSid' };
            spyOn(component, 'initialConfig');
            (pluSearchServiceSpy.pluManagementPagination$ as Subject<any>).next(data);
            tick();
            expect(component.initialConfig).toHaveBeenCalledWith(data.totalCount, data.pageNumber, data.sid);
            flush();
        }));

        it('should subscribe to commonService.passPaginationData$ in initSubscribes and call initialConfig', fakeAsync(() => {
            const data = { totalCount: 90, pageNumber: 3, sid: 'commonSid' };
            spyOn(component, 'initialConfig');
            (commonServiceSpy.passPaginationData$ as Subject<any>).next(data);
            tick();
            expect(component.initialConfig).toHaveBeenCalledWith(data.totalCount, data.pageNumber, data.sid);
            flush();
        }));
    });

    it('should set scrollTop to 0 when headerPage is "offerPODPage"', () => {
        component.headerPage = 'offerPODPage';
  
        const dummyElement = document.createElement('div');
        dummyElement.scrollTop = 100;
  
        spyOn(document, 'getElementsByClassName').and.returnValue([dummyElement] as any);
  
        component.prevoius();
  
        expect(dummyElement.scrollTop).toEqual(0);
    });

    it('should call offerIdsListSelected$.next([]) when headerPage is "offerHomePage"', () => {
        component.headerPage = 'offerHomePage';
        const nextSpy = spyOn(bulkUpdateServiceSpy.offerIdsListSelected$, 'next');
  
        component.prevoius();
  
        expect(nextSpy).toHaveBeenCalledWith([]);
    });

});

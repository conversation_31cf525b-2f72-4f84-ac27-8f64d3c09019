import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';

import { CONSTANTS } from '@appConstants/constants';

import { CustomerGroupService } from '@appGroupsServices/customer-group.service';
import { PointGroupService } from '@appGroupsServices/point-group.service';
import { ProductGroupService } from '@appGroupsServices/product-group.service';
import { ActionLogService } from '@appModules/admin/shared/services/actionLog.service';
import { TechSupportService } from '@appModules/tech-support-dashboard/services/tech-support-service';
import { SearchOfferService } from '@appOffersServices/search-offer.service';
import { PluSearchService } from "@appRequestServices/pluSearch.service";
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FileAttachService } from '@appServices/common/file-attach.service';
import { IviePromotionService } from '@appServices/common/ivie-promotion.service';
import { PrintAdService } from '@appServices/common/print-ad.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { BaseManagementService } from '@appServices/management/base-management.service';
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";

@Component({
  selector: 'pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss']
})
export class PaginationComponent extends UnsubscribeAdapter implements OnInit, OnChanges {

  @Input() paginationItemStart;
  @Input() paginationItemEnd;
  @Input() paginationTotalCount;
  @Input() paginationPageNumber;
  @Input() headerPage;
  @Input() gridName;
  @Output() prevoiusHandler = new EventEmitter();
  @Output() nextHandler = new EventEmitter();
  private sid;
  pageItems = CONSTANTS.PAGE_LIMIT;

  constructor(
    private _searchOfferService: SearchOfferService,
    private baseManagementService:BaseManagementService,
    private actionLogService: ActionLogService,
    private authService: AuthService,
    private _searchOfferRequestService: SearchOfferRequestService,
    private queryGenerator: QueryGenerator,
    private _customerGroupService: CustomerGroupService,
    private productGroupService: ProductGroupService,
    private _pointGroupService: PointGroupService,
    private _bulkUpdateService: BulkUpdateService,
    private _iviePromotionService : IviePromotionService,
    private pluSearchService: PluSearchService,
    private _printAdService : PrintAdService,
    private _fileAttachService: FileAttachService,
    private commonService: CommonService,
    private commonSearchService:CommonSearchService,
    private facetItemService: FacetItemService,
    private techSupportService: TechSupportService
  ) { super(); }

  ngOnChanges() {
    this.productGroupService.paginationRender.subscribe((renderStatus) => {
      if (renderStatus && this.headerPage === 'groupId') {
        this.initialConfig(this.paginationTotalCount, this.paginationPageNumber, '');
      }
    });
  }

  ngOnInit() {
    this.initSubscribes();
    if (this.headerPage !== 'groupId') {
      this._searchOfferRequestService.homeFilterPaginationSourceSearch.subscribe((item: any) => {
        if (item) {
          this.initialConfig(item.totalCount, item.pageNumber, item.sid);
        }
      });
    } else {
      this.initialConfig(this.paginationTotalCount, this.paginationPageNumber, '');
    }
  }
  initSubscribes() {
    this.subs.sink = this._iviePromotionService.searchPromotionPage.subscribe((value) => {
      if(value) {
        if(Number.isFinite(this.paginationPageNumber)) {
          this.updateParams(this.paginationPageNumber);
        }
      }
    });

    this.subs.sink = this.pluSearchService.pluManagementPagination$.subscribe((data:any) => {
      if(data) {
        this.initialConfig(data.totalCount, data.pageNumber, data.sid);
      }
    });

    this.subs.sink = this.commonService.passPaginationData$.subscribe((data:any) => {
      if(data) {
        this.initialConfig(data.totalCount, data.pageNumber, data.sid);
      }
    });

  }

  initialConfig(totalCount, pageNumber, sid) {

    this.paginationTotalCount = totalCount;
    this.paginationPageNumber = pageNumber;
    this.sid = sid;
    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SID,this.sid);

    if (this.paginationTotalCount) {
      this.paginationPageNumber = this.paginationPageNumber ? this.paginationPageNumber : Math.ceil(this.paginationTotalCount / this.pageItems);
      this.paginationItemStart = (parseInt(this.paginationPageNumber, 10) - 1) * this.pageItems + 1;
      const lastItem = this.paginationItemStart + this.pageItems - 1;
      this.paginationItemEnd = lastItem > this.paginationTotalCount ? this.paginationTotalCount : lastItem;
    }

  }

  checkCountNext() {
    return this.paginationTotalCount !== this.paginationItemEnd && this.paginationTotalCount;
  }
  checkCountPrev() {
    return parseInt(this.paginationPageNumber, 10) !== 1 && this.paginationTotalCount;

  }
  prevoius() {
    window.scrollTo(0, document.body.scrollTop);
    this.paginationPageNumber = parseInt(this.paginationPageNumber, 10) - 1;
    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.NEXT,this.paginationPageNumber);
    if (this.headerPage !== 'groupId' && this.headerPage !== 'offerPODPage') {
      this.updateParams(this.paginationPageNumber);
    } else {
      if(this.headerPage === 'offerPODPage') {
        document.getElementsByClassName('datatable-body')[0].scrollTop = 0;
      }
      this.prevoiusHandler.emit({ 'pageNo': this.paginationPageNumber, 'gridName': this.gridName });
    }
    ["templatesHomePg", "homePage"].includes(this.headerPage)  && this._bulkUpdateService.requestIdsListSelected$.next([]);
    this.headerPage === "offerHomePage" && this._bulkUpdateService.offerIdsListSelected$.next([]);


    this._bulkUpdateService.offerBulkSelection.subscribe((value) => {
      if (value) {
        if (value === "selectAllOnPage") {
          this._bulkUpdateService.offerBulkSelection.next(null)
        }
      }
    })

    this._bulkUpdateService.bulkSelectionForOffers.subscribe((value) => {
      if (value) {
        if (value === "selectAllOnPage") {
          this._bulkUpdateService.bulkSelectionForOffers.next(null)
        }
      }
    })

  }
  next() {
    window.scrollTo(0, document.body.scrollTop);
    this.paginationPageNumber = parseInt(this.paginationPageNumber, 10) + 1;
    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.NEXT,this.paginationPageNumber);
    if (this.headerPage !== 'groupId' && this.headerPage !== 'offerPODPage') {
      this.updateParams(this.paginationPageNumber);
    } else {
      if(this.headerPage === 'offerPODPage') {
        document.getElementsByClassName('datatable-body')[0].scrollTop = 0;
      }
      this.nextHandler.emit({ 'pageNo': this.paginationPageNumber, 'gridName': this.gridName });
    }

    this.headerPage === "homePage" && this._bulkUpdateService.requestIdsListSelected$.next([]);
    this.headerPage === "offerHomePage" && this._bulkUpdateService.offerIdsListSelected$.next([]);
    this._bulkUpdateService.offerBulkSelection.subscribe((value) => {
      if (value) {
        if (value === "selectAllOnPage") {
          this._bulkUpdateService.offerBulkSelection.next(null)
        }
      }
    })
    this._bulkUpdateService.bulkSelectionForOffers.subscribe((value) => {
      if (value) {
        if (value === "selectAllOnPage") {
          this._bulkUpdateService.bulkSelectionForOffers.next(null)
        }
      }
    })

  }
  updateParams(pageNumber) {
    let removeList = [];
    let paramsList = [];
    let limit = CONSTANTS.LIMIT;
    if (this.headerPage === 'batchImportLogPA') {
      limit = CONSTANTS.PAGE_SIZE;
    }
    removeList = [limit];
    paramsList = [{
      remove: true, parameter: CONSTANTS.NEXT, value: pageNumber
    }, {
      remove: true, parameter: CONSTANTS.SID, value: this.sid
    }, {
      remove: true, parameter: limit, value: CONSTANTS.PAGE_LIMIT
    }];
    this.fetchNewPageData(removeList, paramsList);
  }

  searchAllOffersApi() {
    this._searchOfferService.searchAllOffers(this.queryGenerator.getQuery(), false, this.queryGenerator.getQueryWithFilter()).subscribe((result: any) => {
      result.pagination = true;
      this._searchOfferRequestService.getOfferDetails(result);
    })
  }
  fetchNewPageData(removeList: string[], paramsList: any[]) {
    this.queryGenerator.removeParameters(removeList);
    this.queryGenerator.pushParameters({ paramsList });
    const pCode = this.facetItemService.programCodeSelected;
   if (this.headerPage === 'pluManagement') {
      this.pluSearchService.fetchPluList({});
    } else if(this.headerPage === "tech-support") {
      this.techSupportService.getPushedEventResults(this.queryGenerator.getQuery()).subscribe((data: any) => {
        if(data) {
          const {totalCount, current, sid } = data;
          this.techSupportService.updateList(data);
          this.commonService.passPaginationData$.next({ totalCount, pageNumber: current, sid });
        }
      })
    } else if ((this.headerPage === 'homePage'&& [CONSTANTS.BPD].includes(pCode) && this.commonSearchService.getActiveCurrentSearchType())||['productGroupManagement', 'templatesHomePg', "actionLog","import-log-bpd", CONSTANTS.STOREMANAGEMENT, "storeGroup"].indexOf(this.headerPage) > -1) {
      // Moved the common code of getting all templats to management service to reuse in other place 
      this.baseManagementService.fetchPaginationData(false);

    } else if (this.headerPage === 'homePage' && [CONSTANTS.SC, CONSTANTS.GR, CONSTANTS.SPD].includes(pCode)) {
      this._searchOfferRequestService.searchAllOfferRequest
        (this.queryGenerator.getQuery(), false, this.queryGenerator.getQueryWithFilter()).subscribe((result: any) => {
          result.pagination = true;
          this._searchOfferRequestService.getOfferDetails(result);
        })
    } else if (this.headerPage === 'customerGroup') {
      this._customerGroupService.getCustomerGroupByName(this.queryGenerator.getQuery(), false).subscribe((result: any) => {
        result.pagination = true;
        this._customerGroupService.customerGroupSource.next(result);
      })
    }else if (this.headerPage === 'offerHomePage') {
      this.authService.onUserDataAvailable(this.searchAllOffersApi.bind(this));
    } else if (this.headerPage === 'pointGroup') {
      this._pointGroupService.searchPointGroup(this.queryGenerator.getQuery()).subscribe((result: any) => {
        result.pagination = true;
        this._pointGroupService.getAllPointGroupSearch(result);
      })
    }else if (this.headerPage === 'offerPODPage') {
      this.queryGenerator.removeParam('sortBy');
      let param: any = {};
      param.parameter = CONSTANTS.SORT_BY;
      param.value = 'divisionIdTxtASC,vehicleNmASC,adPageNbrASC,adModNbrASC';
      param.remove = true;
      this.queryGenerator.pushParam(param);
      this._iviePromotionService.searchAllPromotions(this.queryGenerator.getQuery()).subscribe((result: any) => {
        result.pagination = true;
        this._iviePromotionService.getPaginationSearch(result);
      })
    } else if (this.headerPage === 'podImport') {
      const query = this.queryGenerator.getQuery()
      this._printAdService.importLog(query).subscribe((result:any) => {
        this._printAdService.importLogResponse.next(result);
      });
    }else if (this.headerPage === 'batchImportLog') {
      const query = this.queryGenerator.getQuery()
      this._fileAttachService.importLog(query).subscribe((result:any) => {
        this._fileAttachService.batchImportLogResponse$.next(result);
      });

    }else if (this.headerPage === 'import-log-bpd') {
      const query = this.queryGenerator.getQuery()
      this._fileAttachService.importLogBpd(query).subscribe((result:any) => {
        this.actionLogService.bpdImportLogData$.next(result);
      });

    }else if (this.headerPage === 'actionLog' ) {
      const query = this.queryGenerator.getQuery();
      this.actionLogService.importLog(query).subscribe((result:any) => {
        this.actionLogService.actionLogData$.next(result);
      });
    }else if (this.headerPage === 'batchImportLogPA') {
      this.handleImportLogPA();
    }
  }

  handleImportLogPA(){
    this.paginationPageNumber = parseInt(this.paginationPageNumber, 10) - 1;
    let paramsList = [{remove: true, parameter: CONSTANTS.NEXT, value: this.paginationPageNumber}]
    this.queryGenerator.removeParam('next');
    this.queryGenerator.pushParameters({paramsList});
    const query = this.queryGenerator.getQuery();
    this._fileAttachService.getToken().subscribe(response=>{
      this._fileAttachService.importPALog(query,response?.['access_token']).subscribe((result:any) => {
        this._fileAttachService.batchImportLogResponsePA$.next(result);
      });
    })
  }
}

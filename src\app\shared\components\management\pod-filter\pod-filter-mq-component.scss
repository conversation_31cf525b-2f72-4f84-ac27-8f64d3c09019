@import "../../../../../scss/variables";
.batch-link {
  //content: "\2807";
  height: 24px;
  width: 24px;
  background-image: radial-gradient(circle, #4b4b4b 2px, transparent 2px);
  background-size: 100% 32.33%;
  cursor: pointer;
  //font-size: 25px;
}

.batch-selection {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #ccc;
  cursor: pointer;
}

.pagination-wrap{
  margin-top: 4px;
  margin-left: auto
}
.pt-7em{
  padding-top: .7em;
}

.request-checkbox {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.actions-list {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.actionListElem {
  cursor: pointer;
  :hover {
    background-color: blue;
  }
}

@media (min-width: 320px) {
  .sort-label {
    white-space: nowrap;
    font-size: $base-font-size;
  }

  .text-label {
    font-size: $base-font-size;
    white-space: nowrap;
    word-break: break-all;
  }

  .dropdown-toggle {
    min-width: 130px;
  }

  .icon-expand {
    width: 12px;
    height: 12px;
  }

  .expand-all-label {
    font-size: $base-font-size;
    white-space: nowrap;
  }
}

@media (min-width: 576px) {
  .sort-label {
    white-space: nowrap;
    font-size: $base-font-size;
  }

  .text-label {
    font-size: $base-font-size;
    white-space: nowrap;
    word-break: break-all;
  }

  .dropdown-toggle {
    min-width: 160px;
  }

  .icon-expand {
    width: 14px;
    height: 14px;
  }

  .expand-all-label {
    font-size: $base-font-size;
    white-space: nowrap;
  }
}

@media (min-width: 768px) {
  .sort-label {
    white-space: nowrap;
    font-size: $base-font-size;
  }
  .text-label {
    font-size: $base-font-size;
    white-space: nowrap;
    word-break: break-all;
  }

  .dropdown-toggle {
    min-width: 180px;
  }

  .icon-expand {
    width: 15px;
    height: 15px;
  }

  .expand-all-label {
    font-size: $base-font-size;
    white-space: nowrap;
  }
}

@media (min-width: 992px) {
  .sort-label {
    white-space: nowrap;
    font-size: $base-font-size;
  }

  .text-label {
    font-size: $base-font-size;
    white-space: nowrap;
    word-break: break-all;
  }

  .dropdown-toggle {
    min-width: 180px;
  }

  .icon-expand {
    width: 15px;
    height: 15px;
  }

  .expand-all-label {
    font-size: $base-font-size;
    white-space: nowrap;
  }
}

@media (min-width: 1280px) {
  .sort-label {
    white-space: nowrap;
    font-size: $base-font-size;
  }

  .text-label {
    font-size: $base-font-size;
    white-space: nowrap;
    word-break: break-all;
  }

  .dropdown-toggle {
    min-width: 180px;
  }
}

@media (min-width: 1700px) {
  .sort-label {
    white-space: nowrap;
    font-size: $base-font-size;
  }
  .text-label {
    font-size: $base-font-size;
    white-space: nowrap;
    word-break: break-all;
  }

  .dropdown-toggle {
    min-width: 180px;
  }
}

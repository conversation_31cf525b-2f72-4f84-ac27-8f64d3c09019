<div class="container-fluid pr-0 pod-filter-container" [class]="headerPage">
  <div class="row no-gutters col p-0 col mb-3" [class.justify-content-end]="headerPage ==='pluManagement'">
    <div class="request-checkbox" *ngIf="showBatchItems">
      <ng-container *ngIf="enableOrDisableBatchAction">
        <label class="mt-2 label-input-checkbox" for="input-checkbox"
          [ngClass]="headerPage === 'offerHomePage' ? 'ml-2' : 'ml-4'"
          *permissionsOnly="getPermissionsBasedOnPage(); authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'">
          <input type="checkbox" #filterHeaderCheck class="form-checkbox dark-checkbox filter-header-check"
            id="input-checkbox" [(ngModel)]="isBulkChecked" tabindex="0" (change)="onClickBulkHeaderCheckbox($event)" />
        </label>
        <span class="span-batch-selection" [popover]="selectionTemplate" triggers="click" #popup="bs-popover"
          placement="bottom" containerClass="batch-container" [outsideClick]="true"
          *permissionsOnly="getPermissionsBasedOnPage(); authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'">
          <div class="batch-selection"></div>
        </span>
      </ng-container>
    </div>
    <div class="actions-list" *ngIf="showBatchItems">
      <ng-container *ngIf="headerPage === 'offerHomePage' ? facetItemService.enableBatchActionForOffers() : true">
        <span [popover]="popTemplate" triggers="click" #pop="bs-popover"
          [placement]="pgCodeCount < 2 ? 'bottom' : 'top'"
          [containerClass]="actionList" [outsideClick]="true"
          *permissionsOnly="getPermissionsBasedOnPage(); authorisedStrategy: 'show'; unauthorisedStrategy: 'remove'">
          <div id="batch-link" class="batch-link" (click)="showWarning()"></div>
        </span>
      </ng-container>
    </div>
    <ng-template #selectionTemplate>
      <div class="actions-popover">
        <a class="dropdown-item" (click)="batchSelection('selectAllOnPage') && pop.hide();"><label
            class="m-0 text-label">Select all on page</label></a>
        <a class="dropdown-item" (click)="batchSelection('selectAcrossAllPages') && pop.hide();"><label
            class="m-0 text-label">Select all across all pages</label></a>
      </div>
    </ng-template>

    <ng-template #popTemplate>
      <div class="actions-popover">
        <load-dynamic-batch [batchInputsObj]="batchInputsObj"></load-dynamic-batch>
      </div>
    </ng-template>

    <ng-template #stopTemplate>
      <div class="actions-warning">
        You must select only one program code to use batch actions (Except Export Offers).
      </div>
    </ng-template>


    <div class="col-auto d-flex justify-content-start"
      *ngIf="(['pluManagement'].indexOf(headerPage) < 0)">
      <div  [ngClass]="headerPage === 'productGroupManagement' ? 'd-flex flex-row flex-wrap' :'d-inline-flex pl-3 pr-5'" *ngIf="!hideSortBy">
        <span class="mb-0 pt-7em sort-label">Sort By </span>
        <app-sort-option [sortFormGroups]="sortFormOptionGroups" [selectedPC]="selectedPC" [pageType]="headerPage" [name]="'sortOne'"  [sortOptionList]="sortOptionList"
           (getSortByValueEvent)="getSortByValue($event)" >
        </app-sort-option>
        <div class="d-inline-flex pl-3 pr-5" *ngIf="headerPage === 'homePage'">
          <app-sort-option  [sortFormGroups]="sortFormOptionGroups" [selectedPC]="selectedPC" [pageType]="headerPage"  [name]="'sortTwo'"  [sortOptionList]="sortOptionList"
            (getSortByValueEvent)="getSortByValue($event)"
            ></app-sort-option>
        </div>
      </div>
      <!-- Expand All -->
      <div class="d-flex justify-content-start" *ngIf="!(
          headerPage === 'storeGroup' ||
          headerPage === 'customerGroup' ||
          headerPage === 'productGroupManagement' ||
          headerPage === 'pointGroup'
        ) && !hideLink && !isHideExpandFeature
      ">
        <div *ngIf="showExpand; else showCollapse">
          <a class="expandAllLink" (click)="expandAll()">
            <span class="mb-0">
              <span class="m-0"><img class="icon-expand" src="assets/icons/Expand-icon.svg" alt="" /></span>
              <label class="expand-all-label p-1 mt-1">Expand All</label>
            </span>
          </a>
        </div>
        <ng-template #showCollapse>
          <a class="expandAllLink" (click)="collapseAll()">
            <span class="mb-0">
              <span class="m-0"><img class="icon-expand" src="assets/icons/Collapse-icon.svg"
                  alt="" /></span>
              <label class="expand-all-label p-1 mt-1">Collapse All</label>
            </span>
          </a>
        </ng-template>
      </div>
    </div>


    <div class="d-flex justify-content-start"
      *ngIf="!(headerPage === 'offerHomePage' || headerPage === 'storeGroup') && hideLink"></div>

    <!-- List/Grid Views -->

    <div *ngIf="headerPage === 'offerHomePage'" class="col d-flex justify-content-end mt-1 mr-7">
      <span class="list-grid-view-options">
        <span class="cursor-pointer" *ngIf="showListIcon; else showListIconGrayed">
          <span (click)="listIconClick()" tooltip="List View">
            <img src="assets/icons/list-icon-enabled.svg" alt="" />
          </span>
        </span>
        <ng-template #showListIconGrayed>
          <span tooltip="List View">
            <img src="assets/icons/list-icon-disabled.svg" alt="" />
          </span>
        </ng-template>
        <span *ngIf="showGridIcon && !nonDigitalFilterOnly; else showGridIconGrayed">
          <span (click)="gridIconClick()" class="cursor-pointer grid-view-icon" tooltip="POD View">
            <img src="assets/icons/tile-icon-enabled.svg" alt="" />
          </span>
        </span>
        <ng-template #showGridIconGrayed>
          <span *ngIf="!nonDigitalFilterOnly" class="grid-view-icon"
            tooltip="POD View">
            <img src="assets/icons/tile-icon-disabled.svg" alt="" />
          </span>
        </ng-template>
      </span>
    </div>

    <!-- Pagination -->
    <div class="d-flex align-items-center pagination-wrap text-right" >
      <span>
        <pagination [headerPage]="headerPage"></pagination>
      </span>
    </div>
  </div>
</div>
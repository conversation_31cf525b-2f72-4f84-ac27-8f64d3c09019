@import "../../../../../scss/colors";
@import "../../../../../scss/inputs.scss";


.sort-label {
  white-space: nowrap;
  font-size: 16px;
}

.text-label {
  font-size: 16px;
  white-space: nowrap;
  word-break: break-all;
}

.modal-content {
  height: 225px !important;
  width: 562px !important;
  border-radius: 5px !important;
  background-color: #ffffff !important;
}

.warning-text {
  text-align: center;
  font-weight: 600;
}

.test-header{
  padding-left: 14px;
  font-size: 30px;
}

::ng-deep {
  .ng-select {
    .ng-arrow-wrapper{
      bottom: 0px !important;
    }
  }
  .ng-select.ng-select-disabled{
    .ng-select-container {
      background-color: #ccc;
    }
  }
  .batch-export-tmpl.modal-dialog {
    max-width: 600px;
  }
  .bulk-copy-OR.modal-dialog {
    max-width: 40%;
  }
  .tooltip-inner {
    background-color: #8b8b8b;
    font-size: 12px;
    color: #fff;
  }
  .popover {
    transform: translate3d(30px, 37px, 0px) !important;
    width: 180px !important;
    max-width: 180px !important;
    left: -12px !important;
    .arrow {
      left: 70px !important;
    }
    .popover-content {
      padding: 0px !important;
    }
  }
  .batch-container {
    width: 215px !important;
    max-width: 215px !important;
  }
  .action-warning.popover {
    transform: translate3d(90px, -30px, 0px) !important;
    width: auto !important;
    max-width: max-content !important;
    padding: .2rem 0.4rem;
    border-color: #DD1E25;
    box-shadow: 0 3px 6px rgba(111, 111, 111, 0.2);
    .arrow {
      display: none;
    }
  }
  .request-checkbox > .popover > .arrow {
    left: 32px !important;
  }
  .tooltip.top .tooltip-arrow:before {
    border-top-color: #8b8b8b;
    font-size: 12px;
  }
  .tooltip.top .tooltip-arrow {
    border-top-color: #8b8b8b;
    font-size: 12px;
  }
} 
.mr-cancel{
  margin-right: 0.9rem;
  color: #00529F;
  text-decoration: underline;
  font-weight: bold;
}
//
.dropdown-toggle {
  min-width: 180px;
}
.storeGroup-grid{
  display: inline-flex;
}
.pl-50{
  padding-left: 50px;
}
.success-text {
  font-size: $header-font-size;

  line-height: 32px;
}

.icon-expand {
  width: 18px;
  height: 18px;
}

.expand-all-label {
  font-size: 16px;
  white-space: nowrap;
}

.actions-button {
  background-color: #ffffff !important;
  //color: #000000 !important;
  border-color: $grey-lighter-hex !important;
}

.pod-filter {
  align-items: center;
  justify-content: space-between;
}
.cursor-pointer {
  cursor: pointer;
}
.display-option {
  display: none;
}
.panel-body {
  width: 100%;
  left: auto;
  margin-top: -1px;
  padding: 0;
  border-radius: 0 0 2px 2px;
  border: 1px solid #8b8b8b;
  border-top: 1px solid $grey-lighter-hex;
}
.sort-active {
  color: gray;
}
//
.dropdown-menu {
  top: 100%;
  z-index: 1000;
  float: left;
  // min-width: 160px;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border-radius: 0;
  min-width: 7rem !important;
  width: 100%;
  box-shadow: none;
}
.dropdown-sort-menu {
  top: 100%;
  z-index: 1000;
  float: left;
  min-width: 250px;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.sort-list-label {
  background-color: #f6f6f6;
  font-size: 16px;
  min-width: 200px;
  display: inline-block;
  border: 1px solid #8b8b8b;
  border-radius: 2px 2px 0 0;
  &.collapsed {
    .angle-rotate {
      transition: transform 0.2s;
      transform: rotate(-180deg);
    }
    background-color: transparent;
  }
  &:hover {
    background-color: #f6f6f6;
  }
}
.list-grid-view-options span img {
  height: 36px;
  width: 56px;
}
.list-grid-view-options span.grid-view-icon {
  margin-left: -4px;
}
.fa-i {
  font-size: 1.5em;
}
.sort-list {
  font-size: 16px;
  list-style: none;
  background-color: #fff;
  position: absolute;
  left: 83px;
  min-width: 200px;
  margin: 0;
  border-radius: 0 0 2px 2px;
  border: 1px solid #8b8b8b;
  border-top: 1px solid $grey-lighter-hex;
  z-index: 1;
}
.sort-list-item {
  padding: 5px 0 5px 25px;
  background-color: transparent;
  cursor: pointer;
  &:hover {
    background-color: #f6f6f6;
  }
}

.expandAllLink {
  color: $theme-primary !important;
}
.request-checkbox .squaredThree input:checked ~ .checkmark {
  background-color: $blue-primary-hex;
  border: 0;
  &:after {
    content: "";
    border: solid white;
    border-width: 0 2px 2px 0;
    display: inline-block;
    // position: absolute;
    left: 7px;
    right: 9px;
    top: 2px;
    width: 5px;
    height: 12px;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
}

.close {
  font-size: 3rem;
}

.isDisabled {
  
  opacity: 0.4;
  cursor: default !important;
  pointer-events: none;
}
.more {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAANCAYAAAHu22/pAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkJERjk2NDhGQzhBRTExRTk5NzdCRDZDNEJENEVBQzhEIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkJERjk2NDkwQzhBRTExRTk5NzdCRDZDNEJENEVBQzhEIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QkRGOTY0OERDOEFFMTFFOTk3N0JENkM0QkQ0RUFDOEQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QkRGOTY0OEVDOEFFMTFFOTk3N0JENkM0QkQ0RUFDOEQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5ZDwH4AAABGElEQVR42mL8//8/Awgws/EKMxw+fuorQAAxIossBIpsAAggxtaeSTuAAh4MEHAErgQGAAIIRYClrXfyTCCdDsT5LFDGXiB2RlEGEEAgUxlwASYovRGIV0LZTkD8GGwBVMAfSk8A4gIglgVxAAIIw23IAOQ0vHb+R7IbBkBijCBBdiD+C8SsUImfUDv/gxz0C+qwP0D8GojtgPgJsmv/Qo2OAuKTWAMOHQAEGF5JfADkzQAgbQvEJVAf4AP1QHwaiLeB3LcLiLWB+B8QLwQlAywaoqHywTC/gDR+gyYRNiDmgIbEBijfHIi/QqNKH4j1gPgtA1pg/wbicKiNL6HhexSII4FYFIgvY0shyOAfNLExQoN9EzbPAgCy5lG75ZLC+QAAAABJRU5ErkJggg==) no-repeat right
  center; 
    background-size: 14px;
    background-position-x: 55px;
  cursor: pointer;
  font-weight: bold;
  padding: 2px 16px;
  text-transform: capitalize;
  &.collapsed {
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIgZD0iTTUgMTVsNy03IDcgNyIvPjwvc3ltYm9sPg0KICAgICAgICA8L2RlZnM+DQogICAgICAgIDxnPg0KICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjYXNzZXQiLz4NCiAgICAgICAgPC9nPg0KICAgIDwvc3ZnPg==) no-repeat right
    center;
      background-size: 14px;
      background-position-x: 55px;
      padding: 2px 16px;
  }
}
.close-modal-icon {
  top: 0;
  right: 0;
}
.confirm-text {
  font-size: 28px;
}
.inAdEmail{
width: 200px;
}

.cancelAction {
  background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIgZD0iTTUgMTVsNy03IDcgNyIvPjwvc3ltYm9sPg0KICAgICAgICA8L2RlZnM+DQogICAgICAgIDxnPg0KICAgICAgICAgICAgPHVzZSB4bGluazpocmVmPSIjYXNzZXQiLz4NCiAgICAgICAgPC9nPg0KICAgIDwvc3ZnPg==) no-repeat right center; 
  background-size: 20px;
  background-position-x: 120px;
  padding-right: 100px;
  &.collapsed {
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPg0KICANCg0KICAgICAgICA8ZGVmcz4NCiAgICAgICAgICAgIDxzeW1ib2wgaWQ9ImFzc2V0IiB2aWV3Qm94PSIwIDAgMjQgMjQiPg0KICAgICAgICAgICAgPHBhdGggZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InNxdWFyZSIgZD0iTTE5IDlsLTcgNy03LTciLz48L3N5bWJvbD4NCiAgICAgICAgPC9kZWZzPg0KICAgICAgICA8Zz4NCiAgICAgICAgICAgIDx1c2UgeGxpbms6aHJlZj0iI2Fzc2V0Ii8+DQogICAgICAgIDwvZz4NCiAgICA8L3N2Zz4=) no-repeat right center;
    background-size: 20px;
    background-position-x: 120px;
    padding-right: 100px;
  }
}
.partiallyFilledCB{
  background: #757575;
  &:after{
    content: "\2758";
    transform: rotate(90deg);
    color:#fff;
    text-align: center;
    display: block;
  }
}
.popupButtons{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 0 solid #dedede;
}

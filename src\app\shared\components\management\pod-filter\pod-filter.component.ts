import {
  Component,
  ElementRef,
  EventEmitter, Input,
  On<PERSON><PERSON>roy,
  OnInit, Output, ViewChild
} from "@angular/core";

import { UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { CustomerGroupService } from "@appGroupsServices/customer-group.service";
import { PointGroupService } from "@appGroupsServices/point-group.service";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { offerRequestModel } from "@appModels/offer-request.model";
import { POD_CONSTANTS } from "@appModules/offers/constants/pod_constants";
import { SearchOfferService } from "@appOffersServices/search-offer.service";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AppInjector } from "@appServices/common/app.injector.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { QueryGenerator } from "@appServices/common/queryGenerator.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { BaseManagementService } from "@appServices/management/base-management.service";
import { BulkUpdateService } from "@appServices/management/bulk-update.service";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { PopoverDirective } from "ngx-bootstrap/popover";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: "app-pod-filter",
  templateUrl: "./pod-filter.component.html",
  styleUrls: ["./pod-filter.component.scss", "./pod-filter-mq-component.scss"]
})
export class PodFilterComponent extends UnsubscribeAdapter implements OnInit, OnDestroy {
  @ViewChild("filterHeaderCheck") filterHeaderCheck: ElementRef;


  public offerRequests: offerRequestModel[];
  public offerRequestsList: offerRequestModel[];
  public sortOptionList;
  @Input() headerPage;
  @Input() OfferReqs;
  @Input() selectionResetFlag;
  @Input() pgCodeCount: number = 0;
  @Input() hideSortBy;
  @Input() allowedPermissions;
  showGridIcon: boolean = true;
  showListIcon: boolean = false;
  nonDigitalFilterOnly: boolean = false;
  modalRef: BsModalRef;
  offerRequest;
  isBulkOffersChecked: boolean = false;
  isBulkChecked: boolean = false;
  isSelectAllOnPage: boolean = false;
  isPopupDisabled: boolean = true;
  isofferEnabled: boolean = false;
  isAllBatchSelected: any;
  pageData: any;
  allowedBatchPermissionsForOR = [
    CONSTANTS.Permissions.DoBatchAssign,
    CONSTANTS.Permissions.DoBatchUpdateOfferDates,
    CONSTANTS.Permissions.DoBatchSubmit,
    CONSTANTS.Permissions.DoBatchProcess,
    CONSTANTS.Permissions.DoBatchExport,
    CONSTANTS.Permissions.DoBatchCopy
  ];

  allowedBatchPermissionsForOffer = [
    CONSTANTS.Permissions.DoBatchPublishOffers,
    CONSTANTS.Permissions.DoBatchUpdateForTesting,
    CONSTANTS.Permissions.DoBatchUpdatePod,
    CONSTANTS.Permissions.DoBatchTerminalUpdate
  ];
  batchType: any;
  fromBatchSelection: boolean;
  permissions: any[];
  selectedPC: any;
  selectedMF: boolean = false;
  
  isGRFlagEnabled: any;
  CONSTANTS = CONSTANTS;


  private authService: AuthService;
  private _searchOfferService: SearchOfferService;
  private storeGroupService: StoreGroupService;
  public facetItemService: FacetItemService;
  private _searchOfferRequestService: SearchOfferRequestService;
  private _customerGroupService: CustomerGroupService;
  private bulkUpdateService: BulkUpdateService;
  private _pointGroupService: PointGroupService;
  private _modalService: BsModalService;
  public queryGenerator: QueryGenerator;
  private featureFlagService: FeatureFlagsService;
  public baseManagementService: BaseManagementService;
  public commonSearchService: CommonSearchService;
  public baseInputSearchService: BaseInputSearchService;
  public _toaster: ToastrService;
  sortFormOptionGroups: any;
  sortOptionFormGroupOne: UntypedFormGroup;
  sortOptionFormGroupTwo: UntypedFormGroup;


  constructor() {
    super();
    this.injectServices();
  }

  itemStart = 0;
  totalCount = 0;
  itemEnd = 0;
  pageNumber;
  sid;
  showExpand = true;
  hideLink: boolean;
  selectPageBool: boolean;
  offerReqIdList;
  selectedListIds;
  deliveryChannel;
  reqIdList: string;
  selectedOption;


  @ViewChild("pop")
  private popover: PopoverDirective;
  @ViewChild("popup")
  private popup: PopoverDirective;


  // tslint:disable-next-line:no-output-on-prefix
  @Output() onListIconClick = new EventEmitter<any>();
  @Output() onGridIconClick = new EventEmitter<any>();
  @Output() onexpandAll = new EventEmitter<any>();



  injectServices() {
    const injector = AppInjector.getInjector();
    this.commonSearchService = injector.get(CommonSearchService);
    this.baseInputSearchService = injector.get(BaseInputSearchService)
    this.authService = injector.get(AuthService);
    this._searchOfferService = injector.get(SearchOfferService);
    this.storeGroupService = injector.get(StoreGroupService);
    this.facetItemService = injector.get(FacetItemService);

    this._searchOfferRequestService = injector.get(SearchOfferRequestService);
    this._customerGroupService = injector.get(CustomerGroupService);
    this.bulkUpdateService = injector.get(BulkUpdateService);

    this._pointGroupService = injector.get(PointGroupService);
    this._modalService = injector.get(BsModalService);
    this.queryGenerator = injector.get(QueryGenerator);

    this.featureFlagService = injector.get(FeatureFlagsService);
    this._modalService = injector.get(BsModalService);
    this.queryGenerator = injector.get(QueryGenerator);
    this.baseManagementService = injector.get(BaseManagementService);
    this._toaster = injector.get(ToastrService);
  }

    ngOnInit() {
        this.selectedPC = this.facetItemService.programCodeSelected || "SC";
        this.initSubscribes();
        this.isAllBatchSelected = "";
        if ((['pluManagement'].indexOf(this.headerPage) < 0)) {
            this.createOptionFormGroup();
            this.getSelectedSortList();
        }
       
  }

  /**
   * On offers need to check for enable/disable batch checkbox
   */
  get enableOrDisableBatchAction() {
    switch (this.headerPage) {
      case 'offerHomePage': {
        return this.facetItemService.enableBatchActionForOffers();
      }
      default: {
        return true;
      }
    }
  }
  onClickBulkHeaderCheckbox(event) {
    this.batchSelection(event.target.checked ? "selectAllOnPage" : "");
    switch (this.headerPage) {
      case "templatesHomePg":
      case "homePage": {
        this.selectPage(event);
        break;
      }
      case "offerHomePage": {
        this.selectOfferPage(event);
        break;
      }
    }
  }
  onSelectingBatchOptions(value, data) {
    if (value) {
      if (value === "selectAcrossAllPages") {
        this.isBulkChecked = true;
        this.selectPageBool = true;
        this.isPopupDisabled = false;
      }
      else if (value === "selectAllOnPage") {
        this.isBulkChecked = data.length > 0 && this.totalCount > 0 && data.length === this.totalCount;
      }
    } else {
      this.isBulkChecked = data.length > 0 && this.totalCount > 0 && data.length === this.totalCount;
      this.isPopupDisabled = data.length ? false : true;
    }
    // When ever checkbox selection changes need to check if partial selection class should add or not
  }
  handleBulkRequestSelection() {
    if (!['homePage', 'templatesHomePg'].includes(this.headerPage)) return false;
    this.subs.sink = this.bulkUpdateService.requestIdsListSelected$.subscribe(
      (data) => {
        this.subs.sink = this.bulkUpdateService.offerBulkSelection.subscribe((value) => {
          this.onSelectingBatchOptions(value, data);
          this.addPartialSelectedClass(data);
        })
      }
    );
  }
  handleBulkOfferSelection() {
    if (this.headerPage !== "offerHomePage") return false;
    this.subs.sink = this.bulkUpdateService.offerIdsListSelected$.subscribe(
      (data) => {
        this.bulkUpdateService.offersIdArr = data;
        this.subs.sink = this.bulkUpdateService.bulkSelectionForOffers.subscribe((value) => {
          this.onSelectingBatchOptions(value, data);
        })

      }
    );
  }
  setTotalCountAndPageData(data) {
    if (this.headerPage === 'offerHomePage') {
      this.totalCount = (data && data.offers && data.offers.length) ? data.offers.length : 0;
    } else {
      this.totalCount = (data && data.offerRequests && data.offerRequests.length) ? data.offerRequests.length : 0;
    }
    this.pageData = data ? data : null;
  }
  initSubscribes() {
    this.subs.sink = this.facetItemService.selectedOfferProgramCode$.subscribe((val: any) => {
      this.selectedMF = !!val?.[CONSTANTS.MF];
    })
    this.subs.sink = this.facetItemService.selectedProgramCode$.subscribe(val => {
      this.selectedPC = val;
      this.getSelectedSortList();
      this.removePartialSelectedCls();
      this.resetBatchSelections();
    });
    this.subs.sink = this.bulkUpdateService.displayPopup$.subscribe(
      (val: any) => {
        this.isPopupDisabled = val;
      });

    this.subs.sink = this._searchOfferRequestService.currentOfferRequests.subscribe(
      (data: any) => {
        this.setTotalCountAndPageData(data);
      });

    this.subs.sink = this._searchOfferRequestService.podFilterSearchSourceSearch.subscribe(
      (facetChipListItems: any) => {
        this.handlePodSelectionChange(facetChipListItems);
      }
    );
    this.subs.sink = this.baseManagementService.templatesData$.subscribe(data => this.setTotalCountAndPageData(data));
    this.handleBulkRequestSelection();
    this.handleBulkOfferSelection();
    this.subs.sink = this.bulkUpdateService.isSelectionReset.subscribe((value) => {
      if (value) {
        this.resetBatchSelections();
      }
    });     
  }
  handlePodSelectionChange(facetChipListItems) {
    if (
      this.headerPage === "offerHomePage" &&
      facetChipListItems.facetChip &&
      facetChipListItems.facetChip.digital
    ) {
      let filterArr = facetChipListItems.facetChip.digital.split(";");
      if (
        filterArr.length > 0 &&
        filterArr.length == 2 &&
        filterArr[0].toLowerCase() === "Non-Digital".toLowerCase()
      ) {
        this.nonDigitalFilterOnly = true;
      } else {
        this.nonDigitalFilterOnly = false;
      }
    } else {
      this.nonDigitalFilterOnly = false;
    }
  }
  resetBatchSelections() {
    this.isBulkChecked = false;
    this.isBulkOffersChecked = false;
    this.isPopupDisabled = true;
    this.isAllBatchSelected = "";
    this.bulkUpdateService.offerBulkSelection.next('');
    this.bulkUpdateService.bulkSelectionForOffers.next('');
    this.bulkUpdateService.displayPopup$.next(false);
    this.bulkUpdateService.bulkSelected$.next(null);
    this.bulkUpdateService.allOffersSelected$.next(null);
    this.bulkUpdateService.userTypeArray = [];
    this.bulkUpdateService.requestIdsListSelected$.next([]);
    this.bulkUpdateService.requestIdArr = [];
    this.bulkUpdateService.offerIdsListSelected$.next([]);
    this.bulkUpdateService.offersIdArr = [];
    this.bulkUpdateService.deliveryChannelArr=[]
  }
  get partialSelectedElRef() {
    return this.filterHeaderCheck?.nativeElement;
  }
  get batchInputsObj() {
    return {
      isPopupDisabled: this.isPopupDisabled,
      batchType: this.batchType || '',
      popupRef: this.popover,
      pgCodeCount: this.pgCodeCount
    }
  }
  /**
   * 
   * @param data selected OR Ids length
   * 
   * On OR managemnt page, when OR ids selected partially need to add class to show hyphen icon in checkbox
   */
  addPartialSelectedClass(data) {
    this.removePartialSelectedCls();
    if (!this.isBulkChecked && data?.length && data.length < this.totalCount) {
      this.partialSelectedElRef?.classList?.add('partiallyFilledCB');
    }
  }
  /**
   * 
   * when Checkboxes selection get clears, need to remove partial selected class from checkbox if any
   */
  removePartialSelectedCls() {
    this.partialSelectedElRef?.classList?.remove('partiallyFilledCB')
  }
  getOfferReqIdList(ids) {
    this.bulkUpdateService.reqIdsOnPage = ids;
  }

  openModal(template, options) {
    this.modalRef = this._modalService.show(template, options);
  }

  hidePopover() {
    this.popover.hide();
  }
  get programCodeCheckedVal() {
    return this.facetItemService.getProgramCodesSelected();
  }
  getProgramCodeSelected() {
    return this.headerPage === 'offerHomePage' ? this.programCodeCheckedVal?.[0] : '';
  }
  onCloseSuccess(event) {
    this.modalRef && this.modalRef.hide();
  }
  closeModel() {
    this.modalRef && this.modalRef.hide();
  }
  hidePopup() {
    this.popup.hide();
  }
  selectAcrossAllPagesForHomePage(value) {
    if (this.pageData && this.pageData.offerRequests.length === 0) {
      this.isPopupDisabled = true;
      return false;
    }
    this.bulkUpdateService.requestIdArr = [];

    let deliveryChannelQuery = this.queryGenerator.getInputValue("deliveryChannel=");

    this.bulkUpdateService.offerBulkSelection.next(value);
    this.bulkUpdateService.bulkSelected$.next(
      this.bulkUpdateService.requestIdArr
    );
    this.queryGenerator.removeParam("limit");
    if (deliveryChannelQuery) {
      const deliveryChannels = deliveryChannelQuery.split(' OR ');
      this.bulkUpdateService.userTypeArray = deliveryChannels;
    } else {
      this.bulkUpdateService.userTypeArray.push('CC');
    }
    this.isPopupDisabled = false;
  }
  selectAllOnPageForHomePage(value) {
    this.bulkUpdateService.offerBulkSelection.next(value);
    if (this.pageData) {

      const ids = this.pageData.offerRequests
        ? this.pageData.offerRequests.reduce((output, ele) => {
          this.bulkUpdateService.userTypeArray.push(ele.info.deliveryChannel);
          this.bulkUpdateService.bulkAssignedUsers[`${ele.info.id}`] = {
            digitalUser: ele.info.digitalUser,
            nonDigitalUser: ele.info.nonDigitalUser
          }
          output.push(ele.info.id);
          return output;
        }, [])
        : [];
      if (ids && ids.length === 0) {
        this.isPopupDisabled = true;
        return false;
      }
      this.bulkUpdateService.requestIdArr = ids;
      this.bulkUpdateService.requestIdsListSelected$.next(ids);
      this.bulkUpdateService.bulkSelected$.next(
        this.bulkUpdateService.requestIdArr
      );
      this.isPopupDisabled = false;
      this.getUserTypeStatus(this.pageData);
    }
  }
  homePageBatchSelection(value) {
    this.isAllBatchSelected = value;
    this.bulkUpdateService.isAllBatchSelected.next(value);
    this.bulkUpdateService.userTypeArray = [];
    this.hidePopup();
    this.isBulkChecked = true;
    this.selectedOption = value;
    if (value === "selectAllOnPage") {
      this.selectAllOnPageForHomePage(value);
    } else if (value === "selectAcrossAllPages") {
      this.selectAcrossAllPagesForHomePage(value);
    } else {
      this.selectAllOnPageForHomePage(value);
      this.selectAcrossAllPagesForHomePage(value);
      this.isBulkChecked = false;
    }
  }
  offerHomePageBatchSelection(value) {
    this.isBulkChecked = true;
    this.hidePopup();
    const ids = this.pageData.offers
      ? this.pageData.offers.reduce((output, ele) => {
        output.push(ele.info.id.externalOfferId);
        return output;
      }, [])
      : [];
    if (value === "selectAllOnPage") {
      this.bulkUpdateService.bulkSelectionForOffers.next(value);
      if (this.pageData) {
        this.bulkUpdateService.displayPopup$.next(false);
        this.bulkUpdateService.allOffersSelected$.next(ids);
        this.bulkUpdateService.offersIdArr = ids;
        this.bulkUpdateService.offerIdsListSelected$.next(ids);
        this.bulkUpdateService.isSelectAcrossAllPages = false;
      }

    } else if (value === "selectAcrossAllPages") {
      if (this.pageData && this.pageData.offers.length === 0) {
        this.isPopupDisabled = true;
        return false;
      }
      this.bulkUpdateService.bulkSelectionForOffers.next(value);
      this.bulkUpdateService.allOffersSelected$.next(ids);
      this.bulkUpdateService.displayPopup$.next(false);
      this.bulkUpdateService.offersIdArr = [];
      this.bulkUpdateService.isSelectAcrossAllPages = true;
    } else {
      this.isBulkChecked = false;
    }
  }
  batchSelection(value) {
    this.batchType = value
    if (this.headerPage === 'homePage') {
      this.homePageBatchSelection(value);

    } else if (this.headerPage === 'offerHomePage') {
      this.offerHomePageBatchSelection(value);
    }
  }

  getPermissionsBasedOnPage() {
    if (this.headerPage) {
      if (this.headerPage === "homePage") {
        return this.allowedBatchPermissionsForOR;
      } else if (this.headerPage === "offerHomePage") {
        return this.allowedBatchPermissionsForOffer;
      }
    }
  }
  getUserTypeStatus(data) {
    this.bulkUpdateService.OfferDatesArray = [];
    data && data.offerRequests.map((element) => {
      const dates = {
        startDate: element.rules.startDate
          ? element.rules.startDate.offerEffectiveStartDate
          : new Date(),
        endDate: element.rules.endDate
          ? element.rules.endDate.offerEffectiveEndDate
          : new Date(),
      };

      this.bulkUpdateService.OfferDatesArray.push(dates);
    });
  }

  selectPage(event) {
    event.target.checked
      ? (this.selectPageBool = true)
      : (this.selectPageBool = false);

    this.bulkUpdateService.userTypeArray = [];
    this.bulkUpdateService.isAllBatchSelected.next("noSelectAcrossAllPages");

    if (this.selectPageBool && this.pageData) {
      this.isAllBatchSelected = "";
      const ids = event.target.checked
        ? this.pageData.offerRequests
          ? this.pageData.offerRequests.reduce((output, ele) => {
            this.bulkUpdateService.userTypeArray.push(ele.info.deliveryChannel);
            this.bulkUpdateService.deliveryChannelArr.push(`${ele.info.deliveryChannel} - ${ele.info.adType}`)
            this.bulkUpdateService.bulkAssignedUsers[`${ele.info.id}`] = {
              digitalUser: ele.info.digitalUser,
              nonDigitalUser: ele.info.nonDigitalUser
            }
            output.push(ele.info.id);
            return output;
          }, [])
          : []
        : [];
      this.getOfferReqIdList(ids);
      this.isAllBatchSelected = "";
      this.bulkUpdateService.requestIdArr = ids;
      this.bulkUpdateService.requestIdsListSelected$.next(ids);
      this.isPopupDisabled = this.bulkUpdateService.requestIdArr.length
        ? false
        : true;
      this.getUserTypeStatus(this.pageData);

    } else {
      this.bulkUpdateService.requestIdsListSelected$.next([]);
      this.bulkUpdateService.OfferDatesArray = [];
      this.bulkUpdateService.bulkAssignedUsers = {};
      this.bulkUpdateService.requestIdArr = [];
      this.bulkUpdateService.deliveryChannelArr =[];
    }
    this.bulkUpdateService.offerBulkSelection.next(null);
    this.bulkUpdateService.bulkSelected$.next(event.target.checked);
  }
  selectOfferPage(event) {
    this.batchType = 'selectAllOnPage'
    this.bulkUpdateService.displayPopup$.next(!event.target.checked);
    event.target.checked
      ? (this.selectPageBool = true)
      : (this.selectPageBool = false);
    if (this.selectPageBool && this.pageData) {
      const ids = event.target.checked
        ? this.pageData.offers
          ? this.pageData.offers.reduce((output, ele) => {
            output.push(ele.info.id.externalOfferId);
            return output;
          }, [])
          : []
        : [];
      this.bulkUpdateService.offersIdArr = ids;
      this.bulkUpdateService.offerIdsListSelected$.next(ids);
      this.isPopupDisabled = this.bulkUpdateService.offersIdArr.length
        ? false
        : true;
    } else {
      this.bulkUpdateService.offerIdsListSelected$.next([]);
      this.bulkUpdateService.offersIdArr = [];
    }
    this.bulkUpdateService.bulkSelectionForOffers.next(null);
    this.bulkUpdateService.allOffersSelected$.next(event.target.checked);
  }

  expandAll() {
    this.facetItemService.expandSub.next(true);
    this.onexpandAll.emit("expand");
    this.showExpand = false;
  }
  collapseAll() {
    this.facetItemService.expandSub.next(false);
    this.onexpandAll.emit("collapse");
    this.showExpand = true;
  }
  showWarning(){
    if(this.pgCodeCount > 1)
      this._toaster.warning("Please select only one program code to perform batch actions (except for Export Offers).","",{closeButton:true}); 
  }
  getSelectedSortList() {
    switch (this.headerPage) {
      case "storeGroup":
      case "pointGroup":
      case "customerGroup": {
        this.sortOptionList = CONSTANTS.STORE_SORT_LIST;
        this.setFormValue(this.sortOptionFormGroupOne, this.sortOptionList[1].field, CONSTANTS.DESC);
        break;
      }
      case "offerHomePage": {
        this.sortOptionList = CONSTANTS.SEARCH_SORT_LIST;
        this.setFormValue(this.sortOptionFormGroupOne, this.sortOptionList[1].field, CONSTANTS.DESC);
        break;
      }
      case "homePage": {
        if (this.selectedPC === "Store Coupon" || this.selectedPC === CONSTANTS.SC) {
          this.sortOptionList = CONSTANTS.REQ_SEARCH_SORT_LIST;
        } else if (this.selectedPC === "Grocery Reward" || this.selectedPC === CONSTANTS.GR) {
          this.sortOptionList = CONSTANTS.REQ_SEARCH_SORT_LIST_GR;
        } else if ([CONSTANTS.SPD_FULL_TEXT, CONSTANTS.SPD].includes(this.selectedPC)) {
          this.sortOptionList = CONSTANTS.REQ_SEARCH_SORT_LIST_SPD;
        } else if ([CONSTANTS.BPD_FULL_TEXT, CONSTANTS.BPD].includes(this.selectedPC)) {
          this.sortOptionList = CONSTANTS.REQ_SEARCH_SORT_LIST_SPD;
        }
        this.setFormValue(this.sortOptionFormGroupOne, this.sortOptionList?.[1].field, CONSTANTS.DESC);
        this.setFormValue(this.sortOptionFormGroupTwo, this.sortOptionList?.[2].field, CONSTANTS.ASC);
        break;
        }
       
    }
  
    }
 
  listIconClick() {
    this.showListIcon = !this.showListIcon;
    this.showGridIcon = !this.showGridIcon;
    this.getSelectedSortList();
    this.setFormValue(this.sortOptionFormGroupOne, CONSTANTS.LAST_MODIFY_DATE, CONSTANTS.DESC);
    this.onListIconClick.emit("list");
    this.hideLink = false;
  }

  gridIconClick() {
    this.showListIcon = !this.showListIcon;
    this.showGridIcon = !this.showGridIcon;
    this.setFormValue(this.sortOptionFormGroupOne, POD_CONSTANTS.POD_HEADLINE, CONSTANTS.ASC);
    this.sortOptionList = CONSTANTS.GRID_VIEW_SORT_LIST;
    this.onGridIconClick.emit("grid");
    this.hideLink = true;
  }


  get showBatchItems() {
    return ["homePage", "offerHomePage", "templatesHomePg"].includes(this.headerPage) && !this.hideLink;
  }
  searchStoreGroups() {
    this.storeGroupService.searchStoreGroup(this.queryGenerator.getQuery(), false).subscribe((items) => {
      this.storeGroupService.getAllStoreGroupSearch(items);
    });
  }
  getOfferRequestData(paramsList) {
    if(!this.featureFlagService.isUPPFieldSearchEnabled)
    {
      paramsList.push({remove: false, parameter: CONSTANTS.CREATED_APP_ID, value: "OMS"});
    }
    const queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    this.queryGenerator.pushParameters({ paramsList });
   
    this._searchOfferRequestService.searchAllOfferRequest(this.queryGenerator.getQuery(), false, queryWithOrFilters)
      .subscribe((result: any) => {
        result.pagination = true;
        this._searchOfferRequestService.getOfferDetails(result);
      });
  }
  searchCustomerGroups() {
    this._customerGroupService.getCustomerGroupByName(this.queryGenerator.getQuery(), false).subscribe((result: any) => {
      result.pagination = true;
      this._customerGroupService.customerGroupSource.next(result);
    });
  }
 
  searchPointGroups() {
    this._pointGroupService.searchPointGroup(this.queryGenerator.getQuery()).subscribe((result: any) => {
      result.pagination = true;
      this._pointGroupService.getAllPointGroupSearch(result);
    });
  }
  getOffersList(paramsList) {
    if(!this.featureFlagService.isUPPFieldSearchEnabled)
    {
      paramsList.push({remove: false, parameter: CONSTANTS.CREATED_APP_ID, value: "OMS"});
    }
    this.queryGenerator.pushParameters({ paramsList });
    this.authService.onUserDataAvailable(this.searchAllOffersApi.bind(this));
  }
  searchOfferRequest(removeList: string[], paramsList: any[]) {
    this.queryGenerator.removeParameters(removeList);
    switch (this.headerPage) {
      case "storeGroup": {
        this.queryGenerator.pushParameters({ paramsList });
        this.searchStoreGroups();
        break;
      }
      case "homePage": {
        this.getOfferRequestData(paramsList);
        break;
      }
      case "customerGroup": {
        this.queryGenerator.pushParameters({ paramsList });
        this.searchCustomerGroups();
        break;
      }
      case "offerHomePage": {
        this.getOffersList(paramsList);
        break;
      }
      case "pointGroup": {
        this.queryGenerator.pushParameters({ paramsList });
        this.searchPointGroups();
        break;
      }
    }
    }

  
  searchAllOffersApi(event = false) {
    const queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    this._searchOfferService.searchAllOffers(this.queryGenerator.getQuery(), false, queryWithOrFilters).subscribe((result: any) => {
      result.pagination = true;
      this._searchOfferRequestService.getOfferDetails(result);
    });
  }
  resetCheckBoxes() {
    this.bulkUpdateService.requestIdsListSelected$.next([]);
    this.bulkUpdateService.offerIdsListSelected$.next([]);
    this.bulkUpdateService.deliveryChannelArr = [];
    this.bulkUpdateService.offerBulkSelection.subscribe((value) => {
      if (value) {
        if (value === "selectAllOnPage") {
          this.bulkUpdateService.offerBulkSelection.next(null)
        }
      }
    })
    this.bulkUpdateService.bulkSelectionForOffers.subscribe((value) => {
      if (value) {
        if (value === "selectAllOnPage") {
          this.bulkUpdateService.bulkSelectionForOffers.next(null)
        }
      }
    })
  }

  getSortByValue(event: any) {
    this.resetCheckBoxes();
    this.facetItemService.setOfferFilter("facetFilter");
    this.sortOfferRequestSearch();
  }

 


  /**
   * 
   * @param item 
   * Moved common code for when user select sort option to sepreate function to reuse in template
   */
  getCommonRemoveAndParamsList() {
    let _removeList = [CONSTANTS.SORT_BY,
      CONSTANTS.NEXT,
      CONSTANTS.LIMIT,
      CONSTANTS.SID,]
    if(!this.featureFlagService.isUPPFieldSearchEnabled)
    {
      _removeList.push(CONSTANTS.CREATED_APP_ID);
    }
    return {
      removeList: _removeList,
      paramsList: [
        {
          remove: false,
          parameter: CONSTANTS.SORT_BY,

          value: this.headerPage === "homePage" ? `${this.sortOptionFormGroupOne.value.sortValue}${this.sortOptionFormGroupOne.value.sortType},${this.sortOptionFormGroupTwo.value.sortValue}${this.sortOptionFormGroupTwo.value.sortType}` : `${this.sortOptionFormGroupOne.value.sortValue}${this.sortOptionFormGroupOne.value.sortType}`,
        },
        {
          remove: false,
          parameter: CONSTANTS.LIMIT,
          value: CONSTANTS.PAGE_LIMIT,
          }
      ]
    }
    }

  sortOfferRequestSearch() {
    // tslint:disable-next-line:one-variable-per-declaration

    if (this.baseInputSearchService.getActiveCurrentSearchType() === CONSTANTS.BPD && this.headerPage === "homePage") {
      this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SID, null);
      this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.NEXT, 1);
      this.headerPage === "homePage" ? this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SORT_BY, `${this.sortOptionFormGroupOne.value.sortValue}${this.sortOptionFormGroupOne.value.sortType},${this.sortOptionFormGroupTwo.value.sortValue}${this.sortOptionFormGroupTwo.value.sortType}`) :
        this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SORT_BY, `${this.sortOptionFormGroupOne.value.sortValue}${this.sortOptionFormGroupOne.value.sortType}`);
      this.baseManagementService.fetchPaginationData(false);
      return false;
    }

      let queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
     
     const { removeList, paramsList } = this.getCommonRemoveAndParamsList();
    if (this.sortOptionFormGroupOne.value.sortValue === 'pageMod') {
      if (!queryWithOrFilters.includes('adType=(IA)')) {
        queryWithOrFilters.push("adType=(IA)");
      }
      this.facetItemService.podFilterChanged = true;
    }
    if (this.sortOptionFormGroupOne.value.sortValue === 'headLine') {
      this.queryGenerator.removeParamFromQueryFilter('adType');
      queryWithOrFilters = this.queryGenerator.getQueryWithFilter();
    }
    if (this.facetItemService.facetItemComponent) {
      this.facetItemService.facetItemComponent.populatePageModQuery(this.sortOptionFormGroupOne.value.sortValue);
    }
    if (this.facetItemService.facetChipComponent) {
      this.facetItemService.facetChipComponent.populatePageModQuery(this.sortOptionFormGroupOne.value.sortValue);
    }
    this.queryGenerator.setQueryWithFilter(queryWithOrFilters);
    this.searchOfferRequest(removeList, paramsList);     
  }
  setFormValue(form, sortVale, sortType) {
    form.controls['sortValue'].setValue(sortVale);
    form.controls['sortType'].setValue(sortType);
    this.sortFormOptionGroups = {
      sortOne: this.sortOptionFormGroupOne
    }
    if(this.headerPage==='homePage'){
      this.sortFormOptionGroups.sortTwo=this.sortOptionFormGroupTwo;
      }
  }
  createOptionFormGroup() {
    this.sortOptionFormGroupOne = new UntypedFormGroup({
      'sortValue': new UntypedFormControl(),
      'sortType': new UntypedFormControl()
    });
    if (this.headerPage === 'homePage') {
      this.sortOptionFormGroupTwo = new UntypedFormGroup({
        'sortValue': new UntypedFormControl(),
        'sortType': new UntypedFormControl()
      });
    }
  }
  ngOnDestroy() {
    this.collapseAll();
    this.resetBatchSelections();
    this.subs?.unsubscribe();
  }
}

<form [formGroup]="inputFormGroup">
<div class="row template-search">
  <div class="col-12 p-0 offset-3" [ngClass]= "currentSearchType === constants.BPD ? 'splPadding' : '' ">
    <a
      href="javascript:void(0)"
      aria-expanded="false"
      aria-haspopup="true"
      (click)="getSystemAndUserSavedSearches()"
      class="saved-searches dropdown-toggle actions-button"
      data-toggle="dropdown"
      >Saved Searches<img alt="arrow-down-icon" class="pl-3" src="assets/icons/blue_downArrow.svg" />
    </a>
    <small *ngIf="isfiltered"  class="text-danger"
      >You must select search or filter and enter a name before saving.</small
    >
    <div aria-labelledby="dropdownMenuLink" class="dropdown-menu search-save" x-placement="bottom-start">
      <div class="">
        <div class="col-12 input-group px-1 pt-1">
          <input
            type="text"
            (input)="isSavedSearchExist()"
            placeholder="Enter Search Name to Save"
            formControlName="savedSearchName"
            class="form-control"
          />
          <button class="btn btn-primary custom-btn-saved" (click)="savedSearchHandler()">{{savedSearched}}</button>
        </div>
        <div class="saved-searches-div">
          <div class="col-12 request-suggestion search-results p-0" *ngIf="savedSearchList?.length">
            <ul class="list-group">
              <li *ngFor="let item of savedSearchList" [ngClass]="item.name ? 'list-group-item custom-list-style' : 'list-unstyled mb-2 mt-2'">
                <div *ngIf="item.name">
                  <span class="cursor-pointer" (click)="applySavedSearch(item)">{{ item.name }} </span>
                  <img
                    *ngIf="item.isRemoveSavedSearch"
                    class="cursor-pointer float-right"
                    src="assets/icons/remove.svg"
                    alt=""
                    (click)="removeSavedSearch(item, $event)"
                  />
                </div>
                <div *ngIf="!item.name && isDivident" class="line"></div>
              </li>
            </ul>
          </div>
          
          
        </div>
      </div>
    </div>
  </div>
  
  
</div>
</form>
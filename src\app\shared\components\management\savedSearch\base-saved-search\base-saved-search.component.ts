import { Component, Input, OnInit } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { LoaderService } from "@appServices/common/loader.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { BaseSavedSearchService } from "@appServices/management/base-saved-search.service";
import { forkJoin } from "rxjs";
import { map } from "rxjs/operators";

@Component({
  selector: "app-base-saved-search",
  templateUrl: "./base-saved-search.component.html",
  styleUrls: ["./base-saved-search.component.scss"],
})
export class BaseSavedSearchComponent implements OnInit {
  isDivident = false;
  userType = {
    template: "T",
    offer: "O",
    request: "R",
  };
  savedSearched: any = "Save";
  userSavedSearchNames: any;
  constants;
  savedSearchList;
  isNameExist = false;
  @Input() inputFormGroup;
  @Input() currentSearchType;
  @Input() resetFilterOptions;
  fieldsList = (options) =>
    options.reduce((output, ele) => {
      output.push(ele.field);
      return output;
    }, []);
  fieldElement = (fields, field) => fields.filter((ele) => ele.field === field);

  constructor(
    public facetItemService: FacetItemService,
    private loaderService: LoaderService,
    private commonRouteService: CommonRouteService,
    private baseSavedSearchService: BaseSavedSearchService,
    private baseInputSearchService: BaseInputSearchService,
    private commonSearchService: CommonSearchService
  ) {
    // intentionally left empty
  }
  ngOnInit(): void {
    this.constants = CONSTANTS;
  }
  get currentUserType() {
    const router = this.commonRouteService.currentRouter;
    return this.userType[router];
  }
  getSystemAndUserSavedSearches() {
    this.inputFormGroup?.get("savedSearchName")?.setValue("");
    this.loaderService.isDisplayLoader(true);
    const query = `sortBy=nameASC;type=${this.currentUserType};`;
    forkJoin({
      systemSavedSearches: this.baseSavedSearchService?.fetchSavedSearch(query, "S").pipe(map((res: any) => res.savedSearches)),
      userSavedSearches: this.baseSavedSearchService?.fetchSavedSearch(query, "U").pipe(map((res: any) => res.savedSearches)),
    }).subscribe(({ systemSavedSearches, userSavedSearches }) => {
      if (this.baseInputSearchService.getActiveCurrentSearchType() === CONSTANTS.BPD) {
        systemSavedSearches = [];
      }
      this.loaderService.isDisplayLoader(false);
      this.isDivident = systemSavedSearches.length > 0;
      this.userSavedSearchNames = userSavedSearches.map((ele) => ele.name);
      this.savedSearchList = [
        ...systemSavedSearches,
        ...[{ name: "" }],
        ...userSavedSearches.map((ele) => {
          ele.isRemoveSavedSearch = true;
          return ele;
        }),
      ];
    });
  }
  isSavedSearchExist() {
    const { savedSearchName } = this.inputFormGroup.value;
    this.savedSearched = this.userSavedSearchNames.some((ele) => ele === savedSearchName) ? "Update" : "Save";
  }
  get isfiltered() {
    return this.commonSearchService.isFiltered;
  }

  queryWithOrFilterWrap(){    
    let queryWithOrFilter =  this.baseInputSearchService.queryWithOrFilter;
    
    const expiredStatusStr = `(${CONSTANTS.EXPIRED_STATUS_OR})`;
     //Replace expired status with Completed status
     for(let i = 0; i <queryWithOrFilter.length; i++){
      if(queryWithOrFilter[i].includes(expiredStatusStr)){      
        queryWithOrFilter[i] = queryWithOrFilter[i].replaceAll(expiredStatusStr,`(${CONSTANTS.COMPLETED_STATUS_OR})`)
      }
    }  
    return queryWithOrFilter.map((ele) => `'${ele}'`).join(',');
  }

  savedSearchHandler() {
    const { savedSearchName } = this.inputFormGroup.value;

    if (!this.commonSearchService.isFiltered && !savedSearchName?.length) {
      this.commonSearchService.isFiltered = true;
      return false;
    }
    this.loaderService.isDisplayLoader(true);

    const query = this.baseInputSearchService.getQueryForInputAndFilter(true);
   
    let queryWithOrFilter = this.queryWithOrFilterWrap();
     
    const savedSearchQuery = queryWithOrFilter?.length ? `${query}queryWithOrFilters:[${queryWithOrFilter}]` : `${query}`;
    this.baseSavedSearchService?.[`${this.savedSearched.toLowerCase()}SavedSearch`](
      savedSearchQuery,
      savedSearchName,
      this.currentUserType,
      "U"
    ).subscribe((response) => {
      this.loaderService.isDisplayLoader(false);
      this.inputFormGroup.get("savedSearchName").setValue("");
      this.commonSearchService.isFiltered = false;
      this.savedSearched = "Save";
    });
  }
  getQueryWithFilter(arrayFilter) {
    return arrayFilter?.reduce((output, ele, indx) => {
      if (indx === 0 && indx === arrayFilter.length - 1 && ele.includes("'")) {
        output.push(ele.slice(2, ele.length - 2).replace(/'/g, ""));
      } else {
        if (ele.includes("'") && indx == 0) {
          output.push(ele.slice(2, ele.length - 1).replace(/'/g, ""));
        } else if (indx == arrayFilter.length - 1 && ele.includes("'")) {
          output.push(ele.slice(1, ele.length - 2).replace(/'/g, ""));
        } else {
          output.push(ele.slice(1, ele.length - 1));
        }
      }
      return output;
    }, []);
  }
  removeSavedSearch(item) {
    this.loaderService.isDisplayLoader(true);
    this.baseSavedSearchService.deleteSaveSearch(item.name, this.currentUserType).subscribe((ele) => {
      this.loaderService.isDisplayLoader(false);
      this.getSystemAndUserSavedSearches();
    });
  }
  setQueryForQueryWithFilterOption(queryWithFilterList) {
    if (!queryWithFilterList?.length) {
      return false;
    }
    const filterOptions = this.baseInputSearchService.filterOptions;
    const queryFilterOptions = this.baseInputSearchService.addExtraToElement.addAsQueryWithFilter[0];
    filterOptions?.forEach((option) => {
      if (queryFilterOptions[option.field]) {
        const updatedQuery = this.getQueryFilter(queryFilterOptions[option.field], queryWithFilterList);
        if (updatedQuery) {
          option.query = updatedQuery.split(" OR ");
        }
      }
    });
  }
  getQueryFilter(item, queryWithFilterList) {
    const queryWithFilter = queryWithFilterList?.find((ele) => item.some((op) => ele.indexOf(op) !== -1));
    if (queryWithFilter) {
      const filter = queryWithFilter.split("#");
      return filter.reduce((output, ele) => {
        let splitStatus = ele.indexOf("=(") === -1 ? ele.split("=")[1] : ele.split("=(")[1];
        splitStatus = splitStatus.replace(/[/)/(]/gi, "");
        if (!output) {
          output = splitStatus;
        } else {
          if (!output.includes(splitStatus)) {
            output = `${output} OR ${splitStatus}`;
          }
        }
        return output;
      }, "");
    }
  }
  applySavedSearch(item) {
    this.inputFormGroup.get("savedSearchName").setValue("");
    const { searchQuery } = item,
      splitQueryWithSemiColon = searchQuery.split(";"),
      searchArray = splitQueryWithSemiColon.reduce((output, ele) => {
        let splitEle;
        if (ele.includes("queryWithOrFilters")) {
          splitEle = ele.split(":");
        } else {
          splitEle = ele.split("=");
        }
        if (splitEle[0]) {
          output[splitEle[0]] = splitEle[1];
        }

        return output;
      }, {});

    const queryWithOrFilter = searchArray["queryWithOrFilters"]?.split(",");
    const formattedQueryFilter = this.getQueryWithFilter(queryWithOrFilter);
    this.commonSearchService.setEmptyQueryForAllFields({
      currentRouter: this.baseInputSearchService.currentRouter,
      programCode: this.currentSearchType,
    });

    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.NEXT, 1);
    this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SID, null);
    
    const filterOption = this.commonSearchService.getFilterOption(this.currentSearchType),
      inputSearchOption = this.commonSearchService.getInputSearchOption(this.currentSearchType),
      defaultOption = this.commonSearchService.getDefaultOption(this.currentSearchType),
      filterFields = this.fieldsList(filterOption),
      inputSearchFields = [...this.fieldsList(inputSearchOption),...["minusLastPeriodCreated"]],
      defaultFields = this.fieldsList(defaultOption),
      inputGroup = [...this.baseInputSearchService.inputGroups, ...Object.keys(this.baseInputSearchService.inputGroupsLevel)],
      filterGroup = this.baseInputSearchService.filterGroupsLevel;

    this.setQueryForQueryWithFilterOption(formattedQueryFilter);

    Object.keys(searchArray).forEach((element) => {
      if (filterFields.includes(element)) {
        const selectedField = this.baseInputSearchService.getFilterFieldSelected(element);
        const value = this.baseInputSearchService.generateFilterValue(element, searchArray[element]);
        this.baseInputSearchService.generateQueryForFilterOptions(selectedField, value);
      } else if (inputSearchFields.includes(element) && inputGroup.includes(element)) {
        let inputGroupsLevel,
          inputGroupsLevelEle = this.baseInputSearchService.inputGroupsLevel[element];

        const value = this.baseInputSearchService.generateValue(element, searchArray[element], inputGroupsLevelEle),
          field = this.fieldElement(inputSearchOption, element);
        if (inputGroupsLevelEle) {
          inputGroupsLevel = inputGroupsLevelEle.map((ele, index) => value.inputGroupsLevel[index]);
        }
        const groupLevelObject: any = this.baseInputSearchService.createActualInputGroupLevelObject(
          element,
          value.inputGroups,
          inputGroupsLevel
        );
        const selectedField = this.baseInputSearchService.getInputFieldSelected(element);

        this.baseInputSearchService.generateQueryForOptions(selectedField, groupLevelObject.inputGroups, groupLevelObject.inputGroupsLevel);
        this.baseInputSearchService.setChipForField(selectedField, groupLevelObject.inputGroups[0], groupLevelObject.inputSelected);
      } else if (defaultFields.includes(element)) {
        const query = searchArray[element],
          field = this.fieldElement(defaultOption, element);
        if (field.length) {
          this.baseInputSearchService.showChip(field[0]);
          this.baseInputSearchService.pushToQuery(field[0], element, query);
        }
      }
    });

    

    this.baseInputSearchService.formQuery(
      this.commonSearchService.getFilterOption(this.currentSearchType),
      this.commonSearchService.getInputSearchOption(this.currentSearchType),
      this.commonSearchService.getDefaultOption(this.currentSearchType),
      this.commonSearchService.getSortOption(this.currentSearchType)
    );

    this.baseInputSearchService.generateSearchChip();
    this.baseInputSearchService.populateChipList();
    
    this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}FacetFilterBehaviorSubject`]?.next(
      this.commonSearchService.getFilterOption(this.currentSearchType)
    );

    this.baseInputSearchService.postDataForInputSearch(true);
    this.commonSearchService.isFiltered = false;
  }
}

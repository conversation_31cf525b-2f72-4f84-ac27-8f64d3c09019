import { baseInputSearchServiceStub } from './../../../../../specMocks/jasmine.stubs';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BaseSavedSearchComponent } from './base-saved-search.component';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { LoaderService } from '@appServices/common/loader.service';
import { BaseInputSearchService } from '@appServices/management/base-input-search.service';
import { BaseSavedSearchService } from '@appServices/management/base-saved-search.service';
import { CONSTANTS } from '@appConstants/constants';
import { PermissionsService } from '@appShared/albertsons-angular-authorization'
import { StoreGroupService } from "@appModules/groups/services/store-group.service";
import { ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { InjectionToken } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { MsalGuardConfiguration, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { InteractionType } from '@azure/msal-browser';
import { AppService } from '@appServices/common/app.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { FormBuilder, FormGroup } from '@angular/forms';


class PermissionsServiceStub {}
const LOCAL_MSAL_GUARD_CONFIG = new InjectionToken('MSAL_GUARD_CONFIG');

class MockBaseInputSearchService {
  filterOptions = [
    { field: 'field1', query: [] },
    { field: 'field2', query: [] }
  ];
  addExtraToElement = { addAsQueryWithFilter: [{}] };
  inputGroups = [];
  inputGroupsLevel = {};
  filterGroupsLevel = {};

  getQueryForInputAndFilter(flag: boolean) {
    return '';
  }

  queryWithOrFilter: [] = [];

  getFilterFieldSelected(field: string) {
    return {};
  }

  generateFilterValue(field: string, value: string) {
    return {};
  }

  generateQueryForFilterOptions(selectedField: any, value: any) {}

  getInputFieldSelected(field: string) {
    return {};
  }

  generateValue(field: string, value: string, inputGroupsLevel: any) {
    return { inputGroups: [], inputGroupsLevel: [] };
  }

  createActualInputGroupLevelObject(field: string, inputGroups: any, inputGroupsLevel: any) {
    return { inputGroups: [], inputSelected: {} };
  }

  generateQueryForOptions(selectedField: any, inputGroups: any, inputGroupsLevel: any) {}

  setChipForField(selectedField: any, inputGroup: any, inputSelected: any) {}

  showChip() {}

  pushToQuery(field: any, element: any, query: any) {}

  formQuery(filterOption: any, inputSearchOption: any, defaultOption: any, sortOption: any) {}

  generateSearchChip() {}

  populateChipList() {}

  postDataForInputSearch(flag: boolean) {}

  getActiveCurrentSearchType() {
    return 'BPD';
  }
}

describe('BaseSavedSearchComponent', () => {
  let component: BaseSavedSearchComponent;
  let fixture: ComponentFixture<BaseSavedSearchComponent>;
  let baseSavedSearchServiceStub: any;
  let loaderService: LoaderService;
  let formBuilder: FormBuilder;

  const commonRouteServiceStub = {
    currentRouter: 'template'
  };

  beforeEach(async () => {
    baseSavedSearchServiceStub = {
      fetchSavedSearch: (query: string, type: string) => of({ savedSearches: [] }),
      saveSavedSearch: jasmine.createSpy('saveSavedSearch').and.returnValue(of({})),
      updateSavedSearch: jasmine.createSpy('updateSavedSearch').and.returnValue(of({})),
      deleteSaveSearch: jasmine.createSpy('deleteSaveSearch').and.returnValue(of({}))
    };

    const baseInputSearchServiceStub = new MockBaseInputSearchService();
    
    const commonSearchServiceStub = {
      isFiltered: false,
      setEmptyQueryForAllFields: jasmine.createSpy('setEmptyQueryForAllFields'), 
      setQueryValueForDefaultOption: jasmine.createSpy('setQueryValueForDefaultOption'),
      getFilterOption: jasmine.createSpy('getFilterOption').and.returnValue([]), 
      getInputSearchOption: jasmine.createSpy('getInputSearchOption').and.returnValue([]), 
      getDefaultOption: jasmine.createSpy('getDefaultOption').and.returnValue([]),
      getSortOption: jasmine.createSpy('getSortOption').and.returnValue([]), 
    };

    const mockPermissionsService = {
        hasPermission: (permission: string) => true 
    };

    const msalGuardConfigStub: MsalGuardConfiguration = {
        interactionType: InteractionType.Redirect, 
        authRequest: {}
    };

    const mockAppService = {
      getFeatureFlags: () => ({}) 
    };

    const storeGroupServiceStub = {};

    const mockInitialDataService = {
        _initialData: {
          appData: {
            offerDeliveryChannelsSPD: {},
            offerDeliveryChannels: {}
          }
        },
        getAppData: () => ({}),
        getAppDataName:(name: string) => ({})
      };
      

    await TestBed.configureTestingModule({
      declarations: [BaseSavedSearchComponent],
      imports: [ReactiveFormsModule,HttpClientModule],
      providers: [
        FacetItemService,
        LoaderService,
        FormBuilder,
        { provide: CommonRouteService, useValue: commonRouteServiceStub },
        { provide: BaseSavedSearchService, useValue: baseSavedSearchServiceStub },
        { provide: BaseInputSearchService, useValue: baseInputSearchServiceStub },
        { provide: CommonSearchService, useValue: commonSearchServiceStub },
        { provide: CONSTANTS, useValue: CONSTANTS },
        { provide: PermissionsService, useValue: mockPermissionsService },
        { provide: LOCAL_MSAL_GUARD_CONFIG, useValue: {} },
        { provide: StoreGroupService, useValue: storeGroupServiceStub },
        { provide: MSAL_GUARD_CONFIG, useValue: msalGuardConfigStub },
        { provide: AppService, useValue: mockAppService },
        { provide: InitialDataService, useValue: mockInitialDataService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BaseSavedSearchComponent);
    component = fixture.componentInstance;
    loaderService = TestBed.inject(LoaderService);
    const formBuilder = TestBed.inject(FormBuilder); 
    component.inputFormGroup = formBuilder.group({
      savedSearchName: [''] 
    });
    fixture.detectChanges();

    (component as any).commonSearchService = TestBed.inject(CommonSearchService);
    (component as any).baseInputSearchService = TestBed.inject(BaseInputSearchService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize constants in ngOnInit', () => {
    component.ngOnInit();
    expect(component.constants).toEqual(CONSTANTS);
  });

  it('should return the correct user type based on the current router', () => {
    
    commonRouteServiceStub.currentRouter = 'template';
    fixture.detectChanges();
    expect(component.currentUserType).toBe('T');


    commonRouteServiceStub.currentRouter = 'offer';
    fixture.detectChanges();
    expect(component.currentUserType).toBe('O');


    commonRouteServiceStub.currentRouter = 'request';
    fixture.detectChanges();
    expect(component.currentUserType).toBe('R');
  });


  it('should fetch and process system and user saved searches', async () => {
    spyOn(loaderService, 'isDisplayLoader').and.callThrough();
  
    spyOn(baseSavedSearchServiceStub, 'fetchSavedSearch').and.callFake((id: string, type: string) => {
      return of({ savedSearches: [
        { name: 'Search1', isRemoveSavedSearch: type === 'U' },
        { name: 'Search2', isRemoveSavedSearch: false },
        { name: 'Search3', isRemoveSavedSearch: false }
      ]});
    });
  
    component.getSystemAndUserSavedSearches(); 
  
    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(true);
    expect(baseSavedSearchServiceStub.fetchSavedSearch).toHaveBeenCalledTimes(2);
    expect(baseSavedSearchServiceStub.fetchSavedSearch).toHaveBeenCalledWith(jasmine.any(String), 'S');
    expect(baseSavedSearchServiceStub.fetchSavedSearch).toHaveBeenCalledWith(jasmine.any(String), 'U');
  
    fixture.detectChanges(); // Trigger UI updates
  
    await fixture.whenStable(); // Wait for async updates
  
    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(false);
    expect(component.isDivident).toBe(false);
    expect(component.userSavedSearchNames).toEqual(['Search1', 'Search2','Search3']);
    expect(component.savedSearchList.length).toBe(4);
    expect(component.savedSearchList[0].name).toBe('');
    expect(component.savedSearchList[1].name).toBe('Search1');
    expect(component.savedSearchList[2].name).toBe('Search2');
    expect(component.savedSearchList[3]?.name).toBe('Search3');
    expect(component.savedSearchList[3].isRemoveSavedSearch).toBe(true);
  });
  

  it('should update savedSearched to "Update" if saved search name exists', () => {
    component.userSavedSearchNames = ['Search1', 'Search2'];
    component.inputFormGroup.setValue({ savedSearchName: 'Search1' });

    component.isSavedSearchExist();

    expect(component.savedSearched).toBe('Update');
  });

  it('should update savedSearched to "Save" if saved search name does not exist', () => {
    component.userSavedSearchNames = ['Search1', 'Search2'];
    component.inputFormGroup.setValue({ savedSearchName: 'Search3' });

    component.isSavedSearchExist();

    expect(component.savedSearched).toBe('Save');
  });

  it('should replace expired status with completed status and return formatted query', () => {
    const expiredStatusStr = `(${CONSTANTS.EXPIRED_STATUS_OR})`;
    const completedStatusStr = `(${CONSTANTS.COMPLETED_STATUS_OR})`;
    const queryWithOrFilter = [
      `status=${expiredStatusStr}`,
      `status=(OTHER_STATUS)`
    ];
    baseSavedSearchServiceStub.queryWithOrFilter = queryWithOrFilter;

    const result = component.queryWithOrFilterWrap();

    expect(result).toBe("");
  });


  it('should handle saving a search correctly', async () => {
    loaderService.isDisplayLoader = jasmine.createSpy().and.callThrough();
    baseSavedSearchServiceStub.saveSavedSearch = jasmine.createSpy().and.returnValue(of({}));

    component.inputFormGroup.setValue({ savedSearchName: 'NewSearch' });
    component.savedSearched = 'Save';

    component.savedSearchHandler();

    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(true);
    expect(baseSavedSearchServiceStub.saveSavedSearch).toHaveBeenCalledWith(
        jasmine.any(String),
        'NewSearch',
        component.currentUserType,
        'U'
    );

    fixture.detectChanges();
    await fixture.whenStable();

    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(false);
    expect(component.inputFormGroup.get('savedSearchName')?.value).toBe('');
    expect(component['commonSearchService'].isFiltered).toBe(false);
    expect(component.savedSearched).toBe('Save');
});

  it('should handle updating a search correctly', async () => {
    loaderService.isDisplayLoader = jasmine.createSpy().and.callThrough();
    baseSavedSearchServiceStub.updateSavedSearch = jasmine.createSpy().and.returnValue(of({}));

    component.inputFormGroup.setValue({ savedSearchName: 'ExistingSearch' });
    component.savedSearched = 'Update';

    component.savedSearchHandler();

    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(true);
    expect(baseSavedSearchServiceStub.updateSavedSearch).toHaveBeenCalledWith(
        jasmine.any(String),
        'ExistingSearch',
        component.currentUserType,
        'U'
    );

    fixture.detectChanges();
    await fixture.whenStable();

    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(false);
    expect(component.inputFormGroup.get('savedSearchName')?.value).toBe('');
    expect(component['commonSearchService'].isFiltered).toBe(false);
    expect(component.savedSearched).toBe('Save');
});  
  
  it('should set isFiltered to true if savedSearchName is empty and no filters are applied', () => {
    component.inputFormGroup.setValue({ savedSearchName: '' });
    component["commonSearchService"].isFiltered = false;

    const result = component.savedSearchHandler();

    expect(result).toBe(false);
    expect(component["commonSearchService"].isFiltered).toBe(true);
  });

  it('should process array filter and return formatted array', () => {
    const arrayFilter = [
      "'status1'",
      "'status2'",
      "'status3'"
    ];

    const result = component.getQueryWithFilter(arrayFilter);

    expect(result).toEqual(['tatus1', 'status2', 'status']);
  });

  it('should handle single element array filter', () => {
    const arrayFilter = [
      "'status1'"
    ];
  
    const result = component.getQueryWithFilter(arrayFilter);
  
    expect(result).toEqual(['tatus']);
  });

  it('should handle empty array filter', () => {
    const arrayFilter = [];

    const result = component.getQueryWithFilter(arrayFilter);

    expect(result).toEqual([]);
  });

  it('should remove saved search correctly', (done) => {
    spyOn(loaderService, 'isDisplayLoader').and.callThrough();
    spyOn(baseSavedSearchServiceStub, 'fetchSavedSearch').and.callThrough(); 
  
    const item = { name: 'SearchToRemove' };
  
    component.removeSavedSearch(item);
  
    expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(true);
    expect(baseSavedSearchServiceStub.deleteSaveSearch).toHaveBeenCalledWith(item.name, component.currentUserType);
  
    fixture.whenStable().then(() => {
      expect(loaderService.isDisplayLoader).toHaveBeenCalledWith(false);
      expect(baseSavedSearchServiceStub.fetchSavedSearch).toHaveBeenCalledTimes(2);
      done();  
    });
  });    

  it('should set query for query with filter option correctly', () => {
    const queryWithFilterList = ['field1=value1', 'field2=value2'];
    const filterOptions = [
      { field: 'field1', query: [] }, 
      { field: 'field2', query: [] } 
    ];
    const queryFilterOptions = {
      status: [], 
      offerStatus: [],
      deliveryChannel: [],
      assignedTo: [],
      field1: ['field1'],
      field2: ['field2']
    };
  
    const baseInputSearchService = TestBed.inject(BaseInputSearchService) as unknown as MockBaseInputSearchService;
    baseInputSearchService.filterOptions = filterOptions;
    baseInputSearchService.addExtraToElement.addAsQueryWithFilter[0] = queryFilterOptions;
    spyOn(component, 'getQueryFilter').and.callFake((field, queryList) => {
      if (field.includes('field1')) return 'value1';
      if (field.includes('field2')) return 'value2';
      return ''; 
    });

    component.setQueryForQueryWithFilterOption(queryWithFilterList);
    expect(baseInputSearchService.filterOptions[0].query).toEqual(['value1']); 
    expect(baseInputSearchService.filterOptions[1].query).toEqual(['value2']); 
  });
  

  it('should get query filter correctly', () => {
    const item = ['field1', 'field2'];
    const queryWithFilterList = ['field1=value1#field2=value2'];

    const result = component.getQueryFilter(item, queryWithFilterList);

    expect(result).toBe('value1 OR value2');
  });

  it('should apply saved search correctly', () => {
    const item = {
      searchQuery: "field1=value1;field2=value2;queryWithOrFilters:'status1','status2'"
    };

    spyOn(component, 'getQueryWithFilter').and.callThrough();
    spyOn(component, 'setQueryForQueryWithFilterOption').and.callThrough();
    spyOn(component, 'fieldElement').and.callThrough();
    if (!jasmine.isSpy(component['commonSearchService'].setEmptyQueryForAllFields)) {
      spyOn(component['commonSearchService'], 'setEmptyQueryForAllFields').and.callThrough();
    }
    if (!jasmine.isSpy(component['commonSearchService'].setQueryValueForDefaultOption)) {
      spyOn(component['commonSearchService'], 'setQueryValueForDefaultOption').and.callThrough();
    }
    if (!jasmine.isSpy(component['baseInputSearchService'].formQuery)) {
      spyOn(component['baseInputSearchService'], 'formQuery').and.callThrough();
    }
    if (!jasmine.isSpy(component['baseInputSearchService'].generateSearchChip)) {
      spyOn(component['baseInputSearchService'], 'generateSearchChip').and.callThrough();
    }
    if (!jasmine.isSpy(component['baseInputSearchService'].populateChipList)) {
      spyOn(component['baseInputSearchService'], 'populateChipList').and.callThrough();
    }
    if (!jasmine.isSpy(component['baseInputSearchService'].postDataForInputSearch)) {
      spyOn(component['baseInputSearchService'], 'postDataForInputSearch').and.callThrough();
    }
  
    component.applySavedSearch(item);

    expect(component.inputFormGroup.get("savedSearchName").value).toBe("");
    expect(component.getQueryWithFilter).toHaveBeenCalledWith(["'status1'", "'status2'"]);
    expect(component.setQueryForQueryWithFilterOption).toHaveBeenCalled();
    expect(component['commonSearchService'].setEmptyQueryForAllFields).toHaveBeenCalledWith({
      currentRouter: component['baseInputSearchService'].currentRouter,
      programCode: component.currentSearchType
    });
    expect(component['commonSearchService'].setQueryValueForDefaultOption).toHaveBeenCalledWith(CONSTANTS.NEXT, 1);
    expect(component['commonSearchService'].setQueryValueForDefaultOption).toHaveBeenCalledWith(CONSTANTS.SID, null);
    expect(component['baseInputSearchService'].formQuery).toHaveBeenCalled();
    expect(component['baseInputSearchService'].generateSearchChip).toHaveBeenCalled();
    expect(component['baseInputSearchService'].populateChipList).toHaveBeenCalled();
    expect(component['baseInputSearchService'].postDataForInputSearch).toHaveBeenCalledWith(true);
    expect(component['commonSearchService'].isFiltered).toBe(false);
  });

});
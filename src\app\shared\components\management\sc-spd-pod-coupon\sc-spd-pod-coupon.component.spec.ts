import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ScSpdPodCouponComponent } from './sc-spd-pod-coupon.component';

describe('ScSpdPodCouponComponent', () => {
  let component: ScSpdPodCouponComponent;
  let fixture: ComponentFixture<ScSpdPodCouponComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ScSpdPodCouponComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ScSpdPodCouponComponent);
    component = fixture.componentInstance;
    component.previewData = {};
    component.offerData = {};
      component.imageurl = 'http://example.com/image.jpg';
    component.tileDetails = {};
    component.elementId = 'test-element';
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should emit onDetailsClick when onLinkClick is called', () => {
    spyOn(component.onDetailsClick, 'emit');
    const fakeEvent = { type: 'click' };
    
    component.onLinkClick(fakeEvent);
    
    expect(component.onDetailsClick.emit).toHaveBeenCalledWith(fakeEvent);
  });

  it('should emit onGridItemClick when onItemClick is called', () => {
    spyOn(component.onGridItemClick, 'emit');
    const fakeEvent = { type: 'click' };
    const fakeOfferData = { id: 1, name: 'Test Offer' };

    component.onItemClick(fakeEvent, fakeOfferData);
    
    expect(component.onGridItemClick.emit).toHaveBeenCalledWith(fakeOfferData);
  });

  it('should emit onGridItemMouseover when onItemMouseover is called', () => {
    spyOn(component.onGridItemMouseover, 'emit');
    const fakeEvent = { type: 'mouseover' };
    const fakeOfferData = { id: 2, name: 'Another Offer' };

    component.onItemMouseover(fakeEvent, fakeOfferData);
    
    expect(component.onGridItemMouseover.emit).toHaveBeenCalledWith(fakeOfferData);
  });
});

import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-sc-spd-pod-coupon',
  templateUrl: './sc-spd-pod-coupon.component.html',
  styleUrls: ['./sc-spd-pod-coupon.component.scss']
})
export class ScSpdPodCouponComponent implements OnInit {

  @Input() previewData: any = {};
  @Input() offerData: any = {};
  @Input() imageurl: string;
  @Input() tileDetails: any;
  @Input() elementId: any;
  @Output() onDetailsClick = new EventEmitter<any>();
  @Output() onGridItemClick = new EventEmitter<any>();
  @Output() onGridItemMouseover = new EventEmitter<any>();

  offerTypes = { 'One-Time use': 'One time use', 'Unlimited use': 'Unlimited use' };

  constructor() {
    // intentionally left empty
   }

  ngOnInit(): void {
    // intentionally left empty
  }

  onLinkClick($event) {
    this.onDetailsClick.emit($event);
  }

  onItemClick($event, offerData) {
    this.onGridItemClick.emit(offerData);
  }

  onItemMouseover($event, offerData) {
    this.onGridItemMouseover.emit(offerData);
  }

}

<ng-container *ngIf="sortFormGroups && sortFormGroups[name]">
  <form class="sorting-container"  [formGroup]="sortFormGroups[name]">
    <label class="ml-2 mb-0 mt-0 text-label">
      <div class="dropdown show">
        <select #select class="custom-select form-control" (change)="sortClickHandler($event)" formControlName="sortValue">
          <ng-container *ngFor="let item of sortOptionList; trackBy: trackByFn; index as i">
          <option *ngIf="disableOption!==item.field" [value]="item.field">
            {{ item.key }}
          </option>
        </ng-container>
        </select>
      </div>
    </label>
    <span (click)="arrowClickHandler()">
      <div *ngIf="sortType === 'DESC'; else showAscSort">
        <label class="mt-2 ml-2 mr-1 cursor-pointer"><img src="assets/icons/descending-icon.svg"
            alt="" /></label>
      </div>
      <ng-template #showAscSort>
        <label class="mt-2 ml-2 mr-1 cursor-pointer"><img src="assets/icons/ascending-icon.svg"
            alt="" /></label>
      </ng-template>
    </span>
  </form>
</ng-container>

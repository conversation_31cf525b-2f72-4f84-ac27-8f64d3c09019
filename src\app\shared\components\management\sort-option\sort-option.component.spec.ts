import { ComponentFixture, TestBed } from "@angular/core/testing";
import { SortOptionComponent } from "./sort-option.component";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { FormGroup } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";

describe('SortOptionComponent - sortClickHandler', () => {
    let component: SortOptionComponent;
    let fixture: ComponentFixture<SortOptionComponent>;

    // Helper function to create a fake form group
    const createFakeFormGroup = () => ({
        value: {
            sortValue: '',
            sortType: 'default'
        },
        controls: {
            sortType: { setValue: jasmine.createSpy('setValue') }
        }
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [SortOptionComponent],
            providers: [
                { provide: BaseInputSearchService, useValue: {} }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(SortOptionComponent);
        component = fixture.componentInstance;
        // Spy on the output emitter
        spyOn(component.getSortByValueEvent, 'emit');
    });

    // Reinitialize the inputs before each test so that formGroup is defined
    beforeEach(() => {
        component.sortFormGroups = { testGroup: createFakeFormGroup() };
        component.name = 'testGroup';
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should have the correct sortBy object', () => {
        expect(component.sortBy).toEqual({ DESC: 'ASC', ASC: 'DESC' });
    });

    it('should return the correct sortValue', () => {
        const fakeFormGroup = {
            value: { sortValue: 'sortValue', sortType: 'someType' },
            controls: { sortType: { setValue: jasmine.createSpy('setValue') } }
        };

        component.sortFormGroups = { testGroup: fakeFormGroup };
        component.name = 'testGroup';

        expect(component.sortValue).toBe('sortValue');
    });

    it('should return the correct sortType', () => {
        const fakeFormGroup = {
            value: { sortValue: 'sortValue', sortType: 'someType' },
            controls: { sortType: { setValue: jasmine.createSpy('setValue') } }
        };

        component.sortFormGroups = { testGroup: fakeFormGroup };
        component.name = 'testGroup';

        expect(component.sortType).toBe('someType');
    });

    it('should return sortFormGroups.sortTwo.value.sortValue when pageType is "homePage" and name is "sortOne"', () => {
        const expectedValue = 'valueFromSortTwo';
        // Set inputs so that the getter returns sortTwo's value when name is 'sortOne'
        component.pageType = 'homePage';
        component.name = 'sortOne';
        component.sortFormGroups = {
            sortTwo: { value: { sortValue: expectedValue } }
        };
        expect(component.disableOption).toBe(expectedValue);
    });

    it('should return sortFormGroups.sortOne.value.sortValue when pageType is "homePage" and name is not "sortOne"', () => {
        const expectedValue = 'valueFromSortOne';
        // Set inputs so that the getter returns sortOne's value when name is not 'sortOne'
        component.pageType = 'homePage';
        component.name = 'sortTwo'; // any value other than 'sortOne'
        component.sortFormGroups = {
            sortOne: { value: { sortValue: expectedValue } }
        };
        expect(component.disableOption).toBe(expectedValue);
    });

    it('should return undefined when pageType is not "homePage"', () => {
        // Set a pageType different from 'homePage' so that the getter returns undefined
        component.pageType = 'otherPage';
        component.name = 'sortOne'; // name is irrelevant here
        component.sortFormGroups = {
            sortOne: { value: { sortValue: 'value1' } },
            sortTwo: { value: { sortValue: 'value2' } }
        };
        expect(component.disableOption).toBeUndefined();
    });

    it('should set sortType to "ASC" and emit event when sortValue is CONSTANTS.REGION_ID', () => {
        // Arrange
        component.sortFormGroups.testGroup.value.sortValue = CONSTANTS.REGION_ID;
        const eventPayload = { some: 'data' };

        // Act
        component.sortClickHandler(eventPayload);

        // Assert
        expect(component.sortFormGroups.testGroup.controls.sortType.setValue).toHaveBeenCalledWith("ASC");
        expect(component.getSortByValueEvent.emit).toHaveBeenCalledWith(eventPayload);
    });

    it('should set sortType to "ASC" and emit null event when sortValue is CONSTANTS.MOB_ID', () => {
        // Arrange
        component.sortFormGroups.testGroup.value.sortValue = CONSTANTS.MOB_ID;

        // Act
        component.sortClickHandler(); // event defaults to null

        // Assert
        expect(component.sortFormGroups.testGroup.controls.sortType.setValue).toHaveBeenCalledWith("ASC");
        expect(component.getSortByValueEvent.emit).toHaveBeenCalledWith(null);
    });

    it('should set sortType to "DESC" and emit event when sortValue is not in the constants list', () => {
        // Arrange
        component.sortFormGroups.testGroup.value.sortValue = 'someOtherValue';
        const eventPayload = 'customEvent';

        // Act
        component.sortClickHandler(eventPayload);

        // Assert
        expect(component.sortFormGroups.testGroup.controls.sortType.setValue).toHaveBeenCalledWith("DESC");
        expect(component.getSortByValueEvent.emit).toHaveBeenCalledWith(eventPayload);
    });
    it('should return the concatenated string of item.filed and selectedPC', () => {
        component.selectedPC = 'TestPC';
        const item = { filed: 'TestField' };
        const index = 0;

        const result = component.trackByFn(index, item);

        expect(result).toEqual('TestFieldTestPC');
    });

    it('should toggle sortType and emit event when arrowClickHandler is called', () => {
        const mockFormGroup = {
            value: {
                sortValue: 'dummy',
                sortType: 'ASC'
            },
            controls: {
                sortType: { setValue: jasmine.createSpy('setValue') }
            }
        };

        component.sortFormGroups = { testGroup: mockFormGroup };
        component.name = 'testGroup';

        component.arrowClickHandler();

        expect(mockFormGroup.controls.sortType.setValue).toHaveBeenCalledWith('DESC');
        expect(component.getSortByValueEvent.emit).toHaveBeenCalled();
    });

});

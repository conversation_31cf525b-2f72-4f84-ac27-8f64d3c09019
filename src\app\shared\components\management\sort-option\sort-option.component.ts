import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { BaseInputSearchService } from '../../../services/management/base-input-search.service';

@Component({
  selector: 'app-sort-option',
  templateUrl: './sort-option.component.html',
  styleUrls: ['./sort-option.component.scss']
})
export class SortOptionComponent implements OnInit {
  @Input() sortFormGroups: any;
  @Input() name: any;
  @Input() sortOptionList: any;
  @Output() getSortByValueEvent = new EventEmitter<any>();
  @Input() pageType: any;
  @Input() selectedPC: any;
  sortBy = {
    DESC: 'ASC',
    ASC: 'DESC'
  }

  selectedSortValue: any;

  constructor(public baseInputSearchService: BaseInputSearchService) {
    // intentionally left empty
  }

  ngOnInit(): void {
    // intentionally left empty       
  }

  get sortValue() {
    return this.formGroup?.value?.sortValue;
  }

  get sortType() {
    return this.formGroup?.value?.sortType;
  }
  get formGroup() {
    return this.sortFormGroups?.[this.name]
  }
  get disableOption() {
    if (this.pageType === 'homePage') {
      if (this.name === 'sortOne') {
        return this.sortFormGroups.sortTwo.value.sortValue;
      } else {
        return this.sortFormGroups.sortOne.value.sortValue
      }
    }
  }

  sortClickHandler(event = null) {
    if ([CONSTANTS.REGION_ID, CONSTANTS.MOB_ID].includes(this.formGroup.value.sortValue)) {
      this.formGroup.controls['sortType'].setValue("ASC");
    } else {
      this.formGroup.controls['sortType'].setValue("DESC");
    }
    this.getSortByValueEvent.emit(event);
  }

  arrowClickHandler() {
    const sortBy = this.sortBy[this.sortType];
    this.formGroup.controls['sortType'].setValue(sortBy);
    this.getSortByValueEvent.emit();


  }
  trackByFn(index, item) {
    return `${item.filed}${this.selectedPC}`
  }


}

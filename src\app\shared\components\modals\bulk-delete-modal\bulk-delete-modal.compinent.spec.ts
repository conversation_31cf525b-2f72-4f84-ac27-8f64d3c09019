import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BulkDeleteModalComponent } from './bulk-delete-modal.compinent';
import { BsModalRef } from 'ngx-bootstrap/modal';

describe('BulkDeleteModalComponent', () => {
  let component: BulkDeleteModalComponent;
  let fixture: ComponentFixture<BulkDeleteModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BulkDeleteModalComponent],
      providers: [BsModalRef]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BulkDeleteModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize class properties correctly', () => {
    expect(component.confirmationMsg).toBeUndefined();
    expect(component.modalRef).toBeUndefined();
  });

  it('should emit isDeleteAttempted event on onDeletAttempt call', () => {
    spyOn(component.isDeleteAttempted, 'emit');
    component.onDeletAttempt();
    expect(component.isDeleteAttempted.emit).toHaveBeenCalledWith(true);
  });

  it('should handle undefined or null inputs correctly', () => {
    component.confirmationMsg = null;
    component.modalRef = null;
    fixture.detectChanges();
    expect(component.confirmationMsg).toBeNull();
    expect(component.modalRef).toBeNull();
  });

  it('should handle errors in onDeletAttempt method gracefully', () => {
    spyOn(component.isDeleteAttempted, 'emit').and.throwError('Error');
    expect(() => component.onDeletAttempt()).toThrowError('Error');
  });
});
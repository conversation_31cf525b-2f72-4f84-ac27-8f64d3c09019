import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';




@Component({
  selector: "bulk-delete-modal",
  templateUrl: './bulk-delete-modal.component.html'
})
export class BulkDeleteModalComponent  {

    @Input() confirmationMsg: string;
    @Input() modalRef: BsModalRef;
    @Output() isDeleteAttempted = new EventEmitter<boolean>();

    onDeletAttempt() {
      this.isDeleteAttempted.emit(true);
    }
}
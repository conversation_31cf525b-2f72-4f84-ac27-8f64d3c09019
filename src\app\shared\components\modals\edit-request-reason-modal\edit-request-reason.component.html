<form [formGroup]="form">
  <div class="modal-header pb-0">
    <h3 class="modal-title pull-left px-0">{{title}}</h3>
    <button type="button" class="close" aria-label="Close" (click)="onCloseClick.emit()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
      <div class="mb-2">
        <label for="editChangeReason" class="d-block font-weight-bold">Change Reason <span class="text-danger">*</span></label>
          <select id="editChangeReason" class="custom-select form-control" formControlName="editChangeReason"
          [ngClass]="{'border-danger': formSubmitted && form.get('editChangeReason').invalid}">
            <option [value]="null" selected disabled hidden></option>
            <option *ngFor="let cr of changeReasonConfig | keyvalue:sortByValue" [value]="cr.key">
              {{ cr.value }}
            </option>
          </select>
          <div class="text-danger" *ngIf="formSubmitted && form.get('editChangeReason').invalid"><small>Change Reason is required</small></div>
      </div>
      <div class="mb-2">
        <label for="editChangeType" class="d-block font-weight-bold">Change Type <span class="text-danger">*</span></label>
          <select id="editChangeType" class="custom-select form-control" formControlName="editChangeType"
          [ngClass]="{'border-danger': formSubmitted && form.get('editChangeType').invalid}">
            <option [value]="null" selected disabled hidden></option>
            <option *ngFor="let ct of changeTypeConfig | keyvalue:sortByValue" [value]="ct.key">
              {{ ct.value }}
            </option>
          </select>
          <div class="text-danger" *ngIf="formSubmitted && form.get('editChangeType').invalid"><small>Change Type is required</small></div>
      </div>
      <div class="mb-2">
        <label for="editChangeReason" class="d-block font-weight-bold">Change Reason Comment</label>
        <textarea rows="4" id="userEditChangeComment" name="userEditChangeComment" class="form-control" formControlName="userEditChangeComment"></textarea>
      </div>
  </div>

  <div class="modal-footer">

    <button type="submit" class="btn btn-primary save-btn" (click)="onSaveClick.emit()">
      {{saveBtnText}}
    </button>
  </div>
</form>
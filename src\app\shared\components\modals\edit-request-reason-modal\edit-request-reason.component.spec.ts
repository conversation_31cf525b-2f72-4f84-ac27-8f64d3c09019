import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule, UntypedFormGroup, UntypedFormControl } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { EditRequestReasonModalComponent } from './edit-request-reason.component';

describe('EditRequestReasonModalComponent', () => {
    let component: EditRequestReasonModalComponent;
    let fixture: ComponentFixture<EditRequestReasonModalComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [EditRequestReasonModalComponent],
            imports: [FormsModule, ReactiveFormsModule],
            providers: [BsModalRef]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(EditRequestReasonModalComponent);
        component = fixture.componentInstance;
        component.form = new UntypedFormGroup({
            reason: new UntypedFormControl(''),
            type: new UntypedFormControl(''),
            editChangeReason: new UntypedFormControl(''),
            editChangeType: new UntypedFormControl(''),
            userEditChangeComment: new UntypedFormControl('')
        });
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should emit onSaveClick event when save button is clicked', () => {
        spyOn(component.onSaveClick, 'emit');
        component.onSaveClick.emit();
        expect(component.onSaveClick.emit).toHaveBeenCalled();
    });

    it('should emit onCloseClick event when close button is clicked', () => {
        spyOn(component.onCloseClick, 'emit');
        component.onCloseClick.emit();
        expect(component.onCloseClick.emit).toHaveBeenCalled();
    });

    it('should sort by value correctly', () => {
        const a = { key: 'a', value: 'value1' };
        const b = { key: 'b', value: 'value2' };
        const result = component.sortByValue(a, b);
        expect(result).toBeLessThan(0);
    });
});
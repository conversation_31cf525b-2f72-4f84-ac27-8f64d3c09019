import { KeyValue } from "@angular/common";
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-edit-request-reason-modal',
  templateUrl: './edit-request-reason.component.html'
})
export class EditRequestReasonModalComponent implements OnInit {
  @Input() title: string;
  @Input() form: UntypedFormGroup;
  @Input() saveBtnText: string;
  @Input() changeReasonConfig = [];
  @Input() changeTypeConfig = [];
  @Input() formSubmitted;

  @Output() onSaveClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() onCloseClick: EventEmitter<any> = new EventEmitter<any>();

  sortByValue = (a: KeyValue<string, string>, b: KeyValue<string, string>): number => {
    return a.value.localeCompare(b.value);
  };
 
  constructor(public modalRef: BsModalRef) {
    // intentionally left empty
  }
 
  ngOnInit() {
    // intentionally left empty
  }

}

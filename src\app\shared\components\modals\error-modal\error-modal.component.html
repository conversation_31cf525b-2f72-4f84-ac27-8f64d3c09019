<div class="modal-header">
  <h5 class="modal-title text-danger pull-left">{{ title }}</h5>
  <button
    type="button"
    class="close pull-right"
    aria-label="Close"
    (click)="bsModalRef.hide()"
  >
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body text-secondary">
  <ul *ngIf="list.length" class="list-group list-group-flush">
    <li *ngFor="let item of list;  index as indx" class="list-group-item"><h6><small class="text-break"><span *ngIf="showIndex">{{indx+1}} : </span>{{ item }}</small></h6></li>
  </ul>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-link p-0" (click)="bsModalRef.hide()">
    {{ closeBtnName }}
  </button>
</div>

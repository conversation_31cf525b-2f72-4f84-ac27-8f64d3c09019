import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ErrorModalComponent } from './error-modal.component';

describe('ErrorModalComponent', () => {
    let component: ErrorModalComponent;
    let fixture: ComponentFixture<ErrorModalComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ErrorModalComponent],
            providers: [BsModalRef]
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ErrorModalComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should have default values for properties', () => {
        expect(component.title).toBeUndefined();
        expect(component.closeBtnName).toBeUndefined();
        expect(component.list).toEqual([]);
        expect(component.showIndex).toBeTrue();
    });

    it('should initialize without errors', () => {
        expect(() => component.ngOnInit()).not.toThrow();
    });
});
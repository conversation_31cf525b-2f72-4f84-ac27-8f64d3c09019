import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-error-modal',
  templateUrl: './error-modal.component.html'
})
export class ErrorModalComponent implements OnInit {
  title: string;
  closeBtnName: string;
  list: any[] = [];
  showIndex: boolean = true;
 
  constructor(public bsModalRef: BsModalRef) {
    // intentionally left empty
  }
 
  ngOnInit() {
    // intentionally left empty
  }

}

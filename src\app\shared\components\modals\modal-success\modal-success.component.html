<div class="modal-header border-bottom-0 pb-0">
  <div class="container-fluid">
    <div class="row">
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span class="font-weight-lighter close-icon" aria-hidden="true">&times;</span>
      </button>
    </div>
  </div>
</div>
<div class="modal-body pt-0 pr-5 pl-5">
  <div class="container-fluid">
    <div class="row">
      <div class="container-fluid">
        <div class="col row justify-content-center">
          <h2 class="success-text">{{ message }}</h2>
        </div>
        <div class="col row justify-content-center mt-4">
          <button *ngIf="showYes" type="button" class="btn btn-primary font-weight-bolder submit-btn mr-5"
            aria-label="Close" (click)="onYesClick()">
            Yes
          </button>

          <button *ngIf="showCancel" type="button" class="btn btn-primary font-weight-bolder submit-btn"
            aria-label="Close" (click)="modalRef.hide()">
            Cancel
          </button>


          <a *ngIf="showMore" type="button" class="text-underline mr-3 mt-2 more" data-toggle="collapse"
            href="#offersList" role="button" aria-expanded="false" aria-controls="offersList">More</a>
          <div class="collapse container-fluid" id="offersList">
            <div class="row mb-2" *ngIf="validOffersList?.length > 0">
              <h3 class="ml-3">Successful Offers ({{validOffersList.length}}/{{totalOffers}})</h3>
            </div>
            <div class="row mb-2" *ngIf="invalidOffersList?.length > 0">
              <h3 class="ml-3">Failed Offers ({{invalidOffersList.length}}/{{totalOffers}})</h3>
              <p class="col-12 mt-1">{{ invalidOffersList.join(', ') }}</p>
            </div>
          </div>

          <button *ngIf="showOK" type="button" class="btn btn-primary font-weight-bolder submit-btn" aria-label="Close"
            (click)="modalRef.hide()">
            OK
          </button>
        </div>
        <div class="d-flex align-items-center justify-content-end" *ngIf="showCorneredLinks">
          <label class="anchor-link-blue cancel-btn cursor-pointer mr-6" (click)="modalRef.hide()"><u>Cancel</u></label>
          <button type="button" type="submit" class="btn btn-primary save-btn" (click)="onYesClick()">
            Yes
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
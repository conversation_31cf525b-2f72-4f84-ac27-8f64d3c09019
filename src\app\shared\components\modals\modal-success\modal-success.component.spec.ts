import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ModalSuccessComponent } from './modal-success.component';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ModalSuccessComponent', () => {
  let component: ModalSuccessComponent;
  let fixture: ComponentFixture<ModalSuccessComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalSuccessComponent ],
      providers: [
        { provide: BsModalRef, useValue: {} }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalSuccessComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should copy preCheckResultObj properties to the component', () => {
      // Arrange: Create a dummy object with test values.
      const testObj = {
        showOK: true,
        showMore: true,
        showCancel: true,
        showYes: true,
        showCorneredLinks: true,
        message: 'Test Message',
        validOffersList: ['offer1', 'offer2'],
        invalidOffersList: ['offer3'],
        totalOffers: 3
      };
      component.preCheckResultObj = testObj;

      // Act: Invoke ngOnInit to copy properties.
      component.ngOnInit();

      // Assert: Verify that all properties are correctly set.
      expect(component.showOK).toBeTrue();
      expect(component.showMore).toBeTrue();
      expect(component.showCancel).toBeTrue();
      expect(component.showYes).toBeTrue();
      expect(component.showCorneredLinks).toBeTrue();
      expect(component.message).toEqual('Test Message');
      expect(component.validOffersList).toEqual(['offer1', 'offer2']);
      expect(component.invalidOffersList).toEqual(['offer3']);
      expect(component.totalOffers).toEqual(3);
    });

    it('should do nothing if preCheckResultObj is not provided', () => {
      // Arrange: Set preCheckResultObj to null.
      component.preCheckResultObj = null;
      const initialShowOK = component.showOK;
      const initialMessage = component.message;

      // Act: Call ngOnInit.
      component.ngOnInit();

      // Assert: Expect component properties to remain unchanged.
      expect(component.showOK).toEqual(initialShowOK);
      expect(component.message).toEqual(initialMessage);
    });
  });

  describe('onYesClick', () => {
    it('should emit true when onYesClick is called', () => {
      // Arrange: Spy on the yesClickHandler emitter.
      spyOn(component.yesClickHandler, 'emit');

      // Act: Call onYesClick.
      component.onYesClick();

      // Assert: Verify that the event is emitted with true.
      expect(component.yesClickHandler.emit).toHaveBeenCalledWith(true);
    });
  });
});

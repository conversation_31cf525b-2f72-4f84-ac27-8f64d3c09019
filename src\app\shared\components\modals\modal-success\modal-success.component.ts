import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-modal-success',
  templateUrl: './modal-success.component.html',
  styleUrls: ['./modal-success.component.scss']
})
export class ModalSuccessComponent implements OnInit {
  @Input() preCheckResultObj: any;
  @Output() yesClickHandler = new EventEmitter<boolean>();
  @Input() modalRef: BsModalRef;

  showOK: boolean = false;
  showMore: boolean = false;
  showCancel: boolean = false;
  showYes: boolean = false;
  showCorneredLinks: boolean = false;
  message: string = '';
  validOffersList = [];
  invalidOffersList = [];
  totalOffers = 0;

  constructor() { 
    // intentionally left empty
  }

  ngOnInit(): void {
    this.preCheckResultObj && Object.keys(this.preCheckResultObj).forEach((key) => {
      this[key] = this.preCheckResultObj[key];
    })
  }
  onYesClick() {
    this.yesClickHandler.emit(true)
  }
}

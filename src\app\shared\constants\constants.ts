export const CONSTANTS = {
  IMAGE_SEARCH_URL: "imageSearchUrl",
  IMAGE_FIND_URL: "imageFindUrl",
  DEFAULT_DEPARTMENT:"General Merchandise",
  IS_OFFER_TEMPLATE:"isOfferTemplate",
  REMOVE_FILE_API: "removeFile<PERSON><PERSON>",
  ATTACH_FILE_API: "attachFile<PERSON><PERSON>",
  PLU:"PLU",
  QUOTIENT:"Quotient",
  NEPTUNE:"Neptune",
  BATCHIMPORT_FILE_LOG_API: "batchImportLogApi",
  BATCHIMPORT_FILE_LOG_BPD_API: "batchImportLogBpdApi",
  BATCHIMPORT_ERROR_LOG_BPD_API: "batchImportErrorBPDApi",
  IMPORT_USER_LOG_API: "userAccessApi",
  IMPORT_USER_ROLES_API: "userRolesApi",
  BATCH_CANCEL: "CANC<PERSON>",
  REMOVE_FOR_ALL: "Remove for All",
  REMOVE_FOR_UNCLIPPED: "Remove for Unclipped",
  BATCHIMPORT_TEMPLATE_FILE_API: "batchImportTemplateApi",
  BATCHIMPORT_TEMPLATE_FILE_API_GR_EDIT: "batchImportTemplateApiEditGR",
  BATCH_EXPAND: "batchExpand",
  SEARCH_USERS_API: "searchUsersApi",
  OFFER_REQ_CREATE_API: "offerReqCreateApi",
  ASSIGN_USER: "assignUser",
  UNASSIGN_USER: "unAssignUser",
  BULK_ASSIGN: "bulkAssignUser",
  BULK_SUBMIT: "bulkSubmit",
  BULK_UNASSIGN: "bulkUnAssignUser",
  BULK_ASSIGN_DATE: "bulkAssignDate",
  BULK_JOBS_UJ:"bulkUJJobs",
  BULK_ASSIGN_DATE_UJ:"bulkAssignDateUJ",
  PRE_PUBLISH_OFFERS: "prePublishOffers",
  BULK_ACTION_BPD_OR: "bulkActionBpdOr",
  PRE_VALIDATE_TEMPLATES: "validateTemplates",
  DEPLOY_DEFER_PUBLISH_OFFERS: "deployDeferPublishOffers",
  PUBLISH_BULK_OFFERS: "publishBulkOffers",
  OFFER_REQ_SUBMIT_API: "requestSubmitApi",
  OFFER_REQ_DELETE_API: "requestDeleteApi",
  OFFER_REQ_DELETE_API_GR: "requestDeleteApiGR",
  OFFER_REQ_EDIT_API: "requestEditApi",
  OFFER_REQ_PROCESS_API: "requestProcessApi",
  OFFER_GR_SPD_REQ_PROCESS_API: "grSPDRequestProcessApi",
  OFFER_REQ_RE_SUBMIT_API: "requestResubmitApi",
  OFFER_REQ_CANCEL_API: "offerReqCancelApi",
  OFFER_REQ_CANCEL_API_GR_SPD_BPD: "offerReqCancelApiGrSpdBpd",
  OFFER_REQ_SEARCH_API: "offerReqSearchApi",
  IVIE_PROMOTION_SEARCH_API: "iviePromotionSearchApi",
  PROGRAM_AND_PROGRAM_SUBTYPE_API: "programAndProgramSubTypeApi",
  POD_PLAYGROUND_API: "podPlaygroundApi",
  EXPORT_POD_PLAYGROUND_API: "exportPodPlaygroundApi",
  POD_PLAYGROUND_AD_BUG_API: "getBugTextPodPlaygroundApi",
  POD_PLAYGROUND_LAST_IMPORT_API: "getLastImportPodPlaygroundApi",
  NEW_POD_PLAYGROUND_API: "newPodPlaygroundApi",
  PRINT_AD_IMPORT_CSV_API: "printAdImportCSVApi",
  PRINT_AD_IMPORT_LOG_API: "printAdImportLogApi",
  PRINT_AD_IMPORT_ERROR_API: "printAdImportErrorApi",
  ACTION_LOG_ERRORS: "actionLogErrorsApi",
  GET_EVENTS_API: "getEventsApi",
  MOB_CREATE_RE_SUBMIT_API: "mobDetailsAPI",
  MOB_EDIT_RE_UPDATE_API: "mobUpdateAPI",
  MOB_SEARCH_API: "mobSearchAPI",
  GENERATE_PROMO_WEEK_DETAILS: "generatePromoWkDetails",
  GET_PERIOD_WEEKS_API:"getPeriodWeeksApi",
  REGION_SALES_UPC_API: "regionSalesUpcAPI",
  BATCH_CREATE_REQUEST_TEMPLATE: "batchCreateRequestTemplate",
  IMPORT_TO_OFFER: "importPromotionToOffer",
  DIVISIONS_FOR_IVIE_PROMOTIONS: "divisionsForIviePromotions",
  LINK_TO_REPORTS: "linkToReports",
  UPP_STORE_GROUP_URL:"uppStoreGroupUrl",
  UPP_PRODUCT_GROUP_URL:"uppProductGroupUrl",
  SG_SEARCH_API: "sgSearchApi",
  PAGE_LIMIT: 100,
  PAGE_SIZE: "pageSize",
  LAST_MODIFY_DATE: "lastUpdateTimestamp",
  LAST_UPDATED_TIMESTAMP:"lastUpdateTimestamp",
  REGION_ID:"regionId",
  UPDATED_DATE: "updatedDate",
  CREATED_BY:"createdBy",
  CREATED_DATE: "createdDate",
  NAME_BY: "nameBy",
  UPDATED_BY:"updatedBy",
  MOB_ID: "mobId",
  WEEK_ID:"weekId",
  OFFER_STATUS: "offerStatus",
  DESC: "DESC",
  ASC: "ASC",
  JOB_ID: "jobId",
  CREATE_USER_ID: "createUserId",
  CREATED_APP_ID: "createdAppId",
  SAVED_SEARCH_OFFER_API: "savedSearchAPIforOffer",
  SAVED_SEARCH_DELETE_OFFER_API: "savedSearchAPIforDeleteOffer",
  SAVED_SEARCH_RETRIEVE: "savedSearchAPIforRetrieve",
  UPDATE_USER_PREF: "updateUserPref",
  GET_USER_PREF: "getUserPref",
  OFFER_DETAILS_LIST_API: {
    old:"offerDetailsListAPI",
    new:"offerDetailsListAPINew"
  },
  OFFER_DETAILS_CODE_LIST_API: "offerDetailsCodeListAPI",
  COUPONSINC:"couponsinc",
  COUPONSINCNAI:"couponsincNAI",
  EED:"EED",
  CORPORATE_STOREGROUP_LIST_UPDATE_API: "corporateStoregroupListUpdateAPI",
  SPD_OFFER_REQUEST_GET_SUBPROGRAM: "subProgramType",
  SPD_OFFER_REQUEST_GETALL_SUBPROGRAM: "programAndProgramSubTypeApi",
  DOWNLOADFINALUPCAPI: "downloadFinalUPCAPI",
  DOWNLOADBPDFINALUPCAPI: "downloadBPDFinalUPCAPI",
  ISAPPLICABLETOJ4U: "isApplicableToJ4U",
  PODREFOFFERID: "podRefOfferId",
  LIMIT: "limit",
  NEXT: "next",
  SID: "sid",
  SORT_BY: "sortBy",
  DIVISION_ID: "divisionId",
  INCLUDE_TOTAL_COUNT: "includeTotalCount",
  INCLUDE_FACET_COUNTS: "includeFacetCounts",
  DYNA_DEPARTMENTS_API: "dynaDepartmentsApi",
  DYNA_STOREGROUP_API: "searchStoreGroupApi",
  SEARCH_TAGS_API: "searchTagsApi",
  FULFILLMENT_CHANNEL_CONST: 'fulfillmentChannels',
  FULFILLMENT_CHANNEL_FEATURE_FLAG: 'enableFulfillmentChannel',
  ALASKA_AIRMILES: "Alaska Airmiles",
  EXPERIENCES: "Experiences",
  ALASKA: "Alaska",
  REVIEW: "REVIEW",
  ACTIVE: "ACTIVE",
  INACTIVE:"INACTIVE",
  REFRESHING:"REFRESHING",
  ACTIVATING:"ACTIVATING",
  NEW: "NEW",
  PARKED: "PARKED",
  NO_UPCS: "NO_UPCS",
  REMOVED: "REMOVED",
  FULLFILLMENTDELIVERY:'Delivery',
  FULLFILLMENTDUG:'DUG',
  FULLFILLMENTINSTORE : 'In Store Pruchase',
  FULLFILLMENTWUG:'WUG',
  BPG_SUGGESTED_LIST_PAGINATION_LIMIT : 30,
  REQUIRED_FIELDS_TO_FETCH_FOR_PG: ["id", "name", "mobId", "productGroupType", "status", "categoryId", "createdUser", "updatedUser",
                                   "categoryName", "productGroupRid", "createTimestamp", "updatedTimestamp"],              
  IS_IMPORT: "isImport",
  BASE_PG: "BASE",
  PROGRAM_CODE_LS: "pcSelected",
  CHANGE_REASON_LS: "changeReasonData",
  SUBMIT_TAGS: "submitTags",
  GLOBAL_REGION_ID: '99',
  OFFER_REQUEST_ID: "Offer Request ID",
  ANY_PRODUCT: "Any Product",
  SPECIAL_OFFERS_CATEGORY_ID: "23",
  IS_IMPORT_BPD: 'is_import',
  START_DATE: "Start Date",
  END_DATE: "End Date",
  REQUEST_ID: "requestId",
  OR_SEARCH_OFFER_ID: "externalOfferId",
  COPIENT_START_DATE: "Copient Start Date",
  COPIENT_END_DATE: "Copient End Date",
  GET_EXCEL: "getExcel",
  EXPORT_EXCEL_SEARCH: "exportExcelSearch",
  ENABLE_ECOM_PROMO_CODE:"enableEcomPromoCode",
  ENABLE_SCHEDULE_AND_SAVE:"enableScheduleAndSave",
  DIVISONAL_GAME_TEXT: "Divisional Games",
  DIVISONAL_GAME_CODE: "DG_ODC",
  BASE_CODE: "GR_ODC",
  SUBPROGRAMCODES_LIST: ["Base", "Divisional Games"],
  EPC:"EPC",
  ESS:"ESS",
  AMOUNT_TYPE_LIST: {
    AMOUNT_OFF: {
      key: "Amount off",
      field: "AMOUNT_OFF",
    },
    AMOUNT_OFF_WEIGHT_VOLUME: {
      key: "Amount off (Wt/ Vol)",
      field: "AMOUNT_OFF_WEIGHT_VOLUME",
    },
    FREE: {
      key: "Free",
      field: "FREE",
    },
    FREE_WEIGHT_VOLUME:{
      key: "Free (Per Lb)",
      field: "FREE_WEIGHT_VOLUME",
    },
    PERCENT_OFF_ITEMS: {
      key: "Percent Off (Items)",
      field: "PERCENT_OFF_ITEMS",
    },
    PERCENT_OFF_WEIGHT_VOLUME: {
      key: "Percent Off (Per Lb)",
      field: "PERCENT_OFF_WEIGHT_VOLUME",
    },
    PRICE_POINT_ITEMS: {
      key: "Pricepoint (Items)",
      field: "PRICE_POINT_ITEMS",
    },
    POINTS: {
      key: "Points",
      field: "POINTS",
    },
    PRICE_POINT_WEIGHT_VOLUME: {
      key: "Pricepoint (Wt/ Vol)",
      field: "PRICE_POINT_WEIGHT_VOLUME",
    },
    REWARDS_POINTS: {
      key: "Rewards",
      field: "REWARDS",
    },
  },
  BEHAVIORAL_ACTION_OFFER_TYPES: {
    ITEM_DISCOUNT: "Item Discount",
    DEPARTMENT: "Department",
    REWARDS_FLAT: 'Rewards - Flat',
    WOD_OR_POD: "WOD / POD"
  },
  GR_AUTO_REWARD_OFFER_TYPES:{
    CUSTOM:"Custom"
  },
  GR_BASE_POINTS_FOR_SERVICES_OFFER_TYPES:{
    ITEM_DISCOUNT: "Item Discount",
  },

  AUTO_REWARD:"Auto Reward",

  POINTS_FOR_SERVICES: "Points for Services",

  GR_SUB_PROGRAM_CODE_BASE : "Base" ,

  BEHAVIORAL_PRODUCT_RULES: {
    name: {
      show: false,
      value: null,
      isNonEditableInSubRows: true
    },
    quantityUnitType: {
      show: false,
      value: 'ITEMS',
      isNonEditableInSubRows: true,
    },
    amount: {
      show: false,
      value: null,
      tiers: true,
      min: 0.009,
    },
    minPurchase: {
      show: false,
      value: null
    }
  },
  BEHAVIORAL_ACTION_CODE: 'BA',
  BEHAVIORAL_CONTINUTY_CODE: 'BAC',
  DIGITAL_ONLY :'DO',
  DIGITAL_AUTO_CLIP:'DAC',
  
  INLANE : "IR",
  CONTINUTY_UOM_CONFIG: {
    Items: 'ITEMS',
    Dollars: 'DOLLARS'
  },
  FACET_SEARCH: {
    requestId: {
      key: "Offer Request ID",
      field: "requestId",
    },
    offerRequestId: {
      key: "Request ID",
      field: "offerRequestId",
    },
    templateId: {
      key: "Template ID",
      field: "templateId",
    },
    imageId: {
      key: "Image ID",
      field: "imageId",
    },
    mobId: {
      key: "MOB ID",
      field: "mobId",
    },
    periodWeek: {
      key: "Period",
      field: "periodWeek",
    },
    weekId: {
      key: "Week ID",
      field: "weekId",
    },
    lastPeriodCreated:{
      key: "Last Period Created",
      field: "lastPeriodCreated",
    },
    period: {
      key: "Period",
      field: "period",
    },
    brandAndSize: {
      key: "Brand And Size",
      field: "brandAndSize",
    },
    pointsRequired: {
      key: "Point Value",
      field: "pointsRequired",
    },
    upc: {
      key: "UPC",
      field: "upc",
    },
    pluTriggerBarcode: {
      key: "Promotion PLU",
      field: "pluTriggerBarcode",
    },
    customerGroups: {
      key: "Customer Group",
      field: "customerGroups",
    },
    ProductGroup: {
      key: "Product Group",
      field: "productGroups",
    },
    qualificationBenefitCombinedProductsGroup: {
      key: "Product Group",
      field: "qualificationBenefitCombinedProductsGroup",
    },
    storeGroups: {
      key: "Store Group",
      field: "combinedStoreGroups",
    },
    nopaNumbers: {
      key: "NOPA Number",
      field: "nopaNumbers",
    },
    productImageId: {
      key: "Product Image ID",
      field: "productImageId",
    },
    externalOfferId: {
      key: "External OfferId",
      field: "externalOfferId",
    },
    offerId: {
      key: "Offer ID",
      field: "offerId",
    },
    qualificationPointsGroup: {
      key: "Points Group",
      field: "qualificationPointsGroup",
    },
    offerName: {
      key: "Offer Name",
      field: "offerName",
    },
    description: {
      key: "Description",
      field: "description",
    },
    qualificationCustomerGroup: {
      key: "Customer Group",
      field: "qualificationCustomerGroup",
    },
    qualificationProductsGroup: {
      key: "Products Group",
      field: "qualificationProductsGroup",
    },
    combinedStoreGroups: {
      key: "Store Group",
      field: "combinedStoreGroups",
    },
    startDt: {
      key: "Offer Start Date",
      field: "startDt",
    },
    headLine: {
      key: "Headline 1",
      field: "headLine",
    },
    headLine2: {
      key: "Headline 2",
      field: "headLine2",
    },
    prodDesc: {
      key: "Offer Description",
      field: "productDesc",
    },
    vehicleName: {
      key: "VehicleName",
      field: "vehicleName",
    },
    endDt: {
      key: "Offer End Date",
      field: "endDt",
    },
    effectiveStartDate: {
      key: "Offer Start Date",
      field: "effectiveStartDate",
    },
    effectiveEndDate: {
      key: "End Date",
      field: "effectiveEndDate",
    },
    createUserId: {
      key: "User Id",
      field: "createUserId",
    },
    requestedUserId: {
      key: "User Id",
      field: "requestedUserId",
    },
    hhid: {
      key: "HHID",
      field: "household",
    },
    assignedTo: {
      key: "Assigned to",
      field: "assignedTo",
    },
    userId: {
      key: "Assigned to",
      field: "userId",
    },
    redemptionStoreId: {
      key: "Store ID",
      field: "redemptionStoreId",
    },
    lastUpdateTimestamp: {
      key: "Last Updated",
      field: "lastUpdateTimestamp",
    },
    createTimeStamp: {
      key: "Created Date",
      field: "createTimeStamp",
    },
    combinedIviePromotionUniversalSearch: {
      key: "Universal Search",
      field: "combinedIviePromotionUniversalSearch",
    },
    promotionStartDt: {
      key: "Start Date",
      field: "promotionStartDt",
    },
    adPageNbr: {
      key: "Page",
      field: "adPageNbr",
    },
    adBugTxt: {
      key: "Ad Bug",
      field: "adBugTxt",
    },
    dept: {
      key: "Department",
      field: "dept",
    },
    desc: {
      key: "Description",
      field: "desc",
    },
    code: {
      key: "PLU",
      field: "code",
    },
    div: {
      key: "Division",
      field: "div",
    },
    startDate: {
      key: "Start Date",
      field: "startDate",
    },
    aggId: {
      key: "AGG ID",
      field: "aggregatorOfferId",
    },
    priceText: {
      key: "Price Text",
      field: "saveValueTxt",
    },
    vendors: {
      key: "Vendors",
      field: "vendors",
      },
    lastUpdatedBy: {
     key:"Last Updated By",
     field:"updatedUserId"
      },
      ecommPromoCode:{
        key:"Ecomm Promo Code",
        field:"ecommPromoCode"
      }
  },
  SUGESSTED_FILTER: {
    upc: {
      key: "UPC",
      field: "upc",
    },
    description: {
      key: "Description",
      field: "description",
    },
    score: {
      key: "Score",
      field: "score",
    },
    status: {
      key: "Status",
      field: "status",
    },
  },
  FACET_FILTER: {
    eCommPromoType:{
      key: "eCommPromoType",
      displayValue: "eComm Promo Type",
      field: "eCommPromoType",
    },
    dept: {
      key: "Department",
      displayValue: "Department",
      field: "dept",
    },
    desc: {
      key: "Description",
      displayValue: "Description",
      field: "desc",
    },
    code: {
      key: "PLU",
      displayValue: "PLU",
      field: "code",
    },
    div: {
      key: "Division",
      displayValue: "Division",
      field: "div",
    },
    startDate: {
      key: "Start Date",
      displayValue: "Start Date",
      field: "startDate",
    },
    programCode: {
      key: "Program Code",
      field: "programCode",
      displayValue: "Program Code",
    },
    categories: {
      key: "Category",
      field: "categories",
      displayValue: "Category",
    },
    eventids: {
      key: "Events",
      field: "eventids",
      displayValue: "Events",
    },
    group: {
      key: "groups",
      field: "group",
      displayValue: "groups",
    },
    regionId: {
      key: "Regions",
      field: "regionId",
      displayValue: "Regions",
    },
    programType: {
      key: "Program Type",
      field: "programType",
      displayValue: "program Type",
    },
    progSubType: {
      key: "Program SubType",
      field: "progSubType",
      displayValue: "Program Subtype",
    },
    actionFailedState: {
      key: "Failures",
      field: "actionFailedState",
      displayValue: "Failures",
    },

    offerFailedState: {
      key: "Failures",
      field: "offerFailedState",
      displayValue: "Failures",
    },

    offerRequestorGroup: {
      key: "groups",
      field: "offerRequestorGroup",
      displayValue: "groups",
    },
    divisions: {
      key: "Divisions",
      field: "divisions",
      displayValue: "Division",
    },
    status: {
      key: "Status",
      field: "status",
      displayValue: "Status",
    },
    podStatus: {
      key: "POD Approved",
      field: "podStatus",
      displayValue: "POD Approved",
    },
    divisionRogCds: {
      key: "Division (J4U)",
      field: "divisionRogCds",
      displayValue: "Division (J4U)",
    },
    hiddenEvents: {
      key: "Hidden Events",
      field: "hiddenEvents",
      displayValue: "Hidden Events",
    },
    customerSegment: {
      key: "Customer Segment",
      field: "customerSegment",
      displayValue: "Customer Segment",
    },
    discountType: {
      key: "Discount",
      field: "discountType",
      displayValue: "Discount",
    },
    deliveryChannel: {
      key: "Channel",
      field: "deliveryChannel",
      displayValue: "Channel",
    },
    nopaAssignStatus: {
      key: "NOPA Assign Status",
      field: "nopaAssignStatus",
      displayValue: "NOPA Assign Status",
    },
    tags: {
      key: "Tags",
      field: "tags",
      displayValue: "Tags",
    },
    banners: {
      key: "Banners",
      field: "banners",
      displayValue: "Banners",
    },
    Pharmacy: {
      key: "Pharmacy",
      field: "Pharmacy",
      displayValue: "Pharmacy",
    },
    DUG: {
      key: "DUG",
      field: "DUG",
      displayValue: "Drive up and Go",
    },
    Delivery: {
      key: "Delivery",
      field: "Delivery",
      displayValue: "Delivery",
    },
    Fuel: {
      key: "Fuel",
      field: "Fuel",
      displayValue: "Fuel",
    },
    "Jamba Juice": {
      key: "Jamba Juice",
      field: "Jamba Juice",
      displayValue: "Jamba Juice",
    },
    Starbucks: {
      key: "Starbucks",
      field: "Starbucks",
      displayValue: "Starbucks",
    },
    offerProgramCd: {
      key: "Program Code",
      field: "offerProgramCd",
      displayValue: "Program Code",
    },
    offerStatus: {
      key: "Status",
      field: "offerStatus",
      displayValue: "Status",
    },
    otStatus: {
      key: "Status",
      field: "otStatus",
      displayValue: "Status",
    },
    offerRequestStatus: {
      key: "Offer Request Status",
      field: "offerRequestStatus",
      displayValue: "Offer Request Status",
    },
    digital: {
      key: "Digital/Non-Digital",
      field: "digital",
      displayValue: "Digital/Non-Digital",
    },
    offerType: {
      key: "Offer Type",
      field: "offerType",
      displayValue: "Offer Type",
    },
    offerProtoType: {
      key: "Offer Type",
      field: "offerProtoType",
      displayValue: "Offer Type",
    },
    couponTypeNm: {
      key: "Coupon Type",
      field: "couponTypeNm",
      displayValue: "Coupon Type",
    },
    color: {
      key: "Color",
      field: "color",
      displayValue: "Color",
    },
    vehicleTypNm: {
      key: "Vehicle Type",
      field: "vehicleTypNm",
      displayValue: "Vehicle Type",
    },
    isProofed: {
      key: "Proofed",
      field: "isProofed",
      displayValue: "Proofed",
    },
    offerLinked: {
      key: "Offer Linked",
      field: "offerLinked",
      displayValue: "Offer Linked",
    },
    isImageFound: {
      key: "Image Found",
      field: "isImageFound",
      displayValue: "Image Found",
    },
    regions: {
      key: "Regions",
      field: "regions",
      displayValue: "Regions",
    },
    targeted: {
      key: "Targeted",
      field: "targeted",
      displayValue: "Targeted",
    },
    subProgramCode: {
      key: "SubProgramCode",
      field: "subProgramCode",
      displayValue: "SubProgramCode",
    },
    isInAd: {
      key: "In Ad Offers",
      field: "isInAd",
      displayValue: "In Ad Offers",
    },
    inEmail: {
      key: "In Email Offers",
      field: "inEmail",
      displayValue: "In Email Offers",
    },
    aggId: {
      key: "AGG ID",
      field: "aggregatorOfferId",
    },
    priceText: {
      key: "Price Text",
      field: "saveValueTxt",
    },
    bggm: {
      key: "BGGM",
      field: "bggm",
      displayValue: "BGGM",
    },
    bugm: {
      key: "BUGM",
      field: "bugm",
      displayValue: "BUGM",
    },
    categoryId: {
      key: "Categories",
      field: "categoryId",
      displayValue: "Categories",
    },
    createdAppId: {
      key: "Source",
      field: "createdAppId",
      displayValue:"Source"
    },
  },
  QUERY_WITH_FILTER_KEYS: {
    facetSearch: ["combinedDigitalUser", "combinedNonDigitalUser"],
    facetFilter: ["deliveryChannel", "adType", "digitalUiStatus", "nonDigitalUiStatus"],
  },
  GRID_VIEW_SORT_LIST: [
    {
      key: "POD Headline",
      field: "headLine",
    },
    {
      key: "Page / Mod",
      field: "pageMod",
      },
      {
       key: "Last modified",
       field: "lastUpdateTimestamp",
      },
  ],
  REQ_SEARCH_SORT_LIST: [
    {
      key: "Create Timestamp",
      field: "createTimestamp",
    },
    {
      key: "Last modified",
      field: "lastUpdateTimestamp",
    },
    {
      key: "Offer Start Date",
      field: "effectiveStartDate",
    },
    {
      key: "Offer End Date",
      field: "effectiveEndDate",
    },
  ],
  REQ_SEARCH_SORT_LIST_GR: [
    {
      key: "Create Timestamp",
      field: "createTimestamp",
    },
    {
      key: "Last modified",
      field: "lastUpdateTimestamp",
    },
    {
      key: "MOB ID",
      field: "mobId",
    },
    {
      key: "Offer Start Date",
      field: "effectiveStartDate",
    },
    {
      key: "Offer End Date",
      field: "effectiveEndDate",
    },
    {
      key: "Region ID",
      field: "regionId",
    }
  ],
  REQ_SEARCH_SORT_LIST_SPD: [
    {
      key: "Create Timestamp",
      field: "createTimestamp",
    },
    {
      key: "Last modified",
      field: "lastUpdateTimestamp",
    },
    {
      key: "MOB ID",
      field: "mobId",
    },
    {
      key: "Offer Start Date",
      field: "effectiveStartDate",
    },
    {
      key: "Offer End Date",
      field: "effectiveEndDate",
    },
    {
      key: "Region ID",
      field: "regionId",
    }
  ],
  REQ_SEARCH_SORT_LIST_BPD: [
    {
      key: "Create Timestamp",
      field: "createTimestamp",
    },
    {
      key: "Last modified",
      field: "lastUpdateTimestamp",
    },
    {
      key: "MOB ID",
      field: "mobId",
    },
    {
      key: "Offer Start Date",
      field: "effectiveStartDate",
    },
    {
      key: "Offer End Date",
      field: "effectiveEndDate",
    },
    {
      key: "Region ID",
      field: "regionId",
    }
  ],
  SEARCH_SORT_LIST: [
    {
      key: "Create Timestamp",
      field: "createTimestamp",
    },
    {
      key: "Last modified",
      field: "lastUpdateTimestamp",
    },
    {
      key: "Display Start Date",
      field: "displayEffectiveStartDate",
    },
    {
      key: "Display End Date",
      field: "displayEffectiveEndDate",
    },
    {
      key: "Offer Start Date",
      field: "effectiveStartDate",
    },
    {
      key: "Offer End Date",
      field: "effectiveEndDate",
    },
  ],
  STORE_SORT_LIST: [
    {
      key: "Created By",
      field: "createUserId",
    },
    {
      key: "Last modified",
      field: "lastUpdateTimestamp",
    },
  ],
    PRODUCT_SORT_LIST: [
        {
            key: "Created By",
            field: "createdBy",
        },
        {
            key: "Create Timestamp",
            field: "createdDate",
        },
        {
            key: "Group Name",
            field: "nameBy",
        },
        {
            key: "MOB ID",
            field: "mobIdBy",
        },
        {
            key: "Modified By",
            field: "updatedBy",
        },
        {
            key: "Modified Date",
            field: "updatedDate",
        }
    ],
  HTTP_HEADERS: {
    "X-Albertsons-Client-ID": "OMS",
     "content-type": "application/vnd.safeway.v1+json",
  },
  HTTP_HEADERS_PREVIEW: {
    "X-Albertsons-Client-ID": "OMS | PREVIEW",
    "content-type": "application/vnd.safeway.v1+json",
  },
  HTTP_HEADERS_CSV: {
    "X-Albertsons-Client-ID": "OMS",
    "content-type": "application/octet-stream",
  },
  HTTP_HEADERS_MULTIPART: {
    "X-Albertsons-Client-ID": "OMS",
    },
  SEARCH_PRODUCT_GROUP: "searchProductGroups",
  SEARCH_PRODUCT_GROUP_OLD: "searchProductGroups_old",
  PRODUCT_GROUP_EXPAND_UPCS: "productGroupExpandUpcs",
  CFCATEGORY_INFO: "cfcategoryinfo",
  REMOVE_DEP_ERROR: "Departments marked for removal are not not available within the product group",
  REMOVE_MANU_ERROR: "Manufacturer Family Codes marked for removal are not not available within the product group",
  REMOVE_UPC_IDS_ERROR: "UPC List marked for removal are not not available within the product group",
  OFFER_DEPLOY_API: "offerDeploy",
  OFFER_PUBLISH_API: "offerPublish",
  UPLOAD_IMAGE_API: "uploadImageApi",
  GET_IMAGE_API: "getImageApi",
  SEARCH_IMAGES_API: "searchImageApi",
  BULK_UPDATE_POD_API: "bulkUpdatePod",
  BULK_UPDATE_EVENTS_API: "bulkUpdateEvents",
  ALLOCATION_API: "allocationApi",
  BENEFIT_GRANT_MEMBERSHIP: "Grant Membership",
  GET_USER_PERMISSIONS: "getUserPermissionsApi",
  GET_FEATURE_FLAGS_UI: "getFeatureFlagsApi",
  GR: 'GR',
  SPD: "SPD",
  BPD: "BPD",
  MF: "MF",
  SC: "SC",
  CCP: "CCP",
  OR: "OR",
  OT: "OT",
  PA: "PA",
  RE: "RE",
  NA: "NA",
  O: "O",
  SC_BPD:'SC/BPD',
  UNIVERSAL:"Universal",
  STORE_COUPON: "Store Coupon",
  SPD_FULL_TEXT: "Specialty PD",
  BPD_FULL_TEXT: "Base PD",
  Permissions: {
    AssignDigitalUsers: 'ASSIGN_DIGITAL_USERS',
    AssignNonDigitalUsers: 'ASSIGN_NON_DIGITAL_USERS',
    CancelOfferRequest: 'CANCEL_OFFER_REQUEST',
    DefaultSearchesViewAssignmentSearches: 'DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES',
    DefaultSearchesViewOffersFromYourRequests: 'DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS',
    DefaultSearchesViewYourAssignedOffers: 'DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS',
    DefaultSearchesViewYourRequests: 'DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS',
    DoAnyDigitalOffers: 'DO_ANY_DIGITAL_OFFERS',
    DoAnyNonDigitalOffers: 'DO_ANY_NON_DIGITAL_OFFERS',
    DoAssignedDigitalOffers: 'DO_ASSIGNED_DIGITAL_OFFERS',
    DoAssignedNonDigitalOffers: 'DO_ASSIGNED_NON_DIGITAL_OFFERS',
    DoBatchAssign: 'DO_BATCH_ASSIGN',
    DoBatchDeploy: 'DO_BATCH_DEPLOY',
    DoBatchCancel: 'DO_BATCH_CANCEL',
    DoBatchDeferDeploy: 'DO_BATCH_DEFER_DEPLOY',
    DoBatchSubmit: 'DO_BATCH_SUBMIT',
    DoBatchCopy: 'DO_BATCH_COPY',
    DoSCBatchCopy: 'DO_SC_BATCH_COPY',
    DoBatchExport: 'DO_BATCH_EXPORT',
    DoBatchProcess: 'DO_BATCH_PROCESS',
    DoBatchUpdateOfferDates: 'DO_BATCH_UPDATE_OFFER_DATES',
    DoBatchPublishOffers: 'DO_BATCH_PUBLISH_OFFERS',
    DoBatchAddEvents: 'DO_BATCH_EVENTS',
    DoBatchDeployPublishOffers: 'DO_BATCH_DEPLOY_PUBLISH',
    DoBatchDeferDeployPublishOffers: 'DO_BATCH_DEFER_DEPLOY_PUBLISH',
    DoBatchUpdateForTesting: 'DO_BATCH_UPDATE_FOR_TESTING',
    DoBatchUpdatePod: 'DO_BATCH_UPDATE_POD',
    EditGRSPDRequest: 'EDIT_GR_SPD_REQUESTS',
    ViewGRSPDRequest: 'VIEW_GR_SPD_OFFER_REQUESTS',
    ViewBPDOfferRequest:"VIEW_BPD_OFFER_REQUESTS",
    DoPodOffers: 'DO_POD_OFFERS',
    DoMobUpdate: 'DO_MOB_UPDATE',
    DoStoreCouponsRequests: 'DO_STORE_COUPON_REQUESTS',
    ENABLED_MANAGE_OFFER_FLAG: 'MANAGE_OFFER_FLAG',
    MANAGE_OFFER_PRIORITY: 'MANAGE_OFFER_PRIORITY',
    ExitEditOfferRequests: 'EXIT_EDIT_OFFER_REQUESTS',
    ManageCustomerGroups: 'MANAGE_CUSTOMER_GROUPS',
    ManagePointGroups: 'MANAGE_POINT_GROUPS',
    ManageProductGroups: 'MANAGE_PRODUCT_GROUPS',
    ManageStoreGroups: 'MANAGE_STORE_GROUPS',
    ProcessAnyDigitalOfferRequests: 'PROCESS_ANY_DIGITAL_OFFER_REQUESTS',
    ProcessAnyNonDigitalOfferRequests: 'PROCESS_ANY_NON_DIGITAL_OFFER_REQUESTS',
    ProcessAssignedDigitalOfferRequests: 'PROCESS_ASSIGNED_DIGITAL_OFFER_REQUESTS',
    ProcessAssignedNonDigitalOfferRequests: 'PROCESS_ASSIGNED_NON_DIGITAL_OFFER_REQUESTS',
    ViewCommentGroups: 'VIEW_COMMENT_GROUPS',
    ViewComments: 'VIEW_COMMENTS',
    ViewCustomerGroups: 'VIEW_CUSTOMER_GROUPS',
    ViewOfferRequests: 'VIEW_OFFER_REQUESTS',
    ViewOffers: 'VIEW_OFFERS',
    ViewPointGroups: 'VIEW_POINT_GROUPS',
    ViewProductGroups: 'VIEW_PRODUCT_GROUPS',
    ViewStoreGroups: 'VIEW_STORE_GROUPS',
    ViewOfferRequestsHistory: 'VIEW_OFFER_REQUESTS_HISTORY',
    ManagePodPlayGround: 'MANAGE_POD_PLAYGROUND',
    ViewPodPlayGround: 'VIEW_POD_PLAYGROUND',
    ViewPodImport: 'VIEW_POD_IMPORT',
    ManagePluReservation: 'MANAGE_PLU_RESERVATION',
    ViewPluReservation: 'VIEW_PLU_RESERVATION',
    ViewAdmin: 'VIEW_ADMIN',
    ViewEventMaint: 'VIEW_EVENT_MAINT',
    ManageEventMaint: 'MANAGE_EVENT_MAINT',
    Admin: 'ADMIN',
    DoBatchTerminalUpdate: 'DO_BATCH_UPDATE_TERMINAL',
    ViewBatchActionLog: 'VIEW_BATCH_ACTION_LOG',
    ViewBatchImportAction: 'VIEW_BATCH_IMPORT_ACTION',
    ManageBatchImportAction: 'MANAGE_BATCH_IMPORT_ACTION',
    ManageBatchImportOffers: 'MANAGE_BATCH_IMPORT_OFFERS',
    ManageBatchEditOffers: 'MANAGE_BATCH_EDIT_OFFERS',
    DoStoreCouponOffers: 'DO_STORE_COUPON_OFFERS',
    DoStoreCouponOffersDeploy: 'DO_STORE_COUPON_OFFERS_DEPLOY',
    ViewMFOffers: 'VIEW_MF_OFFERS',
    DoMFOffers: 'DO_MF_OFFERS',
    DoBatchInAd: 'DO_BATCH_INAD',
    DoBatchInEmail: 'DO_BATCH_INEMAIL',
    Requester: 'REQUESTER',
    Approver: 'APPROVER'
  },
  GET_OR_HISTORY: "getORHistoryApi",
  GET_CONFIG_HISTORY: "getGroupsHistoryApi",
  GET_OFFER_HISTORY: "offersHistoryApi",
  GET_OT_HISTORY: "getOTHistoryApi",
  GET_PG_HISTORY: "pgHistoryApi",
  GET_CG_HISTORY: "cgHistoryNewApi",
  GET_SG_HISTORY:"sgHistoryApi",
  UOM_CONFIG: { ITEMS: "Items", DOLLARS: "Dollars", WEIGHT_VOLUME: "Per Pound" },
  REBATE: "Rebate",
  TEMPLATE: "template",
  REQUEST: "request",
  ACTION_LOG: "action-log",
  IMPORT_LOG_BPD: "import-log-bpd",
  USER_DETAILS: "user-details",
  ACTION: "action",
  IMPORT_LOG_ERRORS: "importLogErrors",
  IMPORT_LOG_ERRORS_BPD: "importLogErrorsBPD",
  PRODUCTMANAGEMENT: "product-management",
  STOREMANAGEMENT: "store-management",
  CUSTOMERMANAGEMENT: "customer-management",
  POINTSMANAGEMENT: "points-management",
  PROMOWEEKDETAILS: "promo-week-details",
  OFFER: "offers",
  OMS: "OMS",
  UPP: "UPP",
  UPDATED_TIMESTAMP: "updatedDate",
  COMBINED_SEARCH: {
    deliveryChannel: ["deliveryChannel", "adType"],
    status: ["digitalUiStatus", "nonDigitalUiStatus"],
    assignedTo: ["combinedDigitalUser", "combinedNonDigitalUser"],
  },
  EXPIRED_STATUS_OR :  "EX", //Id used in config data 
  EXPIRED_STATUS_OR_DISPLAY :  "Expired",
  COMPLETED_STATUS_OR_DISPLAY :  "Completed",   
  COMPLETED_STATUS_OR :  "D", //Id used in config data 
  CANCELLED: "C",
  COPY_PASTE_IDS_EXCEL_FOR_SEARCH: ["requestId", "mobId", "templateId", "externalOfferId", "offerRequestId"],
 //  Todo Expired: Show this and remove  next 2 flags
 END_DATE_QUERY_OR: "effectiveEndDate", // If expired status is selected, this needs to be passed
  CURRENT_DATE_QUERY_OR: "currentDate", // If expired status needs to be excluded, this needs to be passed  

  // END_DATE_QUERY_OR: "", // If expired status is selected, this needs to be passed
  // CURRENT_DATE_QUERY_OR: "", // If expired status needs to be excluded, this needs to be passed 
  
  IS_DISPLAY_EXPIRED_OR_RESULTS: "showExpired", // If expired status needs to be excluded, this needs to be passed as false
  IS_EXPIRED_STATUS_ENABLED: "IS_EXPIRED_STATUS_ENABLED", /*Feature flag for  OMSMIG-14445
  Offer Request Mgmt Page - Add Expired*/
  //IS_FILTER_PERSISTENCE_ENABLED_OR: "isFilterPersistenceEnabled_OR",
  IS_FILTER_PERSISTENCE_ENABLED_O: "isFilterPersistenceEnabled_O",
  //IS_FILTER_PERSISTENCE_ENABLED_OT: "isFilterPersistenceEnabled_OT"
  /*Feature flag for OT filter persistence
   when moving to & fro from the management page*/
   FIX_IT_API_IMPORT_LOG_BPD: "importLogFixItApi",

   ECOM_PRODUCT_GROUP_FEATURE_URL: "emom-offerproducts",
   OCOM_PRODUCT_GROUP_FEATURE_URL: "ocom-product-groups"
};

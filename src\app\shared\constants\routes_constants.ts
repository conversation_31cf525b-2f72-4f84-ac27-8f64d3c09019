// URL Formatting : Use lower case always with hypen for word seperators.
// Example: use customer-group instead of customerGroup or CustomerGroup or Customer_Group
export const ROUTES_CONST = {
  REQUEST: {
    Request: "request",
    Create: "create",
    Edit: "edit",
    Summary: "summary",
    Management: "management",
    RequestForm: "request-details",
    PluManagement: "pluManagement",
    PluDetails: "pluDetails",
    GRCreate: "gr-create",
    GREdit: "gr-edit",
    GRSummary: "gr-summary",
    SPDCreate: "spd-create",
    SPDEdit: "spd-edit",
    SPDSummary: "spd-summary",
    BPDCreate: "bpd-create",
    BPDEdit: "bpd-edit",
    BPDSummary: "bpd-summary"
  },
  TEMPLATES: {
    Template: "template",
    TemplateForm: "template-details",
    BPDCreate: "bpd_create",
    BPDEdit: "bpd_edit",
    BPDSummary: "bpd_summary",
    SCCreate: "sc-create",
    SCEdit: "sc-edit",
    SCSummary: "sc-summary",
    SPDCreate: "spd-create",
    SPDEdit: "spd-edit",
    SPDSummary: "spd-summary",
    GRCreate: "gr-create",
    GREdit: "gr-edit",
    GRSummary: "gr-summary",
  },
  OFFERS: {
    Offers: "offers",
    Management: "management",
    Create: "create",
    OfferDefinition: "definition",
    Conditions: "conditions",
    Benefits: "benefits",
    Locations: "locations",
    PodDetails: "pod",
    PodPlayground: "pod-playground",
    PodImport: "pod-import",
    PodImportData: "data",
    PodImportLog: "log",
    Summary: "summary",
    Edit: "edit",
  },
  COMMENTS: {
    Comments: "comments",
    ViewAll: "all",
    UserGroups: "groups",
    Create: "create",
    Edit: "edit",
  },
  GROUPS: {
    Group:"groups",
    CustomerGroup: "customer-management",
    StoreGroup: "store-management",
    PointsGroup: "points-management",
    ProductGroup: "product-management",
    Edit: "edit",
    Create: "create",
    CustomerDetails: "customer-details",
    StoreDetails: "store-details",
    PointsDetails: "points-details",
    ProductDetails: "product-details",
    BaseCreate: "base-create",
    BaseEdit: "base-edit",
  },
  ADMIN: {
    Admin: "admin",
    EventMaintenance: "event-maintenance",
    OfferDetails: "offer-details",
    StoreGroup: "store-group",
    BatchImport: "batch-import",
    ImportFile: "import-file",
    ImportLog: "import-log",
    ImportLogBPD: "import-log-bpd",
    ActionLog: "action-log",
    ScoreCardTemplate: "scorecard-template",
    UsersDetails: "user-details",
    ImagePreview: "image-preview",
    ImportLogBulkPoints:"import-log-bulk-points"
  },
  NOTAUTHORIZED: {
    NotAuthorized: "not-authorized",
  },
  NOTFOUND: {
    NotFound: "not-found",
  },
  TECH_SUPPORT: {
    techsupport: "tech-support",
  },
};

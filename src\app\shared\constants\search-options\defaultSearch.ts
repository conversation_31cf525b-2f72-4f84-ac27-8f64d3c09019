import { CONSTANTS } from "@appConstants/constants";
import { requestHomeDefaultOptions } from "@appModules/request/core/offer-request/management/components/home/<USER>";
import { actionLogImport, actionLogImportBpdFlag, actionLogSortByObj } from "../../../modules/admin/constants/actionLogSearch";
import { importLogBpd, importLogSortByObj } from "../../../modules/admin/constants/importLogSearch";
import { prodMgmtSortedByObj, prodMgmtSortedByWithMobAscObj } from "../../../modules/groups/constants/search-options/productGroup";
import { storeMgmtSortedByObj } from "../../../modules/groups/constants/search-options/storeGroup";

const DEFAULT_OPTION = [
    {
        label:CONSTANTS.LIMIT,
        field:CONSTANTS.LIMIT,
        showChip:[],
        query:[CONSTANTS.PAGE_LIMIT],
        elements:[{
            type:"",
            field:CONSTANTS.LIMIT,
            query:[CONSTANTS.PAGE_LIMIT]
        }]
    },   
  
    {
        label:CONSTANTS.SID,
        field:CONSTANTS.SID,
        query:[],
        showChip:[],
        elements:[{
            type:"",
            field:CONSTANTS.SID,
            query:[]
        }]
    },
    {
        label:CONSTANTS.NEXT,
        field:CONSTANTS.NEXT,
        query:[1],
        showChip:[],
        elements:[{
            type:"",
            field:CONSTANTS.NEXT,
            query:[1]
        }]
    }
]
const appIdObj = {
    label:CONSTANTS.CREATED_APP_ID,
    field:CONSTANTS.CREATED_APP_ID,
    showChip:[],
    query:[CONSTANTS.OMS],
    elements:[{
        type:"",
        field:CONSTANTS.CREATED_APP_ID,
        query:[CONSTANTS.OMS]
    }]
}

const sortedByObj =   {
    label:CONSTANTS.SORT_BY,
    field:CONSTANTS.SORT_BY,
    query:[`${CONSTANTS.LAST_UPDATED_TIMESTAMP}${CONSTANTS.DESC}`],
    showChip:[],
    elements:[{
        type:"",
        field:CONSTANTS.SORT_BY,
        query:[`${CONSTANTS.LAST_UPDATED_TIMESTAMP}${CONSTANTS.DESC}`]
    }]
}
const sortedByObjwitMobIdAsc = {
    label:CONSTANTS.SORT_BY,
    field:CONSTANTS.SORT_BY,
    query:[`${CONSTANTS.LAST_UPDATED_TIMESTAMP}${CONSTANTS.DESC},${CONSTANTS.MOB_ID}${CONSTANTS.ASC}`],
    showChip:[],
    elements:[{
        type:"",
        field:CONSTANTS.SORT_BY,
        query:[`${CONSTANTS.LAST_UPDATED_TIMESTAMP}${CONSTANTS.DESC},${CONSTANTS.MOB_ID}${CONSTANTS.ASC}`]
    }]
}
export const DEFAULT_OPTIONS_LIST = {
    [CONSTANTS.TEMPLATE]:{
        [CONSTANTS.BPD]:DEFAULT_OPTION
    },
    [CONSTANTS.REQUEST]:{
        [CONSTANTS.SC]:{

        },
        [CONSTANTS.GR]:{

        },
        [CONSTANTS.SPD]:{

        },
        [CONSTANTS.BPD]:DEFAULT_OPTION
    },
    [CONSTANTS.OFFER]:{
        [CONSTANTS.SC]:{

        },
        [CONSTANTS.GR]:{

        },
        [CONSTANTS.SPD]:{

        },
        [CONSTANTS.MF]:{

        },
        [CONSTANTS.BPD]:{

        }
    },
    [CONSTANTS.PRODUCTMANAGEMENT]:{
        [CONSTANTS.PRODUCTMANAGEMENT]: DEFAULT_OPTION
    },
    [CONSTANTS.ACTION_LOG]:{
        [CONSTANTS.ACTION_LOG]: DEFAULT_OPTION
    },
    [CONSTANTS.IMPORT_LOG_BPD]:{
        [CONSTANTS.IMPORT_LOG_BPD]: DEFAULT_OPTION
    },
    [CONSTANTS.STOREMANAGEMENT]:{
        [CONSTANTS.STOREMANAGEMENT]: DEFAULT_OPTION
    }
}


export const getDefaultOptions = (data)=>{
    const {key,currentRouter, isBaseTypeSelected, isEnableNonBasePGToBasePG,batchActionActiveTab} = data; 
    const list:any = DEFAULT_OPTIONS_LIST?.[currentRouter]?.[key];    
    const sortObj = currentRouter === CONSTANTS.REQUEST ? sortedByObjwitMobIdAsc : sortedByObj;
    let defaultOptions =  [...list ||[], appIdObj, sortObj ];

   if(currentRouter == CONSTANTS.PRODUCTMANAGEMENT){
         let sortByObj = JSON.parse(JSON.stringify(prodMgmtSortedByObj));
        //If Ks is turned off, support old API
        if(!isEnableNonBasePGToBasePG){
            let sortVal = [`${CONSTANTS.LAST_UPDATED_TIMESTAMP}${CONSTANTS.DESC}`];
            sortByObj.query = sortVal
            sortByObj.elements[0].query = sortVal;
        }
        
        //Reqmt: Default sort MOB ID with lowest number first, If Base type is seelected
        if(isBaseTypeSelected){
            sortByObj = JSON.parse(JSON.stringify(prodMgmtSortedByWithMobAscObj));
        }
        defaultOptions = [...DEFAULT_OPTION];
   }   
   if(currentRouter == CONSTANTS.ACTION_LOG){
    let sortByObj = JSON.parse(JSON.stringify(actionLogSortByObj));
    defaultOptions = batchActionActiveTab === CONSTANTS.UNIVERSAL ? [...DEFAULT_OPTION, sortByObj , actionLogImportBpdFlag  ] :
                     [...DEFAULT_OPTION, sortByObj, actionLogImport  ]
   }
   if(currentRouter == CONSTANTS.IMPORT_LOG_BPD){
    let sortByObj = JSON.parse(JSON.stringify(importLogSortByObj));
    defaultOptions = [...DEFAULT_OPTION, sortByObj, importLogBpd ] 
   }
   if(currentRouter == CONSTANTS.STOREMANAGEMENT){
    let sortByObj = JSON.parse(JSON.stringify(storeMgmtSortedByObj));
    defaultOptions = [...DEFAULT_OPTION];
   }

   defaultOptions = updateDefaultOptionsForOfferReq({currentRouter, defaultOptions});
   return defaultOptions;
}

export const updateDefaultOptionsForOfferReq = ({ currentRouter, defaultOptions }) => {
  //Add Default options specific to  OR management
  if (currentRouter == CONSTANTS.REQUEST) {
    return [...defaultOptions, ...requestHomeDefaultOptions];
  }
  return defaultOptions;
};

import { CONSTANTS } from "@appConstants/constants";

export const FILTER_OPTIONS = {
    [CONSTANTS.TEMPLATE]:{
        [CONSTANTS.BPD]:[
            
        ]
    },
    [CONSTANTS.REQUEST]:{
        [CONSTANTS.SC]:{

        },
        [CONSTANTS.GR]:{

        },
        [CONSTANTS.SPD]:{

        },
        [CONSTANTS.BPD]:[]
    },
    [CONSTANTS.OFFER]:{
        [CONSTANTS.SC]:{

        },
        [CONSTANTS.GR]:{

        },
        [CONSTANTS.SPD]:{

        },
        [CONSTANTS.MF]:{

        },
        [CONSTANTS.BPD]:{

        }
    },
    [CONSTANTS.PRODUCTMANAGEMENT]:{
        [CONSTANTS.PRODUCTMANAGEMENT]: []
    },
    [CONSTANTS.ACTION_LOG] : {
        [CONSTANTS.ACTION_LOG]: []
    },
    [CONSTANTS.IMPORT_LOG_BPD] : {
        [CONSTANTS.IMPORT_LOG_BPD]: []
    },
    [CONSTANTS.STOREMANAGEMENT]:{
        [CONSTANTS.STOREMANAGEMENT]: []
    }
}

export const getFilterOptions = (obj)=> {
    const {key,currentRouter} = obj;
    const pgObj:any = FILTER_OPTIONS?.[currentRouter]?.[key] ||'';
    return  JSON.stringify(pgObj);
} 

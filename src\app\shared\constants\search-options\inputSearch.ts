
import { CONSTANTS } from "@appConstants/constants";
import { action_log_searchOptions } from "../../../modules/admin/constants/actionLogSearch";
import { import_log_searchOptions } from "../../../modules/admin/constants/importLogSearch";
import { pgManagementSearchOptionPgNameObj_New, pgManagementSearchOptionPgNameObj_Old, productGroupManagementSearchOptions } from "../../../modules/groups/constants/search-options/productGroup";
import { storeGroupManagementSearchOptions } from "../../../modules/groups/constants/search-options/storeGroup";
import { bpdORSearchFields } from "../../../modules/request/constants/search-options/bpd";
import { bpdOTSearchFields } from "../../../modules/templates/constants/bpd";


export const INPUT_SEARCH_OPTIONS = {
    // Options to display in the search dropdown
    [CONSTANTS.TEMPLATE]:{
        [CONSTANTS.BPD]:bpdOTSearchFields
    },
    [CONSTANTS.REQUEST]:{
        [CONSTANTS.SC]:{

        },
        [CONSTANTS.GR]:{

        },
        [CONSTANTS.SPD]:{

        },
        [CONSTANTS.BPD]:bpdORSearchFields,
    },
    [CONSTANTS.OFFER]:{
        [CONSTANTS.SC]:{

        },
        [CONSTANTS.GR]:[{
            label:"verbiageForm",
            field:"verbiageForm",
            query:[],
            showChip:[],
            elements:[{
                type:"text",
                field:'headLine',
                query:[],
            },
            {
                type:"text",
                field:'headLine2',
                query:[],
            },{
                type:"text",
                field:'productDesc',
                query:[],
            }],
            searchButton:true,
            defaultSelect:false
        }
       ],
        [CONSTANTS.SPD]:[],
        [CONSTANTS.BPD]:[],
        [CONSTANTS.MF]:[

            {
                label:"verbiageForm",
                field:"verbiageForm",
                query:[],
                showChip:[],
                elements:[{
                    type:"text",
                    field:'headLine',
                    query:[],
                },
                {
                    type:"text",
                    field:'headLine2',
                    query:[],
                },{
                    type:"text",
                    field:'productDesc',
                    query:[],
                }],
                searchButton:true,
                defaultSelect:false
            }]
        
    },
    [CONSTANTS.PRODUCTMANAGEMENT]:{
        [CONSTANTS.PRODUCTMANAGEMENT]: productGroupManagementSearchOptions
    },
    [CONSTANTS.ACTION_LOG] : {
        [CONSTANTS.ACTION_LOG]: action_log_searchOptions
    },  
    [CONSTANTS.IMPORT_LOG_BPD] : {
        [CONSTANTS.IMPORT_LOG_BPD]: import_log_searchOptions
    },
    [CONSTANTS.STOREMANAGEMENT] : {
        [CONSTANTS.STOREMANAGEMENT]: storeGroupManagementSearchOptions
    }
   
}


const getPageSearchOptions = (obj)=>{
    //Search Options for pg mgmt pg needs to support both old, new Search APIs.

    const {key,currentRouter, isEnableNonBasePGToBasePG, batchActionActiveTab} = obj;
    let pgObj:any = INPUT_SEARCH_OPTIONS?.[currentRouter]?.[key]
    
    if(currentRouter == CONSTANTS.PRODUCTMANAGEMENT){
        const groupNameIndex = 2;
        if(isEnableNonBasePGToBasePG){
            !isGroupNameExists({groupNameIndex, pgObj}) &&  pgObj.splice(2, 0, pgManagementSearchOptionPgNameObj_New);
        }else{
            pgObj = [pgManagementSearchOptionPgNameObj_Old];
        }
    }
    if(currentRouter === CONSTANTS.ACTION_LOG) {
        pgObj = batchActionActiveTab === CONSTANTS.UNIVERSAL ? action_log_searchOptions : []
    }
    return pgObj;
}


function isGroupNameExists(obj){
    //Check if the groupname object already exists
    let {groupNameIndex, pgObj} = obj, isGroupNameExists = false;

    if(pgObj?.[groupNameIndex].field === "name"){
        isGroupNameExists =  true;
    }
    return isGroupNameExists;

}

export const getSearchOptions = (obj)=> {
    let pgObj =  getPageSearchOptions(obj) || '';  
    return JSON.stringify(pgObj);
} 

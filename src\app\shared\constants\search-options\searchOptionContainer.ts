import { getDefaultOptions } from "./defaultSearch";
import { getFilterOptions } from "./filterSearch";
import { getSearchOptions } from "./inputSearch";
import { getSortOptions } from "./sortOptions";

export const inputSearchOption = (obj)=>JSON.parse(getSearchOptions(obj) ||'');
export const filterOption = (obj)=>JSON.parse(getFilterOptions(obj) ||'');
export const defaultOption = (data)=>JSON.parse(JSON.stringify(getDefaultOptions(data)||''));
export const sortOption = (obj) => JSON.parse(getSortOptions(obj) || '');










 
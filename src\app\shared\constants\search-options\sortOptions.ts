import { CONSTANTS } from "@appConstants/constants";
import { pgManagementSortObj } from "@appGroup/constants/search-options/productGroup";
import { sgManagementSortObj } from "../../../modules/groups/constants/search-options/storeGroup";


export const SORT_OPTIONS = {
    [CONSTANTS.TEMPLATE]: {
        [CONSTANTS.BPD]: [

        ]
    },
    [CONSTANTS.REQUEST]: {
        [CONSTANTS.SC]: {

        },
        [CONSTANTS.GR]: {

        },
        [CONSTANTS.SPD]: {

        },
        [CONSTANTS.BPD]: []
    },
    [CONSTANTS.OFFER]: {
        [CONSTANTS.SC]: {

        },
        [CONSTANTS.GR]: {

        },
        [CONSTANTS.SPD]: {

        },
        [CONSTANTS.MF]: {

        },
        [CONSTANTS.BPD]: {
            [CONSTANTS.BPD]: []
        }
    },
    [CONSTANTS.PRODUCTMANAGEMENT]: {
        [CONSTANTS.PRODUCTMANAGEMENT]: pgManagementSortObj
    },
    [CONSTANTS.ACTION_LOG]: {
        [CONSTANTS.ACTION_LOG]: []
    },
    [CONSTANTS.IMPORT_LOG_BPD]: {
        [CONSTANTS.IMPORT_LOG_BPD]: []
    },
    [CONSTANTS.STOREMANAGEMENT]: {
        [CONSTANTS.STOREMANAGEMENT]: sgManagementSortObj
    }
}

export const getSortOptions = (obj) => {
    const { key, currentRouter } = obj;
    const pgObj: any = SORT_OPTIONS?.[currentRouter]?.[key] || '';
    return JSON.stringify(pgObj);
}
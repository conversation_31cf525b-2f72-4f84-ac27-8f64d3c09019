export const addFieldParameters = ({inQuery,inORCondition},{bStart,bEnd},{sStart,sEnd})=>{
    const applyBrackets = [],applyStars = [];
    applyExtraParams(bStart,bEnd,applyBrackets);
    applyExtraParams(sStart,sEnd,applyStars);
    return {
    inQuery,
    inORCondition,
    applyBrackets,
    applyStars
   }
}
const applyExtraParams = (start,end,param)=>{
    start && param.push(start);
    end && param.push(end);
}
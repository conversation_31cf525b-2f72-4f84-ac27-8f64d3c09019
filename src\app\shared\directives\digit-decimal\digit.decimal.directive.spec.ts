import { DigitDecimaNumberDirective } from './digit.decimal.directive';
import { ElementRef } from '@angular/core';

describe('DigitDecimaNumberDirective', () => {
    let directive: DigitDecimaNumberDirective;
    let elementRef: ElementRef;

    beforeEach(() => {
        elementRef = new ElementRef(document.createElement('input'));
        directive = new DigitDecimaNumberDirective(elementRef);
    });

    it('should create an instance', () => {
        expect(directive).toBeTruthy();
    });

    it('should allow valid decimal input', () => {
        directive.digits = '2';
        const event = new KeyboardEvent('keydown', { key: '1' });
        elementRef.nativeElement.value = '1.2';
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should prevent invalid decimal input', () => {
        directive.digits = '2';
        const event = new KeyboardEvent('keydown', { key: '3' });
        elementRef.nativeElement.value = '1.23';
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should allow special keys', () => {
        const event = new KeyboardEvent('keydown', { key: 'Backspace' });
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should prevent paste of invalid decimal input', () => {
        directive.digits = '2';
        const event = new ClipboardEvent('paste', {
            clipboardData: new DataTransfer()
        });
        event.clipboardData.setData('text', '1.234');
        elementRef.nativeElement.value = '1.';
        spyOn(event, 'preventDefault');
        directive.onPaste(event);
        expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should allow valid tender input', () => {
        directive.digits = 'tender';
        const event = new KeyboardEvent('keydown', { key: '5' });
        elementRef.nativeElement.value = '9999.9';
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should prevent invalid tender input', () => {
        directive.digits = 'tender';
        const event = new KeyboardEvent('keydown', { key: '9' });
        elementRef.nativeElement.value = '9999.99';
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).toHaveBeenCalled();
    });
    it('should allow ctrl+c', () => {
        const event = new KeyboardEvent('keydown', { key: 'c', ctrlKey: true });
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should allow ctrl+v', () => {
        const event = new KeyboardEvent('keydown', { key: 'v', ctrlKey: true });
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should allow ctrl+a', () => {
        const event = new KeyboardEvent('keydown', { key: 'a', ctrlKey: true });
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should allow ctrl+x', () => {
        const event = new KeyboardEvent('keydown', { key: 'x', ctrlKey: true });
        spyOn(event, 'preventDefault');
        directive.onKeyDown(event);
        expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should handle paste event with window clipboardData', () => {
        directive.digits = '2';
        const event = {
            clipboardData: null,
            preventDefault: jasmine.createSpy('preventDefault')
        } as unknown as ClipboardEvent;
        window['clipboardData'] = {
            getData: () => '1.23'
        };
        elementRef.nativeElement.value = '1.';
        directive.onPaste(event);
        expect(elementRef.nativeElement.value).toBe('1.');
        delete window['clipboardData'];
    });
});
//Allows to restict number of decimals  usage: appDigitDecimalNumber [digits]="2"  allows 1.23

import { Directive, ElementRef, HostListener, Input } from "@angular/core";

@Directive({
  selector: "[appDigitDecimalNumber]"
})
export class DigitDecimaNumberDirective {
  @Input() digits: string;

  private specialKeys: Array<string> = ["Backspace", "Tab", "End", "Home", "ArrowLeft", "ArrowRight", "Del", "Delete"];

  constructor(private el: ElementRef) { }

  private getRegex(): RegExp {
    const decimalDigits = this.digits === 'tender' ? '2' : (this.digits || '4');
    return this.digits === 'tender'
      ? new RegExp(`^\\d*(\\.\\d{0,${decimalDigits}})?$`, 'g')
      : new RegExp(`^\\d*\\.?\\d{0,${decimalDigits}}$`, 'g');
  }

  private isValidInput(value: string): boolean {
    const regex = this.getRegex();
    return regex.test(value) && !(this.digits === 'tender' && parseFloat(value) > 9999.99);
  }

  @HostListener("keydown", ["$event"]) onKeyDown(event: KeyboardEvent) {
    if (this.specialKeys.includes(event.key) || (event.ctrlKey && ['c', 'v', 'a', 'x'].includes(event.key))) {
      return;
    }

    const current: string = this.el.nativeElement.value;
    const position = this.el.nativeElement.selectionStart;
    const next: string = [current.slice(0, position), event.key === "Decimal" ? "." : event.key, current.slice(position)].join("");

    if (!this.isValidInput(next)) {
      event.preventDefault();
    }
  }

  @HostListener("paste", ["$event"]) onPaste(event: ClipboardEvent) {
    const clipboardData = event.clipboardData || window['clipboardData'];
    const pastedText = clipboardData.getData('text');
    const current: string = this.el.nativeElement.value;
    const position = this.el.nativeElement.selectionStart;
    const next: string = [current.slice(0, position), pastedText, current.slice(position)].join("");

    if (!this.isValidInput(next)) {
      event.preventDefault();
    }
  }
}

import { Directive, Input, OnInit, TemplateRef, ViewContainerRef } from '@angular/core';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';

@Directive({
    selector: "[featureFlag]",
})
export class FeatureFlagDirective implements OnInit {
    private featureFlags: string = "";
    private isHidden = true;
    @Input() set featureFlag(val) {
        if (val) {
            this.featureFlags = val;
            this.updateDom();
        }
    }
    constructor(
        private _templateRef: TemplateRef<any>,
        private _viewContainer: ViewContainerRef,
        private _featureFlags: FeatureFlagsService
    ) {
        // intentionally left empty
    }
    ngOnInit() {

        this.updateDom();
    }
    private updateDom() {
        if (this.checkFlagValidity()) {
            if (this.isHidden) {
                this._viewContainer.createEmbeddedView(this._templateRef);
                this.isHidden = false;
            }
        } else {
            this._viewContainer.clear();
            this.isHidden = true;
        }
    }
    private checkFlagValidity() {
        return this._featureFlags.hasFlags(this.featureFlags);
    }
}
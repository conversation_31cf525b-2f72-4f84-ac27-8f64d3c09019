import { Directive, ElementRef, HostListener, Input, Renderer2 } from '@angular/core';

@Directive({
  selector: "[appInputPattern]"
})
export class InputPatternDirective {
  @Input("appInputPattern") inputType: string;
  pattern: RegExp;

  private regexMap = {
    integer: /^[0-9 ]*$/g,
    float: /^[+-]?([0-9]*[.])?[0-9]*$/g,
    words: /([A-z]*\s)*/g,
    alphaNumeric: /[A-Za-z0-9!@#$%&*-+.?/ ]+$/g,
    commaSepreatedNumber: /^[0-9, ]*$/g,
    onlyAlphaNumeric: /[A-Za-z0-9/ ]+$/g,
    onlyAlphaNumericWithSpace: /[A-Za-z0-9 ]+$/g,
    brandAndSizeAllowedChars: /^[A-Za-z0-9-!@#$%&*+:.?/ ]+$/g,
    onlyonlyAlphaNumericWithComma: /[A-Za-z0-9,/ ]+$/g,
    alphaNumericWithExtraChars: /[A-Za-z0-9-!.:@#$%&*+?/ ]+$/g,
    alphaNumericWithSplChars:/^[ A-Za-z0-9_-]*$/, // Allows spl chars: _  - 
    patternForBasePgName: /^[0-9a-zA-Z.*'/#&@%!+\-?^${}()|[\]\\\s]+$/ ,
    date: /(0[1-9]|1[012])[- /.](0[1-9]|[12][0-9]|3[01])[/](19|20)\d\d/g,  //Matches 10/08/2019
    onlyAlphaNumericWithSpaceAndComma: /[A-Za-z0-9 ,]+$/g,
    integerOrEmpty: /^[0-9]+$|^$|^\s$/
  };

  constructor(public el: ElementRef, public renderer: Renderer2) {
    // intentionally left empty
  }

  @HostListener("keypress", ["$event"]) onInput(e) {
    this.pattern = this.regexMap[this.inputType];
    const inputChar = e.key;
    this.pattern.lastIndex = 0;
    if (!this.pattern.test(inputChar)) {
      e.preventDefault();
    }
  }

  @HostListener('paste', ['$event']) blockPaste(e) {
    this.pattern = this.regexMap[this.inputType];
    this.pattern.lastIndex = 0;
    const clipboardData = e.clipboardData.getData('Text');
    if (!this.pattern.test(clipboardData)) {
      e.preventDefault();
    }
  }

}
import { Directive, HostListener, Input } from "@angular/core";

@Directive({
  selector: "[markAsTouchedOnFocus]"
})
export class markAsTouchedOnFocus {
  @Input() formCtrl;

  constructor() {
    // intentionally left empty
  }

  @HostListener("focus", ["$event"]) onFocus(e) {
    //If a control is focused, mark it as touched. This is useful for displaying validation messages based on the state
    this.formCtrl && this.formCtrl.markAsTouched();
  }
}

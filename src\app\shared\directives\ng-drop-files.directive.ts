import {
  Directive,
  EventEmitter,
  HostListener,
  Input,
  Output
} from '@angular/core';
import { FileItem } from '@appModels/file-item';
import { UploadImagesService } from '@appServices/common/upload-images.service';
import { ToastrService } from 'ngx-toastr';

@Directive({
  selector: '[appNgDropFiles]'
})
export class NgDropFilesDirective {
  msg = '';
  imagePath;
  imageURL: any;

  @Input() files: FileItem[] = [];
  @Output() mouseOver: EventEmitter<boolean> = new EventEmitter();

  constructor(private toastr: ToastrService, public uploadImagesService: UploadImagesService) { 
    // intentionally left empty
  }

  @HostListener('dragover', ['$event'])
  public onDragEnter(event: any) {
    this.mouseOver.emit(true);
    this._preventStop(event);
  }


  @HostListener('dragleave', ['$event'])
  public onDragLeave(event: any) {
    this.mouseOver.emit(false);

  }

  @HostListener('drop', ['$event'])
  public onDrop(event: any) {
    const transfer = this._getTransfer(event);
    if (!transfer) {
      return;
    }
    this._extractFiles(transfer.files);
    this.mouseOver.emit(false);
    this._preventStop(event);

  }

  private _getTransfer(event: any ) {
    return event.dataTransfer ? event.dataTransfer : event.originalEvent.dataTransfer;
  }
  private _extractFiles(filesList: FileList) {

    // tslint:disable-next-line:forin
    for (const property in Object.getOwnPropertyNames(filesList)) {

      const fileTemp = filesList[property];

      if (this._fileCanBeUploaded(fileTemp)) {
        const  newFile = new FileItem( fileTemp);
        if  (this.files.length < 1)  {
          const reader = new FileReader();
          this.imagePath = fileTemp;
          reader.readAsDataURL(fileTemp);
          reader.onload = () => {
            this.imageURL = reader.result;
            newFile.imageURL = this.imageURL;
          };
          this.files.push(newFile);
        } else {
          this.toastr.error('You can add only one file', 'Error!');
        }

      }
    }



  }

  private _fileCanBeUploaded(file: File ): boolean {
    if (this.uploadImagesService.getfiles() === 0  && this.files.length === 0 ) {
      return !this._fileDropped(file.name) && this._isImage(file.type) && this._size(file.size);
    } else {
      this.toastr.error('You can add only one file', 'Error!');
    }
  }

  private _preventStop(event) {
    event.preventDefault();
    event.stopPropagation();
  }

  private _fileDropped(fileName: string ): boolean {

    for (const file of this.files) {
      if (file.fileName === fileName ) {
        this.msg = ' you already added ' + fileName;
        this.toastr.error(this.msg, 'Error!');
        return true;
      }

    }
    return false;
  }

  private _isImage(fileType: string ): boolean {
    if (!fileType.startsWith('image')) {
      this.toastr.error('Please select an image', 'Error!');
    }
    return ( fileType === '' || fileType === undefined ) ? false : fileType.startsWith('image');
  }

  private _size(fileSize): boolean {
    const result = fileSize / 1024 / 1024;
    if (result > 3) {
      this.toastr.error('This image is: ' + result.toFixed(2) + 'MB' + ' please select a 3MB image max.', 'Error!');
      return  false;
    } else {
      return true;
    }
  }
}

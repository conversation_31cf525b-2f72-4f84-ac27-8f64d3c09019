import { Directive, ElementRef, HostListener, Input } from "@angular/core";

@Directive({
    // tslint:disable-next-line:directive-selector
    selector: "[OnlyNumber]",
})
export class OnlyNumberDirective {
    @Input() allowDecimals;
    @Input() allowNegatives;
    @Input() maxLengthNumber;
    @Input() maxNumber;
    @Input() minNumber;
    @Input() OnlyNumber;
    @Input() property;
    @Input() disableZeroKey;
    private regex: RegExp;
    private specialKeys: Array<string> = ["Backspace", "Tab", "End", "Home", "ArrowLeft", "ArrowRight", "Del", "Delete", "Control", "c", "v"];

    constructor(private el: ElementRef) {
        // intentionally left empty
    }



    @HostListener("keydown", ["$event"])
    onKeyDown(event: KeyboardEvent) {
        if(this.OnlyNumber ||this.OnlyNumber===''){


            if(this.allowNegatives && this.specialKeys.indexOf("-") < 0){
                //  this.specialKeys.push("-");
            }

            // Allow Backspace, tab, end, and home keys
            if (this.specialKeys.indexOf(event.key) !== -1) {
                return;

            }
            const current: string = this.el.nativeElement.value;
            const next: string = current.concat(event.key);
            if(this.allowNegatives && event.key==='-' && current.indexOf(event.key)!==-1){
                event.preventDefault();
            }

            if (this.allowDecimals) {
                this.regex = new RegExp("^[,.0-9]+([,.][0-9]+)?$"); //allows  decimals
                if(this.allowNegatives){
                    this.regex = new RegExp("^[-]{0,1}[0-9]*[0-9]*[0-9]*$");
                }

            } else {
                this.regex = new RegExp("^[0-9]*[0-9][0-9]*$"); //allows only integers
                if(this.allowNegatives){
                    this.regex = new RegExp("^[-]{0,1}[0-9]*[0-9]*[0-9]*$");
                }
            }

            if (next && !String(next).match(this.regex)) {
                event.preventDefault();
            }

            if (this.maxLengthNumber) {
                this.maxLengthNumber = parseInt(this.maxLengthNumber);
                let updatedStrLen = next.length;
                if (
                    updatedStrLen &&
                    ((next.includes(".") && updatedStrLen > this.maxLengthNumber + 1) || (!next.includes(".") && updatedStrLen > this.maxLengthNumber))
                ) {
                    event.preventDefault();
                }
            }

            //Max validator
            if (this.maxNumber) {
                if (+next > +this.maxNumber) {
                    event.preventDefault();
                }
            }
            
            //Min validator
            if (this.minNumber) {
                if (+next < +this.minNumber) {
                    event.preventDefault();
                }
            }

            //Input property fields cannot be zero for offer request
            if (this.property) {
                switch (this.property) {
                    case "customPeriod":
                    case "usageLimitPerUser":
                        this.disableZeroKeys(event)
                        break;
                }

            }

            //Prevents input field from being zero
            if (this.disableZeroKey) {
                this.disableZeroKeys(event)
                }
        }



    }
    disableZeroKeys(event: KeyboardEvent) {
        const current: string = this.el.nativeElement.value;
        if (event.key === "0" && current === "") {
            event.preventDefault();

        }
    }
}

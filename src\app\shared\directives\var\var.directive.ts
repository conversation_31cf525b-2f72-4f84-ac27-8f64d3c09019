/* 
Declare a variable in a template
https://stackoverflow.com/questions/38582293/how-to-declare-a-variable-in-a-template-in-angular\

Usage:
 <ng-container *ngVar="getFieldErrorsPostSave('offerEffectiveStartDate') as err">
  <div class="text-danger" *ngIf="err">
    <small *ngIf="err.reqdStartDt">Start date required</small>
    <small *ngIf="err.apiError" class="block">{{err.apiError}}</small>
  </div>
</ng-container>
*/

import { Directive, Input, TemplateRef, ViewContainerRef } from "@angular/core";
@Directive({
  selector: "[ngVar]",
})
export class VarDirective {
  @Input()
  set ngVar(context: any) {
    this.context.$implicit = this.context.ngVar = context;
    this.updateView();
  }

  context: any = {};

  constructor(private vcRef: ViewContainerRef, private templateRef: TemplateRef<any>) {
    // intentionally left empty
  }

  updateView() {
    this.vcRef.clear();
    this.vcRef.createEmbeddedView(this.templateRef, this.context);
  }
}

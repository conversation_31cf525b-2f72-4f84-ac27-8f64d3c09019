import { TestBed } from '@angular/core/testing';
import { HttpRequest, HttpResponse, HttpHandler, HttpHeaders } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { finalize } from 'rxjs/operators';

import { HttpConfigInterceptor } from './httpconfig.interceptor';
import { CONSTANTS } from '@appConstants/constants';
import { ApiErrorsService } from '@appServices/common/api-errors.service';
import { AuthService } from '@appServices/common/auth.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { LoaderService } from '@appServices/common/loader.service';

describe('HttpConfigInterceptor', () => {
    let interceptor: HttpConfigInterceptor;
    let loaderServiceSpy: jasmine.SpyObj<LoaderService>;
    let apiErrorsServiceSpy: jasmine.SpyObj<ApiErrorsService>;
    let featureFlagsServiceSpy: jasmine.SpyObj<FeatureFlagsService>;
    let authServiceSpy: jasmine.SpyObj<AuthService>;

    beforeEach(() => {
        const loaderSpy = jasmine.createSpyObj('LoaderService', ['isDisplayLoader']);
        const apiErrorsSpy = jasmine.createSpyObj('ApiErrorsService', [], {
            apiErrors$: { next: jasmine.createSpy('next') }
        });
        const featureFlagsSpy = jasmine.createSpyObj('FeatureFlagsService', ['isFeatureFlagEnabled']);
        const authSpy = jasmine.createSpyObj('AuthService', ['getTokenString']);

        TestBed.configureTestingModule({
            providers: [
                HttpConfigInterceptor,
                { provide: LoaderService, useValue: loaderSpy },
                { provide: ApiErrorsService, useValue: apiErrorsSpy },
                { provide: FeatureFlagsService, useValue: featureFlagsSpy },
                { provide: AuthService, useValue: authSpy }
            ]
        });

        interceptor = TestBed.inject(HttpConfigInterceptor);
        loaderServiceSpy = TestBed.inject(LoaderService) as jasmine.SpyObj<LoaderService>;
        apiErrorsServiceSpy = TestBed.inject(ApiErrorsService) as jasmine.SpyObj<ApiErrorsService>;
        featureFlagsServiceSpy = TestBed.inject(FeatureFlagsService) as jasmine.SpyObj<FeatureFlagsService>;
        authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

        featureFlagsServiceSpy.isFeatureFlagEnabled.and.returnValue(true);
        authServiceSpy.getTokenString.and.returnValue('dummy-token');
    });

    it('should replace ECOM URL when feature flag is enabled', (done) => {
        const originalUrl = CONSTANTS.ECOM_PRODUCT_GROUP_FEATURE_URL + 'test';
        const expectedUrlPart = CONSTANTS.OCOM_PRODUCT_GROUP_FEATURE_URL;
        const req = new HttpRequest('GET', originalUrl);

        let handledRequest: HttpRequest<any> | undefined;
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                handledRequest = request;
                return of(new HttpResponse({ status: 200 }));
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            next: () => {
                expect(handledRequest).toBeDefined();
                expect(handledRequest!.url).toContain(expectedUrlPart);
                done();
            }
        });
    });

    it('should add normal headers for non-multipart requests', (done) => {
        const req = new HttpRequest('POST', 'http://api/test', {});
        let handledRequest: HttpRequest<any> | undefined;
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                handledRequest = request;
                return of(new HttpResponse({ status: 200 }));
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            next: () => {
                expect(handledRequest).toBeDefined();
                const headers = handledRequest!.headers;
                expect(headers.has('X-Albertsons-userAttributes')).toBeTrue();
                expect(headers.get('X-Albertsons-userAttributes')).toBe('dummy-token');
                expect(headers.has('x-oms-localedatetime')).toBeTrue();
                expect(headers.has('x-oms-timezoneoffset')).toBeTrue();
                done();
            }
        });
    });

    it('should not modify image requests', (done) => {
        const imageUrl = 'http://images.albertsons-media.com/some/image.jpg';
        const req = new HttpRequest('GET', imageUrl);
        let handledRequest: HttpRequest<any> | undefined;
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                handledRequest = request;
                return of(new HttpResponse({ status: 200 }));
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            next: () => {
                expect(handledRequest).toBeDefined();
                expect(handledRequest!.url).toBe(imageUrl);
                done();
            }
        });
    });

    it('should handle requests with req.body.reqObj', (done) => {
        const reqObj = {
            headers: { 'Custom-Header': 'value' },
            isHidePgLoader: true
        };
        const reqBody = { reqObj };
        const req = new HttpRequest('PUT', 'http://api/test', reqBody);

        let handledRequest: HttpRequest<any> | undefined;
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                handledRequest = request;
                return of(new HttpResponse({ status: 200 }));
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            next: () => {
                expect(handledRequest).toBeDefined();
                expect(handledRequest!.body.reqObj).toBeUndefined();
                const headers = handledRequest!.headers;
                expect(headers.get('Custom-Header')).toBe('value');
                expect(headers.has('x-oms-localedatetime')).toBeTrue();
                expect(headers.has('x-oms-timezoneoffset')).toBeTrue();
                expect(loaderServiceSpy.isDisplayLoader).toHaveBeenCalledWith(false);
                done();
            }
        });
    });

    it('should call apiErrorsService.apiErrors$.next(false) on a successful response', (done) => {
        const req = new HttpRequest('GET', 'http://api/test');
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                return of(new HttpResponse({ status: 200 }));
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            next: () => {
                expect(apiErrorsServiceSpy.apiErrors$.next).toHaveBeenCalledWith(false);
                done();
            }
        });
    });

    it('should call apiErrorsService.apiErrors$.next(err) on error when checkString returns false', (done) => {
        const errorResponse = { url: 'http://api/unknown', message: 'error' };
        const req = new HttpRequest('GET', 'http://api/test');
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                return throwError(errorResponse);
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            error: () => {
                expect(apiErrorsServiceSpy.apiErrors$.next).toHaveBeenCalledWith(errorResponse);
                done();
            }
        });
    });

    it('should not call apiErrorsService.apiErrors$.next(err) on error when checkString returns true', (done) => {
        const errorResponse = { url: 'dyna/customerGroup/add', message: 'error' };
        const req = new HttpRequest('GET', 'http://api/test');
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                return throwError(errorResponse);
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            error: () => {
                expect(apiErrorsServiceSpy.apiErrors$.next).not.toHaveBeenCalled();
                done();
            }
        });
    });

    it('should decrement pendingApiRequest and hide the loader on finalize', (done) => {
        const req = new HttpRequest('GET', 'http://api/test');
        let finalizeCalled = false;
        const fakeNext: HttpHandler = {
            handle: (request: HttpRequest<any>) => {
                return of(new HttpResponse({ status: 200 })).pipe(
                    finalize(() => {
                        finalizeCalled = true;
                    })
                );
            }
        };

        interceptor.intercept(req, fakeNext).subscribe({
            next: () => {
                expect(interceptor.pendingApiRequest).toBe(1);
                const lastCallArg = loaderServiceSpy.isDisplayLoader.calls.mostRecent().args[0];
                expect(lastCallArg).toBe(true);
                expect(finalizeCalled).toBeFalse();
                done();
            }
        });
    });
    
    it('should call loaderService.isDisplayLoader(true) when reqObj.isHidePgLoader is false or undefined', () => {
        const reqObj = { someKey: 'someValue', isHidePgLoader: false };

        interceptor.toggleLoader(reqObj);

        expect(loaderServiceSpy.isDisplayLoader).toHaveBeenCalledWith(true);
        expect(reqObj.hasOwnProperty('isHidePgLoader')).toBeFalse();
    });

    describe('getRequestData', () => {
        beforeEach(() => {
            spyOn(interceptor, 'checkLoader').and.returnValue(false);
        });

        it('should reuse incoming headers for multipart requests matching allowed conditions', () => {
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(true);
            const initialHeaders = new HttpHeaders({ 'existing': 'value' });
            const req = new HttpRequest('POST', 'http://test/offers/comment/attach', null, { headers: initialHeaders });
    
            const result = interceptor.getRequestData(req);

            expect(result.headers.get('existing')).toEqual('value');
        });
        it('should create new multipart headers when URL does not match allowed inner conditions', () => {
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(true);
            const req = new HttpRequest('GET', 'http://test/offers/request/attach');
  
            const result = interceptor.getRequestData(req);
  
            expect(result.headers.get('X-Albertsons-userAttributes')).toEqual('dummy-token');
        });


        it('should reuse incoming headers for image search paths in non-multipart branch', () => {
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(false);
            const initialHeaders = new HttpHeaders({ 'existing': 'value' });
            const req = new HttpRequest('GET', 'http://test/offerimages/search', null, { headers: initialHeaders });
    
            const result = interceptor.getRequestData(req);

            expect(result.headers.get('existing')).toEqual('value');
        });

        it('should create normal custom headers for non-multipart normal requests', () => {
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(false);
            const req = new HttpRequest('GET', 'http://test/normal');
    
            const result = interceptor.getRequestData(req);

            expect(result.headers.get('X-Albertsons-userAttributes')).toEqual('dummy-token');
            expect(result.headers.get('x-oms-localedatetime')).toEqual(interceptor.timeDateValue);
            expect(result.headers.get('x-oms-timezoneoffset')).toEqual(interceptor.offsetValueHeader);
        });

        it('should return the original request if URL contains "images.albertsons-media.com"', () => {
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(false);
            const req = new HttpRequest('GET', 'http://images.albertsons-media.com/test');

            const result = interceptor.getRequestData(req);

            expect(result).toEqual(req);
        });

        it('should process req.body.reqObj branch, merging headers and deleting reqObj from body', () => {
            const customReqObj = {
                headers: { 'Custom-Header': 'custom' },
                someOther: 'value',
                isHidePgLoader: false
            };
            const reqBody = { reqObj: customReqObj };
            const req = new HttpRequest('POST', 'http://test/normal', reqBody);
            spyOn(interceptor, 'toggleLoader').and.callThrough();
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(false);

            const result = interceptor.getRequestData(req);

            expect(interceptor.toggleLoader).toHaveBeenCalledWith(customReqObj);
            expect(result.someOther).toEqual('value');
            expect(result.headers.get('Custom-Header')).toEqual('custom');
            expect(result.headers.get('x-oms-localedatetime')).toEqual(interceptor.timeDateValue);
            expect(result.headers.get('x-oms-timezoneoffset')).toEqual(interceptor.offsetValueHeader);
            expect(req.body.reqObj).toBeUndefined();
        });
    });

    describe('getRequestData - Multipart Branch with Provided Headers', () => {
        beforeEach(() => {
            spyOn(interceptor, 'multipartHeadersCheck').and.returnValue(true);
        });

        it('should reuse provided headers when URL contains "ocrp"', () => {
            const initialHeaders = new HttpHeaders({ 'Test-Header': 'test-value' });
            const req = new HttpRequest('GET', 'http://example.com/ocrp', undefined, { headers: initialHeaders });

            const result = interceptor.getRequestData(req);

            expect(result.headers.get('Test-Header')).toEqual('test-value');
        });

        it('should reuse provided headers when URL contains "batch/importCreationStatus"', () => {
            const initialHeaders = new HttpHeaders({ 'Test-Header': 'test-value' });
            const req = new HttpRequest('GET', 'http://example.com/batch/importCreationStatus', undefined, { headers: initialHeaders });

            const result = interceptor.getRequestData(req);

            expect(result.headers.get('Test-Header')).toEqual('test-value');
        });

    });
});

import {
  <PERSON><PERSON>p<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  <PERSON>ttpHead<PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpResponse
} from "@angular/common/http";
import { Injectable, Injector } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { ApiErrorsService } from "@appServices/common/api-errors.service";
import { AuthService } from "@appServices/common/auth.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { LoaderService } from "@appServices/common/loader.service";
import * as moment from "moment";
import "moment-timezone";
import { Observable } from "rxjs";
import { finalize, tap } from "rxjs/operators";

@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {
  constructor(
    private loaderService: LoaderService,
    private injector: Injector,
    private apiErrorsService: ApiErrorsService,
    private featureFlagService: FeatureFlagsService
  ) {
    // intentionally left empty
  }
  pendingApiRequest = 0;
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    req = this.replaceEcomUrlAndGetRequest(req);

    this.pendingApiRequest++;
    return next.handle(req).pipe(
      // retryWithBackoff(100), // retry for 5 times, with each retry, backoff and increment the delay
      tap({
        next: (event: HttpEvent<any>) => {
          if (event instanceof HttpResponse) {
            this.apiErrorsService.apiErrors$.next(false);
            // Moved the code of hiding loader in finalize operator
          }
        },
        error: (err) => {
          const checkFlag = this.checkString(err.url);
          if (!checkFlag) {
            this.apiErrorsService.apiErrors$.next(err);
          }
          // Moved the code of hiding loader in finalize operator

          // if (err instanceof HttpErrorResponse) {
          // }
        }
  }),
      finalize(() => {
        // This is responsible for hiding the loader, if no request is pending then only hide the loader
        this.pendingApiRequest--;
        this.checkRequestAndHideLoader();
      })
    );
  }
  get isOcomProductGroupEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled(
      "enableOcomProductGroup"
    );
  }

  replaceEcomUrlAndGetRequest(request: HttpRequest<any>) {
    if (
      request.url.indexOf(CONSTANTS.ECOM_PRODUCT_GROUP_FEATURE_URL) > -1 &&
      this.isOcomProductGroupEnabled
    ) {
      let newRequestUrl = request.url.replace(
        CONSTANTS.ECOM_PRODUCT_GROUP_FEATURE_URL,
        CONSTANTS.OCOM_PRODUCT_GROUP_FEATURE_URL
      );
      request = request.clone({ url: newRequestUrl });
    }

    return request.clone(this.getRequestData(request));
  }

  checkRequestAndHideLoader() {
    this.pendingApiRequest === 0 && this.loaderService.isDisplayLoader(false);
  }
  checkString(reqUrl) {
    const urlArray = [
      "/api/users/one/",
      "dyna/customerGroup/add",
      "customergroups/replace",
      "dyna/customerGroup/replace",
      "dyna/customerGroup/remove",
      "ocrp",
      "customergroups/add",
      "customergroups/remove",
    ];
    return urlArray.some((url) => {
      return reqUrl.indexOf(url) > -1;
    });
  }
  multipartHeadersCheck(reqUrl) {
    let urlArray = [
      "offers/request/attach",
      "offers/comment/attach",
      "printAd/importCSV",
      "batch/import",
      "universaljob/import/reupload",
      "register/import/",
      "dyna/productGroup/add",
      "dyna/productGroup/replace",
      "customergroups/replace",
      "dyna/productGroup/remove",
      "dyna/customerGroup/add",
      "customergroups/add",
      "customergroups/remove",
      "dyna/customerGroup/replace",
      "dyna/customerGroup/remove",
      "ocrp",
      "api/bulkpoint/validate",
      "api/rewards/validate",
      "nonbase/productgroups/remove",
      "nonbase/productgroups/add",
      "nonbase/productgroups/replace",
    ];
    return urlArray.some((url) => {
      return reqUrl.indexOf(url) > -1;
    });
  }

  checkLoader(reqUrl) {
    let urlArray = [
      "offers/request/attach",
      "offers/comment/attach",
      "dyna/productGroup/add",
      "dyna/productGroup/replace",
      "customergroups/replace",
      "dyna/productGroup/remove",
      "dyna/customerGroup/add",
      "customergroups/add",
      "customergroups/remove",
      "dyna/customerGroup/replace",
      "dyna/customerGroup/remove",
      "nonbase/productgroups/remove",
      "nonbase/productgroups/add",
      "nonbase/productgroups/replace",
    ];
    return urlArray.some((url) => {
      return reqUrl.indexOf(url) > -1;
    });
  }

  getRequestData(req) {
    // Forms the request data
    let headers;
    let reqData;

    if (this.multipartHeadersCheck(req.url)) {
      if (
        (req.url.indexOf("offers/comment/attach") > -1 ||
          req.url.indexOf("ocrp") > -1 ||
          req.url.indexOf("batch/import") > -1 ||
          req.url.indexOf("universaljob/import/reupload") > -1 ||
          req.url.indexOf("register/import") > -1 ||
          req.url.indexOf("printAd/importCSV") > -1) &&
        req.headers
      ) {
        // offers/comment/attach and printAd/importCSV has a formdata object
        // and so the reqObj can't be passed, so trust the headers provided
        headers = req.headers;
        if (req.url.indexOf("batch/importCreationStatus") > -1) {
          headers = req.headers;
        }
      } else {
        headers = new HttpHeaders(CONSTANTS.HTTP_HEADERS_MULTIPART);
        headers = headers.set(
          "X-Albertsons-userAttributes",
          this.injector.get(AuthService).getTokenString()
        );
      }
    } else {
      if (
        req.url.includes("/offerimages/find") || 
        req.url.includes("/offerimages/search") ||
        req.url.includes("images/search") ||
        req.url.includes("offers/images/find")
      ) {
        // Scene 7 or specific image request paths, reuse incoming headers
        headers = req.headers;
      } else {
        // Normal request path: add additional custom headers
        headers = new HttpHeaders(CONSTANTS.HTTP_HEADERS)
          .set("X-Albertsons-userAttributes", this.injector.get(AuthService).getTokenString())
          .set("x-oms-localedatetime", this.timeDateValue)
          .set("x-oms-timezoneoffset", this.offsetValueHeader);
      }
      
    }

    if (req && req.body && req.body.reqObj) {
      // For  spl  cases
      this.toggleLoader(req.body.reqObj);

      headers = req.body.reqObj.headers
        ? new HttpHeaders(req.body.reqObj.headers)
        : headers;

      headers = headers.set("x-oms-localedatetime", this.timeDateValue);
      headers = headers.set("x-oms-timezoneoffset", this.offsetValueHeader);

      reqData = {
        ...req.body.reqObj,
        headers,
      };

      delete req.body.reqObj;
    } else {
      // For  normal  cases
      this.loaderService.isDisplayLoader(!this.checkLoader(req.url));
      if(req.url.indexOf("images.albertsons-media.com") > -1)
      {
        return req;
      }
      reqData = { headers };
    }
    return reqData;
  }
  get timeDateValue() {
    let currDate = new Date();
    return moment(currDate).format("YYYY-MM-DD HH:mm:ss").toString();
  }
  get offsetValueHeader() {
    let currDate = new Date();
    return currDate.getTimezoneOffset().toString();
  }
  toggleLoader(reqObj) {
    // Incase of typeaheads, we need  to display inline loader instead of page level loader.
    // This would ensure that pg loader is not displayed for input fields
    if (!reqObj.isHidePgLoader) {
      this.loaderService.isDisplayLoader(true);
    } else {
      this.loaderService.isDisplayLoader(false);
    }
    delete reqObj.isHidePgLoader;
  }
}

import { of, throwError } from 'rxjs';
import { TestScheduler } from 'rxjs/testing';
import { retryWithBackoff } from './retryWithBackoff';

describe('retryWithBackoff', () => {
    let testScheduler: TestScheduler;

    beforeEach(() => {
        testScheduler = new TestScheduler((actual, expected) => {
            expect(actual).toEqual(expected);
        });
    });

    it('should retry the specified number of times with backoff', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-#', {}, { status: 500 });
            const expected = '- 300ms - 900ms - 1500ms #';

            const result$ = source$.pipe(retryWithBackoff(300, 3, 300));
        });
    });

    it('should not retry if error status is not in retryCodes', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-#', {}, { status: 404 });
            const expected = '-#';

            const result$ = source$.pipe(retryWithBackoff(300, 3, 300));

            expectObservable(result$).toBe(expected, {}, { status: 404 });
        });
    });

    it('should complete without errors if source completes', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-a|');
            const expected = '-a|';

            const result$ = source$.pipe(retryWithBackoff(300, 3, 300));

            expectObservable(result$).toBe(expected);
        });
    });

    it('should retry only the specified number of times', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-#', {}, { status: 500 });
            const expected = '- 300ms - 900ms - 1500ms #';

            const result$ = source$.pipe(retryWithBackoff(300, 3, 300));
        });
    });

    it('should not retry if maxRetry is set to 0', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-#', {}, { status: 500 });
            const expected = '-#';

            const result$ = source$.pipe(retryWithBackoff(300, 0, 300));

            expectObservable(result$).toBe(expected, {}, { status: 500 });
        });
    });

    it('should retry with increasing backoff time', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-#', {}, { status: 500 });
            const expected = '- 300ms a 900ms b 1500ms c #';

            const result$ = source$.pipe(retryWithBackoff(300, 3, 300));

        });
    });

    it('should stop retrying after max retries are exhausted', () => {
        testScheduler.run(({ cold, expectObservable }) => {
            const source$ = cold('-#', {}, { status: 500 });
            const expected = '- 300ms - 900ms - 1500ms #';

            const result$ = source$.pipe(retryWithBackoff(300, 3, 300));

        });
    });
});
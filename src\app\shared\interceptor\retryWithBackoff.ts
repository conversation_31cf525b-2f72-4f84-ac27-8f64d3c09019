import { Observable, of, throwError } from 'rxjs';
import { delay, mergeMap, retry, retryWhen } from 'rxjs/operators';


const DEFAULT_MAX_RETRIES = 3;
const DEFAULT_BACKOFF = 300;

export function retryWithBackoff(delayMs: number, maxRetry = DEFAULT_MAX_RETRIES, backoffMs = DEFAULT_BACKOFF) {
    let retries = maxRetry;
    const retryCodes = ["408", "500", "502", "503", "504", "522", "524"]
    return (src: Observable<any>) =>
        src.pipe(
            retry({
              delay: (error) => {
                let errorStatus = error.status.toString();
                if (retryCodes.includes(errorStatus)) {
                  if (retries-- > 0) {
                    const backoffTime = delayMs + (maxRetry - retries) * backoffMs;
                    return of(error).pipe(delay(backoffTime)); // Retry with backoff
                  }
                }
                // Return error if retry condition is not met
                return throwError(() => error);
              }
            })
          );
      } 
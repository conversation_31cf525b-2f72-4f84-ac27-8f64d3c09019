export interface UpcListItem {
  avgPrice?: number;
  bggm?: string;
  bugm?: string;
  categoryDescription?: string;
  categoryId?: string;
  consumerPackageGoods?: any;
  corporateItemCode?: null;
  groupId?: string;
  groupName?: string;
  has90DaysSales?: boolean;
  has365DaysSales?: boolean;
  isActive?: boolean;
  isDiscontinued?: boolean;
  isDisplayer?: boolean;
  manufacturerCode?: string;
  quantity?: string;
  salesRank: null;
  uom?: string;
  upc?: string;
  upcDescription?: string;
  version?: string;
}

export interface UpcListItemShort {
  upc?: string;
  description?: string;
  quantity?: string;
  uom?: string;
}

export interface UpcResponseData {
  region?: string;
  rankedUpcs?: UpcListItem[];
  rogCodeRing?: any[];
}

export interface UpcListItemResponse {
  success?: boolean;
  messageCode?: number;
  message?: string;
  upcRegionResponse?: UpcResponseData;
}

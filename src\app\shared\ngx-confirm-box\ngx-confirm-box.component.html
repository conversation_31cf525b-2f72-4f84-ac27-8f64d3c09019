<div class="overlay" [ngStyle]="{'background-color':confirmaray.bgColor}" *ngIf="showHide" >
    <div class="confirmbox-container">
	<h3 *ngIf="confirmaray.confirmHeading">{{confirmaray.confirmHeading}}</h3>
	<p>{{confirmaray.confirmContent}}</p>
	<div class="col-md-12 btn-grp col-sm-12">
	<button class="cancel-btn btn-default btn btn-sm" (click)="hideConfirm()">{{confirmaray.confirmCanceltext}}</button>
	<button class="btn-primary btn btn-sm confirm-btn" (click)="returnConfirmBox()">{{confirmaray.confirmOkaytext}}</button></div>
	</div>
</div>
import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { NgxConfirmBoxService } from '@appShared/ngx-confirm-box/ngx-confirm-box.service';
import { NgxConfirmBoxComponent } from './ngx-confirm-box.component';

@NgModule({
  imports: [
    CommonModule
  ],
  declarations: [NgxConfirmBoxComponent],
  exports: [NgxConfirmBoxComponent],
  providers:[NgxConfirmBoxService]
})
export class NgxConfirmBoxModule { 


static forRoot(): ModuleWithProviders<NgxConfirmBoxModule> {
    return {
      ngModule: NgxConfirmBoxModule,
      providers: [
        NgxConfirmBoxService,
      ],
    };
  }
}
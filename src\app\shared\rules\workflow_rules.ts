import { WORKFLOW_RULES_BPD } from "./workflow_rules_bpd";

export const OFFER_REQUEST_WORKFLOW_RULES_UPP = {
  Draft: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Save"]
  },
  Submitted: {
    Manage: ["Assign", "Edit"],
    Summary: [],
    Detail: []
  },
  Assigned: {
    Manage: ["Edit"],
    Summary: [],
    Detail: []
  },
  Processing: {
    Manage: ["Edit"],
    Summary: [],
    Detail: []
  },
  Completed: {
    Manage: ["Edit"],
    Summary: [],
    Detail: []
  },
  Expired: {
    Manage: [],
    Summary: [],
    Edit: [],
    Detail: []
  },
  Editing: {
    Manage: ["Edit"],
    Summary: [],
    Edit: ["Save"],
    Detail: [],
    Process: false
  },
  Updating: {
    Manage: ["Edit"],
    Summary:  [],
    Edit: ["Save"],
    Detail: [],
    Process: false
  },
  Canceled: {
    Manage: [],
    Summary: [],
    Edit: [],
    Detail: [],
    Process: false
  }
};

export const OFFER_REQUEST_WORKFLOW_RULES = {
  Draft: {
    Manage: ["Edit", "Copy", "Delete"],
    Summary: ["Copy", "Delete"],
    Detail: ["Save", "Copy", "Delete"]
  },
  Submitted: {
    Manage: ["Assign", "Edit", "Copy", "Delete"],
    Summary: ["Copy", "Delete"],
    Detail: ["Copy", "Delete"]
  },
  Assigned: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Cancel"],
    Detail: ["Copy"]
  },
  Processing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Cancel"],
    Detail: ["Copy"]
  },
  Completed: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Cancel"],
    Detail: ["Copy"]
  },
  Expired: {
    Manage: ["Copy"],
    Summary: ["Copy"],
    Edit: ["Copy"],
    Detail: []
  },
  Editing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Updating: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary:  ["Copy", "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Canceled: {
    Manage: ["Copy"],
    Summary: ["Copy"],
    Edit: ["Copy"],
    Detail: [],
    Process: false
  }
};

export const OFFER_REQUEST_WORKFLOW_RULESGR = {
  Draft: {
    Manage: ["Edit", "Copy", "Delete"],
    Summary: ["Copy", "Multiple Copies", "Delete"],
    Detail: ["Save", "Copy","Delete"]
  },
  Submitted: {
    Manage: ["Edit", "Copy", "Delete"],
    Summary: ["Copy", "Multiple Copies", "Delete"],
    Detail: ["Copy", "Delete"]
  },
  Assigned: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Detail: ["Copy"]
  },
  Processing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Detail: ["Copy"]
  },
  Completed: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Detail: ["Copy"]
  },
  Expired: {
    Manage: ["Copy"],
    Summary: ["Copy"],
    Edit: ["Copy"],
    Detail: []
  },
  Editing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Updating: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary:  ["Copy", "Multiple Copies", "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Canceled: {
    Manage: ["Copy"],
    Summary: ["Copy" ,"Multiple Copies"],
    Edit: ["Copy"],
    Detail: [],
    Process: false
  },
  'Removed Unclipped': {
    Manage: ["Edit", "Cancel","Copy"],
    Summary: ["Cancel","Copy", "Multiple Copies"],
    Detail: [],
    Process: false
  }
};

export const OFFER_REQUEST_WORKFLOW_RULESSPD = {
  Draft: {
    Manage: ["Edit", "Copy", "Delete"],
    Summary: ["Copy", "Multiple Copies", "Delete"],
    Detail: ["Save", "Copy","Delete"]
  },
  Submitted: {
    Manage: ["Edit", "Copy", "Delete"],
    Summary: ["Copy", "Multiple Copies", "Delete"],
    Detail: ["Copy", "Delete"]
  },
  Assigned: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Detail: ["Copy"]
  },
  Processing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Detail: ["Copy"]
  },
  Completed: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Detail: ["Copy"]
  },
  Expired: {
    Manage: ["Copy"],
    Summary: ["Copy"],
    Edit: ["Copy"],
    Detail: []
  },
  Editing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Multiple Copies", "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Updating: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary:  ["Copy", "Cancel","Multiple Copies"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Canceled: {
    Manage: ["Copy"],
    Summary: ["Copy" , "Multiple Copies"],
    Edit: ["Copy"],
    Detail: [],
    Process: false
  },
  'Removed Unclipped': {
    Manage: ["Edit", "Cancel","Copy"],
    Summary: ["Cancel","Copy", "Multiple Copies"],
    Detail: [],
    Process: false
  }
};
export const OFFER_REQUEST_WORKFLOW_RULESBPD = {
  Draft: {
    Manage: ["Edit", "Copy","Cancel", "Delete"],
    Summary: ["Copy", "Cancel",  "Delete"],
    Detail: ["Save", "Copy","Cancel","Delete"]
  },
  Submitted: {
    Manage: ["Edit", "Copy", "Cancel", "Delete"],
    Summary: ["Copy", "Cancel", "Delete"],
    Detail: ["Copy", "Cancel", "Delete"]
  },
  Processing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy",  "Cancel"],
    Detail: ["Copy","Cancel"]
  },
  Completed: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy", "Cancel"],
    Detail: ["Copy", "Cancel"]
  },
  Expired: {
    Manage: ["Copy"],
    Summary: ["Copy"],
    Edit: ["Copy"],
    Detail: []
  },
  Editing: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary: ["Copy",  "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy"],
    Process: false
  },
  Updating: {
    Manage: ["Edit", "Copy", "Cancel"],
    Summary:  ["Copy", "Cancel"],
    Edit: ["Save", "Copy"],
    Detail: ["Copy", "Cancel"],
    Process: false
  },
  Canceled: {
    Manage: ["Copy"],
    Summary: ["Copy" ],
    Edit: ["Copy"],
    Detail: [],
    Process: false
  },
  'Removed Unclipped': {
    Manage: ["Edit", "Cancel","Copy"],
    Summary: ["Cancel","Copy"],
    Detail: ["Cancel"],
    Process: false
  }
};
export const OFFER_WORKFLOW_RULES_SC = {
  Draft: {
    Manage: ["Edit", "Deploy Now", "Defer Deploy"],
    Summary: [],
    Detail: ["Preview"]
  },
  Deployed: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Preview: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Published: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Expired: {
    Manage: [],
    Summary: [],
    Detail: []
  }
};
export const OFFER_WORKFLOW_RULES_GR_SPD = {
  Draft: {
    Manage: ["Edit", "Deploy Now + Publish", "Defer Deploy + Publish"],
    Summary: [],
    Detail: ["Preview"]
  },
  Deployed: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Preview: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Published: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Expired: {
    Manage: [],
    Summary: [],
    Detail: []
  }
};
export const OFFER_WORKFLOW_RULES_MF = {
  Draft: {
    Manage: ["Edit", "Deploy Now + Publish", "Defer Deploy + Publish"],
    Summary: [],
    Detail: ["Preview"]
  },
  Deployed: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Preview: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Published: {
    Manage: ["Edit"],
    Summary: [],
    Detail: ["Preview"]
  },
  Expired: {
    Manage: [],
    Summary: [],
    Detail: []
  }
};
export const OFFER_WORKFLOW_RULESBPD = WORKFLOW_RULES_BPD;

export const PLU_MANAGEMENT_RULES={
  General: {
    Manage: ["Edit","Delete"]
  }
}
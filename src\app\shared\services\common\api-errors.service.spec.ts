import { TestBed } from '@angular/core/testing';
import { ApiErrorsService } from './api-errors.service';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { CommonGroupService } from '@appGroupsServices/common-group.service';
import { PluDetailsService } from '../../../modules/request/services/pluDetails.service';
import { of, Subject, BehaviorSubject } from 'rxjs';
import { FormGroup } from '@angular/forms';
import { ROUTES_CONST } from '@appConstants/routes_constants';

describe('ApiErrorsService', () => {
  let service: ApiErrorsService;
  let offerMappingService: jasmine.SpyObj<OfferMappingService>;
  let requestFormService: jasmine.SpyObj<RequestFormService>;
  let commonGroupService: jasmine.SpyObj<CommonGroupService>;
  let pluDetailsService: jasmine.SpyObj<PluDetailsService>;

  beforeEach(() => {
    const offerMappingServiceSpy = jasmine.createSpyObj('OfferMappingService', [], {
      isformSubmitAttempted$: new BehaviorSubject<boolean>(false),
      offerDefinitionForm: new FormGroup({}),
      conditionForm: new FormGroup({}),
      benefitForm: new FormGroup({}),
      podDetailsForm: new FormGroup({}),
      storeTerminalsForm: new FormGroup({}),
    });
    const requestFormServiceSpy = jasmine.createSpyObj('RequestFormService', ['displayPgError$'], {
      displayPgError$: of({}),
      isDraftSaveAttempted: new BehaviorSubject<boolean>(false),
      requestForm: new FormGroup({}),
    });
    const commonGroupServiceSpy = jasmine.createSpyObj('CommonGroupService', [], {
      isStoreGroupSubmitAttempted$: new Subject(),
      storeGroupForm: new FormGroup({}),
    });
    const pluDetailsServiceSpy = jasmine.createSpyObj('PluDetailsService', [], {
      isDraftSaveAttempted: new BehaviorSubject<boolean>(false),
      pluForm: new FormGroup({}),
    });

    TestBed.configureTestingModule({
      providers: [
        ApiErrorsService,
        { provide: OfferMappingService, useValue: offerMappingServiceSpy },
        { provide: RequestFormService, useValue: requestFormServiceSpy },
        { provide: CommonGroupService, useValue: commonGroupServiceSpy },
        { provide: PluDetailsService, useValue: pluDetailsServiceSpy },
      ],
    });

    service = TestBed.inject(ApiErrorsService);
    offerMappingService = TestBed.inject(OfferMappingService) as jasmine.SpyObj<OfferMappingService>;
    requestFormService = TestBed.inject(RequestFormService) as jasmine.SpyObj<RequestFormService>;
    commonGroupService = TestBed.inject(CommonGroupService) as jasmine.SpyObj<CommonGroupService>;
    pluDetailsService = TestBed.inject(PluDetailsService) as jasmine.SpyObj<PluDetailsService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize subscriptions in constructor', () => {
    spyOn(ApiErrorsService.prototype, 'initSubscribes').and.callThrough();
    service = new ApiErrorsService(offerMappingService, requestFormService, commonGroupService, pluDetailsService);
    expect(service.initSubscribes).toHaveBeenCalled();
  });

  it('should bind component function to service', () => {
    const fn = () => {};
    service.bindComponentFunctionToService(fn);
    expect(service.myFunc).toBe(fn);
  });

  it('should return the current href', () => {
    spyOn(service, 'getHref').and.returnValue('http://localhost');
    expect(service.getHref()).toBe('http://localhost');
  });

  it('should get forms based on URL fragments', () => {
    spyOn(service, 'getHref').and.returnValue('http://localhost/offers');
    spyOn(service, 'getOfferTabForms').and.returnValue(['form1']);
    expect(service.getForm()).toEqual(['form1']);
  });

  it('should get config forms based on URL fragments', () => {
    const urlFragments = [ROUTES_CONST.GROUPS.StoreGroup];
    commonGroupService.isStoreGroupSubmitAttempted$ = new Subject();
    commonGroupService.storeGroupForm = new FormGroup({});
    expect(service.getConfigForms(urlFragments)).toEqual([commonGroupService.storeGroupForm]);
  });

  it('should check if inline groups modal is open', () => {
    const mockElement = document.createElement('div');
    spyOn(document, 'querySelector').and.returnValue(mockElement);
    commonGroupService.isStoreGroupSubmitAttempted$ = new Subject();
    commonGroupService.storeGroupForm = new FormGroup({});
    expect(service.isInlineGroupsModalOpen()).toEqual([commonGroupService.storeGroupForm]);
  });

  it('should get PLU forms', () => {
    pluDetailsService.isDraftSaveAttempted = new BehaviorSubject<boolean>(false);
    pluDetailsService.pluForm = new FormGroup({});
    expect(service.getPLUForms()).toEqual([pluDetailsService.pluForm]);
  });

  it('should get request forms', () => {
    const inlineGroupsModalForm = new FormGroup({});
    const inlinePLUModalForm = new FormGroup({});
    const isInlineGroupsModalOpenSpy = spyOn(service, 'isInlineGroupsModalOpen').and.returnValue([inlineGroupsModalForm]);
    const isInlinePLUModalOpenSpy = spyOn(service, 'isInlinePLUModalOpen').and.returnValue([inlinePLUModalForm]);
    requestFormService.isDraftSaveAttempted = new BehaviorSubject<boolean>(false);
    requestFormService.requestForm = new FormGroup({});

    // Test when inline groups modal is open
    expect(service.getRequestForms()).toEqual([inlineGroupsModalForm]);

    // Test when inline PLU modal is open
    isInlineGroupsModalOpenSpy.and.returnValue(null);
    expect(service.getRequestForms()).toEqual([inlinePLUModalForm]);

    // Test when neither modal is open
    isInlinePLUModalOpenSpy.and.returnValue(null);
    expect(service.getRequestForms()).toEqual([requestFormService.requestForm]);
  });

  it('should check if inline PLU modal is open', () => {
    const mockElement = document.createElement('div');
    spyOn(document, 'querySelector').and.returnValue(mockElement);
    pluDetailsService.isDraftSaveAttempted = new BehaviorSubject<boolean>(false);
    pluDetailsService.pluForm = new FormGroup({});
    expect(service.isInlinePLUModalOpen()).toEqual([pluDetailsService.pluForm]);
  });

  it('should generate page level error', () => {
    const apiError = { error: { errors: [{ message: 'Error1' }, { message: 'Error2' }], message: '' } };
    service.generatePageLevelError(apiError);
    expect(apiError.error.message).toBe('Error1&&Error2');
  });

  it('should get offer tab forms based on URL fragments', () => {
    spyOn(service, 'isInlineGroupsModalOpen').and.returnValue(null);

    const urlFragments = [ROUTES_CONST.OFFERS.OfferDefinition];
    expect(service.getOfferTabForms(urlFragments)).toEqual([offerMappingService.offerDefinitionForm]);

    urlFragments[0] = ROUTES_CONST.OFFERS.Conditions;
    expect(service.getOfferTabForms(urlFragments)).toEqual([offerMappingService.conditionForm]);

    urlFragments[0] = ROUTES_CONST.OFFERS.Benefits;
    expect(service.getOfferTabForms(urlFragments)).toEqual([offerMappingService.benefitForm]);

    urlFragments[0] = ROUTES_CONST.OFFERS.PodDetails;
    expect(service.getOfferTabForms(urlFragments)).toEqual([offerMappingService.podDetailsForm]);

    urlFragments[0] = ROUTES_CONST.OFFERS.Locations;
    expect(service.getOfferTabForms(urlFragments)).toEqual([offerMappingService.storeTerminalsForm]);
  });

  it('should get forms based on URL fragments in getForm method', () => {
    const getHrefSpy = spyOn(service, 'getHref');

    getHrefSpy.and.returnValue(`http://localhost/${ROUTES_CONST.OFFERS.Offers}`);
    spyOn(service, 'getOfferTabForms').and.returnValue(['offerForm']);
    expect(service.getForm()).toEqual(['offerForm']);

    const pluForm = new FormGroup({});
    getHrefSpy.and.returnValue(`http://localhost/${ROUTES_CONST.REQUEST.PluDetails}`);
    spyOn(service, 'getPLUForms').and.returnValue([pluForm]);
    expect(service.getForm()).toEqual([pluForm]);

    getHrefSpy.and.returnValue(`http://localhost/${ROUTES_CONST.REQUEST.Request}`);
    spyOn(service, 'getRequestForms').and.returnValue(['requestForm']);
    expect(service.getForm()).toEqual(['requestForm']);

    getHrefSpy.and.returnValue(`http://localhost/${ROUTES_CONST.GROUPS.CustomerGroup}`);
    spyOn(service, 'getConfigForms').and.returnValue(['configForm']);
    expect(service.getForm()).toEqual(['configForm']);
  });
});
import { Injectable } from '@angular/core';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { CommonGroupService } from '@appGroupsServices/common-group.service';
import { OfferMappingService } from '@appOffersServices/offer-mapping.service';
import { RequestFormService } from '@appRequestServices/request-form.service';
import { UnsubscribeAdapter } from '@appUtilities/subscription/unsubscribe-adapter';
import { Subject } from 'rxjs';
import { PluDetailsService } from '../../../modules/request/services/pluDetails.service';

@Injectable({
  providedIn: 'root',
})
export class ApiErrorsService extends UnsubscribeAdapter{
  constructor(
    private offerMappingService: OfferMappingService,
    public requestFormService: RequestFormService,
    private commonGroupService: CommonGroupService,
    private pluDetailsService: PluDetailsService,
  ) {
    super();
    this.initSubscribes();
  }
  apiErrors$ = new Subject();
  isformSubmitAttempted$;
  myFunc =  (a) => {};

  bindComponentFunctionToService(fn: () => void) {
    this.myFunc = fn;
    // from now on, call myFunc wherever you want inside this service
  }

  initSubscribes(){
    this.subs.sink = this.requestFormService.displayPgError$.subscribe((response:any) => {
      
      this.myFunc(response);
    });
  }



  getHref() {
    // this is so we can stub this in the tests to control the path
    return window.location.href;
  }

  getForm() {
    const urlFragments = this.getHref().split('/');
    let formsArr;

    if (urlFragments.includes(ROUTES_CONST.OFFERS.Offers)) {
      formsArr = this.getOfferTabForms(urlFragments);
    } else if (urlFragments.includes(ROUTES_CONST.REQUEST.PluDetails)) {
      formsArr = this.getPLUForms();
    } else if (urlFragments.includes(ROUTES_CONST.REQUEST.Request)) {
      formsArr = this.getRequestForms();
    } else if (urlFragments.includes(ROUTES_CONST.GROUPS.CustomerGroup)
              || urlFragments.includes(ROUTES_CONST.GROUPS.PointsGroup)
              || urlFragments.includes(ROUTES_CONST.GROUPS.ProductGroup)
              || urlFragments.includes(ROUTES_CONST.GROUPS.StoreGroup)  ) {
      formsArr = this.getConfigForms(urlFragments);
    }
    return formsArr;
  }

  getConfigForms(urlFragments) {
    let formsArr;

    if (urlFragments.includes(ROUTES_CONST.GROUPS.StoreGroup)) {
      this.isformSubmitAttempted$ = this.commonGroupService.isStoreGroupSubmitAttempted$;
      formsArr = [this.commonGroupService.storeGroupForm];
    } /* else if (urlFragments.includes("customer-groups")) {
      this.isformSubmitAttempted$ = this.commonGroupService.isCustomerGroupSubmitAttempted$;
      formsArr = [this.commonGroupService.customerGroupForm];
    } else if (urlFragments.includes("productGroup")) {
      this.isformSubmitAttempted$ = this.commonGroupService.isProductGroupSubmitAttempted$;
      formsArr = [this.commonGroupService.productGroupForm];
    }else if (urlFragments.includes("pointsGroups")) {
      this.isformSubmitAttempted$ = this.commonGroupService.isPointGroupSubmitAttempted$;
      formsArr = [this.commonGroupService.pointGroupForm];
    }*/

    return formsArr;
  }
  isInlineGroupsModalOpen() {
    // If inline group creation modal is open
    let formsArr;
    if (document.querySelector('body  create-store-group')) {
      this.isformSubmitAttempted$ = this.commonGroupService.isStoreGroupSubmitAttempted$;
      formsArr = [this.commonGroupService.storeGroupForm];
    }
    return formsArr;
  }
  getPLUForms() {
    this.isformSubmitAttempted$ = this.pluDetailsService.isDraftSaveAttempted;
    let formsArr = [this.pluDetailsService.pluForm];
    return formsArr;
  }

  getRequestForms() {
    let formsArr = this.isInlineGroupsModalOpen();
    let pluFormArr = this.isInlinePLUModalOpen();
    if (formsArr) {
      return formsArr;
    }
    if(pluFormArr){
      return pluFormArr;
    }
    this.isformSubmitAttempted$ = this.requestFormService.isDraftSaveAttempted;
    formsArr = [this.requestFormService.requestForm];
    return formsArr;
  }

  isInlinePLUModalOpen(){
    // If inline group creation modal is open
    let formsArr;
    if (document.querySelector('body  app-plu-add-edit-section')) {
      this.isformSubmitAttempted$ = this.pluDetailsService.isDraftSaveAttempted;
      formsArr = [this.pluDetailsService.pluForm];
    }
    return formsArr;

  }
  generatePageLevelError(apiError){
  const errors =    apiError?.error?.errors?.map((err) => {
                     return `${err.message}`;
                   });
      apiError.error.errors = null;
      apiError.error.message = errors?.join('&&'); 
  }

  getOfferTabForms(urlFragments) {
    let formsArr = this.isInlineGroupsModalOpen();
    if (formsArr) {
      return formsArr;
    }

    this.isformSubmitAttempted$ = this.offerMappingService.isformSubmitAttempted$;
    if (urlFragments.includes(ROUTES_CONST.OFFERS.OfferDefinition)) {
      formsArr = [this.offerMappingService.offerDefinitionForm];
    } else if (urlFragments.includes(ROUTES_CONST.OFFERS.Conditions)) {
      formsArr = [this.offerMappingService.conditionForm];
    } else if (urlFragments.includes(ROUTES_CONST.OFFERS.Benefits)) {
      formsArr = [this.offerMappingService.benefitForm];
    } else if (urlFragments.includes(ROUTES_CONST.OFFERS.PodDetails)) {
      formsArr = [this.offerMappingService.podDetailsForm];
    } else if (urlFragments.includes(ROUTES_CONST.OFFERS.Locations)) {
      formsArr = [this.offerMappingService.storeTerminalsForm];
    }

    return formsArr;
  }
}

import { TestBed } from '@angular/core/testing';
import { AppService } from './app.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { AuthService } from './auth.service';
import { of, throwError } from 'rxjs';

describe('AppService', () => {
  let service: AppService;
  let permissionsService: jasmine.SpyObj<PermissionsService>;
  let authService: jasmine.SpyObj<AuthService>;

  beforeEach(() => {
    const permissionsSpy = jasmine.createSpyObj('PermissionsService', ['loadPermissions']);
    const authSpy = jasmine.createSpyObj('AuthService', ['login', 'getUserPermissions', 'getFeatureFlagsUI'], { authenticated: false, apiConfigService: { featuresFlag: {} } });

    TestBed.configureTestingModule({
      providers: [
        AppService,
        { provide: PermissionsService, useValue: permissionsSpy },
        { provide: AuthService, useValue: authSpy }
      ]
    });

    service = TestBed.inject(AppService);
    permissionsService = TestBed.inject(PermissionsService) as jasmine.SpyObj<PermissionsService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    spyOn(console, 'error'); // Spy on console.error
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('authenticateApp', () => {
    it('should call login if not authenticated', () => {
      Object.defineProperty(authService, 'authenticated', { value: false });
      service.authenticateApp();
      expect(authService.login).toHaveBeenCalled();
    });

    it('should not call login if authenticated', () => {
      Object.defineProperty(authService, 'authenticated', { value: true });
      service.authenticateApp();
      expect(authService.login).not.toHaveBeenCalled();
    });
  });

  describe('authorizeApp', () => {
    it('should load permissions if authenticated', async () => {
      Object.defineProperty(authService, 'authenticated', { value: true });
      authService.getUserPermissions.and.returnValue(of(['PERMISSION_1', 'PERMISSION_2']));
      await service.authorizeApp();
      expect(permissionsService.loadPermissions).toHaveBeenCalledWith(['PERMISSION_1', 'PERMISSION_2']);
    });

    it('should handle error if getUserPermissions fails', async () => {
      Object.defineProperty(authService, 'authenticated', { value: true });
      authService.getUserPermissions.and.returnValue(throwError('Error'));
      try {
        await service.authorizeApp();
      } catch (e) {
        expect(permissionsService.loadPermissions).toHaveBeenCalledWith([]);
        expect(console.error).toHaveBeenCalledWith('Error');
      }
    });

    it('should return undefined if not authenticated', () => {
      Object.defineProperty(authService, 'authenticated', { value: false });
      const result = service.authorizeApp();
      expect(result).toBeUndefined();
    });
  });

  describe('InitializeFeatureFlags', () => {
    it('should initialize feature flags if authenticated', async () => {
      Object.defineProperty(authService, 'authenticated', { value: true });
      authService.getFeatureFlagsUI.and.returnValue(of(['flag1']));
      const features = await service.InitializeFeatureFlags();
      expect(features).toEqual(['flag1']);
      expect(service.features).toEqual(['flag1']);
      expect(service.featureFlags$.value).toEqual(['flag1']);
      expect(authService.apiConfigService.featuresFlag).toEqual(['flag1']);
    });

    it('should handle error if getFeatureFlagsUI fails', async () => {
      Object.defineProperty(authService, 'authenticated', { value: true });
      authService.getFeatureFlagsUI.and.returnValue(throwError('Error'));
      try {
        await service.InitializeFeatureFlags();
      } catch (e) {
        expect(console.error).toHaveBeenCalledWith('Error initializing feature flags:', 'Error');
      }
    });

    it('should return undefined if not authenticated', async () => {
      Object.defineProperty(authService, 'authenticated', { value: false });
      const features = await service.InitializeFeatureFlags();
      expect(features).toBeUndefined();
    });
  });

  describe('getFeatureFlags', () => {
    it('should return feature flags', () => {
      service.features = { flag1: true };
      const featureFlags = service.getFeatureFlags();
      expect(featureFlags).toEqual({ flag1: true });
    });
  });

  describe('getUserAuthorizationData', () => {
    it('should load admin permissions', () => {
      service.getUserAuthorizationData();
      expect(permissionsService.loadPermissions).toHaveBeenCalledWith([
        "ADMIN",
        "VIEW_OFFER_REQUESTS",
        "VIEW_GR_SPD_OFFER_REQUESTS",
        "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
        "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
        "DO_STORE_COUPON_REQUESTS",
        "ASSIGN_DIGITAL_USERS",
        "ASSIGN_NON_DIGITAL_USERS",
        "PROCESS_ASSIGNED_DIGITAL_OFFER_REQUESTS",
        "PROCESS_ASSIGNED_NON_DIGITAL_OFFER_REQUESTS",
        "PROCESS_ANY_DIGITAL_OFFER_REQUESTS",
        "PROCESS_ANY_NON_DIGITAL_OFFER_REQUESTS",
        "EXIT_EDIT_OFFER_REQUESTS",
        "VIEW_OFFERS",
        "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
        "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
        "DO_ASSIGNED_DIGITAL_OFFERS",
        "DO_ASSIGNED_NON_DIGITAL_OFFERS",
        "DO_ANY_DIGITAL_OFFERS",
        "DO_ANY_NON_DIGITAL_OFFERS",
        "DO_POD_OFFERS",
        "VIEW_CUSTOMER_GROUPS",
        "MANAGE_CUSTOMER_GROUPS",
        "VIEW_STORE_GROUPS",
        "MANAGE_STORE_GROUPS",
        "VIEW_PRODUCT_GROUPS",
        "MANAGE_PRODUCT_GROUPS",
        "VIEW_POINT_GROUPS",
        "MANAGE_POINT_GROUPS",
        "VIEW_COMMENTS",
        "VIEW_COMMENT_GROUPS",
        "VIEW_POD_PLAYGROUND",
        "MANAGE_POD_PLAYGROUND",
        "MANAGE_PLU_RESERVATION",
        "VIEW_PLU_RESERVATION",
        'VIEW_ADMIN',
        'VIEW_EVENT_MAINT',
        'MANAGE_EVENT_MAINT',
        "MANAGE_EVENT_MAINTENANCE_PAGE",
        "VIEW_EVENT_MAINTENANCE_PAGE",
        "DO_BATCH_ASSIGN",
        "DO_BATCH_UPDATE_OFFER_DATES",
        "DO_BATCH_PUBLISH_OFFERS",
        "DO_BATCH_EVENTS",
        "DO_BATCH_UPDATE_FOR_TESTING",
        "DO_BATCH_UPDATE_POD"
      ]);
    });
  });
});
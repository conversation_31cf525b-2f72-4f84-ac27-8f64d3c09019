import { PermissionsService } from '@appShared/albertsons-angular-authorization';
// app.service.ts
import { Injectable, Injector } from "@angular/core";
import { BehaviorSubject, firstValueFrom } from "rxjs";
import { AuthService } from "./auth.service";
@Injectable()
export class AppService {
  featureFlags$ = new BehaviorSubject(null);
  features: any;
  constructor(private injector: Injector, private permissionsService: PermissionsService) { 
    // intentionally left empty
  }

  authenticateApp(): void {
    if (!this.injector.get(AuthService).authenticated) {
      this.injector.get(AuthService).login();
    }
  }

  authorizeApp(): Promise<any> | void {
    if (this.injector.get(AuthService).authenticated) {
      return new Promise((resolve, reject) => {
        this.injector
          .get(AuthService)
          .getUserPermissions()
          .toPromise()
          .then((res) => {
            this.permissionsService.loadPermissions(res);
            resolve(res);
          })
          .catch((err) => {
            const per = [];
            this.permissionsService.loadPermissions(per);
            console.error(`${err}`);
            reject(err);
          });
      });
    }
  }


  async InitializeFeatureFlags(): Promise<any> {
    /** Method for initializing feature flags- used in app initialization*/
    const authService = this.injector.get(AuthService);
    if (authService.authenticated) {
      try {
        const res = await firstValueFrom(authService.getFeatureFlagsUI());
        this.features = res;
        authService.apiConfigService.featuresFlag = this.features;
        this.featureFlags$.next(this.features);
        return this.features; // Return features
      } catch (err) {
        console.error('Error initializing feature flags:', err);
        throw err; // Propagate the error to the caller
      }
    } else {
      return Promise.resolve(undefined); // Return a resolved promise with undefined
    }
  }
  
  
  
  

  getFeatureFlags() {
    let featureFlags = this.features;
    return featureFlags;
  }

  // *** toggle commenting on authorizeApp methods to change which permissions, listed below, are loaded. ***
  // authorizeApp() {
  //   this.getUserAuthorizationData();
  // }

  getUserAuthorizationData() {
    const adminPermissions = [
      "ADMIN",
      "VIEW_OFFER_REQUESTS",
      "VIEW_GR_SPD_OFFER_REQUESTS",
      "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
      "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      "DO_STORE_COUPON_REQUESTS",
      "ASSIGN_DIGITAL_USERS",
      "ASSIGN_NON_DIGITAL_USERS",
      "PROCESS_ASSIGNED_DIGITAL_OFFER_REQUESTS",
      "PROCESS_ASSIGNED_NON_DIGITAL_OFFER_REQUESTS",
      "PROCESS_ANY_DIGITAL_OFFER_REQUESTS",
      "PROCESS_ANY_NON_DIGITAL_OFFER_REQUESTS",
      "EXIT_EDIT_OFFER_REQUESTS",
      "VIEW_OFFERS",
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      "DO_ASSIGNED_DIGITAL_OFFERS",
      "DO_ASSIGNED_NON_DIGITAL_OFFERS",
      "DO_ANY_DIGITAL_OFFERS",
      "DO_ANY_NON_DIGITAL_OFFERS",
      "DO_POD_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "MANAGE_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "MANAGE_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "VIEW_POD_PLAYGROUND",
      "MANAGE_POD_PLAYGROUND",
      "MANAGE_PLU_RESERVATION",
      "VIEW_PLU_RESERVATION",
      'VIEW_ADMIN',
      'VIEW_EVENT_MAINT',
      'MANAGE_EVENT_MAINT',
      "MANAGE_EVENT_MAINTENANCE_PAGE",
      "VIEW_EVENT_MAINTENANCE_PAGE",
      "DO_BATCH_ASSIGN",
      "DO_BATCH_UPDATE_OFFER_DATES",
      "DO_BATCH_PUBLISH_OFFERS",
      "DO_BATCH_EVENTS",
      "DO_BATCH_UPDATE_FOR_TESTING",
      "DO_BATCH_UPDATE_POD"
    ];

    const noAccessPermissions = [];

    const offerRequestorPermissions = [
      "VIEW_OFFER_REQUESTS",
      "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      "DO_STORE_COUPON_REQUESTS",
      "VIEW_OFFERS",
      "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "MANAGE_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "MANAGE_PLU_RESERVATION",
      "VIEW_PLU_RESERVATION",
      "VIEW_OFFER_REQUESTS_HISTORY",
      "VIEW_OFFERS_HISTORY",
      "DO_BATCH_ASSIGN",
      "DO_BATCH_UPDATE_OFFER_DATES"
    ];

    const offerBuilder = [
      "VIEW_OFFER_REQUESTS",
      "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
      "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      "DO_STORE_COUPON_REQUESTS",
      
      "ASSIGN_DIGITAL_USERS",
      "ASSIGN_NON_DIGITAL_USERS",
      "PROCESS_ASSIGNED_DIGITAL_OFFER_REQUESTS",
      "PROCESS_ASSIGNED_NON_DIGITAL_OFFER_REQUESTS",
      "VIEW_OFFERS",
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      "DO_ASSIGNED_DIGITAL_OFFERS",
      "DO_ASSIGNED_NON_DIGITAL_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "MANAGE_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "MANAGE_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "MANAGE_PLU_RESERVATION",
      "VIEW_PLU_RESERVATION",
      "VIEW_OFFER_REQUESTS_HISTORY",
      "VIEW_OFFERS_HISTORY",
      "VIEW_POD_PLAYGROUND",
      "MANAGE_POD_PLAYGROUND",
      "DO_BATCH_ASSIGN",
      "DO_BATCH_UPDATE_OFFER_DATES",
      "DO_BATCH_UPDATE_FOR_TESTING"
    ];

    const offerBuilderOnlyDigital = [
      "VIEW_OFFER_REQUESTS",
      "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
      "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      "DO_STORE_COUPON_REQUESTS",
      "ASSIGN_DIGITAL_USERS",
      "PROCESS_ASSIGNED_DIGITAL_OFFER_REQUESTS",
      "VIEW_OFFERS",
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      "DO_ASSIGNED_DIGITAL_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "MANAGE_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "MANAGE_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "MANAGE_PLU_RESERVATION",
      "VIEW_PLU_RESERVATION",
    ];

    const offerBuilderOnlyNonDigital = [
      "VIEW_OFFER_REQUESTS",
      "DEFAULT_SEARCHES_VIEW_ASSIGNMENT_SEARCHES",
      "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      "DO_STORE_COUPON_REQUESTS",
      
      "ASSIGN_NON_DIGITAL_USERS",
      "PROCESS_ASSIGNED_NON_DIGITAL_OFFER_REQUESTS",
      "VIEW_OFFERS",
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      "DO_ASSIGNED_NON_DIGITAL_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "MANAGE_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "MANAGE_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "MANAGE_PLU_RESERVATION",
      "VIEW_PLU_RESERVATION",
    ];
    const readOnlyPermissions = [
      "VIEW_OFFER_REQUESTS",
      "VIEW_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "VIEW_PLU_RESERVATION",
      "VIEW_OFFERS_HISTORY",
      "VIEW_OFFER_REQUESTS_HISTORY",
      "VIEW_BPD_OFFER_REQUESTS",
      "VIEW_BATCH_IMPORT_ACTION",
      "VIEW_GR_SPD_OFFER_REQUESTS",
      "VIEW_MF_OFFERS"
    ];

    const podBuilderPermissions = [
      "VIEW_OFFER_REQUESTS",
      "VIEW_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "VIEW_OFFERS",
      "DO_POD_OFFERS",
      "MANAGE_PLU_RESERVATION",
      "VIEW_PLU_RESERVATION",
      "DO_BATCH_UPDATE_POD",
      "DO_BATCH_PUBLISH_OFFERS",
      "MANAGE_EVENT_MAINTENANCE_PAGE",
      "VIEW_EVENT_MAINTENANCE_PAGE",
      "VIEW_ADMIN",
      "MANAGE_POD_PLAYGROUND",
      "VIEW_POD_PLAYGROUND",
      "VIEW_OFFERS_HISTORY",
      "VIEW_OFFER_REQUESTS_HISTORY"
    ];

    const mfBuilderPermissions = [
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "VIEW_MF_OFFERS",
      "DO_MF_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "MANAGE_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "MANAGE_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "VIEW_OFFERS_HISTORY",
      "DO_BATCH_UPDATE_TERMINAL",
      "DO_BATCH_DEPLOY_PUBLISH",
      "DO_BATCH_EVENTS",
      "DO_BATCH_EXPORT",
      "DO_BATCH_INAD",
      "DO_BATCH_INEMAIL",
    ];

    const mfReadOnlyPermissions = [
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "VIEW_MF_OFFERS",
      "VIEW_CUSTOMER_GROUPS",
      "VIEW_STORE_GROUPS",
      "VIEW_PRODUCT_GROUPS",
      "VIEW_POINT_GROUPS",
      "VIEW_COMMENTS",
      "VIEW_COMMENT_GROUPS",
      "VIEW_OFFER_REQUESTS_HISTORY",
      "VIEW_OFFERS_HISTORY",
    ];
    const mfReaderWithSCrequester = [
      "VIEW_COMMENTS",
      "VIEW_OFFERS",
      "DO_BATCH_ASSIGN",
      "DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS",
      "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS",
      "MANAGE_PLU_RESERVATION",
      "MANAGE_STORE_GROUPS",
      "VIEW_OFFERS_HISTORY",
      "VIEW_POINT_GROUPS",
      "VIEW_PLU_RESERVATION",
      "VIEW_CUSTOMER_GROUPS",
      "MANAGE_CUSTOMER_GROUPS",
      "MANAGE_PRODUCT_GROUPS",
      "VIEW_OFFER_REQUESTS_HISTORY",
      "DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS",
      "CANCEL_OFFER_REQUEST",
      "VIEW_COMMENT_GROUPS",
      "DO_BATCH_UPDATE_OFFER_DATES",
      "VIEW_MF_OFFERS",
      "VIEW_STORE_GROUPS",
     "VIEW_OFFER_REQUESTS",
      "VIEW_PRODUCT_GROUPS",
      "DO_STORE_COUPON_REQUESTS"
    ]
    const mfReaderWithImportRequester = ["VIEW_BATCH_IMPORT_ACTION","VIEW_COMMENTS",
    "VIEW_OFFERS_HISTORY","VIEW_POINT_GROUPS","VIEW_CUSTOMER_GROUPS","VIEW_BPD_OFFER_REQUESTS"
    ,"DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS","DO_BATCH_EXPORT",
    "MANAGE_BATCH_IMPORT_ACTION","VIEW_COMMENT_GROUPS",
    "VIEW_MF_OFFERS","VIEW_ADMIN","VIEW_STORE_GROUPS",
    "VIEW_PRODUCT_GROUPS"
  ]
  const mfReaderWithGRSPDRequester = ["VIEW_BATCH_IMPORT_ACTION","DO_BATCH_COPY","MANAGE_POINT_GROUPS",
  "VIEW_OFFERS","DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS","VIEW_GR_SPD_OFFER_REQUESTS","VIEW_OFFERS_HISTORY",
  "VIEW_PLU_RESERVATION","VIEW_POINT_GROUPS","VIEW_CUSTOMER_GROUPS","VIEW_OFFER_REQUESTS_HISTORY",
  "EDIT_GR_SPD_REQUESTS","DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS","CANCEL_OFFER_REQUEST","DO_BATCH_EXPORT",
  "VIEW_STORE_GROUPS","VIEW_BATCH_ACTION_LOG","VIEW_COMMENTS","DO_BATCH_IMPORT_EDIT_REQUEST",
  "DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS","MANAGE_PLU_RESERVATION","MANAGE_STORE_GROUPS","MANAGE_CUSTOMER_GROUPS",
  "MANAGE_PRODUCT_GROUPS","VIEW_COMMENT_GROUPS","VIEW_MF_OFFERS","VIEW_PRODUCT_GROUPS","DO_BATCH_SUBMIT"]

  const mfReaderWithPodBuilder =["VIEW_COMMENTS","VIEW_OFFERS","DO_BATCH_PUBLISH_OFFERS","DO_BATCH_UPDATE_POD",
  "MANAGE_PLU_RESERVATION","VIEW_OFFERS_HISTORY","VIEW_POINT_GROUPS","VIEW_PLU_RESERVATION","VIEW_CUSTOMER_GROUPS",
  "MANAGE_POD_PLAYGROUND","VIEW_OFFER_REQUESTS_HISTORY","DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS","VIEW_EVENT_MAINT",
  "VIEW_COMMENT_GROUPS","MANAGE_EVENT_MAINT","VIEW_MF_OFFERS","VIEW_POD_PLAYGROUND","VIEW_STORE_GROUPS","VIEW_ADMIN",
  "VIEW_OFFER_REQUESTS","VIEW_PRODUCT_GROUPS","DO_POD_OFFERS"];

  const allRequesters = ['VIEW_BATCH_IMPORT_ACTION', 
  'DO_BATCH_COPY', 
  'MANAGE_POINT_GROUPS', 
  'VIEW_OFFERS', 
  'DEFAULT_SEARCHES_VIEW_YOUR_REQUESTS', 
  'VIEW_GR_SPD_OFFER_REQUESTS', 
  'VIEW_OFFERS_HISTORY', 
  'VIEW_PLU_RESERVATION', 
  'VIEW_POINT_GROUPS', 
  'VIEW_CUSTOMER_GROUPS', 
  'VIEW_OFFER_REQUESTS_HISTORY', 
  'EDIT_GR_SPD_REQUESTS', 
  'DEFAULT_SEARCHES_VIEW_YOUR_ASSIGNED_OFFERS', 
  'CANCEL_OFFER_REQUEST', 
  'DO_BATCH_EXPORT', 
  'VIEW_STORE_GROUPS', 
  'VIEW_BATCH_ACTION_LOG', 
  'VIEW_COMMENTS', 
  'DO_BATCH_IMPORT_EDIT_REQUEST', 
  'DEFAULT_SEARCHES_VIEW_OFFERS_FROM_YOUR_REQUESTS', 
  'MANAGE_PLU_RESERVATION', 'MANAGE_STORE_GROUPS', 'MANAGE_CUSTOMER_GROUPS', 'MANAGE_PRODUCT_GROUPS', 
  'VIEW_COMMENT_GROUPS', 'VIEW_PRODUCT_GROUPS', 'DO_BATCH_SUBMIT', 'VIEW_BPD_OFFER_REQUESTS', 
  'MANAGE_BATCH_IMPORT_ACTION', 'VIEW_MF_OFFERS', 'VIEW_ADMIN', 'DO_BATCH_ASSIGN', 
  'DO_BATCH_UPDATE_OFFER_DATES', 'VIEW_OFFER_REQUESTS', 'DO_STORE_COUPON_REQUESTS'];

    this.permissionsService.loadPermissions(adminPermissions);
  }
}

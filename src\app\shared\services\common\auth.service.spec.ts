import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { RouterTestingModule } from '@angular/router/testing';
import { AuthService } from './auth.service';
import { MsalService, MsalBroadcastService, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { Router } from '@angular/router';
import { of, BehaviorSubject, throwError } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { User } from '../../../../user';
// import { OAuthSettings } from '../../../../oauth';


const OAuthSettings = {
  scopes: ['user.read']
};

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  let msalServiceMock: jasmine.SpyObj<MsalService>;
  let msalBroadcastServiceMock: jasmine.SpyObj<MsalBroadcastService>;
  let initialDataServiceMock: jasmine.SpyObj<InitialDataService>;
  let routerMock: jasmine.SpyObj<Router>;

  beforeEach(() => {
    msalServiceMock = jasmine.createSpyObj('MsalService', ['instance']);
    msalServiceMock.instance = jasmine.createSpyObj('instance', ['loginPopup', 'getAllAccounts', 'logoutRedirect']);
    msalBroadcastServiceMock = jasmine.createSpyObj('MsalBroadcastService', [], {
      msalSubject$: new BehaviorSubject({}),
      inProgress$: new BehaviorSubject('None')
    });
    initialDataServiceMock = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);
    routerMock = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        AuthService,
        { provide: MsalService, useValue: msalServiceMock },
        { provide: MsalBroadcastService, useValue: msalBroadcastServiceMock },
        { provide: MSAL_GUARD_CONFIG, useValue: {} },
        { provide: InitialDataService, useValue: initialDataServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
  })  
  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });


  it('should return true if user is authenticated', () => {
    msalServiceMock.instance = {
      getActiveAccount: jasmine.createSpy().and.returnValue({ name: 'test' }),
      initialize: jasmine.createSpy(),
      acquireTokenPopup: jasmine.createSpy(),
      acquireTokenRedirect: jasmine.createSpy(),
      acquireTokenSilent: jasmine.createSpy(),
      getAccountByHomeId: jasmine.createSpy(),
      getAccountByLocalId: jasmine.createSpy(),
      getAccountByUsername: jasmine.createSpy(),
      getAllAccounts: jasmine.createSpy(),
      handleRedirectPromise: jasmine.createSpy(),
      loginPopup: jasmine.createSpy(),
      loginRedirect: jasmine.createSpy(),
      logout: jasmine.createSpy(),
      logoutPopup: jasmine.createSpy(),
      logoutRedirect: jasmine.createSpy(),
      ssoSilent: jasmine.createSpy(),
      addEventCallback: jasmine.createSpy(),
      removeEventCallback: jasmine.createSpy(),
      enableAccountStorageEvents: jasmine.createSpy(),
      disableAccountStorageEvents: jasmine.createSpy(),
      getLogger: jasmine.createSpy(),
      setLogger: jasmine.createSpy(),
      setActiveAccount: jasmine.createSpy(),
      acquireTokenByCode: jasmine.createSpy(),
      addPerformanceCallback: jasmine.createSpy(),
      removePerformanceCallback: jasmine.createSpy(),
      getAccount: jasmine.createSpy(),
      getTokenCache: jasmine.createSpy(),
      getConfiguration: jasmine.createSpy(),
      initializeWrapperLibrary: jasmine.createSpy(),
      setNavigationClient: jasmine.createSpy(),
      hydrateCache: jasmine.createSpy(),
      clearCache: jasmine.createSpy()
    };
    expect(service.authenticated).toBeTrue();
  });

  describe('setLoginDisplay', () => {
    it('should set loginDisplay to true if there are accounts', () => {
      msalServiceMock.instance.getAllAccounts = jasmine.createSpy().and.returnValue([{}]);
      service.setLoginDisplay();
      expect(service.loginDisplay).toBeTrue();
    });

    it('should set loginDisplay to false if there are no accounts', () => {
      msalServiceMock.instance.getAllAccounts = jasmine.createSpy().and.returnValue([]);
      service.setLoginDisplay();
      expect(service.loginDisplay).toBeFalse();
    });
  });

  describe('getUserDetails', () => {
    it('should return user details if there is an active account', () => {
      const account = { name: 'John Doe', username: '<EMAIL>' };
      msalServiceMock.instance.getAllAccounts = jasmine.createSpy().and.returnValue([account]);

      const user = service.getUserDetails();

      expect(user.firstName).toBe('John');
      expect(user.lastName).toBe('Doe');
      expect(user.displayName).toBe('John Doe');
      expect(user.emailId).toBe('<EMAIL>');
      expect(user.userPrincipalName).toBe('<EMAIL>');
    });

    it('should return user with undefined properties if there is no active account', () => {
      msalServiceMock.instance.getAllAccounts = jasmine.createSpy().and.returnValue([]);

      const user = service.getUserDetails();

      expect(user.firstName).toBeUndefined();
      expect(user.lastName).toBeUndefined();
      expect(user.displayName).toBeUndefined();
      expect(user.emailId).toBeUndefined();
      expect(user.userPrincipalName).toBeUndefined();
    });
  })


  describe('getProfile', () => {
    it('should fetch and set the profile', async () => {
      const mockProfile = { name: 'John Doe', email: '<EMAIL>' };
      service.getProfile().then(profile => {
        expect(profile).toEqual(mockProfile);
        expect(service.profile).toEqual(mockProfile);
      });

      const reqs = httpMock.match('https://graph.microsoft.com/v1.0/me');
      expect(reqs.length).toBe(1);
      expect(reqs[0].request.method).toBe('GET');
      reqs[0].flush(mockProfile);
    });

    it('should handle errors gracefully', async () => {
      const mockError = new ErrorEvent('Network error', {
        message: 'Failed to fetch profile'
      });

      service.getProfile().catch(error => {
        expect(error).toBeDefined();
        expect(service.profile).toBeUndefined();
      });

      const reqs = httpMock.match('https://graph.microsoft.com/v1.0/me');
      expect(reqs.length).toBe(1);
      expect(reqs[0].request.method).toBe('GET');
      reqs[0].error(mockError);
    });
  });

  describe('login', () => {
    it('should not call loginPopup if user is authenticated', () => {
      spyOnProperty(service, 'authenticated', 'get').and.returnValue(true);

      const result = service.login();

      expect(result).toBeFalse();
      expect(msalServiceMock.instance.loginPopup).not.toHaveBeenCalled();
    });
  })


  describe('getTokenString', () => {
    it('should construct and return the token string with user details', () => {
      const user = new User();
      user.firstName = 'John';
      user.lastName = 'Doe';
      user.emailId = '<EMAIL>';
      user.userPrincipalName = '<EMAIL>';
      spyOn(service, 'getUserDetails').and.returnValue(user);

      const tokenString = service.getTokenString();

      expect(service.getUserDetails).toHaveBeenCalled();
      expect(tokenString).toBe('userId=john.doe;firstName=John;lastName=Doe;email=<EMAIL>');
      expect(localStorage.getItem('tokenString')).toBe(tokenString);
    });

    it('should construct and return the token string with empty user details if user is not available', () => {
      spyOn(service, 'getUserDetails').and.returnValue(null);

      const tokenString = service.getTokenString();

      expect(service.getUserDetails).toHaveBeenCalled();
      expect(tokenString).toBe('userId=;firstName=;lastName=;email=');
      expect(localStorage.getItem('tokenString')).toBe(tokenString);
    });
  });

  describe('getUserId', () => {
    it('should return the extracted user ID in lowercase', () => {
      const user = new User();
      user.userPrincipalName = '<EMAIL>';
      service.user = user;

      const userId = service.getUserId();

      expect(userId).toBe('john.doe');
    });

    it('should return undefined if user is not defined', () => {
      service.user = undefined;

      const userId = service.getUserId();

      expect(userId).toBeUndefined();
    });

    it('should return undefined if userPrincipalName is not defined', () => {
      const user = new User();
      user.userPrincipalName = undefined;
      service.user = user;

      const userId = service.getUserId();

      expect(userId).toBeUndefined();
    });
  });


  describe('onUserDataAvailable', () => {
    it('should call the callback and resolve the promise when user data is available', async () => {
      const callback = jasmine.createSpy('callback').and.returnValue('result');
      service.isUserDataAvailable.next(true);

      const result = await service.onUserDataAvailable(callback);

      expect(callback).toHaveBeenCalled();
      expect(result).toBe('result');
    });

    it('should reject the promise if the callback throws an error', async () => {
      const callback = jasmine.createSpy('callback').and.throwError('Test error');
      service.isUserDataAvailable.next(true);

      try {
        await service.onUserDataAvailable(callback);
        fail('Expected promise to be rejected');
      } catch (error) {
        expect(callback).toHaveBeenCalled();
        expect(error.message).toBe('Test error');
      }
    });
  }) 

  describe('getUserPermissions', () => {
    it('should make an HTTP GET request to getUserPermissionsAPI and return the response', () => {
      const mockPermissions = ['permission1', 'permission2'];
      service.getUserPermissions().subscribe((permissions) => {
        expect(permissions).toEqual(mockPermissions);
      });

      const req = httpMock.expectOne(() => true);
      expect(req.request.method).toBe('GET');
      req.flush(mockPermissions);
    });
  })  

  it('should handle HTTP errors gracefully', () => {
    const mockError = new ErrorEvent('Network error', {
      message: 'Failed to fetch permissions'
    });

    service.getUserPermissions().subscribe({
      next: () => fail('Expected error, but got success response'),
      error: (error) => {
        expect(error).toBeDefined();
      }
    });

    const req = httpMock.expectOne(() => true);
    expect(req.request.method).toBe('GET');
    req.error(mockError);
  });

  describe('getFeatureFlagsUI', () => {
      it("should make an HTTP GET request to getFeatureFlagsApi and return the response", () => {
          const HttpClientStub = TestBed.inject(HttpClient);
          const stub = spyOn(HttpClientStub, "get").and.returnValue(of({}));
          service.getFeatureFlagsUI();
          expect(stub).toHaveBeenCalled
      })
  });

  describe('ngOnDestroy', () => {
    it('should call next and complete on _destroying$', () => {
      spyOn(service['_destroying$'], 'next');
      spyOn(service['_destroying$'], 'complete');

      service.ngOnDestroy();

      expect(service['_destroying$'].next).toHaveBeenCalledWith(undefined);
      expect(service['_destroying$'].complete).toHaveBeenCalled();
    });
  })  
})
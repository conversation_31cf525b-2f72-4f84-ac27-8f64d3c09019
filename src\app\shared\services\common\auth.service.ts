import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { MSAL_GUARD_CONFIG, MsalBroadcastService, MsalGuardConfiguration, MsalService } from '@azure/msal-angular';
import { AuthenticationResult, EventMessage, EventType, InteractionStatus } from '@azure/msal-browser';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { OAuthSettings } from '../../../../oauth';
import { User } from '../../../../user';

const GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/me';
@Injectable({
  providedIn: 'root'
})
export class AuthService implements OnDestroy {
  //public authenticated: boolean;
  public user: User;

  _authenticationContext$ = new Subject();
  readonly _destroying$ = new Subject<void>();
  isUserDataAvailable: BehaviorSubject<any> = new BehaviorSubject(false);
  profile
  getUserPermissionsAPI: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_USER_PERMISSIONS);

  getFeatureFlagsApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_FEATURE_FLAGS_UI);
  loginDisplay: boolean;

  constructor(
    @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private broadcastService: MsalBroadcastService, private msalService: MsalService,
    private http: HttpClient,
    public apiConfigService: InitialDataService,
    private router: Router
  ) {
    this.broadcastService.msalSubject$
      .pipe(
        filter(
          (msg: EventMessage) => msg.eventType === EventType.LOGIN_SUCCESS
            || msg.eventType === EventType.SSO_SILENT_SUCCESS)
      ).subscribe((result: EventMessage) => {
        const payload = result?.payload as AuthenticationResult;
        this.msalService?.instance?.setActiveAccount(payload?.account);
        this.user = this.getUserDetails()
        this.isUserDataAvailable.next(true);
        window.location.reload();
      });
    this.broadcastService.inProgress$
      .pipe(filter((status: InteractionStatus) => status === InteractionStatus.None)
        , takeUntil(this._destroying$))
      .subscribe(async () => {
        if (!this.authenticated) return false;
        this.user = this.getUserDetails();
        this.isUserDataAvailable.next(true);
        this.setLoginDisplay();
      });
  }
  get authenticated(): boolean {
    return this.msalService?.instance?.getActiveAccount() ? true : false;
  }
  setLoginDisplay() {
    this.loginDisplay = this.msalService.instance.getAllAccounts().length > 0;
  }
  getUserDetails() {
    let user = new User();
    let accounts = this.msalService?.instance?.getAllAccounts();
    const activeAccount = accounts && accounts[0];
    let splitName = activeAccount?.name?.split(" ");
    user.firstName = splitName?.[0];
    user.lastName = splitName?.[1];
    user.displayName = activeAccount?.name;
    // Prefer the mail property, but fall back to userPrincipalName
    user.emailId = activeAccount?.username;
    user.userPrincipalName = activeAccount?.username;

    return user

  }
  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }
  async getProfile(): Promise<any> {
    await this.http.get(GRAPH_ENDPOINT)
      .toPromise().then(profile => {
        this.profile = profile;

      });
    return this.profile
  }

  login() {
    if (this.authenticated) return false;
    setTimeout(() => this.msalService?.instance?.loginPopup(OAuthSettings.scopes), 0);
  }
  logout() {
    this.user = null;
    localStorage.removeItem('tokenString');
    return this.msalService?.logoutRedirect();
  }

  getTokenString() {
    this.user = this.getUserDetails();
    let extractedUserId = this.user ? this.user?.userPrincipalName?.substring(0, this.user?.userPrincipalName?.lastIndexOf("@")) : '';
    let tokenString = `userId=${extractedUserId.toLowerCase()};firstName=${this.user ? this.user.firstName : ''};lastName=${this.user ? this.user.lastName : ''};email=${this.user ? this.user.emailId.toLowerCase() : ''}`;
    localStorage.setItem('tokenString', tokenString);
    return tokenString;
  }
  getUserId() {
    // let extractedUserId = this.myTaskService.isMyTasksClicked ? this.user && this.user.userPrincipalName && this.user.userPrincipalName.substring(0, this.user.userPrincipalName.lastIndexOf("@")):"OMS";
    let extractedUserId = this.user?.userPrincipalName?.substring(0, this.user?.userPrincipalName?.lastIndexOf("@"));
    return extractedUserId?.toLowerCase();
  }
  onUserDataAvailable(callback) {
    return new Promise((resolve, reject) => {
      this.isUserDataAvailable.subscribe((user: boolean) => {
        if (user && typeof callback === 'function') {
          try {
            const result = callback();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        }
      });
    });
  }

  getUserPermissions(programCode = CONSTANTS.SC): Observable<string[]> {
    return this.http.get<string[]>(`${this.getUserPermissionsAPI}`);
  }

  getFeatureFlagsUI(): Observable<string[]> {
    return this.http.get<string[]>(this.getFeatureFlagsApi);
  }

}
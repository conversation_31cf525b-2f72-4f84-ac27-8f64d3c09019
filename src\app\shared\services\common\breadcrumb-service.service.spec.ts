import { TestBed } from '@angular/core/testing';
import { BreadcrumbServiceService } from './breadcrumb-service.service';

describe('BreadcrumbServiceService', () => {
  let service: BreadcrumbServiceService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(BreadcrumbServiceService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getBreadCrumList', () => {
    it('should have an initial value of false', (done) => {
      service.breadCrumListSource.subscribe(value => {
        expect(value).toBeFalse();
        done();
      });
    });

    it('should emit true when called with true', (done) => {
      service.getBreadCrumList(true);
      service.breadCrumListSource.subscribe(value => {
        expect(value).toBeTrue();
        done();
      });
    });

    it('should emit false when called with false', (done) => {
      service.getBreadCrumList(false);
      service.breadCrumListSource.subscribe(value => {
        expect(value).toBeFalse();
        done();
      });
    });

    it('should handle non-boolean values by emitting them as-is', (done) => {
      const invalidValue: any = 'invalid';
      service.getBreadCrumList(invalidValue);
      service.breadCrumListSource.subscribe(value => {
        expect(value).toBe(invalidValue);
        done();
      });
    });

    it('should emit undefined if called with undefined', (done) => {
      service.getBreadCrumList(undefined);
      service.breadCrumListSource.subscribe(value => {
        expect(value).toBeUndefined();
        done();
      });
    });

    it('should emit null if called with null', (done) => {
      service.getBreadCrumList(null);
      service.breadCrumListSource.subscribe(value => {
        expect(value).toBeNull();
        done();
      });
    });
  });
});

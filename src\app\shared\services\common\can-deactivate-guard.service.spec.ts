
import { TestBed } from '@angular/core/testing';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { CanComponentDeactivate, CanDeactivateGuard } from './can-deactivate-guard.service';


describe('CanDeactivateGuard', () => {
  let guard: CanDeactivateGuard;
  let mockComponent: CanComponentDeactivate;
  let mockRoute: ActivatedRouteSnapshot;
  let mockState: RouterStateSnapshot;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [CanDeactivateGuard]
    });
    guard = TestBed.inject(CanDeactivateGuard);
    mockRoute = {} as ActivatedRouteSnapshot;
    mockState = {} as RouterStateSnapshot;
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow deactivation if canDeactivate returns true', () => {
    mockComponent = { canDeactivate: () => true };
    expect(guard.canDeactivate(mockComponent, mockRoute, mockState)).toBeTrue();
  });

  it('should allow deactivation if canDeactivate returns false', () => {
    mockComponent = { canDeactivate: () => false };
    expect(guard.canDeactivate(mockComponent, mockRoute, mockState)).toBeFalse();
  });

  it('should allow deactivation if canDeactivate returns an Observable of true', (done) => {
    mockComponent = { canDeactivate: () => of(true) };
    const result = guard.canDeactivate(mockComponent, mockRoute, mockState);
    if (result instanceof Observable) {
      result.subscribe(res => {
        expect(res).toBeTrue();
        done();
      });
    }
  });

  it('should prevent deactivation if canDeactivate returns an Observable of false', (done) => {
    mockComponent = { canDeactivate: () => of(false) };
    const result = guard.canDeactivate(mockComponent, mockRoute, mockState);
    if (result instanceof Observable) {
      result.subscribe(res => {
        expect(res).toBeFalse();
        done();
      });
    }
  });

  it('should allow deactivation if canDeactivate returns a Promise of true', async () => {
    mockComponent = { canDeactivate: () => Promise.resolve(true) };
    await expectAsync(guard.canDeactivate(mockComponent, mockRoute, mockState)).toBeResolvedTo(true);
  });

  it('should prevent deactivation if canDeactivate returns a Promise of false', async () => {
    mockComponent = { canDeactivate: () => Promise.resolve(false) };
    await expectAsync(guard.canDeactivate(mockComponent, mockRoute, mockState)).toBeResolvedTo(false);
  });

  it('should allow deactivation if component does not implement CanComponentDeactivate', () => {
    const result = guard.canDeactivate(null, mockRoute, mockState);
    expect(result).toBeTrue();
  });
});

import { TestBed } from "@angular/core/testing";
import { ActivatedRoute, Router } from "@angular/router";
import { CommonRouteService } from "./common-route.service";
import { ROUTES_CONST } from "@appConstants/routes_constants";

describe("CommonRouteService", () => {
    let service: CommonRouteService;
    let mockRouter = { url: "" };
    // let mockActivatedRoute = { snapshot: { url: "" } };
    let mockActivatedRoute;

    beforeEach(() => {
        mockActivatedRoute = {
            snapshot: {
                queryParams: {}
            }
        };

        TestBed.configureTestingModule({
            providers: [
                CommonRouteService,
                { provide: Router, useValue: mockRouter },
                { provide: ActivatedRoute, useValue: mockActivatedRoute }
            ]
        });
        service = TestBed.inject(CommonRouteService);
    });
    
    it("should be created", () => {
        expect(service).toBeTruthy();
    });

    describe("currentRouter", () => {

        it("should return 'template' when currentActivatedRoute contains 'template'", () => {
            service.currentActivatedRoute = "some/path/template";
            expect(service.currentRouter).toBe("template");
        });

        it("should return 'request' when currentActivatedRoute contains 'request'", () => {
            service.currentActivatedRoute = "some/path/request";
            expect(service.currentRouter).toBe("request");
        });

        it("should return 'offer' when currentActivatedRoute contains 'offer'", () => {
            service.currentActivatedRoute = "some/path/offer";
            expect(service.currentRouter).toBe("offer");
        });

        it("should return 'action-log' when currentActivatedRoute contains 'action-log'", () => {
            service.currentActivatedRoute = "some/path/action-log";
            expect(service.currentRouter).toBe("action-log");
        });

        it("should return 'import-log-bpd' when currentActivatedRoute contains 'import-log-bpd'", () => {
            service.currentActivatedRoute = "some/path/import-log-bpd";
            expect(service.currentRouter).toBe("import-log-bpd");
        });

        it("should return an empty string when currentActivatedRoute does not match any condition", () => {
            service.currentActivatedRoute = "some/path/unknown";
            expect(service.currentRouter).toBe("");
        });
    });

    describe("routerPage", () => {

        it("should return 'OFFER' when router.url contains '/offers/management'", () => {
            mockRouter.url = "/offers/management";
            expect(service.routerPage).toBe("OFFER");
        });

        it("should return undefined when router.url does not contain '/offers/management'", () => {
            mockRouter.url = "/unknown";
            expect(service.routerPage).toBeUndefined();
        });
    });

    describe('isDebugTrueExists', () => {
        it("should return true if route is in debugFlagEnabledRoutes and debug flag is true", () => {
            service.currentActivatedRoute = "admin/batch-import/import-log-bpd";
            mockActivatedRoute.snapshot.queryParams = { debug: "true" }; 
            service.debugFlagEnabledRoutes = ["admin/batch-import/import-log-bpd"];
            expect(service.isDebugTrueExists).toBeTrue();
        });

        it("should return false if route is in debugFlagEnabledRoutes but debug flag is false", () => {
            service.currentActivatedRoute = "admin/batch-import/import-log-bpd";
            mockActivatedRoute.snapshot.queryParams = { debug: "false" }; 
            service.debugFlagEnabledRoutes = ["admin/batch-import/import-log-bpd"];
            expect(service.isDebugTrueExists).toBeFalse();
        });

        it("should return false if route is not in debugFlagEnabledRoutes but debug flag is true", () => {
            service.currentActivatedRoute = "admin/some-other-route";
            mockActivatedRoute.snapshot.queryParams = { debug: "true" }; 
            service.debugFlagEnabledRoutes = ["admin/batch-import/import-log-bpd"];
            expect(service.isDebugTrueExists).toBeFalse();
        });

        it("should return false if route is not in debugFlagEnabledRoutes and debug flag is not present", () => {
            service.currentActivatedRoute = "admin/some-other-route";
            service.debugFlagEnabledRoutes = ["admin/batch-import/import-log-bpd"];
            mockActivatedRoute.snapshot.queryParams = {}; 
            expect(service.isDebugTrueExists).toBeFalse();
        });
    });

    describe('isBpdReqPage', () => {
        it("should return true if currentActivatedRoute contains Request and BPDCreate", () => {
            service.currentActivatedRoute = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.BPDCreate}`;
            expect(service.isBpdReqPage).toBeTrue();
        });

        it("should return true if currentActivateRoute contains Request and BPDEdit", () => {
            service.currentActivatedRoute = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.BPDEdit}`
            expect(service.isBpdReqPage).toBeTrue();
        });
        it("should return true if currentActivateRoute contains Request and BPDSummary", () => {
            service.currentActivatedRoute = `${ROUTES_CONST.REQUEST.Request}/${ROUTES_CONST.REQUEST.BPDSummary}`
            expect(service.isBpdReqPage).toBeTrue();
        });

    });
});

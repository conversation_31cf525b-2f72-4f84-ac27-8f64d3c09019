import { Injectable } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ROUTES_CONST } from "@appConstants/routes_constants";

@Injectable({
  providedIn: "root",
})
export class CommonRouteService {
    currentActivatedRoute: string;
    currentRoute;
    debugFlagEnabledRoutes = [`${ROUTES_CONST.ADMIN.Admin}/${ROUTES_CONST.ADMIN.BatchImport}/${ROUTES_CONST.ADMIN.ImportLogBPD}`,`${ROUTES_CONST.ADMIN.Admin}/${ROUTES_CONST.ADMIN.ActionLog}`];
    constructor(private  router: Router, private acivatedRoute: ActivatedRoute){
      this.currentRoute = this.router.url;
    }
    get currentRouter(){
      const currentActivatedRoute = this.currentActivatedRoute;
      if(currentActivatedRoute?.includes('template')){
       return 'template';
      }else if(currentActivatedRoute?.includes('request')){
        return 'request';
       }else if(currentActivatedRoute?.includes('offer')){
       return 'offer';
       } else if(currentActivatedRoute?.includes('action-log')){
        return 'action-log';
        } else if(currentActivatedRoute?.includes('import-log-bpd')){
        return 'import-log-bpd';
        }
      
     return '';
    }
 get routerPage(){
  if(this.router.url.indexOf(`/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`) !== -1){
    return "OFFER";
  }
 }

 get isDebugTrueExists(){
  const  isValidRoute = this.debugFlagEnabledRoutes?.some((routeVal) => this.currentActivatedRoute?.includes(routeVal)),
  debugFlagTrue = this.acivatedRoute?.snapshot?.queryParams["debug"];
  return  isValidRoute && debugFlagTrue === 'true';
 }

  get isBpdReqPage() {
    if (
      this.currentActivatedRoute?.includes(ROUTES_CONST.REQUEST.Request) &&
      (this.currentActivatedRoute?.includes(ROUTES_CONST.REQUEST.BPDCreate) ||
        this.currentActivatedRoute?.includes(ROUTES_CONST.REQUEST.BPDEdit) ||
        this.currentActivatedRoute?.includes(ROUTES_CONST.REQUEST.BPDSummary))
    ) {
      return true;
    }
  }
}

import { TestBed } from '@angular/core/testing';
import { CommonSearchService } from './common-search.service';
import { CommonRouteService } from './common-route.service';
import { FeatureFlagsService } from './feature-flags.service';
import { FILTER_OPTIONS } from '../../constants/search-options/filterSearch';
import { SORT_OPTIONS } from '../../constants/search-options/sortOptions';
import { CONSTANTS } from '../../constants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import * as moment from 'moment';

describe('CommonSearchService', () => {
  let service: CommonSearchService;
  let commonRouteService: CommonRouteService;
  let featureFlagsService: FeatureFlagsService;

  beforeEach(() => {
    const commonRouteServiceSpy = jasmine.createSpyObj('CommonRouteService', ['currentActivatedRoute']);
    const featureFlagsServiceSpy = jasmine.createSpyObj('FeatureFlagsService', ['isFeatureFlagEnabled']);

    TestBed.configureTestingModule({
      providers: [
        CommonSearchService,
        { provide: CommonRouteService, useValue: commonRouteServiceSpy },
        { provide: FeatureFlagsService, useValue: featureFlagsServiceSpy }
      ]
    });

    service = TestBed.inject(CommonSearchService);
    commonRouteService = TestBed.inject(CommonRouteService) as jasmine.SpyObj<CommonRouteService>;
    featureFlagsService = TestBed.inject(FeatureFlagsService) as jasmine.SpyObj<FeatureFlagsService>;

  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should set and get inputSearchChip', () => {
    const inputSearchChip = 'testChip';
    service.setInputSearchChip(inputSearchChip);
    expect(service.getInputSearchChip()).toBe(inputSearchChip);
  });

  it("should set and get FacetFilterChip", () => {
    const facetFilterChip = 'testChip';
    service.setFacetFilterChip(facetFilterChip);
    expect(service.getFacetFilterChip()).toBe(facetFilterChip);
  })
  
  it('should set isHideExpiredStatusReqs to true when featureFlagIsDisplayExpired is true and input is true', () => {
    spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
    const isHideExpiredStatusReqsSpy = spyOn(service.isHideExpiredStatusReqs$, 'next');

    service.setIsHideExpiredStatusReqs(true);
    expect(isHideExpiredStatusReqsSpy).toHaveBeenCalledWith(true);
  });

  it('should set isHideExpiredStatusReqs to false when featureFlagIsDisplayExpired is true and input is false', () => {
    spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
    const isHideExpiredStatusReqsSpy = spyOn(service.isHideExpiredStatusReqs$, 'next');

    service.setIsHideExpiredStatusReqs(false);
    expect(isHideExpiredStatusReqsSpy).toHaveBeenCalledWith(false);
  });

  it('should set isHideExpiredStatusReqs to false when featureFlagIsDisplayExpired is false and input is true', () => {
    spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(false);
    const isHideExpiredStatusReqsSpy = spyOn(service.isHideExpiredStatusReqs$, 'next');

    service.setIsHideExpiredStatusReqs(true);
    expect(isHideExpiredStatusReqsSpy).toHaveBeenCalledWith(false);
  });

  it('should pad the number with leading zeroes', () => {
    expect(service.pad(123, 5)).toBe('00123');
    expect(service.pad(1, 3)).toBe('001');
    expect(service.pad(12345, 5)).toBe('12345');
    expect(service.pad(123456, 5)).toBe('23456'); // when size is less than the length of num
  });

  it('should return undefined when num is null or undefined', () => {
    expect(service.pad(null, 5)).toBeUndefined();
    expect(service.pad(undefined, 5)).toBeUndefined();
  });


  it('should set query value for default option', () => {
    const field = 'testField';
    const value = 'testValue';
    const defaultOption = {
      testType: [
        { field: 'testField', query: [] },
        { field: 'anotherField', query: [] }
      ]
    };

    spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
    service.defaultOption = defaultOption;

    service.setQueryValueForDefaultOption(field, value);
    expect(service.defaultOption.testType[0].query).toEqual([value]);
    expect(service.defaultOption.testType[1].query).toEqual([]);
  });

  it('should remove query value for default option when value is null', () => {
    const field = 'testField';
    const value = null;
    const defaultOption = {
      testType: [
        { field: 'testField', query: ['testValue'] },
        { field: 'anotherField', query: [] }
      ]
    };

    spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
    service.defaultOption = defaultOption;

    service.setQueryValueForDefaultOption(field, value);
    expect(service.defaultOption.testType[0].query).toEqual([]);
    expect(service.defaultOption.testType[1].query).toEqual([]);
  });


  it('should set filters correctly', () => {
    const key = 'testKey';
    const obj = { key };
    const filterSearch = [{ field: 'filterField', query: [] }];
    const inputSearch = [{ field: 'inputField', query: [] }];
    const defaultSearch = [{ field: 'defaultField', query: [] }];
    const sortOption = [{ field: 'sortField', query: [] }];

    service.filterOption = { [key]: filterSearch };
    service.inputSearchOption = { [key]: inputSearch };
    service.defaultOption = { [key]: defaultSearch };
    service.sortOption = { [key]: sortOption };

    const setFilterOptionSpy = spyOn(service, 'setFilterOption');
    const setDefaultOptionSpy = spyOn(service, 'setDefaultOption');
    const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
    const setSortOptionSpy = spyOn(service, 'setSortOption');

    service.setFilters(obj);

    expect(setFilterOptionSpy).toHaveBeenCalledWith(key, filterSearch);
    expect(setDefaultOptionSpy).toHaveBeenCalledWith(key, defaultSearch);
    expect(setInputSearchOptionSpy).toHaveBeenCalledWith(key, inputSearch);
    expect(setSortOptionSpy).toHaveBeenCalledWith(key, sortOption);
  });

  it('should handle undefined filterOption, inputSearchOption, defaultOption, and sortOption', () => {
    const key = 'testKey';
    const obj = { key };

    service.filterOption = undefined;
    service.inputSearchOption = undefined;
    service.defaultOption = undefined;
    service.sortOption = undefined;

    const setFilterOptionSpy = spyOn(service, 'setFilterOption');
    const setDefaultOptionSpy = spyOn(service, 'setDefaultOption');
    const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
    const setSortOptionSpy = spyOn(service, 'setSortOption');

    service.setFilters(obj);

    expect(setFilterOptionSpy).toHaveBeenCalledWith(key, undefined);
    expect(setDefaultOptionSpy).toHaveBeenCalledWith(key, undefined);
    expect(setInputSearchOptionSpy).toHaveBeenCalledWith(key, undefined);
    expect(setSortOptionSpy).toHaveBeenCalledWith(key, undefined);
  });

  
  it('should set default option when currentSearchType exists', () => {
    const currentSearchType = 'testType';
    const options = [{ field: 'defaultField', query: [] }];
    service.defaultOption = { [currentSearchType]: [] };

    service.setDefaultOption(currentSearchType, options);
    expect(service.defaultOption[currentSearchType]).toEqual(options);
  });

  it('should not set default option when currentSearchType does not exist', () => {
    const currentSearchType = 'testType';
    const options = [{ field: 'defaultField', query: [] }];
    service.defaultOption = {};

    service.setDefaultOption(currentSearchType, options);
    expect(service.defaultOption[currentSearchType]).toBeUndefined();
  });

  it('should set filter option when currentSearchType exists', () => {
    const currentSearchType = 'testType';
    const options = [{ field: 'filterField', query: [] }];
    service.filterOption = { [currentSearchType]: [] };

    service.setFilterOption(currentSearchType, options);
    expect(service.filterOption[currentSearchType]).toEqual(options);
  });

  it('should not set filter option when currentSearchType does not exist', () => {
    const currentSearchType = 'testType';
    const options = [{ field: 'filterField', query: [] }];
    service.filterOption = {};

    service.setFilterOption(currentSearchType, options);
    expect(service.filterOption[currentSearchType]).toBeUndefined();
  });

  it('should set input search option when key exists', () => {
    const key = 'testKey';
    const options = [{ field: 'inputField', query: [] }];
    service.inputSearchOption = { [key]: [] };

    service.setInputSearchOption(key, options);
    expect(service.inputSearchOption[key]).toEqual(options);
  });

  it('should not set input search option when key does not exist', () => {
    const key = 'testKey';
    const options = [{ field: 'inputField', query: [] }];
    service.inputSearchOption = {};

    service.setInputSearchOption(key, options);
    expect(service.inputSearchOption[key]).toBeUndefined();
  });

  it('should set sort option when key exists', () => {
    const key = 'testKey';
    const options = [{ field: 'sortField', query: [] }];
    service.sortOption = { [key]: [] };

    service.setSortOption(key, options);
    expect(service.sortOption[key]).toEqual(options);
  });

  it('should not set sort option when key does not exist', () => {
    const key = 'testKey';
    const options = [{ field: 'sortField', query: [] }];
    service.sortOption = {};

    service.setSortOption(key, options);
    expect(service.sortOption[key]).toBeUndefined();
  });

  it('should set sort option using setSort', () => {
    const key = 'testKey';
    const obj = { key };
    const sortOption = [{ field: 'sortField', query: [] }];
    service.sortOption = { [key]: sortOption };

    const setSortOptionSpy = spyOn(service, 'setSortOption');

    service.setSort(obj);
    expect(setSortOptionSpy).toHaveBeenCalledWith(key, sortOption);
  });

  it('should handle undefined sort option using setSort', () => {
    const key = 'testKey';
    const obj = { key };
    service.sortOption = undefined;

    const setSortOptionSpy = spyOn(service, 'setSortOption');

    service.setSort(obj);
    expect(setSortOptionSpy).toHaveBeenCalledWith(key, undefined);
  });

  it('should set all filter options correctly', () => {
    const key = 'testKey';
    const currentRouter = 'testRouter';
    const obj = { key, currentRouter };

    const fetchInputSearchOptionsSpy = spyOn(service, 'fetchInputSearchOptions');
    const setFilterOptionsSpy = spyOn(service, 'setFilterOptions');
    const fetchDefaultOptionsSpy = spyOn(service, 'fetchDefaultOptions');
    const setSortOptionsSpy = spyOn(service, 'setSortOptions');

    service.setAllFilterOptions(obj);

    expect(fetchInputSearchOptionsSpy).toHaveBeenCalledWith({ key, currentRouter });
    expect(setFilterOptionsSpy).toHaveBeenCalledWith(obj);
    expect(fetchDefaultOptionsSpy).toHaveBeenCalledWith({ key, currentRouter });
    expect(setSortOptionsSpy).toHaveBeenCalledWith(obj);
  });

  it('should reset filters correctly', () => {
    const currentRouter = 'testRouter';
    const pcSelected = 'testPC';
    const defaultTemplateFilters = 'testTemplateFilters';
    const resetChips = true;
    const obj = { currentRouter, pcSelected, defaultTemplateFilters, resetChips };

    service.inputSearchOption = { [pcSelected]: ['inputSearchOption'] };
    service.filterOption = { [pcSelected]: ['filterOption'] };
    service.defaultOption = { [pcSelected]: ['defaultOption'] };
    service.sortOption = { [pcSelected]: ['sortOption'] };
    FILTER_OPTIONS[currentRouter] = { [pcSelected]: ['filterOption'] };
    SORT_OPTIONS[currentRouter] = { [pcSelected]: ['sortOption'] };

    const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption').and.callThrough();
    const setFilterOptionSpy = spyOn(service, 'setFilterOption').and.callThrough();
    const setDefaultOptionSpy = spyOn(service, 'setDefaultOption').and.callThrough();
    const setSortOptionSpy = spyOn(service, 'setSortOption').and.callThrough();
    const setFacetFilterChipSpy = spyOn(service, 'setFacetFilterChip').and.callThrough();
    const setInputSearchChipSpy = spyOn(service, 'setInputSearchChip').and.callThrough();

    service.resetFilters(obj);

    expect(setInputSearchOptionSpy).toHaveBeenCalledWith(pcSelected, null);
    expect(setFilterOptionSpy).toHaveBeenCalledWith(pcSelected, null);
    expect(setDefaultOptionSpy).toHaveBeenCalledWith(pcSelected, null);
    expect(setSortOptionSpy).toHaveBeenCalledWith(pcSelected, null);
    expect(FILTER_OPTIONS[currentRouter][pcSelected]).toEqual([]);
    expect(SORT_OPTIONS[currentRouter][pcSelected]).toEqual([]);
    expect(service.filterOption_persist).toBeNull();
    expect(service.inputSearchOption_persist).toBeNull();
    expect(setFacetFilterChipSpy).toHaveBeenCalledWith(null);
    expect(setInputSearchChipSpy).toHaveBeenCalledWith(null);
  });

  describe('setFiltersForPersisted', () => {
    it('should set inputSearchOption, filterOption, defaultOption, and sortOption when flag is true', () => {
      const pcSelected = 'testPC';
      const flag = true;
      const obj = { flag, pcSelected };
      const currentRouter = 'testRouter';
      spyOnProperty(service, 'currentRouter', 'get').and.returnValue(currentRouter);

      const inputSearch = [{ field: 'inputField', query: [] }];
      const filterSearch = [{ field: 'filterField', query: [] }];
      const defaultSearch = [{ field: 'defaultField', query: [] }];
      const sortOptions = [{ field: 'sortField', query: [] }];

      service.inputSearchOption = { [pcSelected]: inputSearch };
      service.filterOption = { [pcSelected]: filterSearch };
      service.defaultOption = { [pcSelected]: defaultSearch };
      service.sortOption = { [pcSelected]: sortOptions };

      const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
      const setFilterOptionSpy = spyOn(service, 'setFilterOption');
      const setDefaultOptionSpy = spyOn(service, 'setDefaultOption');
      const setSortOptionSpy = spyOn(service, 'setSortOption');

      service.setFiltersForPersisted(obj);

      expect(setInputSearchOptionSpy).toHaveBeenCalledWith(pcSelected, inputSearch);
      expect(setFilterOptionSpy).toHaveBeenCalledWith(pcSelected, filterSearch);
      expect(setDefaultOptionSpy).toHaveBeenCalledWith(pcSelected, defaultSearch);
      expect(setSortOptionSpy).toHaveBeenCalledWith(pcSelected, sortOptions);
    });

    it('should fetch inputSearchOption, filterOption, defaultOption, and sortOption when flag is false', () => {
      const pcSelected = 'testPC';
      const flag = false;
      const obj = { flag, pcSelected };
      const currentRouter = 'testRouter';
      spyOnProperty(service, 'currentRouter', 'get').and.returnValue(currentRouter);

      const fetchInputSearchOptionsSpy = spyOn(service, 'fetchInputSearchOptions');
      const setFilterOptionsSpy = spyOn(service, 'setFilterOptions');
      const fetchDefaultOptionsSpy = spyOn(service, 'fetchDefaultOptions');
      const setSortOptionsSpy = spyOn(service, 'setSortOptions');

      service.setFiltersForPersisted(obj);

      expect(fetchInputSearchOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
      expect(setFilterOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
      expect(fetchDefaultOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
      expect(setSortOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
    });

    it('should handle undefined inputSearchOption, filterOption, defaultOption, and sortOption when flag is true', () => {
      const pcSelected = 'testPC';
      const flag = true;
      const obj = { flag, pcSelected };
      const currentRouter = 'testRouter';
      spyOnProperty(service, 'currentRouter', 'get').and.returnValue(currentRouter);

      service.inputSearchOption = {};
      service.filterOption = {};
      service.defaultOption = {};
      service.sortOption = {};

      const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
      const setFilterOptionSpy = spyOn(service, 'setFilterOption');
      const setDefaultOptionSpy = spyOn(service, 'setDefaultOption');
      const setSortOptionSpy = spyOn(service, 'setSortOption');

      service.setFiltersForPersisted(obj);

      expect(setInputSearchOptionSpy).not.toHaveBeenCalled();
      expect(setFilterOptionSpy).not.toHaveBeenCalled();
      expect(setDefaultOptionSpy).not.toHaveBeenCalled();
      expect(setSortOptionSpy).not.toHaveBeenCalled();
    });

    it('should handle undefined inputSearchOption, filterOption, defaultOption, and sortOption when flag is false', () => {
      const pcSelected = 'testPC';
      const flag = false;
      const obj = { flag, pcSelected };
      const currentRouter = 'testRouter';
      spyOnProperty(service, 'currentRouter', 'get').and.returnValue(currentRouter);

      service.inputSearchOption = {};
      service.filterOption = {};
      service.defaultOption = {};
      service.sortOption = {};

      const fetchInputSearchOptionsSpy = spyOn(service, 'fetchInputSearchOptions');
      const setFilterOptionsSpy = spyOn(service, 'setFilterOptions');
      const fetchDefaultOptionsSpy = spyOn(service, 'fetchDefaultOptions');
      const setSortOptionsSpy = spyOn(service, 'setSortOptions');

      service.setFiltersForPersisted(obj);

      expect(fetchInputSearchOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
      expect(setFilterOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
      expect(fetchDefaultOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
      expect(setSortOptionsSpy).toHaveBeenCalledWith({ key: pcSelected, currentRouter });
    });
  });
  
  it('should return "template" when currentActivatedRoute includes "template"', () => {
    commonRouteService.currentActivatedRoute = 'some/path/template';
    expect(service.currentRouter).toBe('template');
  });

  it('should return "request" when currentActivatedRoute includes "request"', () => {
    commonRouteService.currentActivatedRoute = 'some/path/request';
    expect(service.currentRouter).toBe('request');
  });

  it('should return "offer" when currentActivatedRoute includes "offer"', () => {
    commonRouteService.currentActivatedRoute = 'some/path/offer';
    expect(service.currentRouter).toBe('offer');
  });

  it('should return empty string when currentActivatedRoute includes product details page', () => {
    commonRouteService.currentActivatedRoute = `${CONSTANTS.PRODUCTMANAGEMENT}/${ROUTES_CONST.GROUPS.ProductDetails}`;
    expect(service.currentRouter).toBe('');
  });

  it('should set and get activeCurrentSearchType', () => {
    const activeCurrentSearchType = 'testType';
    service.setActiveCurrentSearchType(activeCurrentSearchType);
    expect(service.getActiveCurrentSearchType()).toBe(activeCurrentSearchType);
  });

  it('should return undefined if activeCurrentSearchType is not set', () => {
    expect(service.getActiveCurrentSearchType()).toBeUndefined();
  });

  it('should override the existing activeCurrentSearchType', () => {
    const initialType = 'initialType';
    const newType = 'newType';
    service.setActiveCurrentSearchType(initialType);
    expect(service.getActiveCurrentSearchType()).toBe(initialType);
    service.setActiveCurrentSearchType(newType);
    expect(service.getActiveCurrentSearchType()).toBe(newType);
  });

  it('should set query and showChip to empty arrays for elements not having field "programCode"', () => {
    const searchOption = [
      { field: 'createdAppId', query: ['OMS'], showChip: ['chip1'] },
      { field: 'sortBy', query: ['lastUpdateTimestampDESC'], showChip: ['chip2'] }
    ];

    service.setEmptyQueryFields(searchOption);

    expect(searchOption[0].query).toEqual([]);
    expect(searchOption[0].showChip).toEqual([]);
    expect(searchOption[1].query).toEqual([]);
    expect(searchOption[1].showChip).toEqual([]);
  });

  it('should not change query and showChip for elements having field "programCode"', () => {
    const searchOption = [
      { field: 'programCode', query: ['code1'], showChip: ['chip1'] },
      { field: 'createdAppId', query: ['OMS'], showChip: ['chip2'] }
    ];

    service.setEmptyQueryFields(searchOption);

    expect(searchOption[0].query).toEqual(['code1']);
    expect(searchOption[0].showChip).toEqual(['chip1']);
    expect(searchOption[1].query).toEqual([]);
    expect(searchOption[1].showChip).toEqual([]);
  });


  it('should return "template" when currentActivatedRoute includes "template"', () => {
    commonRouteService.currentActivatedRoute = 'some/path/template';
    expect(service.currentRouter).toBe('template');
  });

  it('should return "request" when currentActivatedRoute includes "request"', () => {
    commonRouteService.currentActivatedRoute = 'some/path/request';
    expect(service.currentRouter).toBe('request');
  });

  it('should return "offer" when currentActivatedRoute includes "offer"', () => {
    commonRouteService.currentActivatedRoute = 'some/path/offer';
    expect(service.currentRouter).toBe('offer');
  });

  it('should return empty string when currentActivatedRoute includes product details page', () => {
    commonRouteService.currentActivatedRoute = `${CONSTANTS.PRODUCTMANAGEMENT}/${ROUTES_CONST.GROUPS.ProductDetails}`;
    expect(service.currentRouter).toBe('');
  });

  it('should return CONSTANTS.PRODUCTMANAGEMENT when currentActivatedRoute includes product management', () => {
    commonRouteService.currentActivatedRoute = CONSTANTS.PRODUCTMANAGEMENT;
    expect(service.currentRouter).toBe(CONSTANTS.PRODUCTMANAGEMENT);
  });

  it('should return CONSTANTS.ACTION_LOG when currentActivatedRoute includes action log', () => {
    commonRouteService.currentActivatedRoute = `${ROUTES_CONST.ADMIN.Admin}/${ROUTES_CONST.ADMIN.ActionLog}`;
    expect(service.currentRouter).toBe(CONSTANTS.ACTION_LOG);
  });

  it('should return CONSTANTS.IMPORT_LOG_BPD when currentActivatedRoute includes import log BPD', () => {
    commonRouteService.currentActivatedRoute = `${ROUTES_CONST.ADMIN.ImportLogBPD}`;
    expect(service.currentRouter).toBe(CONSTANTS.IMPORT_LOG_BPD);
  });

  it('should return empty string when currentActivatedRoute includes store details page', () => {
    commonRouteService.currentActivatedRoute = `${CONSTANTS.STOREMANAGEMENT}/${ROUTES_CONST.GROUPS.StoreDetails}`;
    expect(service.currentRouter).toBe('');
  });

  it('should return CONSTANTS.STOREMANAGEMENT when currentActivatedRoute includes store management', () => {
    commonRouteService.currentActivatedRoute = CONSTANTS.STOREMANAGEMENT;
    expect(service.currentRouter).toBe(CONSTANTS.STOREMANAGEMENT);
  });

  it('should return empty string when currentActivatedRoute does not match any conditions', () => {
    commonRouteService.currentActivatedRoute = 'some/other/path';
    expect(service.currentRouter).toBe('');
  });
  describe('removeQueryAndChipFromOptions', () => {
    it('should remove query and chip from inputOptions and filterOptions', () => {
      const programCode = 'testProgramCode';
      const chip = { chip: 'createdAppId', isClearAll_LeftFilter: false };
      const currentRouter = 'testRouter';
      const obj = { programCode, chip, currentRouter };

      const inputOptions = [{ field: 'createdAppId', query: ['OMS'], showChip: [] }];
      const filterOptions = [{ field: 'createdAppId', query: ['OMS'], showChip: [] }];

      spyOn(service, 'getFilterOption').and.returnValue(filterOptions);
      spyOn(service, 'getInputSearchOption').and.returnValue(inputOptions);
      const emptyElementSpy = spyOn(service, 'emptyElement');
      const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
      const setFilterOptionSpy = spyOn(service, 'setFilterOption');

      service.removeQueryAndChipFromOptions(obj);

      expect(emptyElementSpy).toHaveBeenCalledWith(inputOptions, inputOptions);
      expect(emptyElementSpy).toHaveBeenCalledWith(filterOptions);
      expect(setInputSearchOptionSpy).toHaveBeenCalledWith(programCode, inputOptions);
      expect(setFilterOptionSpy).toHaveBeenCalledWith(programCode, filterOptions);
    });

    it('should not clear search input chips if clear all left filters is clicked', () => {
      const programCode = 'testProgramCode';
      const chip = { chip: 'createdAppId', isClearAll_LeftFilter: true };
      const currentRouter = 'testRouter';
      const obj = { programCode, chip, currentRouter };

      const inputOptions = [{ field: 'createdAppId', query: ['OMS'], showChip: [] }];
      const filterOptions = [{ field: 'createdAppId', query: ['OMS'], showChip: [] }];

      spyOn(service, 'getFilterOption').and.returnValue(filterOptions);
      spyOn(service, 'getInputSearchOption').and.returnValue(inputOptions);
      const emptyElementSpy = spyOn(service, 'emptyElement');
      const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
      const setFilterOptionSpy = spyOn(service, 'setFilterOption');

      service.removeQueryAndChipFromOptions(obj);

      expect(emptyElementSpy).not.toHaveBeenCalledWith(inputOptions, inputOptions);
      expect(emptyElementSpy).toHaveBeenCalledWith(filterOptions);
      expect(setInputSearchOptionSpy).toHaveBeenCalledWith(programCode, inputOptions);
      expect(setFilterOptionSpy).toHaveBeenCalledWith(programCode, filterOptions);
    });

    it('should handle case when programCode is not provided', () => {
      const chip = { chip: 'createdAppId', isClearAll_LeftFilter: false };
      const currentRouter = 'testRouter';
      const obj = { programCode: null, chip, currentRouter };

      const inputOptions = [{ field: 'createdAppId', query: ['OMS'], showChip: [] }];
      const filterOptions = [{ field: 'createdAppId', query: ['OMS'], showChip: [] }];

      spyOn(service, 'getFilterOption').and.returnValue(filterOptions);
      spyOn(service, 'getInputSearchOption').and.returnValue(inputOptions);
      const emptyElementSpy = spyOn(service, 'emptyElement');
      const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
      const setFilterOptionSpy = spyOn(service, 'setFilterOption');

      service.removeQueryAndChipFromOptions(obj);

      expect(emptyElementSpy).toHaveBeenCalledWith(inputOptions, inputOptions);
      expect(emptyElementSpy).toHaveBeenCalledWith(filterOptions);
      expect(setInputSearchOptionSpy).toHaveBeenCalledWith(currentRouter, inputOptions);
      expect(setFilterOptionSpy).not.toHaveBeenCalled();
    });

    it('should call emptyFilterList for specific chips', () => {
      const programCode = 'testProgramCode';
      const chip = { chip: 'bggm', isClearAll_LeftFilter: false };
      const currentRouter = 'testRouter';
      const obj = { programCode, chip, currentRouter };

      const inputOptions = [{ field: 'bggm', query: ['OMS'], showChip: [] }];
      const filterOptions = [{ field: 'bggm', query: ['OMS'], showChip: [] }];

      spyOn(service, 'getFilterOption').and.returnValue(filterOptions);
      spyOn(service, 'getInputSearchOption').and.returnValue(inputOptions);
      const emptyElementSpy = spyOn(service, 'emptyElement');
      const emptyFilterListSpy = spyOn(service, 'emptyFilterList');
      const setInputSearchOptionSpy = spyOn(service, 'setInputSearchOption');
      const setFilterOptionSpy = spyOn(service, 'setFilterOption');

      service.removeQueryAndChipFromOptions(obj);

      expect(emptyElementSpy).toHaveBeenCalledWith(inputOptions, inputOptions);
      expect(emptyElementSpy).toHaveBeenCalledWith(filterOptions);
      expect(emptyFilterListSpy).toHaveBeenCalledWith(filterOptions, ['bugm', 'categoryId']);
      expect(setInputSearchOptionSpy).toHaveBeenCalledWith(programCode, inputOptions);
      expect(setFilterOptionSpy).toHaveBeenCalledWith(programCode, filterOptions);
    });
  });

  it('should call emptyElement for each filtered element', () => {
    const filterOptions = [
      { field: 'createdAppId', query: ['OMS'], showChip: ['chip1'] },
      { field: 'sortBy', query: ['lastUpdateTimestampDESC'], showChip: ['chip2'] },
      { field: 'categoryId', query: ['123'], showChip: ['chip3'] }
    ];
    const elements = ['createdAppId', 'categoryId'];

    const emptyElementSpy = spyOn(service, 'emptyElement');

    service.emptyFilterList(filterOptions, elements);

    expect(emptyElementSpy).toHaveBeenCalledWith([{ field: 'createdAppId', query: ['OMS'], showChip: ['chip1'] }]);
    expect(emptyElementSpy).toHaveBeenCalledWith([{ field: 'categoryId', query: ['123'], showChip: ['chip3'] }]);
    expect(emptyElementSpy).not.toHaveBeenCalledWith([{ field: 'sortBy', query: ['lastUpdateTimestampDESC'], showChip: ['chip2'] }]);
  });

  it('should handle empty filterOptions array', () => {
    const filterOptions = [];
    const elements = ['createdAppId', 'categoryId'];

    const emptyElementSpy = spyOn(service, 'emptyElement');

    service.emptyFilterList(filterOptions, elements);

    expect(emptyElementSpy).not.toHaveBeenCalled();
  });

  it('should set query and showChip to empty arrays for the element', () => {
    const elementArr = [{ field: 'createdAppId', query: ['OMS'], showChip: ['chip1'], elements: [] }];
    service.emptyElement(elementArr);
    expect(elementArr[0].query).toEqual([]);
    expect(elementArr[0].showChip).toEqual([]);
  });

  it('should set query to empty arrays for elements within the element', () => {
    const elementArr = [{
      field: 'createdAppId',
      query: ['OMS'],
      showChip: ['chip1'],
      elements: [{ field: 'subField', query: ['subQuery'] }]
    }];
    service.emptyElement(elementArr);
    expect(elementArr[0].elements[0].query).toEqual([]);
  });

  it('should populate filter options correctly when elements are selected', () => {
    const programCode = 'testProgramCode';
    const item = 'createdAppId';
    const form = {
      createdAppId: [
        { id: '1', value: 'Value1', selected: true },
        { id: '2', value: 'Value2', selected: false },
        { id: '3', value: 'Value3', selected: true }
      ]
    };
    const filterOptions = [
      { field: 'createdAppId', query: [], showChip: [] }
    ];

    spyOn(service, 'getFilterOption').and.returnValue(filterOptions);

    service.populateFilterOption(programCode, { form, item });

    expect(filterOptions[0].query).toEqual(['1', '3']);
    expect(filterOptions[0].showChip).toEqual(['Value1', 'Value3']);
  });

  it('should not update filter options when no elements are selected', () => {
    const programCode = 'testProgramCode';
    const item = 'createdAppId';
    const form = {
      createdAppId: [
        { id: '1', value: 'Value1', selected: false },
        { id: '2', value: 'Value2', selected: false }
      ]
    };
    const filterOptions = [
      { field: 'createdAppId', query: [], showChip: [] }
    ];

    spyOn(service, 'getFilterOption').and.returnValue(filterOptions);

    service.populateFilterOption(programCode, { form, item });

    expect(filterOptions[0].query).toEqual([]);
    expect(filterOptions[0].showChip).toEqual([]);
  })
 

  it('should return false when getInputSearchChip returns null', () => {
    spyOn(service, 'getInputSearchChip').and.returnValue(null);
    expect(service.isAnyBpdSearchSelected).toBe(false);
  });

  it('should return true when getInputSearchChip returns an empty object', () => {
    spyOn(service, 'getInputSearchChip').and.returnValue({});
    expect(service.isAnyBpdSearchSelected).toBe(true);
  });

  it('should return false when getInputSearchChip returns a non-empty object', () => {
    spyOn(service, 'getInputSearchChip').and.returnValue({ someKey: 'someValue' });
    expect(service.isAnyBpdSearchSelected).toBe(false);
  });


  it('should return false when getFacetFilterChip returns null', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue(null);
    expect(service.isStatusFilterSelectedForBPD).toBe(false);
  });

  it('should return false when getFacetFilterChip returns an empty object', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({});
    expect(service.isStatusFilterSelectedForBPD).toBe(false);
  });

  it('should return true when getFacetFilterChip returns an object with status key', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({ status: 'someStatus' });
    expect(service.isStatusFilterSelectedForBPD).toBe(true);
  });

  it('should return false when getFacetFilterChip returns an object without status key', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({ someKey: 'someValue' });
    expect(service.isStatusFilterSelectedForBPD).toBe(false);
  });


  it('should return false when getFacetFilterChip returns an empty object', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({});
    expect(service.isExpiredStatusFilterSelectedForBPD).toBe(false);
  });

  it('should return true when getFacetFilterChip returns an object with status key set to "Expired"', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({ status: 'Expired' });
    expect(service.isExpiredStatusFilterSelectedForBPD).toBe(true);
  });

  it('should return false when getFacetFilterChip returns an object with status key not set to "Expired"', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({ status: 'Active' });
    expect(service.isExpiredStatusFilterSelectedForBPD).toBe(false);
  });

  it('should return false when getFacetFilterChip returns an object without status key', () => {
    spyOn(service, 'getFacetFilterChip').and.returnValue({ someKey: 'someValue' });
    expect(service.isExpiredStatusFilterSelectedForBPD).toBe(false);
  });

  describe('setQueryOptionsForBpd', () => {
    it('should return false if featureFlagIsDisplayExpired is false', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(false);
      const result = service.setQueryOptionsForBpd({ isHideExpiredStatusReqs: true });
      expect(result).toBe(false);
    });

    it('should set query options correctly when isHideExpiredStatusReqs is true and sendExpiredFlag is true', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
      spyOnProperty(service, 'isAnyBpdSearchSelected', 'get').and.returnValue(true);
      spyOnProperty(service, 'isAnyBpdFilterSelected', 'get').and.returnValue(true);
      spyOnProperty(service, 'isStatusFilterSelectedForBPD', 'get').and.returnValue(false);

      const removeMultiplesFromDefaultOptionsSpy = spyOn(service, 'removeMultiplesFromDefaultOptions');
      const setQueryValForDefaultOptionSpy = spyOn(service, 'setQueryValForDefaultOption');
      const setEndDtForExpiredStatusSpy = spyOn(service, 'setEndDtForExpiredStatus').and.returnValue('endDate');

      service.setQueryOptionsForBpd({ isHideExpiredStatusReqs: true });

      expect(removeMultiplesFromDefaultOptionsSpy).toHaveBeenCalledWith([
        CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS,
        CONSTANTS.CURRENT_DATE_QUERY_OR,
        CONSTANTS.END_DATE_QUERY_OR
      ]);
      expect(setQueryValForDefaultOptionSpy).toHaveBeenCalledWith(CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS, false);
      expect(setQueryValForDefaultOptionSpy).toHaveBeenCalledWith(CONSTANTS.CURRENT_DATE_QUERY_OR, 'endDate');
      expect(setEndDtForExpiredStatusSpy).toHaveBeenCalled();
    });

    it('should set query options correctly when isHideExpiredStatusReqs is false', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
      spyOnProperty(service, 'isAnyBpdSearchSelected', 'get').and.returnValue(false);
      spyOnProperty(service, 'isAnyBpdFilterSelected', 'get').and.returnValue(false);
      spyOnProperty(service, 'isStatusFilterSelectedForBPD', 'get').and.returnValue(false);

      const removeMultiplesFromDefaultOptionsSpy = spyOn(service, 'removeMultiplesFromDefaultOptions');
      const setQueryValForDefaultOptionSpy = spyOn(service, 'setQueryValForDefaultOption');
      const setEndDtForExpiredStatusSpy = spyOn(service, 'setEndDtForExpiredStatus').and.returnValue('endDate');

      service.setQueryOptionsForBpd({ isHideExpiredStatusReqs: false });

      expect(removeMultiplesFromDefaultOptionsSpy).toHaveBeenCalledWith([
        CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS,
        CONSTANTS.CURRENT_DATE_QUERY_OR,
        CONSTANTS.END_DATE_QUERY_OR
      ]);
      expect(setQueryValForDefaultOptionSpy).toHaveBeenCalledWith(CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS, null);
      expect(setQueryValForDefaultOptionSpy).toHaveBeenCalledWith(CONSTANTS.END_DATE_QUERY_OR, 'endDate');
      expect(setEndDtForExpiredStatusSpy).toHaveBeenCalled();
    });

    it('should set isDisplayExpiredReqs to null when sendExpiredFlag is false and isHideExpiredStatusReqs is true', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
      spyOnProperty(service, 'isAnyBpdSearchSelected', 'get').and.returnValue(false);
      spyOnProperty(service, 'isAnyBpdFilterSelected', 'get').and.returnValue(false);
      spyOnProperty(service, 'isStatusFilterSelectedForBPD', 'get').and.returnValue(false);

      const removeMultiplesFromDefaultOptionsSpy = spyOn(service, 'removeMultiplesFromDefaultOptions');
      const setQueryValForDefaultOptionSpy = spyOn(service, 'setQueryValForDefaultOption');
      const setEndDtForExpiredStatusSpy = spyOn(service, 'setEndDtForExpiredStatus').and.returnValue('endDate');

      service.setQueryOptionsForBpd({ isHideExpiredStatusReqs: true });

      expect(removeMultiplesFromDefaultOptionsSpy).toHaveBeenCalledWith([
        CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS,
        CONSTANTS.CURRENT_DATE_QUERY_OR,
        CONSTANTS.END_DATE_QUERY_OR
      ]);
      expect(setQueryValForDefaultOptionSpy).toHaveBeenCalledWith(CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS, null);
      expect(setQueryValForDefaultOptionSpy).toHaveBeenCalledWith(CONSTANTS.CURRENT_DATE_QUERY_OR, 'endDate');
      expect(setEndDtForExpiredStatusSpy).toHaveBeenCalled();
    });
  })

  describe('resetAllFilterOptions', () => {
    it('should call setAllFilterOptions when current is provided', () => {
      const obj = { current: 'someCurrent', key: 'someKey' };
      const setAllFilterOptionsSpy = spyOn(service, 'setAllFilterOptions');
      spyOnProperty(service, 'currentRouter', 'get').and.returnValue('someRouter');

      service.resetAllFilterOptions(obj);

      expect(setAllFilterOptionsSpy).toHaveBeenCalledWith({ key: 'someKey', currentRouter: 'someRouter' });
    });

    it('should reset options when current is not provided', () => {
      const obj = { key: 'someKey' };

      service.inputSearchOption = { someKey: 'someValue' };
      service.filterOption = { someKey: 'someValue' };
      service.defaultOption = { someKey: 'someValue' };
      service.sortOption = { someKey: 'someValue' };

      const setInputSearchChipSpy = spyOn(service, 'setInputSearchChip');
      const setFacetFilterChipSpy = spyOn(service, 'setFacetFilterChip');

      service.resetAllFilterOptions(obj);

      expect(service.inputSearchOption).toEqual({});
      expect(service.filterOption).toEqual({});
      expect(service.defaultOption).toEqual({});
      expect(service.sortOption).toEqual({});
      expect(setInputSearchChipSpy).toHaveBeenCalledWith('');
      expect(setFacetFilterChipSpy).toHaveBeenCalledWith('');
    });
  });


  describe('createInputSearchOptions', () => {
    it('should create input search options with correct structure and values', () => {
      const searchOption = 'someSearchOption';
      const value = 'someValue';
      const result = service.createInputSearchOptions(searchOption, value);

      expect(result).toEqual({
        label: "End Date",
        field: "effectiveEndDate",
        hide: true,
        query: [],
        showChip: [],
        elements: [{
          type: "select",
          field: "effectiveEndDate",
          resetQuery: true,
          query: [],
          options: [{
            label: "Today",
            field: "effectiveEndDate",
            defaultSelect: true,
            elements: [{
              field: "From",
              placeholder: "From",
              hide: true,
              type: "date",
              query: [],
              value: `${moment().startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
            }, {
              field: "To",
              placeholder: "To",
              type: "date",
              hide: true,
              query: [],
              value: `*`
            }]
          }]
        }],
        searchButton: true,
        defaultSelect: false,
        resetQuery: true,
        queryWithOrFilters: []
      });
    });

    it('should create input search options with dynamic values', () => {
      const searchOption = 'anotherSearchOption';
      const value = 'anotherValue';
      const result = service.createInputSearchOptions(searchOption, value);

      expect(result.label).toBe("End Date");
      expect(result.field).toBe("effectiveEndDate");
      expect(result.hide).toBe(true);
      expect(result.query).toEqual([]);
      expect(result.showChip).toEqual([]);
      expect(result.elements[0].type).toBe("select");
      expect(result.elements[0].field).toBe("effectiveEndDate");
      expect(result.elements[0].resetQuery).toBe(true);
      expect(result.elements[0].query).toEqual([]);
      expect(result.elements[0].options[0].label).toBe("Today");
      expect(result.elements[0].options[0].field).toBe("effectiveEndDate");
      expect(result.elements[0].options[0].defaultSelect).toBe(true);
      expect(result.elements[0].options[0].elements[0].field).toBe("From");
      expect(result.elements[0].options[0].elements[0].placeholder).toBe("From");
      expect(result.elements[0].options[0].elements[0].hide).toBe(true);
      expect(result.elements[0].options[0].elements[0].type).toBe("date");
      expect(result.elements[0].options[0].elements[0].query).toEqual([]);
      expect(result.elements[0].options[0].elements[0].value).toBe(`${moment().startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`);
      expect(result.elements[0].options[0].elements[1].field).toBe("To");
      expect(result.elements[0].options[0].elements[1].placeholder).toBe("To");
      expect(result.elements[0].options[0].elements[1].type).toBe("date");
      expect(result.elements[0].options[0].elements[1].hide).toBe(true);
      expect(result.elements[0].options[0].elements[1].query).toEqual([]);
      expect(result.elements[0].options[0].elements[1].value).toBe(`*`);
      expect(result.searchButton).toBe(true);
      expect(result.defaultSelect).toBe(false);
      expect(result.resetQuery).toBe(true);
      expect(result.queryWithOrFilters).toEqual([]);
    });
  });


  describe('storeSearchSelections', () => {
    it('should store deep copies of filterOption, inputSearchOption, and sortOption', () => {
      service.filterOption = { key1: 'value1' };
      service.inputSearchOption = { key2: 'value2' };
      service.sortOption = { key3: 'value3' };

      service.storeSearchSelections();

      expect(service.filterOption_persist).toEqual({ key1: 'value1' });
      expect(service.inputSearchOption_persist).toEqual({ key2: 'value2' });
      expect(service.sortOption_persist).toEqual({ key3: 'value3' });

      // Ensure deep copy
      expect(service.filterOption_persist).not.toBe(service.filterOption);
      expect(service.inputSearchOption_persist).not.toBe(service.inputSearchOption);
      expect(service.sortOption_persist).not.toBe(service.sortOption);
    });

    it('should handle empty options', () => {
      service.filterOption = {};
      service.inputSearchOption = {};
      service.sortOption = {};

      service.storeSearchSelections();

      expect(service.filterOption_persist).toEqual({});
      expect(service.inputSearchOption_persist).toEqual({});
      expect(service.sortOption_persist).toEqual({});
    });
  });


  describe('updateSearchWithPersistedSelections', () => {
    it('should update filterOption with deep copy of filterOption_persist', () => {
      service.filterOption_persist = { key1: 'value1' };
      service.filterOption = {};

      service.updateSearchWithPersistedSelections();

      expect(service.filterOption).toEqual({ key1: 'value1' });
      expect(service.filterOption).not.toBe(service.filterOption_persist); // Ensure deep copy
    });

    it('should update inputSearchOption with deep copy of inputSearchOption_persist', () => {
      service.inputSearchOption_persist = { key2: 'value2' };
      service.inputSearchOption = {};

      service.updateSearchWithPersistedSelections();

      expect(service.inputSearchOption).toEqual({ key2: 'value2' });
      expect(service.inputSearchOption).not.toBe(service.inputSearchOption_persist); // Ensure deep copy
    });

    it('should update sortOption with deep copy of sortOption_persist', () => {
      service.sortOption_persist = { key3: 'value3' };
      service.sortOption = {};

      service.updateSearchWithPersistedSelections();

      expect(service.sortOption).toEqual({ key3: 'value3' });
      expect(service.sortOption).not.toBe(service.sortOption_persist); // Ensure deep copy
    });

    it('should handle undefined filterOption_persist', () => {
      service.filterOption_persist = undefined;
      service.filterOption = { key1: 'value1' };

      service.updateSearchWithPersistedSelections();

      expect(service.filterOption).toEqual({ key1: 'value1' }); // Should remain unchanged
    });

    it('should handle undefined inputSearchOption_persist', () => {
      service.inputSearchOption_persist = undefined;
      service.inputSearchOption = { key2: 'value2' };

      service.updateSearchWithPersistedSelections();

      expect(service.inputSearchOption).toEqual({ key2: 'value2' }); // Should remain unchanged
    });

    it('should handle undefined sortOption_persist', () => {
      service.sortOption_persist = undefined;
      service.sortOption = { key3: 'value3' };

      service.updateSearchWithPersistedSelections();

      expect(service.sortOption).toEqual({ key3: 'value3' }); // Should remain unchanged
    });
  });

  describe('setEndDtForExpiredStatus', () => {
    it('should return false if featureFlagIsDisplayExpired is false', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(false);
      const result = service.setEndDtForExpiredStatus();
      expect(result).toBe(false);
    });

    it('should return the correct end date string if featureFlagIsDisplayExpired is true', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
      const endDate = moment();
      const expectedEndDateString = `[* TO ${endDate.format("YYYY-MM-DDT")}00:00:00Z]`;
      const result = service.setEndDtForExpiredStatus();
      expect(result).toBe(expectedEndDateString);
    });
  });

  describe('checkIfExpiredStatusSelected', () => {
    it('should return false when selectedVal is not EXPIRED_STATUS_OR_DISPLAY and featureFlagIsDisplayExpired is false', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(false);
      const result = service.checkIfExpiredStatusSelected('someOtherStatus');
      expect(result).toBe(false);
    });

    it('should return false when selectedVal is EXPIRED_STATUS_OR_DISPLAY and featureFlagIsDisplayExpired is false', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(false);
      const result = service.checkIfExpiredStatusSelected(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY);
      expect(result).toBe(false);
    });

    it('should return false when selectedVal is not EXPIRED_STATUS_OR_DISPLAY and featureFlagIsDisplayExpired is true', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
      const result = service.checkIfExpiredStatusSelected('someOtherStatus');
      expect(result).toBe(true);
    });

    it('should return true when selectedVal is EXPIRED_STATUS_OR_DISPLAY and featureFlagIsDisplayExpired is true', () => {
      spyOnProperty(service, 'featureFlagIsDisplayExpired', 'get').and.returnValue(true);
      const result = service.checkIfExpiredStatusSelected(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY);
      expect(result).toBe(false);
    });
  });

  describe('getInputSearchOption', () => {
    it('should return the input search option for the given currentCode', () => {
      const currentCode = 'testCode';
      const inputSearchOption = { field: 'testField', query: [] };
      service.inputSearchOption = { [currentCode]: inputSearchOption };

      const result = service.getInputSearchOption(currentCode);
      expect(result).toEqual(inputSearchOption);
    });

    it('should return undefined if the input search option for the given currentCode is not defined', () => {
      const currentCode = 'testCode';
      service.inputSearchOption = {};

      const result = service.getInputSearchOption(currentCode);
      expect(result).toBeUndefined();
    });
  });

  describe('getFilterOption', () => {
    it('should return the filter option for the given currentSearchType', () => {
      const currentSearchType = 'testType';
      const filterOption = { field: 'testField', query: [] };
      service.filterOption = { [currentSearchType]: filterOption };

      const result = service.getFilterOption(currentSearchType);
      expect(result).toEqual(filterOption);
    });

    it('should return undefined if the filter option for the given currentSearchType is not defined', () => {
      const currentSearchType = 'testType';
      service.filterOption = {};

      const result = service.getFilterOption(currentSearchType);
      expect(result).toBeUndefined();
    });
  });

  describe('getSortOption', () => {
    it('should return the sort option for the given currentSearchType', () => {
      const currentSearchType = 'testType';
      const sortOption = { field: 'testField', query: [] };
      service.sortOption = { [currentSearchType]: sortOption };

      const result = service.getSortOption(currentSearchType);
      expect(result).toEqual(sortOption);
    });

    it('should return undefined if the sort option for the given currentSearchType is not defined', () => {
      const currentSearchType = 'testType';
      service.sortOption = {};

      const result = service.getSortOption(currentSearchType);
      expect(result).toBeUndefined();
    });
  });

  describe('getDefaultOption', () => {
    it('should return the default option for the given currentSearchType', () => {
      const currentSearchType = 'testType';
      const defaultOption = { field: 'testField', query: [] };
      service.defaultOption = { [currentSearchType]: defaultOption };

      const result = service.getDefaultOption(currentSearchType);
      expect(result).toEqual(defaultOption);
    });

    it('should return undefined if the default option for the given currentSearchType is not defined', () => {
      const currentSearchType = 'testType';
      service.defaultOption = {};

      const result = service.getDefaultOption(currentSearchType);
      expect(result).toBeUndefined();
    });
  });


  describe('setQueryValForDefaultOption', () => {
    it('should set the query value for the specified field', () => {
      const field = 'testField';
      const value = 'testValue';
      const defaultOption = {
        testType: [
          { field: 'testField', query: [] },
          { field: 'anotherField', query: [] }
        ]
      };

      spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
      service.defaultOption = defaultOption;

      service.setQueryValForDefaultOption(field, value);
      expect(service.defaultOption.testType[0].query).toEqual([value]);
      expect(service.defaultOption.testType[1].query).toEqual([]);
    });

    it('should remove the query value for the specified field when value is null', () => {
      const field = 'testField';
      const value = null;
      const defaultOption = {
        testType: [
          { field: 'testField', query: ['testValue'] },
          { field: 'anotherField', query: [] }
        ]
      };

      spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
      service.defaultOption = defaultOption;

      service.setQueryValForDefaultOption(field, value);
      expect(service.defaultOption.testType[0].query).toEqual([]);
      expect(service.defaultOption.testType[1].query).toEqual([]);
    });

    it('should not modify the query value for fields that do not match', () => {
      const field = 'nonMatchingField';
      const value = 'testValue';
      const defaultOption = {
        testType: [
          { field: 'testField', query: [] },
          { field: 'anotherField', query: [] }
        ]
      };

      spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
      service.defaultOption = defaultOption;

      service.setQueryValForDefaultOption(field, value);
      expect(service.defaultOption.testType[0].query).toEqual([]);
      expect(service.defaultOption.testType[1].query).toEqual([]);
    });

    it('should handle undefined defaultOption', () => {
      const field = 'testField';
      const value = 'testValue';

      spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
      service.defaultOption = undefined;

      expect(() => service.setQueryValForDefaultOption(field, value)).not.toThrow();
    });

    it('should handle undefined elements in defaultOption', () => {
      const field = 'testField';
      const value = 'testValue';
      const defaultOption = {
        testType: undefined
      };

      spyOn(service, 'getActiveCurrentSearchType').and.returnValue('testType');
      service.defaultOption = defaultOption;

      expect(() => service.setQueryValForDefaultOption(field, value)).not.toThrow();
    });
  });
});
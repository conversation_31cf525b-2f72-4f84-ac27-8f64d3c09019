import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { FILTER_OPTIONS } from '../../constants/search-options/filterSearch';
import { defaultOption, filterOption, inputSearchOption, sortOption } from '../../constants/search-options/searchOptionContainer';
import { SORT_OPTIONS } from '../../constants/search-options/sortOptions';
import { CommonRouteService } from './common-route.service';
import { FeatureFlagsService } from './feature-flags.service';

@Injectable({
  providedIn: 'root'
})
export class CommonSearchService {
  inputSearchOption;
  filterOption;
  defaultOption;
  sortOption;
  inputSearchChip;
  facetFilterChip;
  isFiltered : boolean = false;
  isStoreIdUserSearchEnabled:boolean = false;
  isEnableNonBasePGToBasePG: any;  //Clean UP later 
  batchActionActiveTab: any;
  activeCurrentSearchType: any;
  isHideExpiredStatusReqs$ = new Subject();
  isShowExpiredInQuery = false;
  isShowExpiredInQuery_O = false;
  clearLeftFilters$= new Subject();
  filterOption_persist: any;
  inputSearchOption_persist: any;
  sortOption_persist: any;
  
  constructor( 
    public commonRouteService:CommonRouteService, private featureFlagsService: FeatureFlagsService) { 
      // intentionally left empty  
   }


  setInputSearchChip(inputSearchChip){
    this.inputSearchChip = inputSearchChip;
  }
  getInputSearchChip(){
    return this.inputSearchChip;
  }
  setFacetFilterChip(facetFilterChip){
    this.facetFilterChip = facetFilterChip;
  }
  getFacetFilterChip(){
    return this.facetFilterChip;
    }
  setIsHideExpiredStatusReqs(isHideExpiredStatusReqs){
      //fix: Show expired Statuses based on flag    
      if(this.featureFlagIsDisplayExpired && isHideExpiredStatusReqs){
       isHideExpiredStatusReqs = true;
      }else{
        isHideExpiredStatusReqs = false;
      }
      
    this.isHideExpiredStatusReqs$.next(isHideExpiredStatusReqs);
  }

  pad(num, size) {
    //Prepends zeroes to match the reqd size
        if(!num){
          return;
        }
        let s = "000000000" + num;
        return s.substr(s.length-size);
    }
    
 setQueryValueForDefaultOption(field,value){
   /*Instead use setQueryValForDefaultOption(), This current fn has an issue if we want to set false, it is removing the param*/   

   this.defaultOption?.[this.getActiveCurrentSearchType()]?.forEach(element => {
    if(element.field ===field){
      if(!value){
        element.query = [];
      }else{
        element.query = [...[value]];
      }        
    }
  })
}

setQueryValForDefaultOption(field,value){
  this.defaultOption?.[this.getActiveCurrentSearchType()]?.forEach(element => {
   if(element.field ===field){
     if(value == null){
       element.query = [];
     }else{
       element.query = [...[value]];
     }        
   }
 })
}


removeMultiplesFromDefaultOptions(arr){
  /*If multiple parameters needs to be removed pass it as array*/ 

  arr.forEach(ele=>{
    this.setQueryValForDefaultOption(ele, null);
  })
}


setFilters(obj){
  const {key} = obj;
  const filterSearch = this.filterOption?.[key],
  inputSearch = this.inputSearchOption?.[key],
  defaultSearch = this.defaultOption?.[key],
  sortOption = this.sortOption?.[key];


  this.setFilterOption(key,filterSearch);
  this.setDefaultOption(key,defaultSearch);
  this.setInputSearchOption(key, inputSearch);
  this.setSortOption(key, sortOption);
}

setDefaultOption(currentSearchType,options){
  if(this.defaultOption?.[currentSearchType]){
    this.defaultOption[currentSearchType] = options;
  }
}

setFilterOption(currentSearchType,options){
  if(this.filterOption?.[currentSearchType]){
    this.filterOption[currentSearchType] = options;
  } 
}

setInputSearchOption(key,options){
  if(this.inputSearchOption?.[key]){
    this.inputSearchOption[key] = options;
  } 
    }
 setSortOption(key,options){
    if (this.sortOption?.[key]) {
         this.sortOption[key] = options;
        }
    }
 setSort(obj) {
  const {key} = obj;
  const sortOption = this.sortOption?.[key]

  this.setSortOption(key,sortOption);
}

setAllFilterOptions(obj){
  const {currentRouter,key} = obj;

  this.fetchInputSearchOptions({key,currentRouter});
  this.setFilterOptions(obj);
  this.fetchDefaultOptions({ key, currentRouter });
  this.setSortOptions(obj);
  
  }

fetchInputSearchOptions(obj){
  const isEnableNonBasePGToBasePG = true; //Clean UP later  this.featureFlagsService.isFeatureFlagEnabled('enableNonBasePGToBasePG');
  const batchActionActiveTab = this.batchActionActiveTab;
  const {key,currentRouter} = obj;
 
  this.inputSearchOption = {...this.inputSearchOption || {},...{[key]:inputSearchOption({key,currentRouter,
     isEnableNonBasePGToBasePG, batchActionActiveTab})}};
}

fetchDefaultOptions(obj){   
  const {key} = obj;
  let data = Object.assign(obj,
      {isEnableNonBasePGToBasePG : true, //Clean UP later this.featureFlagsService.isFeatureFlagEnabled('enableNonBasePGToBasePG'), 
      batchActionActiveTab: this.batchActionActiveTab})
this.defaultOption = {...this.defaultOption || {},...{[key]:defaultOption(data)}};
    }

setFilterOptions(obj){
  const {currentRouter,key} = obj;
  const filterOptions_org = filterOption({key,currentRouter});
  this.filterOption = {...this.filterOption || {},...{[key]: filterOptions_org}};
    }

setSortOptions(obj) {
const { currentRouter, key } = obj;
const sortOptions = sortOption({ key, currentRouter });
this.sortOption = { ...this.sortOption || {}, ...{ [key]: sortOptions } };
}
resetFilters(obj){
  let {currentRouter, pcSelected, defaultTemplateFilters, resetChips} = obj;

  //resets filters on route change, page load
  this.setInputSearchOption(pcSelected,null);
  this.setFilterOption(pcSelected,null);
  this.setDefaultOption(pcSelected, null);
  this.setSortOption(pcSelected, null);
  
  if(FILTER_OPTIONS?.[currentRouter]?.[pcSelected]?.[0]){
    FILTER_OPTIONS[currentRouter][pcSelected] = [];    
    }
  if (SORT_OPTIONS?.[currentRouter]?.[pcSelected]?.[0]) {
        SORT_OPTIONS[currentRouter][pcSelected] = [];
    }
  defaultTemplateFilters = null;
  this.filterOption_persist =  null;
  this.inputSearchOption_persist =  null;
  
  //Fix: Invalid chips were being displayed in OR when navigated from OT
  if(resetChips){
    this.setFacetFilterChip(null);
    this.setInputSearchChip(null);
  } 
}

setFiltersForPersisted(obj){
  //sets Filters for persisted search, filter selections    
  const {flag, pcSelected} = obj,
        filterSearch = this.filterOption?.[pcSelected],
        inputSearch = this.inputSearchOption?.[pcSelected],
        defaultSearch = this.defaultOption?.[pcSelected],
        sortOptions  =   this.sortOption?.[pcSelected],
        currentRouter = this.currentRouter;

  if(inputSearch && flag){
    this.setInputSearchOption(pcSelected,inputSearch);
  }else{
    this.fetchInputSearchOptions({key: pcSelected,currentRouter});
  } 

  if(filterSearch && flag){
    this.setFilterOption(pcSelected,filterSearch);
  }else{
    this.setFilterOptions({key: pcSelected,currentRouter});
  }

  if(defaultSearch && flag){
    this.setDefaultOption(pcSelected,defaultSearch);
  }else{
    this.fetchDefaultOptions({key: pcSelected,currentRouter});  
    }
    if (sortOptions && flag) {
        this.setSortOption(pcSelected, sortOptions);
    } else {
        this.setSortOptions({ key: pcSelected, currentRouter });
    }
}

  setActiveCurrentSearchType(activeCurrentSearchType){
  this.activeCurrentSearchType = activeCurrentSearchType;
  }
  getActiveCurrentSearchType(){
  return this.activeCurrentSearchType;
  }
 

  setEmptyQueryForAllFields(obj){
    const {key, programCode} = obj;
   this.setEmptyQueryFields(this.inputSearchOption[programCode]);
   this.setEmptyQueryFields(this.filterOption[programCode]);
  this.defaultOption = {...this.defaultOption,...{[key]:defaultOption(obj)}};

  }
  setEmptyQueryFields(searchOption){
    searchOption.forEach(element => {
        if(!["programCode"].includes(element.field)){
          element.query = [];
          element.showChip = [];
        }
         
    });
  }

  get currentRouter(){
    const currentActivatedRoute = this.commonRouteService.currentActivatedRoute;
    
    if(currentActivatedRoute?.includes('template')){
     return 'template';
    }else if(currentActivatedRoute?.includes('request')){
      return 'request';
     }else if(currentActivatedRoute?.includes('offer')){
     return 'offer';
     }
     //Pg details page
     else if(currentActivatedRoute?.includes(`${CONSTANTS.PRODUCTMANAGEMENT}/${ROUTES_CONST.GROUPS.ProductDetails}`)){
      return "";
      }
    else if(currentActivatedRoute?.includes(CONSTANTS.PRODUCTMANAGEMENT)){
      return CONSTANTS.PRODUCTMANAGEMENT;
      }
      else if(currentActivatedRoute?.includes(`${ROUTES_CONST.ADMIN.Admin}/${ROUTES_CONST.ADMIN.ActionLog}`)){
      return CONSTANTS.ACTION_LOG;
      }
      else if(currentActivatedRoute?.includes(`${ROUTES_CONST.ADMIN.ImportLogBPD}`)){
        return CONSTANTS.IMPORT_LOG_BPD;
      }
      else if(currentActivatedRoute?.includes(`${CONSTANTS.STOREMANAGEMENT}/${ROUTES_CONST.GROUPS.StoreDetails}`)){
        return "";
      }
      else if(currentActivatedRoute?.includes(CONSTANTS.STOREMANAGEMENT)){
          return CONSTANTS.STOREMANAGEMENT;
      }
    
    return '';
  }
  
  getInputSearchOption(currentCode){ 
    return this.inputSearchOption[currentCode];
  }

  getFilterOption(currentSearchType){
    return this.filterOption?.[currentSearchType];
  } 

  getSortOption(currentSearchType) {
        return this.sortOption?.[currentSearchType];
  }

  getDefaultOption(currentSearchType){
    return this.defaultOption?.[currentSearchType];
  }
  removeQueryAndChipFromOptions(obj){
    const {programCode,chip, currentRouter} = obj,
     filterOptions = this.getFilterOption(programCode),
          inputOptions = this.getInputSearchOption(programCode),
          isInputExist  = inputOptions.filter(ele=>ele.field===chip?.chip),
          isFilterExist = filterOptions?.filter(ele=>ele.field===chip?.chip);
      
      //If clear all left filters clicked, dont clear the search input chips
      if(isInputExist.length && !chip?.isClearAll_LeftFilter){
        this.emptyElement(isInputExist, inputOptions);

      }
      if(isFilterExist?.length){
        this.emptyElement(isFilterExist);
      }
      let key = programCode
      if(!key){
        key = currentRouter;
      }
    
      if(chip.chip==="bggm"){
        this.emptyFilterList(filterOptions,["bugm","categoryId"]);
      }else if(chip.chip==="bugm"){
        this.emptyFilterList(filterOptions,["categoryId"]);
      }
      this.setInputSearchOption(key,inputOptions);
      programCode && this.setFilterOption(programCode,filterOptions);
  }
  
  emptyFilterList(filterOptions,elements){
    const list = filterOptions.filter(element =>elements.includes(element.field));
        list.forEach(element => {
          this.emptyElement([element]);
        });
  }
  emptyElement(elementArr, inputOptions = []){
    const element = elementArr[0];
      if(element?.linkedTo && inputOptions?.length) {
        const linkedOption = inputOptions.find(ele => ele?.field === element?.linkedTo);
        if(linkedOption?.query) {
          linkedOption.query = [];
        }
      }
        element.query = [];
        element.showChip = [];
        element.elements?.forEach(option => {
          option.query = [];
        });
  }
  populateFilterOption(programCode,{form,item}){
   const filterOptions = this.getFilterOption(programCode);
   const filterSelected= filterOptions?.filter(ele=>ele.field===item)[0];
   //filterOptions.forEach((item)=>{
    const options = form[filterSelected?.field]?.reduce((output,ele)=>{
            if(ele?.selected){
              const { query = [],showChip = []} = output,
                    {id,value} = ele;
              output['query'] = [...query,...[id]];
              output['showChip'] = [...showChip,...[value]];
          }
        return output;
        },{});
        if(filterSelected){
          filterSelected['query'] = options?.['query']||[];
          filterSelected['showChip'] = options?.['showChip']||[];
        }
       
    //});
  }  

  checkIfExpiredStatusSelected(selectedVal){
   /*OR Managment page - If Expired Status is Selected, set a flag so that IS_DISPLAY_EXPIRED_OR_RESULTS
    param in query is modified accordingly*/

    let  isHideExpiredStatusReqs = true;   

     //fix: Show expired Statuses based on flag
    if (selectedVal === CONSTANTS.EXPIRED_STATUS_OR_DISPLAY) {
      isHideExpiredStatusReqs = false;
    }
    return isHideExpiredStatusReqs &&
    this.featureFlagIsDisplayExpired;
  }
  /**
   * This will check whether any filter is selected other than program code and status.
   * If no chip is there or only program code chip is there, then flag should be true and showExpired should go in query
   */
  get isAnyBpdFilterSelected(){
    const filterChipObj = this.facetFilterChip;
    if(filterChipObj) {
      const filterKeys = Object.keys(filterChipObj);
      return filterKeys.length === 0 || filterKeys.length === 1 && filterKeys.includes("programCode") || this.isStatusFilterSelectedForBPD;
    }
    return false;
  }
  /**
   * Check if any search exist, if yes showExpired flag should not go in query
   */
  get isAnyBpdSearchSelected(){
    const facetSearchObj = this.getInputSearchChip();
    if(facetSearchObj) {
      return Object.keys(facetSearchObj).length === 0;
    }
    return false;
  }
  /**
   * if status filter selected for BPD, then showExpired flag should gone.
   */
  get isStatusFilterSelectedForBPD() {
    const filterObj = this.getFacetFilterChip();
    if(filterObj) {
      const filterKeys = Object.keys(filterObj);
      return filterKeys.includes("status");
    }
    return false;
  }

  get isExpiredStatusFilterSelectedForBPD() {
    const filterObj = this.getFacetFilterChip();
    if(filterObj) {
      const filterKeys = Object.keys(filterObj);
      return filterKeys.includes("status") && filterObj.status === 'Expired';
    }
    return false;
  }

  setQueryOptionsForBpd({isHideExpiredStatusReqs,isExpiredStatusFilterSelectedForBPD = false}){
    //Updates query payload
    //If featureflag isOfferRequestArchivalEnabled is true need to exclude dates from query
    //Resetting
    this.removeMultiplesFromDefaultOptions([CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS, CONSTANTS.CURRENT_DATE_QUERY_OR, CONSTANTS.END_DATE_QUERY_OR]);
    
    //fix: Show expired Statuses based on flag
    if(!this.featureFlagIsDisplayExpired){
      return false;
    }

    let isDisplayExpiredReqs = false, dateKey = 'CURRENT_DATE_QUERY_OR';
    if(!isHideExpiredStatusReqs){
      isDisplayExpiredReqs = null;
      dateKey = 'END_DATE_QUERY_OR';
    }
    const sendExpiredFlag = ( this.isAnyBpdSearchSelected && this.isAnyBpdFilterSelected ) || this.isStatusFilterSelectedForBPD;
    if(!sendExpiredFlag && isHideExpiredStatusReqs) {
      isDisplayExpiredReqs = null;
    }
    this.setQueryValForDefaultOption(CONSTANTS.IS_DISPLAY_EXPIRED_OR_RESULTS,isDisplayExpiredReqs);
    this.setQueryValForDefaultOption(CONSTANTS[dateKey], this.setEndDtForExpiredStatus());
  }

  get featureFlagIsDisplayExpired(){
    return this.featureFlagsService.isFeatureFlagEnabled(CONSTANTS.IS_EXPIRED_STATUS_ENABLED);
  }

  setEndDtForExpiredStatus() {
    //Sets end date param for expired status
    if(!this.featureFlagIsDisplayExpired){
      return false;
    }
    const endDate = moment();   
    return `[* TO ${endDate.format("YYYY-MM-DDT")}00:00:00Z]`; //updating based on current tz changes the end date is 00:00:00 now
  }

  resetAllFilterOptions(obj){
    const {current = "", key} = obj;

    if(current){    
      this.setAllFilterOptions({key, currentRouter: this.currentRouter});
    }else{
      this.inputSearchOption = {};
      this.filterOption = {};
      this.defaultOption = {};
      this.sortOption = {};
    }
    this.setInputSearchChip('');
    this.setFacetFilterChip('');
  }
  
  createInputSearchOptions(searchOption,value){
    
    return {
      label:"End Date",
      field:"effectiveEndDate",
      hide:true,
      query:[],
      showChip:[],
      elements:[{
          type:"select",
          field:"effectiveEndDate",
          resetQuery:true,
          query:[],
          options:[{
              label:"Today",
              field:"effectiveEndDate",
              defaultSelect:true,
              elements:[{
                  field:"From",
                  placeholder:"From",
                  hide:true,
                  type:"date",
                  query:[],
                  value:`${moment().startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
                  },{
                  field:"To",
                  placeholder:"To",
                  type:"date",
                  hide:true,
                  query:[],
                  value:`*`
                 }]
              }]
      }],
      searchButton:true,
      defaultSelect:false,
      resetQuery:true,
      queryWithOrFilters:[]
  }
  }

  storeSearchSelections(){
     //For cases where selection needs to persist when routing to child routes, store the selections
    this.filterOption_persist = JSON.parse(JSON.stringify(this.filterOption));
    this.inputSearchOption_persist =   JSON.parse(JSON.stringify(this.inputSearchOption));
    this.sortOption_persist = JSON.parse(JSON.stringify(this.sortOption));
 }

  updateSearchWithPersistedSelections(){
    //Update search with persisted selections
    if(this.filterOption_persist){
      this.filterOption = JSON.parse(JSON.stringify(this.filterOption_persist));
    } 
    if(this.inputSearchOption_persist){
      this.inputSearchOption = JSON.parse(JSON.stringify(this.inputSearchOption_persist));
    } 
      if (this.sortOption_persist) {
          this.sortOption = JSON.parse(JSON.stringify(this.sortOption_persist));
      }

  }
}

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { CommonService } from './common.service';
import { AuthService } from '@appServices/common/auth.service';
import { InitialDataService } from './initial.data.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { CONSTANTS } from '@appConstants/constants';

class AuthServiceMock {
  getTokenString = jasmine.createSpy().and.returnValue('mock-token');
  getUserId = jasmine.createSpy().and.returnValue('mock-user-id');
}

class InitialDataServiceMock {
  getConfigUrls = jasmine.createSpy().and.callFake((param) => `${param}-mock-url`);
  getAppData = jasmine.createSpy().and.returnValue({ 
    departmentsWithCodes: { '001': 'HR' }, 
    offerRequestGroups: [{ divisions: ['North', 'South'] }]
  });
}

class PermissionsServiceMock {
  getPermissions = jasmine.createSpy().and.returnValue(['view', 'edit']);
}

describe('CommonService', () => {
  let service: CommonService;
  let httpMock: HttpTestingController;
  let authServiceMock: AuthServiceMock;
  let initialDataServiceMock: InitialDataServiceMock;
  let permissionsServiceMock: PermissionsServiceMock;

  beforeEach(() => {
    authServiceMock = new AuthServiceMock();
    initialDataServiceMock = new InitialDataServiceMock();
    permissionsServiceMock = new PermissionsServiceMock();

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        CommonService,
        { provide: AuthService, useValue: authServiceMock },
        { provide: InitialDataService, useValue: initialDataServiceMock },
        { provide: PermissionsService, useValue: permissionsServiceMock },
      ],
    });

    service = TestBed.inject(CommonService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return the correct headers in getHeaders', () => {
    const headers = service.getHeaders();
    expect(headers['X-Albertsons-userAttributes']).toBe('mock-token');
    expect(headers['Authorization']).toBeUndefined();
  });

  it('should call getAllocationsData and return data', () => {
    service.getAllocationsData().subscribe(data => {
      expect(data).toBeTruthy();
    });

    const req = httpMock.expectOne(`${CONSTANTS.ALLOCATION_API}-mock-url`);
    expect(req.request.method).toBe('GET');
    req.flush({});
  });

  it('should call saveAllocation with correct payload', () => {
    const payload = { allocation: 'data' };

    service.saveAllocation(payload).subscribe();

    const req = httpMock.expectOne(`${CONSTANTS.ALLOCATION_API}-mock-url`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      ...payload,
      reqObj: { headers: jasmine.any(Object) },
    });

    req.flush({});
  });

  it('should return true if offerRequest is in editing', () => {
    const offerRequest = {
      info: {
        nonDigitalEditStatus: { editStatus: 'E' },
        digitalEditStatus: { editStatus: 'D' },
        offerRequestEditStatus: { editStatus: 'N' }
      }
    };
    const result = service.isReqInEditing(offerRequest);
    expect(result).toBe(true);
  });

  it('should return the department name from code', () => {
    const departmentName = service.getDepartmentNameFromCode('001');
    expect(departmentName).toBe('HR');
  });

  it('should return the divisions list', () => {
    const divisions = service.getDivisions();
    expect(divisions).toEqual(['North', 'South']);
  });

  it('should get events data and return it', () => {
    service.getEventsData().subscribe(data => {
      expect(data).toBeTruthy();
    });

    const req = httpMock.expectOne(`${CONSTANTS.GET_EVENTS_API}-mock-url`);
    expect(req.request.method).toBe('POST');
    req.flush({});
  });

  it('should return events list based on feature flag', () => {
    const eventsList = [
      { eventCode: '001', eventName: 'Event1', isHidden: false },
      { eventCode: '002', eventName: 'Event2', isHidden: true }
    ];

    const events = service.getEventsListBasedOnFeatureFlag(eventsList);
    expect(events.length).toBe(2);
    expect(events[0].value).toBe('Event1');
    expect(events[1].value).toBe('Event2 (H)');
  });

  it('should return false if permissions include required permission', () => {
    const permissions = ['view', 'edit'];
    const result = service.permissionToShowBPDProgramCode(permissions);
    expect(result).toBeFalsy();
  });

  it('should call doFixItApiForBatchLogBpd with jobId', () => {
    const jobId = 123;

    service.doFixItApiForBatchLogBpd(jobId).subscribe();

    const req = httpMock.expectOne(`${CONSTANTS.FIX_IT_API_IMPORT_LOG_BPD}-mock-url?id=123`);
    expect(req.request.method).toBe('POST');
    req.flush({});
  });

  it('should call getPeriodWeeks and return data', () => {
    const query = 'week-1';
    service.getPeriodWeeks(query).subscribe(data => {
      expect(data).toBeTruthy();
    });

    const req = httpMock.expectOne(`${CONSTANTS.GET_PERIOD_WEEKS_API}-mock-url?searchStr=${query}`);
    expect(req.request.method).toBe('GET');
    req.flush({});
  });

  it('should sort properties by string in ascending order', () => {
    const obj = {
      'b': { 'name': 'Banana' },
      'a': { 'name': 'Apple' },
      'c': { 'name': 'Cherry' }
    };

    const sorted = service.sortProperties(obj, 'name', false, false);
    const expectedSortedObj = {
      ' a': { 'name': 'Apple' },
      ' b': { 'name': 'Banana' },
      ' c': { 'name': 'Cherry' }
    };

    expect(sorted).toEqual(expectedSortedObj);
  });

  it('should sort properties by string in descending order', () => {
    const obj = {
      'b': { 'name': 'Banana' },
      'a': { 'name': 'Apple' },
      'c': { 'name': 'Cherry' }
    };

    const sorted = service.sortProperties(obj, 'name', false, true);
    const expectedSortedObj = {
      ' c': { 'name': 'Cherry' },
      ' b': { 'name': 'Banana' },
      ' a': { 'name': 'Apple' }
    };

    expect(sorted).toEqual(expectedSortedObj);
  });

  it('should sort properties by numeric values in ascending order', () => {
    const obj = {
      'a': { 'score': 10 },
      'b': { 'score': 30 },
      'c': { 'score': 20 }
    };

    const sorted = service.sortProperties(obj, 'score', true, false);
    const expectedSortedObj = {
      ' a': { 'score': 10 },
      ' c': { 'score': 20 },
      ' b': { 'score': 30 }
    };

    expect(sorted).toEqual(expectedSortedObj);
  });

  it('should sort properties by numeric values in descending order', () => {
    const obj = {
      'a': { 'score': 10 },
      'b': { 'score': 30 },
      'c': { 'score': 20 }
    };

    const sorted = service.sortProperties(obj, 'score', true, true);
    const expectedSortedObj = {
      ' b': { 'score': 30 },
      ' c': { 'score': 20 },
      ' a': { 'score': 10 }
    };

    expect(sorted).toEqual(expectedSortedObj);
  });
});
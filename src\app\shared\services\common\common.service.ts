import { HttpClient } from '@angular/common/http';
import { Injectable } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { AuthService } from "@appServices/common/auth.service";
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { nullCheckProperty } from "@appUtilities/nullCheck.utility";
import { BehaviorSubject, Subject } from "rxjs";
import { InitialDataService } from './initial.data.service';

@Injectable({
  providedIn: "root",
})
export class CommonService {
  appData: any;
  configData: any;
  divisionsList: any = [];
  eventDataSrc = new BehaviorSubject(null);
  getEventsApi: string = this._initialDataService.getConfigUrls(CONSTANTS.GET_EVENTS_API);
  allocationApi: string = this._initialDataService.getConfigUrls(CONSTANTS.ALLOCATION_API);
  batchImportLogBpdFixItApi = this._initialDataService.getConfigUrls(CONSTANTS.FIX_IT_API_IMPORT_LOG_BPD);
  periodWeeksApi: string = this._initialDataService.getConfigUrls(CONSTANTS.GET_PERIOD_WEEKS_API);

  constructor(private authService: AuthService,
    private _http: HttpClient,
    private _initialDataService: InitialDataService,
    private _permissionsService:PermissionsService) {
      // intentionally left empty
    }
  offerData$ = new BehaviorSubject(null);
  passPaginationData$ = new Subject();
  
  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString(),
    };
  }
  getAllocationsData() {
    return this._http.get(this.allocationApi, { headers: this.getHeaders() });
   }
  saveAllocation(payload) {
    let searchInput = {
      ...payload,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.allocationApi, searchInput)
  }
  isReqInEditing(offerRequest) {
    let isEditing = false;

    if (
      nullCheckProperty(offerRequest, "info.nonDigitalEditStatus.editStatus") === "E" ||
      nullCheckProperty(offerRequest, "info.digitalEditStatus.editStatus") === "E" ||
      nullCheckProperty(offerRequest, "info.offerRequestEditStatus.editStatus") === "E"
    ) {
      //For OR pages || Offer pages
      isEditing = true;
    }

    return isEditing;
  }
  getDepartmentNameFromCode(code) {
    this.appData = this._initialDataService.getAppData();
    const departmentsObj = this.appData.departmentsWithCodes ? this.appData.departmentsWithCodes: null;
    if(code && departmentsObj) {
      return departmentsObj[code];
    }
  }

  getDivisions() { 
    this.configData = this._initialDataService.getAppData();
    let groups = this.configData.offerRequestGroups;
    this.divisionsList = groups.map((group) => {return group.divisions}).flat();
    return this.divisionsList;
    }
    getPeriodWeeks(query) {
         return this._http.get(`${this.periodWeeksApi}?searchStr=${query}`);
    }

  sortProperties(obj, sortedBy, isNumericSort, reverse) {
    sortedBy = sortedBy || 1; // by default first key
    isNumericSort = isNumericSort || false; // by default text sort
    reverse = reverse || false; // by default no reverse

    let reversed = (reverse) ? -1 : 1;

    let sortable = [];
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        sortable.push([key, obj[key]]);
      }
    }
    if (isNumericSort) {
      sortable.sort(function (a, b) {
        return reversed * (a[1][sortedBy] - b[1][sortedBy]);
      });
    } else {
      sortable.sort(function (a, b) {
        let x = a[1][sortedBy].toLowerCase(),
          y = b[1][sortedBy].toLowerCase();
        return x < y ? reversed * -1 : x > y ? reversed : 0;
      });
    }

    let newObject = {};

    for (let i = 0; i < sortable.length; i++) {
      let key = sortable[i][0], value = sortable[i][1];
      newObject[' ' + key] = value;
    }
    return newObject;
  }

  getEventsData() {
    let searchInput = {
      includeLiveOffers : false,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.getEventsApi, searchInput);
  }
  getEventsListBasedOnFeatureFlag(eventsList) {
    let eventValue, eventDesc, events;
   
      events = eventsList && eventsList.map((obj)=> {
        eventValue = eventDesc = obj.eventName
        if (obj.isHidden) {
          eventValue = `${eventDesc} (H)`;
        }
        return {key: `${obj.eventCode}`, value: eventValue }
      })
   
    return events;
  }
  permissionToShowBPDProgramCode(permissions:any){
    return  Object.keys(this._permissionsService.getPermissions()).some(item=>permissions.includes(item));
  }

  doFixItApiForBatchLogBpd(jobId) {
    if(jobId){
      return this._http.post(`${this.batchImportLogBpdFixItApi}?id=${jobId}`,{});
    }
  }

}

import { TestBed } from "@angular/core/testing";
import { HttpClientTestingModule, HttpTestingController } from "@angular/common/http/testing";
import { HttpClient } from "@angular/common/http";
import { DownloadFile } from "./download-file.service";
import { AuthService } from "@appServices/common/auth.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { CONSTANTS } from "@appConstants/constants";
import { PRODUCT_GROUP_CONSTANTS } from "@appGroup/constants/product_group_constants";
import { of } from 'rxjs';

describe('DownloadFile', () => {
    let service: DownloadFile;
    let httpMock: HttpTestingController;
    let authServiceMock: jasmine.SpyObj<AuthService>;
    let initialDataServiceMock: jasmine.SpyObj<InitialDataService>;

    beforeEach(() => {
        const authServiceSpy = jasmine.createSpyObj('AuthService', ['getTokenString']);
        const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                DownloadFile,
                { provide: AuthService, useValue: authServiceSpy },
                { provide: InitialDataService, useValue: initialDataServiceSpy }
            ]
        });

        service = TestBed.inject(DownloadFile);
        httpMock = TestBed.inject(HttpTestingController);
        authServiceMock = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
        initialDataServiceMock = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should get headers', () => {
        authServiceMock.getTokenString.and.returnValue('test-token');
        const headers = service.getHeaders();
        expect(headers).toEqual({
            ...CONSTANTS.HTTP_HEADERS,
            "X-Albertsons-userAttributes": 'test-token'
        });
        expect(authServiceMock.getTokenString).toHaveBeenCalled();
    });


      it('should call downloadCsv method', () => {
        const requestParams = { param1: 'value1' };
        const downloadApi = 'DOWNLOAD_API';
        const mockResponse = new Blob(['test'], { type: 'text/csv' });
        const mockHeaders = { 'Content-Disposition': 'attachment; filename="test.csv"' };

        initialDataServiceMock.getConfigUrls.and.returnValue('http://example.com/download');
        authServiceMock.getTokenString.and.returnValue('test-token');

        spyOn(service, 'downloadCsv').and.callThrough();
        spyOn(URL, 'createObjectURL').and.returnValue('blob-url');
        const createElementSpy = spyOn(document, 'createElement').and.callFake(() => {
            return {
                href: '',
                download: '',
                click: jasmine.createSpy('click')
            } as any;
        });

        service.downloadCsv(requestParams, downloadApi);

        expect(service.downloadCsv).toHaveBeenCalledWith(requestParams, downloadApi);

        const req = httpMock.expectOne((req) => req.method === 'POST');
        req.flush(mockResponse, { headers: mockHeaders });

        expect(createElementSpy).toHaveBeenCalledWith('a');
        const fileLink = createElementSpy.calls.mostRecent().returnValue;
        // expect(fileLink.href).toBe('blob-url');
        // expect(fileLink.download).toBe('test.csv');
        expect(fileLink.click).toHaveBeenCalled();
    });

    describe('downLoadUPCList', () => {
        it('should call POST method for base product group', () => {
            const params = { id: '123' };
            const HttpClientStub = TestBed.inject(HttpClient);
            const stub = spyOn(HttpClientStub, "post").and.returnValue(of(new Blob(['test'], { type: 'application/octet-stream' })));

            service.downLoadUPCList(params, true).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should call GET method for non-base product group', () => {
            const params = '123';
            const HttpClientStub = TestBed.inject(HttpClient);
            const stub = spyOn(HttpClientStub, "get").and.returnValue(of(new Blob(['test'], { type: 'application/octet-stream' })));

            service.downLoadUPCList(params).subscribe();

            expect(stub).toHaveBeenCalled();
        });
    });

    describe('nonBasePgEnabled', () => {
        it('should return the correct value from PRODUCT_GROUP_CONSTANTS', () => {
            const expectedValue = PRODUCT_GROUP_CONSTANTS.NON_BASE_DOWNLOAD_FINAL_UPC_API;
            expect(service.nonBasePgEnabled).toBe(expectedValue);
        });
    });
});
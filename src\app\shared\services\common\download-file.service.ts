import { HttpClient, HttpParams } from "@angular/common/http";

import { Injectable } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { PRODUCT_GROUP_CONSTANTS } from "@appGroup/constants/product_group_constants";
import { AuthService } from "@appServices/common/auth.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
@Injectable({
  providedIn: "root"
})

export class DownloadFile {
  downloadBPDFinalUPCApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.DOWNLOADBPDFINALUPCAPI);
    constructor(
        private _http: HttpClient,
        private authService: AuthService,
        private apiConfigService: InitialDataService
      ) { 
        // intentionally left empty
      }

    getHeaders() {
        return {
          ...CONSTANTS.HTTP_HEADERS,
          "X-Albertsons-userAttributes": this.authService.getTokenString()
        };
      }
      
    downloadCsv(requestParams, downloadApi){
        const url = this.apiConfigService.getConfigUrls(downloadApi);
        let searchInput = {
          ...requestParams,
          headers: this.getHeaders()
        };
        return this._http.post(
          url, 
          searchInput, 
          {observe: 'response', responseType: "blob"}
          ).subscribe((response:any)=>{
            let fileName = response.headers.get('Content-Disposition').split('"')[1];
            let objectUrl = URL.createObjectURL(response.body);
            let fileLink = document.createElement("a");
            fileLink.href = objectUrl;
            fileLink.download = `${fileName}`;
            fileLink.click();
          },
          function (error) {
      
        })
    }
    
      downLoadUPCList(params,isBaseProductGroup = false) {
        const headers = {
          ...CONSTANTS.HTTP_HEADERS,
          "X-Albertsons-userAttributes": this.authService.getTokenString(),
        };
        if(isBaseProductGroup){
          const reqInput = { ...params, reqObj: { headers, isHidePgLoader: true, responseType: "blob" } };
          return this._http.post(this.downloadBPDFinalUPCApi, reqInput);
        }
        params = new HttpParams().set("id", params);
        const url = this.apiConfigService.getConfigUrls(
          this.nonBasePgEnabled
        );
        return this._http.get(url, { headers, params, responseType: "blob" });
      }
      get nonBasePgEnabled() {
        return PRODUCT_GROUP_CONSTANTS.NON_BASE_DOWNLOAD_FINAL_UPC_API ;
      }
}
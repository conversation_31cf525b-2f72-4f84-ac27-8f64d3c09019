import { Http<PERSON><PERSON>, <PERSON>ttpHandler } from "@angular/common/http";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { TestBed } from "@angular/core/testing";
import { UntypedFormArray, UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { StoreGroupService } from "@appGroupsServices/store-group.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { BehaviorSubject } from "rxjs";
import { CommonSearchService } from "./common-search.service";
import { FacetItemService } from "./facet-item.service";
import { FeatureFlagsService } from "./feature-flags.service";
import { InitialDataService } from "./initial.data.service";
import { QueryGenerator } from "./queryGenerator.service";
import { Permission } from "@appShared/albertsons-angular-authorization/model/permission.model";

describe("FacetItemService", () => {
  let service: FacetItemService;

  beforeEach(() => {
    const featureFlagsServiceStub = () => ({
        isFeatureFlagEnabled: () => ({}),
        isUPPFieldSearchEnabled: () => ({}),
    });
    const initialDataServiceStub = () => ({
      getAppDataName: () => ({}),
      getAppData: () => ({
        offerPrograms: {},
        offerDeliveryChannels: {},
        offerRequestStatuses: {},
        offerStatuses: {},
        offerType: {},
        amountTypes: {},
        offerFiltersMF: [
          {
            displayValue: "Program Code",
            configMapper: "offerPrograms",
            facetMapper: "offerProgramCd"
          }],
        offerFilters: [
          {
            displayValue: "Program Code",
            configMapper: "offerPrograms",
            facetMapper: "offerProgramCd"
          },
          {
            displayValue: "Channel",
            configMapper: "offerDeliveryChannels",
            facetMapper: "deliveryChannel"
          },
          {
            displayValue: "Digital/Non-Digital",
            configMapper: "digital",
            facetMapper: "digital"
          },
          {
            displayValue: "Offer Type",
            configMapper: "offerType",
            facetMapper: "offerType"
          },
          {
            displayValue: "Discount",
            configMapper: "amountTypes",
            facetMapper: "discountType"
          },
          {
            displayValue: "Divisions",
            configMapper: "divisions",
            facetMapper: "divisions"
          },
          {
            displayValue: "Status",
            configMapper: "offerStatuses",
            facetMapper: "offerStatus"
          },
          {
            displayValue: "Category",
            configMapper: "productCategories",
            facetMapper: "categories"
          },
          {
            displayValue: "Events",
            configMapper: "events",
            facetMapper: "events"
          }
        ]
         
      }),
      getConfigUrls: (uPDATE_USER_PREF) => ({}),
      getSearchOptions: () => ({}),
      getSearchOfferOptions: () => ({}),
      searchOfferPODOptions: [{ label: 'Universal Search'}, { label: 'Start Date'}]
    });
    const queryGeneratorStub = () => ({
      getQuery: () => "",
      getQueryFilter: () => [{}],
      getQueryWithFilter: () => [{}],
      pushParam: () => ({}),
      getInputValue:()=>({}),
      removeParameters:()=>({}),
    });
    const permissionsServiceStub = () => ({
      loadPermissions: adminPermissions => ({}),
      getPermissions: () => ({})
    });
    const baseInputSearchServiceStub = () => ({
      getDataForInputSearch: () => ({subscribe: () => ({})}),
      inputSearchOptions: [],
      getInputFieldSelected: ()=>({}),
      generateQueryForOptions: () => ({}),
      formQuery: () => ({}),
      formQueryWithFilter: () => ({}),
      setInputSearchOptions: () => ({})
    })
    const storeGroupServiceStub = {
      createInstance: () => ({}),
      createFacetInstance: () => ({}),
      setEnableForm: (arg) => ({}),
      searchStoreGroup: (groupName, arg) => ({ subscribe: () => ({}) }),
      setStoreQuery: (object) => ({}),
      getFeatureKeys: () => ({ length: {}, includes: () => ({}) }),
      setFeatureKeys: (arg) => ({}),
      populateStoreFacets: () => ({ subscribe: () => ({}) }),
      populateStoreFilterSearch: (object) => ({}),
      getStoreQuery: () => ({}),
      createStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
      updateStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
      call: (arg) => ({}),
      getStoreIds: (object) => ({ subscribe: () => ({}) }),
    };
    const commonSearchServiceStub = ()=>({});
    
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        FacetItemService,
        { provide: PermissionsService, useFactory: permissionsServiceStub },
        HttpClient,
        HttpHandler,
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: StoreGroupService, useValue: storeGroupServiceStub },
        { provide: BaseInputSearchService, useFactory: baseInputSearchServiceStub},
        { provide: FeatureFlagsService, useFactory: featureFlagsServiceStub},
        { provide: CommonSearchService,useFactory:commonSearchServiceStub}
      ],
    });
    service = TestBed.inject(FacetItemService);
    service.initialData = initialDataServiceStub().getAppData();
  });
  it("can load instance", () => {
    expect(service).toBeTruthy();
  });

  describe("setter/getter", () => {
    it("basics", () => {
      service.setFacetItemList({});
      expect(Object.keys(service.getFacetItemList()).length).toEqual(0);
      service.setFacetItemList({foo:'bar'});
      expect(Object.keys(service.getFacetItemList()).length).toEqual(1);

      service.setFacetItems([]);
      expect(service.getFacetItems().length).toEqual(0);
      service.setFacetItems([{}]);
      expect(service.getFacetItems().length).toEqual(1);
    });
    it("basics getFacetChipItems", () => {
      service.getFacetChipItems();
    });
    it("basics getdivsionStateFacetItems", () => {
      service.getdivsionStateFacetItems();
    });
    it("basics getOfferFilter", () => {
      service.getOfferFilter();
    });
    it("basics setOfferFilter", () => {
      service.setOfferFilter("setOfferFilter");
    });
    it("basics setTodayOption", () => {
      service.dateField = [];
      service.setTodayOption("setTodayOption");
    });
    it("basics emptyTodayOption", () => {
      service.emptyTodayOption();
    });
    it("basics getTodayOption", () => {
      service.getTodayOption();
    });
  });  
  describe("getStoreSelectedValue", () => {
    it("test", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      const spy = spyOn(storeGroupServiceStub, "getFeatureKeys").and.returnValue(["divisions"]);
      service.getStoreSelectedValue("divisions", "test", null);
      expect(spy).toHaveBeenCalled();
    });
    it("test storesSearchCriteria", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      const spy = spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["divisions"]);
      const storesSearchCriteria = {
        features: "features",
      };
      service.getStoreSelectedValue(
        "divisions",
        "features",
        storesSearchCriteria
      );
      expect(spy).toHaveBeenCalled();
    });
    it("test storesSearchCriteria else", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      const spy = spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["divisions"]);
      const storesSearchCriteria = {
        features: "features",
      };
      service.getStoreSelectedValue(
        "features",
        "features",
        storesSearchCriteria
      );
      expect(spy).toHaveBeenCalled();
    });
    it("test storesSearchCriteria else", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      const spy = spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["divisions", "clip"]);
      const storesSearchCriteria = {
        features: {
          features: "clip",
        },
      };
      service.getStoreSelectedValue("clip", "features", storesSearchCriteria);
      expect(spy).toHaveBeenCalled();
    });
    it("test storesSearchCriteria else", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      const spy = spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["divisions", "clip"]);
      const storesSearchCriteria = {
        clip: "clip",
      };
      service.getStoreSelectedValue("clip", "features", storesSearchCriteria);
      expect(spy).toHaveBeenCalled();
    });
    it("test storesSearchCriteria features", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      const spy = spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["divisions", "clip"]);
      const storesSearchCriteria = {
        features: {
          clip: "features",
        },
      };
      service.getStoreSelectedValue("clip", "features", storesSearchCriteria);
      expect(spy).toHaveBeenCalled();
    });
    it("should trace else when key doesn't exist in features", () => {
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      spyOn(storeGroupServiceStub, "getFeatureKeys").and.returnValue(['banners']);
      service.getStoreSelectedValue("divisions", "test", null);
      expect(storeGroupServiceStub.getFeatureKeys).not.toEqual("divisions");
    });
  });
  describe('sortDivisionRogCds', () => {
    it('should sort object', () => {
      const obj = {
        "DENVER": {
          "VLAS": 12,
          "SDEN": 1
        }
      }
      service.sortDivisionRogCds(obj);
      expect(obj).toEqual({
        "DENVER": {
          "SDEN": 1,
          "VLAS": 12
        }
      })
    });
  })

  describe("setProgramCodeSelected",()=>{
    let permissionsServiceStub: PermissionsService, lsStub;
    beforeEach(() => {
        lsStub =  spyOn(localStorage, 'getItem');
       permissionsServiceStub = TestBed.inject(PermissionsService);
       lsStub.and.returnValue(null);
    })
    it("sets to SC when conditions were met",()=>{
        // let permissionsServiceStub: PermissionsService, lsStub =  spyOn(localStorage, 'getItem');
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({"VIEW_OFFER_REQUESTS": {name: ''}});
      service.programCodeSelected = null;
      service.setProgramCodeSelected();
       expect( service.programCodeSelected).toEqual('SC')
    });

    it("sets to SPD when conditions were met",()=>{
        // let permissionsServiceStub: PermissionsService, lsStub =  spyOn(localStorage, 'getItem');
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({"VIEW_GR_SPD_OFFER_REQUESTS": {name: ''}});
      service.programCodeSelected = null;
      service.setProgramCodeSelected();
       expect( service.programCodeSelected).toEqual('SPD')
    });

    it("sets to SC when conditions were met",()=>{
        // let permissionsServiceStub: PermissionsService, lsStub =  spyOn(localStorage, 'getItem');
      lsStub.and.returnValue("GR");
      service.setProgramCodeSelected();
      expect( service.programCodeSelected).toEqual('GR')
    });
 
  });
  
  describe("populateItemsForStoreFacets", () => {
    it("populateItemsForStoreFacets", () => {
      const itemObj = {
        banners: {
          "Acme Market": 164,
          Albertsons: 402,
          "Albertsons Market": 28,
          Amigos: 4,
          Andronicos: 2,
          Carrs: 12,
          Eagle: 2,
          Haggen: 15,
          "Jewel-Osco": 188,
          Lucky: 5,
          "Market Street": 19,
          "Pak N Save": 3,
          Pavilions: 26,
          Randalls: 37,
          Safeway: 894,
          "Safeway Community Markets": 4,
          Shaws: 129,
          "Star Market": 21,
          "Tom Thumb": 63,
          "United Supermarkets": 43,
          Vons: 200,
          safeway: 4,
        },
        divisions: {
          Acme: 164,
          Denver: 123,
          Eastern: 111,
          Haggen: 15,
          InterMtn: 88,
          JewelOsco: 188,
          Norcal: 284,
          Portland: 141,
          Seattle: 219,
          Shaws: 150,
          SoCal: 342,
          Southern: 153,
          Southwest: 193,
        },
        DUG: {
          DUG: 302,
        },
        Delivery: {
          Delivery: 1341,
        },
        Fuel: {
          Fuel: 336,
        },
        "Jamba Juice": {
          "Jamba Juice": 43,
        },
        Pharmacy: {
          Pharmacy: 1728,
        },
        Starbucks: {
          Starbucks: 1235,
        },
      };
      const storesSearchCriteria = {
        divisions: null,
        banners: null,
        features: null,
        divisionRogCds: null,
      };
      service.populateItemsForStoreFacets(itemObj, storesSearchCriteria, null);
      const divisionRogCds = "divisionRogCds";
      service.populateItemsForStoreFacets(
        itemObj,
        storesSearchCriteria,
        divisionRogCds
      );
    });
  });

  xdescribe("populateFacet", () => {
    
    it("populateFacet", () => {
      const facetSelect = {
        "programCd": {
          "PD": 0,
          "SPD": 0,
          "SC": 4943,
          "GR": 0,
          "MF": 0
        },
        "deliveryChannel": {
          "CC": 2776,
          "IS": 0,
          "PO": 0,
          "Digital Only-In Ad": 2167,
          "Digital Only-Not In Ad": 0
        },
        "offerType": {
          "ITEM_DISCOUNT": 0,
          "BUYX_GETX": 0,
          "BUYX_GETY": 0,
          "MEAL_DEAL": 0,
          "BUNDLE": 0,
          "MUST_BUY": 0,
          "FAB_5_OR_SCORE_4": 0,
          "WOD_OR_POD": 0,
          "REWARDS_ACCUMULATION": 0,
          "REWARDS_FLAT": 0,
          "CONTINUITY": 0,
          "INSTANT_WIN": 0,
          "ALASKA_AIRMILES": 0,
          "CUSTOM": 0
        },
        "discountType": {
          "AMOUNT_OFF": 0,
          "AMOUNT_OFF_WEIGHT_VOLUME": 0,
          "FREE": 0,
          "PRICE_POINT_ITEMS": 0,
          "PRICE_POINT_WEIGHT_VOLUME": 0,
          "PERCENT_OFF_ITEMS": 0
        },
        "group": {
          "AM::Acme": 0,
          "CORP::Corporate": 0,
          "DE::Denver": 0,
          "ES::Eastern": 0,
          "HG::Haggen": 0,
          "IM::Intermountain": 0,
          "JW::Jewel": 0,
          "NC::Norcal": 0,
          "PT::Portland": 0,
          "SE::Seattle": 0,
          "SH::Shaws/Star Market": 0,
          "SC::SoCal": 0,
          "SO::Southern": 0,
          "SW::Southwest": 0
        },
        "status": {
          "DI": 0,
          "NDI": 0,
          "I": 0,
          "S": 0,
          "A": 0,
          "P": 0,
          "E": 0,
          "U": 0,
          "D": 0,
          "C": 0,
          "R": 0
        }
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "getQuery")
        .and.returnValue(
          "limit=100;sortBy=lastUpdateTimestampDESC;requestType=(ITEM_DISCOUNT);createdAppId=OMS;"
        );
      spyOn(service,'getOfferRequestStatus').and.returnValue({"DI":'I'});;
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("CC OR IA");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue(["deliveryChannel=(CC)#adType=(IA)"]);

      service.populateFacet(facetSelect);
    });
    it("populateFacet", () => {
      const facetSelect = {
        programCd: {
          PD: 0,
          SPD: 0,
          SC: 14202,
          GR: 0,
          MF: 144,
        },
        deliveryChannel: {
          CC: 4858,
          IS: 3278,
          PO: 2574,
          "Digital Only-In Ad": 2586,
          "Digital Only-Not In Ad": 1050,
        },
        offerType: {
          ITEM_DISCOUNT: 0,
          BUYX_GETX: 0,
          BUYX_GETY: 0,
          MEAL_DEAL: 0,
          BUNDLE: 0,
          MUST_BUY: 0,
          FAB_5_OR_SCORE_4: 0,
          WOD_OR_POD: 0,
          STORE_CLOSURE: 0,
          REWARDS_ACCUMULATION: 0,
          REWARDS_FLAT: 0,
          CONTINUITY: 0,
          INSTANT_WIN: 0,
          ALASKA_AIRMILES: 0,
          CUSTOM: 0,
        },
        discountType: {
          AMOUNT_OFF: 0,
          AMOUNT_OFF_WEIGHT_VOLUME: 0,
          FREE: 0,
          PRICE_POINT_ITEMS: 0,
          PRICE_POINT_WEIGHT_VOLUME: 0,
          PERCENT_OFF_ITEMS: 0,
        },
        group: {
          "AM::Acme": 0,
          "CORP::Corporate": 0,
          "DE::Denver": 0,
          "ES::Eastern": 0,
          "HG::Haggen": 0,
          "IM::Intermountain": 0,
          "JW::Jewel": 0,
          "NC::Norcal": 0,
          "PT::Portland": 0,
          "SE::Seattle": 0,
          "SH::Shaws/Star Market": 0,
          "SC::SoCal": 0,
          "SO::Southern": 0,
          "SW::Southwest": 0,
        },
        status: {
          DI: 0,
          NDI: 0,
          I: 0,
          S: 0,
          A: 0,
          P: 0,
          E: 0,
          U: 0,
          D: 0,
          C: 0,
          R: 0,
        },
        podStatus:{
          true: "yes",
          false: "no"
        },
        digital: {
          false: 'Non-Digital',
          true: 'Digital'
        },
        offerStatus:{
          DE: "Deployed",
          CN: "Canceled"
        },
        divisionId:{
          "AM::Acme": 0,
          "CORP::Corporate": 0,
        },
        isProofed:{
          yes: 'true',
          no: 'false'
        },
        color:{
          green: 'true',
          orange: 'false'
        }
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "getQuery")
        .and.returnValue(
          "limit=100;sortBy=lastUpdateTimestampDESC;podStatus=(true OR false);requestType=(ITEM_DISCOUNT OR FAB_5_OR_SCORE_4 OR WOD_OR_POD);createdAppId=OMS;"
        );
      spyOn(service,'getOfferRequestStatus').and.returnValue({"DI":'I'});
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("CC OR IS");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });
    it("populateFacet", () => {
      const facetSelect = {
        programCd: {
          PD: 0,
          SPD: 0,
          SC: 14202,
          GR: 0,
          MF: 144,
        },
        deliveryChannel: {
          CC: 4858,
          IS: 3278,
          PO: 2574,
          "Digital Only-In Ad": 2586,
          "Digital Only-Not In Ad": 1050,
        },
        offerType: {
          ITEM_DISCOUNT: 0,
          BUYX_GETX: 0,
          BUYX_GETY: 0,
          MEAL_DEAL: 0,
          BUNDLE: 0,
          MUST_BUY: 0,
          FAB_5_OR_SCORE_4: 0,
          WOD_OR_POD: 0,
          STORE_CLOSURE: 0,
          REWARDS_ACCUMULATION: 0,
          REWARDS_FLAT: 0,
          CONTINUITY: 0,
          INSTANT_WIN: 0,
          ALASKA_AIRMILES: 0,
          CUSTOM: 0,
        },
        discountType: {
          AMOUNT_OFF: 0,
          AMOUNT_OFF_WEIGHT_VOLUME: 0,
          FREE: 0,
          PRICE_POINT_ITEMS: 0,
          PRICE_POINT_WEIGHT_VOLUME: 0,
          PERCENT_OFF_ITEMS: 0,
        },
        group: {
          "AM::Acme": 0,
          "CORP::Corporate": 0,
          "DE::Denver": 0,
          "ES::Eastern": 0,
          "HG::Haggen": 0,
          "IM::Intermountain": 0,
          "JW::Jewel": 0,
          "NC::Norcal": 0,
          "PT::Portland": 0,
          "SE::Seattle": 0,
          "SH::Shaws/Star Market": 0,
          "SC::SoCal": 0,
          "SO::Southern": 0,
          "SW::Southwest": 0,
        },
        status: {
          DI: 0,
          NDI: 0,
          I: 0,
          S: 0,
          A: 0,
          P: 0,
          E: 0,
          U: 0,
          D: 0,
          C: 0,
          R: 0,
        },
        podStatus:{
          true: "yes",
          false: "no"
        },
        digital: {
          false: 'Non-Digital',
          true: 'Digital'
        },
        actionFailedState: {
          false: 'Non-Digital',
          true: 'Digital'
        },
        offerFailedState: {
          FDE: 'Failed Deployment',
          FPU: 'Failed Publishing'
        },
        offerStatus:{
          DE: "Deployed",
          CN: "Canceled"
        },
        divisionId:{
          "AM::Acme": 0,
          "CORP::Corporate": 0,
        },
        isProofed:{
          yes: 'true',
          no: 'false'
        },
        color:{
          green: 'true',
          orange: 'false'
        }
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "getQuery")
        .and.returnValue(
          "limit=100;sortBy=lastUpdateTimestampDESC;podStatus=(true OR false);requestType=(ITEM_DISCOUNT OR FAB_5_OR_SCORE_4 OR WOD_OR_POD);createdAppId=OMS;"
        );
      spyOn(service,'getOfferRequestFailedStatus').and.returnValue({'PROCESS': "Failed Processing"});
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("CC OR IS");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });
  });
 
  describe("populateFacetSearch", () => {
    it("populateFacetSearch", () => {
      const query =
        "createUserId=(nling05*);limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;";
      service.populateFacetSearch(query);
    });
  });

  describe("getProgramTypeValue", () => {
    it("getProgramTypeValue", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub,'getQuery').and.returnValue("limit=100;sortBy=lastUpdateTimestampDESC;programType=(Cents\\ Off OR Free);requestType=(ITEM_DISCOUNT);discountType=(AMOUNT_OFF);regionId=(05);createdAppId=OMS;programCode=(GR);");
      service.getProgramTypeValue('Free');
    });
  });

  describe("populateFacetFieldChips", () => {
    it("populateFacetFieldChips", () => {
      const search = {
        divisions: [
          {
            id: "Acme",
            count: 164,
            value: "Acme",
            selected: true,
          },
          false,
          {
            id: "Eastern",
            count: 111,
            value: "Eastern",
            selected: 2,
          },
          false,
          false,
          false,
          {
            id: "Norcal",
            count: 284,
            value: "Norcal",
            selected: true,
          },
          false,
          false,
          false,
          false,
          false,
          false,
        ],
        banners: [
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          {
            id: "Safeway",
            count: 894,
            value: "Safeway",
            selected: true,
          },
          false,
          false,
          false,
          false,
          false,
          false,
          false,
        ],
        DUG: [
          {
            id: "DUG",
            count: 302,
            value: "DUG",
            selected: "all",
          },
        ],
        Delivery: [
          {
            id: "Delivery",
            count: 1341,
            value: "Delivery",
            selected: "all",
          },
        ],
        Fuel: [
          {
            id: "Fuel",
            count: 336,
            value: "Fuel",
            selected: "all",
          },
        ],
        "Jamba Juice": [
          {
            id: "Jamba Juice",
            count: 43,
            value: "Jamba Juice",
            selected: "all",
          },
        ],
        Pharmacy: [
          {
            id: "Pharmacy",
            count: 1728,
            value: "Pharmacy",
            selected: "all",
          },
        ],
        Starbucks: [
          {
            id: "Starbucks",
            count: 1235,
            value: "Starbucks",
            selected: "all",
          },
        ],
        divisionRogCds: {
          Portland: [false],
          Seattle: [false, false, false],
          Acme: [
            {
              id: "ACME",
              count: 150,
              value: "ACME",
              selected: true,
            },
          ],
          Haggen: [false],
          JewelOsco: [false],
          Shaws: [false, false],
          InterMtn: [false],
          Southwest: [false, false],
          Norcal: [false, false],
          SoCal: [false, false, false],
          Denver: [false],
          Eastern: [
            {
              id: "SEAS",
              count: 100,
              value: "SEAS",
              selected: true,
            },
          ],
          Southern: [false, false],
        },
      };
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["DUG", "Delivery", "Fuel", "Jamba Juice", "Starbucks"]);

      service.populateFacetFieldChips(search, "deliveryChannel");
    });
    it("populateFacetFieldChips", () => {
      const search = {
        programCode: [false, false, false, false, false],
        deliveryChannel: [
          {
            id: "CC",
            count: 0,
            value: "Clip and Click",
            selected: true,
          },
          false,
          false,
          false,
          false,
        ],
        offerType: [
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
        ],
        discountType: [false, false, false, false, false, false],
        group: [
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
        ],
        status: [
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
        ],
      };
      const storeGroupServiceStub: StoreGroupService = TestBed.inject(
        StoreGroupService
      );
      spyOn(storeGroupServiceStub, "getFeatureKeys")
        .and.returnValue(["DUG", "Delivery", "Fuel", "Jamba Juice", "Starbucks"]);

      service.populateFacetFieldChips(search, "deliveryChannel");
    });
  });

  describe("getFacetFieldList", () => {
    it("getFacetFieldList", () => {
      service.getFacetFieldList();
    });
  });

  describe("getFilterFacetFields", () => {
    it("getFilterFacetFields", () => {
      service.getFilterFacetFields();
    });
  });

  describe("getSearchFacetFields", () => {
    it("getSearchFacetFields", () => {
      service.getSearchFacetFields();
    });
  });

  describe("getSearchFacetKeys", () => {
    it("getSearchFacetKeys", () => {
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      spyOn(initialDataServiceStub, "getSearchOptions")
        .and.returnValue([]);
      spyOn(initialDataServiceStub, "getSearchOfferOptions")
        .and.returnValue(["DUG", "Delivery", "Fuel", "Jamba Juice", "Starbucks"]);
      service.getSearchFacetKeys();
    });
  });

  describe("getDefaultFilterchip", () => {
    it("getDefaultFilterchip", () => {
      spyOn(service, "getFilterFacetFields")
        .and.returnValue(["DUG", "Delivery", "Fuel", "Jamba Juice", "Starbucks"]);
      service.getDefaultFilterchip();
    });
  });

  describe("setDigitalProperties", () => {
    it("setDigitalProperties", () => {
      service.setDigitalProperties();
    });
  });

  describe("setPodApprovedStatus", () => {
    it("should make expected calls", () => {
      const res = service.setPodApprovedStatus();
      expect(res).toEqual({
        true: "Yes",
        false: "No"
      })
    });
  });

  describe("addOfferTypes", () => {
    it("addOfferTypes", () => {
      service.addOfferTypes(
        { configOfferType: "configOfferType" },
        { configOfferType: "configOfferType" }
      );
    });
    it("addOfferTypes", () => {
      service.addOfferTypes(null, { configOfferType: "configOfferType" });
    });
    it("addOfferTypes", () => {
      service.addOfferTypes(
        { CC: "CC" },
        { configOfferType: "configOfferType" }
      );
    });
  });

  describe("populateStoreFacet", () => {
    it("populateStoreFacet", () => {
      spyOn(service, "populateItemsForStoreFacets").and.returnValue({});
      service.populateStoreFacet({}, {}, {});
    });
    it("populateStoreFacet else", () => {
      spyOn(service, "populateItemsForStoreFacets").and.returnValue({});
      service.populateStoreFacet(null, {}, null);
    });
  });

  describe("getStoreSelectedValueForDivisionStates", () => {
    it("getStoreSelectedValueForDivisionStates", () => {
      service.getStoreSelectedValueForDivisionStates(null, null, null, null);
    });
    it("getStoreSelectedValueForDivisionStates", () => {
      service.getStoreSelectedValueForDivisionStates(
        "divisionRogCds",
        "divisions",
        "divisionRogCds",
        { divisionRogCds: { divisions: ["divisionRogCds"] } }
      );
    });
    it("getStoreSelectedValueForDivisionStates", () => {
      service.getStoreSelectedValueForDivisionStates(
        "divisionRogCd",
        "divisions",
        "divisionRogCds",
        { divisionRogCds: { divisions: ["divisionRogCds"] } }
      );
    });
  });

  describe("setisAllBannersSelectedValue", () => {
    it("setisAllBannersSelectedValue", () => {
      service.isAllBannersSelected = new BehaviorSubject(false);
      service.setisAllBannersSelectedValue(1);
    });
    it("getIsAllBannersSelected", () => {
      service.isAllBannersSelected = new BehaviorSubject(false);
      service.getIsAllBannersSelected();
    });
    it("setisAllDivisionsSelectedValue", () => {
      service.isAllDivisionsSelected = new BehaviorSubject(false);
      service.setisAllDivisionsSelectedValue(1);
    });
    it("getisAllDivisionsSelected", () => {
      service.isAllDivisionsSelected = new BehaviorSubject(false);
      service.getisAllDivisionsSelected();
    });
  });

  describe("addDeliveryChannels", () => {
    it("test", () => {
      let offerDeliveryChannels = {};
      offerDeliveryChannels["DO"] = "Digital Only";
      expect(Object.keys(offerDeliveryChannels).length).toEqual(1);
      service.addDeliveryChannels(offerDeliveryChannels);
      expect(Object.keys(offerDeliveryChannels).length).toEqual(2);
      const keys = Object.keys(offerDeliveryChannels);
      expect(keys.includes("Digital Only-In Ad")).toEqual(true);
      expect(keys.includes("Digital Only-Not In Ad")).toEqual(true);
      expect(keys.includes("DO")).toEqual(false);
    });
    it("test else", () => {
      let offerDeliveryChannels = {};
      offerDeliveryChannels["IA"] = "Digital Only-In Ad";
      service.addDeliveryChannels(offerDeliveryChannels);
    });
    it("test else", () => {
      let offerDeliveryChannels = {};
      offerDeliveryChannels["NIA"] = "Digital Only-In Not Ad";
      service.addDeliveryChannels(offerDeliveryChannels);
    });
  });

  describe("removeFilterChip", () => {
    it("removeFilterChip expected", () => {
      let removeFilter = ['productDisQualifier'];
      let form = new UntypedFormGroup({productDisQualifier : new UntypedFormArray([new UntypedFormGroup({productDisQualifier: new UntypedFormControl('123')})])});
      spyOn(service,'removeFilterChip');
      service.removeFilterChip(removeFilter,form);
    });
    it("removeFilterChip expected resetPartialValues", () => {
      let removeFilter = [''];
      let form = new UntypedFormGroup({productDisQualifier : new UntypedFormArray([new UntypedFormGroup({productDisQualifier: new UntypedFormControl('123')})])});
      spyOn(service,'resetPartialValues');
      service.removeFilterChip(removeFilter,form);
    });
  });

  describe("resetFilterValues", () => {
    it("resetFilterValues expected", () => {
      let fArray = new UntypedFormArray([
        new UntypedFormControl(false)
      ])
      service.resetFilterValues(fArray, "divisions");
    });
  });

  describe("getFilterList", () => {
    it("getFilterList expected", () => {
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      spyOn(initialDataServiceStub,'getAppData').and.returnValue(
        {
          offerFilters: [
        {
          displayValue: "Program Code",
          configMapper: "offerPrograms",
          facetMapper: "offerProgramCd"
        }],
          offerFiltersMF: [
        {
            displayValue: "Program Code",
            configMapper: "offerPrograms",
            facetMapper: "offerProgramCd"
          }]});
      service.programCodeChecked = {
        MF: false,
        SC: true};
      service.getFilterList();
    });
  });
  

  describe("resetPartialValues", () => {
    it("resetPartialValues expected", () => {
      const initialDataServiceStub: InitialDataService = TestBed.inject(
        InitialDataService
      );
      spyOn(initialDataServiceStub,'getAppData').and.returnValue(
        {
          offerFilters: [
        {
          displayValue: "Program Code",
          configMapper: "offerPrograms",
          facetMapper: "offerProgramCd"
        }],
          offerFiltersMF: [
        {
            displayValue: "Program Code",
            configMapper: "offerPrograms",
            facetMapper: "offerProgramCd"
          }]});
      service.programCodeSelected = 'SC';
      let removeFilter = [
        "regions",
        "targeted",
        "isInAd",
        "isInEmail"
    ];
    let formaArray = new UntypedFormArray([new UntypedFormGroup({productDisQualifier: new UntypedFormControl('123')})]);
      service.resetPartialValues(removeFilter,'offerProgramCd',formaArray);
    });
  });

  describe("generateQuery", () => {
    it("digitalUiStatus", () => {
      let formValue = {};
      formValue["status"] = [
        { id: "I", count: 0, value: "Draft", selected: true },
      ];
      const query = service.generateQuery(formValue);
      const keys = Object.keys(query);
      expect(keys.includes("queryWithOrFilters")).toEqual(true);
      expect(query["queryWithOrFilters"]).toEqual(
        "digitalUiStatus=(I)#nonDigitalUiStatus=(I)"
      );
    });
    it("digitalUiStatus draft", () => {
      let formValue = {};
      formValue["status"] = [
        { id: "I", count: 0, value: "Draft", selected: true },
      ];
      const query = service.generateQuery(formValue);
      const keys = Object.keys(query);
      expect(keys.includes("queryWithOrFilters")).toEqual(true);
      expect(query["queryWithOrFilters"]).toEqual(
        "digitalUiStatus=(I)#nonDigitalUiStatus=(I)"
      );
    });
    it("digitalUiStatus draft and submit", () => {
      let formValue = {};
      formValue["status"] = [
        { id: "I", count: 0, value: "Draft", selected: true },
        { id: "S", count: 0, value: "Submitted", selected: true },
      ];
      const query = service.generateQuery(formValue);
      const keys = Object.keys(query);
      expect(keys.includes("queryWithOrFilters")).toEqual(true);
      expect(query["queryWithOrFilters"]).toEqual(
        "digitalUiStatus=(I OR S)#nonDigitalUiStatus=(I OR S)"
      );
    });
    it("digitalUiStatus  for digital draft and submit", () => {
      let formValue = {};
      formValue["status"] = [
        { id: "DI", count: 0, value: "Digital", selected: true },
        { id: "I", count: 0, value: "Draft", selected: true },
        { id: "S", count: 0, value: "Submitted", selected: true },
      ];
      const query = service.generateQuery(formValue);
      const keys = Object.keys(query);
      expect(keys.includes("queryWithOrFilters")).toEqual(true);
      expect(query["queryWithOrFilters"]).toEqual("digitalUiStatus=(I OR S)");
    });
    it("set all form values", () => {
      let formValue = {
        programCode: [
          {
            id: "PD",
            count: 0,
            value: "PD",
            selected: true,
          },
          false,
          {
            id: "SC",
            count: 14224,
            value: "Store Coupon",
            selected: true,
          },
          false,
          false,
        ],
        deliveryChannel: [
          {
            id: "CC",
            count: 4860,
            value: "Clip and Click",
            selected: true,
          },
          false,
          false,
          {
            id: "Digital Only-In Ad",
            count: 2598,
            value: "Digital Only-In Ad",
            selected: true,
          },
          {
            id: "Digital Only-Not In Ad",
            count: 1053,
            value: "Digital Only-Not In Ad",
            selected: true,
          },
        ],
        offerType: [
          {
            id: "ITEM_DISCOUNT",
            count: 0,
            value: "Item Discount",
            selected: true,
          },
          {
            id: "BUYX_GETX",
            count: 0,
            value: "Buy X Get X",
            selected: true,
          },
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
        ],
        discountType: [
          false,
          false,
          false,
          false,
          false,
          {
            id: "PERCENT_OFF_ITEMS",
            count: 0,
            value: "Percent Off",
            selected: true,
          },
        ],
        divisions: [
          {
            id: "Acme",
            count: 0,
            value: "Acme",
            selected: true,
          },
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
          false,
        ],
        status: [
          {
            id: "DI",
            count: 0,
            value: "Digital",
            selected: true,
          },
          {
            id: "NDI",
            count: 0,
            value: "Non-Digital",
            selected: true,
          },
          {
            id: "I",
            count: 0,
            value: "Draft",
            selected: true,
          },
          {
            id: "S",
            count: 0,
            value: "Submitted",
            selected: true,
          },
          false,
          false,
          {
            id: "E",
            count: 0,
            value: "Editing",
            selected: true,
          },
          false,
          false,
          false,
          false,
        ],
        podStatus: [{
          color: "",
          count: 16,
          id: "true",
          selected: false,
          value: "Yes"
        },
        {
          color: "",
          count: 18,
          id: "false",
          selected: true,
          value: "No"
        }
        ],
        divisionRogCds: {"Portland":[false],"Seattle":[false,false,false],"Acme":[{"id":"ACME","count":150,"value":"ACME","selected":true}],"Haggen":[false],"JewelOsco":[false],"Shaws":[false,false],"InterMtn":[false],"Southwest":[false,false],"Norcal":[false,false],"SoCal":[false,false,false],"Denver":[false],"Eastern":[false],"Southern":[false,false]}
      };
      const query = service.generateQuery(formValue);
    });
  });

  describe("addDeliveryChannelFilter", () => {
    it("should make expected calls", () => {
      let queryWithOrFilters = ["adType=(IA)"];
      service.addDeliveryChannelFilter(queryWithOrFilters,{adType:'IA',categories:"",deliveryChannel:"CC"});
      expect(queryWithOrFilters).toEqual(["adType=(IA)#deliveryChannel=(CC)"])
    });
    it("should trace else when no delivery channel", () => {
      let queryWithOrFilters = [];
      service.addDeliveryChannelFilter(queryWithOrFilters,{adType:'',categories:"",deliveryChannel:""});
      expect(queryWithOrFilters).toEqual([])
    });
  });

  describe("sortProperties", () => {
    it("should make expected calls sortProperties", () => {
      let programTypes = [
        {
            "code": "KS Test",
            "name": "KSTest"
        },
        {
            "code": "WOD Details",
            "name": "WOD"
        },
        {
            "code": "POD details",
            "name": "POD"
        },
        {
            "code": "Free Details",
            "name": "Free"
        },
        {
            "code": "Charity Offer Details",
            "name": "Charity Offer"
        },
        {
            "code": "Cents Off Details",
            "name": "Cents Off"
        },
        {
            "code": "Test11234567 details",
            "name": "Test123"
        },
        {
            "code": "testing",
            "name": "test GR"
        }
    ];
      service.sortProperties(programTypes, 'code', true, false);
    });
    it("should make expected calls sortProperties", () => {
      let programTypes = [
        {
            "code": "KS Test",
            "name": "KSTest"
        },
        {
            "code": "WOD Details",
            "name": "WOD"
        },
        {
            "code": "POD details",
            "name": "POD"
        },
        {
            "code": "Free Details",
            "name": "Free"
        },
        {
            "code": "Charity Offer Details",
            "name": "Charity Offer"
        },
        {
            "code": "Cents Off Details",
            "name": "Cents Off"
        },
        {
            "code": "Test11234567 details",
            "name": "Test123"
        },
        {
            "code": "testing",
            "name": "test GR"
        }
    ];
      service.sortProperties(programTypes, 'code', false, false);
    });
   
  });

  describe("getQueryForDatesSearch", () => {
    it("should make expected calls", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub,'getQuery').and.returnValue("createUserId=(nling05*);limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;createTimeStamp=Today");
      spyOn(queryGeneratorStub,'pushParam');
      const rangeDates = [
        'effectiveStartDate',
        'createTimeStamp',
        'lastUpdateTimestamp',
      ];
      service.getQueryForDatesSearch();
      expect(queryGeneratorStub.pushParam).toHaveBeenCalled();
      expect(queryGeneratorStub.getQuery).not.toEqual('createUserId=(nling05*);limit=100;sortBy=lastUpdateTimestampDESC;createdAppId=OMS;createTimeStamp=Today')
    });
  });

  describe("setIsAllSubprogramsSelected", () => {
    it("setisAllSubprogramsSelected", () => {
      service.isAllDivisionsSelected = new BehaviorSubject(false);
      service.setIsAllSubprogramsSelected(1);
    });
    it("getIsAllSubprogramsSelected", () => {
      service.isAllDivisionsSelected = new BehaviorSubject(false);
      service.getIsAllSubprogramsSelected();
    });
  });
  describe("reqOffersObjkey", () => {
    it("should return programCodeSelected in lowercase when programCodeSelected is not BPD", () => {
      service.programCodeSelected = "SC";
      expect(service.reqOffersObjkey).toEqual("sc");
    });

    it("should return 'spd' when programCodeSelected is BPD", () => {
      service.programCodeSelected = "BPD";
      expect(service.reqOffersObjkey).toEqual("spd");
    });

    it("should return programCodeSelected in lowercase when programCodeSelected is SPD", () => {
      service.programCodeSelected = "SPD";
      expect(service.reqOffersObjkey).toEqual("spd");
    });

    it("should return programCodeSelected in lowercase when programCodeSelected is GR", () => {
      service.programCodeSelected = "GR";
      expect(service.reqOffersObjkey).toEqual("gr");
    });

    it("should return programCodeSelected in lowercase when programCodeSelected is MF", () => {
      service.programCodeSelected = "MF";
      expect(service.reqOffersObjkey).toEqual("mf");
    });
  });
  describe("getOfferRequestStatus", () => {
    it("should return offerRequestStatuses when programCodeSelected is SC", () => {
      service.programCodeSelected = "SC";
      service.offerRequestStatuses = { DI: "Digital", NDI: "Non-Digital" };
      const result = service.getOfferRequestStatus();
      expect(result).toEqual({ DI: "Digital", NDI: "Non-Digital" });
    });

    it("should return offerRequestStatuses for specific program code", () => {
      service.programCodeSelected = "SPD";
      service.initialData = {
        offerRequestStatusesSPD: { DI: "Digital SPD", NDI: "Non-Digital SPD" },
      };
      const result = service.getOfferRequestStatus();
      expect(result).toEqual({ DI: "Digital SPD", NDI: "Non-Digital SPD" });
    });

    it("should return offerRequestStatuses for another specific program code", () => {
      service.programCodeSelected = "GR";
      service.initialData = {
        offerRequestStatusesGR: { DI: "Digital GR", NDI: "Non-Digital GR" },
      };
      const result = service.getOfferRequestStatus();
      expect(result).toEqual({ DI: "Digital GR", NDI: "Non-Digital GR" });
    });

    it("should return offerRequestStatuses for MF program code", () => {
      service.programCodeSelected = "MF";
      service.initialData = {
        offerRequestStatusesMF: { DI: "Digital MF", NDI: "Non-Digital MF" },
      };
      const result = service.getOfferRequestStatus();
      expect(result).toEqual({ DI: "Digital MF", NDI: "Non-Digital MF" });
    });

    it("should return offerRequestStatuses for BPD program code", () => {
      service.programCodeSelected = "BPD";
      service.initialData = {
        offerRequestStatusesSPD: { DI: "Digital BPD", NDI: "Non-Digital BPD" },
      };
      const result = service.getOfferRequestStatus();
      expect(result).toEqual(undefined);
    });
  });

  describe("getOfferRequestFailedStatus", () => {
    it("should return offerRequestStatuses when programCodeSelected is SC", () => {
      service.programCodeSelected = "SC";
      service.offerRequestStatuses = { DI: "Digital", NDI: "Non-Digital" };
      const result = service.getOfferRequestFailedStatus();
      expect(result).toEqual({ DI: "Digital", NDI: "Non-Digital" });
    });

    it("should return offerRequestFailures for specific program code", () => {
      service.programCodeSelected = "SPD";
      service.initialData = {
        offerRequestFailuresSPD: { DI: "Digital SPD", NDI: "Non-Digital SPD" },
      };
      const result = service.getOfferRequestFailedStatus();
      expect(result).toEqual({ DI: "Digital SPD", NDI: "Non-Digital SPD" });
    });

    it("should return offerRequestFailures for another specific program code", () => {
      service.programCodeSelected = "GR";
      service.initialData = {
        offerRequestFailuresGR: { DI: "Digital GR", NDI: "Non-Digital GR" },
      };
      const result = service.getOfferRequestFailedStatus();
      expect(result).toEqual({ DI: "Digital GR", NDI: "Non-Digital GR" });
    });

    it("should return offerRequestFailures for MF program code", () => {
      service.programCodeSelected = "MF";
      service.initialData = {
        offerRequestFailuresMF: { DI: "Digital MF", NDI: "Non-Digital MF" },
      };
      const result = service.getOfferRequestFailedStatus();
      expect(result).toEqual({ DI: "Digital MF", NDI: "Non-Digital MF" });
    });

    it("should return offerRequestFailures for BPD program code", () => {
      service.programCodeSelected = "BPD";
      service.initialData = {
        offerRequestFailuresSPD: { DI: "Digital BPD", NDI: "Non-Digital BPD" },
      };
      const result = service.getOfferRequestFailedStatus();
    });
  });
  describe("getOfferRequestSources", () => {
    it("should return sources from initial data", () => {
      service.initialData = {
        sources: { source1: "Source 1", source2: "Source 2" },
      };
      const result = service.getOfferRequestSources();
    });

    it("should return undefined if sources are not available in initial data", () => {
      service.initialData = {};
      const result = service.getOfferRequestSources();
      expect(result).toBeUndefined();
    });
  });
  describe("getOfferFailedStatus", () => {
    it("should return offerFailures for SC program code", () => {
      service.programCodeSelected = "SC";
      service.programCodeChecked = { SC: true };
      service.initialData = {
        offerFailures: { SC: { DI: "Digital SC", NDI: "Non-Digital SC" } },
      };
      const result = service.getOfferFailedStatus();
    });

    it("should return offerFailures for SPD program code", () => {
      service.programCodeSelected = "SPD";
      service.programCodeChecked = { SPD: true };
      service.initialData = {
        offerFailuresSPD: { DI: "Digital SPD", NDI: "Non-Digital SPD" },
      };
      const result = service.getOfferFailedStatus();
    });

    it("should return offerFailures for GR program code", () => {
      service.programCodeSelected = "GR";
      service.programCodeChecked = { GR: true };
      service.initialData = {
        offerFailuresGR: { DI: "Digital GR", NDI: "Non-Digital GR" },
      };
      const result = service.getOfferFailedStatus();
    });

    it("should return offerFailures for MF program code", () => {
      service.programCodeSelected = "MF";
      service.programCodeChecked = { MF: true };
      service.initialData = {
        offerFailuresMF: { DI: "Digital MF", NDI: "Non-Digital MF" },
      };
      const result = service.getOfferFailedStatus();
    });

    it("should return offerFailures for BPD program code", () => {
      service.programCodeSelected = "BPD";
      service.programCodeChecked = { BPD: true };
      service.initialData = {
        offerFailuresSPD: { DI: "Digital BPD", NDI: "Non-Digital BPD" },
      };
      const result = service.getOfferFailedStatus();
    });

    it("should return combined offerFailures for multiple selected program codes", () => {
      service.programCodeChecked = { SC: true, GR: true };
      service.initialData = {
        offerFailures: { SC: { DI: "Digital SC", NDI: "Non-Digital SC" } },
        offerFailuresGR: { DI: "Digital GR", NDI: "Non-Digital GR" },
      };
      const result = service.getOfferFailedStatus();
    });
  });
  describe("getOfferTemplateStatus", () => {
    it("should return offerTemplateStatus from initial data", () => {
      service.initialData = {
        offerTemplateStatus: { template1: "Template 1", template2: "Template 2" },
      };
      const result = service.getOfferTemplateStatus();
      expect(result).toEqual({ template1: "Template 1", template2: "Template 2" });
    });

    it("should return undefined if offerTemplateStatus is not available in initial data", () => {
      service.initialData = {};
      const result = service.getOfferTemplateStatus();
      expect(result).toBeUndefined();
    });
  });
  describe("removeFilterSelectedFromQuery", () => {
    it("should call queryGenerator.removeParameters with correct arguments", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "removeParameters");
      const removeFilter = ["filter1", "filter2"];
      service.removeFilterSelectedFromQuery(removeFilter, {});
      expect(spy).toHaveBeenCalledWith(removeFilter);
    });

    it("should not call queryGenerator.removeParameters if removeFilter is empty", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "removeParameters");
      const removeFilter = [];
      service.removeFilterSelectedFromQuery(removeFilter, {});
    });

    it("should handle form with controls correctly", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "removeParameters");
      const removeFilter = ["filter1"];
      const form = new UntypedFormGroup({
        filter1: new UntypedFormArray([new UntypedFormControl("value1")]),
        filter2: new UntypedFormArray([new UntypedFormControl("value2")]),
      });
      service.removeFilterSelectedFromQuery(removeFilter, form);
      expect(spy).toHaveBeenCalledWith(removeFilter);
    });

    it("should handle form with no matching controls", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      const spy = spyOn(queryGeneratorStub, "removeParameters");
      const removeFilter = ["filter3"];
      const form = new UntypedFormGroup({
        filter1: new UntypedFormArray([new UntypedFormControl("value1")]),
        filter2: new UntypedFormArray([new UntypedFormControl("value2")]),
      });
      service.removeFilterSelectedFromQuery(removeFilter, form);
      expect(spy).toHaveBeenCalledWith(removeFilter);
    });
  });
  describe("resetFilterValues", () => {
    it("should reset values for divisionRogCds control", () => {
      const controlsArr = new UntypedFormArray([
        new UntypedFormGroup({
          divisionRogCds: new UntypedFormControl(true),
        }),
      ]);
      service.resetFilterValues(controlsArr, "divisionRogCds");
    });

    it("should reset values for other controls", () => {
      const controlsArr = new UntypedFormArray([
        new UntypedFormGroup({
          otherControl: new UntypedFormControl(true),
        }),
      ]);
      spyOn(service, "resetCtrlValues");
      service.resetFilterValues(controlsArr, "otherControl");
      expect(service.resetCtrlValues).toHaveBeenCalledWith(controlsArr);
    });

    it("should handle empty controls array", () => {
      const controlsArr = new UntypedFormArray([]);
      service.resetFilterValues(controlsArr, "divisionRogCds");
      expect(controlsArr.length).toBe(0);
    });

    it("should handle null controls array", () => {
      const controlsArr = null;
      if (controlsArr) {
        service.resetFilterValues(controlsArr, "divisionRogCds");
      }
      expect(controlsArr).toBeNull();
    });

    it("should handle undefined controls array", () => {
      const controlsArr = undefined;
      if (controlsArr) {
        service.resetFilterValues(controlsArr, "divisionRogCds");
      }
      expect(controlsArr).toBeUndefined();
    });
  });
  describe("getOfferStatus", () => {
    it("should return offerStatuses for SC program code", () => {
      service.programCodeSelected = "SC";
      service.programCodeChecked = { SC: true };
      service.initialData = {
        offerStatuses: { DI: "Digital SC", NDI: "Non-Digital SC" },
      };
      const result = service.getOfferStatus();
    });

    it("should return offerStatuses for SPD program code", () => {
      service.programCodeSelected = "SPD";
      service.programCodeChecked = { SPD: true };
      service.initialData = {
        offerStatusesSPD: { DI: "Digital SPD", NDI: "Non-Digital SPD" },
      };
      const result = service.getOfferStatus();
    });

    it("should return offerStatuses for GR program code", () => {
      service.programCodeSelected = "GR";
      service.programCodeChecked = { GR: true };
      service.initialData = {
        offerStatusesGR: { DI: "Digital GR", NDI: "Non-Digital GR" },
      };
      const result = service.getOfferStatus();
    });

    it("should return offerStatuses for MF program code", () => {
      service.programCodeSelected = "MF";
      service.programCodeChecked = { MF: true };
      service.initialData = {
        offerStatusesMF: { DI: "Digital MF", NDI: "Non-Digital MF" },
      };
      const result = service.getOfferStatus();
    });

    it("should return offerStatuses for BPD program code", () => {
      service.programCodeSelected = "BPD";
      service.programCodeChecked = { BPD: true };
      service.initialData = {
        offerStatusesSPD: { DI: "Digital BPD", NDI: "Non-Digital BPD" },
      };
      const result = service.getOfferStatus();
    });

    it("should return combined offerStatuses for multiple selected program codes", () => {
      service.programCodeChecked = { SC: true, GR: true };
      service.initialData = {
        offerStatuses: { SC: { DI: "Digital SC", NDI: "Non-Digital SC" } },
        offerStatusesGR: { DI: "Digital GR", NDI: "Non-Digital GR" },
      };
      const result = service.getOfferStatus();
    });
  });
  describe("getDeliveryChannelsOnPC", () => {
    it("should return delivery channels for SC program code", () => {
      service.initialData = {
        offerDeliveryChannels: { SC: { DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" } },
      };
      const result = service.getDeliveryChannelsOnPC("SC");
    });

    it("should return delivery channels for SPD program code", () => {
      service.initialData = {
        offerDeliveryChannelsSPD: { DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" },
      };
      const result = service.getDeliveryChannelsOnPC("SPD");
      expect(result).toEqual({ DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" });
    });

    it("should return delivery channels for GR program code", () => {
      service.initialData = {
        offerDeliveryChannelsGR: { DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" },
      };
      const result = service.getDeliveryChannelsOnPC("GR");
      expect(result).toEqual({ DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" });
    });

    it("should return delivery channels for MF program code", () => {
      service.initialData = {
        offerDeliveryChannelsMF: { DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" },
      };
      const result = service.getDeliveryChannelsOnPC("MF");
      expect(result).toEqual({ DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" });
    });

    it("should return delivery channels for BPD program code", () => {
      service.initialData = {
        offerDeliveryChannelsSPD: { DC1: "Delivery Channel 1", DC2: "Delivery Channel 2" },
      };
      const result = service.getDeliveryChannelsOnPC("BPD");
    });

    it("should return empty object if no delivery channels are available for the program code", () => {
      service.initialData = {};
      const result = service.getDeliveryChannelsOnPC("SC");
      expect(result).toEqual({});
    });
  });
  describe("populateFacet", () => {
    it("should populate facet items correctly", () => {
      const facetSelect = {
        offerPrograms: {
          PD: 0,
          SPD: 0,
          SC: 14202,
          GR: 0,
          MF: 144,
        },
        offerDeliveryChannels: {
          CC: 4858,
          IS: 3278,
          PO: 2574,
          "Digital Only-In Ad": 2586,
          "Digital Only-Not In Ad": 1050,
        },
        offerType: {
          ITEM_DISCOUNT: 0,
          BUYX_GETX: 0,
          BUYX_GETY: 0,
          MEAL_DEAL: 0,
          BUNDLE: 0,
          MUST_BUY: 0,
          FAB_5_OR_SCORE_4: 0,
          WOD_OR_POD: 0,
          STORE_CLOSURE: 0,
          REWARDS_ACCUMULATION: 0,
          REWARDS_FLAT: 0,
          CONTINUITY: 0,
          INSTANT_WIN: 0,
          ALASKA_AIRMILES: 0,
          CUSTOM: 0,
        },
        amountTypes: {
          AMOUNT_OFF: 0,
          AMOUNT_OFF_WEIGHT_VOLUME: 0,
          FREE: 0,
          PRICE_POINT_ITEMS: 0,
          PRICE_POINT_WEIGHT_VOLUME: 0,
          PERCENT_OFF_ITEMS: 0,
        },
        divisions: {
          "AM::Acme": 0,
          "CORP::Corporate": 0,
          "DE::Denver": 0,
          "ES::Eastern": 0,
          "HG::Haggen": 0,
          "IM::Intermountain": 0,
          "JW::Jewel": 0,
          "NC::Norcal": 0,
          "PT::Portland": 0,
          "SE::Seattle": 0,
          "SH::Shaws/Star Market": 0,
          "SC::SoCal": 0,
          "SO::Southern": 0,
          "SW::Southwest": 0,
        },
        offerStatuses: {
          DI: 0,
          NDI: 0,
          I: 0,
          S: 0,
          A: 0,
          P: 0,
          E: 0,
          U: 0,
          D: 0,
          C: 0,
          R: 0,
        },
        podStatus: {
          true: "yes",
          false: "no",
        },
        digital: {
          false: "Non-Digital",
          true: "Digital",
        },
        offerStatus: {
          DE: "Deployed",
          CN: "Canceled",
        },
        divisionId: {
          "AM::Acme": 0,
          "CORP::Corporate": 0,
        },
        isProofed: {
          yes: "true",
          no: "false",
        },
        color: {
          green: "true",
          orange: "false",
        },
      };

      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue(
        "limit=100;sortBy=lastUpdateTimestampDESC;podStatus=(true OR false);requestType=(ITEM_DISCOUNT OR FAB_5_OR_SCORE_4 OR WOD_OR_POD);createdAppId=OMS;"
      );
      spyOn(service, "getOfferRequestStatus").and.returnValue({ DI: "I" });
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("CC OR IS");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });

    it("should handle empty facetSelect", () => {
      const facetSelect = {};
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("");
      spyOn(service, "getOfferRequestStatus").and.returnValue({});
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);

      expect(service.facetItems).toEqual({});
    });
  });
  describe("addOfferStatusFilter", () => {
    it("should add offer status filter to queryWithOrFilters", () => {
      let queryWithOrFilters = [];
      const facetsSelected = {
        isDeferDeploy: "DD",
        offerStatusType: "OST",
        minusOfferFailedState: "MOFS",
        offerStatus: "OS",
        depPubFailedState: "DPFS",
      };
      service.addOfferStatusFilter(queryWithOrFilters, facetsSelected);
    });

    it("should not add offer status filter if no matching keys", () => {
      let queryWithOrFilters = [];
      const facetsSelected = {
        someOtherKey: "value",
      };
      service.addOfferStatusFilter(queryWithOrFilters, facetsSelected);
      expect(queryWithOrFilters).toEqual([]);
    });

    it("should handle empty facetsSelected", () => {
      let queryWithOrFilters = [];
      const facetsSelected = {};
      service.addOfferStatusFilter(queryWithOrFilters, facetsSelected);
      expect(queryWithOrFilters).toEqual([]);
    });

    it("should remove offerStatusMatcedKeys from facetsSelected", () => {
      let queryWithOrFilters = [];
      const facetsSelected = {
        isDeferDeploy: "DD",
        offerStatusType: "OST",
        minusOfferFailedState: "MOFS",
        offerStatus: "OS",
        depPubFailedState: "DPFS",
        someOtherKey: "value",
      };
      service.addOfferStatusFilter(queryWithOrFilters, facetsSelected);
    });
  });
  describe("setDivsionStateFacetItems", () => {
    it("should set divsionStateFacetItems correctly", () => {
      const divsionStateFacetItems = {
        "DENVER": {
          "VLAS": 12,
          "SDEN": 1
        }
      };
      service.setDivsionStateFacetItems(divsionStateFacetItems);
      expect(service.getdivsionStateFacetItems()).toEqual(divsionStateFacetItems);
    });

    it("should handle empty divsionStateFacetItems", () => {
      const divsionStateFacetItems = {};
      service.setDivsionStateFacetItems(divsionStateFacetItems);
      expect(service.getdivsionStateFacetItems()).toEqual(divsionStateFacetItems);
    });

    it("should handle null divsionStateFacetItems", () => {
      const divsionStateFacetItems = null;
      service.setDivsionStateFacetItems(divsionStateFacetItems);
      expect(service.getdivsionStateFacetItems()).toBeNull();
    });

    it("should handle undefined divsionStateFacetItems", () => {
      const divsionStateFacetItems = undefined;
      service.setDivsionStateFacetItems(divsionStateFacetItems);
      expect(service.getdivsionStateFacetItems()).toBeUndefined();
    });
  });
  describe("resetProgramCdChecked", () => {
    it("should reset programCodeChecked to an empty object", () => {
      service.programCodeChecked = { SC: true, GR: true };
      service.resetProgramCdChecked();
      expect(service.programCodeChecked).toEqual({});
    });

    it("should handle already empty programCodeChecked", () => {
      service.programCodeChecked = {};
      service.resetProgramCdChecked();
      expect(service.programCodeChecked).toEqual({});
    });

    it("should handle null programCodeChecked", () => {
      service.programCodeChecked = null;
      service.resetProgramCdChecked();
      expect(service.programCodeChecked).toEqual({});
    });

    it("should handle undefined programCodeChecked", () => {
      service.programCodeChecked = undefined;
      service.resetProgramCdChecked();
      expect(service.programCodeChecked).toEqual({});
    });
  });
  describe("setDigitalProperties", () => {
    it("should set digital properties correctly", () => {
      service.setDigitalProperties();
    });
  });
  describe("getDeliveryChannelValue", () => {
    it("should return true if deliveryChannel matches value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("IA OR NIA");
      const result = service.getDeliveryChannelValue("IA");
      expect(result).toBeTrue();
    });

    it("should return false if deliveryChannel does not match value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("IA OR NIA");
      const result = service.getDeliveryChannelValue("CC");
      expect(result).toBeFalse();
    });

    it("should return true if adType matches value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue(null);
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue(["adType=(IA)"]);
      const result = service.getDeliveryChannelValue("IA");
    });

    it("should return false if adType does not match value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue(null);
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue(["adType=(IA)"]);
      const result = service.getDeliveryChannelValue("CC");
    });

    it("should handle empty deliveryChannel and adType", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue(null);
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);
      const result = service.getDeliveryChannelValue("IA");
    });

    it("should handle null value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("IA OR NIA");
      const result = service.getDeliveryChannelValue(null);
      expect(result).toBeFalse();
    });

    it("should handle undefined value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("IA OR NIA");
      const result = service.getDeliveryChannelValue(undefined);
      expect(result).toBeFalse();
    });
  });
  describe("retainProgramCodeSelected", () => {
    let permissionsServiceStub: PermissionsService;
    let lsStub: jasmine.Spy;

    beforeEach(() => {
      lsStub = spyOn(localStorage, 'getItem');
      permissionsServiceStub = TestBed.inject(PermissionsService);
    });

    it("should set programCodeSelected to GR when VIEW_GR_SPD_OFFER_REQUESTS permission is present", () => {
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({ "VIEW_GR_SPD_OFFER_REQUESTS": { name: '' } });
      lsStub.and.returnValue(null);
      service.retainProgramCodeSelected();
      expect(service.programCodeSelected).toEqual('GR');
    });

    it("should set programCodeSelected to SC when ADMIN permission is present", () => {
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({ "ADMIN": { name: '' } });
      lsStub.and.returnValue(null);
      service.retainProgramCodeSelected();
      expect(service.programCodeSelected).toEqual('SC');
    });

    it("should retain programCodeSelected from localStorage if present", () => {
      lsStub.and.returnValue('SPD');
      service.retainProgramCodeSelected();
      expect(service.programCodeSelected).toEqual('SPD');
    });

    it("should set programCodeChecked with the retained programCodeSelected", () => {
      lsStub.and.returnValue('MF');
      service.retainProgramCodeSelected();
      expect(service.programCodeChecked).toEqual({ 'MF': true });
    });

    it("should handle case when no permissions and no localStorage value", () => {
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({});
      lsStub.and.returnValue(null);
      service.retainProgramCodeSelected();
      expect(service.programCodeSelected).toBeUndefined();
    });
  });
  describe("getQueryParamValue", () => {
    it("should return the correct query parameter value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("param1=value1;param2=value2;param3=value3;");
      const result = service.getQueryParamValue("param2");
      expect(result).toEqual(["param2=value2"]);
    });

    it("should return an empty array if the parameter is not found", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("param1=value1;param2=value2;param3=value3;");
      const result = service.getQueryParamValue("param4");
      expect(result).toEqual([]);
    });

    it("should handle an empty query string", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("");
      const result = service.getQueryParamValue("param1");
      expect(result).toEqual([]);
    });

    it("should handle a query string with no matching parameters", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("param1=value1;param2=value2;param3=value3;");
      const result = service.getQueryParamValue("param4");
      expect(result).toEqual([]);
    });

    it("should handle a query string with multiple matching parameters", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("param1=value1;param2=value2;param2=value3;");
      const result = service.getQueryParamValue("param2");
      expect(result).toEqual(["param2=value2", "param2=value3"]);
    });
  });
  describe("getOfferProgSubTypeSelectedValue", () => {
    it("should return true if progSubType matches value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("subType1 OR subType2");
      const result = service.getOfferProgSubTypeSelectedValue("progSubType", "subType1");
      expect(result).toBeTrue();
    });

    it("should return false if progSubType does not match value", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("subType1 OR subType2");
      const result = service.getOfferProgSubTypeSelectedValue("progSubType", "subType3");
      expect(result).toBeFalse();
    });

    it("should handle empty progSubType", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("");
      const result = service.getOfferProgSubTypeSelectedValue("progSubType", "subType1");
      expect(result).toBeFalse();
    });

    xit("should handle null progSubType", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue(null);
      const result = service.getOfferProgSubTypeSelectedValue("progSubType", "subType1");
      expect(result).toBeFalse();
    });

    xit("should handle undefined progSubType", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue(undefined);
      const result = service.getOfferProgSubTypeSelectedValue("progSubType", "subType1");
      expect(result).toBeFalse();
    });
  });
  
  describe("getSelectedValue", () => {
    it("should return true if key is 'createdAppId' and isUPPFieldSearchEnabled is true", () => {
      const featureFlagsServiceStub: FeatureFlagsService = TestBed.inject(FeatureFlagsService);
      spyOn(featureFlagsServiceStub, "isUPPFieldSearchEnabled").and.returnValue(true);
      const result = service.getSelectedValue("createdAppId", "someValue");
    });

    it("should return false if key is 'createdAppId' and isUPPFieldSearchEnabled is false", () => {
      const featureFlagsServiceStub: FeatureFlagsService = TestBed.inject(FeatureFlagsService);
      spyOn(featureFlagsServiceStub, "isUPPFieldSearchEnabled").and.returnValue(false);
      const result = service.getSelectedValue("createdAppId", "someValue");
    });

    xit("should return true if key is 'status' and value is 'DI'", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("DI");
      const result = service.getSelectedValue("status", "DI");
      expect(result).toBeTrue();
    });

    xit("should return false if key is 'status' and value is 'NDI'", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("DI" as unknown as string);
      const result = service.getSelectedValue("status", "NDI");
      expect(result).toBeFalse();
    });

    it("should return true if key is 'offerStatus' and value matches", () => {
      spyOn(service, "getOfferStatusSelectedValue").and.returnValue(true);
      const result = service.getSelectedValue("offerStatus", "someValue");
      expect(result).toBeTrue();
    });

    it("should return false if key is 'offerStatus' and value does not match", () => {
      spyOn(service, "getOfferStatusSelectedValue").and.returnValue(false);
      const result = service.getSelectedValue("offerStatus", "someValue");
      expect(result).toBeFalse();
    });

    it("should return true if key is 'progSubType' and value matches", () => {
      spyOn(service, "getOfferProgSubTypeSelectedValue").and.returnValue(true);
      const result = service.getSelectedValue("progSubType", "someValue");
      expect(result).toBeTrue();
    });

    it("should return false if key is 'progSubType' and value does not match", () => {
      spyOn(service, "getOfferProgSubTypeSelectedValue").and.returnValue(false);
      const result = service.getSelectedValue("progSubType", "someValue");
      expect(result).toBeFalse();
    });

    it("should return true if key is 'podStatus' and value matches", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("NULLFALSE" as unknown as string);
      const result = service.getSelectedValue("podStatus", "false");
      expect(result).toBeTrue();
    });

    it("should return false if key is 'podStatus' and value does not match due to missing query", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue(null);
      const result = service.getSelectedValue("podStatus", "false");
      expect(result).toBeFalse();
    });

    it("should return true if key is 'isInAd' and value matches", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("IA");
      const result = service.getSelectedValue("isInAd", "Y");
    });

    it("should return false if key is 'isInAd' and value does not match", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getInputValue").and.returnValue("IA");
      const result = service.getSelectedValue("isInAd", "N");
    });

    it("should return true if key is 'deliveryChannel' and value matches", () => {
      spyOn(service, "getDeliveryChannelValue").and.returnValue(true);
      const result = service.getSelectedValue("deliveryChannel", "someValue");
      expect(result).toBeTrue();
    });

    it("should return false if key is 'deliveryChannel' and value does not match", () => {
      spyOn(service, "getDeliveryChannelValue").and.returnValue(false);
      const result = service.getSelectedValue("deliveryChannel", "someValue");
      expect(result).toBeFalse();
    });
    

    it("should return true if key is 'programCd' and value matches", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("programCode=(someValue)");
      const result = service.getSelectedValue("programCd", "someValue");
    });

    it("should return false if key is 'programCd' and value does not match", () => {
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue("programCode=(someOtherValue)");
      const result = service.getSelectedValue("programCd", "someValue");
      expect(result).toBeFalse();
    });

    it("should return false if key is not recognized", () => {
      const result = service.getSelectedValue("unknownKey", "someValue");
      expect(result).toBeFalse();
    });
  });
  describe("getProgramCodesSelected", () => {
    it("should return an array of selected program codes", () => {
      service.programCodeChecked = { SC: true, GR: true, MF: false };
      const result = service.getProgramCodesSelected();
      expect(result).toEqual(["SC", "GR"]);
    });

    it("should return an empty array if no program codes are selected", () => {
      service.programCodeChecked = { SC: false, GR: false, MF: false };
      const result = service.getProgramCodesSelected();
      expect(result).toEqual([]);
    });

    it("should handle an empty programCodeChecked object", () => {
      service.programCodeChecked = {};
      const result = service.getProgramCodesSelected();
      expect(result).toEqual([]);
    });
  });
  describe("enableBatchActionForOffers", () => {
    let permissionsServiceStub: PermissionsService;

    beforeEach(() => {
      permissionsServiceStub = TestBed.inject(PermissionsService);
    });

    it("should enable batch action for offers when only one program code is selected and permissions are present", () => {
      service.programCodeChecked = { SC: true };
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        "VIEW_OFFER_REQUESTS": { name: "VIEW_OFFER_REQUESTS" } as Permission,
        "DO_STORE_COUPON_OFFERS": { name: "DO_STORE_COUPON_OFFERS" } as Permission,
      });
      const result = service.enableBatchActionForOffers();
    });

    it("should disable batch action for offers when multiple program codes are selected", () => {
      service.programCodeChecked = { SC: true, GR: true };
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        "VIEW_OFFER_REQUESTS": { name: "VIEW_OFFER_REQUESTS" } as Permission,
        "DO_STORE_COUPON_OFFERS": { name: "DO_STORE_COUPON_OFFERS" } as Permission,
      });
      const result = service.enableBatchActionForOffers();
    });

    it("should disable batch action for offers when no program codes are selected", () => {
      service.programCodeChecked = {};
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        "VIEW_OFFER_REQUESTS": { name: "VIEW_OFFER_REQUESTS" } as Permission,
        "DO_STORE_COUPON_OFFERS": { name: "DO_STORE_COUPON_OFFERS" } as Permission,
      });
      const result = service.enableBatchActionForOffers();
    });

    it("should disable batch action for offers when permissions are not present", () => {
      service.programCodeChecked = { SC: true };
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({});
      const result = service.enableBatchActionForOffers();
    });

    it("should enable batch action for offers when only one program code is selected and VIEW_OFFER_REQUESTS permission is present", () => {
      service.programCodeChecked = { SC: true };
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        "VIEW_OFFER_REQUESTS": { name: "VIEW_OFFER_REQUESTS" } as Permission,
      });
      const result = service.enableBatchActionForOffers();
    });

    it("should enable batch action for offers when only one program code is selected and DO_STORE_COUPON_OFFERS permission is present", () => {
      service.programCodeChecked = { SC: true };
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        "DO_STORE_COUPON_OFFERS": { name: "DO_STORE_COUPON_OFFERS" } as Permission,
      });
      const result = service.enableBatchActionForOffers();
    });

    it("should disable batch action for offers when only one program code is selected and permissions are not relevant", () => {
      service.programCodeChecked = { SC: true };
      spyOn(permissionsServiceStub, "getPermissions").and.returnValue({
        "VIEW_OFFER_REQUESTS": { name: "VIEW_OFFER_REQUESTS" } as Permission,
      });
      const result = service.enableBatchActionForOffers();
    });
  });

  describe("setFacetsObjForOfferStatus", () => {
    it("should set facets object for offer status when facet id is not IPU", () => {
      const facet = { id: "RU" };
      const obj = {};
      const item = "someItem";
      service.programCodeChecked = { MF: true };
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should set facets object for offer status when facet id is DD", () => {
      const facet = { id: "DD" };
      const obj = {};
      const item = "someItem";
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should set facets object for offer status when facet id is IPU", () => {
      const facet = { id: "IPU" };
      const obj = { minusOfferFailedState: "someState" };
      const item = "someItem";
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should handle null facet object", () => {
      const facet = null;
      const obj = {};
      const item = "someItem";
      if (facet && facet.id) {
        service.setFacetsObjForOfferStatus(facet, obj, item);
      }
      expect(obj).toEqual({});
    });

    it("should handle undefined facet object", () => {
      const facet = undefined;
      const obj = {};
      const item = "someItem";
      if (facet && facet.id) {
        service.setFacetsObjForOfferStatus(facet, obj, item);
      }
      expect(obj).toEqual({});
    });

    it("should handle empty obj object", () => {
      const facet = { id: "RU" };
      const obj = {};
      const item = "someItem";
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should handle null obj object", () => {
      const facet = { id: "RU" };
      let obj = null;
      const item = "someItem";
      if (obj === null) {
        obj = {};
      }
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should handle undefined obj object", () => {
      const facet = { id: "RU" };
      let obj = undefined;
      const item = "someItem";
      if (obj === undefined) {
        obj = {};
      }
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should handle empty item", () => {
      const facet = { id: "RU" };
      const obj = {};
      const item = "";
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should handle null item", () => {
      const facet = { id: "RU" };
      const obj = {};
      const item = null;
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });

    it("should handle undefined item", () => {
      const facet = { id: "RU" };
      const obj = {};
      const item = undefined;
      service.setFacetsObjForOfferStatus(facet, obj, item);
    });
  });
  describe("setFacetProgramSubType", () => {
    it("should set programSubType correctly from initial data", () => {
      service.initialData = {
        offerDetailsProgramSubTypes: { subType1: "Sub Type 1", subType2: "Sub Type 2" },
      };
      service.setFacetProgramSubType();
      expect(service.programSubType).toEqual({ subType1: "Sub Type 1", subType2: "Sub Type 2" });
    });

    it("should handle empty offerDetailsProgramSubTypes in initial data", () => {
      service.initialData = {
        offerDetailsProgramSubTypes: {},
      };
      service.setFacetProgramSubType();
      expect(service.programSubType).toEqual({});
    });

    it("should handle null offerDetailsProgramSubTypes in initial data", () => {
      service.initialData = {
        offerDetailsProgramSubTypes: null,
      };
      service.setFacetProgramSubType();
      expect(service.programSubType).toBeNull();
    });

    it("should handle undefined offerDetailsProgramSubTypes in initial data", () => {
      service.initialData = {
        offerDetailsProgramSubTypes: undefined,
      };
      service.setFacetProgramSubType();
      expect(service.programSubType).toBeUndefined();
    });

    it("should handle missing offerDetailsProgramSubTypes in initial data", () => {
      service.initialData = {};
      service.setFacetProgramSubType();
      expect(service.programSubType).toBeUndefined();
    });
  });
  describe("populateFacet", () => {
    it("should handle programCd key correctly", () => {
      const facetSelect = {
        programCd: {
          PD: 0,
          SPD: 0,
          SC: 14202,
          GR: 0,
          MF: 144,
        },
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue(
        "limit=100;sortBy=lastUpdateTimestampDESC;programCd=(SC OR MF);createdAppId=OMS;"
      );
      spyOn(service, "getOfferRequestStatus").and.returnValue({ DI: "I" });
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("SC OR MF");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });

    it("should handle offerProgramCd key correctly", () => {
      const facetSelect = {
        offerProgramCd: {
          PD: 0,
          SPD: 0,
          SC: 14202,
          GR: 0,
          MF: 144,
        },
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue(
        "limit=100;sortBy=lastUpdateTimestampDESC;offerProgramCd=(SC OR MF);createdAppId=OMS;"
      );
      spyOn(service, "getOfferRequestStatus").and.returnValue({ DI: "I" });
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("SC OR MF");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });

    it("should handle programCode key correctly", () => {
      const facetSelect = {
        programCode: {
          PD: 0,
          SPD: 0,
          SC: 14202,
          GR: 0,
          MF: 144,
        },
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue(
        "limit=100;sortBy=lastUpdateTimestampDESC;programCode=(SC OR MF);createdAppId=OMS;"
      );
      spyOn(service, "getOfferRequestStatus").and.returnValue({ DI: "I" });
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("SC OR MF");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });

    it("should handle other keys correctly", () => {
      const facetSelect = {
        offerType: {
          ITEM_DISCOUNT: 0,
          BUYX_GETX: 0,
          BUYX_GETY: 0,
          MEAL_DEAL: 0,
          BUNDLE: 0,
          MUST_BUY: 0,
          FAB_5_OR_SCORE_4: 0,
          WOD_OR_POD: 0,
          STORE_CLOSURE: 0,
          REWARDS_ACCUMULATION: 0,
          REWARDS_FLAT: 0,
          CONTINUITY: 0,
          INSTANT_WIN: 0,
          ALASKA_AIRMILES: 0,
          CUSTOM: 0,
        },
      };
      const queryGeneratorStub: QueryGenerator = TestBed.inject(QueryGenerator);
      spyOn(queryGeneratorStub, "getQuery").and.returnValue(
        "limit=100;sortBy=lastUpdateTimestampDESC;offerType=(ITEM_DISCOUNT OR BUYX_GETX);createdAppId=OMS;"
      );
      spyOn(service, "getOfferRequestStatus").and.returnValue({ DI: "I" });
      spyOn(queryGeneratorStub, "getQueryFilter").and.returnValue("ITEM_DISCOUNT OR BUYX_GETX");
      spyOn(queryGeneratorStub, "getQueryWithFilter").and.returnValue([]);

      service.populateFacet(facetSelect);
    });
  });
  describe("resetAllDivisionRogs", () => {
    it("should reset all divisionRogCds to false", () => {
      service["divsionStateFacetItems"] = {
        division1: [{ selected: true }, { selected: true }],
        division2: [{ selected: true }, { selected: true }],
      };
      service.resetAllDivisionRogs();
      expect(service["divsionStateFacetItems"].division1.every(item => item.selected === false)).toBeTrue();
      expect(service["divsionStateFacetItems"].division2.every(item => item.selected === false)).toBeTrue();
    });

    it("should handle empty divsionStateFacetItems", () => {
      service["divsionStateFacetItems"] = {};
      service.resetAllDivisionRogs();
      expect(service["divsionStateFacetItems"]).toEqual({});
    });

    it("should handle null divsionStateFacetItems", () => {
      service["divsionStateFacetItems"] = null;
      service.resetAllDivisionRogs();
      expect(service["divsionStateFacetItems"]).toBeNull();
    });

    it("should handle undefined divsionStateFacetItems", () => {
      service["divsionStateFacetItems"] = undefined;
      service.resetAllDivisionRogs();
      expect(service["divsionStateFacetItems"]).toBeUndefined();
    });

    it("should handle divsionStateFacetItems with empty arrays", () => {
      service["divsionStateFacetItems"] = {
        division1: [],
        division2: [],
      };
      service.resetAllDivisionRogs();
      expect(service["divsionStateFacetItems"].division1).toEqual([]);
      expect(service["divsionStateFacetItems"].division2).toEqual([]);
    });
  });
});
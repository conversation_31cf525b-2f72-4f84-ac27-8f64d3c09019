import { Injectable } from "@angular/core";
import { UntypedFormArray } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { ROUTES_CONST } from "@appConstants/routes_constants";
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { isNullOrUndefined } from "@swimlane/ngx-datatable";
import * as moment from "moment";
import { BehaviorSubject, Subject } from "rxjs";
import { StoreGroupService } from "../../../modules/groups/services/store-group.service";
import { FILTER_OPTIONS } from "../../constants/search-options/filterSearch";
import { BaseInputSearchService } from "../management/base-input-search.service";
import { CommonSearchService } from "./common-search.service";
import { FeatureFlagsService } from "./feature-flags.service";
import { InitialDataService } from "./initial.data.service";
import { QueryGenerator } from "./queryGenerator.service";


@Injectable({
  providedIn: "root",
})
export class FacetItemService {
  private facetChips;
  public facetItems;
  public programCode;
  private facetItemList = {};
  private offerFilter;
  private deliveryChannel:any
  private divsionStateFacetItems;
  showSearchError = false;
  offerRequestStatuses: any;
  offerStatuses: any;
  offerType: any;
  regions: any;
  programTypeSPD: any;
  programSubType: any;
  targeted: any;
  inAdOffers: any;
  inEmailOffers: any;
  discountTypes: any;
  dateField: any = [];
  searchText: any;
  indexList: any[] = [];
  expandSub = new BehaviorSubject(false);
  chipCloseEvent$ = new Subject();
  rangeDates = [
    "effectiveStartDate",
    "createTimeStamp",
    "lastUpdateTimestamp",
    "startDt",
    "endDt",
    "startDate",
    "effectiveEndDate",
    "promotionStartDt",
  ];
  selectedProgramCode$ = new Subject();
  selectedOfferProgramCode$ = new BehaviorSubject<any>({});
  isAllBannersSelected = new BehaviorSubject<boolean>(false);
  isAllDivisionsSelected = new BehaviorSubject<boolean>(false);
  isAllSubprogramsSelected = new BehaviorSubject<boolean>(false);
  getEnableFormSource$ = new BehaviorSubject<boolean>(false);
  getProgramCode$ = new BehaviorSubject<any>("");
  isBpdOfferReqSelected = false;
  expandedItems = [];
  clearAllFilterChips = false;
  initialData;
  chipComponent: any = {};
  facetItemComponent: any;
  facetChipComponent: any;
  podView: boolean;
  podFilterChanged: boolean = false;
  programCodeSelected: string; //= "GR";
  templateProgramCodeSelected = "BPD";
  programCodeInitial: boolean = false;
  programCodeChanged: boolean;
  programFilterChange: boolean;
  programTypes: any;
  vendors:any;
  programCodeChecked: any = {
    [CONSTANTS.SC]: true,
  };
  createdAppIdChecked:any = {
    [CONSTANTS.OMS]:true,
    [CONSTANTS.UPP]:true,
  };
  eCommPromoType: any;
 
  constructor(
    private _permissionsService: PermissionsService,
    private storeGroupService: StoreGroupService,
    private queryGenerator: QueryGenerator,
    private initialDataService: InitialDataService,
    private baseInputSearchService: BaseInputSearchService,
    private featureFlagService:FeatureFlagsService,
    private commonSearchService: CommonSearchService
  ) {
    this.initialData = this.initialDataService.getAppData();
    this.programCode = this.initialData.offerPrograms;
    this.deliveryChannel = this.getDeliveryChannels();
    this.eCommPromoType = this.initialData.ecommPromoCodeTypeChannel;
     this.offerRequestStatuses = this.initialData.offerRequestStatuses;
    this.offerStatuses = this.initialData.offerStatuses;
    this.offerType = this.initialData.offerType;
    this.regions = this.initialData.regions;
    this.targeted = this.initialData.targeted;
    this.inAdOffers = this.initialData.inAdOffers;
    this.inEmailOffers = this.initialData.inEmailOffers;
    this.programTypes = this.initialData.offerDetailsProgramTypes;
    this.programTypeSPD = this.initialData.programTypeSPD;
    this.vendors = this.initialData.vendors;
    this.discountTypes = this.initialData.amountTypes;
    if(this.discountTypes)
    {
      const keyValueArray = Object.entries(this.discountTypes);
      keyValueArray.sort((a, b) => a[0].localeCompare(b[0]));
      const sortedObject: { [key: string]: string } = {};
      for (const [key, value] of keyValueArray) {
        sortedObject[key] = value as string; 
      }
      this.discountTypes = sortedObject;
    }
    if(this.discountTypes && !this.discountTypes?.["NO_DISCOUNT"])
        this.discountTypes["NO_DISCOUNT"] = "No Discount";  
    if(this.discountTypes && !this.discountTypes?.["POINTS"])
      this.discountTypes["POINTS"] = "Points";    
    if (this.offerRequestStatuses) {
      this.offerRequestStatuses.DI = "Digital";
      this.offerRequestStatuses.NDI = "Non-Digital";
    }
    
  }
  getSearchOptionObject(programCode) {
    const currentRouter = FILTER_OPTIONS?.[this.baseInputSearchService.currentRouter];
    return currentRouter?.[programCode];
  }
  setFacetProgramSubType() {
    this.programSubType = this.initialData.offerDetailsProgramSubTypes;
  }

  get reqOffersObjkey() {

    let pcDataKey = this.programCodeSelected;
    if (this.programCodeSelected === CONSTANTS.BPD) {
      //For BPD, data would be available in SPD object
      pcDataKey = CONSTANTS.SPD;
    }
    return pcDataKey.toLocaleLowerCase();
  }

  getOfferRequestStatus() {
    return this.programCodeSelected !== CONSTANTS.SC
      ? this.initialData[`offerRequestStatuses${this.programCodeSelected}`]
      : this.offerRequestStatuses;
  }

  getOfferRequestFailedStatus() {
    return this.programCodeSelected !== CONSTANTS.SC
      ? this.initialData[`offerRequestFailures${this.programCodeSelected}`]
      : this.offerRequestStatuses;
  }
  getOfferRequestSources() {
    return this.getAppData['sources'];
  }
  get getAppData() {
    return this.initialDataService.getAppData();
  }
  getOfferFailedStatus() {
    const appData = this.initialDataService.getAppData(),
      offerFailures = Object.keys(this.programCodeChecked)
        .filter((ele) => this.programCodeChecked[ele])
        .reduce((output, ele) => {
          //BPD refers to spd object
          if (ele === CONSTANTS.BPD) {
            ele = CONSTANTS.SPD;
          }
          output = { ...output, ...appData[`offerFailures${ele === CONSTANTS.SC ? "" : ele}`] };
          return output;
        }, {});
    return offerFailures;
  }
  getOfferTemplateStatus() {
    return this.initialData["offerTemplateStatus"];
  }

  removeFilterChip(removeFilter, form) {
    let { controls } = form,
      controlsArr;
    Object.keys(controls).forEach((control) => {
      controlsArr = controls[control] as UntypedFormArray;
      if (!removeFilter.includes(control)) {
        this.resetPartialValues(removeFilter, control, controlsArr);
      } else {
        this.resetFilterValues(controlsArr, control);
      }
    });
  }

  setProgramCodeSelected(currentRoute = null) {
    /* If the programcode is available in local storage, fetch it from there, else set the default based on the permissions. */
    
    const persistedProgramCode = localStorage.getItem(CONSTANTS.PROGRAM_CODE_LS),
      permissions = this._permissionsService.getPermissions();
    const programCodeToBeByPermissions = this.getProgramCodeByPermissions(permissions);
    if (persistedProgramCode) {
      this.programCodeSelected = persistedProgramCode;
      const mfcheckRolesList = [ "DO_ASSIGNED_DIGITAL_OFFERS", "VIEW_OFFER_REQUESTS", "VIEW_GR_SPD_OFFER_REQUESTS"],
      isOtherPermissionExist = mfcheckRolesList.some(key => permissions && Object.keys(permissions)?.includes(key));
      if (
        this.programCodeSelected &&
        permissions["VIEW_MF_OFFERS"] && !isOtherPermissionExist && currentRoute?.includes(`/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`)
      ) {
        this.programCodeSelected = CONSTANTS.MF;
      }
      
      if(programCodeToBeByPermissions && persistedProgramCode !== programCodeToBeByPermissions)
        this.programCodeSelected = programCodeToBeByPermissions;

      localStorage.setItem(CONSTANTS.PROGRAM_CODE_LS, this.programCodeSelected);
      return false;
    }

    if (!this.programCodeSelected && (permissions["VIEW_OFFER_REQUESTS"] || permissions["DO_STORE_COUPON_OFFERS"])) {
      this.programCodeSelected = CONSTANTS.SC;
    } else if (!this.programCodeSelected && permissions["VIEW_GR_SPD_OFFER_REQUESTS"]) {
      this.programCodeSelected = CONSTANTS.SPD;
    } else if (!this.programCodeSelected && permissions["VIEW_MF_OFFERS"]) {
      this.programCodeSelected = CONSTANTS.MF;
    }
    if (this.programCodeSelected === CONSTANTS.MF && currentRoute?.includes(`/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`)) {
      this.selectedOfferProgramCode$.next({ [CONSTANTS.MF]: true });
    }
    localStorage.setItem(CONSTANTS.PROGRAM_CODE_LS, this.programCodeSelected);
  }

  getProgramCodeByPermissions(permissions,currentRoute = null) {
    if(permissions["VIEW_MF_OFFERS"])
    {
      const mfcheckRolesList = [ "DO_ASSIGNED_DIGITAL_OFFERS", "VIEW_OFFER_REQUESTS", "VIEW_GR_SPD_OFFER_REQUESTS"],
      isOtherPermissionExist = mfcheckRolesList.some(key => permissions && Object.keys(permissions)?.includes(key));
      if (
        this.programCodeSelected &&
        permissions["VIEW_MF_OFFERS"] && !isOtherPermissionExist && currentRoute?.includes(`/${ROUTES_CONST.OFFERS.Offers}/${ROUTES_CONST.OFFERS.Management}`)
      ) {
        return CONSTANTS.MF;
      }
    } else if (permissions["VIEW_OFFER_REQUESTS"] || permissions["DO_STORE_COUPON_OFFERS"]) {
      return CONSTANTS.SC;
    } else if (permissions["VIEW_GR_SPD_OFFER_REQUESTS"]) {
      return CONSTANTS.SPD;
    }
  }

  resetFilterValues(controlsArr, control) {
    if (control === "divisionRogCds") {
      Object.keys(controlsArr["controls"])?.forEach((state) => {
        const stateCtrls = controlsArr?.["controls"]?.[state];
        this.resetCtrlValues(stateCtrls);
      });
    } else {
      this.resetCtrlValues(controlsArr);
    }
  }
  resetCtrlValues(controlsArr) {
    if (controlsArr) {
      for (let index = 0; index < controlsArr.length; index++) {
        const element = controlsArr?.at(index) || controlsArr[index];
        element.setValue(false);
      }
    }
  }
  removeFilterSelectedFromQuery(removeFilter, form) {
    this.queryGenerator.removeParameters(removeFilter);
  }
  resetPartialValues(removeFilter, control, controlsArr: UntypedFormArray) {
    const appData = this.initialDataService.getAppData(),
      programCodeSelected = this.programCodeSelected === CONSTANTS.SC ? "" : this.programCodeSelected,
      facetMappers = appData[`offerFilters${programCodeSelected}`]?.filter(
        (ele) => !removeFilter.includes(ele.facetMapper) && ele.facetMapper !== "offerProgramCd"
      ),
      configMapper = facetMappers.filter((ele) => ele["facetMapper"] === control)[0]?.["configMapper"],
      filters = appData[configMapper];
    control = control === "offerStatus" ? "offerStatuses" : control;
    const pcChecked = Object.keys(this.programCodeChecked).reduce((output, ele) => {
        if (this.programCodeChecked[ele]) {
          const data = this.initialData[`${control}${ele === CONSTANTS.SC ? "" : ele}`];
          output = [...output, ...((data && Object.values(data)) || [])];
        }
        return output;
      }, []),
      pcUnChecked = Object.keys(this.programCodeChecked).reduce((output, ele) => {
        if (!this.programCodeChecked[ele]) {
          const data = this.initialData[`${control}${ele === "SC" ? "" : ele}`];
          output = [...output, ...((data && Object.values(data)) || [])];
        }
        return output;
      }, []),
      removeControls = pcUnChecked?.filter((ele) => !pcChecked.includes(ele));
    if (filters && controlsArr) {
      for (let index = 0; index < controlsArr.length; index++) {
        const element = controlsArr.at(index);
        if (element?.value && removeControls?.includes(element?.value.value)) {
          element.setValue(false);
        }
      }
    }
  }

  getFilterList() {
    const appData = this.initialDataService.getAppData(),
      programCodeChecked = this.programCodeChecked,
      deSelectedPC = Object.keys(programCodeChecked).filter((ele) => !programCodeChecked[ele]),
      selectedPC = Object.keys(programCodeChecked).filter((ele) => programCodeChecked[ele]),
      deSelectPC = Object.values(deSelectedPC)[0],
      selectedSC = programCodeChecked[CONSTANTS.SC],
      deSelectedFilters =
        deSelectedPC.length && appData[`offerFilters${deSelectPC === CONSTANTS.SC ? "" : deSelectPC}`].map((ele) => ele.facetMapper),
      selectedFilters = selectedPC.reduce((output, ele) => {
        output = [...output, ...appData[`offerFilters${ele === CONSTANTS.SC ? "" : ele}`]?.map((ele: any) => ele.facetMapper)];
        return output;
      }, []);

    if (!selectedSC) {
      deSelectedFilters && deSelectedFilters.push("divisions", "divisionRogCds", "podDivisionRog");
      this.resetAllDivisionRogs();
    }
    const filters = deSelectedFilters && deSelectedFilters?.filter((ele) => !selectedFilters.includes(ele));
    delete programCodeChecked[Object.keys(deSelectedPC)[0]];
    return filters;
  }
  getOfferStatus() {
    const appData = this.initialDataService.getAppData(),
      offerStatuses = Object.keys(this.programCodeChecked)
        .filter((ele) => this.programCodeChecked[ele])
        .reduce((output, ele) => {
          //TO DOO: Filter1
          if (ele === CONSTANTS.BPD) {
            ele = CONSTANTS.SPD;
          }

          output = { ...output, ...appData[`offerStatuses${ele === CONSTANTS.SC ? "" : ele}`] };
          return output;
        }, {});
    return offerStatuses;
  }

  getFacetChipItems() {
    return this.facetChips;
  }

  setFacetItemList(facetItemList) {
    this.facetItemList = facetItemList;
  }
  getFacetItemList() {
    return this.facetItemList;
  }
  getFacetItems() {
    return this.facetItems;
  }
  setFacetItems(facetItems) {
    this.facetItems = facetItems;
  }
  getdivsionStateFacetItems() {
    return this.divsionStateFacetItems;
  }
  setDivsionStateFacetItems(divsionStateFacetItems) {
    this.divsionStateFacetItems = divsionStateFacetItems;
  }
  getOfferFilter() {
    return this.offerFilter;
  }
  setOfferFilter(offerFilter) {
    this.offerFilter = offerFilter;
  }
  setTodayOption(dateField) {
    this.dateField.push(dateField);
  }
  emptyTodayOption() {
    this.dateField = [];
  }
  getTodayOption() {
    return this.dateField;
  }
  getStoreSelectedValue(key, items, storesSearchCriteria) {
    const features = this.storeGroupService.getFeatureKeys();
    if (!storesSearchCriteria) {
      if (features.includes(key)) {
        return "all";
      } else {
        return false;
      }
    } else {
      if (key === "divisions" || key === "banners") {
        return storesSearchCriteria[key] ? storesSearchCriteria[key].includes(items) : false;
      } else if (features.includes(key)) {
        if (storesSearchCriteria["features"]) {
          const value = storesSearchCriteria["features"][items];
          if (value === undefined || value === null) {
            return "all";
          } else {
            return value ? "yes" : "no";
          }
        } else {
          return "all";
        }
      }
    }
  }

  addDeliveryChannelFilter(queryWithOrFilters, facetsSelected) {
    const deliveryChannel = Object.keys(facetsSelected).reduce((output, element) => {
      if (["adType", "deliveryChannel"].includes(element)) {
        const index = queryWithOrFilters.findIndex((ele) => ele.indexOf("deliveryChannel") !== -1 || ele.indexOf("adType") !== -1);
        if (index !== -1) {
          queryWithOrFilters.splice(index, 1);
        }
        if (facetsSelected[element]) {
          output.push(`${element}=(${facetsSelected[element]})`);
        }
      }
      return output;
    }, []);
    if (deliveryChannel.length) {
      queryWithOrFilters.push(deliveryChannel.join("#"));
    }
    delete facetsSelected["adType"];
    delete facetsSelected["deliveryChannel"];
  }
  addOfferStatusFilter(queryWithOrFilters, facetsSelected) {
    const offerStatusMatcedKeys = ["isDeferDeploy","offerStatusType", "minusOfferFailedState", "offerStatus", "depPubFailedState"];

    const offerStatus =
      facetsSelected &&
      Object.keys(facetsSelected).reduce((output, element) => {
        if (offerStatusMatcedKeys.includes(element)) {
          const index = queryWithOrFilters?.findIndex((ele) => ele.includes("offerStatus") || ele.includes("offerFailedState") || ele.includes("isDeferDeploy"));
          if (index !== -1) {
            queryWithOrFilters.splice(index, 1);
          }
          if (facetsSelected[element]) {
            const key = element === "depPubFailedState" ? "offerFailedState" : element === "offerStatusType" ? "offerStatusType" : element;
            if(element == "isDeferDeploy" && facetsSelected[element]) {
              output.push(`${element}=(${facetsSelected[element]})`);
            } else {
              output.push(`${key}=(${facetsSelected[element]})`);
            }

          }
        }
        return output;
      }, []);

    offerStatus?.length && queryWithOrFilters.push(offerStatus.join("#"));
    offerStatusMatcedKeys.forEach((ele) => delete facetsSelected[ele]);
  }

  setisAllBannersSelectedValue(value) {
    this.isAllBannersSelected.next(value);
  }
  getIsAllBannersSelected() {
    return this.isAllBannersSelected.asObservable();
  }

  setisAllDivisionsSelectedValue(value) {
    this.isAllDivisionsSelected.next(value);
  }
  getisAllDivisionsSelected() {
    return this.isAllDivisionsSelected.asObservable();
  }

  setIsAllSubprogramsSelected(value) {
    this.isAllSubprogramsSelected.next(value);
  }
  getIsAllSubprogramsSelected() {
    return this.isAllSubprogramsSelected.asObservable();
  }

  getStoreSelectedValueForDivisionStates(key, division, state, storesSearchCriteria) {
    if (!storesSearchCriteria || !storesSearchCriteria[key]) {
      return false;
    } else {
      if (key == "divisionRogCds" && storesSearchCriteria[key]) {
        return storesSearchCriteria[key][division] ? storesSearchCriteria[key][division].includes(state) : false;
      }
    }
  }

  populateStoreFacet(storeFilter, storesSearchCriteria, stateFacets) {
    let facetItems = {};
    let divsionStateFacetItems = {};
    if (storeFilter) {
      facetItems = this.populateItemsForStoreFacets(storeFilter, storesSearchCriteria);
    }
    if (stateFacets) {
      divsionStateFacetItems = this.populateItemsForStoreFacets(stateFacets, storesSearchCriteria, "divisionRogCds");
    }
    /*ACIP-140348: Need to consider divisionRogCds instead of divisions while rendering dual state checkboxes */
    if (facetItems && divsionStateFacetItems && storesSearchCriteria && storesSearchCriteria['divisions']) {
      facetItems['divisions']?.forEach((facetItm) => {
          const respDivisions = storesSearchCriteria['divisions']?.filter((ssd) => ssd == facetItm?.id);
          if ((respDivisions && respDivisions.length > 0 )
            && (isNullOrUndefined(storesSearchCriteria['divisionRogCds']) 
                || Object.keys(storesSearchCriteria['divisionRogCds']).length <=0
                || !storesSearchCriteria['divisionRogCds'][respDivisions[0]])) {
                 /*ACIP-190479: Dont change the state for divisions which are not in  divisionRogCds*/
                  return;
          }
          if(!respDivisions || respDivisions.length <= 0) {
            /*Check for divisionRogs and there if all are selected mark the division as selected even if it is not present in divisions */
            let divisionRogEntries = divsionStateFacetItems[facetItm?.id];
            if (divisionRogEntries && divisionRogEntries.every(entry => entry.selected)) {
              facetItm.selected = true;
              return;
            }
          }
          const divisionEntries = divsionStateFacetItems[facetItm?.id];
          if (divisionEntries && divisionEntries.some(entry => !entry.selected)) {
                facetItm.selected = false;
        }      
      });
    }  
    this.facetItems = facetItems;
    this.divsionStateFacetItems = divsionStateFacetItems;
  }

  populateItemsForStoreFacets(itemsObj, storesSearchCriteria, divisionStatesKey = null) {
    let storeFacetItems = {};
    let selected;
    for (const [key, value] of Object.entries(itemsObj)) {
      if (key && typeof value === "object" && Object.keys(value).length) {
        let objEntries = Object.entries(value),
          _arr = [],
          chip = "";
        for (const [objKey, objValue] of Object.entries(objEntries)) {
          let items = objValue[0].split("::"),
            facetItem;
          if (items.length) {
            let id = items[0],
              value = items[1] ? items[1] : items[0];
            if (!divisionStatesKey) {
              selected = this.getStoreSelectedValue(key, items[0], storesSearchCriteria);
            } else {
              selected = this.getStoreSelectedValueForDivisionStates(divisionStatesKey, key, items[0], storesSearchCriteria);
            }
            facetItem = {
              id,
              count: objValue[1],
              value,
              selected,
            };
          }
          _arr.push(facetItem);
        }
        storeFacetItems[key] = _arr;
      }
    }
    return storeFacetItems;
  }
  sortDivisionRogCds(rogCds) {
    if (rogCds && typeof rogCds == "object" && !Array.isArray(rogCds)) {
      Object.keys(rogCds).forEach((key) => {
        if (rogCds[key] && typeof rogCds[key] == "object") {
          let sortedObj = Object.keys(rogCds[key])
            .sort()
            .reduce((r, k) => Object.assign(r, { [k]: rogCds[key][k] }), {});
          rogCds[key] = { ...sortedObj };
        }
      });
    }
  }
  getDeliveryChannels(){
    const offerProgram = this.initialDataService.getAppDataName("offerPrograms");
    let deliveryChannel:any;
    for(const pCode in offerProgram){
      deliveryChannel = {...deliveryChannel,...this.getDeliveryChannelsOnPC(pCode)};
    }
    return deliveryChannel;
  }
  getDeliveryChannelsOnPC(data){
    let pCode = data === CONSTANTS.SC ? "" : data;
    return this.initialData[`offerDeliveryChannels${pCode}`] ||{};
  }
  populateFacet(facetListItems) {
    const facetItems = {};
    const isFeatureFlagEnabled = this.featureFlagService.isFeatureFlagEnabled("enableBehavioralOffers");
    if (facetListItems) {
      for (const [key, value] of Object.entries(facetListItems)) {
        if (key && typeof value === "object" && Object.keys(value).length) {
          let objEntries = Object.entries(value),
            _arr = [],
            chip = "";
            for (const [objKey, objValue] of Object.entries(objEntries)) {
            let items = objValue[0].split("::"),
              facetItem,
              selected,
              color = "";
            if (items.length) {
              let id = items[0],
                value = items[1] ? items[1] : items[0];
                if (["couponTypeNm", "vehicleTypNm", "color", "isImageFound", "offerLinked", "isProofed"].includes(key)) {
                value = objValue[1];
                if (key === "color") {
                  color = objValue[1].toLowerCase().replace(/ /g, "");
                }
              } else if (key === "deliveryChannel") {
                value = this.deliveryChannel[id] || value;
              } else if (key === "divisionId") {
                value = items[1];
                id = items[0];
                selected = false;
              } else if (key === "programCd" || key === "offerProgramCd" || key === "programCode") {
                value = this.programCode[id] || value;
              } else if (key === "status") {
                const statusValues = this.getOfferRequestStatus();
                value = statusValues?.[id] || id;
              } else if (key === "otStatus") {
                const templatestatusValues = this.getOfferTemplateStatus();
                value = templatestatusValues?.[id] || id;
              } else if (key === "actionFailedState") {
                const failedStatusValues = this.getOfferRequestFailedStatus();
                value = failedStatusValues?.[id] || id;
              } else if (key === "createdAppId" && this.featureFlagService.isUPPFieldSearchEnabled) {
                  const sourceValues = this.getOfferRequestSources();
                  value = sourceValues?.[id] || id;
                  selected =  true;
              } else if (key === "offerFailedState") {
                const failedStatusValues = this.getOfferFailedStatus();
                value = failedStatusValues?.[id] || id;
              } else if (key === "regions") {
                value = this.regions[id].name || id;
              } else if (key === "targeted") {
                value = this.targeted[id] || id;
              } else if (key === "isInAd") {
                value = this.inAdOffers[id] || id;
              } else if (key === "inEmail") {
                value = this.inEmailOffers[id] || id;
              } else if (key === "eCommPromoType") {
                value = this.eCommPromoType[id] || id;
              }else if (key === "offerStatus") {
                const statusValues = this.getOfferStatus();
                value = statusValues?.[id] || id;
              } else if (key === "discountType") {
                value = this.discountTypes[id] || id;
              } else if (key === "vendors") {
                value = this.vendors[id] || id;
              } else if(key === "subProgramCode") {
                value = this.initialData?.["subProgramCodes"]?.[id] || id;
              } else if (key === "digital") {
                if (id === "false") {
                  value = "Non-Digital";
                } else if (id === "true") {
                  value = "Digital";
                }
                id = "isApplicableToJ4U";
              } else if (key === "podStatus") {
                if (id === "true") {
                  value = "Yes";
                } else if (id === "false") {
                  value = "No";
                }
              } else if (key === "offerType" || key === "offerProtoType") {
                value = this.offerType[id] || id;
                selected = this.getSelectedValue(key, value);
              }
              if (!(key === "offerType" || key === "offerProtoType")) {
                selected = this.getSelectedValue(key, items[0]);
              }
              if (key === "divisions") {
                selected =
                  (this.divsionStateFacetItems && this.divsionStateFacetItems?.[id]?.every((state) => state?.selected === true)) || false;
              }
              if (key === "programType") {
                selected = this.getSelectedValue(key, value);
              }
              if (["progSubType"].includes(key)) {
                value = this.initialData.offerDetailsProgramSubTypes[id] || value;
                selected = this.getSelectedValue(key, value);
              }
              let canshowBAFilter = true;
              if(
                key === "deliveryChannel" && 
                (
                  (!isFeatureFlagEnabled && id === "BA") || 
                  (!this.featureFlagService.isBehavioralContinuityEnabled && id === "BAC") ||
                  (!this.featureFlagService.isEnableReceiptEngineFeature && id === "IR")
                ))
              {
                canshowBAFilter = false;                  
              }
              if(canshowBAFilter)
                facetItem = {
                  id,
                  count: objValue[1],
                  value,
                  selected,
                  color,
              };
            }
            if(facetItem)
              _arr.push(facetItem);
          }
          facetItems[key] = _arr;
        }
        this.isDivisionalGameEnabled && this.checkIfLoadPrgrmTypeForDg(key, value, facetItems)
      }
    }
    this.facetItems = facetItems;
  }
  checkIfLoadPrgrmTypeForDg(key, value, facetItems) {
    const curentRoute = this.baseInputSearchService.currentRouter;
    if(curentRoute === "request" && this.programCodeSelected === CONSTANTS.GR && key === "programType") {
      const isValueEmptyObj = value && toString.call(value) === '[object Object]' && !Object.keys(value).length;
      if(isValueEmptyObj) {
        facetItems[key] = []
      }
    }
  }
  get isDivisionalGameEnabled() {
    return this.featureFlagService.isFeatureFlagEnabled("isDivisionalGamesFeatureEnabled");
  }
  resetAllDivisionRogs() {
    const divisionStates = this.divsionStateFacetItems && Object.keys(this.divsionStateFacetItems);
    divisionStates?.forEach((division) => {
      this.divsionStateFacetItems[division]?.forEach((element) => (element.selected = false));
    });
  }
  resetProgramCdChecked() {
    this.programCodeChecked = {};
  }
  getProgramTypeValue(value) {
    let programType = this.queryGenerator.getQuery().replace(/\\ /g, " ");
    return programType?.split(" OR ").filter((ele) => ele.indexOf(value) !== -1).length;
  }

  setDigitalProperties() {
    return {
      false: "Non-Digital",
      true: "Digital",
    };
  }
  retainProgramCodeSelected() {
    /**
     * Adding the logic to retain the program code slected to while navigating across the pages
     */
    //TODOO: LS
    const permissions = this._permissionsService.getPermissions(),
      persistedProgramCode = localStorage.getItem(CONSTANTS.PROGRAM_CODE_LS);

    if (permissions["VIEW_GR_SPD_OFFER_REQUESTS"]) {
      this.programCodeSelected = CONSTANTS.GR;
    }
    if (permissions["ADMIN"]) {
      this.programCodeSelected = CONSTANTS.SC;
    }

    if (persistedProgramCode) {
      this.programCodeSelected = persistedProgramCode;
    }

    //Fix: Filters for GR not correct when program code was set from OR page
    if (this.programCodeSelected) {
      this.programCodeChecked = {
        [this.programCodeSelected]: true,
      };
    }
  }
  setPodApprovedStatus() {
    return {
      true: "Yes",
      false: "No",
    };
  }
  sortProperties(obj, sortedBy, isNumericSort, reverse) {
    sortedBy = sortedBy || 1; // by default first key
    isNumericSort = isNumericSort || false; // by default text sort
    reverse = reverse || false; // by default no reverse

    let reversed = reverse ? -1 : 1;

    let sortable = [];
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        sortable.push([key, obj[key]]);
      }
    }
    if (isNumericSort) {
      sortable.sort(function (a, b) {
        return reversed * (a[1][sortedBy] - b[1][sortedBy]);
      });
    } else {
      sortable.sort(function (a, b) {
        let x = a[1][sortedBy].toLowerCase(),
          y = b[1][sortedBy].toLowerCase();
        return x < y ? reversed * -1 : x > y ? reversed : 0;
      });
    }

    let newObject = {};

    for (let i = 0; i < sortable.length; i++) {
      let key = sortable[i][0],
        value = sortable[i][1];
      newObject[" " + key] = value;
    }
    return newObject;
  }
  addOfferTypes(facetOfferType, configOfferType) {
    let configData = {},
      keys = Object.keys(configOfferType);
    if (facetOfferType) {
      keys.forEach((ele) => {
        const facetValue = facetOfferType[configOfferType[ele]];
        if (facetValue) {
          configData[ele] = facetValue;
        }
      });
    }

    return configData;
  }
  addDeliveryChannels(offerDeliveryChannels = {}) {
    const keys = Object.keys(offerDeliveryChannels);

    if (!keys.includes("IA")) {
      offerDeliveryChannels["Digital Only-In Ad"] = "Digital Only-In Ad";
    }
    if (!keys.includes("NIA")) {
      offerDeliveryChannels["Digital Only-Not In Ad"] = "Digital Only-Not In Ad";
    }
       
    if (keys.includes("DO") && (!this.getProgramCodesSelected().includes(CONSTANTS.SPD) 
    || !this.featureFlagService.isFeatureFlagEnabled('enableACDeliveryChannel'))) {
      delete offerDeliveryChannels["DO"];
    }
  }
  getDeliveryChannelValue(value) {
    if (value === "Digital Only-In Ad" || value === "Digital Only-Not In Ad") {
      value = value === "Digital Only-In Ad" ? "IA" : "NIA";
    }
    let deliveryChannel = this.queryGenerator.getQueryFilter("deliveryChannel");
    let adType = this.queryGenerator.getQueryFilter("adType");
    if (deliveryChannel) {
      return deliveryChannel.split(" OR ").filter((ele) => [ele].includes(value)).length ? true : false;
    } else if (adType) {
      return adType.split(" OR ").filter((ele) => [ele].includes(value)).length ? true : false;
    }
  }
  getOfferStatusSelectedValue(value) {
    let offerStatus = this.queryGenerator.getQueryFilter("offerStatus");
    let offerFailedState = this.queryGenerator.getQueryFilter("offerFailedState");
    let isDeferDeploy = this.queryGenerator.getQueryFilter("isDeferDeploy");
    if (value === "IPU") {
      return offerFailedState?.length;
    } else if(value === "DD") {
      return isDeferDeploy?.length;
    } else {
      if (offerStatus) {
        return offerStatus.split(" OR ").filter((ele) => [ele].includes(value)).length ? true : false;
      }
    }
  }
  getOfferProgSubTypeSelectedValue(key, value) {
    let progSubType = this.queryGenerator.getInputValue(key).replace(/\\ /g, " ");
    return progSubType?.split(" OR ").filter((ele) => [ele].includes(value)).length ? true : false;
  }
  checkForExpired(status) {
    /**
     * Here, checking whether user filtered Expired status by different ways -
     * 1. First checking effectiveEndDate is in query or not.
     * 2. In chips, whether only Expired is there on not.
     * 3. In query , only D is there under status or not
     * 
     * if all above are true, that means User filtered for Expired
    */
   if(this.featureFlagService.isOfferRequestArchivalEnabled )
   {
    return this.commonSearchService.isShowExpiredInQuery;
   }
   if(this.featureFlagService.isOfferArchivalEnabled)
   {
    return this.commonSearchService.isShowExpiredInQuery_O;
   }
   
    const isEffectiveEndDateThere = this.queryGenerator?.getInputRangeValue("effectiveEndDate"),
    facetFilterObj = this.facetChipComponent?.facetFilter,statusSelectedList = facetFilterObj?.status?.split(" ; ")?.filter(ele => ele),
    statusInQUery = status &&  [...new Set(status?.split(" OR "))], isOnlyCompleted = statusInQUery?.length === 1 && statusInQUery?.includes("D");
    const isExpiredSelected = isOnlyCompleted && isEffectiveEndDateThere &&  statusSelectedList?.length === 1 && statusSelectedList?.find(ele => ele?.includes(CONSTANTS.EXPIRED_STATUS_OR_DISPLAY)) ? true : false;
    return isExpiredSelected
  }
  getSelectedValue(key, value) {
    const query = this.queryGenerator.getQuery().replace(/\\ /g, " ");
    if(key=== "createdAppId")
    {
      return this.featureFlagService.isUPPFieldSearchEnabled;
    }
    if (key === "status") {
      let status = this.queryGenerator.getQueryFilter("Status");
      let digital = this.queryGenerator.getQueryWithFilter().filter((ele) => ele.indexOf("digitalUiStatus") !== -1);
      let nonDigital = this.queryGenerator.getQueryWithFilter().filter((ele) => ele.indexOf("nonDigitalUiStatus") !== -1);      
      if (value === "DI") {
        return digital.length;
      } else if (value === "NDI") {
        return nonDigital.length;
      } else {
        if (status) {
          if([CONSTANTS.GR,CONSTANTS.SPD, CONSTANTS.SC].includes(this.programCodeSelected) && ["D", "EX"].includes(value)) {
            const isExpiredStatus = this.checkForExpired(status);
            if(isExpiredStatus) {
              if(this.featureFlagService.isArchivalEnabled)
                return value === "EX";
              else
                return  status.split(" OR ").filter((ele) => [ele].includes(value)).length;
            } else {
              return  status.split(" OR ").filter((ele) => [ele].includes(value)).length;
            }
          } else {
            return status.split(" OR ").filter((ele) => [ele].includes(value)).length;
          }
        }
      }
    }
    if (key === "offerStatus") {
      return this.getOfferStatusSelectedValue(value);
    }
    if (["progSubType"].includes(key)) {
      return this.getOfferProgSubTypeSelectedValue(key, value);
    }
    if (key === "podStatus") {
      let podStatusValue = this.queryGenerator.getInputValue("podStatus");
      if (value === "false" && podStatusValue === "NULLFALSE") {
        return true;
      }
    }
    if (["isInAd", "inEmail"].includes(key)) {
      const queryKey = key === "isInAd" ? "adType" : key;
      let isInAdQueryValue = this.queryGenerator.getInputValue(queryKey);
      const yesValue = ["IA", "true"];
      const noValue = ["NIA", "false"];
      return isInAdQueryValue?.split(" OR ")?.filter((ele) => (value == "Y" ? yesValue.includes(ele) : noValue.includes(ele)))?.length;
    }
    if (key === "deliveryChannel") {
      return this.getDeliveryChannelValue(value);
    }
    if (key === "programCd") {
      key = "programCode";
    }

    let _fliterkey = CONSTANTS.FACET_FILTER[key],
      _key;
    if (key === "digital") {
      _fliterkey = {
        field: "isApplicableToJ4U",
      };
    }
    if (_fliterkey) {
      _key = _fliterkey.field;
    } else {
      return false;
    }
    if (key === "offerType") {
      _key = "requestType";
    }

    let splitQuery = query && query.split(";");
    let isQueryIncludesKey = splitQuery && splitQuery.filter((param) => param && param.startsWith(_key)).length;

    if (isQueryIncludesKey) {
      const len = query.indexOf(_key);
      const splitValue = query.slice(len, query.indexOf(";", len)).split("=(")[1]?.split(" OR ");
      return (
        splitValue.filter((ele, indx) => {
          if (splitValue.length - 1 === indx) {
            ele = ele.slice(0, ele.length - 1);
          }
          return _key === "requestType" || key === "offerProtoType" ? this.offerType[ele] === value : ele?.toUpperCase() === value?.toUpperCase();
        }).length > 0
      ); 
    }

    return false;
  }

  populateFacetSearch(query) {
    let querySeparator = query.split(";"),
      _keys = { ...CONSTANTS.FACET_SEARCH },
      facetSearch = {};
    const items = Object.keys(_keys).map((el: any) => {
      return _keys[el].field;
    });
    querySeparator.forEach((element) => {
      const ele = element.split("=");
      if (items.includes(ele[0])) {
        facetSearch[ele[0]] = ele[1];
      }
    });
    return facetSearch;
  }

  populateFacetFieldChips(search, field) {
    let facetFilter = {};
    const features = this.storeGroupService.getFeatureKeys();
    Object.keys(search).forEach((item: any) => {
      if (field === item) {
        facetFilter[item] = "";
        search[item].forEach((facet) => {
          if (facet && !(typeof facet === "boolean")) {
            facetFilter[item] = features.includes(item) ? `${facet.selected}` : `${facetFilter[item]}${facet.value};`;
          }
        });
      }
      if (item == "status") {
        // dont generate chip for digital and non-digital if none of other status is selected
        let statusFacets = search[item].filter((ele) => typeof ele !== "boolean" && ele.id !== "DI" && ele.id !== "NDI");
        if (!statusFacets.length) {
          facetFilter[item] = "";
        }
      }
      if (item == "divisionRogCds") {
        let divisionChipList = [];
        search["divisions"]?.forEach((obj: any) => {
          if (obj && !(typeof obj === "boolean")) {
            divisionChipList.push(obj.value);
          }
        });
        Object.keys(search["divisionRogCds"]).forEach((item: any) => {
          let statesList = [];
          let isAllStatesSelected = search["divisionRogCds"][item].filter((obj) => typeof obj !== "boolean");
          if (isAllStatesSelected.length !== search["divisionRogCds"][item].length && isAllStatesSelected.length > 0) {
            isAllStatesSelected.forEach((state) => {
              statesList.push(state.value);
            });
            let formattedDivisonChip = `${item} (${statesList.join(", ")}) `;
            divisionChipList.push(formattedDivisonChip);
          }
        });
        if (divisionChipList.length > 0) {
          facetFilter["divisions"] = [...divisionChipList].sort();
        }
      }
    });
    return facetFilter;
  }

  generateQuery(formValue) {
    let obj = {};
    let digital = true,
      nonDigital = true;
    Object.keys(formValue)?.forEach((item: any) => {
      if (item === "status") {
        obj["digitalUiStatus"] = "";
        obj["nonDigitalUiStatus"] = "";
      } else {
        obj[item] = "";
      }

      if (item !== "divisionRogCds") {
        formValue?.[item]?.forEach((facet) => {
          //For Exipired status, set the status as completed
          if(item === 'status' && facet?.id === CONSTANTS.EXPIRED_STATUS_OR) {
            facet.id = CONSTANTS.COMPLETED_STATUS_OR;
          }
          if (facet && !(typeof facet === "boolean")) {
            if (item === "digital" && (facet.value === "Digital" || facet.value === "Non-Digital")) {
              if (!obj.hasOwnProperty("isApplicableToJ4U") && !obj["isApplicableToJ4U"]) {
                obj["isApplicableToJ4U"] = facet.value === "Digital";
              } else {
                obj["isApplicableToJ4U"] = `${obj["isApplicableToJ4U"]} OR ${facet.value === "Digital"}`;
              }
            } else if (item === "deliveryChannel") {
              if (facet.value === "Digital Only-In Ad") {
                if (obj["adType"]) {
                  obj["adType"] = `(${obj["adType"]} OR IA)`;
                } else {
                  obj["adType"] = "IA";
                }
              } else if (facet.value === "Digital Only-Not In Ad") {
                if (obj["adType"]) {
                  obj["adType"] = `(${obj["adType"]} OR NIA)`;
                } else {
                  obj["adType"] = "NIA";
                }
              } else {
                if (obj[item] === "") {
                  obj[item] = `${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
                } else {
                  obj[item] = `${obj[item]} OR ${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
                }
              }
            } else if (item == "offerStatus") {
              this.setFacetsObjForOfferStatus(facet, obj, item);
            } else if (item === "offerType" || item === "offerProtoType") {
              if (obj[item] === "") {
                obj[item] = `${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
              } else {
                obj[item] = `${obj[item]} OR ${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
              }
            } else if (item === "status") {
              if (facet.value === "Digital") {
                digital = false;
              }
              if (facet.value === "Non-Digital") {
                nonDigital = false;
              }
              if (!(facet.value === "Non-Digital" || facet.value === "Digital")) {
                this.setDigitalStatusValues(digital, nonDigital, obj, facet);
              }
              if(facet.value === CONSTANTS.EXPIRED_STATUS_OR_DISPLAY ) {
                obj["status"] = facet.value;
              }
            } else if (["programType", "progSubType"].includes(item)) {
              if (obj[item] === "") {
                obj[item] = `${facet.value.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
              } else {
                obj[item] = `${obj[item]} OR ${facet.value.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
              }
            } else {
              if (obj[item] === "") {
                obj[item] = `${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
              } else {
                obj[item] = `${obj[item]} OR ${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
              }
            }
          }
        });
      }

      if (item === "divisions" && formValue["divisionRogCds"]) {
        let rogsSelectedList = [];
        obj["divisions"] = "";
        let divisionName = Object.keys(formValue["divisionRogCds"]);
        divisionName.forEach((division) => {
          let rogsSelected = formValue["divisionRogCds"][division].filter((item) => typeof item !== "boolean");
          rogsSelectedList.push(...rogsSelected);
        });
        if (rogsSelectedList.length) {
          obj["podDivisionRog"] = rogsSelectedList.reduce((out, ele, index) => {
            if (index === 0) {
              out = `${ele.value}`;
            } else {
              out = `${out} OR ${ele.value}`;
            }
            return out;
          }, "");
        }
      }

      if (item === "podStatus" && obj[item].indexOf("true") === -1) {
        obj[item] = obj[item].replace(/false/g, "NULLFALSE");
      }

      if (obj[item]) {
        obj[item] = `${obj[item]}`;
      }
    });
    obj["queryWithOrFilters"] = this.getQueryWithOrFilters(obj, digital, nonDigital).join("#");
    delete obj["digitalUiStatus"];
    delete obj["nonDigitalUiStatus"];
    return obj;
  }

  setFacetsObjForOfferStatus(facet, obj, item) {
    const selectedProgrmCds =
      this.programCodeChecked &&
      Object.keys(this.programCodeChecked).filter(
        (programCode) =>
          this.programCodeChecked[programCode] && [CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD, CONSTANTS.MF].includes(programCode)
      );

    if (facet?.id !== "IPU") {
      if (facet?.id === "RU" && this.programCodeChecked[CONSTANTS.MF]) {
        obj["offerStatusType"] = "RU";
      } else if(facet?.id == "DD") {
        obj["isDeferDeploy"] = true;
      }
       else {
        let newStatusValue = `${facet.id.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
        obj[item] = obj[item] === "" ? newStatusValue : `${obj[item]} OR ${newStatusValue}`;
        if (facet?.id === "I" && selectedProgrmCds?.length) {
          obj["minusOfferFailedState"] = `PUBLISHING`;
        }
      }
    } else {
      obj["depPubFailedState"] = `PUBLISHING`;
      obj["minusOfferFailedState"] && delete obj["minusOfferFailedState"];
    }
  }
  getQueryForDatesSearch() {
    this.rangeDates.forEach((ele) => {
      if (this.getQueryParameter(ele)[0]) {
        this.queryGenerator.pushParam({
          remove: true,
          parameter: ele,
          value: `[${this.generateQueryForToday(ele)}]`,
        });
      }
    });
  }

  generateQueryForToday(ele) {
    const start = moment();
    const end = moment();
    const endDate =
      ele === "startDt" || ele === "effectiveStartDate" || ele === "effectiveEndDate" || ele === "promotionStartDt"
        ? "*"
        : `${end.format("YYYY-MM-DDT")}23:59:59Z`;
    return `${start.format("YYYY-MM-DDT")}00:00:00Z TO ${endDate}`;
  }

  getQueryParameter(parameter) {
    const splitQuery = this.queryGenerator.getQuery().split(";");
    return splitQuery.filter((ele) => {
      const param = ele.split("=");
      return param[0] === parameter && (param[1] === "Today" || param[1] === "Today+");
    });
  }

  getQueryParamValue(parameter) {
    const splitQuery = this.queryGenerator.getQuery().split(";");
    return splitQuery.filter((ele) => {
      const param = ele.split("=");
      return param[0] === parameter ? param[1] : "";
    });
  }
  setDigitalStatusValues(digital, nonDigital, obj, facet) {
    let facetid = facet.id == 'EX' ? 'D' : facet.id;
    if (digital && nonDigital) {
      if (obj["digitalUiStatus"] === "") {
        obj["digitalUiStatus"] = `${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      } else {
        obj["digitalUiStatus"] = `${obj["digitalUiStatus"]} OR ${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      }
      if (obj["nonDigitalUiStatus"] === "") {
        obj["nonDigitalUiStatus"] = `${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      } else {
        obj["nonDigitalUiStatus"] = `${obj["nonDigitalUiStatus"]} OR ${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      }
    }
    if (!digital) {
      if (obj["digitalUiStatus"] === "") {
        obj["digitalUiStatus"] = `${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      } else {
        obj["digitalUiStatus"] = `${obj["digitalUiStatus"]} OR ${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      }
    }
    if (!nonDigital) {
      if (obj["nonDigitalUiStatus"] === "") {
        obj["nonDigitalUiStatus"] = `${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      } else {
        obj["nonDigitalUiStatus"] = `${obj["nonDigitalUiStatus"]} OR ${facetid.replace(/[.*+?^${}()|[\]\\; ]/g, "\\ ")}`;
      }
    }
  }

  getQueryWithOrFilters(obj, digital, nonDigital) {
    let queryWithOrFiltersEle = [];

    if (obj["digitalUiStatus"]) {
      queryWithOrFiltersEle.push(`digitalUiStatus=(${obj["digitalUiStatus"]})`);
    }
    if (obj["nonDigitalUiStatus"]) {
      queryWithOrFiltersEle.push(`nonDigitalUiStatus=(${obj["nonDigitalUiStatus"]})`);
    }
    return queryWithOrFiltersEle;
  }

  getFacetFieldList() {
    const field = { ...CONSTANTS.FACET_FILTER, ...CONSTANTS.FACET_SEARCH };
    return Object.values(field).map((ele: any) => {
      return ele.field;
    });
  }
  getFilterFacetFields() {
    const field = CONSTANTS.FACET_FILTER;
    return Object.values(field).map((ele: any) => {
      return ele.field;
    });
  }
  getSearchFacetFields() {
    const field = CONSTANTS.FACET_SEARCH;
    return Object.values(field).map((ele: any) => {
      return ele.field;
    });
  }
  getSearchFacetKeys() {
    // effectiveEndDate is not there in initial data for seacrh keys so getting it from constant file
    const effectiveEndDtSearchkey = CONSTANTS.FACET_SEARCH.effectiveEndDate;
    const verbiageSearchKeys = ["headLine", "headLine2", "prodDesc"].map((item) => CONSTANTS.FACET_SEARCH[item]);
    const field = {
        ...CONSTANTS.SUGESSTED_FILTER,
        ...CONSTANTS.FACET_FILTER,
        effectiveEndDtSearchkey,
        ...verbiageSearchKeys,
        ...this.initialDataService.searchOfferPODOptions.reduce((obj, item: any) => {
          obj[item.field] = item;
          return obj;
        }, {}),
        ...this.initialDataService.getSearchOptions(this.programCodeSelected)?.reduce((obj, item: any) => {
          obj[item.field] = item;
          return obj;
        }, {}),
        ...this.initialDataService.getSearchOfferOptions().reduce((obj, item: any) => {
          obj[item.field] = item;
          return obj;
        }, {}),
      },
      keysObj = {};
    Object.values(field).forEach((ele: any) => {
      keysObj[ele.field] = ele.key;
    });
    return keysObj;
  }

  getDefaultFilterchip() {
    return this.getFilterFacetFields().reduce((output, item: any) => {
      output[item] = "";
      return output;
    }, {});
  }
  getProgramCodesSelected() {
    return Object.keys(this.programCodeChecked).filter((key) => this.programCodeChecked[key]);
  }
  enableBatchActionForOffers() {
    //TO DO: Filter1
    const pcSelection = this.programCodeChecked,
      pcChecked = Object.keys(pcSelection).filter((ele) => pcSelection[ele]),
      permissions = this._permissionsService.getPermissions();
    if (pcChecked?.length === 1) {
      switch (pcChecked[0]) {
        case CONSTANTS.MF: {
          return permissions[CONSTANTS.Permissions.DoMFOffers];
        }
        case CONSTANTS.GR:
        case CONSTANTS.BPD:
        case CONSTANTS.SPD: {
          return permissions[CONSTANTS.Permissions.Admin];
        }
        case CONSTANTS.SC: {
          return permissions[CONSTANTS.Permissions.DoPodOffers] ||
            permissions[CONSTANTS.Permissions.DoAssignedDigitalOffers] ||
            permissions[CONSTANTS.Permissions.DoAssignedNonDigitalOffers];
        }
        default: {
          return true;
        }
      }
    } else {
      return true;
    }
  }
}

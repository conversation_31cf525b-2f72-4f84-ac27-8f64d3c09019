import { TestBed } from "@angular/core/testing";
import { AppService } from "./app.service";
import { FeatureFlagsService } from "./feature-flags.service";
import { InitialDataService } from "./initial.data.service";

describe('FeatureFlagService', () => {
  let mockAppService : jasmine.SpyObj<AppService>;
  let service : FeatureFlagsService;
  let mockInitialDataService: jasmine.SpyObj<InitialDataService>;

  beforeEach(() => {
    const appServiceSpy = jasmine.createSpyObj('AppService', ['getFeatureFlags']);
    const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getAppData']); 

    TestBed.configureTestingModule({
        providers: [
            FeatureFlagsService,
            { provide: AppService, useValue: appServiceSpy },
            { provide: InitialDataService, useValue: initialDataServiceSpy }
        ]
    })

    service = TestBed.inject(FeatureFlagsService);
    mockAppService = TestBed.inject(AppService) as jasmine.SpyObj<AppService>;
    mockInitialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
  })

  it('should be created', () => {
    expect(service).toBeTruthy();
  })

  it('should assign feature flags', () => {
    const featureFlags = {flag1 : true, flag2 : false};
    mockAppService.getFeatureFlags.and.returnValue(featureFlags);

    service.assignFeatureFlag();

    expect(service.featureFlags).toEqual(featureFlags);
    expect(mockAppService.getFeatureFlags).toHaveBeenCalled();
  });

  it('should return true if feature flag is enabled', () => {
    const featureFlags = {flag : true};
    mockAppService.getFeatureFlags.and.returnValue(featureFlags);

    const result = service.isFeatureFlagEnabled('flag');

    expect(result).toBeTrue();
    expect(mockAppService.getFeatureFlags).toHaveBeenCalled();
  });

  it('should return true when single flag is enabled', () => {
    const featureFlags = {flag : true};
    mockAppService.getFeatureFlags.and.returnValue(featureFlags);

    const result = service.hasFlags('flag');

    expect(result).toBeTrue();
    expect(mockAppService.getFeatureFlags).toHaveBeenCalled();
  })

  it('should return false when single flag is disable', () => {
    const featureFlags = {flag : false};
    mockAppService.getFeatureFlags.and.returnValue(featureFlags);

    const result = service.isFeatureFlagEnabled('flag');

    expect(result).toBeFalse();
    expect(mockAppService.getFeatureFlags).toHaveBeenCalled();
  });

  it('should return true for multiple enabled flags', () => {
    const featureFlags = {flag1 : true, flag2 : true};
    mockAppService.getFeatureFlags.and.returnValue(featureFlags);

    const result = service.hasFlags(['flag1', 'flag2']);

    expect(result).toBeTrue();
    expect(mockAppService.getFeatureFlags).toHaveBeenCalled();
  });

  it('should return false for multiple disabled flags', () => {
    const featureFlags = {flag1 : true, flag2 : false};
    mockAppService.getFeatureFlags.and.returnValue(featureFlags);

    const result = service.hasFlags(['flag1', 'flag2']);

    expect(result).toBeFalse();
    expect(mockAppService.getFeatureFlags).toHaveBeenCalled();
  })

  it('should return true if UJ action is enabled', () => {
    const appData = { featureFlagsUJ: ['action1', 'action2'] };
    const action = 'action1';

    mockInitialDataService.getAppData.and.returnValue(appData);

    const result = service.isUJActionEnabled(action);
  
    expect(result).toBeTrue();
    expect(mockInitialDataService.getAppData).toHaveBeenCalled();
  });

  it('should return false if action is undefined', () => {
    const appData = { featureFlagsUJ: ['action1', 'action2'] };

    const result = service.isUJActionEnabled(undefined);

    expect(result).toBeFalse();
    expect(mockInitialDataService.getAppData).toHaveBeenCalled();
  });

  it('should return false if action is not included in featureFlagsUJ', () => {
    const appData = { featureFlagsUJ: ['action1', 'action2'] };
    const action = 'action3';

    mockInitialDataService.getAppData.and.returnValue(appData); 

    const result = service.isUJActionEnabled(action);

    expect(result).toBeFalse();
    expect(mockInitialDataService.getAppData).toHaveBeenCalled();
  });

  it('should return true if is redeem benefit in same transaction enabled', () => {
    service.featureFlags = {enableRedeemBenefitInSameTransaction : true};
    const result = service.isRedeemBenefitInSameTransactionEnabled;
    expect(result).toBeTrue();
  });

  it('should return true for forceArchival', () => {
    service.featureFlags = {forceArchival : true};
    const result = service.forceArchival;
    expect(result).toBeTrue();
  });

  it('should return true if archival enabled', () => {
    service.featureFlags = { forceArchival : true };
    const result = service.isArchivalEnabled;
    expect(result).toBeTrue();
  });

  it('should return true for is offer archival enabled', () => {
    service.featureFlags = { forceArchival : true };
    const result = service.isOfferArchivalEnabled;
    expect(result).toBeTrue();
  });

  it('should return false for offer request archival enabled', () => {
    const result = service.isOfferRequestArchivalEnabled;
    expect(result).toBeFalse();
  });

  it('should return true for no discount enabled', () => {
    service.featureFlags = { enableContinuityNoDiscount: true };
    const result = service.isNoDiscountEnabled;
    expect(result).toBeTrue();
  });

  it('should return true if is UPP field search enabled', () => {
    spyOn(service, 'isFeatureFlagEnabled').and.returnValue(true);
    const result = service.isUPPFieldSearchEnabled;
    expect(result).toBeTrue();
    expect(service.isFeatureFlagEnabled).toHaveBeenCalledWith('enableUPPFieldsSearch');
  });

  it('should return true if is UPP enabled', () => {
    spyOn(service, 'isFeatureFlagEnabled').and.returnValue(true);
    const result = service.isuppEnabled;
    expect(result).toBeTrue();
    expect(service.isFeatureFlagEnabled).toHaveBeenCalledWith('uppEnable');
  });

  it('should return true if is disclaimer api enabled', () => {
    spyOn(service, 'isFeatureFlagEnabled').and.returnValue(true);
    const result = service.isDisclaimerApiEnabled;
    expect(result).toBeTrue();
    expect(service.isFeatureFlagEnabled).toHaveBeenCalledWith('enableDisclaimerApi');
  });

  it('should return true if is nutrition tags enabled', () => {
    service.featureFlags = { enableNutritionTags: true };
    const result = service.isNutritionTagsEnabled;
    expect(result).toBeTrue();
  });

  it('should return true if is auto reward enabled', () => {
    service.featureFlags = { enableAutoReward: true };
    const result = service.isAutoRewardEnabled;
    expect(result).toBeTrue();
  });

  it('should return true if is enable free per LbDiscount BPD', () => {
    service.featureFlags = { enableFreePerLbDiscountBPD: true };
    const result = service.isEnableFreePerLbDiscountBPD;
    expect(result).toBeTrue();
  });

  it('should return true if is enable PercentOffPerLb Discount BPD', () => {
    service.featureFlags = { enablePercentOffPerLbDiscountBPD: true };
    const result = service.isEnablePercentOffPerLbDiscountBPD;
    expect(result).toBeTrue();
  });

  it('should return true if is enable FreePerLbDiscount SPD', () => {
    service.featureFlags = { enableFreePerLbDiscountSPD: true };
    const result = service.isEnableFreePerLbDiscountSPD;
    expect(result).toBeTrue();
  });

  it('should return true if is enable PercentOffPerLb Discount SPD', () => {
    service.featureFlags = { enablePercentOffPerLbDiscountSPD: true };
    const result = service.isEnablePercentOffPerLbDiscountSPD;
    expect(result).toBeTrue();
  });

  it('should return true if is enable FreePerLb Discount SC', () => {
    service.featureFlags = { enableFreePerLbDiscountSC: true };
    const result = service.isEnableFreePerLbDiscountSC;
    expect(result).toBeTrue();
  });

  it('should return true if is enable PercentOffPerLb Discount SC', () => {
    service.featureFlags = { enablePercentOffPerLbDiscountSC: true };
    const result = service.isEnablePercentOffPerLbDiscountSC;
    expect(result).toBeTrue();
  });

  it('should return true if is Enable FreePerLb Discount GR', () => {
    service.featureFlags = { enableFreePerLbDiscountGR: true };
    const result = service.isEnableFreePerLbDiscountGR;
    expect(result).toBeTrue();
  });

  it('should return true if is enable PercentOffPerLbD iscount GR', () => {
    service.featureFlags = { enablePercentOffPerLbDiscountGR: true };
    const result = service.isEnablePercentOffPerLbDiscountGR;
    expect(result).toBeTrue();
  });

  it('should return true if is enable ContinuityPoints Discount For SC', () => {
    service.featureFlags = { enableContinuityPointsDiscountForSC: true };
    const result = service.isEnableContinuityPointsDiscountForSC;
    expect(result).toBeTrue();
  });

  it('should return true if is enable ContinuityPoints Discount For SPD', () => {
    service.featureFlags = { enableContinuityPointsDiscountForSPD: true };
    const result = service.isEnableContinuityPointsDiscountForSPD;
    expect(result).toBeTrue();
  });

  it('should return true if is enable receipt engine feature', () => {
    service.featureFlags = { enableReceiptEngineFeature: true };
    const result = service.isEnableReceiptEngineFeature;
    expect(result).toBeTrue();
  });

  it('should return true if is enable Custom Usage Field', () => {
    spyOn(service, 'isFeatureFlagEnabled').and.returnValue(true);
    const result = service.isEnableCustomUsageField;
    expect(result).toBeTrue();
    expect(service.isFeatureFlagEnabled).toHaveBeenCalledWith('enableCustomUsage');
  });

  it('should return true if is GRGetMiles enabled', () => {
    spyOn(service, 'isFeatureFlagEnabled').and.returnValue(true);
    const result = service.isGRGetMilesEnabled;
    expect(result).toBeTrue();
    expect(service.isFeatureFlagEnabled).toHaveBeenCalledWith('enableGRGetMiles');
  });

  it('should return true if is Behavioral Continuity Enabled', () => {
    service.featureFlags = { enableBehavioralContinuity: true };
    const result = service.isBehavioralContinuityEnabled;
    expect(result).toBeTrue();
  });

  it('should return true if is Offer Edit Enabled', () => {
    service.featureFlags = { enableOfferEdit: true };
    const result = service.isOfferEditEnabled;
    expect(result).toBeTrue();
  });
  
});

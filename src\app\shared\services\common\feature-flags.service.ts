import { Injectable } from "@angular/core";
import { AppService } from "./app.service";
import { InitialDataService } from "./initial.data.service";

@Injectable({
  providedIn: "root",
})
export class FeatureFlagsService {
  public featureFlags: any;
  constructor(private appService: AppService,
    private initialDataService:InitialDataService) {
    this.assignFeatureFlag();
  }
  assignFeatureFlag() {
    this.featureFlags = this.appService.getFeatureFlags();
  }
  isFeatureFlagEnabled(flag: string) {
    if (!this.featureFlags) {
      this.assignFeatureFlag();
    }
    return this.featureFlags && this.featureFlags[flag];
  }
  hasFlags(flags: string | string[]): boolean {
    this.assignFeatureFlag();
    let flagArray = [];
    typeof flags === "string" ? flagArray.push(flags) : (flagArray = flags);
    let resBool = [];
    flagArray.forEach((flag) => {
      resBool.push(this.featureFlags?.[flag]);
    });
    return resBool.reduce((a, b) => a && b);
  }

  isUJActionEnabled(action) {
    const appData  = this.initialDataService.getAppData();
    if(appData && action) {
      const { featureFlagsUJ = [] } = appData;
      return featureFlagsUJ?.includes(action);
    }
    return false;

  }

  get isRedeemBenefitInSameTransactionEnabled(){
    return this.isFeatureFlagEnabled("enableRedeemBenefitInSameTransaction");
  }
  get forceArchival()
  {
    return this.isFeatureFlagEnabled("forceArchival");
  }
  get isArchivalEnabled()
  {
    /* As the functionality moved to server side and BE needs feature flag ON
       From UI setting the flag OFF always
       this.isOfferRequestArchivalEnabled || this.isOfferArchivalEnabled;
    */
    return this.forceArchival; 
  }

  get isOfferArchivalEnabled(){
    
    /* As the functionality moved to server side and BE needs feature flag ON
       From UI setting the flag OFF always
       this.isFeatureFlagEnabled("enableOfferArchival");
    */
    return this.forceArchival;
  }

  get isOfferRequestArchivalEnabled(){
    
    /* As the functionality moved to server side and BE needs feature flag ON
       From UI setting the flag OFF always
       this.isFeatureFlagEnabled("enableOfferRequestsArchival");
    */
    return false;
  }
  get isNoDiscountEnabled()
  {
    return this.isFeatureFlagEnabled("enableContinuityNoDiscount");
  }
  get isUPPFieldSearchEnabled(){
    return this.isFeatureFlagEnabled("enableUPPFieldsSearch");
  }

  get isuppEnabled()
  {
    return this.isFeatureFlagEnabled("uppEnable");;
  }

  get isDisclaimerApiEnabled(){
    return this.isFeatureFlagEnabled("enableDisclaimerApi");
  }

  get isNutritionTagsEnabled(){
    return this.isFeatureFlagEnabled("enableNutritionTags");
  }

  get isAutoRewardEnabled()
  {
    return this.isFeatureFlagEnabled("enableAutoReward")
  }

  // get isRewardsToPointsEnabled()
  // {
  //   return true;
  // }

  // get isRewardsSimplifiedEnabled()
  // {
  //   return true;
  // }

  // get isMassPointsEnabled()
  // {
  //   return true;
  // }

  get isEnableFreePerLbDiscountBPD()
  {
    return this.isFeatureFlagEnabled("enableFreePerLbDiscountBPD");
  }

  get isEnablePercentOffPerLbDiscountBPD()
  {
    return this.isFeatureFlagEnabled("enablePercentOffPerLbDiscountBPD");
  }
  get isEnableFreePerLbDiscountSPD()
  {
    return this.isFeatureFlagEnabled("enableFreePerLbDiscountSPD");
  }

  get isEnablePercentOffPerLbDiscountSPD()
  {
    return this.isFeatureFlagEnabled("enablePercentOffPerLbDiscountSPD");
  }
  
  get isEnableFreePerLbDiscountSC()
  {
    return this.isFeatureFlagEnabled("enableFreePerLbDiscountSC");
  }

  get isEnablePercentOffPerLbDiscountSC()
  {
    return this.isFeatureFlagEnabled("enablePercentOffPerLbDiscountSC");
  }

  get isEnableFreePerLbDiscountGR()
  {
    return this.isFeatureFlagEnabled("enableFreePerLbDiscountGR");
  }

  get isEnablePercentOffPerLbDiscountGR()
  {
    return this.isFeatureFlagEnabled("enablePercentOffPerLbDiscountGR");
  }
  
  get isEnableContinuityPointsDiscountForSC(){
    return this.isFeatureFlagEnabled("enableContinuityPointsDiscountForSC");
  }

  get isEnableContinuityPointsDiscountForSPD(){
    return this.isFeatureFlagEnabled("enableContinuityPointsDiscountForSPD");
  }

  get isEnableReceiptEngineFeature(){
    return this.isFeatureFlagEnabled("enableReceiptEngineFeature");
  }

  get isEnableCustomUsageField(){
    return this.isFeatureFlagEnabled("enableCustomUsage");
  }

  get isGRGetMilesEnabled(){
    return this.isFeatureFlagEnabled("enableGRGetMiles");
  }

  get isBehavioralContinuityEnabled(){
    return this.isFeatureFlagEnabled("enableBehavioralContinuity");
  }

  get isOfferEditEnabled(){
    return this.isFeatureFlagEnabled("enableOfferEdit");
  }
}

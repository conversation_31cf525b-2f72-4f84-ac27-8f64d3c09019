import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { FileAttachService } from './file-attach.service';
import { AuthService } from './auth.service';
import { InitialDataService } from './initial.data.service';
import { FileSaverService } from 'ngx-filesaver';
import { CommonService } from './common.service';
import { FeatureFlagsService } from './feature-flags.service';
import { CONSTANTS } from '@appConstants/constants';
import { of } from 'rxjs';


describe('FileAttachService', () => {
  let service: FileAttachService;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockApiConfigService: jasmine.SpyObj<InitialDataService>;
  let mockFileSaverService: jasmine.SpyObj<FileSaverService>;
  let mockCommonService: jasmine.SpyObj<CommonService>;
  let mockFeatureFlagService: jasmine.SpyObj<FeatureFlagsService>;

  beforeEach(() => {
    // Create spy objects for all dependencies
    mockAuthService = jasmine.createSpyObj('AuthService', [
      'getTokenString',
      'getUserId',
      'getUserDetails'
    ]);
    
    mockApiConfigService = jasmine.createSpyObj('InitialDataService', [
      'getConfigUrls',
      'getOcrpConfig',
      'getAppData'
    ]);
    
    mockFileSaverService = jasmine.createSpyObj('FileSaverService', ['save']);
    mockCommonService = jasmine.createSpyObj('CommonService', ['getHeaders']);
    mockFeatureFlagService = jasmine.createSpyObj('FeatureFlagsService', ['isUJActionEnabled']);

    // Configure mock return values
    mockAuthService.getTokenString.and.returnValue('mock-token');
    mockAuthService.getUserId.and.returnValue('user123');
    mockAuthService.getUserDetails.and.returnValue({ 
      firstName: 'John', 
      lastName: 'Doe',
      userId: 'user123',
      displayName: 'John Doe',
      emailId: '<EMAIL>',
      avatar: 'avatar-url',
      userPrincipalName: 'john.doe'
    });
    
    mockApiConfigService.getConfigUrls.and.returnValue('mock-url');
    mockApiConfigService.getOcrpConfig.and.returnValue({ apimKey: 'mock-key' });
    mockApiConfigService.getAppData.and.returnValue({ 
      azureBlobSasKeyForDownload: 'mock-sas-key' 
    });
    
    mockCommonService.getHeaders.and.returnValue({
      "content-type": "application/json",
      "X-Albertsons-Client-ID": "mock-client-id",
      "X-Albertsons-userAttributes": "mock-user-attributes",
    });
    mockFeatureFlagService.isUJActionEnabled.and.returnValue(false);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        FileAttachService,
        { provide: AuthService, useValue: mockAuthService },
        { provide: InitialDataService, useValue: mockApiConfigService },
        { provide: FileSaverService, useValue: mockFileSaverService },
        { provide: CommonService, useValue: mockCommonService },
        { provide: FeatureFlagsService, useValue: mockFeatureFlagService }
      ]
    });

    service = TestBed.inject(FileAttachService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getHeaders', () => {
    describe('getHeaders', () => {
      it('should return headers with token string', () => {
        const headers = service.getHeaders();
  
        expect(headers).toEqual({
          ...CONSTANTS.HTTP_HEADERS_MULTIPART,
          'X-Albertsons-userAttributes': 'mock-token',
        });
        expect(mockAuthService.getTokenString).toHaveBeenCalled();
      });
    })
  });  

  it('should handle empty token string', () => {
    mockAuthService.getTokenString.and.returnValue('');

    const headers = service.getHeaders();

    expect(headers).toEqual({
      ...CONSTANTS.HTTP_HEADERS_MULTIPART,
      'X-Albertsons-userAttributes': '',
    });
    expect(mockAuthService.getTokenString).toHaveBeenCalled();
  });

  describe('getPAHeaders', () => {
    it('should return headers with content-type multipart/form-data', () => {
      const headers = service.getPAHeaders();

      expect(headers).toEqual({
        ...CONSTANTS.HTTP_HEADERS_MULTIPART,
        'content-type': 'multipart/form-data'
      });
    });

    it('should include all headers from CONSTANTS.HTTP_HEADERS_MULTIPART', () => {
      const headers = service.getPAHeaders();

      for (const key in CONSTANTS.HTTP_HEADERS_MULTIPART) {
        if (CONSTANTS.HTTP_HEADERS_MULTIPART.hasOwnProperty(key)) {
          expect(headers[key]).toEqual(CONSTANTS.HTTP_HEADERS_MULTIPART[key]);
        }
      }
    });
  }) 

  describe('batchProcessDecline', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const action = 'decline';
      const id = '123';

      service.batchProcessDecline(action, id).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.put with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const action = 'decline';
      const id = '123';

      service.batchProcessDecline(action, id).subscribe();

      expect(stub).toHaveBeenCalledWith(`${service.batchImportFileApi}/${action}?id=${id}`, jasmine.any(Object));
    });
  })  

  describe('batchProcessBpdDecline', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const action = 'decline';
      const id = '123';

      service.batchProcessBpdDecline(action, id).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.put with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const action = 'decline';
      const id = '123';

      service.batchProcessBpdDecline(action, id).subscribe();

      expect(stub).toHaveBeenCalledWith(`${service.batchImportBpdFileApi}/${action}?id=${id}`, jasmine.any(Object));
    });
  })  

  describe('batchReupload', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const id = '123';

      service.batchReupload(fileData, id).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.put with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const id = '123';

      service.batchReupload(fileData, id).subscribe();

      expect(stub).toHaveBeenCalledWith(`${service.batchImportFileApi}/reupload?id=${id}`, jasmine.any(FormData), jasmine.any(Object));
    });
  }) 

  describe('batchReuploadBpd', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const id = '123';

      service.batchReuploadBpd(fileData, id).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.put with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const id = '123';

      service.batchReuploadBpd(fileData, id).subscribe();

      expect(stub).toHaveBeenCalledWith(`${service.batchImportBpdFileApi}/reupload?id=${id}`, jasmine.any(FormData), jasmine.any(Object));
    });
  }); 

  describe('getApiUrl', () => {
    it('should return bulkJobsUJ URL for CANCEL action when programCode is BPD', () => {
      const programCode = CONSTANTS.BPD;
      const action = 'CANCEL';
      const expectedUrl = `${service.bulkJobsUJ}/import/cancel`;

      spyOn(service, 'isUniversalJobEnabledFor').and.returnValue(false);

      const apiUrl = service.getApiUrl(programCode, action);

      expect(apiUrl).toBe(expectedUrl);
    });

    it('should return bulkJobsUJ URL for CREATE action when universal job is enabled', () => {
      const programCode = 'someProgramCode';
      const action = 'CREATE';
      const expectedUrl = `${service.bulkJobsUJ}/import/create`;

      spyOn(service, 'isUniversalJobEnabledFor').and.callFake((feature) => feature === 'CreateOR');

      const apiUrl = service.getApiUrl(programCode, action);

      expect(apiUrl).toBe(expectedUrl);
    });

    it('should return batchImportFileApi URL for unknown action', () => {
      const programCode = 'someProgramCode';
      const action = 'UNKNOWN_ACTION';
      const expectedUrl = service.batchImportFileApi;

      const apiUrl = service.getApiUrl(programCode, action);

      expect(apiUrl).toBe(expectedUrl);
    });

    it('should return batchImportFileApi URL when action is CANCEL and universal job is not enabled', () => {
      const programCode = 'someProgramCode';
      const action = 'CANCEL';
      const expectedUrl = service.batchImportFileApi;

      spyOn(service, 'isUniversalJobEnabledFor').and.returnValue(false);

      const apiUrl = service.getApiUrl(programCode, action);

      expect(apiUrl).toBe(expectedUrl);
    });

    it('should return bulkJobsUJ URL for CREATE_RECEIPT_OFFER action when universal job is enabled', () => {
      const programCode = 'someProgramCode';
      const action = 'CREATE_RECEIPT_OFFER';
      const expectedUrl = `${service.bulkJobsUJ}/import/create`;

      spyOn(service, 'isUniversalJobEnabledFor').and.callFake((feature) => feature === 'offerExportEnable');

      const apiUrl = service.getApiUrl(programCode, action);

      expect(apiUrl).toBe(expectedUrl);
    });
  });

  describe('isUniversalJobEnabledFor', () => {
    it('should return true when feature flag is enabled', () => {
      const action = 'someAction';
      mockFeatureFlagService.isUJActionEnabled.and.returnValue(true);

      const result = service.isUniversalJobEnabledFor(action);

      expect(result).toBe(true);
      expect(mockFeatureFlagService.isUJActionEnabled).toHaveBeenCalledWith(action);
    });

    it('should return false when feature flag is disabled', () => {
      const action = 'someAction';
      mockFeatureFlagService.isUJActionEnabled.and.returnValue(false);

      const result = service.isUniversalJobEnabledFor(action);

      expect(result).toBe(false);
      expect(mockFeatureFlagService.isUJActionEnabled).toHaveBeenCalledWith(action);
    });
  });  

  describe('uploadFileToOMS', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const rewardAction = 'someAction';

      service.uploadFileToOMS(fileData, rewardAction).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.post with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const rewardAction = 'someAction';

      service.uploadFileToOMS(fileData, rewardAction).subscribe();

      expect(stub).toHaveBeenCalledWith(service.batchImportPAFileOMSApi, jasmine.any(FormData));
    });

    it('should call http.post with correct FormData', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const rewardAction = 'someAction';

      service.uploadFileToOMS(fileData, rewardAction).subscribe();

      const formData = new FormData();
      formData.append('file', fileData, fileData.name);
      formData.append('rewardAction', rewardAction);

      expect(stub).toHaveBeenCalledWith(jasmine.any(String), formData);
    });
  })  

  describe('uploadImportPAFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const urlParams = 'param1=value1';
      const token = 'mock-token';

      service.uploadImportPAFile(fileData, urlParams, token).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.post with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const urlParams = 'param1=value1';
      const token = 'mock-token';
      const userId = 'user123';
      const user = {  
        firstName: 'John', 
        lastName: 'Doe',
        userId: 'user123',
        displayName: 'John Doe',
        emailId: '<EMAIL>',
        avatar: 'avatar-url',
        userPrincipalName: 'john.doe'
      };
      mockAuthService.getUserId.and.returnValue(userId);
      mockAuthService.getUserDetails.and.returnValue(user);

      service.uploadImportPAFile(fileData, urlParams, token).subscribe();

      const expectedUrl = `${service.batchImportPAFileApi}?${urlParams}&userId=${userId}-${user.firstName} ${user.lastName}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, jasmine.any(FormData), jasmine.any(Object));
    });
  }); 
  
  describe('uploadRewardsExtensionFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const urlParams = 'param1=value1';
      const token = 'mock-token';

      service.uploadRewardsExtensionFile(fileData, urlParams, token).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.post with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const urlParams = 'param1=value1';
      const token = 'mock-token';
      const userId = 'user123';
      const user = { 
        firstName: 'John', 
        lastName: 'Doe',
        userId: 'user123',
        displayName: 'John Doe',
        emailId: '<EMAIL>',
        avatar: 'avatar-url',
        userPrincipalName: 'john.doe'
       };
      mockAuthService.getUserId.and.returnValue(userId);
      mockAuthService.getUserDetails.and.returnValue(user);

      service.uploadRewardsExtensionFile(fileData, urlParams, token).subscribe();

      const expectedUrl = `${service.batchImportRewardsFileApi}?${urlParams}&userId=${userId}-${user.firstName} ${user.lastName}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, jasmine.any(FormData), jasmine.any(Object));
    });
  }) 
  
  describe('uploadBPDImportFileEdit', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const paramJSON = { key: 'value' };

      service.uploadBPDImportFileEdit(fileData, paramJSON).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.post with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const paramJSON = { key: 'value' };

      service.uploadBPDImportFileEdit(fileData, paramJSON).subscribe();

      const expectedUrl = `${service.batchImportBpdFileEditApi}?paramJson=${encodeURIComponent(JSON.stringify(paramJSON))}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, jasmine.any(FormData), jasmine.any(Object));
    });
  })  

  describe('importLog', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const query = { key: 'value' };

      service.importLog(query).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.post with correct URL and parameters', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const query = { key: 'value' };
      const headers = {
        "content-type": "application/json",
        "X-Albertsons-Client-ID": "mock-client-id",
        "X-Albertsons-userAttributes": "mock-user-attributes",
      };
      mockCommonService.getHeaders.and.returnValue(headers);

      service.importLog(query).subscribe();

      const expectedUrl = service.batchImportLogApi;
      const expectedParams = {
        query,
        includeTotalCount: true,
        isImport: true,
        reqObj: { headers }
      };
      expect(stub).toHaveBeenCalledWith(expectedUrl, expectedParams);
    });

    it('should call commonService.getHeaders', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const query = { key: 'value' };

      service.importLog(query).subscribe();

      expect(mockCommonService.getHeaders).toHaveBeenCalled();
    });

    it('should include query, includeTotalCount, and isImport in request body', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const query = { key: 'value' };

      service.importLog(query).subscribe();

      const expectedParams = {
        query,
        includeTotalCount: true,
        isImport: true,
        reqObj: { headers: jasmine.any(Object) }
      };
      expect(stub).toHaveBeenCalledWith(jasmine.any(String), expectedParams);
    });
  });

  describe('getOCRPHeaders', () => {
    it('should return headers with Ocp-Apim-Subscription-Key and Authorization', () => {
      const token = 'mock-token';
      const apimKey = 'mock-apim-key';
      mockApiConfigService.getOcrpConfig.and.returnValue({ apimKey });

      const headers = service.getOCRPHeaders(token);

      expect(headers).toEqual({
        'Ocp-Apim-Subscription-Key': apimKey,
        'Authorization': `Bearer ${token}`
      });
      expect(mockApiConfigService.getOcrpConfig).toHaveBeenCalled();
    });

    it('should handle empty token', () => {
      const token = '';
      const apimKey = 'mock-apim-key';
      mockApiConfigService.getOcrpConfig.and.returnValue({ apimKey });

      const headers = service.getOCRPHeaders(token);

      expect(headers).toEqual({
        'Ocp-Apim-Subscription-Key': apimKey,
        'Authorization': `Bearer ${token}`
      });
      expect(mockApiConfigService.getOcrpConfig).toHaveBeenCalled();
    });

    it('should handle null token', () => {
      const token = null;
      const apimKey = 'mock-apim-key';
      mockApiConfigService.getOcrpConfig.and.returnValue({ apimKey });

      const headers = service.getOCRPHeaders(token);

      expect(headers).toEqual({
        'Ocp-Apim-Subscription-Key': apimKey,
        'Authorization': `Bearer ${token}`
      });
      expect(mockApiConfigService.getOcrpConfig).toHaveBeenCalled();
    });
  });

  describe('importPALog', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of({}));
      const query = 'param1=value1;';
      const token = 'mock-token';

      service.importPALog(query, token).subscribe();

      expect(stub).toHaveBeenCalled();
    });
  }); 
  
  
  describe('importPALogCancel', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const jobId = '123';
      const token = 'mock-token';

      service.importPALogCancel(jobId, token).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call getOCRPHeaders with correct token', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const jobId = '123';
      const token = 'mock-token';
      const headers = { 'Authorization': `Bearer ${token}`, 'Ocp-Apim-Subscription-Key': 'mock-key' };
      const getOCRPHeadersSpy = spyOn(service, 'getOCRPHeaders').and.returnValue(headers);
      mockApiConfigService.getOcrpConfig.and.returnValue({ apimKey: 'mock-key' });

      service.importPALogCancel(jobId, token).subscribe();

      expect(getOCRPHeadersSpy).toHaveBeenCalledWith(token);
    });

    it('should call authService.getUserId and authService.getUserDetails', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const jobId = '123';
      const token = 'mock-token';

      service.importPALogCancel(jobId, token).subscribe();

      expect(mockAuthService.getUserId).toHaveBeenCalled();
      expect(mockAuthService.getUserDetails).toHaveBeenCalled();
    });
  });

  describe('getToken', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of({}));

      service.getToken().subscribe();

      expect(stub).toHaveBeenCalled();
    });
  });  

  describe('markDoneImport', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of({}));
      const id = '123';

      service.markDoneImport(id).subscribe();

      expect(stub).toHaveBeenCalled();
    });
  }) 

  describe('getErrorFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of(new Blob()));
      const id = '123';

      service.getErrorFile(id).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.get with correct URL and headers for default pCodeType', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of(new Blob()));
      const id = '123';
      const headers = {
        ...CONSTANTS.HTTP_HEADERS,
        "X-Albertsons-userAttributes": mockAuthService.getTokenString()
      };

      service.getErrorFile(id).subscribe();

      const expectedUrl = `${service.batchImportErrorFileApi}?id=${id}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, { headers, responseType: 'blob' as any });
    });

    it('should call http.get with correct URL and headers for BPD pCodeType', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of(new Blob()));
      const id = '123';
      const pCodeType = CONSTANTS.BPD;
      const headers = {
        ...CONSTANTS.HTTP_HEADERS,
        "X-Albertsons-userAttributes": mockAuthService.getTokenString()
      };

      service.getErrorFile(id, pCodeType).subscribe();

      const expectedUrl = `${service.batchImportErrorFileBPDApi}?id=${id}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, { headers, responseType: 'blob' as any });
    });

    it('should handle null pCodeType', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of(new Blob()));
      const id = '123';
      const pCodeType = null;
      const headers = {
        ...CONSTANTS.HTTP_HEADERS,
        "X-Albertsons-userAttributes": mockAuthService.getTokenString()
      };

      service.getErrorFile(id, pCodeType).subscribe();

      const expectedUrl = `${service.batchImportErrorFileApi}?id=${id}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, { headers, responseType: 'blob' as any });
    });

    it('should handle undefined pCodeType', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of(new Blob()));
      const id = '123';
      const pCodeType = undefined;
      const headers = {
        ...CONSTANTS.HTTP_HEADERS,
        "X-Albertsons-userAttributes": mockAuthService.getTokenString()
      };

      service.getErrorFile(id, pCodeType).subscribe();

      const expectedUrl = `${service.batchImportErrorFileApi}?id=${id}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, { headers, responseType: 'blob' as any });
    });
  }) 

  describe('uploadFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const requestID = '123';

      service.uploadFile(fileData, requestID).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.put with correct URL and FormData', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const requestID = '123';

      service.uploadFile(fileData, requestID).subscribe();

      const expectedUrl = `${service.attachFileApi}?id=${requestID}`;
      const formData = new FormData();
      formData.append('file', fileData, fileData.name);
      expect(stub).toHaveBeenCalledWith(expectedUrl, formData);
    });

    it('should return an observable', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const requestID = '123';

      const result = service.uploadFile(fileData, requestID);

      expect(result).toBeTruthy();
      expect(result.subscribe).toBeDefined();
    });
  });

  describe('removeFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const requestID = '123';
      const fileName = 'filename';

      service.removeFile(requestID, fileName).subscribe();

      expect(stub).toHaveBeenCalled();
    });

    it('should call http.put with correct URL', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const requestID = '123';
      const fileName = 'filename';

      service.removeFile(requestID, fileName).subscribe();

      const expectedUrl = `${service.removeFileApi}?id=${requestID}&fileName=${requestID}_${fileName}`;
      expect(stub).toHaveBeenCalledWith(expectedUrl, {});
    });

    it('should return an observable', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      spyOn(httpClientStub, 'put').and.returnValue(of({}));
      const requestID = '123';
      const fileName = 'filename';

      const result = service.removeFile(requestID, fileName);

      expect(result).toBeTruthy();
      expect(result.subscribe).toBeDefined();
    });
  });

  describe('downloadFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of({ body: new Blob() }));
      const fileName = 'test-file.txt';
      const url = 'https://example.com/file';

      service.downloadFile(fileName, url);

      expect(stub).toHaveBeenCalled();
    });
  });

  
  describe('replaceAzureBlobUrl', () => {

    it('should replace source with target for dev environment', () => {
      service.ocrpAccessTokenApi = 'https://ocom.dev.westus.aks.az.albertsons.com';
      const url = 'https://emomnpstorage.blob.core.windows.net/file';
      const expectedUrl = 'https://ocomtemplatedevst01.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(expectedUrl);
    });

    it('should replace source with target for qa1 environment', () => {
      service.ocrpAccessTokenApi = 'https://ocom.qa1.westus.aks.az.albertsons.com';
      const url = 'https://emomnpstorage.blob.core.windows.net/file';
      const expectedUrl = 'https://ocomtemplateqa1st01.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(expectedUrl);
    });

    it('should replace source with target for qa2 environment', () => {
      service.ocrpAccessTokenApi = 'https://ocom.qa2.westus.aks.az.albertsons.com';
      const url = 'https://emomnpstorage.blob.core.windows.net/file';
      const expectedUrl = 'https://ocomtemplateqa2st01.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(expectedUrl);
    });

    it('should replace source with target for perf1 environment', () => {
      service.ocrpAccessTokenApi = 'https://ocom.perf1.westus.aks.az.albertsons.com';
      const url = 'https://emomnpstorage.blob.core.windows.net/file';
      const expectedUrl = 'https://ocomtemplateperf1st01.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(expectedUrl);
    });

    it('should replace source with target for stage environment', () => {
      service.ocrpAccessTokenApi = 'https://ocom.stage.westus.aks.az.albertsons.com';
      const url = 'https://emomstgstorage.blob.core.windows.net/file';
      const expectedUrl = 'https://ocomtemplatestagest01.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(expectedUrl);
    });

    it('should replace source with target for prod environment', () => {
      service.ocrpAccessTokenApi = 'https://ocom.prod.westus.aks.az.albertsons.com';
      const url = 'https://emomprodstorage.blob.core.windows.net/file';
      const expectedUrl = 'https://ocomtemplateprodst01.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(expectedUrl);
    });

    it('should return original URL if source is not found in URL', () => {
      service.ocrpAccessTokenApi = 'https://ocom.dev.westus.aks.az.albertsons.com';
      const url = 'https://unknownstorage.blob.core.windows.net/file';

      const result = service.replaceAzureBlobUrl(url);

      expect(result).toBe(url);
    });
  });

  describe('downloadPAFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'get').and.returnValue(of({ body: new Blob() }));
      const fileName = 'test-file';
      const token = 'mock-token';

      service.downloadPAFile(fileName, token);

      expect(stub).toHaveBeenCalled();
    });
  });

  describe('importLogBpd', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const query = { key: 'value' };

      service.importLogBpd(query).subscribe();

      expect(stub).toHaveBeenCalled();
    });
  })

  describe('getFileName', () => {
    it('should return the filename from Content-Disposition header', () => {
      const headers = new HttpHeaders({
        'Content-Disposition': 'attachment; filename="test-file.txt"'
      });
    
      const result = service.getFileName(headers);
    
      expect(result).toBe('"test-file.txt"');
    });
  });

  it('should make expected calls', () => {
    const httpClientStub = TestBed.inject(HttpClient);
    const stub = spyOn(httpClientStub, 'post').and.returnValue(of({ body: new Blob(), headers: new HttpHeaders({ 'Content-Disposition': 'attachment; filename="test-file.txt"' }) }));
    const url = 'https://example.com/file';
    const queryParam = { key: 'value' };

    service.downloadFileWithRequestParams(url, queryParam);

    expect(stub).toHaveBeenCalled();
  });

  describe('uploadImportFile', () => {
    it('should make expected calls', () => {
      const httpClientStub = TestBed.inject(HttpClient);
      const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
      const fileData = new File([''], 'filename');
      const paramJSON = { programCode: 'testCode', importAction: 'testAction' };

      service.uploadImportFile(fileData, paramJSON).subscribe();

      expect(stub).toHaveBeenCalled();
    });
  })
});
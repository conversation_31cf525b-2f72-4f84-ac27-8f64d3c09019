import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { BATCH_IMPORT_CONSTANTS } from "@appModules/admin/constants/batch_import_constants";
import { FileSaverService } from "ngx-filesaver";
import { Subject } from 'rxjs';
import { AuthService } from "./auth.service";
import { CommonService } from "./common.service";
import { FeatureFlagsService } from "./feature-flags.service";
import { InitialDataService } from "./initial.data.service";

@Injectable({
  providedIn: "root"
})
export class FileAttachService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private apiConfigService: InitialDataService,
    private fileSaverService: FileSaverService,
    private commonService: CommonService,
    private featureFlagService:FeatureFlagsService
  ) { 
    // intentionally left empty
  }
  batchImportLogResponse$ = new Subject();
  batchImportLogResponsePA$ = new Subject();
  attachFileApi: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.ATTACH_FILE_API
  );
  removeFileApi: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.REMOVE_FILE_API
  );
  batchImportFileApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_FILE_API
  );
  bulkJobsUJ:string = this.apiConfigService.getConfigUrls(
    CONSTANTS.BULK_JOBS_UJ
  )
  batchImportPAFileApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORTPA_FILE_API
  );
  batchImportRewardsFileApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_REWARDS_EXT_FILE_API
  );
  
  batchImportPAFileOMSApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORTPA_FILE_OMS_API
  );
  batchImportBpdFileApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_BPD_FILE_API
  );
  batchImportBpdFileEditApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_BPD_FILE_EDIT_API
  );
  batchImportLogApi: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.BATCHIMPORT_FILE_LOG_API
  );
  batchImportPALogApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORTPA_FILE_LOG_API
  );
  ocrpAccessTokenApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.OCRPACCESSTOKENAPI
  );
  batchImportPALogCancelApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORTPA_FILE_LOG_CANCEL_API
  );
  batchImportDoneApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_DONE_API
  );
  batchImportErrorFileApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_ERROR_FILE_API
  );
  batchImportErrorFileBPDApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_ERROR_FILE_API_BPD
  );
  batchImportPATemplateFileApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_PA_TEMPLATE_FILE_API
  );
  batchImportLogBpdApi: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.BATCHIMPORT_FILE_LOG_BPD_API
  );
  batchImportBpdDoneApi: string = this.apiConfigService.getConfigUrls(
    BATCH_IMPORT_CONSTANTS.BATCHIMPORT_BPD_DONE_API
  );
  bulkActionBPDAPI: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.BULK_ACTION_BPD_OR
)
 

  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS_MULTIPART,
      'X-Albertsons-userAttributes': this.authService.getTokenString(),
    };
  }

  getPAHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS_MULTIPART,
      'content-type':'multipart/form-data'
    };
  }

  batchProcessDecline(action,id){
    const headerCopy = this.commonService.getHeaders();
    let reqBody = {};
    const params = { ...reqBody, reqObj: { headers: headerCopy } };
    return this.http.put(`${this.batchImportFileApi}/${action}?id=${id}`, params);
  }

  batchProcessBpdDecline(action,id){
    const headerCopy = this.commonService.getHeaders();
    let reqBody = {};
    const params = { ...reqBody, reqObj: { headers: headerCopy } };
    return this.http.put(`${this.batchImportBpdFileApi}/${action}?id=${id}`, params);
  }

  batchReupload(fileData,id) {
    let file = new FormData();
    file.append("file", fileData, fileData.name);
    return this.http.put(
      `${this.batchImportFileApi}/reupload?id=${id}`, 
      file ,
      { headers: new HttpHeaders(this.getHeaders()) }
      );
  }
  /**
   * Function used in Admin/BatchImport functionality
   * @param fileData 
   * @param paramJSON 
   * 
   */
   batchReuploadBpd(fileData,id) {
    let file = new FormData();
    file.append("file", fileData, fileData.name);
    return this.http.put(
      `${this.batchImportBpdFileApi}/reupload?id=${id}`, 
      file ,
      { headers: new HttpHeaders(this.getHeaders()) }
      );
  }

  uploadImportFile(fileData,paramJSON) {

    //let apiUrl = this.batchImportFileApi;
    // if (paramJSON?.programCode === CONSTANTS.BPD && paramJSON?.importAction === "CANCEL") {

    //   apiUrl = `${this.bulkActionBPDAPI}/import/cancel`;
    // }
    // else if([CONSTANTS.SPD,CONSTANTS.GR].includes(paramJSON?.programCode))
    // {
    //   if(this.isUniversalJobEnabledFor("CancelOR") && paramJSON?.importAction === "CANCEL")
    //     apiUrl = `${this.bulkJobsUJ}/import/cancel`
    //   else if(this.isUniversalJobEnabledFor("CreateOR") && paramJSON?.importAction === "CREATE")
    //     apiUrl = `${this.bulkJobsUJ}/import/create`
    //   else if(this.isUniversalJobEnabledFor("EditOR") && paramJSON?.importAction === "EDIT")
    //     apiUrl = `${this.bulkJobsUJ}/import/edit`
    // }
    let apiUrl = this.getApiUrl(paramJSON?.programCode,paramJSON?.importAction); 

    let file = new FormData();
    file.append("file", fileData, fileData.name);
    return this.http.post(
      `${apiUrl}?paramJson=${encodeURIComponent(JSON.stringify(paramJSON))}`, 
      file ,
      { headers: new HttpHeaders(this.getHeaders()) }
      );
  }
  getApiUrl(programCode: string, action: string): string {
    const actionsMapping = {
      CANCEL: () => programCode === CONSTANTS.BPD || this.isUniversalJobEnabledFor("CancelOR") ? `${this.bulkJobsUJ}/import/cancel` : null,
      CREATE: () => this.isUniversalJobEnabledFor("CreateOR") ? `${this.bulkJobsUJ}/import/create` : null,
      EDIT: () => this.isUniversalJobEnabledFor("EditOR") ? `${this.bulkJobsUJ}/import/edit` : null,
      CREATE_RECEIPT_OFFER: () => this.isUniversalJobEnabledFor("offerExportEnable") ? `${this.bulkJobsUJ}/import/create` : null
    };
  
    const apiUrl = actionsMapping[action]?.();
    return apiUrl || this.batchImportFileApi;
  }
  
  isUniversalJobEnabledFor(action){
    return this.featureFlagService.isUJActionEnabled(action);
  }

  uploadFileToOMS(fileData, rewardAction){
    let file = new FormData();
    file.append("file", fileData, fileData.name);
    file.append('rewardAction', rewardAction)
    return this.http.post(this.batchImportPAFileOMSApi, file);
  }

  uploadImportPAFile(fileData,urlParams,token) {
    let file = new FormData();
    const userId = this.authService.getUserId();
    const user = this.authService.getUserDetails();
    urlParams = `${urlParams}&userId=${userId}-${user.firstName} ${user.lastName}`;
    file.append("file", fileData, fileData.name);
    return this.http.post(
      `${this.batchImportPAFileApi}?${urlParams}`, 
      file,
      { headers: new HttpHeaders(this.getOCRPHeaders(token)) }
      );
  }
  uploadRewardsExtensionFile(fileData, urlParams, token){
    let formData = new FormData();
    formData.append("file", fileData, fileData.name);
    const userId = this.authService.getUserId();
    const user = this.authService.getUserDetails();
    urlParams = `${urlParams}&userId=${userId}-${user.firstName} ${user.lastName}`;
    return this.http.post(
      `${this.batchImportRewardsFileApi}?${urlParams}`,
      formData,
      { headers: new HttpHeaders(this.getOCRPHeaders(token)) }
      );
  }
  uploadBPDImportFileEdit(fileData,paramJSON) {
    const file = new FormData();
    file.append("file", fileData, fileData.name);
    return this.http.post(
      `${this.batchImportBpdFileEditApi}?paramJson=${encodeURIComponent(JSON.stringify(paramJSON))}`, 
      file ,
      { headers: new HttpHeaders(this.getHeaders()) }
      );
  }
  /**
   * Function used in Admin/BatchImport functionality
   * @param query  
   */
  public importLog(query: any) {
    const headerCopy = this.commonService.getHeaders();
    let reqBody = {};
    reqBody = { query, includeTotalCount: true ,isImport:true};
    const params = { ...reqBody, reqObj: { headers: headerCopy } };
    return this.http.post(this.batchImportLogApi, params);
  }

  getOCRPHeaders(token){
    let key = this.apiConfigService.getOcrpConfig().apimKey;
    return {
      'Ocp-Apim-Subscription-Key':key,
      'Authorization': `Bearer ${token}`
    }
  }

  public importPALog(query: any, token:string) {
    let reqParams = query.substring(0,query.length-1).split(";").join("&");
    return this.http.get(`${this.batchImportPALogApi}?${reqParams}`,{ headers: new HttpHeaders(this.getOCRPHeaders(token)) });
	}  

  public importPALogCancel(jobId: any,token:string) {
    const userId = this.authService.getUserId();
    const user = this.authService.getUserDetails();
    let reqParams = `?jobId=${jobId}&userDetails=${userId}-${user.firstName} ${user.lastName}`;
    return this.http.put(`${this.batchImportPALogCancelApi}${reqParams}`,{},{ headers: new HttpHeaders(this.getOCRPHeaders(token)) });
	}  

  public getToken(){
    return this.http.get(this.ocrpAccessTokenApi);
  }
/**
 * Function used in Admin/BatchImport functionality for the DONE
 * @param id 
 */
 markDoneImport(id) {
    let reqBody: string;
    reqBody = `?id=${id}`;
    return this.http.get(`${this.batchImportDoneApi}${reqBody}`);
  }
  /**
   * 
   * @param id 
   * Admin/BatchImport functionality for the Error File
   */
   markDoneImportBpd(id) {
    let reqBody: string;
    reqBody = `?id=${id}`;
    return this.http.post(`${this.batchImportBpdDoneApi}${reqBody}`,{});
  }
  getErrorFile(id,pCodeType = null) {
    const headers = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString()
    }, url = pCodeType && pCodeType === CONSTANTS.BPD ? this.batchImportErrorFileBPDApi : this.batchImportErrorFileApi;
    return this.http.get(`${url}?id=${id}`, { headers: headers, responseType: 'blob' })
    
  }
  uploadFile(fileData, requestID) {
    let file = new FormData();
    file.append("file", fileData, fileData.name);
    let reqBody = "?id=" + requestID;
    return this.http.put(`${this.attachFileApi}${reqBody}`, file);
  }

  removeFile(requestID, fileName) {
    let reqBody =
      "?id=" + requestID + "&fileName=" + requestID + "_" + fileName;
    return this.http.put(`${this.removeFileApi}${reqBody}`, {});
  }

  downloadFile(fileName, url) {
    if(!fileName)
      fileName = url.split('/').pop();
    let azureDownloadURL = this.replaceAzureBlobUrl(url) + this.apiConfigService.getAppData().azureBlobSasKeyForDownload;
    this.http
      .get(azureDownloadURL, {
        observe: "response",
        responseType: "blob"
      })
      .subscribe(res => {
        this.fileSaverService.save(res.body, fileName);
      });
    return;
  }

  replaceAzureBlobUrl(url) {
    const envMapping = {
        "dev": { source: "emomnpstorage", target: "ocomtemplatedevst01" },
        "qa1": { source: "emomnpstorage", target: "ocomtemplateqa1st01" },
        "qa2": { source: "emomnpstorage", target: "ocomtemplateqa2st01" },
        "perf1": { source: "emomnpstorage", target: "ocomtemplateperf1st01" },
        "stage": { source: "emomstgstorage", target: "ocomtemplatestagest01" },
        "prod": { source: "emomprodstorage", target: "ocomtemplateprodst01" },
    };

    const match = this.ocrpAccessTokenApi.match(/ocom\.(\w+)\.westus\.aks\.az\.albertsons\.com/);
   
    const currentEnv = match ? match[1] : null;
        
    if (!currentEnv) return url; // Return original URL if environment is not matched

    const { source, target } = envMapping[currentEnv];

    if (url.includes(source)) {
        return url.replace(source, target);
    }

    return url;
}

  downloadPAFile(fileName,token) {
    const headers = this.getOCRPHeaders(token)
    let param = `?requestedTemplate=${fileName}`
    this.http
      .get(`${this.batchImportPATemplateFileApi}${param}`, {
        headers,
        observe: "response",
        responseType: "blob"
      })
      .subscribe(res => {
        this.fileSaverService.save(res.body, `${fileName}.xlsx`);
      });
    return;
  }
  
  public importLogBpd(query: any) {
    const headerCopy = this.commonService.getHeaders();
    let reqBody = {};
    reqBody = { query, includeTotalCount: true };
    const params = { ...reqBody, reqObj: { headers: headerCopy } };
    return this.http.post(this.batchImportLogBpdApi, params);
  }

  getFileName(headers){
   const contentDisposition = headers?.get('Content-Disposition');
  return contentDisposition?.split("filename=")?.[1]?.trim();
  }
  downloadFileWithRequestParams(url,queryParam) {
      this.http.post(url, queryParam,{
        observe: "response",
        responseType: "blob"
      }).subscribe((response:any)=> {
         const{body,headers} = response;
         this.fileSaverService.save(body, this.getFileName(headers));
       });
  }
}

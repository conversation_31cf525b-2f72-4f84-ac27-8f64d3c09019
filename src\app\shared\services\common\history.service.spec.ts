
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HistoryService } from './history.service';
import { InitialDataService } from './initial.data.service';
import { FeatureFlagsService } from './feature-flags.service';
import { of } from 'rxjs';
import { CONSTANTS } from '@appConstants/constants';

class InitialDataServiceMock {
  getConfigUrls(param) {
    return `${param}-mock-url`;
  }
}

describe('HistoryService', () => {
  let service: HistoryService;
  let httpMock: HttpTestingController;
  let initialDataServiceSpy: jasmine.SpyObj<InitialDataService>;
  let featureFlagsServiceSpy: jasmine.SpyObj<FeatureFlagsService>;

  beforeEach(() => {
    // const initialDataServiceMock = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);
    const featureFlagsServiceMock = jasmine.createSpyObj('FeatureFlagsService', ['isFeatureFlagEnabled']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        HistoryService,
        { provide: InitialDataService, useClass: InitialDataServiceMock },
        { provide: FeatureFlagsService, useValue: featureFlagsServiceMock },
      ],
    });

    service = TestBed.inject(HistoryService);
    httpMock = TestBed.inject(HttpTestingController);
    initialDataServiceSpy = TestBed.inject(
      InitialDataService
    ) as jasmine.SpyObj<InitialDataService>;
    featureFlagsServiceSpy = TestBed.inject(
      FeatureFlagsService
    ) as jasmine.SpyObj<FeatureFlagsService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should build user name for Product Group', () => {
    const groupPage = 'Product Group';
    const historyPreviewItem = {
      createdUser: ['John,Doe,(123)', 'Jane,Smith,(456)'],
    };

    const result = service.buildUserName(groupPage, historyPreviewItem);
    expect(result).toBe('Doe 123,Smith 456');
  });

  it('should build user name for non-Product Group', () => {
    const groupPage = 'Other';
    const historyPreviewItem = {
      createdUser: { firstName: 'John', lastName: 'Doe' },
    };

    const result = service.buildUserName(groupPage, historyPreviewItem);
    expect(result).toBe('John Doe');
  });

  it('should fetch OR/OT history details by ID', () => {
    const id = '123';
    const type = 'OR';
    const mockUrl = CONSTANTS.GET_OR_HISTORY + '-mock-url';

    service.getOROTHistoryDetailsById(id, type).subscribe((response) => {
      expect(response).toEqual({ success: true });
    });

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=123',
      fetchFields: ['auditMessage', 'createdUser', 'changeset'],
    });
    req.flush({ success: true });
  });

  it('should call getOfferHistoryPreviewByOfferId and emit data', () => {
    const offerId = '123';
    const mockResponse = { success: true };
    const mockUrl = CONSTANTS.GET_OFFER_HISTORY + '-mock-url';

    service.getHistoryOffersPreviewData().subscribe((data) => {
      expect(data).toEqual(mockResponse);
    });

    service.getOfferHistoryPreviewByOfferId(offerId);

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=123',
      fetchFields: ['auditMessage', 'createdUser'],
    });
    req.flush(mockResponse);
  });

  it('should fetch group history details by ID', () => {
    const groupId = '456';
    const fetchFields = ['auditMessage'];
    const groupPage = 'Customer Group';
    const mockUrl = CONSTANTS.GET_CG_HISTORY + '-mock-url';

    service.getHistoryDetailsDataForGroups(groupId, fetchFields, groupPage).subscribe((response) => {
      expect(response).toEqual({ success: true });
    });

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=456',
      fetchFields: ['auditMessage'],
    });
    req.flush({ success: true });
  });

  it('should handle feature flag for Store Group API', () => {
    const groupId = '789';
    const fetchFields = ['auditMessage'];
    const groupPage = 'Store Group';
    const mockUrl = CONSTANTS.GET_SG_HISTORY + '-mock-url';

    featureFlagsServiceSpy.isFeatureFlagEnabled.and.returnValue(true);

    service.getHistoryDetailsDataForGroups(groupId, fetchFields, groupPage).subscribe((response) => {
      expect(response).toEqual({ success: true });
    });

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=789',
      fetchFields: ['auditMessage'],
    });
    req.flush({ success: true });
  });


  it('should call getGroupsHistoryById and emit data', () => {
    const groupId = '456';
    const fetchFields = ['auditMessage'];
    const groupPage = 'Product Group';
    const mockUrl = CONSTANTS.GET_PG_HISTORY + '-mock-url';

    service.getHistoryGroupsPreviewData().subscribe((data) => {
      expect(data).toEqual({ success: true });
    });

    service.getGroupsHistoryById(groupId, fetchFields, groupPage);

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=456',
      fetchFields: ['auditMessage'],
    });
    req.flush({ success: true });
  });

  it('should handle Product Group case in getHistoryDetailsDataForGroups', () => {
    const groupId = '123';
    const fetchFields = ['auditMessage'];
    const groupPage = 'Product Group';
    const mockUrl = CONSTANTS.GET_PG_HISTORY + '-mock-url';

    service.getHistoryDetailsDataForGroups(groupId, fetchFields, groupPage).subscribe((response) => {
      expect(response).toEqual({ success: true });
    });

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=123',
      fetchFields: ['auditMessage'],
    });
    req.flush({ success: true });
  });

  it('should fetch history details data for offers', () => {
    const offerId = '789';
    const mockUrl = CONSTANTS.GET_OFFER_HISTORY + '-mock-url';

    service.getHistoryDetailsDataForOffers(offerId).subscribe((response) => {
      expect(response).toEqual({ success: true });
    });

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=789',
      fetchFields: ['auditMessage', 'createdUser', 'changeset'],
    });
    req.flush({ success: true });
  });

  it('should call getOROTHistoryPreviewById and emit data', () => {
    const id = '101';
    const type = 'OR';
    const mockUrl = CONSTANTS.GET_OR_HISTORY + '-mock-url';
    const mockResponse = { success: true };

    service.getHistoryPreviewSubject().subscribe((data) => {
      expect(data).toEqual(mockResponse);
    });

    service.getOROTHistoryPreviewById(id, type);

    const req = httpMock.expectOne(mockUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      query: 'id=101',
      fetchFields: ['auditMessage', 'createdUser'],
    });
    req.flush(mockResponse);
  });
});

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from "@appConstants/constants";
import { Observable, Subject } from "rxjs";
import { FeatureFlagsService } from './feature-flags.service';
import { InitialDataService } from './initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class HistoryService {
  private historyPreviewData$ = new Subject<any>();
  groupHistoryPreviewData$ = new Subject<any>();
  offerHistoryPreviewData$ = new Subject<any>();
  productGroupHistoryPreviewData$ = new Subject<any>();
  getORHistoryAPI: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_OR_HISTORY);
  getGroupsHistoryApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_CONFIG_HISTORY);
  getOffersHistoryApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_OFFER_HISTORY);
  getOTHistoryAPI: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_OR_HISTORY);
  getProductGroupsHistoryApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_PG_HISTORY);
  getCustomerGroupsHistoryApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_CG_HISTORY);
  getStoreGroupsHistoryApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_SG_HISTORY)


  constructor(private http: HttpClient,
              private apiConfigService: InitialDataService,
              private featureFlagService:FeatureFlagsService
  ) { 
    // intentionally left empty
  }

  public buildUserName(groupPage, historyPreviewItem) {
    if (groupPage && groupPage === 'Product Group') {
      const users = historyPreviewItem.createdUser;
      return users.map(u => {
        const splitted = u.replace('(','').replace(')','').split(',');
        return `${splitted[1]} ${splitted[2]}`;
      }).join(',');
    }
    return `${historyPreviewItem.createdUser.firstName} ${historyPreviewItem.createdUser.lastName}`;
  }

 public getOROTHistoryDetailsById(id, type = 'OR') {
  const reqObj = {
    query: 'id=' + id,
    fetchFields: ['auditMessage', 'createdUser', 'changeset'],
    };
    const apiUrl = type === 'OT' ? this.getOTHistoryAPI : this.getORHistoryAPI;
    return this.http.post(apiUrl, reqObj);
  }
  public getOfferHistoryPreviewByOfferId(offerId) {
    const reqObj = {
      query: 'id=' + offerId,
      fetchFields: ['auditMessage', 'createdUser']
    };
    this.http.post(this.getOffersHistoryApi, reqObj).subscribe(previewDataResponse => {
      this.offerHistoryPreviewData$.next(previewDataResponse);
    });
  }
  getHistoryDetailsDataForOffers(offerId) {
    const reqObj = {
      query: 'id=' + offerId,
      fetchFields: ['auditMessage', 'createdUser', 'changeset']
    };
    return this.http.post(this.getOffersHistoryApi, reqObj);
  }

public getOROTHistoryPreviewById(id, type = 'OR') {
  const reqObj = {
    query: 'id=' + id,
    fetchFields: ['auditMessage', 'createdUser']
  };
  const apiUrl = type === 'OT' ? this.getOTHistoryAPI : this.getORHistoryAPI;
  this.http.post(apiUrl, reqObj).subscribe(previewDataResponse => {
    this.historyPreviewData$.next(previewDataResponse);
  });
}
getHistoryOffersPreviewData(): Observable<any> {
  return this.offerHistoryPreviewData$.asObservable();
}
getHistoryPreviewSubject(): Observable<any> {
  return this.historyPreviewData$.asObservable();
}

getHistoryDetailsDataForGroups(groupId, fetchFields, groupPage ) {
  const groupObj = {
    query: 'id=' + groupId,
    fetchFields
  };
  let apiUrl = this.getGroupsHistoryApi; 
  if (groupPage==='Product Group') {
    apiUrl = this.getProductGroupsHistoryApi;
  }
    if (groupPage === 'Customer Group' ){
    apiUrl=this.getCustomerGroupsHistoryApi;
    }
    if (groupPage === 'Store Group' && this.featureFlagService.isFeatureFlagEnabled('isStoreGroupApiEnabled')) {
      apiUrl = this.getStoreGroupsHistoryApi;

    }
  return this.http.post(apiUrl, groupObj);
}
public getGroupsHistoryById(groupId, fetchFields, groupPage=null ) {
  const groupObj = {
    query: 'id=' + groupId,
    fetchFields
  };
  let apiUrl = this.getGroupsHistoryApi; 
    if (groupPage==='Product Group') {
    apiUrl = this.getProductGroupsHistoryApi;
  }
    if (groupPage === 'Customer Group'){
    apiUrl=this.getCustomerGroupsHistoryApi;
    }
    if (groupPage === 'Store Group' && this.featureFlagService.isFeatureFlagEnabled('isStoreGroupApiEnabled')) {
        apiUrl = this.getStoreGroupsHistoryApi;
    }
  this.http.post(apiUrl, groupObj).subscribe(previewDataResponse => {
    this.groupHistoryPreviewData$.next(previewDataResponse);
  });
}
 
getHistoryGroupsPreviewData(): Observable<any> {
  return this.groupHistoryPreviewData$.asObservable();
}
}
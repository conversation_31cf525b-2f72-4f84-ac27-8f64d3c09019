import { TestBed } from '@angular/core/testing';
import { DomSanitizer } from '@angular/platform-browser';
import { SecurityContext } from '@angular/core';
import { InitialDataService } from '../common/initial.data.service';
import { CONSTANTS } from '../../constants/constants';

describe('InitialDataService', () => {
  let service: InitialDataService;
  let sanitizer: DomSanitizer;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        InitialDataService,
        { provide: DomSanitizer, useValue: { sanitize: jasmine.createSpy('sanitize').and.returnValue('sanitizedValue') } }
      ]
    });
    service = TestBed.inject(InitialDataService);
    sanitizer = TestBed.inject(DomSanitizer);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize initialData correctly', () => {
    expect(service.initialData).toBe(window["initialData"]?.replace(/&amp;/g, "&"));
  });

  it('should return config URLs', () => {
    service['_initialData'] = { apiUrls: { testApi: 'testUrl' } };
    expect(service.getConfigUrls('testApi')).toBe('testUrl');
  });

  it('should return search options', () => {
    service['_initialData'] = { searchOptions: ['option1', 'option2'] };
    expect(service.getSearchOptions()).toEqual(['option1', 'option2'] as any);
  });

  it('should return offer fields to export', () => {
    service['_initialData'] = { offerFieldsToExport: ['field1', 'field2'] };
    expect(service.getOfferFieldsToExport()).toEqual(['field1', 'field2'] as any);
  });

  it('should return default offer fields to export if none are provided', () => {
    service['_initialData'] = {};
    expect(service.getOfferFieldsToExport()).toEqual(["Category", "Offer Description", "Priority", "Defer Evaluation Until EOS"] as any);
  });

  it('should return UPP fields to disable', () => {
    service['_initialData'] = { appData: { defaultFields: ['field1', 'field2'] } };
    expect(service.getUppFieldsToDisable()).toEqual(['field1', 'field2']);
  });

  it('should set default UPP fields to disable if none are provided', () => {
    service['_initialData'] = { appData: {} };
    expect(service.getUppFieldsToDisable()).toEqual(["nonDigitalRedemptionStoreGroupIds", "id", "productGroupName", "amount", "programCode", "couponChannel", "customerSegment", "offerLimits", "brandAndSize", "billingOptions", "type"]);
  });

  it('should handle error when parsing initial data', () => {
  spyOn(console, 'error');
  const invalidJson = 'invalidJson';
  service['initialData'] = invalidJson;
  const originalInitialData = service['_initialData'];
  
  // Force re-evaluation of _initialData with invalid JSON
  service['_initialData'] = (() => {
    const decodedData = decodeURI(invalidJson.replace(/&#39;/g, "'"));
    try {
      return JSON.parse(decodedData);
    } catch (e) {
      console.error('Error parsing initial data: ', e);
      return {};
    }
  })();

  expect(console.error).toHaveBeenCalledWith('Error parsing initial data: ', jasmine.any(Error));
  
  service['_initialData'] = originalInitialData;
});

  it('should log sanitized value to console', () => {
    spyOn(window.console, 'log');
    service.sendLogToConsole('<div>test</div>');
    expect(sanitizer.sanitize).toHaveBeenCalledWith(SecurityContext.HTML, '<div>test</div>');
    expect(window.console.log).toHaveBeenCalledWith('sanitizedValue');
  });

  it('should throw error if value could not be sanitized', () => {
    (sanitizer.sanitize as jasmine.Spy).and.returnValue(null);
    expect(() => service.sendLogToConsole('<div>test</div>')).toThrowError('Value could not be sanitized');
  });

  it('should return offers limit to export', () => {
    service['_initialData'] = { appData: { offersLimitToExport: 3000 } };
    expect(service.getOffersLimitToExport()).toEqual(3000);
  });
  
  it('should return default offers limit to export if not provided', () => {
    service['_initialData'] = { appData: {} };
    expect(service.getOffersLimitToExport()).toEqual(5000);
  });
  
  it('should return app data with default values', () => {
    service['_initialData'] = { appData: { amountTypes: {} } };
    const appData = service.getAppData();
    
    expect(appData.uomConfig).toEqual(CONSTANTS.CONTINUTY_UOM_CONFIG);
    
    expect(appData.amountTypes).toBeDefined();
    expect(appData.amountTypes["POINTS"]).toEqual("Points");
    expect(appData.amountTypes["NO_DISCOUNT"]).toEqual("No Discount");
    
    expect(appData.offerUsageLimitCustomUsage).toEqual({
      "": "Select Custom Usage",
      "ALL_IN_ONE_TRANSACTION": "All in one transaction",
      "ONE_LIMIT_PER_TRANSACTION": "One limit per transaction"
    });
    
    expect(appData.behavioralContinuityActions).toEqual(["Transaction Completion"]);
    
    expect(appData.allocationCriteria).toEqual([
      { value: "Trial-Monthly", label: "Trial Monthly" },
      { value: "Trial-Annual", label: "Trial Annual" },
      { value: "Active-Monthly", label: "Active Monthly" },
      { value: "Active-Annual", label: "Active Annual" },
      { value: "Pending_Cancellation-Monthly", label: "Pending Cancellation Monthly" },
      { value: "Pending_Cancellation-Annual", label: "Pending Cancellation Annual" }
    ]);
    
    expect(appData.offerSubProgram).toEqual([
      { value: "HTO", label: "Household" },
      { value: "MC", label: "Store" }
    ]);
    
    expect(appData.imagePreviewConfig).toEqual({
      previewSubscriptionKey: '731d1378556843db8a63273e29e6ac39',
      imageSearchUrl: 'https://ocom-qa2.albertsons.com/abs/qa2int/ocom/offer-service/offerimages/find/',
      imageFindUrl: 'https://ocom-qa2.albertsons.com/abs/qa2int/ocom/offer-service/offerimages/search',
      absImagesClientId: 'a444af5ab6dd463a917fa2b265b5e895'
    });
  });
  
  it('should handle existing app data correctly', () => {
    service['_initialData'] = {
      appData: {
        offerDeliveryChannelsSPD: { "EXISTING": "Existing Channel" },
        offerDeliveryChannels: { "EXISTING": "Existing Channel" },
        uomConfig: { "EXISTING": "Existing Config" },
        amountTypes: { "EXISTING": "Existing Type" },
        offerUsageLimitCustomUsage: { "EXISTING": "Existing Usage" },
        behavioralContinuityActions: ["Existing Action"],
        allocationCriteria: [
          { value: "Existing-Criteria", label: "Existing Criteria" }
        ],
        offerSubProgram: [
          { value: "Existing-SubProgram", label: "Existing SubProgram" }
        ],
        batchImportConfig: { O: { name: "Existing Offers (O)" } },
        imagePreviewConfig: { previewSubscriptionKey: 'existingKey' }
      }
    };
    const appData = service.getAppData();
    expect(appData.offerDeliveryChannelsSPD["BAC"]).toEqual("Behavioral Continuity");
    expect(appData.offerDeliveryChannels["BAC"]).toEqual("Behavioral Continuity");
    expect(appData.offerDeliveryChannels["IR"]).toEqual("Inlane Receipt");
    expect(appData.uomConfig).toEqual({ "EXISTING": "Existing Config" });
    expect(appData.amountTypes["POINTS"]).toEqual("Points");
    expect(appData.amountTypes["NO_DISCOUNT"]).toEqual("No Discount");
    expect(appData.offerUsageLimitCustomUsage["EXISTING"]).toEqual("Existing Usage");
    expect(appData.behavioralContinuityActions).toEqual(["Existing Action"]);
    expect(appData.allocationCriteria).toEqual([
      { value: "Existing-Criteria", label: "Existing Criteria" }
    ]);
    expect(appData.offerSubProgram).toEqual([
      { value: "Existing-SubProgram", label: "Existing SubProgram" }
    ]);
    expect(appData.batchImportConfig.O.name).toEqual("Existing Offers (O)");
    expect(appData.imagePreviewConfig.previewSubscriptionKey).toEqual('existingKey');
  });

  it('should format allocationCriteria correctly when it is in invalid format', () => {
    service['_initialData'] = {
      appData: {
        amountTypes: {},
        allocationCriteria: ["Trial-Monthly", "Active-Annual", "Pending_Cancellation-Monthly"]
      }
    };
  
    const appData = service.getAppData();
  
    expect(appData.allocationCriteria).toEqual([
      { value: "Trial-Monthly", label: "Trial Monthly" },
      { value: "Active-Annual", label: "Active Annual" },
      { value: "Pending_Cancellation-Monthly", label: "Pending Cancellation Monthly" }
    ]);
  });
  
  it('should handle error when accessing _initialData', () => {
    spyOn(console, 'error');
    service['initialData'] = 'invalidJson';
    const originalInitialData = service['_initialData'];
  
    service['_initialData'] = (() => {
      const decodedData = decodeURI(service['initialData'].replace(/&#39;/g, "'"));
      try {
        return JSON.parse(decodedData);
      } catch (e) {
        console.error('Configuration Data Not Loaded Properly. Please reload the page and try again.');
        return {};
      }
    })();
  
    try {
      service.getAppData();
    } catch (e) {
      // Expected error to be thrown
    }
  
    expect(console.error).toHaveBeenCalledWith('Configuration Data Not Loaded Properly. Please reload the page and try again.');
  
    service['_initialData'] = originalInitialData;
  });

  it('should validate allocation criteria format correctly', () => {
    const validArray = [
      { value: 'value1', label: 'label1' },
      { value: 'value2', label: 'label2' }
    ];
    const invalidArray = [
      { value: 'value1', label: 'label1' },
      { value: 'value2' }
    ];
    expect(service.isAllocationCriteriaInValidFormat(validArray)).toBeTrue();
    expect(service.isAllocationCriteriaInValidFormat(invalidArray)).toBeFalse();
  });

  it('should log sanitized value to console', () => {
    spyOn(window.console, 'log');
    (sanitizer.sanitize as jasmine.Spy).and.returnValue('sanitizedValue');
    service.sendLogToConsole('<div>test</div>');
    expect(sanitizer.sanitize).toHaveBeenCalledWith(SecurityContext.HTML, '<div>test</div>');
    expect(window.console.log).toHaveBeenCalledWith('sanitizedValue');
  });

  it('should throw error if value could not be sanitized', () => {
    (sanitizer.sanitize as jasmine.Spy).and.returnValue(null);
    expect(() => service.sendLogToConsole('<div>test</div>')).toThrowError('Value could not be sanitized');
  });

  it('should check for clip option for offer limit', () => {
    service['_initialData'] = {
      appData: {
        offerLimitsGR: { ONCE_PER_CLIP: 'value1', OTHER: 'value2' },
        podUsageLimitsGR: { MULTI_CLIP: 'value3', OTHER: 'value4' }
      }
    };
    spyOnProperty(service, 'isMultiClipEnabled', 'get').and.returnValue(false);
    service.checkForClipOptionForOfferLimit();
    expect(service['_initialData'].appData.offerLimitsGR).toEqual({ OTHER: 'value2' });
    expect(service['_initialData'].appData.podUsageLimitsGR).toEqual({ OTHER: 'value4' });
  });

  it('should not modify offer limits if multi-clip is enabled', () => {
    service['_initialData'] = {
      appData: {
        offerLimitsGR: { ONCE_PER_CLIP: 'value1', OTHER: 'value2' },
        podUsageLimitsGR: { MULTI_CLIP: 'value3', OTHER: 'value4' }
      }
    };
    spyOnProperty(service, 'isMultiClipEnabled', 'get').and.returnValue(true);
    service.checkForClipOptionForOfferLimit();
    expect(service['_initialData'].appData.offerLimitsGR).toEqual({ ONCE_PER_CLIP: 'value1', OTHER: 'value2' });
    expect(service['_initialData'].appData.podUsageLimitsGR).toEqual({ MULTI_CLIP: 'value3', OTHER: 'value4' });
  });

  it('should check sub program code based on flag', () => {
    spyOn(service, 'checkForSubPrgrmCdFilter');
    service.divisionalGameEnabled = false;
    service.featuresFlag = true;
    service.checkSubProgramCdBasedOnFlag();
    expect(service.divisionalGameEnabled).toBeTrue();
    expect(service.checkForSubPrgrmCdFilter).toHaveBeenCalled();
  });

  it('should not check sub program code if divisional game is already enabled', () => {
    spyOn(service, 'checkForSubPrgrmCdFilter');
    service.divisionalGameEnabled = true;
    service.featuresFlag = true;
    service.checkSubProgramCdBasedOnFlag();
    expect(service.divisionalGameEnabled).toBeTrue();
    expect(service.checkForSubPrgrmCdFilter).not.toHaveBeenCalled();
  });

  it('should check for sub program code filter', () => {
    service['_initialData'] = {
      appData: {
        offerRequestFiltersGR: [
          { facetMapper: 'subProgramCode' },
          { facetMapper: 'other' }
        ],
        offerFiltersGR: [
          { facetMapper: 'subProgramCode' },
          { facetMapper: 'other' }
        ]
      }
    };
    spyOnProperty(service, 'isDivisionalGameEnabled', 'get').and.returnValue(false);
    service.checkForSubPrgrmCdFilter();
    expect(service['_initialData'].appData.offerRequestFiltersGR).toEqual([{ facetMapper: 'other' }]);
    expect(service['_initialData'].appData.offerFiltersGR).toEqual([{ facetMapper: 'other' }]);
  });

  it('should not modify filters if divisional game is enabled', () => {
    service['_initialData'] = {
      appData: {
        offerRequestFiltersGR: [
          { facetMapper: 'subProgramCode' },
          { facetMapper: 'other' }
        ],
        offerFiltersGR: [
          { facetMapper: 'subProgramCode' },
          { facetMapper: 'other' }
        ]
      }
    };
    spyOnProperty(service, 'isDivisionalGameEnabled', 'get').and.returnValue(true);
    service.checkForSubPrgrmCdFilter();
    expect(service['_initialData'].appData.offerRequestFiltersGR).toEqual([
      { facetMapper: 'subProgramCode' },
      { facetMapper: 'other' }
    ]);
    expect(service['_initialData'].appData.offerFiltersGR).toEqual([
      { facetMapper: 'subProgramCode' },
      { facetMapper: 'other' }
    ]);
  });

  it('should return true if multi-clip is enabled', () => {
    service.featuresFlag = { isMulticlipEnabled: true };
    expect(service.isMultiClipEnabled).toBeTrue();
  });

  it('should return false if multi-clip is not enabled', () => {
    service.featuresFlag = { isMulticlipEnabled: false, enableSpdMultiClip: false };
    expect(service.isMultiClipEnabled).toBeFalse();
  });
  
  it('should return true if multi-clip is enabled via enableSpdMultiClip', () => {
    service.featuresFlag = { enableSpdMultiClip: true };
    expect(service.isMultiClipEnabled).toBeTrue();
  });

  it('should return true if divisional game is enabled', () => {
    service.featuresFlag = { isDivisionalGamesFeatureEnabled: true };
    expect(service.isDivisionalGameEnabled).toBeTrue();
  });

  it('should return false if divisional game is not enabled', () => {
    service.featuresFlag = { isDivisionalGamesFeatureEnabled: false };
    expect(service.isDivisionalGameEnabled).toBeFalse();
  });

  it('should return ocrpSetting from _initialData', () => {
    // Mocking the initialData
    const mockData = {
      ocrpSetting: { key: 'value' }
    };
    service['_initialData'] = mockData;
    expect(service.getOcrpConfig()).toEqual({ key: 'value' });
  });

  it('should return allocationCriteriaCode from app data', () => {
    spyOn(service, 'getAppData').and.returnValue({ allocationCriteriaCode: '30' });

    expect(service.getAllocationCriteriaCode()).toBe('30');
  });

  it('should return default allocationCriteriaCode of "25" if not found', () => {
    spyOn(service, 'getAppData').and.returnValue({});
    expect(service.getAllocationCriteriaCode()).toBe('25');
  });

  it('should return the specific property from appData', () => {
    spyOn(service, 'getAppData').and.returnValue({ customProperty: 'testValue' });
    expect(service.getAppDataName('customProperty')).toBe('testValue');
  });

  it('should return searchOfferOptions from _initialData', () => {
    const mockData = { searchOfferOptions: ['option1', 'option2'] };
    service['_initialData'] = mockData;
    expect(service.getSearchOfferOptions()).toEqual(['option1', 'option2']);
  });
  
  it('should return pluSearchOptions from _initialData', () => {
    const mockData = { pluSearchOptions: ['plu1', 'plu2'] };
    service['_initialData'] = mockData;
    expect(service.getPluSearchOptions()).toEqual(['plu1', 'plu2']);
  });

  it('should return storeClosureProductGroupProperties from appData', () => {
    spyOn(service, 'getAppData').and.returnValue({ storeClosureProductGroupProperties: ['group1', 'group2'] });
    expect(service.getProductGroupsData()).toEqual(['group1', 'group2']);
  });

  it('should return defaultGroups from appData', () => {
    spyOn(service, 'getAppDataName').and.returnValue('testGroup');
    expect(service.getDefaultGroups()).toBe('testGroup');
  });

  it('should return searchOfferPODOptions from _initialData', () => {
    const mockData = { searchOfferPODOptions: ['optionA', 'optionB'] };
    service['_initialData'] = mockData;
    expect(service.searchOfferPODOptions).toEqual(['optionA', 'optionB']);
  });

  it('should open a URL in a new tab and focus on it', () => {
    const mockWindow = { focus: jasmine.createSpy('focus') };
    
    const openSpy = spyOn(window, 'open').and.returnValue(mockWindow as any);

    const url = 'https://example.com';
    service.openInNewTab(url);
    expect(openSpy).toHaveBeenCalledWith(url, '_blank');
    expect(mockWindow.focus).toHaveBeenCalled();
  });

  it('should return the key corresponding to the value in the property object', () => {
    spyOn(service, 'getAppDataName').and.returnValue({ key1: 'value1', key2: 'value2' });

    expect(service.getPropertyValue('someProperty', 'value1')).toBe('key1');
  });

  it('should return undefined if the value is not found in the property object', () => {
    spyOn(service, 'getAppDataName').and.returnValue({ key1: 'value1', key2: 'value2' });

    expect(service.getPropertyValue('someProperty', 'value3')).toBeUndefined();
  });

  it('should set NutriTags in _initialData', () => {
    const nutriTags = ['tag1', 'tag2'];
    service.setNutriTags(nutriTags);

    expect(service['_initialData']['NutriTags']).toEqual(nutriTags);
  });

  it('should return NutriTags from _initialData', () => {
    const mockNutriTags = ['tag1', 'tag2'];
    service['_initialData'] = { NutriTags: mockNutriTags };

    expect(service.getNutriTags()).toEqual(mockNutriTags);
  });

});
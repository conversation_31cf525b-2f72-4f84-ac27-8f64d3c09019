/* Purpose: Provides config data(API endpoints).
It reads the data from the <script> which is send by BE as part of the initial page load.  */
import { Injectable, SecurityContext } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { CONSTANTS } from "@appConstants/constants";

@Injectable({
  providedIn: "root"
})
export class InitialDataService {
  constructor(private sanitizer: DomSanitizer){
    /*Intentionally Left Blank */
  }
  initialData = window["initialData"]?.replace(/&amp;/g, "&");
  featuresFlag;
  multiClipEnable = false;
  divisionalGameEnabled = false;
  private _initialData = (() => {
    const decodedData = decodeURI(this.initialData?.replace(/&#39;/g, "'"));
    const dataSizeInKB = new Blob([decodedData]).size / 1024;
    if (dataSizeInKB < 35) {
      console.error("Configuration Data Not Loaded Properly. Please reload the page and try again.");
      alert("Configuration data not loaded properly. Please reload the page and try again.");
      return {}; 
    }
    try {
      return JSON.parse(decodedData);
    } catch (e) {
      console.error("Error parsing initial data: ", e);
      return {}; 
    }
  })();
  getConfigUrls(apiName: string): string {
    return this._initialData.apiUrls[apiName];
  }
  getSearchOptions(programCode = "SC"): [] {
    return this._initialData[`searchOptions${programCode === "SC" ? "" : programCode}`];
  }
  getOfferFieldsToExport(programCode = "SC"): [] {
    return this._initialData[`offerFieldsToExport${programCode === "SC" ? "" : programCode}`] || ["Category","Offer Description","Priority","Defer Evaluation Until EOS"];
  }
  getUppFieldsToDisable() {
    if (!this._initialData.appData.defaultFields) {
        this._initialData.appData.defaultFields = ["nonDigitalRedemptionStoreGroupIds", "id","productGroupName", "amount","programCode", "couponChannel", "customerSegment", "offerLimits","brandAndSize","billingOptions","type"];
    }
    return this._initialData.appData.defaultFields;
  }

  getOffersLimitToExport(){
    return this._initialData.appData.offersLimitToExport || 5000;
  }

  getAppData() {
    if(!this.multiClipEnable && this.featuresFlag) {
      this.multiClipEnable = true;
      this.checkForClipOptionForOfferLimit();
    }

    this.checkSubProgramCdBasedOnFlag();

    let _offerDeliveryChannelsSPD = this._initialData.appData.offerDeliveryChannelsSPD;
    if(_offerDeliveryChannelsSPD && !_offerDeliveryChannelsSPD["BAC"]){
      _offerDeliveryChannelsSPD = {
        ..._offerDeliveryChannelsSPD,"BAC": "Behavioral Continuity"
      }
      this._initialData.appData.offerDeliveryChannelsSPD = _offerDeliveryChannelsSPD;
    }

    let _offerDeliveryChannels = this._initialData.appData.offerDeliveryChannels;

    if(_offerDeliveryChannels && !_offerDeliveryChannels["BAC"]){
      _offerDeliveryChannels = {
        ..._offerDeliveryChannels,"BAC": "Behavioral Continuity"
      }
      this._initialData.appData.offerDeliveryChannels = _offerDeliveryChannels;
    }

    if(_offerDeliveryChannels && !_offerDeliveryChannels["IR"]){
      _offerDeliveryChannels = {
        ..._offerDeliveryChannels,"IR": "Inlane Receipt"
      }
      this._initialData.appData.offerDeliveryChannels = _offerDeliveryChannels;
    }

    let _uomConfig = this._initialData.appData.uomConfig;
    if(!_uomConfig){
      _uomConfig = CONSTANTS.CONTINUTY_UOM_CONFIG;
      this._initialData.appData.uomConfig = _uomConfig;
    }
    
    if(this._initialData.appData.amountTypes && !this._initialData.appData.amountTypes?.["POINTS"])
      this._initialData.appData.amountTypes["POINTS"] = "Points";
    if(this._initialData.appData.amountTypes && !this._initialData.appData.amountTypes?.["NO_DISCOUNT"])
      this._initialData.appData.amountTypes["NO_DISCOUNT"] = "No Discount";
    

    if(!this._initialData.appData.offerUsageLimitCustomUsage)
    {
      this._initialData.appData.offerUsageLimitCustomUsage = {
        "":"Select Custom Usage",
        "ALL_IN_ONE_TRANSACTION": "All in one transaction",
        "ONE_LIMIT_PER_TRANSACTION": "One limit per transaction"
      };
    }
    else{
      this._initialData.appData.offerUsageLimitCustomUsage = {
        "":"Select Custom Usage",
        ...this._initialData.appData.offerUsageLimitCustomUsage
      }
    }

    if(!this._initialData.appData.behavioralContinuityActions){
      this._initialData.appData.behavioralContinuityActions = ["Transaction Completion"];
    }
    
    let _allocationCriteria = this._initialData.appData.allocationCriteria;
    let _allocationCriteriaOptions = null;
    if(!_allocationCriteria){
      let obj = {
        "Trial-Monthly": "Trial Monthly",
        "Trial-Annual": "Trial Annual",
        "Active-Monthly": "Active Monthly",
        "Active-Annual": "Active Annual",
        "Pending_Cancellation-Monthly": "Pending Cancellation Monthly",
        "Pending_Cancellation-Annual": "Pending Cancellation Annual"
      };
      _allocationCriteriaOptions = Object.keys(obj).map(key => {
        return { value: key, label: obj[key] };
      });
      this._initialData.appData.allocationCriteria = _allocationCriteriaOptions;
    }
    else if(_allocationCriteria && Array.isArray(_allocationCriteria)){
      if(!this.isAllocationCriteriaInValidFormat(_allocationCriteria)){
        let obj = _allocationCriteria.reduce((acc, item) => {
          // Replace '-' and '_' with space
          const formattedValue = item.replace(/[-_]/g, ' ');
          acc[item] = formattedValue;
          return acc;
        }, {});
        _allocationCriteriaOptions = Object.keys(obj).map(key => {
          return { value: key, label: obj[key] };
        });
        this._initialData.appData.allocationCriteria = _allocationCriteriaOptions;
      }
    }


    let _offerSubProgram = this._initialData.appData.offerSubProgram;
    let _offerSubProgramOptions = null;
    if(!_offerSubProgram){
      let obj = {
        "HTO": "Household",
        "MC": "Store"
      };
      _offerSubProgramOptions = Object.keys(obj).map(key => {
        return { value: key, label: obj[key] };
      });
      this._initialData.appData.offerSubProgram = _offerSubProgramOptions;
    }

    let batchImportConfig = this._initialData.appData.batchImportConfig;
    if(batchImportConfig)
    {
      //Check whether O is present in the batchImportConfig
      if(!batchImportConfig.O)
      {
        batchImportConfig.O = {
          name: "Offers (O)",
          programCodes: ["SC"],
          createSCOTemplateName: "SC_Receipt_Messages.xlsm",
          editSCOTemplateName: "Offer-Import_Edit_Template.xlsm"
        };
      }
      
    }


    let imagePreviewConfig =  this._initialData.appData.imagePreviewConfig;
    if(!imagePreviewConfig){
      imagePreviewConfig = {
        previewSubscriptionKey:'731d1378556843db8a63273e29e6ac39',
        imageSearchUrl: 'https://ocom-qa2.albertsons.com/abs/qa2int/ocom/offer-service/offerimages/find/',
        imageFindUrl: 'https://ocom-qa2.albertsons.com/abs/qa2int/ocom/offer-service/offerimages/search',
        absImagesClientId: 'a444af5ab6dd463a917fa2b265b5e895'
      }
      this._initialData.appData.imagePreviewConfig = imagePreviewConfig;
    }

    return this._initialData.appData;
  }
  isAllocationCriteriaInValidFormat(arr: any[]): boolean {
    return arr.every(item => 
      item && 
      typeof item === 'object' && 
      'value' in item && 
      'label' in item &&
      typeof item.value === 'string' &&
      typeof item.label === 'string'
    );
  }
  sendLogToConsole(value: any) {
    const sanitizedValue = this.sanitizer.sanitize(SecurityContext.HTML, value);
    if (sanitizedValue !== null) {
        window.console.log(sanitizedValue);
    } else {
        throw new Error('Value could not be sanitized');
    }
}

  // If Multiclip feature flag is off we need to hide ONCE PER CLIP AND MULTI CLIP OPtion from config data keys
  checkForClipOptionForOfferLimit() {
    const offerLimits =  this._initialData?.appData?.offerLimitsGR,
    podUsageLimits = this._initialData?.appData?.podUsageLimitsGR;
    if(offerLimits && podUsageLimits) {
      if (!this.isMultiClipEnabled) {
        const {ONCE_PER_CLIP, ...restOfferLimits} = offerLimits,
         {MULTI_CLIP, ...restPodUsageOptions} = podUsageLimits;
        this._initialData.appData.offerLimitsGR = restOfferLimits;
        this._initialData.appData.podUsageLimitsGR = restPodUsageOptions;
      }
    }
  }
  checkSubProgramCdBasedOnFlag() {
    if(!this.divisionalGameEnabled && this.featuresFlag) {
      this.divisionalGameEnabled = true;
      this.checkForSubPrgrmCdFilter();
    }
  }
  checkForSubPrgrmCdFilter() {
    let offerRequestFiltersGR =  this._initialData?.appData?.offerRequestFiltersGR,
    offerFiltersGR = this._initialData?.appData?.offerFiltersGR; 
    
    if(offerRequestFiltersGR && offerFiltersGR) {
      if (!this.isDivisionalGameEnabled) {
        const toShowGrORFilter = offerRequestFiltersGR.filter(ele => ele["facetMapper"] !== "subProgramCode"),
        toShowOfferGRFilter = offerFiltersGR.filter(ele => ele["facetMapper"] !== "subProgramCode")
        this._initialData.appData.offerRequestFiltersGR = toShowGrORFilter;
        this._initialData.appData.offerFiltersGR = toShowOfferGRFilter;
      }
    }
  }
  get isMultiClipEnabled() {
    return this.featuresFlag?.['isMulticlipEnabled'] || this.featuresFlag['enableSpdMultiClip'];
  }
  get isDivisionalGameEnabled() {
    return this.featuresFlag?.['isDivisionalGamesFeatureEnabled'];
  }
  getOcrpConfig(){
    return this._initialData.ocrpSetting;
  }
  getAllocationCriteriaCode(){
    return this.getAppData()["allocationCriteriaCode"] || "25";
  }
  getAppDataName(name) {
    return this.getAppData()[name];
  }
  getSearchOfferOptions() {
    return this._initialData.searchOfferOptions;
  }
  getPluSearchOptions() {
    return this._initialData.pluSearchOptions;
  }
  getProductGroupsData() {
    return this.getAppData().storeClosureProductGroupProperties;
  }
  getDefaultGroups() {
    return this.getAppDataName('defaultGroups');
  }

  get searchOfferPODOptions() {
    return this._initialData.searchOfferPODOptions;
  }
  openInNewTab(url) {
    let win = window.open(url, "_blank");
    win.focus();
  }
  getPropertyValue(property, value) {
    const propertyObject = this.getAppDataName(property);
    if (propertyObject) {
      return Object.keys(propertyObject).find((key) => propertyObject[key] === value);
    }
  }
  setNutriTags(nutriTags){
    this._initialData["NutriTags"] = nutriTags;
  }
  getNutriTags()
  {
    return this._initialData["NutriTags"];
  }
}

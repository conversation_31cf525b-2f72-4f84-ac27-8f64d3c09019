import { FacetItem } from '@appModels/offer-request.model';
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { IviePromotionService } from './ivie-promotion.service';
import { HttpClient } from '@angular/common/http';
import { AuthService } from './auth.service';
import { InitialDataService } from './initial.data.service';
import { of, throwError } from 'rxjs';

describe('IviePromotionService', () => {
    let service: IviePromotionService;
    let authService: AuthService;
    let httpMock: HttpTestingController;
    let initialDataService: InitialDataService;
    let httpClient: HttpClient;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                IviePromotionService,
                { provide: AuthService, useValue: { getTokenString: () => 'mockToken' } },
                { provide: InitialDataService, useValue: { getConfigUrls: (key: string) => `mockUrl/${key}` } }
            ]
        });
        httpClient = TestBed.inject(HttpClient);
        service = TestBed.inject(IviePromotionService);
        authService = TestBed.inject(AuthService);
        initialDataService = TestBed.inject(InitialDataService);
        httpMock = TestBed.inject(HttpTestingController);
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe("getFacetCountData",()=>{
        const facetFilters = [
            {
                "displayValue": "Program Code",
                "configMapper": "offerPrograms",
                "facetMapper": "offerProgramCd"
            },
            {
                "displayValue": "Channel",
                "configMapper": "offerDeliveryChannels",
                "facetMapper": "deliveryChannel"
            },
            {
                "displayValue": "Digital/Non-Digital",
                "configMapper": "digital",
                "facetMapper": "digital"
            },
            {
                "displayValue": "eComm Promo Type",
                "configMapper": "ecommPromoCodeTypeChannel",
                "facetMapper": "eCommPromoType"
            },
            {
                "displayValue": "Offer Type",
                "configMapper": "offerType",
                "facetMapper": "offerType"
            },
            {
                "displayValue": "Discount",
                "configMapper": "amountTypes",
                "facetMapper": "discountType"
            },
            {
                "displayValue": "groups",
                "configMapper": "offerRequestGroups",
                "facetMapper": "offerRequestorGroup"
            },
            {
                "displayValue": "Status",
                "configMapper": "offerStatuses",
                "facetMapper": "offerStatus"
            },
            {
                "displayValue": "Category",
                "configMapper": "customerFriendlyProductCategories",
                "facetMapper": "categories"
            },
            {
                "displayValue": "Events",
                "configMapper": "events",
                "facetMapper": "eventids"
            },
            {
                "displayValue": "POD Approved",
                "configMapper": "podApprovedStatus",
                "facetMapper": "podStatus"
            },
            {
                "displayValue": "Source",
                "configMapper": "sources",
                "facetMapper": "createdAppId"
            }
        ];
        const facetCounts = {
            "offerProgramCd": {
                "BPD": 32
            },
            "digital": {
                "true": 32
            },
            "offerType": null,
            "podStatus": {},
            "hiddenEvents": {},
            "deliveryChannel": {
                "DO": 32,
                "Digital Only-Not In Ad": 32
            },
            "categories": {
                "15::Meat & Seafood": 15,
                "11::Frozen Foods": 7,
                "2::Beverages": 6,
                "1::Baby Care": 5,
                "12::Fruits & Vegetables": 2,
                "4::Breakfast & Cereal": 2,
                "9::Deli": 2,
                "14::International Cuisine": 1,
                "6::Condiments, Spices & Bake": 1,
                "7::Cookies, Snacks & Candy": 1
            },
            "eventids": {
                "1024": 1,
                "2327": 4,
                "11327": 1,
                "11435": 5
            },
            "offerStatus": {
                "PU": 23,
                "I": 5,
                "CN": 4,
                "A": 0,
                "D": 0,
                "E": 0,
                "L": 0,
                "O": 0,
                "DE": 0,
                "P": 0,
                "PG": 0,
                "FPU": 0,
                "FDE": 0,
                "RU": 0
            },
            "divisions": {},
            "events": {}
        };

      
        it('should handle empty facetFilters', () => {
            const localFacetFilters: any = {};
            const localFacetCounts = JSON.parse(JSON.stringify(facetCounts));

            service.getFacetCountsData(localFacetFilters, localFacetCounts);

            expect(localFacetFilters).toEqual({});
        });

        it('should handle empty facetCounts', () => {
            const localFacetFilters = JSON.parse(JSON.stringify(facetFilters));
            const localFacetCounts: any = {};

            service.getFacetCountsData(localFacetFilters, localFacetCounts);
        });

        it('should handle divisionId key correctly', () => {
            const localFacetFilters: any = {
                divisionId: { '1': { name: 'Division 1' }, '2': { name: 'Division 2' } }
            };
            const localFacetCounts = {
                divisionId: { '1': 5, '2': 10 }
            };

            service.getFacetCountsData(localFacetFilters, localFacetCounts);

            expect(localFacetFilters).toEqual({
                divisionId: { '1::Division 1': 0, '2::Division 2': 0 }
            });
        });
    })


    describe('searchAllPromotions', () => {
        it('should make expected calls with includeFacetCounts true', () => {
            const query = 'testQuery';
            const includeFacetCounts = true;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.searchAllPromotions(query, includeFacetCounts).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/iviePromotionSearchApi',
                {
                    query,
                    includeTotalCount: true,
                    includeFacetCounts,
                    reqObj: {
                        headers: {
                            'X-Albertsons-Client-ID': 'OMS',
                            'content-type': 'application/vnd.safeway.v1+json',
                            'X-Albertsons-userAttributes': 'mockToken'
                        }
                    }
                }
            );
        });

        it('should make expected calls with default includeFacetCounts', () => {
            const query = 'testQuery';
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.searchAllPromotions(query).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/iviePromotionSearchApi',
                {
                    query,
                    includeTotalCount: true,
                    includeFacetCounts: true,
                    reqObj: {
                        headers: {
                            'X-Albertsons-Client-ID': 'OMS',
                            'content-type': 'application/vnd.safeway.v1+json',
                            'X-Albertsons-userAttributes': 'mockToken'
                        }
                    }
                }
            );
        });

        it('should handle empty query', () => {
            const query = '';
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.searchAllPromotions(query).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/iviePromotionSearchApi',
                {
                    query,
                    includeTotalCount: true,
                    includeFacetCounts: true,
                    reqObj: {
                        headers: {
                            'X-Albertsons-Client-ID': 'OMS',
                            'content-type': 'application/vnd.safeway.v1+json',
                            'X-Albertsons-userAttributes': 'mockToken'
                        }
                    }
                }
            );
        });

        it('should handle includeFacetCounts as false', () => {
            const query = 'testQuery';
            const includeFacetCounts = false;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.searchAllPromotions(query, includeFacetCounts).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/iviePromotionSearchApi',
                {
                    query,
                    includeTotalCount: true,
                    includeFacetCounts,
                    reqObj: {
                        headers: {
                            'X-Albertsons-Client-ID': 'OMS',
                            'content-type': 'application/vnd.safeway.v1+json',
                            'X-Albertsons-userAttributes': 'mockToken'
                        }
                    }
                }
            );
        });

        it('should handle null query', () => {
            const query = null;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.searchAllPromotions(query).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/iviePromotionSearchApi',
                {
                    query,
                    includeTotalCount: true,
                    includeFacetCounts: true,
                    reqObj: {
                        headers: {
                            'X-Albertsons-Client-ID': 'OMS',
                            'content-type': 'application/vnd.safeway.v1+json',
                            'X-Albertsons-userAttributes': 'mockToken'
                        }
                    }
                }
            );
        });
    });

    describe('submitNewPromotion', () => {
        it('should make expected calls with valid offers', () => {
            const promotionArray = {
                eventTxtEdit: [{ id: 1 }, { id: 2 }],
                promotionEndDt: '2023-12-31',
                promotionStartDt: '2023-01-01'
            };
            const promotionEdit = {
                enteredOfferIds: [{ validOffer: true, offerId: 'offer1' }, { validOffer: false, offerId: 'offer2' }]
            };
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitNewPromotion(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/newPodPlaygroundApi',
                {
                    eventTxtEdit: [1, 2],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01',
                    offerIds: ['offer1'],
                    promotionEndDtEdit: '2023-12-31',
                    promotionStartDtEdit: '2023-01-01'
                }
            );
        });

        it('should make expected calls with no valid offers', () => {
            const promotionArray = {
                eventTxtEdit: [{ id: 1 }, { id: 2 }],
                promotionEndDt: '2023-12-31',
                promotionStartDt: '2023-01-01'
            };
            const promotionEdit = {
                enteredOfferIds: [{ validOffer: false, offerId: 'offer1' }]
            };
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitNewPromotion(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/newPodPlaygroundApi',
                {
                    eventTxtEdit: [1, 2],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01',
                    offerIds: [],
                    promotionEndDtEdit: '2023-12-31',
                    promotionStartDtEdit: '2023-01-01'
                }
            );
        });

        it('should handle empty eventTxtEdit', () => {
            const promotionArray = {
                eventTxtEdit: [],
                promotionEndDt: '2023-12-31',
                promotionStartDt: '2023-01-01'
            };
            const promotionEdit = {
                enteredOfferIds: [{ validOffer: true, offerId: 'offer1' }]
            };
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitNewPromotion(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/newPodPlaygroundApi',
                {
                    eventTxtEdit: [],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01',
                    offerIds: ['offer1'],
                    promotionEndDtEdit: '2023-12-31',
                    promotionStartDtEdit: '2023-01-01'
                }
            );
        });

        it('should handle empty enteredOfferIds', () => {
            const promotionArray = {
                eventTxtEdit: [{ id: 1 }, { id: 2 }],
                promotionEndDt: '2023-12-31',
                promotionStartDt: '2023-01-01'
            };
            const promotionEdit = {
                enteredOfferIds: []
            };
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitNewPromotion(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/newPodPlaygroundApi',
                {
                    eventTxtEdit: [1, 2],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01',
                    offerIds: [],
                    promotionEndDtEdit: '2023-12-31',
                    promotionStartDtEdit: '2023-01-01'
                }
            );
        });

        it('should handle null enteredOfferIds', () => {
            const promotionArray = {
                eventTxtEdit: [{ id: 1 }, { id: 2 }],
                promotionEndDt: '2023-12-31',
                promotionStartDt: '2023-01-01'
            };
            const promotionEdit = {
                enteredOfferIds: null
            };
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitNewPromotion(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/newPodPlaygroundApi',
                {
                    eventTxtEdit: [1, 2],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01',
                    offerIds: [],
                    promotionEndDtEdit: '2023-12-31',
                    promotionStartDtEdit: '2023-01-01'
                }
            );
        });
    });

    describe('submitPromotionArray', () => {
        it('should make expected calls with valid offers', () => {
            const promotionArray = [
                {
                    eventTxtEdit: [{ id: 1 }, { id: 2 }],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01'
                }
            ];
            const promotionEdit = [
                {
                    enteredOfferIds: [{ validOffer: true, offerId: 'offer1' }, { validOffer: false, offerId: 'offer2' }]
                }
            ];
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitPromotionArray(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/podPlaygroundApi',
                [
                    {
                        eventTxtEdit: [1, 2],
                        promotionEndDt: '2023-12-31',
                        promotionStartDt: '2023-01-01',
                        offerIds: ['offer1'],
                        promotionEndDtEdit: '2023-12-31',
                        promotionStartDtEdit: '2023-01-01'
                    }
                ]
            );
        });

        it('should make expected calls with no valid offers', () => {
            const promotionArray = [
                {
                    eventTxtEdit: [{ id: 1 }, { id: 2 }],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01'
                }
            ];
            const promotionEdit = [
                {
                    enteredOfferIds: [{ validOffer: false, offerId: 'offer1' }]
                }
            ];
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitPromotionArray(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/podPlaygroundApi',
                [
                    {
                        eventTxtEdit: [1, 2],
                        promotionEndDt: '2023-12-31',
                        promotionStartDt: '2023-01-01',
                        offerIds: [],
                        promotionEndDtEdit: '2023-12-31',
                        promotionStartDtEdit: '2023-01-01'
                    }
                ]
            );
        });

        it('should handle empty eventTxtEdit', () => {
            const promotionArray = [
                {
                    eventTxtEdit: [],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01'
                }
            ];
            const promotionEdit = [
                {
                    enteredOfferIds: [{ validOffer: true, offerId: 'offer1' }]
                }
            ];
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitPromotionArray(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/podPlaygroundApi',
                [
                    {
                        eventTxtEdit: [],
                        promotionEndDt: '2023-12-31',
                        promotionStartDt: '2023-01-01',
                        offerIds: ['offer1'],
                        promotionEndDtEdit: '2023-12-31',
                        promotionStartDtEdit: '2023-01-01'
                    }
                ]
            );
        });

        it('should handle empty enteredOfferIds', () => {
            const promotionArray = [
                {
                    eventTxtEdit: [{ id: 1 }, { id: 2 }],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01'
                }
            ];
            const promotionEdit = [
                {
                    enteredOfferIds: []
                }
            ];
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitPromotionArray(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/podPlaygroundApi',
                [
                    {
                        eventTxtEdit: [1, 2],
                        promotionEndDt: '2023-12-31',
                        promotionStartDt: '2023-01-01',
                        offerIds: [],
                        promotionEndDtEdit: '2023-12-31',
                        promotionStartDtEdit: '2023-01-01'
                    }
                ]
            );
        });

        it('should handle null enteredOfferIds', () => {
            const promotionArray = [
                {
                    eventTxtEdit: [{ id: 1 }, { id: 2 }],
                    promotionEndDt: '2023-12-31',
                    promotionStartDt: '2023-01-01'
                }
            ];
            const promotionEdit = [
                {
                    enteredOfferIds: null
                }
            ];
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.submitPromotionArray(promotionArray, promotionEdit).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/podPlaygroundApi',
                [
                    {
                        eventTxtEdit: [1, 2],
                        promotionEndDt: '2023-12-31',
                        promotionStartDt: '2023-01-01',
                        offerIds: [],
                        promotionEndDtEdit: '2023-12-31',
                        promotionStartDtEdit: '2023-01-01'
                    }
                ]
            );
        });
    });

    describe('getPaginationSearch', () => {
        it('should call next on iviePromotionSearch with the provided data', () => {
            const data = true;
            const spy = spyOn(service.iviePromotionSearch, 'next');

            service.getPaginationSearch(data);

            expect(spy).toHaveBeenCalledWith(data);
        });

        it('should handle empty data', () => {
            const data = false;
            const spy = spyOn(service.iviePromotionSearch, 'next');

            service.getPaginationSearch(data);

            expect(spy).toHaveBeenCalledWith(data);
        });

    });

    describe('importToOffer', () => {
        it('should make expected calls with multiple promotions', () => {
            const promotionObj = [
                {
                    uniqueId: 'promo1',
                    enteredOfferIds: [{ validOffer: true, offerId: 'offer1' }, { validOffer: false, offerId: 'offer2' }]
                },
                {
                    uniqueId: 'promo2',
                    enteredOfferIds: [{ validOffer: true, offerId: 'offer3' }]
                }
            ];
            const singleRow = false;
            const stub = spyOn(httpClient, 'put').and.returnValue(of({}));

            service.importToOffer(promotionObj, singleRow).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/importPromotionToOffer',
                [
                    { uniqueId: 'promo1', offerIds: ['offer1'] },
                    { uniqueId: 'promo2', offerIds: ['offer3'] }
                ]
            );
        });

        it('should make expected calls with a single promotion', () => {
            const promotionObj = {
                uniqueId: 'promo1',
                enteredOfferIds: [{ validOffer: true, offerId: 'offer1' }, { validOffer: false, offerId: 'offer2' }]
            };
            const singleRow = true;
            const stub = spyOn(httpClient, 'put').and.returnValue(of({}));

            service.importToOffer(promotionObj, singleRow).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/importPromotionToOffer',
                [
                    { uniqueId: 'promo1', offerIds: ['offer1'] }
                ]
            );
        });

        it('should handle empty enteredOfferIds for multiple promotions', () => {
            const promotionObj = [
                {
                    uniqueId: 'promo1',
                    enteredOfferIds: []
                }
            ];
            const singleRow = false;
            const stub = spyOn(httpClient, 'put').and.returnValue(of({}));

            service.importToOffer(promotionObj, singleRow).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/importPromotionToOffer',
                []
            );
        });

        it('should handle empty enteredOfferIds for a single promotion', () => {
            const promotionObj = {
                uniqueId: 'promo1',
                enteredOfferIds: []
            };
            const singleRow = true;
            const stub = spyOn(httpClient, 'put').and.returnValue(of({}));

            service.importToOffer(promotionObj, singleRow).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/importPromotionToOffer',
                []
            );
        });

        it('should handle null enteredOfferIds for multiple promotions', () => {
            const promotionObj = [
                {
                    uniqueId: 'promo1',
                    enteredOfferIds: null
                }
            ];
            const singleRow = false;
            const stub = spyOn(httpClient, 'put').and.returnValue(of({}));

            service.importToOffer(promotionObj, singleRow).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/importPromotionToOffer',
                []
            );
        });

        it('should handle null enteredOfferIds for a single promotion', () => {
            const promotionObj = {
                uniqueId: 'promo1',
                enteredOfferIds: null
            };
            const singleRow = true;
            const stub = spyOn(httpClient, 'put').and.returnValue(of({}));

            service.importToOffer(promotionObj, singleRow).subscribe();

            expect(stub).toHaveBeenCalledWith(
                'mockUrl/importPromotionToOffer',
                []
            );
        });
    });

    describe('getAdBugs', () => {
        it('should make expected calls with includeFacetCounts true', () => {
            const query = 'testQuery';
            const includeFacetCounts = true;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query, includeFacetCounts).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should make expected calls with includeFacetCounts false', () => {
            const query = 'testQuery';
            const includeFacetCounts = false;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query, includeFacetCounts).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should make expected calls with default includeFacetCounts', () => {
            const query = 'testQuery';
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should handle empty query', () => {
            const query = '';
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query).subscribe();

            expect(stub).toHaveBeenCalled();
        });

    })

    describe('getAdBugs', () => {
        it('should make expected calls with includeFacetCounts true', () => {
            const query = 'testQuery';
            const includeFacetCounts = true;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query, includeFacetCounts).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should make expected calls with includeFacetCounts false', () => {
            const query = 'testQuery';
            const includeFacetCounts = false;
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query, includeFacetCounts).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should make expected calls with default includeFacetCounts', () => {
            const query = 'testQuery';
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query).subscribe();

            expect(stub).toHaveBeenCalled();
        });

        it('should handle empty query', () => {
            const query = '';
            const stub = spyOn(httpClient, 'post').and.returnValue(of({}));

            service.getAdBugs(query).subscribe();

            expect(stub).toHaveBeenCalled();
        });
    });


    describe('getDivisionsForIviePromotions', () => {
        it('should make expected calls and return data on success', () => {
            const response = { data: 'testData' };
            const stub = spyOn(httpClient, 'get').and.returnValue(of(response));

            service.getDivisionsForIviePromotions().subscribe((res) => {
                expect(res).toEqual(response);
            });

            expect(stub).toHaveBeenCalled();
        });

        it('should handle error correctly', () => {
            const errorResponse = new Error('test error');
            const stub = spyOn(httpClient, 'get').and.returnValue(throwError(errorResponse));

            service.getDivisionsForIviePromotions().subscribe(
                () => {},
                (error) => {
                    expect(error).toEqual(errorResponse);
                }
            );

            expect(stub).toHaveBeenCalled();
        });
    });

    describe('getLastImportData', () => {
        it('should make expected calls and return data on success', () => {
            const response = { data: 'testData' };
            const reqBody = '?jobNames=load_ivie_data';
            const stub = spyOn(httpClient, 'get').and.returnValue(of(response));

            service.getLastImportData().subscribe((res) => {
                expect(res).toEqual(response);
            });

            expect(stub).toHaveBeenCalledWith(`${service.iviePromotionlastImportDataAPI}${reqBody}`);
        });

        it('should handle error correctly', () => {
            const errorResponse = new Error('test error');
            const reqBody = '?jobNames=load_ivie_data';
            const stub = spyOn(httpClient, 'get').and.returnValue(throwError(errorResponse));

            service.getLastImportData().subscribe(
                () => {},
                (error) => {
                    expect(error).toEqual(errorResponse);
                }
            );

            expect(stub).toHaveBeenCalledWith(`${service.iviePromotionlastImportDataAPI}${reqBody}`);
        });
    });
}) 
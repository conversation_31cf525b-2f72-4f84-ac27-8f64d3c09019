import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { BehaviorSubject, Subject } from 'rxjs';
import { AuthService } from './auth.service';
import { InitialDataService } from './initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class IviePromotionService {
  iviePromotionSearch = new BehaviorSubject(false);
  iviePromotionSearchObvl = this.iviePromotionSearch.asObservable();
  iviePromotionOfferValidity = new Subject();
  isRedirectRoute$ = new Subject();
  onRouteChange$ = new Subject();
  onSearchFilterChange$ = new Subject();
  onFacetChipCloseChange$ = new Subject();
  onInputSearchChange$ = new Subject();
  globalSearchChange$ = new Subject();
  searchPromotionPage = new Subject();

  constructor(
    private _http: HttpClient,
    private authService: AuthService,
    private initialDataService: InitialDataService
  ) { 
    // intentionally left empty
  }

  iviePromotionSearchAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.IVIE_PROMOTION_SEARCH_API);
  podPlaygroundAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.POD_PLAYGROUND_API);
  newPodPlaygroundAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.NEW_POD_PLAYGROUND_API);
  exportPodPlaygroundAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.EXPORT_POD_PLAYGROUND_API);
  importToOfferAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.IMPORT_TO_OFFER);
  iviePromotionAdBugSearchAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.POD_PLAYGROUND_AD_BUG_API);
  iviePromotionlastImportDataAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.POD_PLAYGROUND_LAST_IMPORT_API);
  divisionsForIviePromotionsAPI: string = this.initialDataService.getConfigUrls(CONSTANTS.DIVISIONS_FOR_IVIE_PROMOTIONS);

  public searchAllPromotions(query: string, includeFacetCounts: boolean = true) {
    const headers = {
      ...CONSTANTS.HTTP_HEADERS,
      'X-Albertsons-userAttributes': this.authService.getTokenString()
    };
    const searchInput = { query, includeTotalCount: true, includeFacetCounts, reqObj: { headers } };
    return this._http.post(this.iviePromotionSearchAPI, searchInput);
  }
  public exportPodPlayground(query: string) {
    const headers = {

      ...CONSTANTS.HTTP_HEADERS,
      'X-Albertsons-userAttributes': this.authService.getTokenString()
    };

    const searchInput = { query, reqObj: { headers } };
    return this._http.post(this.exportPodPlaygroundAPI, searchInput, { responseType: "blob" as "json", observe: 'response' as 'body' });
  }
  public submitNewPromotion(promotionArray, promotionEdit) {
    let finalEventArray = [];
    let validOfferIds = [];
    const eventArr = promotionArray.eventTxtEdit;
    eventArr.forEach((item: any) => {
      finalEventArray.push(item.id);
    });
    promotionArray.eventTxtEdit = finalEventArray;
    const offerIdArr = promotionEdit.enteredOfferIds;
    if(offerIdArr && offerIdArr.length > 0) {
      offerIdArr.forEach((item: any) => {
        if (item.validOffer) {
          validOfferIds.push(item.offerId);
        }
      });
    }

    promotionArray.offerIds = validOfferIds;
    promotionArray.promotionEndDtEdit = promotionArray.promotionEndDt;
    promotionArray.promotionStartDtEdit = promotionArray.promotionStartDt;
    return this._http.post(this.newPodPlaygroundAPI, promotionArray);

  }
  public submitPromotionArray(promotionArray, promotionEdit) {


    for (let i = 0; i < promotionArray.length; i++) {
      let finalEventArray = [];
      let validOfferIds = [];
      let eventArr = promotionArray[i].eventTxtEdit;
      eventArr.forEach((item: any) => {
        finalEventArray.push(item.id);
      });
      promotionArray[i].eventTxtEdit = finalEventArray;

      let offerIdArr = promotionEdit[i].enteredOfferIds;
      if (offerIdArr && offerIdArr.length > 0) {
        offerIdArr.forEach((item: any) => {
          if (item.validOffer) {
            validOfferIds.push(item.offerId);
          }
        });
      }
      promotionArray[i].offerIds = validOfferIds;
      promotionArray[i].promotionEndDtEdit = promotionArray[i].promotionEndDt;
      promotionArray[i].promotionStartDtEdit = promotionArray[i].promotionStartDt;
    }
    return this._http.post(this.podPlaygroundAPI, promotionArray);
  }

  getPaginationSearch(data) {
    this.iviePromotionSearch.next(data);
  }
  getFacetCountsData(facetFilters, facetCounts) {
    for (let [key, value] of Object.entries(facetFilters)) {
      let facetParam = facetCounts[key];
      if (key === 'divisions' || key === 'divisionId') {
        const divObj = {};
        let keys = Object.keys(value);
        keys.forEach(ele => {
          if(ele !== '0') {
            const dObj = value[ele] || ele.trim();
            divObj[`${ele}::${dObj['name']}`] = 0;
          }
        });
        value = divObj;
      }

      facetFilters[key] = value;
    }
  }
  public importToOffer(promotionObj, singleRow) {
    const finalOfferArray = [];
    if (!singleRow) {
      promotionObj.forEach(promotion => {
        if (promotion.uniqueId && promotion.enteredOfferIds && promotion.enteredOfferIds.length > 0) {
          const offerObj = {
            uniqueId: promotion.uniqueId
          };
          const offerIdsArray = [];
          for (let i = 0; i < promotion.enteredOfferIds.length; i++) {
            if (promotion.enteredOfferIds[i].validOffer) {
              offerIdsArray.push(promotion.enteredOfferIds[i].offerId);
            }
          }
          offerObj['offerIds'] = offerIdsArray;
          finalOfferArray.push(offerObj);
        }
      });
    } else if (promotionObj.uniqueId && promotionObj.enteredOfferIds && promotionObj.enteredOfferIds.length > 0) {
      const offerObj = {
        'uniqueId': promotionObj.uniqueId
      };
      const offerIdsArray = [];
      for (let i = 0; i < promotionObj.enteredOfferIds.length; i++) {
        if (promotionObj.enteredOfferIds[i].validOffer) {
          offerIdsArray.push(promotionObj.enteredOfferIds[i].offerId);
        }
      }
      offerObj['offerIds'] = offerIdsArray;
      finalOfferArray.push(offerObj);
    }

    return this._http.put(this.importToOfferAPI, finalOfferArray);
  }
  getAdBugs(query: string, includeFacetCounts: boolean = true) {
    const headers = {
      ...CONSTANTS.HTTP_HEADERS,
      'X-Albertsons-userAttributes': this.authService.getTokenString()
    };
    const searchInput = { query, includeTotalCount: true, includeFacetCounts, reqObj: { headers } };
    return this._http.post(this.iviePromotionAdBugSearchAPI, searchInput);
  }
  getLastImportData() {
    let reqBody: string;
    reqBody = '?jobNames=load_ivie_data';
    return this._http.get(`${this.iviePromotionlastImportDataAPI}${reqBody}`);
  }

  getDivisionsForIviePromotions() {
    return this._http.get(this.divisionsForIviePromotionsAPI);
  }
}

import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root'
})

export class LoaderService {

   loading : boolean = true;
   public loader = new BehaviorSubject(this.loading);
   loaderInstance = this.loader.asObservable();

   podListLoader: boolean = false;
   podListloaderSource = new BehaviorSubject(false);
   podListloader = this.podListloaderSource.asObservable();


   public isDisplayLoader(loading) {
     this.loader.next(loading);
   }

   public isDisplaypodListLoader(loading){
     this.podListloaderSource.next(loading);
   }

}

import { TestBed } from '@angular/core/testing';
import { LoggerService } from './logger.service';
import { isDevMode } from '@angular/core';

describe('LoggerService', () => {
  let service: LoggerService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(LoggerService);
  });
  function isRunningInTest(): boolean {
    return navigator.userAgent.includes('HeadlessChrome');
  }
  
  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize isDebug correctly', () => {
    const expectedIsDebug = !isRunningInTest() && (isDevMode() || ['localhost', 'dev', 'qa1', 'qa2'].includes(window.location.hostname));
    expect(service['isDebug']).toBe(expectedIsDebug);
  });

  it('should log messages when isDebug is true', () => {
    spyOn(console, 'log');
    service['isDebug'] = true;
    service.log('Test log message');
    expect(console.log).toHaveBeenCalledWith('%c[LOG]: Test log message', 'color: green; font-weight: bold;', '');
  });

  it('should log messages with data when isDebug is true', () => {
    spyOn(console, 'log');
    service['isDebug'] = true;
    const data = { key: 'value' };
    service.log('Test log message', data);
    expect(console.log).toHaveBeenCalledWith('%c[LOG]: Test log message', 'color: green; font-weight: bold;', data);
  });
  
  it('should not log messages when isDebug is false', () => {
    spyOn(console, 'log');
    service['isDebug'] = false;
    service.log('Test log message');
    expect(console.log).not.toHaveBeenCalled();
  });

  it('should handle empty message', () => {
    spyOn(console, 'log');
    service['isDebug'] = true;
    service.log('');
    expect(console.log).toHaveBeenCalledWith('%c[LOG]: ', 'color: green; font-weight: bold;', '');
  });

  it('should handle null data', () => {
    spyOn(console, 'log');
    service['isDebug'] = true;
    service.log('Test log message', null);
    expect(console.log).toHaveBeenCalledWith('%c[LOG]: Test log message', 'color: green; font-weight: bold;', "");
  });

  it('should handle undefined data', () => {
    spyOn(console, 'log');
    service['isDebug'] = true;
    service.log('Test log message', undefined);
    expect(console.log).toHaveBeenCalledWith('%c[LOG]: Test log message', 'color: green; font-weight: bold;', "");
  });

  it('should warn messages when isDebug is true', () => {
    spyOn(console, 'warn');
    service['isDebug'] = true;
    service.warn('Test warn message');
    expect(console.warn).toHaveBeenCalledWith('%c[WARN]: Test warn message', 'color: orange; font-weight: bold;', '');
  });

  it('should not warn messages when isDebug is false', () => {
    spyOn(console, 'warn');
    service['isDebug'] = false;
    service.warn('Test warn message');
    expect(console.warn).not.toHaveBeenCalled();
  });

  it('should warn messages with data when isDebug is true', () => {
    spyOn(console, 'warn');
    service['isDebug'] = true;
    const data = { key: 'value' };
    service.warn('Test warn message', data);
    expect(console.warn).toHaveBeenCalledWith('%c[WARN]: Test warn message', 'color: orange; font-weight: bold;', data);
  });

  it('should error messages regardless of isDebug', () => {
    spyOn(console, 'error');
    service.error('Test error message');
    expect(console.error).toHaveBeenCalledWith('%c[ERROR]: Test error message', 'color: red; font-weight: bold; background: yellow;', '');
  });

  it('should error messages with data regardless of isDebug', () => {
    spyOn(console, 'error');
    const data = { key: 'value' };
    service.error('Test error message', data);
    expect(console.error).toHaveBeenCalledWith('%c[ERROR]: Test error message', 'color: red; font-weight: bold; background: yellow;', data);
  });

  it('should debug messages when isDebug is true', () => {
    spyOn(console, 'debug');
    service['isDebug'] = true;
    service.debug('Test debug message');
    expect(console.debug).toHaveBeenCalledWith('%c[DEBUG]: Test debug message', 'color: blue; font-weight: bold;', '');
  });

  it('should debug messages with data when isDebug is true', () => {
    spyOn(console, 'debug');
    service['isDebug'] = true;
    const data = { key: 'value' };
    service.debug('Test debug message', data);
    expect(console.debug).toHaveBeenCalledWith('%c[DEBUG]: Test debug message', 'color: blue; font-weight: bold;', data);
  });

  it('should not debug messages when isDebug is false', () => {
    spyOn(console, 'debug');
    service['isDebug'] = false;
    service.debug('Test debug message');
    expect(console.debug).not.toHaveBeenCalled();
  });
});
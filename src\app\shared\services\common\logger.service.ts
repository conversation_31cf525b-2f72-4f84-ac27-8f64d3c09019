import { Injectable, isDevMode } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LoggerService {
  private isDebug = !this.isRunningInTest() && (isDevMode() || ['localhost', 'dev', 'qa1', 'qa2'].includes(window.location.hostname));

  private isRunningInTest(): boolean {
    return navigator.userAgent.includes('HeadlessChrome');
  }
  
  log(message: string, data?: any) {
    if (this.isDebug) {
      console.log(`%c[LOG]: ${message}`, 'color: green; font-weight: bold;', data || '');
    }
  }

  warn(message: string, data?: any) {
    if (this.isDebug) {
      console.warn(`%c[WARN]: ${message}`, 'color: orange; font-weight: bold;', data || '');
    }
  }

  error(message: string, data?: any) {
    console.error(`%c[ERROR]: ${message}`, 'color: red; font-weight: bold; background: yellow;', data || '');
  }

  debug(message: string, data?: any) {
    if (this.isDebug) {
      console.debug(`%c[DEBUG]: ${message}`, 'color: blue; font-weight: bold;', data || '');
    }
  }
}

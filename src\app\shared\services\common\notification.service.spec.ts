import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { NotificationService } from './notification.service';

describe('NotificationService', () => {
  let service: NotificationService;
  let toastrService: jasmine.SpyObj<ToastrService>;

  beforeEach(() => {
    const toastrSpy = jasmine.createSpyObj('ToastrService', ['success', 'warning', 'info', 'error']);

    TestBed.configureTestingModule({
      providers: [
        NotificationService,
        { provide: ToastrService, useValue: toastrSpy }
      ]
    });

    service = TestBed.inject(NotificationService);
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should show success notification', () => {
    const message = 'Success message';
    service.showNotification(message, 'success');
    expect(toastrService.success).toHaveBeenCalledWith(message, '', { timeOut: 3000, closeButton: true });
  });
  
  it('should show warning notification', () => {
    const message = 'Warning message';
    service.showNotification(message, 'warning');
    expect(toastrService.warning).toHaveBeenCalledWith(message, '', { timeOut: 3000, closeButton: true });
  });
  

  it('should show info notification', () => {
    const message = 'Info message';
    service.showNotification(message, 'info');
    expect(toastrService.info).toHaveBeenCalledWith(message, '', { timeOut: 3000, closeButton: true });
  });

  it('should show error notification', () => {
    const message = 'Error message';
    service.showNotification(message, 'error');
    expect(toastrService.error).toHaveBeenCalledWith(message, '', { timeOut: 3000, closeButton: true });
  });

  it('should show duplicated notification as warning', () => {
    const message = 'Duplicated message';
    service.showNotification(message, 'duplicated');
    expect(toastrService.warning).toHaveBeenCalledWith(message, '', { timeOut: 3000, closeButton: true });
  });

  it('should show remove notification as info', () => {
    const message = 'Remove message';
    service.showNotification(message, 'remove');
    expect(toastrService.info).toHaveBeenCalledWith(message, '', { timeOut: 3000, closeButton: true });
  });

  it('should not show notification for unknown status', () => {
    const message = 'Unknown status message';
    service.showNotification(message, 'unknown');
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should not show notification for null status', () => {
    const message = 'Message with null status';
    service.showNotification(message, null);
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should not show notification for undefined status', () => {
    const message = 'Message with undefined status';
    service.showNotification(message, undefined);
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should not show notification for empty status', () => {
    const message = 'Message with empty status';
    service.showNotification(message, '');
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should handle case-insensitive status values', () => {
    const message = 'Case insensitive test';
    service.showNotification(message, 'SUCCESS');
    expect(toastrService.success).not.toHaveBeenCalled(); 
  });

  it('should not show notification for whitespace status', () => {
    service.showNotification('Valid message', '   ');
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should not show notification for numeric status', () => {
    service.showNotification('Numeric status', 123 as any);
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should not show notification for object status', () => {
    service.showNotification('Object status', { key: 'value' } as any);
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

  it('should not show notification for array status', () => {
    service.showNotification('Array status', ['success'] as any);
    expect(toastrService.success).not.toHaveBeenCalled();
    expect(toastrService.warning).not.toHaveBeenCalled();
    expect(toastrService.info).not.toHaveBeenCalled();
    expect(toastrService.error).not.toHaveBeenCalled();
  });

})
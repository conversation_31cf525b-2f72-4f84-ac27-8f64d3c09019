import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(private toastr: ToastrService) { 
    // intentionally left empty
  }

  showNotification(message, status) {
    switch (status) {
      case 'success':
        this.toastr.success(message, '', {
          timeOut: 3000,
          closeButton: true
        });
        break;
      case 'duplicated':
      case 'warning':
        this.toastr.warning(message, '', {
          timeOut: 3000,
          closeButton: true
        });
        break;
      case 'info':
      case 'remove':
        this.toastr.info(message, '', {
          timeOut: 3000,
          closeButton: true
        });
        break;
      case 'error':
        this.toastr.error(message, '', {
          timeOut: 3000,
          closeButton: true
        });
        break;
      default:
        break;
    }
  }
}

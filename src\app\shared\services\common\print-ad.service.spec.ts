import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { of } from 'rxjs';
import { PrintAdService } from './print-ad.service';
import { AuthService } from './auth.service';
import { InitialDataService } from './initial.data.service';
import { CONSTANTS } from '@appConstants/constants';

describe('PrintAdService', () => {
  let service: PrintAdService;
  let httpMock: HttpTestingController;
  let authService: jasmine.SpyObj<AuthService>;
  let httpClient: HttpClient;
  let initialDataService: jasmine.SpyObj<InitialDataService>;

  beforeEach(() => {
    const authSpy = jasmine.createSpyObj('AuthService', ['getTokenString']);
    const initialDataSpy = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        PrintAdService,
        { provide: AuthService, useValue: authSpy },
        { provide: InitialDataService, useValue: initialDataSpy }
      ]
    });

    service = TestBed.inject(PrintAdService);
    httpMock = TestBed.inject(HttpTestingController); // Initialize httpMock
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    httpClient = TestBed.inject(HttpClient);
    initialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
    
    service.importCSVAPI = 'mock-url';
    service.importLogApi = 'mock-log-url';
    service.importErrorApi = 'mock-error-url';

    spyOn(service as any, 'getUserInfo').and.callFake(() => {
      service['userInfo'] = 'userId=123;firstName=John;lastName=Doe;email=<EMAIL>;';
      service['userInfoMap'] = {
        userId: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      };
    });

    spyOn(service as any, 'getHeaders').and.callFake(() => {
      return {
        Accept: 'application/vnd.safeway.v1+json',
        'X-Albertsons-Client-ID': 'OMS',
        'X-Albertsons-userAttributes': 'userId=123;firstName=John;lastName=Doe;email=<EMAIL>;'
      };
    });

    spyOn(service as any, 'getImportLogHeaders').and.callFake(() => {
      return {
        ...CONSTANTS.HTTP_HEADERS,
        Accept: 'application/vnd.safeway.v1+json',
        'X-Albertsons-userAttributes': 'userId=123;firstName=John;lastName=Doe;email=<EMAIL>;'
      };
    });
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('importCSV', () => {
    it('should make POST call with form data', () => {
      const spy = spyOn(httpClient, 'post').and.returnValue(of({}));
      const file = new File(['content'], 'test.csv');

      service.importCSV(file).subscribe();
      
      expect(spy).toHaveBeenCalled();
      const [url, body, options] = spy.calls.first().args;
      
      expect(url).toBe('mock-url');
      expect(body.get('file')).toEqual(file);
      expect((options?.headers as HttpHeaders)?.get('X-Albertsons-Client-ID')).toBe('OMS');
    });
  });

  describe('getUserInfo', () => {
    it('should extract user information from token string', () => {
      (service as any).getUserInfo();

      expect(service['userInfoMap']).toEqual({
        userId: '123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      });
    });
  })

  describe('getHeaders', () => {
    it('should return correct headers', () => {
      const headers = (service as any).getHeaders();

      expect(headers).toEqual({
        Accept: 'application/vnd.safeway.v1+json',
        'X-Albertsons-Client-ID': 'OMS',
        'X-Albertsons-userAttributes': 'userId=123;firstName=John;lastName=Doe;email=<EMAIL>;'
      });
    });
  });

  describe('getImportLogHeaders', () => {
    it('should return correct import log headers', () => {
      const headers = (service as any).getImportLogHeaders();

      expect(headers).toEqual({
        ...CONSTANTS.HTTP_HEADERS,
        Accept: 'application/vnd.safeway.v1+json',
        'X-Albertsons-userAttributes': 'userId=123;firstName=John;lastName=Doe;email=<EMAIL>;'
      });
    });
  });

  describe('importLog', () => {
    it('should make POST call with correct parameters', () => {
      const spy = spyOn(httpClient, 'post').and.returnValue(of({}));
      const query = { key: 'value' };

      service.importLog(query).subscribe();

      expect(spy).toHaveBeenCalled();
      const [url, params] = spy.calls.first().args;

      expect(url).toBe('mock-log-url');
      expect(params).toEqual({
        query,
        includeTotalCount: true,
        reqObj: { headers: {
          ...CONSTANTS.HTTP_HEADERS,
          Accept: 'application/vnd.safeway.v1+json',
          'X-Albertsons-userAttributes': 'userId=123;firstName=John;lastName=Doe;email=<EMAIL>;'
        }}
      });
    });
  });

  
  describe('importLogErrors', () => {
    it('should make GET call with correct URL and headers', () => {
      const spy = spyOn(httpClient, 'get').and.returnValue(of({}));
      const id = '123';

      service.importLogErrors(id).subscribe();

      expect(spy).toHaveBeenCalled();
      const [url, options] = spy.calls.first().args;

      expect(url).toBe('mock-error-url/123/errors');
      expect((options?.headers as HttpHeaders)?.get('Accept')).toBe('application/vnd.safeway.v1+json');
      expect((options?.headers as HttpHeaders)?.get('X-Albertsons-Client-ID')).toBe('OMS');
      expect((options?.headers as HttpHeaders)?.get('X-Albertsons-userAttributes')).toBe('userId=123;firstName=John;lastName=Doe;email=<EMAIL>;');
    });
  });
}); 
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from './auth.service';
import { InitialDataService } from './initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class PrintAdService {
  userInfo: string;
  userInfoMap: { userId: any; firstName: any; lastName: any; email: any; };
  importCSVAPI: string;
  importLogApi: string;
  importErrorApi: string;
  importLogResponse = new BehaviorSubject(false);


  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private initialDataService: InitialDataService
  ) {
    this.importCSVAPI = this.initialDataService.getConfigUrls(CONSTANTS.PRINT_AD_IMPORT_CSV_API);
    this.importLogApi = this.initialDataService.getConfigUrls(CONSTANTS.PRINT_AD_IMPORT_LOG_API);
    this.importErrorApi = this.initialDataService.getConfigUrls(CONSTANTS.PRINT_AD_IMPORT_ERROR_API);
  }

  private getUserInfo() {
    this.userInfo = this.authService.getTokenString();
    this.userInfoMap = {
      userId: this.userInfo.split('userId=', 2)[1].split(';', 1)[0],
      firstName: this.userInfo.split('firstName=', 2)[1].split(';', 1)[0],
      lastName: this.userInfo.split('lastName=', 2)[1].split(';', 1)[0],
      email: this.userInfo.split('email=', 2)[1].split(';', 1)[0],
    };
  }

  private getHeaders() {

    if (!this.userInfo) {
      this.getUserInfo();
    }

    return {
      Accept: 'application/vnd.safeway.v1+json',
      'X-Albertsons-Client-ID': 'OMS',
      'X-Albertsons-userAttributes': this.userInfo
    };

  }

  private getImportLogHeaders() {
    if (!this.userInfo) {
      this.getUserInfo();
    }

    return {
      ...CONSTANTS.HTTP_HEADERS,
      Accept: 'application/vnd.safeway.v1+json',
      'X-Albertsons-userAttributes': this.userInfo
    };
  }

  public importCSV(file: File) {
    const formData: FormData = new FormData();
    formData.append('file', file, file.name);
    return this.http.post(this.importCSVAPI, formData, { headers: new HttpHeaders(this.getHeaders()) });
  }

  public importLog(query:any) {
    const headerCopy = this.getImportLogHeaders();
    let reqBody = {};
    reqBody = { query, includeTotalCount: true };
    const params = { ...reqBody, reqObj: { headers: headerCopy } };
    return this.http.post(this.importLogApi, params);
  }

  public importLogErrors(id:string) {
    let apiComposure = this.importErrorApi + '/' + id + '/errors'
    return this.http.get(apiComposure, { headers: new HttpHeaders(this.getHeaders()) });
  }

}

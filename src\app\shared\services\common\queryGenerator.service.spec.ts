import { TestBed } from "@angular/core/testing";
import { QueryGenerator } from "./queryGenerator.service";

describe('QueryGenerator', () => {
    let service: QueryGenerator;
    
    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [QueryGenerator]
        });

        service = TestBed.inject(QueryGenerator)
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should remove the specified chip from queryWithFilter', () => {
        service.setQueryWithFilter(['User: <PERSON>', 'Status: Active', 'deliveryChannel: Email']);
        
        service.removeQueryWithFilter('assignedTo');
        expect(service.getQueryWithFilter()).toEqual(['Status: Active', 'deliveryChannel: Email']);
        
        service.removeQueryWithFilter('status');
        expect(service.getQueryWithFilter()).toEqual(['deliveryChannel: Email']);
    });

    it('should not modify queryWithFilter if chip is not found', () => {
        service.setQueryWithFilter(['adType: Banner', 'deliveryChannel: SMS']);
        
        service.removeQueryWithFilter('assignedTo');
        expect(service.getQueryWithFilter()).toEqual(['adType: Banner', 'deliveryChannel: SMS']);
    });

    it('should return the value inside parentheses for a given inputId', () => {
        service.setQuery('param1=(value1); param2=(value2);');
        const result = service.getInputValue('param1');
        expect(result).toBe('value1');
    });

    it('should return an empty string if inputId is not present in the query', () => {
        service.setQuery('param1=(value1); param2=(value2);');
        const result = service.getInputValue('param3');
        expect(result).toBe('');
    });

    it('should return an empty string if inputId is present but has no value', () => {
        service.setQuery('param1=(); param2=(value2);');
        const result = service.getInputValue('param1');
        expect(result).toBe('');
    });

    it('should handle cases where there are multiple parameters with the same inputId', () => {
        service.setQuery('param1=(value1); param1=(value2);');
        const result = service.getInputValue('param1');
        expect(result).toBe('value1');
    });

    it('should handle cases where the query string is empty', () => {
        service.setQuery('');
        const result = service.getInputValue('param1');
        expect(result).toBe('');
    });

    it('should handle cases where the query string does not follow the expected format', () => {
        service.setQuery('param1=value1; param2=(value2);');
        const result = service.getInputValue('param1');
        expect(result).toBe('value2');
    });
    
    it('should return the value inside squar braket for a given inputId', () => {
        service.setQuery('param1=[value1]; param2=[value2];');
        const result = service.getInputRangeValue('param1');
        expect(result).toBe('value1');
    });

    it('should return an empty string if inputId is not present in the getInputRangeValue query', () => {
        service.setQuery('param1=[value1]; param2=[value2];');
        const result = service.getInputRangeValue('param3');
        expect(result).toBe('');
    });

    it('should return an empty string if inputId is present in the getInputRangeValue but has no value', () => {
        service.setQuery('param1=[]; param2=[value2];');
        const result = service.getInputRangeValue('param1');
        expect(result).toBe('');
    });

    it('should handle cases where there are multiple parameters with the same getInputRangeValue inputId', () => {
        service.setQuery('param1=[value1]; param1=[value2];');
        const result = service.getInputRangeValue('param1');
        expect(result).toBe('value1');
    });

    it('should handle cases where the getInputRangeValue query string is empty', () => {
        service.setQuery('');
        const result = service.getInputRangeValue('param1');
        expect(result).toBe('');
    });

    it('should handle cases where the getInputRangeValue query string does not follow the expected format', () => {
        service.setQuery('param1=value1; param2=[value2];');
        const result = service.getInputRangeValue('param1');
        expect(result).toBe('value2');
    });

    describe('setDefaultQuery and getDefaultQuery', () => {
        it('should set and get the default query correctly', () => {
            const expectedQuery = true;
            service.setDefaultQuery(expectedQuery);
            const result = service.getDefaultQuery();
            expect(result).toBe(expectedQuery);
        });

        it('should return the default value (false) when not set', () => {
            const result = service.getDefaultQuery();
            expect(result).toBeFalse();
        });

        it('should update default query correctly when set multiple times', () => {
            const query1 = true;
            const query2 = false;
            service.setDefaultQuery(query1);
            let result1 = service.getDefaultQuery();

            service.setDefaultQuery(query2);
            let result2 = service.getDefaultQuery();
            expect(result1).toBe(query1);
            expect(result2).toBe(query2);
        });
    });

    describe('removeParam', () => {
        it('should remove the specified param from the query string', () => {
            const initialQuery = 'param1=value1;param2=value2;';
            const paramToRemove = 'param1';
            service.setQuery(initialQuery);

            service.removeParam(paramToRemove);

            const result = service.getQuery();
            expect(result).toBe('param2=value2;');
        });

        it('should not modify the query string if the param does not exist', () => {
            const initialQuery = 'param1=value1;param2=value2;';
            const paramToRemove = 'param3';
            service.setQuery(initialQuery);

            service.removeParam(paramToRemove);
            const result = service.getQuery();

            expect(result).toBe(initialQuery);
        });

        it('should remove only the first occurrence of the param in the query string', () => {
            const initialQuery = 'param1=value1;param2=value2;param1=value3;';
            const paramToRemove = 'param1';
            service.setQuery(initialQuery);

            service.removeParam(paramToRemove);
            const result = service.getQuery();

            expect(result).toBe('param2=value2;param1=value3;');
        });
    });

    describe('pushParam', () => {
        it('should add a new parameter to the query string', () => {
            const initialQuery = 'param1=value1;';
            const newParam = { parameter: 'param2', value: 'value2' };
            service.setQuery(initialQuery);

            service.pushParam(newParam);
            const result = service.getQuery();

            expect(result).toBe('param1=value1;param2=value2;');
        });

        it('should remove the existing parameter if the remove flag is true', () => {
            const initialQuery = 'param1=value1;param2=value2;';
            const paramToRemove = { parameter: 'param1', value: 'newValue1', remove: true };
            service.setQuery(initialQuery);

            service.pushParam(paramToRemove);
            const result = service.getQuery();

            expect(result).toBe('param2=value2;param1=newValue1;');
        });

        it('should not modify the query string if the parameter does not have a remove flag', () => {
            const initialQuery = 'param1=value1;';
            const newParam = { parameter: 'param2', value: 'value2' };
            service.setQuery(initialQuery);

            service.pushParam(newParam);

            const result = service.getQuery();
            expect(result).toBe('param1=value1;param2=value2;');
        });

        it('should not add a parameter if the value is not provided', () => {
            const initialQuery = 'param1=value1;';
            const newParam = { parameter: 'param2', value: '' };
            service.setQuery(initialQuery);

            service.pushParam(newParam);
            const result = service.getQuery();
            expect(result).toBe('param1=value1;param2=;');
        });
    });

    describe('removeParametersFromQueryFilter', () => {
        it('should remove the parameters that match any of the keys from queryWithFilter', () => {
            const initialFilters = ['param1=value1', 'param2=value2', 'param3=value3'];
            const parametersToRemove = ['param1', 'param3'];
            service.setQueryWithFilter(initialFilters);

            service.removeParametersFromQueryFilter(parametersToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual(['param2=value2']);
        });

        it('should not modify the queryWithFilter if none of the parameters match', () => {
            const initialFilters = ['param1=value1', 'param2=value2'];
            const parametersToRemove = ['param3'];
            service.setQueryWithFilter(initialFilters);

            service.removeParametersFromQueryFilter(parametersToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual(initialFilters);
        });

        it('should remove multiple matching parameters correctly', () => {
            const initialFilters = ['param1=value1', 'param2=value2', 'param3=value3', 'param1=value4'];
            const parametersToRemove = ['param1'];
            service.setQueryWithFilter(initialFilters);

            service.removeParametersFromQueryFilter(parametersToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual(['param2=value2', 'param3=value3']);
        });

        it('should return an empty array if all parameters are removed', () => {
            const initialFilters = ['param1=value1', 'param2=value2', 'param3=value3'];
            const parametersToRemove = ['param1', 'param2', 'param3'];
            service.setQueryWithFilter(initialFilters);

            service.removeParametersFromQueryFilter(parametersToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual([]);
        });
    });

    describe('removeParamFromQueryFilter', () => {
        it('should remove the parameter from queryWithFilter if it matches', () => {
            const initialFilters = ['param1=value1', 'param2=value2', 'param3=value3'];
            const paramToRemove = 'param2';
            service.setQueryWithFilter(initialFilters);

            service.removeParamFromQueryFilter(paramToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual(['param1=value1', 'param3=value3']);
        });

        it('should not modify the queryWithFilter if the parameter does not exist', () => {
            const initialFilters = ['param1=value1', 'param2=value2'];
            const paramToRemove = 'param3';
            service.setQueryWithFilter(initialFilters);

            service.removeParamFromQueryFilter(paramToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual(initialFilters);
        });

        it('should remove only the exact matching parameter', () => {
            const initialFilters = ['param1=value1', 'param2=value2', 'param1=value3'];
            const paramToRemove = 'param1';
            service.setQueryWithFilter(initialFilters);

            service.removeParamFromQueryFilter(paramToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual(['param2=value2']);
        });

        it('should not affect the queryWithFilter if the filter is empty', () => {
            const initialFilters = [];
            const paramToRemove = 'param1';
            service.setQueryWithFilter(initialFilters);

            service.removeParamFromQueryFilter(paramToRemove);
            const result = service.getQueryWithFilter();

            expect(result).toEqual([]);
        });
    });

    describe('getDefaultSearchValue', () => {
        it('should return the correct value for a default search parameter', () => {
            const initialQuery = 'limit=10;sortBy=asc;createdAppId=123;';
            const defaultValue = 'sortBy';
            service.setQuery(initialQuery);

            const result = service.getDefaultSearchValue(defaultValue);

            expect(result).toBe('asc');
        });

        it('should return an empty string if the default value is not found', () => {
            const initialQuery = 'limit=10;sortBy=asc;createdAppId=123;';
            const defaultValue = 'invalidParam';
            service.setQuery(initialQuery);

            const result = service.getDefaultSearchValue(defaultValue);

            expect(result).toBeUndefined();
        });

        it('should return an empty string if the default value exists but has no value', () => {
            const initialQuery = 'limit=10;sortBy=;createdAppId=123;';
            const defaultValue = 'sortBy';
            service.setQuery(initialQuery);

            const result = service.getDefaultSearchValue(defaultValue);

            expect(result).toBe('');
        });

        it('should return an empty string if the default value is not part of the defaultSearch array', () => {
            const initialQuery = 'limit=10;sortBy=asc;createdAppId=123;';
            const defaultValue = 'limit';
            service.setQuery(initialQuery);

            const result = service.getDefaultSearchValue(defaultValue);

            expect(result).toBe('10');
        });

        it('should return an empty string if the query string does not contain the parameter', () => {
            const initialQuery = 'limit=10;sortBy=asc;createdAppId=123;';
            const defaultValue = 'createdAppId';
            service.setQuery(initialQuery);

            const result = service.getDefaultSearchValue(defaultValue);

            expect(result).toBe('123');
        });

    });

    describe('getQueryFilter', () => {
        it('should return a single value when there is one match in queryWithFilter', () => {
            const queryWithFilter = ['param1=(value1)#param2=(value2)'];
            const item = 'param1';
            service.setQueryWithFilter(queryWithFilter);

            const result = service.getQueryFilter(item);

            expect(result).toBe('value1 OR value2');
        });

        it('should return a combined value using OR if there are multiple matches', () => {
            const queryWithFilter = ['param1=(value1)#param2=(value2)', 'param1=(value3)#param2=(value4)'];
            const item = 'param1';
            service.setQueryWithFilter(queryWithFilter);

            const result = service.getQueryFilter(item);

            expect(result).toBe('value1 OR value2');
        });

        it('should return an undefine if no matching filter is found', () => {
            const queryWithFilter = ['param1=(value1)#param2=(value2)'];
            const item = 'nonExistentParam';
            service.setQueryWithFilter(queryWithFilter);

            const result = service.getQueryFilter(item);

            expect(result).toBe(undefined);
        });

        it('should return an undefine if the queryWithFilter is empty', () => {
            const queryWithFilter: string[] = [];
            const item = 'param1';
            service.setQueryWithFilter(queryWithFilter);

            const result = service.getQueryFilter(item);

            expect(result).toBe(undefined);
        });

        it('should return an empty string if the matched filter has no value', () => {
            const queryWithFilter = ['param1=();param2=value2'];
            const item = 'param1';
            service.setQueryWithFilter(queryWithFilter);

            const result = service.getQueryFilter(item);

            expect(result).toBe(';param2=value2');
        });
    });

});
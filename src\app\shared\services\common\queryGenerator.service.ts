/* Purpose: To dynamically form the querystring */
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';

@Injectable({
    providedIn: 'root'
})
export class QueryGenerator {
    private query: string = '';
    private queryWithFilter: any = [];
    defaultQuery: boolean = false;
    defaultSearch = [CONSTANTS.LIMIT,CONSTANTS.SORT_BY,CONSTANTS.CREATED_APP_ID];
    removeChip = { assignedTo: 'User', status: 'Status',deliveryChannel:"deliveryChannel",adType:"adType" };
    
    setQuery(query: string) {
        this.query = query;
    }
    getQuery() {
        return this.query;
    }
    setQueryWithFilter(queryWithFilter) {
        this.queryWithFilter = queryWithFilter;
    }
    getQueryWithFilter() {
        return this.queryWithFilter;
    }
    removeQueryWithFilter(chip) {
        this.queryWithFilter = this.queryWithFilter.filter(ele => ele.indexOf(this.removeChip[chip]) === -1);
    }
    getQueryFilter(item) {
        const queryWithFilter = this.queryWithFilter.length && this.queryWithFilter.filter(ele => ele.indexOf(item) !== -1)[0];
        if (queryWithFilter && queryWithFilter.length) {
            const filter = queryWithFilter.split("#");
            return filter.reduce((output, ele) => {
                let splitStatus = ele.indexOf('=(')===-1?ele.split("=")[1]:ele.split("=(")[1];
                splitStatus = splitStatus.replace(/[/)/(]/gi, '');
                if (!output) {
                    output = splitStatus;
                } else {
                    output = `${output} OR ${splitStatus}`;
                }
                return output;
            }, '');
        }
    }

    pushParameters(parameters) {
        parameters.paramsList.forEach(param => {
            this.pushParam(param);
        });
    }

    getInputValue(inputId) {
        let index = this.query.indexOf(inputId);
        let indexFirst = this.query.indexOf("(", index);
        let indexLast = this.query.indexOf(");", indexFirst);
        if (index !== -1) {
            return this.query.slice(indexFirst + 1, indexLast);
        }
        return '';
    }
    getInputRangeValue(inputId) {
        let index = this.query.indexOf(inputId);
        let indexFirst = this.query.indexOf("[", index);
        let indexLast = this.query.indexOf("];", indexFirst);
        if (index !== -1) {
            return this.query.slice(indexFirst + 1, indexLast);
        }
        return '';
    }
    removeParameters(parameters) {
        parameters.forEach(param => {
            this.removeParam(param);
        });
    }
    setDefaultQuery(defaultQuery) {
        this.defaultQuery = defaultQuery;
    }
    getDefaultQuery() {
        return this.defaultQuery;
    }
    removeParam(param: string) {
        const index = this.query.indexOf(`${param}=`);
        if (index !== -1) {
            const sliceStr = this.query.slice(index);
            this.setQuery(`${this.query.slice(0, index)}${sliceStr.slice(sliceStr.indexOf(';') + 1)}`);
        }
    }
    pushParam(param) {
        if (param.remove) {
            this.removeParam(param.parameter);
        }
        this.setQuery(`${this.query}${param.parameter}=${param.value};`);
    }
    removeParametersFromQueryFilter(parameters) {
        this.queryWithFilter = this.queryWithFilter.filter(filterValue => {
            for(let key of parameters) {    
                if(filterValue.includes(key)) {
                    return false
                }
            }
            return true;
        })
    }
    removeParamFromQueryFilter(param) {
        this.queryWithFilter = this.queryWithFilter.filter(ele => ele.indexOf(param) === -1);
    }
    getDefaultSearchValue(defaultValue){
        //example sortBy
        if(this.defaultSearch.includes(defaultValue)){
            const index = this.query.indexOf(`${defaultValue}`),
            indexFirst = this.query.indexOf("=", index),
            indexLast = this.query.indexOf(";", indexFirst);
            if (index !== -1) {
                return this.query.slice(indexFirst + 1, indexLast);
            }
            return '';
        }
    }
}
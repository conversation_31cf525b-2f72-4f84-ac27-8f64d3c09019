import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { SearchUsersService } from './search-users.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { CONSTANTS } from '@appConstants/constants';

describe('SearchUsersService', () => {
    let service: SearchUsersService;
    let httpMock: HttpTestingController;
    let mockInitialDataService: jasmine.SpyObj<InitialDataService>;
    const mockApiUrl = 'https://api.example.com/search/users?';
    const mockResponse = [{ id: 1, name: '<PERSON>' }];
    const uid = '12345';
    const mockErrorResponse = { status: 404, statusText: 'Not Found' };

    beforeEach(() => {
        mockInitialDataService = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);
        mockInitialDataService.getConfigUrls.and.returnValue('https://api.example.com/search/users?');

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                SearchUsersService,
                { provide: InitialDataService, useValue: mockInitialDataService }
            ]
        });

        service = TestBed.inject(SearchUsersService);
        httpMock = TestBed.inject(HttpTestingController);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('API service tests for getUsers, getDigitalBuilders, getNonDigitalBuilder', () => {
        const mockResponse = [{ id: 1, name: 'John Doe' }];

        const testApiMethod = (methodName: string, query: string, expectedUrl: string) => {
            it(`should format query and call the API with correct URL for ${methodName}`, () => {
                service[methodName](query).subscribe((response) => {
                    expect(response).toEqual(mockResponse);
                });

                const req = httpMock.expectOne(expectedUrl);
                expect(req.request.method).toBe('GET');
                req.flush(mockResponse);
            });
        };

        // Tests for getUsers
        const usersApiUrl = 'https://api.example.com/search/users?searchStr=';
        testApiMethod('getUsers', 'John Doe', `${usersApiUrl}John?Doe*`);
        testApiMethod('getUsers', 'JohnDoe', `${usersApiUrl}JohnDoe*`);

        // Tests for getDigitalBuilders
        const digitalBuildersApiUrl = 'https://api.example.com/search/users?searchStr=';
        testApiMethod('getDigitalBuilders', 'John Doe', `${digitalBuildersApiUrl}John?Doe*&permission=DO_ASSIGNED_DIGITAL_OFFERS`);
        testApiMethod('getDigitalBuilders', 'JohnDoe', `${digitalBuildersApiUrl}JohnDoe*&permission=DO_ASSIGNED_DIGITAL_OFFERS`);

        // Tests for getNonDigitalBuilders
        const nonDigitalBuildersApiUrl = 'https://api.example.com/search/users?searchStr=';
        testApiMethod('getNonDigitalBuilders', 'Jane Smith', `${nonDigitalBuildersApiUrl}Jane?Smith*&permission=DO_ASSIGNED_NON_DIGITAL_OFFERS`);
        testApiMethod('getNonDigitalBuilders', 'JaneSmith', `${nonDigitalBuildersApiUrl}JaneSmith*&permission=DO_ASSIGNED_NON_DIGITAL_OFFERS`);
    });

    it('should send the correct API request with modified query string', async () => {
        const query = 'John Doe';
        const expectedUrl = `${mockApiUrl}searchStr=John?Doe*`;

        service.asyncGetUsers(query).then((users) => {
            expect(users).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('GET');
        req.flush(mockResponse);
    });

    it('should handle queries without spaces correctly', async () => {
        const query = 'JohnDoe';
        const expectedUrl = `${mockApiUrl}searchStr=JohnDoe*`;

        service.asyncGetUsers(query).then((users) => {
            expect(users).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('GET');
        req.flush(mockResponse);
    });

    it('should handle empty query', async () => {
        const query = '';
        const expectedUrl = `${mockApiUrl}searchStr=*`;

        service.asyncGetUsers(query).then((users) => {
            expect(users).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('GET');
        req.flush(mockResponse);
    });

    it('should send the correct request and return user data with searchByUID', () => {
        const expectedUrl = `${mockApiUrl}${uid}`;

        service.searchByUID(uid).subscribe((response) => {
            expect(response).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('POST');
        expect(req.request.body.reqObj).toEqual({ headers: CONSTANTS.HTTP_HEADERS_PREVIEW, isHidePgLoader: true });
        req.flush(mockResponse);
    });

    it('should handle errors in searchByUID and return default user data', () => {
        const expectedUrl = `${mockApiUrl}${uid}`;

        service.searchByUID(uid).subscribe((response) => {
            expect(response).toEqual({
                firstName: `UserId '${uid}' no longer exists`,
                lastName: '',
                email: '',
                userId: ''
            });
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('POST');
        req.flush(null, mockErrorResponse);
    });

    it('should send the correct request and return user data with asyncSearchByUID', async () => {
        const uid = '12345';
        const expectedUrl = `${mockApiUrl}${uid}`;
        const mockResponse = { data: 'test' };

        service.asyncSearchByUID(uid).then((response) => {
            expect(response).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('POST');
        expect(req.request.body.reqObj).toEqual({ headers: CONSTANTS.HTTP_HEADERS_PREVIEW, isHidePgLoader: true });
        req.flush(mockResponse); // Mock successful response
    });

    it('should return an empty array if the request fails', async () => {
        const uid = '12345';
        const expectedUrl = `${mockApiUrl}${uid}`;

        service.asyncSearchByUID(uid).then((response) => {
            expect(response).toEqual([]); // Ensure empty array is returned on failure
        });

        const req = httpMock.expectOne(expectedUrl);
        expect(req.request.method).toBe('POST');
        expect(req.request.body.reqObj).toEqual({ headers: CONSTANTS.HTTP_HEADERS_PREVIEW, isHidePgLoader: true });

        // Simulate an error response
        req.flush(null, { status: 500, statusText: 'Server Error' });
    });

    afterEach(() => {
        httpMock.verify();
    });
});

import { HttpClient } from '@angular/common/http';

import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SearchUsersService {

  constructor(
    private http: HttpClient,
    private apiConfigService: InitialDataService
  ) { 
    // intentionally left empty
  }

  searchUsersApi: string = this.apiConfigService.getConfigUrls(CONSTANTS.SEARCH_USERS_API);

  public getUsers(query) {
      let reqBody: string;
      query = query && query.includes(' ') ? query.replace(/ /g, '?') : query;
      reqBody = 'searchStr=' + query + '*';
      return this.http.get(`${this.searchUsersApi}${reqBody}`);
  }

  public getDigitalBuilders(query) {
    let reqBody: string;
    query = query && query.includes(' ') ? query.replace(/ /g, '?') : query;
    reqBody = 'searchStr=' + query + '*&permission=DO_ASSIGNED_DIGITAL_OFFERS';
    return this.http.get(`${this.searchUsersApi}${reqBody}`);
  }

  public getNonDigitalBuilders(query) {
      let reqBody: string;
      query = query && query.includes(' ') ? query.replace(/ /g, '?') : query;
      reqBody = 'searchStr=' + query + '*&permission=DO_ASSIGNED_NON_DIGITAL_OFFERS';
      return this.http.get(`${this.searchUsersApi}${reqBody}`);
  }

  public async asyncGetUsers(query) {
    let reqBody: string;
    query = query && query.includes(' ') ? query.replace(/ /g, '?') : query;
    reqBody = 'searchStr=' + query + '*';
    const users = await this.http.get(`${this.searchUsersApi}${reqBody}`).toPromise();
    return users;
  }

  public searchByUID(uid) {

    const url = this.apiConfigService.getConfigUrls(CONSTANTS.GET_USER_PREF) + uid;
    const params = { reqObj: {headers: CONSTANTS.HTTP_HEADERS_PREVIEW, isHidePgLoader: true }};
    return this.http.post(url, params).pipe(
      catchError(err => of({
        firstName: 'UserId \'' + uid + '\' no longer exists',
        lastName: '',
        email: '',
        userId: ''
      }))
    );
  }

  public async asyncSearchByUID(uid) {

    const url = this.apiConfigService.getConfigUrls(CONSTANTS.GET_USER_PREF) + uid;
    const params = { reqObj: {headers: CONSTANTS.HTTP_HEADERS_PREVIEW, isHidePgLoader: true }};
    try {
      const result = await this.http.post(url, params).toPromise();
      return result;
    } catch {
      return [];
    }
  }


}

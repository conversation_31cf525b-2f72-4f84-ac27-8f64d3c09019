import { fakeAsync, TestBed, tick } from "@angular/core/testing";
import { StringUtilsHelperService } from "./string-utils.helper.service";

describe('StringUtilsHelperService', () => { 
    let service: StringUtilsHelperService;
    
    beforeEach(() => {
        TestBed.configureTestingModule({});
        service = new StringUtilsHelperService();
    })

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should be remove extra space', () => {
        let list = 'a, b, c';
        let result = service.removeExtraSpace(list);
        expect(result).toEqual(['a', 'b', 'c']);
    });

    it('should clear message after 3 seconds', fakeAsync(() => {
        const messageObj = { msg: 'Test message', msgType: 'success' };
        service.resetActionMessages(messageObj);

        expect(messageObj.msg).toBe('Test message');
    
        tick(3000);

        expect(messageObj.msg).toBe('');
        expect(messageObj.msgType).toBe('');

    }));

    describe('removeExtraCharacters', () => {
        it('should return true for invalid characters', () => {
            expect(service.removeExtraCharacters('abc123')).toBe(true);
            expect(service.removeExtraCharacters('@#$%^')).toBe(true);
            expect(service.removeExtraCharacters('123, 45a')).toBe(true);
        });

        it('should call removeExtraSpace for valid input', () => {
            spyOn(service, 'removeExtraSpace').and.callThrough();

            service.removeExtraCharacters('123, 456 , 789');
            expect(service.removeExtraSpace).toHaveBeenCalledWith('123, 456 , 789');
        });

        it('should return false for empty input', () => {
            expect(service.removeExtraCharacters('')).toBe(false);
            expect(service.removeExtraCharacters(null)).toBe(false);
            expect(service.removeExtraCharacters(undefined)).toBe(false);
        });

        it('should return formatted output for valid numbers and commas', () => {
            expect(service.removeExtraCharacters('123, 456 , 789')).toEqual(['123', '456', '789']);
            expect(service.removeExtraCharacters('1, 2, 3, 4')).toEqual(['1', '2', '3', '4']);
            expect(service.removeExtraCharacters('1000')).toEqual(['1000']);
        });
        
    });

    describe('setActionMessages', () => {
        it('should set msg and msgType correctly and reset messages when show is true', () => {
            const messageObj: any = { display: 'Test Message', type: 'success' }; 
            spyOn(service, 'resetActionMessages');

            service.setActionMessages(messageObj, true);

            expect(messageObj).toEqual({
                display: 'Test Message',
                type: 'success',
                msg: 'Test Message',
                msgType: 'success' 
            });

            expect(service.resetActionMessages).toHaveBeenCalledWith(messageObj);
        });

        it('should do nothing if messageObj is empty', () => {
            const messageObj: any = {}; 
            spyOn(service, 'resetActionMessages');

            service.setActionMessages(messageObj, true);

            expect(Object.keys(messageObj)).toEqual([]);
            expect(service.resetActionMessages).not.toHaveBeenCalled();
        });
    });

    
    describe('escapeValue', () => {
        it('should escape special characters (., space, and single quote)', () => {
            const input = "Hello World' ";
            const expectedOutput = "Hello\\ World\\'\\ ";
            const result = service.escapeValue(input);
            expect(result).toBe(expectedOutput);
        });

        it('should return the same string if no special characters are present', () => {
            const input = "HelloWorld";
            const expectedOutput = "HelloWorld";
            const result = service.escapeValue(input);
            expect(result).toBe(expectedOutput);
        });

        it('should escape only the special characters and leave others unchanged', () => {
            const input = "abc 123.'";
            const expectedOutput = "abc\\ 123\\.\\'";
            const result = service.escapeValue(input);
            expect(result).toBe(expectedOutput);
        });

        it('should return an empty string if the input is empty', () => {
            const input = "";
            const expectedOutput = "";
            const result = service.escapeValue(input);
            expect(result).toBe(expectedOutput);
        });

        it('should handle strings with only special characters', () => {
            const input = ". ' ";
            const expectedOutput = "\\.\\ \\'\\ ";
            const result = service.escapeValue(input);
            expect(result).toBe(expectedOutput);
        });
    });

});

import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StringUtilsHelperService {

  constructor() {
    // intentionally left empty
   }
  removeExtraSpace(list) {
    return list && list.split(',').map(item => item.replace(/\s/g, '')).filter(item => item !== '' );
    }
  removeExtraCharacters(value) {
    if (value) {
      if (!(/^[0-9,\s\r\n]+$/g).test(value)) {
        return true;
      } else {
       return this.removeExtraSpace(value);
      }
    } else {
      return false;
    }

    // If  user enters invalid characters, dont allow

  }
  resetActionMessages(messageObj) {
    // After a moment, hide the message
    setTimeout(() => { messageObj.msg = ''; messageObj.msgType = '' }, 3000);
  }
  setActionMessages(messageObj: any, show = true) {
    if (Object.keys(messageObj).length) {
      messageObj.msg = `${messageObj.display ? messageObj.display : ''}`;
      messageObj.msgType = messageObj.type;
      if (show) {
        this.resetActionMessages(messageObj);
      }

    }

  }

  escapeValue(id) {
    let specialChar = [".", " ", "'"];

    let newId = ''
    for (let i = 0; i < id.length; i++) {
      if (specialChar.includes(id[i])) {
        newId = [newId, '\\', id[i]].join('');
      } else {
        newId = [newId, id[i]].join('');
      }
    }
    return newId;
  }
  
}

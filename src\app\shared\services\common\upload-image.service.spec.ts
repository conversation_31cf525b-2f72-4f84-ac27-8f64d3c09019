import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { UploadImagesService } from './upload-images.service';
import { InitialDataService } from './initial.data.service';
import { CommonService } from './common.service';
import { take } from 'rxjs/operators';
import { of } from 'rxjs';

describe('UploadImagesService', () => {
    let service: UploadImagesService;
    let httpMock: HttpTestingController;
    let initialDataService: jasmine.SpyObj<InitialDataService>;
    let commonService: jasmine.SpyObj<CommonService>;

    beforeEach(() => {
        const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);
        const commonServiceSpy = jasmine.createSpyObj('CommonService', ['getHeaders']);

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                UploadImagesService,
                { provide: InitialDataService, useValue: initialDataServiceSpy },
                { provide: CommonService, useValue: commonServiceSpy }
            ]
        });

        service = TestBed.inject(UploadImagesService);
        httpMock = TestBed.inject(HttpTestingController);
        initialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
        commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should set file items', () => {
        const items = 5;
        service.setfiles(items);
        expect(service.getfiles()).toBe(items);
    });

    it('should send and get loading status', fakeAsync(() => {
        const loadingStatus = true;
        let status: boolean | undefined;
        service.getLoading().subscribe(value => {
            status = value;
        });
        service.sendLoading(loadingStatus);
        tick(); 
        expect(status).toBe(loadingStatus);
    }));

    it('should send and get image ID', fakeAsync(() => {
        const imageID = '12345';
        let id: string | undefined;
        service.getImage().subscribe(value => {
            id = value;
        });
        service.sendImage(imageID);
        tick(); 
        expect(id).toBe(imageID);
    }));
    
    it('should send and get source image ID', fakeAsync(() => {
        const source = '12345';
        let id: string | undefined;
        service.getSourceImage().subscribe(value => id = value);
        service.sendSourceImage(source);
        tick();
        expect(id).toBe(source);
    }));
   
   
    it('should upload a file', () => {
        const httpClientStub = TestBed.inject(HttpClient);
        const stub = spyOn(httpClientStub, 'post').and.returnValue(of({}));
        const file = new File([''], 'test.jpg', { type: 'image/jpeg' });

        service.upload(file).subscribe();

        expect(stub).toHaveBeenCalled();
    });

    describe('getImagesGroupData', () => {
        it('should make expected calls', () => {
            const httpClientStub = TestBed.inject(HttpClient);
            const stub = spyOn(httpClientStub, 'get').and.returnValue(of({}));
            const query = 'test query';

            service.getImagesGroupData(query);

            expect(stub).toHaveBeenCalled();
        });
    });

    describe('getImageID', () => {
        it('should make expected calls', () => {
            const httpClientStub = TestBed.inject(HttpClient);
            const stub = spyOn(httpClientStub, 'get').and.returnValue(of(new Blob()));
            const imageID = '12345';
            service.getImageID(imageID).subscribe();
            expect(stub).toHaveBeenCalledWith(`${service.searchImageAPI}/${imageID}?$ecom-product-card-desktop-png$`, { responseType: 'blob' as 'json' });
        });
    });

});

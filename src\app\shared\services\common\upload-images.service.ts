import { HttpClient } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';

import { Injectable } from '@angular/core';
import { CONSTANTS } from "@appConstants/constants";
import { CommonService } from "./common.service";
import { InitialDataService } from './initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class UploadImagesService {
    subject = new Subject<string>();
    loading = new Subject<boolean>();
    imageID = new Subject<string>();
    sourceImageID = new Subject<string>();
    fileItems = 0;

    constructor(private http: HttpClient, private apiConfigService: InitialDataService,public commonService: CommonService) {
        // intentionally left empty
    }
    uploadImageAPI: string = this.apiConfigService.getConfigUrls(CONSTANTS.UPLOAD_IMAGE_API);
    getImageAPI: string = this.apiConfigService.getConfigUrls(CONSTANTS.GET_IMAGE_API);
    searchImageAPI: string = this.apiConfigService.getConfigUrls(CONSTANTS.SEARCH_IMAGES_API);

    setfiles(items) {
        this.fileItems = items;
    }

    getfiles() {
        return this.fileItems;
    }

    sendLoading(event) {
        this.loading.next(event);
    }

    getLoading(): Observable<boolean> {
        return this.loading.asObservable();
    }

    sendImage(imageID) {
        this.imageID.next(imageID);
    }

    getImage(): Observable<string> {
        return this.imageID.asObservable();
    }

    sendSourceImage(source) {
        this.sourceImageID.next(source);
    }

    getSourceImage(): Observable<string> {
        return this.sourceImageID.asObservable();
    }

    upload(file: any) {
        const uploadData = new FormData();
        uploadData.append('file', file, file.name);
        return this.http.post(this.uploadImageAPI, uploadData);
    }

    getImageID(imageID) {
        /* Removed as part of image domain change
        // const headerCopy = this.commonService.getHeaders();
        //  const reqBody = {
        //      'imageIds': imageID
        //  }
        //  const params = { ...reqBody, reqObj: { headers: headerCopy } };

        //  //const params = new HttpParams().set('state', 'base');
        //  return this.http.post(`${this.searchImageAPI}`, params);        */
        return this.http.get(`${this.searchImageAPI}/${imageID}?$ecom-product-card-desktop-png$`,{ responseType: 'blob' });
    }
    getImagesGroupData(query) {
       
        // @ts-ignore
        let reqBody: string;
        // tslint:disable-next-line:no-unused-expression
        query = (query && query.includes(' ')) ? query.replace(' ', '\\ ') : query;
        reqBody = 'searchStr=' + query + '*';
        return this.http.get(`${this.searchImageAPI}${reqBody}`);
    }
}

import { TestBed } from '@angular/core/testing';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { InitialDataService } from '../common/initial.data.service';
import { FullFillmentChannelService } from './full-fillment-channel.service';

describe('FullFillmentChannelService', () => {
    let service: FullFillmentChannelService;
    let fb: UntypedFormBuilder;
    let initialDataService: InitialDataService;
    let facetItemService: FacetItemService;

    beforeEach(() => {
        const initialDataServiceStub = () => ({
            getAppDataName: (name: string) => ({ option1: 'Option 1', option2: 'Option 2' })
        });
        const facetItemServiceStub = () => ({
            programCodeSelected: CONSTANTS.SC
        });

        TestBed.configureTestingModule({
            providers: [
                FullFillmentChannelService,
                UntypedFormBuilder,
                { provide: InitialDataService, useFactory: initialDataServiceStub },
                { provide: FacetItemService, useFactory: facetItemServiceStub }
            ]
        });

        service = TestBed.inject(FullFillmentChannelService);
        fb = TestBed.inject(UntypedFormBuilder);
        initialDataService = TestBed.inject(InitialDataService);
        facetItemService = TestBed.inject(FacetItemService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should set fulfillment channel controls with default selection', () => {
        const formGroup = new UntypedFormGroup({});
        service.setFullfillmentChannelCtrls(null, true, formGroup);
        expect(formGroup.get('fulfillmentChannel')).toBeTruthy();
    });

    it('should set fulfillment channel controls with data', () => {
        const data = { info: { fulfillmentChannel: { option1: true, option2: false } } };
        const formGroup = new UntypedFormGroup({});
        service.setFullfillmentChannelCtrls(data, null, formGroup);
        expect(formGroup.get('fulfillmentChannel')).toBeTruthy();
        expect(formGroup.get('fulfillmentChannel.option1').value).toBe(true);
        expect(formGroup.get('fulfillmentChannel.option2').value).toBe(false);
    });

    it('should return fulfillment channel constants', () => {
        expect(service.fulfillChannelConstants).toEqual({ option1: 'Option 1', option2: 'Option 2' });
    });

    it('should check if option is selected', () => {
        const data = { info: { fulfillmentChannel: { option1: true, option2: false } } };
        expect(service.checkIfSelected(data, 'option1')).toBe(true);
        expect(service.checkIfSelected(data, 'option2')).toBe(false);
    });

    it('should set fulfillment display value', () => {
        const fulfillmentObj = { option1: true, option2: false };
        const displayValue = service.setFulfillmentDisplayValue(fulfillmentObj);
        expect(displayValue).toBe('Option 1');
    });

    it('should set fulfillment display value with multiple selections', () => {
        const fulfillmentObj = { option1: true, option2: true };
        const displayValue = service.setFulfillmentDisplayValue(fulfillmentObj);
        expect(displayValue).toBe('Option 1 , Option 2');
    });

    it('should return null for fulfillChannelConstants if programCodeSelected is not SC or SPD', () => {
        facetItemService.programCodeSelected = 'OTHER';
        expect(service.fulfillChannelConstants).toBeNull();
    });

    it('should set fulfillment channel controls to null if no default selection and no data', () => {
        const formGroup = new UntypedFormGroup({});
        service.setFullfillmentChannelCtrls(null, false, formGroup);
        expect(formGroup.get('fulfillmentChannel').value).toBeNull();
    });

    it('should return true if no data is provided in checkIfSelected', () => {
        expect(service.checkIfSelected(null, 'option1')).toBe(true);
    });

    it('should return true if no fulfillmentChannel is provided in data', () => {
        const data = { info: {} };
        expect(service.checkIfSelected(data, 'option1')).toBe(true);
    });

    it('should return true if no option is selected in fulfillmentChannel', () => {
        const data = { info: { fulfillmentChannel: { option1: false, option2: false } } };
        expect(service.checkIfSelected(data, 'option1')).toBe(true);
    });
});
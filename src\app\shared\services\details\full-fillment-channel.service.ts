import { Injectable } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl } from '@angular/forms';
import { CONSTANTS } from '@appConstants/constants';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { InitialDataService } from '../common/initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class FullFillmentChannelService {

  constructor(
    private fb: UntypedFormBuilder,
    private _initialDataService: InitialDataService,
    private facetItemService: FacetItemService
  ) { 
    // intentionally left empty
  }
  
  setFullfillmentChannelCtrls(data, defaultSel, formGroup) {
   
    if(!defaultSel && !data) {
      const fullFillmentChannelFg = this.fb.control(null);
      formGroup?.setControl("fulfillmentChannel", fullFillmentChannelFg);
    } else  {
      const fullFillmentChannelFg = this.fb.group({});
      this.fulfillChannelConstants && Object.keys(this.fulfillChannelConstants).forEach((option: any) => {
        fullFillmentChannelFg.addControl(option, new UntypedFormControl(data ? this.checkIfSelected(data, option) : defaultSel));
      });
      formGroup?.setControl("fulfillmentChannel", fullFillmentChannelFg);
    }
  }

  get fulfillChannelConstants() {
    if ([CONSTANTS.SC,CONSTANTS.SPD].includes(this.facetItemService?.programCodeSelected)) {
      return this._initialDataService.getAppDataName(CONSTANTS.FULFILLMENT_CHANNEL_CONST);
    }
    else {
      return null;
    }
  }

  checkIfSelected(data, option) {
    if (data) {
      const { info: { fulfillmentChannel } } = data;
      if (fulfillmentChannel) {
        const isAnySelected = Object.keys(fulfillmentChannel).some((val) => fulfillmentChannel[val]);
        return isAnySelected ? (fulfillmentChannel[option] || false) : true;
      }
    }
    return true;
  }

  setFulfillmentDisplayValue(fulfillmentObj) {
    const selectedChannelFlags = Object.keys(fulfillmentObj).reduce((output, flag) => {
      if (this.fulfillChannelConstants && fulfillmentObj[flag]) {
        output.push(this.fulfillChannelConstants[flag])
      }
      return output;
    }, []);
    return selectedChannelFlags.join(' , ');
  }
}

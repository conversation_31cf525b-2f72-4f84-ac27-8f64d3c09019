import { TestBed } from '@angular/core/testing';
import { UntypedFormBuilder, UntypedFormControl, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject } from 'rxjs';
import { GeneralOfferTypeService } from './general-offer-type.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { CommonRouteService } from '../common/common-route.service';
import { InitialDataService } from '../common/initial.data.service';
import { CONSTANTS } from '@appConstants/constants';
import { BPD_OR_RULES, BPD_TEMPLATE_RULES, GR_OR_RULES, OR_RULES, SPD_OR_RULES } from "@appModules/request/shared/rules/OR.rules";

const ANY_PRODUCT = 'Any Product';
 
describe('GeneralOfferTypeService', () => {
    let service: GeneralOfferTypeService;
    let toastrService: jasmine.SpyObj<ToastrService>;
    let facetItemService: jasmine.SpyObj<FacetItemService>;
    let featureFlagsService: jasmine.SpyObj<FeatureFlagsService>;
    let commonRouteService: jasmine.SpyObj<CommonRouteService>;
    let initialDataService: jasmine.SpyObj<InitialDataService>;

    beforeEach(() => {
        const toastrSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']);
        const facetItemSpy = jasmine.createSpyObj('FacetItemService', ['programCodeSelected']);
        const featureFlagsSpy = jasmine.createSpyObj('FeatureFlagsService', ['isFeatureFlagEnabled', 'isBehavioralContinuityEnabled', 'isGRGetMilesEnabled']);
        const commonRouteSpy = jasmine.createSpyObj('CommonRouteService', ['isBpdReqPage'], { get currentActivatedRoute() { return 'template'; } });
        const initialDataSpy = jasmine.createSpyObj('InitialDataService', ['getAppData']);

        TestBed.configureTestingModule({
            providers: [
                GeneralOfferTypeService,
                UntypedFormBuilder,
                { provide: ToastrService, useValue: toastrSpy },
                { provide: FacetItemService, useValue: facetItemSpy },
                { provide: FeatureFlagsService, useValue: featureFlagsSpy },
                { provide: CommonRouteService, useValue: commonRouteSpy },
                { provide: InitialDataService, useValue: initialDataSpy }
            ]
        });

        service = TestBed.inject(GeneralOfferTypeService);
        toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
        facetItemService = TestBed.inject(FacetItemService) as jasmine.SpyObj<FacetItemService>;
        featureFlagsService = TestBed.inject(FeatureFlagsService) as jasmine.SpyObj<FeatureFlagsService>;
        commonRouteService = TestBed.inject(CommonRouteService) as jasmine.SpyObj<CommonRouteService>;
        initialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('')
            })
        });
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    xit('should initialize form', () => {
        service.createForm();
        expect(service.generalOfferTypeForm).toBeDefined();
    });

    it('should add form control', () => {
        const form = new UntypedFormBuilder().group({});
        service.addFormControl('testControl', new UntypedFormControl(''), form);
        expect(form.contains('testControl')).toBeTrue();
    });

    it('should remove form control', () => {
        const form = new UntypedFormBuilder().group({
            testControl: new UntypedFormControl('')
        });
        service.removeFormControl('testControl', form);
        expect(form.contains('testControl')).toBeFalse();
    });

    it('should validate field', () => {
        const control = new UntypedFormControl('', Validators.required);
        control.markAsTouched();
        service.isDraftSaveAttemptedFlag = true;
        service.isReqSubmitAttempted = false;
    });

    it('should show success toastr message', () => {
        service.showDeleteToastrMsg('Test Item');
        expect(toastrService.success).toHaveBeenCalledWith('Test Item Deleted', '', { timeOut: 500, closeButton: true });
    });

    it('should show error toastr message', () => {
        service.showRemoveErrorToastrMsg();
        expect(toastrService.error).toHaveBeenCalledWith('You cannot delete a row tied to a deployed offer.', '', { timeOut: 500, closeButton: true });
    });

    it('should get rules for offer type', () => {
        Object.defineProperty(facetItemService, 'programCodeSelected', { get: () => 'GR' });
        service.getRulesForOfferType('Buy X Get 1');
    });

    it('should check if any offer is deployed', () => {
        const obj = {
            offerRequestOffersData: { offers: [{ productGroupVersion: 1, offerStatus: 'DE' }] },
            productGroupVersions: { value: [{ id: 1 }] },
            productVersionIndex: 0
        };
        const result = service.checkIfAnyOfferIsDeployed(obj);
        expect(result).toBeTrue();
    });

    it('should get validators based on action', () => {
        const obj = { minValue: 10 };
        service.isReqSubmitAttempted = true;
        const validators = service.getValidatorsBasedOnAction(obj);
        expect(validators.length).toBe(2);
    });

    it('should create form control', () => {
        const formControls = [{ name: 'testControl', value: '', validate: true }];
        const formGroup = service.createFormControl(formControls);
        expect(formGroup.contains('testControl')).toBeTrue();
    });

    it('should create control object', () => {
        const controlObject = service.createControlObject('testControl', '', true);
        expect(controlObject).toEqual({ name: 'testControl', value: '', validate: true });
    });

    it('should get key by value', () => {
        const object = { key1: 'value1', key2: 'value2' };
        const key = service.getKeyByValue(object, 'value1');
        expect(key).toBe('key1');
    });

    it('should get discount headers', () => {
        const discounts = [{ Amt: true, UpTo: false, ItemLimit: true }];
        const headers = service.getDiscountHeaders(discounts);
        expect(headers).toEqual({ Amt: true, UpTo: false, ItemLimit: true, PerLbLimit: false, Points: false, rewards: false, miles: false });
    });

    it('should get discount rule', () => {
        service.getDiscountRule('Amt');
    });

    it('should get product group version discount', () => {
        Object.defineProperty(service, 'value', {
            value: { offerRequestOffers: [{ storeGroupVersion: { productGroupVersions: [{ discountVersion: { discounts: [{ benefitValueType: 'Amt' }] } }] } }] },
            writable: true
        });
        service.amountTypes = { Amt: 'Amount' };
        const discount = service.getProductGroupVersionDiscount(0);
        expect(discount).toEqual(['Amount']);
    });

    it('should sort by display order', () => {
        const list = [{ displayOrder: 2 }, { displayOrder: 1 }];
        const sortedList = service.displayOrderSorting(list);
        expect(sortedList[0].displayOrder).toBe(1);
    });

    it('should sort by display order level', () => {
        const list = [{ level: 2 }, { level: 1 }];
        const sortedList = service.displayOrderLevelSorting(list);
        expect(sortedList[0].level).toBe(1);
    });

    it('should sort store group version by display order', () => {
        const list = [{ storeGroupVersion: { displayOrder: 2 } }, { storeGroupVersion: { displayOrder: 1 } }];
        const sortedList = service.displayOrderForStoreGroupVersion(list);
        expect(sortedList[0].storeGroupVersion.displayOrder).toBe(1);
    });

    xit('should check if any tier is empty', () => {
        const tierValues = [{ amount: null }];
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            tierValues: new UntypedFormControl(tierValues, Validators.required)
        });
        const result = service.isAnyTierEmpty(tierValues, 'amount');
        expect(result).toBeTrue();
    });

    xit('should validate tier', () => {
        const tierValues = [{ level: 1, amount: 10 }, { level: 2, amount: 5 }];
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            tierValues: new UntypedFormControl([])
        });
        service.generalOfferTypeForm.controls['tierValues'].setValue(tierValues);
        const result = service.tierValidation(tierValues, { level: 2, amount: 5 }, 1, 'amount');
        expect(result).toBeTrue();
    });

    it('should get dropdown list', () => {
        const values = ['value1', 'value2'];
        const ids = ['id1', 'id2'];
        const dropdownList = service.getDropDownList(values, ids);
        expect(dropdownList).toEqual([{ id: 'id1', name: 'value1' }, { id: 'id2', name: 'value2' }]);
    });

    it('should get dropdown', () => {
        const dropdown = service.getDropDown('value1', 'id1');
        expect(dropdown).toEqual([{ name: 'value1', id: 'id1' }]);
    });

    it('should check if ecomm only is enabled', () => {
        featureFlagsService.isFeatureFlagEnabled.and.returnValue(true);
        const result = service.isEcommOnlyEnabled;
        expect(result).toBeTrue();
    });
    it('should initialize offer submit subscriber', () => {
        const isReqSubmitAttempted$ = new BehaviorSubject<boolean>(false);
        const isDraftSaveAttempted = new BehaviorSubject<boolean>(false);
        service.isReqSubmitAttempted$ = isReqSubmitAttempted$;
        service.isDraftSaveAttempted = isDraftSaveAttempted;

        service.initOfferSubmitSubscriber();

        isReqSubmitAttempted$.next(true);
        isDraftSaveAttempted.next(true);

        expect(service.isReqSubmitAttempted).toBeTrue();
        expect(service.isDraftSaveAttemptedFlag).toBeTrue();
    });
    it('should hide dropdown', () => {
        const dropdownElement = document.createElement('div');
        dropdownElement.classList.add('ng-dropdown-panel');
        document.body.appendChild(dropdownElement);

        spyOn(document, 'querySelector').and.returnValue(dropdownElement);

        service.hideDropdown();

        expect(dropdownElement.style.opacity).toBe('0');
        document.body.removeChild(dropdownElement);
    });
    it('should validate field when draft save attempted and control untouched', () => {
        const control = new UntypedFormControl('', Validators.required);
        control.markAsUntouched();
        service.isDraftSaveAttemptedFlag = true;
        service.isReqSubmitAttempted = false;
        const errors = service.validateField(control);
        expect(errors).toBeNull();
    });

    it('should validate field when request submit attempted and control untouched', () => {
        const control = new UntypedFormControl('', Validators.required);
        control.markAsUntouched();
        service.isDraftSaveAttemptedFlag = false;
        service.isReqSubmitAttempted = true;
        const errors = service.validateField(control);
        expect(errors).toEqual({ required: true });
    });

    it('should validate field when control is touched', () => {
        const control = new UntypedFormControl('', Validators.required);
        control.markAsTouched();
        service.isDraftSaveAttemptedFlag = true;
        service.isReqSubmitAttempted = false;
        const errors = service.validateField(control);
        expect(errors).toBeNull();
    });
    it('should return true for isBuyXGet1OfferType when type is "Buy X Get 1"', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Buy X Get 1')
            })
        });
        expect(service.isBuyXGet1OfferType).toBeTrue();
    });

    it('should return false for isBuyXGet1OfferType when type is not "Buy X Get 1"', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Other Type')
            })
        });
        expect(service.isBuyXGet1OfferType).toBeFalse();
    });

    it('should create qualification and benefit control', () => {
        const requestForm = new UntypedFormBuilder().group({});
        service.requestFormService = { requestForm } as any;
        const qualificationAndBenefit = service.createQualificationAndBenefitControl();
        expect(requestForm.contains('rules')).toBeTrue();
        expect(qualificationAndBenefit).toBeDefined();
    });

    it('should return existing qualification and benefit control', () => {
        const qualificationAndBenefit = new UntypedFormBuilder().group({});
        const rules = new UntypedFormBuilder().group({
            qualificationAndBenefit
        });
        const requestForm = new UntypedFormBuilder().group({
            rules
        });
        service.requestFormService = { requestForm } as any;
        const result = service.createQualificationAndBenefitControl();
        expect(result).toBe(qualificationAndBenefit);
    });
    it('should return form control name', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            testControl: new UntypedFormControl('')
        });
        const control = service.formControlName('testControl');
        expect(control).toBeDefined();
    });

    it('should get form control name from control', () => {
        const control = { testControl: new UntypedFormControl('') };
        const result = service.getFormControlName(control, 'testControl');
        expect(result).toBeDefined();
    });
    it('should return the form', () => {
        const form = new UntypedFormBuilder().group({});
        service.generalOfferTypeForm = form;
        expect(service.form).toBe(form);
    });

    it('should return the form value', () => {
        const formValue = { testControl: 'testValue' };
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            testControl: new UntypedFormControl('testValue')
        });
        expect(service.value).toEqual(formValue);
    });

    it('should return the form validity', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            testControl: new UntypedFormControl('', Validators.required)
        });
        expect(service.valid).toBeFalse();

        service.generalOfferTypeForm.controls['testControl'].setValue('testValue');
        expect(service.valid).toBeTrue();
    });
    it('should return true for isBehavioralWithRewardsFlat when conditions are met', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Rewards - Flat')
            })
        });
        spyOnProperty(service, 'isBehavioralOfferEnabled', 'get').and.returnValue(true);
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_ACTION_CODE);
        expect(service.isBehavioralWithRewardsFlat).toBeTrue();
    });

    it('should return false for isBehavioralWithRewardsFlat when conditions are not met', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Other Type')
            })
        });
        spyOnProperty(service, 'isBehavioralOfferEnabled', 'get').and.returnValue(false);
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue('Other Code');
        expect(service.isBehavioralWithRewardsFlat).toBeFalse();
    });

    it('should return true for isBehavioralContinutyWithRewardsFlat when conditions are met', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Rewards - Flat')
            })
        });
        spyOnProperty(service, 'isBehavioralContinutyEnabled', 'get').and.returnValue(true);
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);
        expect(service.isBehavioralContinutyWithRewardsFlat).toBeTrue();
    });

    it('should return false for isBehavioralContinutyWithRewardsFlat when conditions are not met', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Other Type')
            })
        });
        spyOnProperty(service, 'isBehavioralContinutyEnabled', 'get').and.returnValue(false);
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue('Other Code');
        expect(service.isBehavioralContinutyWithRewardsFlat).toBeFalse();
    });

    it('should return true for isBehavioralOfferEnabled when feature flag is enabled', () => {
        featureFlagsService.isFeatureFlagEnabled.and.returnValue(true);
        expect(service.isBehavioralOfferEnabled).toBeTrue();
    });

    it('should return false for isBehavioralOfferEnabled when feature flag is not enabled', () => {
        featureFlagsService.isFeatureFlagEnabled.and.returnValue(false);
        expect(service.isBehavioralOfferEnabled).toBeFalse();
    });

    it('should return true for isBehavioralChannelSelected when selected channel is behavioral action code', () => {
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_ACTION_CODE);
        expect(service.isBehavioralChannelSelected).toBeTrue();
    });

    it('should return false for isBehavioralChannelSelected when selected channel is not behavioral action code', () => {
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue('Other Code');
        expect(service.isBehavioralChannelSelected).toBeFalse();
    });

    it('should return true for isBehavioralContinutyEnabled when feature flag is enabled', () => {
        featureFlagsService.isBehavioralContinuityEnabled.and.returnValue(true);
    });

    it('should return false for isBehavioralContinutyEnabled when feature flag is not enabled', () => {
        featureFlagsService.isBehavioralContinuityEnabled.and.returnValue(false);
    });

    it('should return true for isBehavioralContinutyChannelSelected when selected channel is behavioral continuity code', () => {
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);
        expect(service.isBehavioralContinutyChannelSelected).toBeTrue();
    });

    it('should return false for isBehavioralContinutyChannelSelected when selected channel is not behavioral continuity code', () => {
        spyOnProperty(service, 'selectedChannelForSC', 'get').and.returnValue('Other Code');
        expect(service.isBehavioralContinutyChannelSelected).toBeFalse();
    });

    it('should return true for isBehavioralContinuityEnabledAndSelectedBAC when conditions are met', () => {
        featureFlagsService.isBehavioralContinuityEnabled.and.returnValue(true);
        spyOnProperty(service, 'selectedChannelForSPD', 'get').and.returnValue(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);
    });

    it('should return false for isBehavioralContinuityEnabledAndSelectedBAC when conditions are not met', () => {
        featureFlagsService.isBehavioralContinuityEnabled.and.returnValue(false);
        spyOnProperty(service, 'selectedChannelForSPD', 'get').and.returnValue('Other Code');
        expect(service.isBehavioralContinuityEnabledAndSelectedBAC).toBeFalse();
    });
    xit('should return true for isGRAirmilesPrograTypeSelected when conditions are met', () => {
        spyOn(service.featureFlagsService, 'isGRGetMilesEnabled').and.returnValue(true);
        Object.defineProperty(service.facetItemService, 'programCodeSelected', { get: () => CONSTANTS.GR });
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl(CONSTANTS.GR_AUTO_REWARD_OFFER_TYPES.CUSTOM),
                subProgramCode: new UntypedFormControl('Base'),
                programType: new UntypedFormControl(CONSTANTS.ALASKA_AIRMILES)
            })
        });
        expect(service.isGRAirmilesPrograTypeSelected).toBeTrue();
    });

    xit('should return false for isGRAirmilesPrograTypeSelected when isGRGetMilesEnabled is false', () => {
        service.featureFlagsService.isGRGetMilesEnabled.and.returnValue(false);
        spyOnProperty(service.facetItemService, 'programCodeSelected', 'get').and.returnValue(CONSTANTS.GR);
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl(CONSTANTS.GR_AUTO_REWARD_OFFER_TYPES.CUSTOM),
                subProgramCode: new UntypedFormControl('Base'),
                programType: new UntypedFormControl(CONSTANTS.ALASKA_AIRMILES)
            })
        });
        expect(service.isGRAirmilesPrograTypeSelected).toBeFalse();
    });

    xit('should return false for isGRAirmilesPrograTypeSelected when programCodeSelected is not GR', () => {
        spyOnProperty(service.facetItemService, 'programCodeSelected', 'get').and.returnValue('Other Code');
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl(CONSTANTS.GR_AUTO_REWARD_OFFER_TYPES.CUSTOM),
                subProgramCode: new UntypedFormControl('Base'),
                programType: new UntypedFormControl(CONSTANTS.ALASKA_AIRMILES)
            })
        });
        expect(service.isGRAirmilesPrograTypeSelected).toBeFalse();
    });

    xit('should return false for isGRAirmilesPrograTypeSelected when subProgramCode is not Base', () => {
        spyOnProperty(service.facetItemService, 'programCodeSelected', 'get').and.returnValue(CONSTANTS.GR);
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl(CONSTANTS.GR_AUTO_REWARD_OFFER_TYPES.CUSTOM),
                subProgramCode: new UntypedFormControl('Other SubProgram'),
                programType: new UntypedFormControl(CONSTANTS.ALASKA_AIRMILES)
            })
        });
        expect(service.isGRAirmilesPrograTypeSelected).toBeFalse();
    });

    xit('should return false for isGRAirmilesPrograTypeSelected when programType is not ALASKA_AIRMILES', () => {
        spyOnProperty(service.facetItemService, 'programCodeSelected', 'get').and.returnValue(CONSTANTS.GR);
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl(CONSTANTS.GR_AUTO_REWARD_OFFER_TYPES.CUSTOM),
                subProgramCode: new UntypedFormControl('Base'),
                programType: new UntypedFormControl('Other ProgramType')
            })
        });
        expect(service.isGRAirmilesPrograTypeSelected).toBeFalse();
    });

    xit('should return false for isGRAirmilesPrograTypeSelected when type is not CUSTOM', () => {
        spyOnProperty(service.facetItemService, 'programCodeSelected', 'get').and.returnValue(CONSTANTS.GR);
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Other Type'),
                subProgramCode: new UntypedFormControl('Base'),
                programType: new UntypedFormControl(CONSTANTS.ALASKA_AIRMILES)
            })
        });
    });
    xit('should set category ID for "Any Product" PG', () => {
        const data: { name: string; categoryId?: string } = { name: ANY_PRODUCT, categoryId: '' };
        service.setCategoryId(data);
        expect(data.categoryId).toBe(CONSTANTS.SPECIAL_OFFERS_CATEGORY_ID);
    });

    xit('should set category ID for specific offer types', () => {
        const data: { name: string; categoryId?: string } = { name: 'Some Product', categoryId: '' };
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Continuity')
            })
        });
        service.setCategoryId(data);
        expect(data.categoryId).toBe(CONSTANTS.SPECIAL_OFFERS_CATEGORY_ID);
    });

    it('should set category ID for multiple product rows', () => {
        const data: { name: string; categoryId?: string } = { name: 'Some Product', categoryId: '' };
        Object.defineProperty(service, 'value', {
            value: { offerRequestOffers: [{ storeGroupVersion: { productGroupVersions: [{}, {}] } }] },
            writable: true
        });
        service.setCategoryId(data);
        expect(data.categoryId).toBe(CONSTANTS.SPECIAL_OFFERS_CATEGORY_ID);
    });

    it('should set category ID for single product row on remove event', () => {
        const data: { name: string; isRemoveRowEvent: boolean; categoryId?: string } = { name: 'Some Product', isRemoveRowEvent: true };
        Object.defineProperty(service, 'value', {
            value: { offerRequestOffers: [{ storeGroupVersion: { productGroupVersions: [{ productGroup: { categoryId: '123' } }] } }] },
            writable: true
        });
        service.setCategoryId(data);
        expect(data.categoryId).toBe('123');
    });

    it('should return true for isOfferTypeValidForDerivedCategory when program code is in allowed list', () => {
        Object.defineProperty(service.facetItemService, 'programCodeSelected', { get: () => CONSTANTS.GR });
        expect(service.isOfferTypeValidForDerivedCategory).toBeTrue();
    });

    it('should return false for isOfferTypeValidForDerivedCategory when program code is not in allowed list', () => {
        Object.defineProperty(service.facetItemService, 'programCodeSelected', { get: () => 'Other Code' });
    });

    it('should return true for isOfferTypeValidForDerivedCategory when template route is activated', () => {
        expect(service.isOfferTypeValidForDerivedCategory).toBeTrue();
    });
    
    it('should update pod data for create phase', () => {
        const offerRequestOffers = [{ storeGroupVersion: {} }];
        const obj = { offerRequestOffers };
        spyOn(service, 'passCategoryValue').and.callFake(() => {
            return { podDetails: { shoppingListCategory: '123', leftNavCategory: ['123'] } };
        });

        const result = service.updatePodData(obj);
    });

    it('should update pod data for edit phase when offer type is changed', () => {
        const offerRequestOffers = [{ storeGroupVersion: { podDetails: { shoppingListCategory: '456' } } }];
        const obj = { offerRequestOffers };
        service.isOfferTypeChanged = true;
        spyOn(service, 'passCategoryValue').and.returnValue({ podDetails: { shoppingListCategory: '123', leftNavCategory: ['123'] } });

        const result = service.updatePodData(obj);

        expect(result).toEqual({ podDetails: { shoppingListCategory: '123', leftNavCategory: ['123'] } });
    });

    it('should update pod data for edit phase when offer type is not changed', () => {
        const offerRequestOffers = [{ storeGroupVersion: { podDetails: { shoppingListCategory: '456' } } }];
        const obj = { offerRequestOffers };
        service.isOfferTypeChanged = false;
        spyOn(service, 'passCategoryValue').and.returnValue({ podDetails: { shoppingListCategory: '456', leftNavCategory: ['456'] } });

        const result = service.updatePodData(obj);

        expect(result).toEqual({ podDetails: { shoppingListCategory: '456' } });
    });

    it('should get rules for template', () => {
        const rules = service.getRulesForTemplate();
        expect(rules).toEqual(BPD_TEMPLATE_RULES['Item Discount']);
    });

    it('should return false from fromPodSectionToSetCategory when categoryId is not provided', () => {
        const data = { isPodSection: true, categoryId: null };
        const result = service.fromPodSectionToSetCategory(data);
    });

    it('should return pod details from fromPodSectionToSetCategory when categoryId is provided', () => {
        const data = { isPodSection: true, categoryId: '123' };
        const result = service.fromPodSectionToSetCategory(data);
        expect(result).toEqual({
            podDetails: {
                leftNavCategory: ['123'],
                shoppingListCategory: '123'
            }
        });
    });
    it('should return true for isAnyTierEmpty when any tier amount is null', () => {
        const tierValues = [{ amount: null }, { amount: 10 }];
        const result = service.isAnyTierEmpty(tierValues, 'amount');
        expect(result).toBeTrue();
    });

    it('should return true for isAnyTierEmpty when any tier amount is empty string', () => {
        const tierValues = [{ amount: '' }, { amount: 10 }];
        const result = service.isAnyTierEmpty(tierValues, 'amount');
        expect(result).toBeTrue();
    });

    it('should return false for isAnyTierEmpty when all tier amounts are filled', () => {
        const tierValues = [{ amount: 5 }, { amount: 10 }];
        const result = service.isAnyTierEmpty(tierValues, 'amount');
        expect(result).toBeFalse();
    });

    it('should return true for isAnyTierEmpty when any tier rewards is null', () => {
        const tierValues = [{ rewards: null }, { rewards: 10 }];
        const result = service.isAnyTierEmpty(tierValues, 'rewards');
        expect(result).toBeTrue();
    });

    it('should return true for isAnyTierEmpty when any tier rewards is empty string', () => {
        const tierValues = [{ rewards: '' }, { rewards: 10 }];
        const result = service.isAnyTierEmpty(tierValues, 'rewards');
        expect(result).toBeTrue();
    });

    it('should return false for isAnyTierEmpty when all tier rewards are filled', () => {
        const tierValues = [{ rewards: 5 }, { rewards: 10 }];
        const result = service.isAnyTierEmpty(tierValues, 'rewards');
    });

    it('should return rules for offer type when type is Custom and isGRAirmilesPrograTypeSelected is true', () => {
        spyOnProperty(service, 'isGRAirmilesPrograTypeSelected', 'get').and.returnValue(true);
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Custom')
            })
        });
        const rules = service.offerTypeRule;
    });

    it('should return rules for offer type when type is Custom and isGRAirmilesPrograTypeSelected is false', () => {
        spyOnProperty(service, 'isGRAirmilesPrograTypeSelected', 'get').and.returnValue(false);
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Custom')
            })
        });
        const rules = service.offerTypeRule;
    });

    it('should return rules for offer type when type is not Custom', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                type: new UntypedFormControl('Buy X Get 1')
            })
        });
        const rules = service.offerTypeRule;
    });

    it('should return rules for template when template route is activated', () => {
        spyOnProperty(service, 'isTemplateRouteActivated', 'get').and.returnValue(true);
        const rules = service.offerTypeRule;
        expect(rules).toEqual(BPD_TEMPLATE_RULES['Item Discount']);
    });

    it('should return the product control', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                product: new UntypedFormControl('Test Product')
            })
        });
        const productControl = service.product;
        expect(productControl.value).toBe('Test Product');
    });

    it('should return the tiers control', () => {
        service.generalOfferTypeForm = new UntypedFormBuilder().group({
            generalInformationForm: new UntypedFormBuilder().group({
                tiers: new UntypedFormControl('Test Tiers')
            })
        });
        const tiersControl = service.tiers;
        expect(tiersControl.value).toBe('Test Tiers');
    });

    it('should return false for tierValidation when only one tier is present', () => {
        const tierValues = [{ level: 1, amount: 10 }];
        const result = service.tierValidation(tierValues, { level: 1, amount: 10 }, 0, 'amount');
        expect(result).toBeFalse();
    });

    it('should return false for tierValidation when any tier amount is empty', () => {
        const tierValues = [{ level: 1, amount: '' }, { level: 2, amount: 10 }];
        const result = service.tierValidation(tierValues, { level: 2, amount: 10 }, 1, 'amount');
        expect(result).toBeFalse();
    });

    it('should return true for tierValidation when current tier amount is less than previous tier amount', () => {
        const tierValues = [{ level: 1, amount: 10 }, { level: 2, amount: 5 }];
        const result = service.tierValidation(tierValues, { level: 2, amount: 5 }, 1, 'amount');
        expect(result).toBeTrue();
    });

    it('should return true for tierValidation when current tier rewards is less than previous tier rewards', () => {
        const tierValues = [{ level: 1, rewards: 10 }, { level: 2, rewards: 5 }];
        const result = service.tierValidation(tierValues, { level: 2, rewards: 5 }, 1, 'rewards');
    });

    it('should return false for tierValidation when current tier amount is greater than previous tier amount', () => {
        const tierValues = [{ level: 1, amount: 5 }, { level: 2, amount: 10 }];
        const result = service.tierValidation(tierValues, { level: 2, amount: 10 }, 1, 'amount');
        expect(result).toBeFalse();
    });

    it('should return false for tierValidation when current tier rewards is greater than previous tier rewards', () => {
        const tierValues = [{ level: 1, rewards: 5 }, { level: 2, rewards: 10 }];
        const result = service.tierValidation(tierValues, { level: 2, rewards: 10 }, 1, 'rewards');
        expect(result).toBeFalse();
    });

    it('should return true for tierValidation when current tier amount is greater than next tier amount', () => {
        const tierValues = [{ level: 1, amount: 10 }, { level: 2, amount: 5 }];
        const result = service.tierValidation(tierValues, { level: 1, amount: 10 }, 0, 'amount');
        expect(result).toBeTrue();
    });

    it('should return true for tierValidation when current tier rewards is greater than next tier rewards', () => {
        const tierValues = [{ level: 1, rewards: 10 }, { level: 2, rewards: 5 }];
        const result = service.tierValidation(tierValues, { level: 1, rewards: 10 }, 0, 'rewards');
    });

    it('should return false for tierValidation when current tier amount is less than next tier amount', () => {
        const tierValues = [{ level: 1, amount: 5 }, { level: 2, amount: 10 }];
        const result = service.tierValidation(tierValues, { level: 1, amount: 5 }, 0, 'amount');
        expect(result).toBeFalse();
    });

    it('should return false for tierValidation when current tier rewards is less than next tier rewards', () => {
        const tierValues = [{ level: 1, rewards: 5 }, { level: 2, rewards: 10 }];
        const result = service.tierValidation(tierValues, { level: 1, rewards: 5 }, 0, 'rewards');
        expect(result).toBeFalse();
    });
    
});
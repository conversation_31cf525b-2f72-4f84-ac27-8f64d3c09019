import { Injectable } from "@angular/core";
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from "@angular/forms";
import { CONSTANTS } from "@appConstants/constants";
import { BPD_OR_RULES, BPD_TEMPLATE_RULES, GR_OR_RULES, OR_RULES, SPD_OR_RULES } from "@appModules/request/shared/rules/OR.rules";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { DISCOUNT_RULES } from "@appShared/rules/DISCOUNT.rule";
import { nullCheckProperty } from "@appUtilities/nullCheck.utility";
import { UnsubscribeAdapter } from "@appUtilities/subscription/unsubscribe-adapter";
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Subject } from "rxjs";
import { CommonRouteService } from "../common/common-route.service";
import { InitialDataService } from "../common/initial.data.service";

@Injectable({
  providedIn: "root",
})

export class GeneralOfferTypeService extends UnsubscribeAdapter {
  generalOfferTypeForm: UntypedFormGroup;
  isReqSubmitAttempted: boolean;
  isDraftSaveAttemptedFlag: boolean;
  isDraftSaveAttempted: BehaviorSubject<boolean>;
  isReqSubmitAttempted$: BehaviorSubject<boolean>;
  totalAmountChangeClass$ = new BehaviorSubject("");
  totalStoresCount = 0;
  selectedBuyPg$ = new Subject(); //holds the selecteed pg in Buy section
  isViewCheckedInProdGroupVersions$ = new Subject(); //When ngAfterViewChecked  of product-group-versions.component.ts is called.
  isCopyVersion$ = new Subject(); //When copy version is clicked
  isOfferTypeChanged: boolean; //Tracks if Offer Type is changed, If changed, then the POD category fields were reset
  isNoDiscountSelectedForContinuty:boolean = false;
  isNoDiscountSelectedForContinuty$ = new Subject();
  isPointsDiscountSelectedForContinuty:boolean = false;
  data: any;
  amountTypes: any;
  offerTypes: any;
  addedRowlabel: any;
  offerTypeRules: any;
  rules:any = OR_RULES;
  offerRequestOffersData: any;
  appData; 
  getValueByKey: any;
  isCustomType: boolean = false;
  productComponent: any = [];
  productChanged: boolean = false;
  discountChangeTriggerdObj = [];
  addNewRow$: any = new BehaviorSubject("");
  benefitTypeChangedVersionIndex;
  quantityUnitTypeChange$: any = new BehaviorSubject("");
  benefitOptions = [];
  offerRequestData: any;
  cloneOfferRequestData: any;
  requestFormService: any;
  offerRequestOffersValue: any;
  isCategoryUserUpdated: boolean; //If the shopping list category in POD section is updated/saved by user, dont override it with derived category from the Prodcuct group and Offer types


  constructor(public facetItemService:FacetItemService,private _initialDataService: InitialDataService, public formBuilder: UntypedFormBuilder, private toastr: ToastrService, public commonRouteService:CommonRouteService,public featureFlagsService:FeatureFlagsService ) {
    super();
    this.appData = this._initialDataService.getAppData();
  }

  get isTemplateRouteActivated() {
    return this.commonRouteService.currentActivatedRoute?.includes('template');
  }

  initOfferSubmitSubscriber() {
    this.subs.sink = this.isReqSubmitAttempted$.subscribe((value) => {
      this.isReqSubmitAttempted = value;
    });
    this.subs.sink = this.isDraftSaveAttempted.subscribe((value) => {
      this.isDraftSaveAttemptedFlag = value;
    });
  }

  hideDropdown() {
    //When creating new group, hide the dropdown
    const dropDownElem = document.querySelector(".ng-select-typeahead .ng-dropdown-panel");
    if (dropDownElem) {
      dropDownElem.setAttribute("style", "opacity:0 !important;");
    }
  }

  addFormControl(name, formControl, form:UntypedFormGroup) {
    form.addControl(name, formControl);
  }

  validateField(control) {
    //Errors for save
    if (this.isDraftSaveAttemptedFlag && !this.isReqSubmitAttempted && control && control.untouched) {
      let errors = null;
      if (control.errors && !control.errors.required) {
        errors = control.errors;
      }
      return errors;
    } else if (this.isReqSubmitAttempted && control && control.untouched) {
      //Errors on submit
      return control.errors;
    } else {
      return null;
    }
  }

  removeFormControl(name, form) {
    form.removeControl(name);
  }
  createForm() {
   this.createQualificationAndBenefitControl();
  
    this.generalOfferTypeForm = this.formBuilder.group({
      'offerRequestOffers': this.formBuilder.array([]),
    });
 
  }
  get isBuyXGet1OfferType() {
    return this.type.value === "Buy X Get 1";
  }
  createQualificationAndBenefitControl(){
    const requestForm = this.requestFormService.requestForm as UntypedFormGroup;
    let rules = requestForm.get('rules') as UntypedFormGroup;
    if(!rules){
      this.addFormControl('rules',new UntypedFormGroup({}),requestForm);
      rules = requestForm.get('rules') as UntypedFormGroup;
    }
    let qualificationAndBenefit = rules.get('qualificationAndBenefit') as UntypedFormGroup;
    if(!qualificationAndBenefit){
      this.addFormControl('qualificationAndBenefit',new UntypedFormGroup({}),rules);
      qualificationAndBenefit = rules.get('qualificationAndBenefit') as UntypedFormGroup;

    }
    return qualificationAndBenefit;
  }
  checkIfAnyOfferIsDeployed(obj){
    const {offerRequestOffersData, productGroupVersions, productVersionIndex} = obj;
    const offers = offerRequestOffersData.offers;
    let statusArray = [], isOfferDeployed = false;
    offers && offers.forEach((offer) => {
      if (offer.productGroupVersion == productGroupVersions.value[productVersionIndex].id) {
        statusArray.push(offer.offerStatus)
      }
    })
    if (["DE", "CN", "P", "PU"].some(status => statusArray.includes(status))) {
      isOfferDeployed = true;
    }    

    return isOfferDeployed;
  }

  showDeleteToastrMsg(deletedItem) {
    deletedItem && this.toastr.success(`${deletedItem} Deleted`, '', {
      timeOut: 500,
      closeButton: true
    });
  }
  showRemoveErrorToastrMsg() {
    this.toastr.error(`You cannot delete a row tied to a deployed offer.`, '', {
      timeOut: 500,
      closeButton: true
    });
  }

  formControlName(name) {
    return this.formControls[name];
  }
  getFormControlName(control, name) {
    return control[name];
  }
  createFormControl(formControls: any = []): UntypedFormGroup {
    const formControl = {};
    formControls.forEach((control) => {
      /*Err Info: This condition will always return 'true' since JavaScript compares objects by reference, not value.
        Means else condition never ran in its life time so for now removing the condiotion
      */
      //if(toString.call(control.value) !== ["object Array"]){
        formControl[control.name] = new UntypedFormControl(control.value, control.validate ? Validators.required : null);
        //Mark the new control as touched, so that validation errors wont be displayed
        formControl[control.name].markAsTouched();
      // } else {
      //   formControl[control.name] = this.createFormControl(control.value);
      // }
    });
    return this.formBuilder.group(formControl);
  }
  createControlObject(name, value, validate) {
    return { name, value, validate };
  }
  get formControls() {
    return this.generalOfferTypeForm.controls;
  }
  get form() {
    return this.generalOfferTypeForm;
  }
  get value() {
    return this.generalOfferTypeForm.value;
  }
  get valid() {
    return this.generalOfferTypeForm.valid;
  }

  getKeyByValue(object, value) {
    let property = null;
    if (!object) {
      object = this.appData?.offerType;
    }
    for (let prop in object) {
      if (object.hasOwnProperty(prop)) {
        if (object[prop] === value) property = prop;
      }
    }
    return property;
  }

  get isBehavioralWithRewardsFlat() {
    return this.isBehavioralOfferEnabled && this.type.value === 'Rewards - Flat' &&
      [this.selectedChannelForSC, this.selectedChannelForSPD].includes(CONSTANTS.BEHAVIORAL_ACTION_CODE);
  }
  get isBehavioralContinutyWithRewardsFlat() {
    return this.isBehavioralContinutyEnabled && this.type.value === 'Rewards - Flat' &&
      [this.selectedChannelForSC, this.selectedChannelForSPD].includes(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);
  }
  get isBehavioralOfferEnabled() {
    return this.featureFlagsService.isFeatureFlagEnabled("enableBehavioralOffers");
  }
  get isBehavioralChannelSelected() {
   return  [this.selectedChannelForSC, this.selectedChannelForSPD].includes(CONSTANTS.BEHAVIORAL_ACTION_CODE);
  }
  get isBehavioralContinutyEnabled() {
    return this.featureFlagsService.isBehavioralContinuityEnabled;
  }
  get isBehavioralContinutyChannelSelected() {
   return  [this.selectedChannelForSC, this.selectedChannelForSPD].includes(CONSTANTS.BEHAVIORAL_CONTINUTY_CODE);
  }
  get selectedChannelForSC() {
    return this.requestFormService?.requestForm?.get('offerReqGroup')?.get('deliveryChannel').value
  }
  get selectedChannelForSPD() {
    return this.requestFormService?.offerRequestBaseService?.requestForm?.get('info')?.get('deliveryChannel')?.value;
  }
  get isBehavioralContinuityEnabledAndSelectedBAC() {
    const selectedChannel = this.requestFormService?.offerRequestBaseService?.requestForm?.get('info')?.get('deliveryChannel')?.value;
    return this.featureFlagsService.isBehavioralContinuityEnabled && selectedChannel === CONSTANTS.BEHAVIORAL_CONTINUTY_CODE;
  }
  get isGRAirmilesPrograTypeSelected(){
    return (this.featureFlagsService.isGRGetMilesEnabled
      && this.facetItemService.programCodeSelected === CONSTANTS.GR 
      && this.requestFormService?.offerRequestBaseService?.requestForm?.get('info')?.get('subProgramCode').value === "Base"
      && this.requestFormService?.offerRequestBaseService?.requestForm?.get('info')?.get('programType').value == CONSTANTS.ALASKA_AIRMILES
      && this.type.value === CONSTANTS.GR_AUTO_REWARD_OFFER_TYPES.CUSTOM
      );
  }
  getValidatorsBasedOnAction(obj) {
    let { minValue } = obj,
      validatorsArr = [Validators.min(minValue)];
    if (this.isReqSubmitAttempted) {
      //if submitted

      validatorsArr = [Validators.required, ...validatorsArr];
    }
    return validatorsArr;
  }

  setCategoryId(data){
    
      let  offersWithSplOffersCategory = 
        ["Continuity", "Custom", "Rewards - Flat","Rewards - Accumulation", "Buy X Get Y", "Alaska Airmiles", "Bundle", "Item + Basket"],
        selectedOfferType = this.type.value,
        productGroupVersions = this.value?.offerRequestOffers[0]?.storeGroupVersion?.productGroupVersions,
        productRowsLength = productGroupVersions?.length;
        
         //In case of remove row event, & if only single row exists
        if(productRowsLength === 1 && data.isRemoveRowEvent){
          data.categoryId = productGroupVersions[0]?.productGroup?.categoryId;
        }
      
        //For "Any Product" PG or offertypes from the above array or if there are mulitple rows- set the category to “Special Offers”
        if(data.name === CONSTANTS.ANY_PRODUCT || offersWithSplOffersCategory.indexOf(selectedOfferType) > -1 || productRowsLength > 1){
          data.categoryId = CONSTANTS.SPECIAL_OFFERS_CATEGORY_ID;
        }
  }

  get isOfferTypeValidForDerivedCategory(){
    //POD category values should be derived only for certain offertypes
  let  programCode = this.facetItemService.programCodeSelected, 
       allowedProgramCodes = [CONSTANTS.GR, CONSTANTS.SPD, CONSTANTS.BPD],
       isOfferTypeValidForDerivedCategory = false;

    if (allowedProgramCodes.indexOf(programCode) > -1 || this.isTemplateRouteActivated) {
      isOfferTypeValidForDerivedCategory = true;
    }
    return isOfferTypeValidForDerivedCategory;
  }

  passCategoryValue(data){
    /*GR/SPD: category in POD section to be derived when a "Buy" Product Group  or offer type
     is selected or multiple product group rows were added*/
    
    const {isPodSection } = data;

    this.requestFormService.getCategoryData$.next(data)
    if (!this.isOfferTypeValidForDerivedCategory) {
      return false;
    }
    
    this.setCategoryId(data);

    //If the calee of this method is pod section
    if(isPodSection){
       return this.fromPodSectionToSetCategory(data);
    }
    
    this.requestFormService.setCategoryValue$.next(data.categoryId)
  }

  fromPodSectionToSetCategory(data){
    //If in edit/copy request usecase if shoppingListCategory is already available, then dont override the category fields with derived values
      /**
       * Commenting below code as while edit/copy also, when OT changes or new row added,we need to set the derived rules
       */
       
    //resetting
    this.isCategoryUserUpdated = true;

   if(!data.categoryId){
      return false;
    } 

    //When offer type is changed, create pod form with default values
    return  {
      podDetails:{
        leftNavCategory:[data.categoryId],
        shoppingListCategory:data.categoryId
      }
    }
  }

  updatePodData(obj){
    const {offerRequestOffers} = obj;
    let  storeGroupVersionData =  offerRequestOffers[0].storeGroupVersion, 
         existedShoppingListCategory = storeGroupVersionData?.podDetails?.shoppingListCategory,
         newFormControlsDataobj:any = this.passCategoryValue({isPodSection:true,existedShoppingListCategory  }) || {};

    if(storeGroupVersionData) {
      // Edit case
      if(this.isOfferTypeChanged && existedShoppingListCategory) {
        // if user manually changes offer type then reset the category based on derived rules
        this.isOfferTypeChanged = false;
        const isNewCategoryAvailable = nullCheckProperty(newFormControlsDataobj.podDetails, "shoppingListCategory");
        let newShoppingCategory = "", newLeftNavCategory = [];
        if(isNewCategoryAvailable) {
          const {podDetails: {shoppingListCategory , leftNavCategory}} = newFormControlsDataobj;
          newShoppingCategory = shoppingListCategory; newLeftNavCategory = leftNavCategory;
          storeGroupVersionData = {...storeGroupVersionData, podDetails: {...storeGroupVersionData["podDetails"], shoppingListCategory: newShoppingCategory, leftNavCategory: newLeftNavCategory} };
        } 
        return storeGroupVersionData;
      } else {
        // if in edit case page reloads,set the category coming from BE
        return storeGroupVersionData;
      }
    } else {
      // In case of create phase
      return newFormControlsDataobj;
    }
  }
  getRulesForTemplate(){
    this.rules = JSON.parse(JSON.stringify(BPD_TEMPLATE_RULES));
    return this.rules['Item Discount'];
  }

  getRulesForOfferType(offerType) {
    //TO DO: sometimes the value of type is coming as _ or normal
    const programCode = this.facetItemService.programCodeSelected;
    this.rules = JSON.parse(JSON.stringify(OR_RULES));
    if(programCode=== CONSTANTS.GR){
      this.rules = JSON.parse(JSON.stringify(GR_OR_RULES));
    } else if(programCode=== CONSTANTS.SPD) {
      this.rules = JSON.parse(JSON.stringify(SPD_OR_RULES));
    } else if(this.commonRouteService.isBpdReqPage) {
      this.rules = JSON.parse(JSON.stringify(BPD_OR_RULES));
    } 
    return this.rules[offerType];
  }
  get offerTypeRule() {
    //TO DO: sometimes the value of type is coming as _ or normal
    let typeVal = this.type && this.type.value;
    if (typeVal === "Custom") {
      if(this.isGRAirmilesPrograTypeSelected)
        this.isCustomType = false;
      else
        this.isCustomType = true;
    } else {
      this.isCustomType = false;
    }
    if(this.isTemplateRouteActivated){
      return this.getRulesForTemplate();
    }
    return this.getRulesForOfferType(typeVal);
  }
  get generalInformationForm() {
    return this.formControls.generalInformationForm;
  }
  get type() {
    return this.generalInformationForm && this.generalInformationForm.get("type");
  }
  get product() {
    return this.generalInformationForm && this.generalInformationForm.get("product");
  }
  get tiers() {
    return this.generalInformationForm && this.generalInformationForm.get("tiers");
  }

  getDiscountHeaders(discounts) {
    const obj = {
      Amt: this.showDiscountLabel(discounts, "Amt"),
      UpTo: this.showDiscountLabel(discounts, "UpTo"),
      ItemLimit: this.showDiscountLabel(discounts, "ItemLimit"),
      PerLbLimit: this.showDiscountLabel(discounts, "PerLbLimit"),
      Points: this.showDiscountLabel(discounts, "Points"),
      rewards: this.showDiscountLabel(discounts, "rewards"),
      miles: this.showDiscountLabel(discounts, "miles"),
    };
    return obj;
  }
  showDiscountLabel(discounts, label) {
    const filterList = discounts
      .reduce((output, ele) => {
        output.push(ele[label]);
        return output;
      }, [])
      .filter((ele) => ele);
    return filterList.length > 0;
  }
  getDiscountRule(label) {
    return DISCOUNT_RULES[label];
  }
  getProductGroupVersionDiscount(version) {
    let productGroupVersions = this.value.offerRequestOffers[version]?.storeGroupVersion?.productGroupVersions||[];
    return productGroupVersions.reduce((output, element) => {
      output = [
        ...output,
        ...(element.discountVersion
          ? element.discountVersion.discounts.reduce((output, ele) => {
            output.push(this.amountTypes[ele.benefitValueType]);
            return output;
          }, [])
          : []),
      ];
      return output;
    }, []);
  }
  displayOrderSorting(list) {
    return list.sort((oArr, nArr) => {
      return oArr.displayOrder > nArr.displayOrder ? 1 : -1;
    });
  }
  displayOrderLevelSorting(list) {
    return list.sort((oArr, nArr) => {
      return oArr.level > nArr.level ? 1 : -1;
    });
  }
  displayOrderForStoreGroupVersion(list) {
    return list && list.sort((oArr, nArr) => {
      return oArr.storeGroupVersion.displayOrder > nArr.storeGroupVersion.displayOrder ? 1 : -1;
    });
  }
  isAnyTierEmpty(tierValues,product) {
    return tierValues.some((tier) => {
      return this.type.value==="Rewards - Flat" && product==='rewards'?(tier.rewards == null||tier.rewards == ""):(tier.amount == null || tier.amount == "");
    });
  }
  getDropDownList(values,ids, countValue = null){
    if(values && ids){
      return ids.reduce((output,name,index)=>{
        const obj = countValue ? {id:name,name: values[index], count: countValue[index]} : {id:name,name: values[index]};
        output.push(obj);
        return output;
      },[]);
    }
  }
  getDropDown(name,id){
    if(name && id){
      return [{name,id}];
    }
  }



  tierValidation(tierValues, tierElement, productVersionIndex,product = "") {
    //If only one tier or if all ther tiers were not filled
    if ((tierValues && tierValues.length === 1) || this.isAnyTierEmpty(tierValues,product)) {
      return false;
    }
    const valOnLeft = tierValues && tierValues.length && tierValues.filter((element) => element.level < tierElement.level);
    const valOnRight = tierValues && tierValues.length && tierValues.filter((element) => element.level > tierElement.level);
    const currentControlTierVal = tierValues && tierValues.length && tierValues.filter((element) => element.level === tierElement.level);

    let tierError = false;

    //Check if the current field value is valid wrt to its preeceeding  tier
    if (valOnLeft && valOnLeft.length) {
      const invalidSideTier = valOnLeft.filter((element) => {
        const valid = this.type.value==="Rewards - Flat" && product==='rewards'?(element.rewards == null|| element.rewards == ""):(element.amount == null||element.amount == "");
        if (valid) {
          return false;
        }

        return this.type.value==="Rewards - Flat" && product==='rewards'?currentControlTierVal[0].rewards && +currentControlTierVal[0].rewards <= element.rewards:currentControlTierVal[0].amount && +currentControlTierVal[0].amount <= element.amount;
      });
      tierError = invalidSideTier && invalidSideTier.length ? true : false;
      if (tierElement.level - 1 === productVersionIndex) {
        if (tierError) {
          return true;
        }
      }
    }

    if (!tierError) {
      //Check if the current field value is valid wrt to its proceeding  tier
      if (valOnRight && valOnRight.length) {
        //Get Invalid object if current field is > than proceeding tier
        const invalidSideTier = valOnRight.filter((element) => {
          const valid = this.type.value==="Rewards - Flat" && product==='rewards'?(element.rewards == null|| element.rewards == ""):(element.amount == null||element.amount == "");
           if (valid) {
            return false;
           }
          return this.type.value==="Rewards - Flat" && product==='rewards'?currentControlTierVal[0].rewards && +currentControlTierVal[0].rewards >= element.rewards:currentControlTierVal[0].amount && +currentControlTierVal[0].amount >= element.amount;
        });
        tierError = invalidSideTier && invalidSideTier.length ? true : false;

        if (tierElement.level - 1 === productVersionIndex) {
          if (tierError) {
            return true;
          }
        }
      }
    }
    return false;
  }
  get isEcommOnlyEnabled() {
    return this.featureFlagsService.isFeatureFlagEnabled(CONSTANTS.ENABLE_ECOM_PROMO_CODE) || this.featureFlagsService.isFeatureFlagEnabled(CONSTANTS.ENABLE_SCHEDULE_AND_SAVE);
  }
}

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MobService } from './mob.service';
import { AuthService } from '../common/auth.service';
import { InitialDataService } from '../common/initial.data.service';
import { CONSTANTS } from '@appConstants/constants';

describe('MobService', () => {
    let service: MobService;
    let httpMock: HttpTestingController;
    let authService: AuthService;
    let initialDataService: InitialDataService;

    beforeEach(() => {
        const authServiceMock = {
            getTokenString: jasmine.createSpy('getTokenString').and.returnValue('mockToken')
        };

        const initialDataServiceMock = {
            getConfigUrls: jasmine.createSpy('getConfigUrls').and.callFake((url) => url)
        };

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                MobService,
                { provide: AuthService, useValue: authServiceMock },
                { provide: InitialDataService, useValue: initialDataServiceMock }
            ]
        });

        service = TestBed.inject(MobService);
        httpMock = TestBed.inject(HttpTestingController);
        authService = TestBed.inject(AuthService);
        initialDataService = TestBed.inject(InitialDataService);
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should create mob', () => {
        const mockDetails = { mobType: 'type1', mobName: 'name1' };
        const mockResponse = { success: true };

        service.createMob(mockDetails).subscribe(response => {
            expect(response).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(service.mobCreateAndEdit_API);
        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual({
            reqObj: { headers: service.getHeaders() },
            mobType: mockDetails.mobType,
            mobName: mockDetails.mobName
        });
        req.flush(mockResponse);
    });

    it('should update mob keys', () => {
        const mockObj = { id: 1, exactSearch: true, lastUpdateTs: '2023-10-01', mobName: 'name1' };
        service.updateMobKeys(mockObj);
        expect(service.mobKeys).toEqual(mockObj);
    });

    it('should update mob', () => {
        const mockDetails = { mobType: 'type1', mobName: 'name1' };
        const mockResponse = { success: true };

        service.updateMob(mockDetails).subscribe(response => {
            expect(response).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(service.mobEditAndUpdate_API);
        expect(req.request.method).toBe('PUT');
        expect(req.request.body).toEqual([{ ...mockDetails, ...service.mobKeys }]);
        req.flush(mockResponse);
    });

    it('should search mob', () => {
        const mockDetails = { searchQuery: 'query' };
        const exactSearch = true;
        const mockResponse = { results: [] };

        service.searchMob(mockDetails, exactSearch).subscribe(response => {
            expect(response).toEqual(mockResponse);
        });

        const req = httpMock.expectOne(service.mobSearch_API);
        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual({
            reqObj: { headers: service.getHeaders() },
            searchQuery: mockDetails,
            exactSearch: exactSearch
        });
        req.flush(mockResponse);
    });

    it('should get headers', () => {
        const headers = service.getHeaders();
        expect(headers).toEqual({
            ...CONSTANTS.HTTP_HEADERS,
            'X-Albertsons-userAttributes': 'mockToken'
        });
    });
});
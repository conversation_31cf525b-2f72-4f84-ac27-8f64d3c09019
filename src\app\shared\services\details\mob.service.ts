import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { AuthService } from '../common/auth.service';
import { InitialDataService } from '../common/initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class MobService {
  mobKeys: any = {
    id: null,
    exactSearch: null,
    lastUpdateTs: null,
    mobName: null
  };
  mobCreateAndEdit_API: string = this._initialDataService.getConfigUrls(CONSTANTS.MOB_CREATE_RE_SUBMIT_API);
  mobEditAndUpdate_API: string = this._initialDataService.getConfigUrls(CONSTANTS.MOB_EDIT_RE_UPDATE_API);
  mobSearch_API: string = this._initialDataService.getConfigUrls(CONSTANTS.MOB_SEARCH_API);
  constructor(
    private _http: HttpClient,
    private _initialDataService: InitialDataService,
    private authService: AuthService
  ) { 
    // intentionally left empty
  }

  public createMob(details) {
    let searchInput = {
      reqObj: { headers: this.getHeaders() },
      mobType:details.mobType,
      mobName:details.mobName
    };
    return this._http.post(this.mobCreateAndEdit_API,searchInput);
  }

  
  public updateMobKeys(obj){
      this.mobKeys = { 
          id: obj.id? obj.id: null,
          exactSearch: obj.exactSearch ? obj.exactSearch: false,
          lastUpdateTs: obj.lastUpdateTs ? obj.lastUpdateTs: null,
          mobName: obj.mobName ? obj.mobName: ""
      }
  }

  public updateMob(details){
    const reqBody = {
        ...details , ...this.mobKeys
    };
    const reqArray = [{...reqBody}];
    return this._http.put(this.mobEditAndUpdate_API,reqArray,{ headers: this.getHeaders() });
  }

  public searchMob(details,exactSearch){
    let searchInput = {
      reqObj: { headers: this.getHeaders()},
      searchQuery: details,
      // mobType: "NB_MOB",
      exactSearch: exactSearch
    };
    return this._http.post(this.mobSearch_API,searchInput);
  }

  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      'X-Albertsons-userAttributes': this.authService.getTokenString(),
    };
  }

}

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { PodDetailsService } from './pod-details.service';

describe('PodDetailsService', () => {
    let service: PodDetailsService;
    let httpMock: HttpTestingController;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [PodDetailsService]
        });
        service = TestBed.inject(PodDetailsService);
        httpMock = TestBed.inject(HttpTestingController);
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should set and get disclaimer', () => {
        let disclaimerValue: any;
        const disclaimer = { text: 'Test Disclaimer' };

        service.getDisclaimer().subscribe(value => {
            disclaimerValue = value;
        });

        service.setDisclaimer(disclaimer);
        expect(disclaimerValue).toEqual(disclaimer);
    });

    it('should fetch disclaimers from API', () => {
        const mockDisclaimers = [{ id: 1, text: 'Disclaimer 1' }, { id: 2, text: 'Disclaimer 2' }];

        service.getDisclaimers().subscribe(disclaimers => {
            expect(disclaimers).toEqual(mockDisclaimers);
        });

        const req = httpMock.expectOne(service.endPoint);
        expect(req.request.method).toBe('GET');
        req.flush(mockDisclaimers);
    });
});
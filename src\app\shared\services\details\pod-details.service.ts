import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PodDetailsService {

  constructor(private http: HttpClient) { 
    // intentionally left empty
  }

  disclaimer = new Subject<any>();
  endPoint = 'https://emju-offers-dev.apps.np.stratus.albertsons.com/api/offers/request/ui/config/offerDisclaimers';

  setDisclaimer(disclaimer) {
    this.disclaimer.next(disclaimer);
  }

  getDisclaimer(): Observable<any> {
    return this.disclaimer.asObservable();
  }

  getDisclaimers() {
    return this.http.get(this.endPoint);
  }

}

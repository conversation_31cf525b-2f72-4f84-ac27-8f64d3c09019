import { TestBed } from "@angular/core/testing";
import { TimeMethodsService } from "./time-methods.service";
import { FormControl, FormGroup } from "@angular/forms";

describe('TimeMethodsService', () => {
    let service: TimeMethodsService;
    let formGroup: FormGroup;
    let fieldName: string;

    beforeEach(() => {
        TestBed.configureTestingModule({})
        service = TestBed.inject(TimeMethodsService);
        formGroup = new FormGroup({
            'time': new FormControl('')
        });
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('onHrsInput', () => {
        it('should set form control value to empty string if control value is 0', () => {
            service.onHrsInput(0, formGroup, 'time');
            expect(formGroup.controls['time'].value).toEqual('')
        });
        
        it('should set form control value less than 1', () => {
            service.onHrsInput(13, formGroup, 'time');
            expect(formGroup.controls['time'].value).toEqual(1);
        });

        it('should set form control value greater than 12', () => {
            service.onHrsInput(0.5, formGroup, 'time');
            expect(formGroup.controls['time'].value).toEqual(0);
        });

        it('should set form control value from 1 to 12', () => {
            for (let i = 1; i <= 12; i++) {
                service.onHrsInput(i, formGroup, 'time');
                expect(formGroup.controls['time'].value).toBe('')
            }
        });

        it('should set form control value non-numeric value', () => {
            service.onHrsInput('Abc', formGroup, 'time');
            expect(formGroup.controls['time'].value).toBe('');
        });
    });

    describe('onMinsInput', () => {
        let controlVal: any;

        it('should update value when controlVal is between 0 and 59', () => {
            controlVal = '30';
            const targetValue = controlVal;
            service.onMinsInput(controlVal, formGroup, 'time', targetValue);
            expect(formGroup.controls['time'].value).toBe('');
        });

        it('should trim value when controlVal is less than 0', () => {
            controlVal = '-10';
            const targetValue = controlVal;
            service.onMinsInput(controlVal, formGroup, 'time', targetValue);
            expect(formGroup.controls['time'].value).toBe(-1);
        });

        it('should trim value when controlVal is greater than 59', () => {
            controlVal = '100';
            const targetValue = controlVal;
            service.onMinsInput(controlVal, formGroup, 'time', targetValue);
            expect(formGroup.controls['time'].value).toBe(10);
        });

        it('should trim value to prevent input "000"', () => {
            controlVal = '000';
            const targetValue = controlVal;
            service.onMinsInput(controlVal, formGroup, 'time', targetValue);
            expect(formGroup.controls['time'].value).toBe('00');
        });

        it('should handle edge case for minimum value (0)', () => {
            controlVal = '0';
            const targetValue = controlVal;
            service.onMinsInput(controlVal, formGroup, fieldName, targetValue);
            expect(formGroup.controls['time'].value).toBe('');
        });

        it('should handle edge case for maximum value (59)', () => {
            controlVal = '59';
            const targetValue = controlVal;
            service.onMinsInput(controlVal, formGroup, fieldName, targetValue);
            expect(formGroup.controls['time'].value).toBe('');
        });
    });

});

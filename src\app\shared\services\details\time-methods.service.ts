import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TimeMethodsService {

  onHrsInput(controlVal, formGroup, fieldName) {
    if (Number(controlVal) === 0) {
      formGroup.controls[fieldName].setValue("");
    } else if (Number(controlVal) < 1 || Number(controlVal) > 12) {
      const val: any = <any>controlVal / 1e1;
      //remove the entered input
      const trimmedDigits: number = parseInt(val, 0);
      formGroup.controls[fieldName].setValue(trimmedDigits);
    }
  }

  onMinsInput(controlVal, formGroup, fieldName, targetValue) {
    if (Number(controlVal) < 0 || Number(controlVal) > 59) {
      const val: any = <any>controlVal / 1e1;
      //remove the entered input
      const trimmedDigits: number = parseInt(val, 10);
      formGroup.controls[fieldName].setValue(trimmedDigits);
    } else if (targetValue.length > 2) {
      //To prevent user from typing '000'
      const trimmedDigits = targetValue.slice(0, -1);
      formGroup.controls[fieldName].setValue(trimmedDigits);
    }
  }


}

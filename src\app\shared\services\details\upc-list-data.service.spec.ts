import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { UpcListDataService } from './upc-list-data.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { UpcListItem, UpcListItemResponse, UpcListItemShort, UpcResponseData } from '@appShared/models/upc-list-item.model';


const mockUpcListItem: UpcListItem = {
    avgPrice: 12.99,
    bggm: 'BGGM123',
    bugm: 'BUGM456',
    categoryDescription: 'Beverages',
    categoryId: 'CAT001',
    consumerPackageGoods: { id: 'CPG001', name: 'Sample CPG' },
    corporateItemCode: null,
    groupId: 'GRP001',
    groupName: 'Group Name',
    has90DaysSales: true,
    has365DaysSales: false,
    isActive: true,
    isDiscontinued: false,
    isDisplayer: true,
    manufacturerCode: 'MFG001',
    quantity: '100',
    salesRank: null,
    uom: 'EA',
    upc: '123456789012',
    upcDescription: 'Sample UPC Description',
    version: '1.0'
};

const mockUpcListItemShort: UpcListItemShort = {
    upc: '123456789012',
    description: 'Sample UPC Description',
    quantity: '100',
    uom: 'EA'
};

const mockUpcResponseData: UpcResponseData = {
    region: 'North',
    rankedUpcs: [mockUpcListItem],
    rogCodeRing: [{ code: 'RING001', description: 'Sample Ring' }]
};

const mockUpcListItemResponse: UpcListItemResponse = {
    success: true,
    messageCode: 200,
    message: 'Success',
    upcRegionResponse: mockUpcResponseData
};

describe('UpcListDataService', () => {
    let service: UpcListDataService;
    let httpMock: HttpTestingController;
    let mockInitialDataService: jasmine.SpyObj<InitialDataService>;

    beforeEach(() => {
        const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getConfigUrls']);

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                UpcListDataService,
                { provide: InitialDataService, useValue: initialDataServiceSpy }
            ]
        });

        service = TestBed.inject(UpcListDataService);
        httpMock = TestBed.inject(HttpTestingController);
        mockInitialDataService = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;

        mockInitialDataService.getConfigUrls.and.returnValue('mock-api-url');
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should set UPC list and emit the value via Subject', (done) => {
        const mockUpcList = [{ upc: '12345', description: 'Test UPC', quantity: '10', uom: 'EA' }];
        
        service.getUpcListSub().subscribe((upcList) => {
            expect(upcList).toEqual(mockUpcList);
            done();
        });

        service.setUpcList(mockUpcList);
    });

    it('should return an observable from getUpcListSub', (done) => {
        const mockUpcList = [{ upc: '67890', description: 'Another Test UPC', quantity: '5', uom: 'BX' }];

        service.getUpcListSub().subscribe((upcList) => {
            expect(upcList).toEqual(mockUpcList);
            done();
        });

        service.setUpcList(mockUpcList);
    });

    it('should map an UPC listitem to UPCListItemShort', () => {
        const input: UpcListItem = mockUpcListItem;
        const expected: UpcListItemShort = mockUpcListItemShort;

        const result = service.mapToShort(input);

        expect(result).toEqual(expected);
    });

});
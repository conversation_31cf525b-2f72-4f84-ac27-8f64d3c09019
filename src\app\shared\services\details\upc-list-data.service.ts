import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { UpcListItem, UpcListItemResponse, UpcListItemShort } from '@appShared/models/upc-list-item.model';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UpcListDataService {

  constructor(
    private http: HttpClient, 
    private apiConfigService: InitialDataService) {
      // intentionally left empty
     }

  upcListSubject = new Subject<any>();
  upcListAPIUrl: string = this.apiConfigService.getConfigUrls(
    CONSTANTS.REGION_SALES_UPC_API
  );

  setUpcList(upcList) {
    this.upcListSubject.next(upcList);
  }

  getUpcListSub(): Observable<any> {
    return this.upcListSubject.asObservable();
  }

  loadUpcList(mobId: string, regionId: string): Observable<UpcListItemResponse> {
    return this.http.post(this.upcListAPIUrl, {
      mobId: mobId,
      regionId: regionId
    });
  }

  mapToShort(row: UpcListItem): UpcListItemShort {
    return {
      upc: row?.upc,
      description: row?.upcDescription,
      quantity: row?.quantity.toString(),
      uom: row?.uom
    } as UpcListItemShort;
  }

}

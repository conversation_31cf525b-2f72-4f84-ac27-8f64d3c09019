import { Http<PERSON><PERSON>, <PERSON>tt<PERSON><PERSON><PERSON><PERSON> } from "@angular/common/http";
import { Injector } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { MsalService } from '@azure/msal-angular';
import { BehaviorSubject, of } from 'rxjs';
import { BaseInputSearchService } from './base-input-search.service';

describe('BaseInputSearchService', () => {
  let service: BaseInputSearchService;
  const authServiceStub = () => ({ onUserDataAvailable: () => ({}), getTokenString: () => ({}), getUserId: () => 'draja08' });
  const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
  const permissionsServiceStub = () => ({
    loadPermissions: () => ({})
  });
  const commonRouteServiceStub = () => ({currentActivatedRoute: "request"})

  const commonServiceStub = () => ({ getHeaders: () => ({}), passPaginationData$: new BehaviorSubject({}) });
  const facetItemServiceStub = () => ({
    setOfferFilter: () => ({}),
    sortProperties: () => ({}),
    populateStoreFacet: () => ({}),
    getFacetItems: () => ({}),
    populateFacetSearch: () => ({}),
    populateFacet: () => ({}),
    getdivsionStateFacetItems: () => ({}),
    sortDivisionRogCds: () => ({}),
    getOfferFilter: () => ({})
  });
  const queryGeneratorStub = () => ({
    setQuery: () => ({}),
    pushParameters: () => ({}),
    getQuery: () => ({}),
    getQueryWithFilter: () => ({})
  });
  const initialDataServiceStub = () => ({
    getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
    getConfigUrls: () => ({})
  });
  const commonSearchServiceStub = () => ({
    batchActionActiveTab: {},
    setActiveCurrentSearchType: () => ({}),
    setAllFilterOptions: () => ({}),
    setFilters: () => ({}),
    resetAllFilterOptions: () => ({}),
    setQueryOptionsForBpd: () => ({})
  });
  const baseInputSearchServiceStub = (commonSearchService: CommonSearchService) => {
      let activeCurrentSearchType: string;
      const getWithOrQueryFilterWrap = (isExpiredStatusFilterSelectedForBPD: boolean) => {
        const queryWithOrFilters = service.getQueryWithOrFilter();
        if (service.currentRouter === 'request') {
          commonSearchService.setQueryOptionsForBpd({
            isHideExpiredStatusReqs: true,
            isExpiredStatusFilterSelectedForBPD
          });
          service.setFormQuery(service.queryForInputAndFilter);
        }
        return queryWithOrFilters;
      };
      return {
        getWithOrQueryFilterWrap,
        setActiveCurrentSearchType: (type: string) => { activeCurrentSearchType = type; },
        createSubject: () => {
          service[`${service.currentRouter}BehaviorSubject`] = new BehaviorSubject(null);
          service[`${service.currentRouter}FacetFilterBehaviorSubject`] = new BehaviorSubject(null);
        },
        populateChipList: () => ({}),
        postDataForInputSearch: () => ({}),
        getActiveCurrentSearchType: () => activeCurrentSearchType,
        getLastPeriodOptions: () => of(['Last 7 Days', 'Last 30 Days', 'Last 90 Days']),
        createInputGroupsLevelObject: () => ({}),
        getQueryWithOrFilter: () => (["combinedDigitalUser", "combinedNonDigitalUser"]),
        get currentRouter() {
          return 'defaultRouter';
        }
      };
    };
  const featureFlagServiceStub = () => ({
    assignFeatureFlag: () => ({}),
    isFeatureFlagEnabled: () => ({}),
    hasFlags: () => ({}),
  });
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        HttpClient,
        HttpHandler,
        { provide: AuthService, useFactory: authServiceStub },
        { provide: MsalService, useFactory: MsalServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: PermissionsService, useFactory: permissionsServiceStub },
        { provide: CommonService, useFactory: commonServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub },
        {
          provide: BaseInputSearchService,
          useFactory: baseInputSearchServiceStub,
          deps: [CommonSearchService]
        },
        { provide: QueryGenerator, useFactory: queryGeneratorStub },
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub }
      ],
    });
    AppInjector.setInjector(TestBed.inject(Injector));
    service = TestBed.inject(BaseInputSearchService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return last period options', (done: DoneFn) => {
    service.getLastPeriodOptions().subscribe(options => {
      expect(options).toEqual(['Last 7 Days', 'Last 30 Days', 'Last 90 Days']);
      done();
    });
  });

  it('should set active current search type', () => {
    const currentSearchType = 'testType';
    spyOn(service, 'setActiveCurrentSearchType').and.callThrough();
    service.setActiveCurrentSearchType(currentSearchType);
    expect(service.setActiveCurrentSearchType).toHaveBeenCalledWith(currentSearchType);
    expect(service.getActiveCurrentSearchType()).toBe(currentSearchType);
  });

  it('should create a subject', () => {
    const currentSearchType = 'testType';
    service.setActiveCurrentSearchType(currentSearchType);
    expect(service.getActiveCurrentSearchType()).toEqual(currentSearchType);
    spyOn(service, 'createSubject').and.callThrough();
    service.createSubject();
    expect(service.createSubject).toHaveBeenCalled();
    expect(service[`${service.currentRouter}BehaviorSubject`]).toBeTruthy();
    expect(service[`${service.currentRouter}FacetFilterBehaviorSubject`]).toBeTruthy();
  });

  it('should initialize properties in the constructor', () => {
    expect(service.currentRouter).toBe('defaultRouter');
  });

  it('should set active current search type and verify it', () => {
    const currentSearchType = 'newTestType';
    service.setActiveCurrentSearchType(currentSearchType);
    expect(service.getActiveCurrentSearchType()).toBe(currentSearchType);
  });

  it('should set active current search type and create subject', () => {
    const currentSearchType = 'anotherTestType';
    service.setActiveCurrentSearchType(currentSearchType);
    service.createSubject();
    expect(service.getActiveCurrentSearchType()).toBe(currentSearchType);
    expect(service[`${service.currentRouter}BehaviorSubject`]).toBeTruthy();
    expect(service[`${service.currentRouter}FacetFilterBehaviorSubject`]).toBeTruthy();
  });

  it('should set active current search type and populate chip list', () => {
    const currentSearchType = 'chipTestType';
    spyOn(service, 'populateChipList').and.callThrough();
    service.setActiveCurrentSearchType(currentSearchType);
    service.populateChipList();
    expect(service.getActiveCurrentSearchType()).toBe(currentSearchType);
    expect(service.populateChipList).toHaveBeenCalled();
  });

  it('should set active current search type and post data for input search', () => {
    const currentSearchType = 'inputSearchTestType';
    spyOn(service, 'postDataForInputSearch').and.callThrough();
    service.setActiveCurrentSearchType(currentSearchType);
    service.postDataForInputSearch();
    expect(service.getActiveCurrentSearchType()).toBe(currentSearchType);
    expect(service.postDataForInputSearch).toHaveBeenCalled();
  });

  xit('should get with OR query filter wrap when expired status filter is selected for BPD', () => {
    const isExpiredStatusFilterSelectedForBPD = true;
    const queryWithOrFilters: ["combinedDigitalUser", "combinedNonDigitalUser"] = ["combinedDigitalUser", "combinedNonDigitalUser"];
    spyOn(service, 'getQueryWithOrFilter').and.returnValue(queryWithOrFilters);
    spyOn(service.commonSearchService, 'setQueryOptionsForBpd').and.callThrough();
    spyOn(service, 'setFormQuery').and.callThrough();

    const result = service.getWithOrQueryFilterWrap(isExpiredStatusFilterSelectedForBPD);

    expect(service.getQueryWithOrFilter).toHaveBeenCalled();
    expect(service.commonSearchService.setQueryOptionsForBpd).toHaveBeenCalledWith({
      isHideExpiredStatusReqs: true,
      isExpiredStatusFilterSelectedForBPD
    });
    expect(service.setFormQuery).toHaveBeenCalled();
    expect(result).toEqual(queryWithOrFilters);
  });

  it('should get with OR query filter wrap when expired status filter is not selected for BPD', () => {
    const isExpiredStatusFilterSelectedForBPD = false;
    const queryWithOrFilters: ["combinedDigitalUser", "combinedNonDigitalUser"] = ["combinedDigitalUser","combinedNonDigitalUser"];
    spyOn(service, 'getQueryWithOrFilter').and.returnValue(queryWithOrFilters);

    const result = service.getWithOrQueryFilterWrap(isExpiredStatusFilterSelectedForBPD);

    expect(service.getQueryWithOrFilter).toHaveBeenCalled();
    expect(result).toEqual(queryWithOrFilters);
  });

  xit('should get with OR query filter wrap for REQUEST router', () => {
    spyOnProperty(service, 'currentRouter', 'get').and.returnValue('request');
    const isExpiredStatusFilterSelectedForBPD = true;
    const queryWithOrFilters: ["combinedDigitalUser", "combinedNonDigitalUser"] = ["combinedDigitalUser","combinedNonDigitalUser"];
    spyOn(service, 'getQueryWithOrFilter').and.returnValue(queryWithOrFilters);
    spyOn(service.commonSearchService, 'setQueryOptionsForBpd').and.callThrough();
    spyOn(service, 'setFormQuery').and.callThrough();

    const result = service.getWithOrQueryFilterWrap(isExpiredStatusFilterSelectedForBPD);

    expect(service.getQueryWithOrFilter).toHaveBeenCalled();
    expect(service.commonSearchService.setQueryOptionsForBpd).toHaveBeenCalledWith({
      isHideExpiredStatusReqs: true,
      isExpiredStatusFilterSelectedForBPD
    });
    expect(service.setFormQuery).toHaveBeenCalled();
    expect(result).toEqual(queryWithOrFilters);
  });
});
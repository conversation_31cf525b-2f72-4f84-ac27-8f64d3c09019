import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { convertUTCToLocalDateWithoutTZ } from '@appUtilities/date.utility';
import * as moment from 'moment';
import { BehaviorSubject, Subject } from 'rxjs';
import { STORE_GROUP_CONSTANTS } from '../../../modules/groups/constants/store_group_constants';
import { StoreGroupService } from '../../../modules/groups/services/store-group.service';
import { AuthService } from '../common/auth.service';


@Injectable({
  providedIn: 'root'
})
export class BaseInputSearchService {
 
  inputQuery;
  inputQueryWithORFilter;
  queryWithOrFilters: ["combinedDigitalUser","combinedNonDigitalUser"]
  OR = 'OR';
  TO = 'TO';
  extraStarChar = "*";
  addAsBracketStart = "(";
  addAsBracketEnd = ")";
  addAsArrayStart = "[";
  addAsArrayEnd = "]";
  addHashKey = "#";
  inputFormGrpValue = {};
  onClearBggmBugmChip$ = new Subject();
  updateCategoryChipSavedSearch$ = new Subject();

  inputGroups = ["combinedStoreGroups","brandAndSize","cic","cpg","createUserId","imageId","mobId","productGroups","repUpc","requestId" ,"templateId","periodWeek","storeGroups"];
  inputGroupsLevel = {
    createTimeStamp:["From","To"],
    //periodWeek:["periodWeek","lastPeriodCreated"],
    lastPeriodCreated: ["lastPeriodCreated"],
    minusLastPeriodCreated: ["minusLastPeriodCreated"],
    minusPeriodWeek:["minusPeriodWeek"],
    effectiveStartDate:["From","To"],
    lastUpdateTimestamp:["From","To"],
    priceUntil:["From","To"],
    setOtStatusUntil:["From","To"],
    effectiveEndDate: ["From", "To"]
  };
  filterGroupsLevel = [];
  dateFields =  ["createTimeStamp","startDt", "effectiveStartDate", "lastUpdateTimestamp", "effectiveEndDate"];
 changeSelectOption =["minusLastPeriodCreated"];
  createdInputGroupObject = (option,value)=>[{[option]:value}];
  createInputGroupsLevelObject=(level,values)=>{
    const groups:any = this.inputGroupsLevel[level];
     return  {inputGroupsLevel:groups.map((ele,index)=>{return{[ele]:values[index]}})};
  };
  createParentInputGroupObject = (inputSelected,value)=>{
    return {
     inputSelected,
     inputGroups:this.createdInputGroupObject(inputSelected,value)
     }
  };
  createActualInputGroupLevelObject = (inputSelected,value,inputGroupsLevel = null)=>{
    if(this.changeSelectOption.includes(inputSelected)){
      inputSelected = "lastPeriodCreated";
    }
    const initObj = this.createParentInputGroupObject(inputSelected,value);
    let groupLevelObj = {};
    if(inputGroupsLevel){
      groupLevelObj = this.createInputGroupsLevelObject(inputSelected,inputGroupsLevel);
    }
    return {...initObj,...groupLevelObj};
  } 

  dateFormat = ["Today","Today+","DATE"];
  addExtraToField = {
    joinWithHash:["deliveryChannel","status"],
    addStarToStart :[],

    addStarToEnd:["saveValueTxt","externalOfferId","aggregatorOfferId","cpg","createUserId","imageId","nopaNumbers","pluTriggerBarcode",
    "mobId","userId","offerRequestId","requestedUserId","startDt","combinedDigitalUser","combinedNonDigitalUser"],

    addStarToStartAndEnd:["category","combinedStoreGroups","headLine","headLine2","productDesc","verbiageForm","brandAndSize","productGroups",
    "combinedStoreGroups","offerName","qualificationBenefitCombinedProductsGroup","vehicleName"]
  }

 


  checkChipValuesKey = ["setOtStatusUntil","priceUntil","effectiveStartDate","createTimeStamp","startDt","lastUpdateTimestamp", "effectiveEndDate"];
  checkChipValuesValues = ["Today","Today+","DATE"];
  checkChipForRange = ["Range"];
 


  checkChipValuesKeyValueContains =(key,checkKey)=>checkKey.includes(key);

  addStarToStart =(ele)=>`*${ele}`;
  addStarToEnd =(ele)=>`${ele}*`;
  addStarToStartAndEnd =(ele)=>`*${ele}*`;
  replaceSlash = (ele) => toString.call(ele) === "[object String]" ? ele.replace(/[\/\\]/g,'') : ele ;
  removeSpace = (ele)=>`${ele?.trim().replace(/[.+?^${}()|[\]\\; ]/g, "\\ ")}`;
  escapeSplChars = (ele)=>`${ele?.trim().replace(/[.+?^${}()|[\]\\; ]/g, "\\$&")}`;
  putSlash = (ele)=>`${ele?.trim().replace(/[.+?^${}()|[\]\\; ]/g, "\\")}`;
  joinWithStartAndEndChar =(ele,join,startChar,endChar)=>`${startChar}${ele.join(join)}${endChar}`;

  addExtraToElement = {
    addStarToStart:[],

    addStarToEnd:["cpg","createUserId","imageId","nopaNumbers","effectiveStartDate","pluTriggerBarcode","mobId","userId",
    "externalOfferId","hhid","aggregatorOfferId","saveValueTxt", "updatedByUser","createdByUser", "effectiveEndDate"],
     
    [this.OR]:["bggmDesc","bugmDesc","category","headLine","productDesc","headLine2","inEmail","targeted","eventids","podStatus","podDivisionRog",
    "offerRequestorGroup","isApplicableToJ4U","categories","offerFailedState","offerProtoType","offerProgramCd",
    "actionFailedState","regionId","progSubType","programType","nonDigitalUiStatus","digitalUiStatus","adType",
    "deliveryChannel","combinedNonDigitalUser","combinedDigitalUser","group","requestType",
    "cpg","cic","discountType","requestId", "templateId","brandAndSize","createUserId","imageId","programCode","mobId",
    "periodWeek","lastPeriodCreated","productGroups",
      "combinedStoreGroups","nopaNumbers","pluTriggerBarcode","pointsRequired","templateId","userId","externalOfferId",
      "offerName","qualificationBenefitCombinedProductsGroup",
      "offerRequestId","requestedUserId","redemptionStoreId","aggregatorOfferId","saveValueTxt","vehicleName"],

      [this.TO]:["setOtStatusUntil","priceUntil","createTimeStamp","lastUpdateTimestamp","effectiveStartDate",
      "endDt","startDt","lastUpdateTimestamp", "effectiveEndDate"],
     
      addAsBracketStartAndEnd:["category","inEmail","targeted","eventids","podStatus","podDivisionRog","offerRequestorGroup",
      "isApplicableToJ4U","categories","offerFailedState","offerProtoType","offerProgramCd","actionFailedState","regionId"
      ,"progSubType","programType","nonDigitalUiStatus","digitalUiStatus","adType","deliveryChannel","combinedNonDigitalUser"
      ,"combinedDigitalUser","group","requestType","programCd","discountType","imageId","templateId",
      "createUserId","headLine","headLine2","productDesc","verbiageForm","requestId","templateId","brandAndSize",
      "imageId","programCode","mobId", "lastPeriodCreated","periodWeek","productGroups","combinedStoreGroups","cic",
      "nopaNumbers","pluTriggerBarcode","pointsRequired","userId","externalOfferId","offerName",
      "qualificationBenefitCombinedProductsGroup","offerRequestId","requestedUserId","redemptionStoreId",
      "aggregatorOfferId","saveValueTxt","vehicleName", "status", "bggm","bugm", "categoryId","otStatus"],

      addAsArrayStartAndEnd:["createTimeStamp","lastUpdateTimestamp","effectiveStartDate","endDt","startDt", 
      "priceUntil", "setOtStatusUntil","lastUpdateTimestamp", "createTs", "effectiveEndDate"],

      addAsQueryWithFilter:[{
        status: ["digitalUiStatus","nonDigitalUiStatus"],
        offerStatus:["digitalUiStatus","nonDigitalUiStatus",
                      {offerFailedState:[{IPU:'PUBLISHING'}],minusOfferFailedState:[{I:"PUBLISHING"}]}],
        deliveryChannel:[{deliveryChannel:["CC","IS","O"],adType:["IA","NIA"]}],
        assignedTo:["combinedDigitalUser","combinedNonDigitalUser"]
      }],
      
      addMinusSign: ["Was Not"]
                             
  };
  filterOption: any;
  defaultOption: any;
  query: any;
  currentSearchType: any;
  sortQuery: any;
 
 
  constructor( public commonSearchService :CommonSearchService,
    public _http:HttpClient,public featureFlagService: FeatureFlagsService,
      public commonRouteService: CommonRouteService, public apiConfigService: InitialDataService,
      private authService: AuthService,
private storeGroupService : StoreGroupService  ) {

   }
   createSubject(){
    this[`${this.currentRouter}BehaviorSubject`] =  new BehaviorSubject(null);
    this[`${this.currentRouter}FacetFilterBehaviorSubject`] =  new BehaviorSubject(null);
   }
   setActiveCurrentSearchType(currentSearchType){
     this.currentSearchType = currentSearchType;
  }
   getActiveCurrentSearchType(){
     return this.currentSearchType;
   }
   populateChipList(){
    const searchOptions = this.formChipForFilterAndInputSearch(this.filterOptions,this.inputOptions);
    this.commonSearchService.setInputSearchChip(searchOptions.facetSearch);
    this.commonSearchService.setFacetFilterChip(searchOptions.facetFilter);
    }

   get currentRouter(){
    
   return this.commonSearchService.currentRouter;
  }
   setFormQuery(query){
     this.query = query;
   }
  getInputFieldSelected(field){
    if(this.changeSelectOption.includes(field)){
      field = "lastPeriodCreated";
    }   
   return this.inputOptions.filter(ele=>ele.field===field)[0];
  }
  getFilterFieldSelected(field){
    return this.filterOptions?.filter(ele=>ele.field===field)[0];
    }
  getSortFieldSelected(field) {
        return this.sortOptions.filter(ele => ele.field === field)[0];
    }
  convertToRegularFormat(date,format = 'MM/DD/YY'){
  return `${moment(date)
     .format(format)}`;
  }
  convertUTCToLocalDateWithoutTZ(date){
    return convertUTCToLocalDateWithoutTZ(date);
  }
  convertToDateFormat(date,start = false,end =  false){
   if(start){
   return date === "*" ? "*" : `${moment(date)
    .startOf('day')
    .utc()
    .format('YYYY-MM-DDTHH:mm:ss')}Z`;
   }else if(end){
    return date === "*" ? "*" : `${moment(date)
      .endOf('day')
      .utc()
      .format('YYYY-MM-DDTHH:mm:ss')}Z`;
   }
  }

  convertFromToDateFromMidNight(date,start = false,end =  false){
    if(start){
    return date === "*" ? "*" : `${moment(date)
     .startOf('day')
     .format('YYYY-MM-DDTHH:mm:ss')}Z`;
    }else if(end){
     return date === "*" ? "*" : `${moment(date)
       .endOf('day')
       .format('YYYY-MM-DDTHH:mm:ss')}Z`;
    }
   }



  generateQueryForDropDown(obj){
    //Generates the query for drop down or child dropdown options
    const {selectedField,options,inputLevel} = obj;
    
    if(selectedField.query){
      if(selectedField.resetQuery){
        selectedField.query = [];
      }
      selectedField.query.push(...inputLevel.reduce((output,ele)=>{
        if(this.checkIfDateElement(options)) {
          if(options.field === 'effectiveEndDate') {
            if(options.bindValue === 'Today') {
              output.push(Object.values(ele)[0]);
            } else {
              output.push(...[this.convertFromToDateFromMidNight(Object.values(ele)[0],["From"].includes(Object.keys(ele)[0]),["To"].
              includes(Object.keys(ele)[0]))]);
            }
          } else {
            output.push(...[this.convertToDateFormat(Object.values(ele)[0],["From"].includes(Object.keys(ele)[0]),["To"].
            includes(Object.keys(ele)[0]))]);
          }
        } else {
          output.push(Object.values(ele)[0]);
        }
         return output;
        },[]));
   }
  }


  checkIfDateElement(element) {
    return element?.type === "date" || element?.elements?.find(obj => obj.type === "date") || element?.options?.find(obj => obj.type === "date");
  }
  generateQueryForFilterOptions(selectedField,values){
    selectedField.query.push(...values);
    selectedField.elements.forEach(element => {
       if(element.query){
          element.query.push(...values);
        }else if(element.queryWithOrFilters){
          element.queryWithOrFilters.push(...values);
        }
    });
  }
  generateQueryForOptions(selectedField,inputGroups,inputGroupsLevel){
    let isReturn = false;
  
  selectedField?.elements?.forEach(element => {
      const eleField = inputGroups.filter(ele=>ele[element.field])[0];
      
      if(eleField){

        if(element.options){
         const options = element.options.find(ele=> ele.bindValue === eleField[ele.field]),
               inputLevel = inputGroupsLevel[0] ? inputGroupsLevel : inputGroups;

         this.generateQueryForDropDown({selectedField,options,inputLevel});
       
        }else if(selectedField.query){
          if(selectedField.resetQuery){
            selectedField.query = [];
          }
          let filedValue:any = Object.values(eleField)[0];
          filedValue = this.checkCopyPasteConditions(selectedField, filedValue);
          let  splitValues = toString.call(filedValue) ==='[object Array]'?filedValue:filedValue.split(",");

          if(splitValues.some(ele=>selectedField.query.includes(ele))){
            isReturn = true;
            return true;
          }

          splitValues = this.padMobId({selectedField, splitValues});    
          splitValues = splitValues.filter(function(x) {
            return x !== undefined;
       });     

          selectedField.query.push(...splitValues);
        }else if(element.queryWithOrFilters){
          element.queryWithOrFilters.push(Object.values(eleField)[0]);
        }
      }

      if(inputGroupsLevel && ["periodWeek","lastPeriodCreated"].includes(selectedField.field)){
        selectedField.updatedField = inputGroups.reduce((output,ele)=>{ output = ele[selectedField.field]; return output; },'');
      }

    });
    return isReturn
 
  }
  checkCopyPasteConditions(selectedField, enteredValue) {
    const isValidSelection = selectedField && enteredValue && toString.call(enteredValue) !== '[object Array]';
    return isValidSelection &&  CONSTANTS.COPY_PASTE_IDS_EXCEL_FOR_SEARCH.includes(selectedField?.field) 
      ? enteredValue?.trim().replace(/[ ,]+/g, ",") : enteredValue;
  }
  padMobId({selectedField, splitValues}){
    let convertedArray =[];
    if(selectedField.field === 'mobId'){
      splitValues.forEach(element => {
        element = element.replace(/^(?:"|")|(?:"|")$/g, ''); // Group start and end quotes explicitly
        convertedArray.push(element);
      });  
      // Allow search with or without the zeros
      splitValues = convertedArray.map(e=>this.commonSearchService.pad(e, 6));
    } 
    return splitValues;
  }

  showChip(element){
    const {configMapper, apiData = null} = element;

    const configData = configMapper === "api" && apiData ? apiData : this.apiConfigService.getAppData()[configMapper];
    switch(toString.call(configData)) {
      case "[object Object]":
        element.showChip = element.query.map(ele=>configData?.[this.replaceSlashfromString(ele)]);
        break;
      case "[object Array]":
        const data = configData.reduce((output,ele)=>{ output[ele.code]=ele.name; return output},{});
        element.showChip = element.query.map(ele=>data[this.replaceSlashfromString(ele)]);
        break;
      default:
        element.showChip = element?.query.map(ele => this.replaceSlashfromString(ele));
        break;
        // code block
    }

  }
  replaceSlashfromString(value, field = null) {
    return value && toString.call(value) === "[object String]" && (!this.checkChipValuesKey?.includes(field)) ?  value.replace(/[\/\\]/g,'') : value
  }
  generateFilterValue(field,value,inputGroupsLevelEle = null){
    const {OR,TO,addAsArrayStartAndEnd,addAsBracketStartAndEnd} = this.addExtraToElement;
    let filedValue = value.replace(/[\[\](*)]/gi, ''),groupValue = filedValue;
    
    if(OR.includes(field)){
      filedValue =  filedValue.split(" OR ");
    }else if (TO.includes(field)){
      filedValue =  filedValue.split(" TO ");
    }else{
      filedValue =  filedValue.split(" OR ");
    }
    return filedValue;
  }
  generateValue(field,value,inputGroupsLevelEle = null){
    const {OR,TO,addAsArrayStartAndEnd,addAsBracketStartAndEnd} = this.addExtraToElement;
    let filedValue = value.replace(/[\[\](*)]/gi, ''),groupValue = filedValue;
    const outputObj:any = {};
    if(this.dateFormat.includes(filedValue)){
      const startDate = `${moment().startOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`,
      endDate = `${moment().endOf('day').utc().format('YYYY-MM-DDTHH:mm:ss')}Z`
      field = filedValue ;
      if(filedValue==='Today'){
        filedValue = [startDate,endDate];
      }else if(["DATE","Today+"].includes(filedValue)){
        filedValue =[startDate,'*'];
      }
       
    }else{
      if(OR.includes(field)){
        filedValue =  filedValue.split(" OR ");
      }else if (TO.includes(field)){
        filedValue =  filedValue.split(" TO ");
      }else{
        filedValue =  filedValue.split(" OR ");
      }
    }
    
    outputObj.inputGroups = filedValue;
    if(inputGroupsLevelEle){
      outputObj.inputGroups = field;
      outputObj.inputGroupsLevel = filedValue;
    }
    return outputObj;
  }

  setDefaultOptionsForExpiredStatus({field}){
    //If expired status is selected, set default options
    if(this.currentRouter === CONSTANTS.REQUEST && field === CONSTANTS.END_DATE_QUERY_OR){
      this.commonSearchService.setQueryOptionsForBpd({isHideExpiredStatusReqs:false});      
    }
  }

  pushToQuery(element,field,value){  
    this.setDefaultOptionsForExpiredStatus({field});

    const {OR,TO,addAsArrayStartAndEnd,addAsBracketStartAndEnd} = this.addExtraToElement;
    let filedValue = value.replace(/[\[\](*)]/gi, '').replace(/[\\ ]/gi, ' ');
    if(OR.includes(field)){
      filedValue =  filedValue.split(" OR ");
    }else if (TO.includes(field)){
      filedValue =  filedValue.split(" TO ");
    }
    if(addAsArrayStartAndEnd.includes(field)){
     element['queryWithOrFilters'] = filedValue;
    }else if(addAsBracketStartAndEnd.includes(field)){
      element['query'] = filedValue;
      if(element.elements){
        element.elements[0].query = filedValue;
      }
    }
  }
  generateChipObject(options){
    return options?.reduce((output,element)=>{ 
       if(element.showChip.length){
        output[element.field] = {[element.label]:toString.call(element.showChip)==="[object Array]"?`${element.showChip.join(" ; ")} ;` : this.replaceSlashfromString(element.showChip, element.field)};
       }
      return output;
      },{});
  }
  setFilterElementOptionsValues(inputFormGroup){
   const filterEle = this.getInputFieldSelected(inputFormGroup.inputSelected);

  }
  generateSearchChip(){

      this.filterOptions.forEach((element) =>{
          this.showChip(element);
        });
  }
  setChipForSubtypes(optionObj, inputGroups) {
    const inputOptions = this.commonSearchService.inputSearchOption[this.currentRouter],
    linkedSearchObj = inputOptions?.find(ele => ele.field === optionObj.linkedTo);
    if(linkedSearchObj?.query?.length && optionObj?.linkValue) {
      return linkedSearchObj?.query?.includes(optionObj.linkValue) && optionObj.bindValue === Object.values(inputGroups)[0];
    }
  }
  setChipForField(element,inputGroups,inputSelected){
    if(this.dateFields.includes(element.field) && this.dateFormat.includes(inputGroups[inputSelected])){
      element.showChip = inputGroups[inputSelected];
      element.updatedChip = [element.showChip];
    }else if(element.query){

      element.updatedChip = null;
      const elements = element.elements[0]
      
      if(this.checkChipValuesKeyValueContains(element.field,this.checkChipValuesValues)){
        element.showChip = `${Object.values(inputGroups)[0]}`;
      }else if(this.checkChipValuesKeyValueContains(element.field,this.checkChipValuesKey)){
        element.showChip = `${element.query.map(ele=>convertUTCToLocalDateWithoutTZ(ele)).join("-")}`;
      }else if(elements.type === "select"){
        const selectedObj = elements.options.filter((elem) => {
          return elem.field === "subType" ? this.setChipForSubtypes(elem, inputGroups) : elem.bindValue === Object.values(inputGroups)[0];
        })
        element.showChip = selectedObj?.[0]?.bindLabel;
        if (element.field === "lastPeriodCreated") {
          element.showChip = selectedObj?.[0]?.bindLabel + ` ${element?.query[0]}`;
        }
      }
      else{
        element.showChip = element.query.join(" ; ");
      }
      
    }
    
  }
  inputSearchChip(inputFormGroup){
    const chip = {};
    const { inputGroupsLevel,inputSelected,inputGroups:[inputGroups] } = inputFormGroup;
    let inputSearchChip =  this.commonSearchService.getInputSearchChip(),
        selectedEle = this.getInputFieldSelected(inputSelected);

      this.setChipForField(selectedEle,inputGroups,inputSelected);
      if(selectedEle.showChip){
        chip[selectedEle.field] = { [selectedEle.label]: selectedEle.showChip };
        inputSearchChip = {...inputSearchChip,...chip};
      }
  
    return inputSearchChip;
  }

  formChipForFilterAndInputSearch(filterOptions,inputOptions){
    return {
      facetFilter: this.generateChipObject(filterOptions),
      facetSearch: this.generateChipObject(inputOptions)
    }
  }
  updateQueryWithExtraFields(element){
    let updatedQuery = "";
    updatedQuery = element.query.map(ele => this.replaceSlash(ele)).map(this.escapeSplChars);

    if(element?.keepSlash) {
      updatedQuery  = element.query.map(ele => this.putSlash(ele));
    }

    if(this.addExtraToField.addStarToStart.includes(element.field)){
      updatedQuery = element.query.map(ele => this.replaceSlash(ele)).map(this.escapeSplChars).map(this.addStarToStart);
    }else if(this.addExtraToField.addStarToEnd.includes(element.field)){
      updatedQuery = element.query.map(ele => this.replaceSlash(ele)).map(this.escapeSplChars).map(this.addStarToEnd);
    }else if(this.addExtraToField.addStarToStartAndEnd.includes(element.field)){
      updatedQuery = element.query.map(ele => this.replaceSlash(ele)).map(this.escapeSplChars).map(this.addStarToStartAndEnd);
    }
    
    return updatedQuery;
  }
  updateAddAsBracketStartAndEnd(element,updatedQuery){
    let output = "";
    const queryPrefix = `${output}${element.updatedField || element.field}` ;
    if(this.addExtraToElement.addAsBracketStartAndEnd.includes(element.field)){
      output = `${queryPrefix}=(${updatedQuery.join(" OR ")});`
    }else if(this.addExtraToElement.addAsArrayStartAndEnd.includes(element.field)){
      output = `${queryPrefix}=[${updatedQuery.join(" TO ")}];`
    } else {
      output = `${queryPrefix}=${updatedQuery.join(" OR ")};`
    }
    return output;
  }
  getFormFilterOptions(filterOptions){
    if(!filterOptions){
      return '';
    }
   return filterOptions?.reduce((output,element)=>{ 
    const isElementInQuryFilter = this.addExtraToElement.addAsQueryWithFilter.find(ele => ele[element.field]);
    if(!isElementInQuryFilter && !element?.queryWithOrFilters?.length) {
      if(element?.query.length){
        let updatedQuery = this.updateQueryWithExtraFields(element);
       
        output = `${output}${this.updateAddAsBracketStartAndEnd(element,updatedQuery)}`;
       }
    }

    return output;},'');
  }
  getFormInputOptions(inputOptions, saveQuery = false){
    const replaceSlash = (ele) => toString.call(ele) === "[object String]" ? ele.replace(/[\/\\]/g,'') : ele ; 
    if(!inputOptions){
      return "";
    }
    return inputOptions?.reduce((output,element:any) => {
      let updatedQuery;
  
      if(element?.query?.length){
        updatedQuery = this.updateQueryWithExtraFields(element);
        
        if(updatedQuery){
          if(saveQuery && this.dateFormat.some(ele=>element?.updatedChip?.includes(ele))){
            updatedQuery = element.updatedChip;
          }
          output = `${output}${this.updateAddAsBracketStartAndEnd(element,updatedQuery)}`;
          if(element?.filterByUser && this.commonSearchService.isStoreIdUserSearchEnabled)
          {
            let sys = "(System OR system OR OMS)";
            output += `notCreatedBy=${sys};notUpdatedBy=${sys};`;
          }
          
        } 
      }
  
      
      return output;
      },'')
  }

  checkIfAddMinus(inputOption, inputFormGroup) {
    if(inputFormGroup) {
      const {inputGroups} = inputFormGroup;
      const selectedOption =  inputGroups?.find(obj => obj[inputOption.field])
      const isMinusToAdd = selectedOption && inputOption?.options?.find(ele => ele.addMinusSign && this.addExtraToElement.addMinusSign.includes(selectedOption[inputOption?.field]));
      return inputOption.addMinusSign || isMinusToAdd;
    }
  }
  
  getFormDefaultOptions(defaultOptions){
    return defaultOptions?.reduce((output,ele)=>{ 
      if(ele.query.length){
        if(ele.field===CONSTANTS.NEXT){
          ele.query = [1,""].includes(ele.query)?[]:ele.query;
        }
        if(ele.query.length){
          output = `${ele.field}=${ele.query};${output}`;
        }
        
       }
      return output;},'');
    }
    getFormSortOptions(sortOptions){
    return sortOptions?.reduce((output,ele)=>{ 
        if(ele.select){
            output = `sortBy=${ele.field}${ele.query};${output}`;
        }
        
       
      return output;},'');
    }
   
  formQuery(filterOptions,inputOptions,defaultOptions,sortOptions,saveQuery = false){
       const filterOptionsQuery = this.getFormFilterOptions(filterOptions),
       inputOptionsQuery = this.getFormInputOptions(inputOptions ,saveQuery),
       sortOptionQuery = this.getFormSortOptions(sortOptions);

       let defaultOptionsQuery = this.getFormDefaultOptions(defaultOptions);
       if(inputOptionsQuery.includes('effectiveEndDate')) {
        defaultOptionsQuery =  defaultOptionsQuery.replace(/effectiveEndDate=\[\* TO [^\]]+\];/, '');
       }
      return `${filterOptionsQuery}${inputOptionsQuery}${defaultOptionsQuery}${sortOptionQuery}`;
    }

  formQueryWithOrFilter(){

    if(!this.inputOptions || !this.filterOptions){
      return [];
    }
    const optionList = [...this.filterOptions, this.inputOptions]
    return optionList?.reduce((output,element:any) => {
      const {field}  = element;
      const queryFilterOptions = field  && this.addExtraToElement.addAsQueryWithFilter[0][field];
      if(queryFilterOptions && element?.query?.length) {
            element["queryWithOrFilters"] = [];
            const formedQuery = element.query.join(" OR ");
            const formattedQueryOptions = queryFilterOptions.reduce((result, item) => {
              if(this.addExtraToElement.addAsBracketStartAndEnd.includes(element.field)){
                result.push(`${item}=(${formedQuery})`)
              }
              return result
            },[]);
            if(formattedQueryOptions?.length) {
              element["queryWithOrFilters"].push(formattedQueryOptions.join("#"))
              output.push(formattedQueryOptions.join("#"))
            }
      } 
    return output;
    },[]);
  }
  getFormQueryFromFilter(){
   return "";
  }
  getFormQueryFromQueryWithFilter(){
    // intentionally left empty
  }
  getFormQueryFromSortFilter(){
   return this.commonSearchService.getDefaultOption(this.currentSearchType).reduce((output:any,ele:any)=>{
  output =  `${output}${ele.field}=${ele.elements.reduce((result,filter:any)=>{
   result = `${result}${filter.query.join("")}`
    return result;
   },'')};`
    return output;
    },'');
  }

  getPgSearchUrlKey(){
    //Killswitch
    let  isPgNewApiEnabled = true, //Clean UP later this.featureFlagService.isFeatureFlagEnabled('enableNonBasePGToBasePG'), 
    pgSearchUrlKey = "SEARCH_PRODUCT_GROUP_OLD";
    if(isPgNewApiEnabled){
      pgSearchUrlKey = "SEARCH_PRODUCT_GROUP";
    }
    return pgSearchUrlKey;
  }
  getActionLogApi() {
    const activeTab = this.commonSearchService.batchActionActiveTab;
    return activeTab === CONSTANTS.UNIVERSAL ? "BATCHIMPORT_FILE_LOG_BPD_API" :  "BATCHIMPORT_FILE_LOG_API";
    }
  isStoreGroupApiEnabled() {
      let isSgNewApiEnabled = this.featureFlagService.isFeatureFlagEnabled('isStoreGroupApiEnabled'),
      sgSearchUrl = CONSTANTS.DYNA_STOREGROUP_API;

      if (isSgNewApiEnabled) {
      sgSearchUrl = STORE_GROUP_CONSTANTS.SEARCH_STORE_GROUP_NEW_API
      }
      return sgSearchUrl;
    }
    
  getUrls() {
    return {
      [CONSTANTS.TEMPLATE]:this.apiConfigService?.getConfigUrls(CONSTANTS.OFFER_REQ_SEARCH_API),
      [CONSTANTS.REQUEST]:this.apiConfigService?.getConfigUrls(CONSTANTS.OFFER_REQ_SEARCH_API),
      [CONSTANTS.OFFER]:this.apiConfigService?.getConfigUrls(CONSTANTS.OFFER_REQ_SEARCH_API),
      [CONSTANTS.PRODUCTMANAGEMENT]:this.apiConfigService?.getConfigUrls(CONSTANTS[this.getPgSearchUrlKey()]),
      [CONSTANTS.CUSTOMERMANAGEMENT]:this.apiConfigService?.getConfigUrls(CONSTANTS.OFFER_REQ_SEARCH_API),
      [CONSTANTS.STOREMANAGEMENT]: this.apiConfigService?.getConfigUrls(this.isStoreGroupApiEnabled()),
      [CONSTANTS.PROMOWEEKDETAILS]: this.apiConfigService.getConfigUrls(CONSTANTS.GENERATE_PROMO_WEEK_DETAILS),
      [CONSTANTS.ACTION_LOG]:this.apiConfigService?.getConfigUrls(CONSTANTS[this.getActionLogApi()]),
      [CONSTANTS.IMPORT_LOG_BPD]:this.apiConfigService?.getConfigUrls(CONSTANTS.BATCHIMPORT_FILE_LOG_BPD_API),
    }
  }
  removeQueryAndChipFromOptions(chip){
  // intentionally left empty
  }
  getQueryForInputAndFilter(saveQuery){
    return this.formQuery(this.filterOptions,this.inputOptions,this.defaultOptions,this.sortOptions,saveQuery);
  }
  removeParametersForTemplates(){
    let params = ['limit', 'sortBy', 'next', 'sid'];
    this.commonSearchService.defaultOption[this.getActiveCurrentSearchType()]?.forEach(param => {
      if(params.includes(param.field)){
        param.query = [];
        param.showChip = [];
        const element = param.elements[0];
        element.query = [];
        element.showChip = [];
        element.elements?.forEach(option => {
          option.query = [];
        });
      } 
  });
  }
    get queryForInputAndFilter(){
    return this.formQuery(this.filterOptions,this.inputOptions,this.defaultOptions,this.sortOptions);
    }
    get queryWithOrFilter(){
     return this.formQueryWithOrFilter();
    }
    get queryDefaultOptions(){
      return this.getFormDefaultOptions(this.currentSearchType);
     }
     get inputOptions(){
      return this.commonSearchService.getInputSearchOption(this.currentSearchType);
    }
    get sortOptions() {
        return this.commonSearchService.getSortOption(this.currentSearchType)
    }
    get filterOptions(){
      return this.commonSearchService.getFilterOption(this.currentSearchType);
    }
    get defaultOptions(){
      return this.commonSearchService.getDefaultOption(this.currentSearchType);
    }
    postDataForInputSearch(...obj:any){
     let [paginated = false, refreshFilter = false,featureFlagEnabled = false,isExpiredStatus= false] = obj
      if (paginated){
      this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.NEXT,1);
      this.commonSearchService.setQueryValueForDefaultOption(CONSTANTS.SID, null);

      }

            this.setQueryWithOrFilter(this.queryWithOrFilter);
            this.setFormQuery(this.queryForInputAndFilter);
        
    this.getDataForInputSearch(false,featureFlagEnabled,isExpiredStatus).subscribe((data:any)=>{
      this?.[`${this.currentRouter}BehaviorSubject`]?.next({data, paginated, refreshFilter});
    },(err) => {
      this?.[`${this.currentRouter}BehaviorSubject`]?.next(err);
    });
    }

 
  
  
    

 
    setQueryValForSortOption(obj) {
        const {field} = obj

        this.commonSearchService.sortOption[this.getActiveCurrentSearchType()]?.forEach(element => {
            if (element.field !== field) {
                      element.query = "";
                      element.select = false
            }
        })
    }
  getFormQuery(){
   return this.query;
  }
  getQueryWithOrFilter(){
    return this.queryWithOrFilters;
  }
  setQueryWithOrFilter(queryWithOrFilters){
    this.queryWithOrFilters = queryWithOrFilters;
  }
  applyExtraQueryParameters(){
    let query = "";
     const currentRouter = this.currentRouter;
     if(this.currentSearchType ===CONSTANTS.BPD){
      if(currentRouter === CONSTANTS.TEMPLATE){
        query =  `${CONSTANTS.IS_OFFER_TEMPLATE}=true`;
      }else if(currentRouter === CONSTANTS.REQUEST){
        query =  `${CONSTANTS.IS_OFFER_TEMPLATE}=false`;
      }
     }
     if(currentRouter === CONSTANTS.STOREMANAGEMENT && this.getFormQuery().includes("storeIdUser"))
     {
       const appendQuery = "=(System OR system OR OMS);";
        query = `notCreatedBy${appendQuery}notUpdatedBy${appendQuery}`;
     }
     return query;
    }  
    getDataForInputSearch(includeFacetCounts = false,isFeatureFlagEnabled = false,isExpiredChecked = false) {
      let isBPDExpiredChecked = isFeatureFlagEnabled && isExpiredChecked;
      const currentRouter = this.currentRouter
       const extraQueryParameters = this.applyExtraQueryParameters(),
              queryWithOrFilters = this.getWithOrQueryFilterWrap(isBPDExpiredChecked);
    let query = this.getFormQuery();

    let searchInput = {
      query:`${query}${extraQueryParameters}`,
      includeTotalCount: true,
      includeFacetCounts,
        };
      if(isBPDExpiredChecked)
      {
        searchInput['query'] = query;
        searchInput['showExpired'] = true; 
      }
      else{
        const isNewPgEnabled = true; //Clean UP later this.featureFlagService.isFeatureFlagEnabled("enableNonBasePGToBasePG");

        if(isNewPgEnabled && this.currentRouter === CONSTANTS.PRODUCTMANAGEMENT) {
          /* ACIP-233885 
                catergory_id, category_name are not being used by UI, hence can be removed from requiredFieldToFetch list
          */
          const itemsToRemove = ["categoryId", "categoryName"];
          const requiredFieldsToFetch = CONSTANTS.REQUIRED_FIELDS_TO_FETCH_FOR_PG.filter(field => !itemsToRemove.includes(field));
          searchInput = {...searchInput, ...{requiredFieldsToFetch}};
            } 
        if (queryWithOrFilters?.length) {
          searchInput['queryWithOrFilters'] = queryWithOrFilters;
        }
        }
        return this._http.post(this.getUrls()[this.currentRouter], searchInput);
    }
    
  getWithOrQueryFilterWrap(isExpiredStatusFilterSelectedForBPD = false){
    let query:any = this.getQueryWithOrFilter(), isHideExpiredStatusReqs = true;
   
    if(this.currentRouter === CONSTANTS.REQUEST){    
      const expiredStatusStr = `(${CONSTANTS.EXPIRED_STATUS_OR})`;
      
      if(query[0]){
        //Replace expired status with Completed status
        for(let i = 0; i <query.length; i++){
          if(query[i].includes(expiredStatusStr)){
            isHideExpiredStatusReqs = false;
            query[i] = query[i].replaceAll(expiredStatusStr,`(${CONSTANTS.COMPLETED_STATUS_OR})`)
          }
        }  
      }
      this.commonSearchService.setQueryOptionsForBpd({isHideExpiredStatusReqs,isExpiredStatusFilterSelectedForBPD});
      this.setFormQuery(this.queryForInputAndFilter);
      query = this.getQueryWithOrFilter();    
    }       
    return query;
  }

  getLastPeriodOptions() {
    let searchInput = { 
       startDate: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS-00:00'),
       numberOfWeeksToGenerate: 14 
    };
    return this._http.post(this.getUrls()[CONSTANTS.PROMOWEEKDETAILS], searchInput);
  }
  
}

import { Http<PERSON><PERSON>, <PERSON>tt<PERSON><PERSON><PERSON><PERSON> } from "@angular/common/http";
import { Injector } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { SearchOfferRequestService } from '@appRequestServices/search-offer-request.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { CommonRouteService } from '@appServices/common/common-route.service';
import { CommonSearchService } from '@appServices/common/common-search.service';
import { CommonService } from '@appServices/common/common.service';
import { FacetItemService } from '@appServices/common/facet-item.service';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { QueryGenerator } from '@appServices/common/queryGenerator.service';
import { PermissionsService } from '@appShared/albertsons-angular-authorization';
import { MsalService } from '@azure/msal-angular';
import { BehaviorSubject } from 'rxjs';
import { BaseInputSearchService } from './base-input-search.service';
import { BaseManagementService } from './base-management.service';
import { CONSTANTS } from "@appConstants/constants";

const FILTER_OPTIONS: { [key: string]: any } = {};

describe('BaseManagementService', () => {
  let service: BaseManagementService;
  const authServiceStub = () => ({ onUserDataAvailable: () => ({}) , getTokenString: () => ({}) });
  const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
  const permissionsServiceStub = () => ({
    loadPermissions: () => ({})
  });
  const searchOfferRequestServiceStub = () => ({
    fetchRegionsIds:() => ({}),
    mapRegionName: () => ({}),
    fetchProgramTypes:() => ({}),
    populateHomeFilterSearch: () => ({}),
    getOfferDetails: () => ({}),
    searchOfferRequest: () => ({
      subscribe: () => ({}),
      bind: () => ({}),
    }),
    getFacetCountsData: () => ({}),
    searchAllOfferRequest: () => ({
      subscribe: (f: (value: any) => void) => f({}),
      bind: () => ({}),
    }),
    currentOfferRequests: { subscribe: () => ({}) },
    paginationCriteria: () => ({}),
    myTasksObj: {
      myTasksText: ''
    }
  });
  const commonRouteServiceStub = () => ({ currentActivatedRoute: "request" });

  const commonServiceStub = () => ({ getHeaders: () => ({}) , passPaginationData$:new BehaviorSubject({}) });
  const facetItemServiceStub = () => ({
    setOfferFilter: () => ({}),
    sortProperties: () => ({}),
    populateStoreFacet: (
      facets: any,
      storesSearchCriteria: any,
      divisionRogCds: any
    ) => ({}),
    getFacetItems: () => ({}),
    populateFacetSearch: () => ({}),
    populateFacet: () => ({}),
    getdivsionStateFacetItems: () => ({}),
    sortDivisionRogCds: () => ({}),
    getOfferFilter: () => ({}),
    get templateProgramCodeSelected() {
      return 'BPD';
    }
  });
  const queryGeneratorStub = () => ({
    setQuery: () => ({}),
    pushParameters: () => ({}),
    getQuery: () => ({}),
    getQueryWithFilter: () => ({})
  });
  const initialDataServiceStub = () => ({
    getAppData: () => ({ batchImportConfig: { templatePath: {} } }),
    getConfigUrls: (bATCHIMPORT_TEMPLATE_FILE_API: any) => ({})
  });
  const commonSearchServiceStub = () => ({
    batchActionActiveTab: {},
    setActiveCurrentSearchType: () => ({}),
    get currentRouter() {
      return {};
    },
    setAllFilterOptions: () => ({}),
    setFilters: () => ({}),
    resetAllFilterOptions: (object: any) => ({}),
    getFilterOption: () => ({}),
    filterOption: {}
  });
  const baseInputSearchServiceStub = () => ({
    setActiveCurrentSearchType: () => ({}),
    createSubject: () => ({}),
    showChip: () => ({}),
    get currentRouter() {
      return {};
    },
    populateChipList: () => ({}),
    postDataForInputSearch: () => ({}),
    getActiveCurrentSearchType: () => ({})
  });
  const featureFlagServiceStub = () => ({
    assignFeatureFlag: () => ({}),
    isFeatureFlagEnabled: () => ({}),
    hasFlags: () => ({}),
    get isArchivalEnabled() {
      return true;
    }
  });
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        HttpClient,
        HttpHandler,
        { provide: AuthService, useFactory: authServiceStub },
        { provide: MsalService, useFactory: MsalServiceStub },
        { provide: FacetItemService, useFactory: facetItemServiceStub },
        { provide: PermissionsService, useFactory: permissionsServiceStub },
        { provide: CommonService, useFactory: commonServiceStub },
        { provide: CommonRouteService, useFactory: commonRouteServiceStub},
        {
          provide: SearchOfferRequestService,
          useFactory: searchOfferRequestServiceStub,
        },
        {
          provide: BaseInputSearchService,
          useFactory: baseInputSearchServiceStub
        },
        { provide: QueryGenerator, useFactory : queryGeneratorStub},
        { provide: InitialDataService, useFactory: initialDataServiceStub },
        { provide: CommonSearchService, useFactory: commonSearchServiceStub },
        { provide: FeatureFlagsService, useFactory: featureFlagServiceStub }
      ],
    });
    AppInjector.setInjector(TestBed.inject(Injector));
    service = TestBed.inject(BaseManagementService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should pass pagination data correctly', () => {
    const paginationData = { totalCount: 100, current: 1, sid: '123' };
    spyOn(service['commonService'].passPaginationData$, 'next');
    service.passPaginationData(paginationData);
    expect(service['commonService'].passPaginationData$.next).toHaveBeenCalledWith({
      totalCount: paginationData.totalCount,
      pageNumber: paginationData.current,
      sid: paginationData.sid,
    });
  });

  it('should get all template data correctly', () => {
    const templateList = [{ id: 1, name: 'Template 1' }, { id: 2, name: 'Template 2' }];
    spyOn(service.templatesData$, 'next');
    service.getAllTemplateData(templateList);
    expect(service.templatesData$.next).toHaveBeenCalledWith(templateList);
  });

  it('should fetch pagination data correctly when archival is enabled', () => {
    const paginatedData = { page: 1, size: 10 };
    spyOnProperty(service['featureFlagService'], 'isArchivalEnabled', 'get').and.returnValue(true);
    spyOn(service['baseInputSearchService'], 'postDataForInputSearch');
    service.fetchPaginationData(paginatedData);
    expect(service['baseInputSearchService'].postDataForInputSearch).toHaveBeenCalledWith(
      paginatedData,
      false,
      service['featureFlagService'].isOfferRequestArchivalEnabled,
      service['commonSearchService'].isShowExpiredInQuery
    );
  });

  it('should fetch pagination data correctly when archival is not enabled', () => {
    const paginatedData = { page: 1, size: 10 };
    spyOnProperty(service['featureFlagService'], 'isArchivalEnabled', 'get').and.returnValue(false);
    spyOn(service['baseInputSearchService'], 'postDataForInputSearch');
    service.fetchPaginationData(paginatedData);
    expect(service['baseInputSearchService'].postDataForInputSearch).toHaveBeenCalledWith(paginatedData);
  });

  it('should return the correct program code', () => {
    const programCode = 'testProgramCode';
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue(programCode);
    expect(service.programCode).toBe(programCode);
  });

  it('should return the correct headers', () => {
    const tokenString = 'testTokenString';
    spyOn(service['authService'], 'getTokenString').and.returnValue(tokenString);
    const headers = service.getHeaders();
    expect(headers).toEqual({
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": tokenString,
    });
  });

  it('should remove all elements from the query object', () => {
    const queryObject = [{ id: 1 }, { id: 2 }, { id: 3 }];
    service.removeSearchOptionObject(queryObject);
    expect(queryObject.length).toBe(0);
  });
  it('should return true when defaultTemplateFilters is already set', () => {
    service['defaultTemplateFilters'] = { filter: 'testFilter' };
    const result = service.getDefaultTemplateFilters();
    expect(result).toBeTrue();
  });

  it('should call getTemplateFilters when defaultTemplateFilters is not set', () => {
    spyOn(service as any, 'getTemplateFilters').and.returnValue({ filter: 'testFilter' });
    service['defaultTemplateFilters'] = null;
    const result = service.getDefaultTemplateFilters();
    expect(service.getTemplateFilters).toHaveBeenCalled();
    expect(result).toBeTrue();
  });
  
  it('should call fetch method of searchOfferRequestService with correct filter', async () => {
    const filter = { displayValue: 'Regions' };
    spyOn(service['searchOfferRequestService'], 'fetchRegionsIds').and.returnValue(Promise.resolve({}));
    const result = await service.getApiDataForFilter(filter);
    expect(service['searchOfferRequestService'].fetchRegionsIds).toHaveBeenCalled();
    expect(result).toEqual({});
  });

  it('should set additional properties correctly', () => {
    const offerTemplates = {
      totalCount: 100,
      sid: '123',
      current: 1,
      offerRequests: [{ id: 1, name: 'Offer 1' }, { id: 2, name: 'Offer 2' }]
    };
    service.setAdditionalProps(offerTemplates);
    expect(service.totalCount).toBe(offerTemplates.totalCount);
    expect(service.sid).toBe(offerTemplates.sid);
    expect(service.pageNumber).toBe(offerTemplates.current);
    expect(service.orData).toEqual(offerTemplates.offerRequests);
  });

  it('should populate filter list correctly', () => {
    const offerRequestFilters = {
      category: {
        1: { code: 'cat1', name: 'category first' },
        2: { code: 'cat2', name: 'category second' }
      },
      region: {
        3: { code: 'reg1', name: 'SNCA' },
        4: { code: 'reg2', name: 'SHAW' }
      }
    };
    const expectedFacetFilter = {
      category: [
        { id: 'cat1', value: 'category first', selected: false },
        { id: 'cat2', value: 'category second', selected: false }
      ],
      region: [
        { id: 'reg1', value: 'SNCA', selected: false },
        { id: 'reg2', value: 'SHAW', selected: false }
      ]
    };
    const result = service.populateFilterlist(offerRequestFilters);
    expect(result).toEqual(expectedFacetFilter);
  });

  it('should not populate template filter and chip when data is not provided', async () => {
    const result = await service.populateTemplateFilterAndChip({ data: null, paginated: false, refreshFilter: false });
    expect(result).toBeFalse();
  });

   it('should return true if the current route includes "template"', () => {
    commonRouteServiceStub().currentActivatedRoute = 'example/template/path';
  });

  it('should return false if the current route does not include "template"', () => {
    commonRouteServiceStub().currentActivatedRoute = 'example/other/path';
    expect(service.isTemplateRouteActivated).toBeFalse();
  });

  it('should return false if currentActivatedRoute is null or undefined', () => {
    commonRouteServiceStub().currentActivatedRoute = null;
    expect(service.isTemplateRouteActivated).toBeFalse();

    commonRouteServiceStub().currentActivatedRoute = undefined;
    expect(service.isTemplateRouteActivated).toBeFalse();
  });

  it('should populate template filter and chip correctly when data is provided', async () => {
    const data = {
      current: 1,
      sid: '123',
      totalCount: 100,
      offerRequests: [{ id: 1, name: 'Offer 1' }],
      paginate: true
    };
    const templateFilters = { filter: 'testFilter' };
    const facetFilter = { category: [{ id: 'cat1', value: 'category first', selected: false }] };

    spyOn(service, 'getTemplateFilters').and.returnValue(Promise.resolve(templateFilters));
    spyOn(service, 'populateFilterlist').and.returnValue(facetFilter);
    spyOn(service['searchOfferRequestService'], 'paginationCriteria');
    spyOn(service['searchOfferRequestService'], 'mapRegionName');
    spyOn(service['searchOfferRequestService'], 'populateHomeFilterSearch');
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('someRouter');
    service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`] = new BehaviorSubject(null);
    spyOn(service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`], 'next');

    await service.populateTemplateFilterAndChip({ data, paginated: false, refreshFilter: true });

    expect(service['searchOfferRequestService'].paginationCriteria).toHaveBeenCalledWith({
      totalCount: data.totalCount,
      pageNumber: data.current,
      sid: data.sid
    });
    expect(service['searchOfferRequestService'].mapRegionName).toHaveBeenCalledWith(data.offerRequests);
    expect(service['searchOfferRequestService'].populateHomeFilterSearch).toHaveBeenCalledWith({ facetFilter });
    expect(service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`].next).toHaveBeenCalledWith(service['commonSearchService'].getFilterOption(service.programCode));
    });

  it('should handle no offer requests correctly when populating template filter and chip', async () => {
    const data = {
      current: 1,
      sid: '123',
      totalCount: 100,
      offerRequests: [],
      paginate: true
    };

    spyOn(service, 'getTemplateFilters');
    spyOn(service, 'populateFilterlist');
    spyOn(service['searchOfferRequestService'], 'paginationCriteria');

    await service.populateTemplateFilterAndChip({ data, paginated: false, refreshFilter: true });

    expect(service['searchOfferRequestService'].paginationCriteria).toHaveBeenCalledWith({});
    expect(service.orData).toBeNull();
    expect(service.isNoResultsMsg).toBeTrue();
    expect(service.getTemplateFilters).not.toHaveBeenCalled();
    expect(service.populateFilterlist).not.toHaveBeenCalled();
  });

  it('should return false if data is null', async () => {
    const result = await service.populateTemplateFilterAndChip({ data: null, paginated: false, refreshFilter: false });
    expect(result).toBe(false);
  });

  it('should handle empty offerRequests', async () => {
    const data = { current: 1, sid: 'someSid', totalCount: 0, offerRequests: [], paginate: true };
    await service.populateTemplateFilterAndChip({ data: data, paginated: false, refreshFilter: false });
    expect(service.orData).toBeNull();
    expect(service.isNoResultsMsg).toBeTrue();
  });

  it('should handle offerRequests with length', async () => {
    const data = { current: 1, sid: 'someSid', totalCount: 10, offerRequests: [{ id: 1 }], paginate: true };
    const templateFilters = [{ name: 'filter1' }];
    spyOn(service, 'getTemplateFilters').and.returnValue(Promise.resolve(templateFilters));
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('someRouter');
    service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`] = new BehaviorSubject(null);
    spyOn(service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`], 'next');
    await service.populateTemplateFilterAndChip({ data: data, paginated: false, refreshFilter: false });
    expect(service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`].next).toHaveBeenCalled();
  });

  xit('should handle refreshFilter', async () => {
    const data = { current: 1, sid: 'someSid', totalCount: 10, offerRequests: [{ id: 1 }], paginate: true };
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('someRouter');
    service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`] = new BehaviorSubject(null);
    spyOn(service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`], 'next');
    await service.populateTemplateFilterAndChip({ data: data, paginated: false, refreshFilter: true });
    expect(service['baseInputSearchService'][`${service['baseInputSearchService'].currentRouter}FacetFilterBehaviorSubject`].next).toHaveBeenCalled();
  });

  it('should generate search option object correctly', () => {
    const queryObject: any[] = [];
    const element = {
      facetMapper: 'programCd',
      displayValue: 'Program Code',
      configMapper: 'config',
    };
    const programCode = 'testProgramCode';
    const apiData = { id: 1, name: 'API Data' };

    spyOn(service['baseInputSearchService'], 'showChip');

    service.generateSearchOptionObject(queryObject, element, programCode, apiData);
    const expectedEleObject = {
      field: element.facetMapper,
      label: element.displayValue,
      showChip: [],
      configMapper: element.configMapper,
      apiData: apiData,
      query: [programCode],
      elements: [
        {
          type: "",
          field: element.facetMapper,
          query: [programCode]
        }
      ]
    };
  });

  it('should generate search option object correctly without apiData', () => {
    const queryObject = [];
    const element = {
      facetMapper: 'programCd',
      displayValue: 'Program Code',
      configMapper: 'config',
    };
    const programCode = 'testProgramCode';

    spyOn(service['baseInputSearchService'], 'showChip');

    service.generateSearchOptionObject(queryObject, element, programCode);

    const expectedEleObject = {
      field: element.facetMapper,
      label: element.displayValue,
      showChip: [],
      configMapper: element.configMapper,
      apiData: null,
      query: [programCode],
      elements: [
        {
          type: "",
          field: element.facetMapper,
          query: [programCode]
        }
      ]
    };

    expect(service['baseInputSearchService'].showChip).toHaveBeenCalledWith(expectedEleObject);
    expect(queryObject).toContain(expectedEleObject);
  });

  it('should return defaultTemplateFilters if already set', async () => {
    service['defaultTemplateFilters'] = { filter: 'testFilter' };
    const result = await service.getTemplateFilters();
    expect(result).toEqual(service['defaultTemplateFilters']);
  });

  it('should set FILTER_OPTIONS correctly if not already set', async () => {
    service['defaultTemplateFilters'] = null;
    const appData = {
      offerRequestFilters: {
        testProgramCode: [
          { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
          { facetMapper: 'regionId', configMapper: 'api' }
        ]
      },
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } },
      offerRequestDeltaFilters: { categoryId: { 1: { code: 'cat1', name: 'Category 1' } }, regionId: {} }
    };
    const queryObject = [];
    const apiData = { 3: { code: 'reg1', name: 'Region 1' } };

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject').and.callThrough();
    spyOn(service, 'getApiDataForFilter').and.returnValue(Promise.resolve(apiData));

    const programCode = 'testProgramCode';
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue(programCode);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('someRouter');
    spyOnProperty(service, 'programCode', 'get').and.returnValue(programCode);

    if (appData.offerRequestFilters && appData.offerRequestFilters[programCode]) {
        for (const filter of appData.offerRequestFilters[programCode]) {
            const apiData = await service.getApiDataForFilter(filter);
            service.generateSearchOptionObject(queryObject, filter, programCode, apiData);
        }
        FILTER_OPTIONS[programCode] = queryObject;
        service['defaultTemplateFilters'] = FILTER_OPTIONS[programCode];
    }

  });

  it('should handle currentRouter as "request" and set filtersConfig correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'api' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } }
    };
    const queryObject = [];
    const apiData = { 3: { code: 'reg1', name: 'Region 1' } };

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject').and.callThrough();
    spyOn(service, 'getApiDataForFilter').and.returnValue(Promise.resolve(apiData));

    const result = await service.getTemplateFilters();
  });

  it('should handle currentRouter as "template" and set filtersConfig correctly', async () => {
    const appData = {
      offerTemplateFilters: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'api' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } }
    };
    const queryObject = [];
    const apiData = { 3: { code: 'reg1', name: 'Region 1' } };

    spyOn(service['initialDataService'], 'getAppData').and.callFake(() => appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('template');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject').and.callThrough();
    spyOn(service, 'generateSearchOptionObject').and.callThrough();
    spyOn(service, 'getApiDataForFilter').and.callFake(() => Promise.resolve(apiData));

    const result = await service.getTemplateFilters();

  });

  it('should handle empty filtersConfig correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } }
    };
    const queryObject = [];

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject');

    const result = await service.getTemplateFilters();

  });

  it('should handle filtersConfig with non-api configMapper correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'regionConfig' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } },
      regionConfig: { 2: { code: 'reg1', name: 'Region 1' } }
    };
    const queryObject = [];

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject');

    const result = await service.getTemplateFilters();
  });


  it('should handle filtersConfig with mixed configMapper correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'api' },
        { facetMapper: 'divisionId', configMapper: 'divisionConfig' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } },
      divisionConfig: { 4: { code: 'div1', name: 'Division 1' } }
    };
    const queryObject = [];
    const apiData = { 3: { code: 'reg1', name: 'Region 1' } };

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject').and.callThrough();
    spyOn(service, 'getApiDataForFilter').and.returnValue(Promise.resolve(apiData));

    const result = await service.getTemplateFilters();
  });

  it('should handle empty offerRequestDeltaApiFilters correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'api' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } }
    };
    const queryObject = [];

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject').and.callThrough();
    spyOn(service, 'getApiDataForFilter').and.returnValue(Promise.resolve(null));

    const result = await service.getTemplateFilters();
  });

  it('should handle filtersConfig with non-api configMapper or categoryId facetMapper correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'regionConfig' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } },
      regionConfig: { 2: { code: 'reg1', name: 'Region 1' } }
    };
    const queryObject = [];

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject');

    const result = await service.getTemplateFilters();
  });

  it('should handle filtersConfig with api configMapper correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'api' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } }
    };
    const queryObject = [];
    const apiData = { 3: { code: 'reg1', name: 'Region 1' } };

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject');
    spyOn(service, 'getApiDataForFilter').and.returnValue(Promise.resolve(apiData));

    const result = await service.getTemplateFilters();
  });
  
  it('should handle offerRequestDeltaApiFilters with length correctly', async () => {
    const appData = {
      offerRequestFiltersBPD: [
        { facetMapper: 'categoryId', configMapper: 'categoryConfig' },
        { facetMapper: 'regionId', configMapper: 'api' }
      ],
      categoryConfig: { 1: { code: 'cat1', name: 'Category 1' } }
    };
    const queryObject = [];
    const apiData = { 3: { code: 'reg1', name: 'Region 1' } };

    spyOn(service['initialDataService'], 'getAppData').and.returnValue(appData);
    spyOnProperty(service['baseInputSearchService'], 'currentRouter', 'get').and.returnValue('request');
    spyOnProperty(service['facetItemService'], 'templateProgramCodeSelected', 'get').and.returnValue('BPD');
    spyOn(service['commonSearchService'], 'getFilterOption').and.returnValue(queryObject);
    spyOn(service, 'removeSearchOptionObject');
    spyOn(service, 'generateSearchOptionObject').and.callThrough();
    spyOn(service, 'getApiDataForFilter').and.returnValue(Promise.resolve(apiData));

    const result = await service.getTemplateFilters();
  });
});
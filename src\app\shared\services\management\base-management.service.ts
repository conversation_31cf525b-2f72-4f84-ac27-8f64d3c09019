import { Injectable } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { SearchOfferRequestService } from "@appRequestServices/search-offer-request.service";
import { AuthService } from "@appServices/common/auth.service";
import { CommonRouteService } from "@appServices/common/common-route.service";
import { CommonSearchService } from "@appServices/common/common-search.service";
import { CommonService } from "@appServices/common/common.service";
import { FacetItemService } from "@appServices/common/facet-item.service";
import { FeatureFlagsService } from "@appServices/common/feature-flags.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BaseInputSearchService } from "@appServices/management/base-input-search.service";
import { FILTER_OPTIONS } from "@appShared/constants/search-options/filterSearch";
import { Subject } from "rxjs";


@Injectable({
  providedIn: "root",
})
export class BaseManagementService {
  public templatesData$ = new Subject();

  templateSearch_API: string = this.initialDataService.getConfigUrls(CONSTANTS.OFFER_REQ_SEARCH_API);
  appData;
 
  orData: any;
  pageNumber;
  totalCount;
  sid;
  isNoResultsMsg = false;
  filters: string[];
  facetChipShow = false;
  pgCodeCount: number = 0;
  search;
  showList = true;
  showGrid = false;
  expand = false;
  collapse = true;
  showFacets = false;
  items;
  defaultValue: string;
  defaultTemplateFilters: any;

  constructor(
    private initialDataService: InitialDataService,
    private authService: AuthService,
    private facetItemService: FacetItemService,
    private commonService: CommonService,
    public searchOfferRequestService: SearchOfferRequestService,
    public commonRouteService: CommonRouteService,
    private commonSearchService:CommonSearchService,
    private baseInputSearchService:BaseInputSearchService,
    private featureFlagService:FeatureFlagsService
  ) {
    this.appData = this.initialDataService.getAppData();
  }
  passPaginationData(data){
    const { totalCount, current, sid } = data;
    this.commonService.passPaginationData$.next({ totalCount, pageNumber: current, sid });
  }
  getAllTemplateData(templateList: any) {
    this.templatesData$.next(templateList);
  }
  fetchPaginationData(paginated) {
    if(this.featureFlagService.isArchivalEnabled)
      this.baseInputSearchService.postDataForInputSearch(paginated, false,(this.featureFlagService.isOfferRequestArchivalEnabled),this.commonSearchService.isShowExpiredInQuery);
    else
      this.baseInputSearchService.postDataForInputSearch(paginated);
  }
  get programCode(){
    return this.facetItemService.templateProgramCodeSelected;
  }
  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString(),
    };
    
  }
  get isTemplateRouteActivated() {
    return this.commonRouteService.currentActivatedRoute?.includes('template');
  }
  removeSearchOptionObject(queryObject){
     queryObject?.splice(0, queryObject.length);
  }
  
  generateSearchOptionObject(queryObject,element,programCode, apiData = null){
        let query = ["programCd","programCode"].includes(element.facetMapper)?[programCode]:[];
        const eleObject = {
          field:element.facetMapper,
          label:element.displayValue,
          showChip:[],
          configMapper:element.configMapper,
          apiData: element.configMapper === "api" ? apiData : null,
          query,
          elements:[{
           type:"",
           field:element.facetMapper,
           query
          }]
        };
      this.baseInputSearchService.showChip(eleObject);  
      queryObject.push(eleObject);
  }
  
  getDefaultTemplateFilters(){
    if(!this.defaultTemplateFilters){
      this.defaultTemplateFilters =  this.getTemplateFilters();
    }
    return true;
  }
    getApiDataForFilter(filter){
  return this.searchOfferRequestService?.[`fetch${filter.displayValue}Ids`]();
  }
   async getTemplateFilters(){
   if(this.defaultTemplateFilters){
    return this.defaultTemplateFilters;
   }
   const appData = this.appData;
   let currentRouter = this.baseInputSearchService.currentRouter, currentSearchType,filtersConfig;
   if(currentRouter ==="request"){
    currentSearchType = this.facetItemService.programCodeSelected;
    filtersConfig = appData[`offerRequestFilters${currentSearchType}`];
   }else if(currentRouter ==="template"){
    currentSearchType = this.facetItemService.templateProgramCodeSelected;
    filtersConfig = appData[`offerTemplateFilters`];//Need to change to template program codes
   }
    const queryObject = this.commonSearchService.getFilterOption(currentSearchType);

  this.removeSearchOptionObject(queryObject);
    const deltaApiFilters = {};
    let _index = 0;
    const offerRequestDeltaFilters =  filtersConfig?.reduce((output,ele,index) =>  {
      if(ele.configMapper !== 'api' || ele.facetMapper==="categoryId"){
        output[ele.facetMapper] = { ...appData[ele.configMapper] };
        this.generateSearchOptionObject(queryObject,ele,this.facetItemService.templateProgramCodeSelected);
      }else{
        deltaApiFilters[_index] = ele;
        _index = _index + 1;
        output[ele.facetMapper] = {};
      }
      return output;
     },{});
    const offerRequestDeltaApiFilters = await filtersConfig?.filter(ele=>ele.configMapper ==="api" && ele.facetMapper!=="categoryId").map(async (ele) =>  {
     // if(ele.configMapper === 'api'){
     return await this.getApiDataForFilter(ele);
      
    });
    if(offerRequestDeltaApiFilters?.length){
      const results = await Promise.all(offerRequestDeltaApiFilters);
      results.forEach((ele,index)=>{
        const filter = deltaApiFilters[index],
        data = ele || {};
        offerRequestDeltaFilters[filter.facetMapper] = data;
        this.generateSearchOptionObject(queryObject,filter,this.facetItemService.templateProgramCodeSelected, data);

      },{});
   
    }
   
    this.commonSearchService.filterOption[this.programCode] = queryObject;
    
    if(! FILTER_OPTIONS[this.baseInputSearchService.currentRouter][this.programCode][0]){
      FILTER_OPTIONS[this.baseInputSearchService.currentRouter][this.programCode] = queryObject;    
    }

    
    return {...offerRequestDeltaFilters};
  }

  setAdditionalProps(offerTemplates){
    const {totalCount,sid,current:pageNumber} = offerTemplates;
    this.totalCount = totalCount;
    this.sid = sid;
    this.pageNumber = pageNumber;
    this.orData = offerTemplates.offerRequests;
  }
  populateFilterlist(offerRequestFilters){
    const facetFilter =  Object.keys(offerRequestFilters).reduce((output,element)=>{
      const field = offerRequestFilters[element];
        const list = Object.keys(field).reduce((out:any,ele)=>{
          const value = field[ele];
          if(toString.call(value)==='[object Object]'){
            out.push({id:value.code,value:value.name,selected:false});
          }else{
            out.push({id:ele,value:field[ele],selected:false});
          }
          
          return out;
        },[]);
    output[element] = list;
   return output;
   },{});
   return facetFilter;
  }
  async populateTemplateFilterAndChip({data,paginated , refreshFilter}) {
    if (!data) {
      return false;
    }  
       const {current:pageNumber,sid,totalCount,offerRequests,paginate} = data;
        
        this.searchOfferRequestService.paginationCriteria({ totalCount,pageNumber, sid});
        if (offerRequests.length) {
          const templateFilters = await this.getTemplateFilters();
            this.searchOfferRequestService.mapRegionName(offerRequests);
             const facetFilter =  this.populateFilterlist(templateFilters);
             if(refreshFilter) {
              this.searchOfferRequestService.populateHomeFilterSearch({facetFilter });
             }
             this.baseInputSearchService?.[`${this.baseInputSearchService.currentRouter}FacetFilterBehaviorSubject`].
             next(this.commonSearchService.getFilterOption(this.programCode));
        } else {
          this.orData = null;
          this.isNoResultsMsg = true;
          this.searchOfferRequestService.paginationCriteria({});
      }

  }
}

import { TestBed } from '@angular/core/testing';
import { BaseQueryGenerator } from './base-query-gen.service';
import { QueryGenerator } from '../common/queryGenerator.service';
import { PersistenceSearchService } from './persistence-search.service';
import { CONSTANTS } from '@appConstants/constants';

describe('BaseQueryGenerator', () => {
  let service: BaseQueryGenerator;
  let queryGen: QueryGenerator;
  let persistanceService: PersistenceSearchService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        BaseQueryGenerator,
        { provide: QueryGenerator, useValue: jasmine.createSpyObj('QueryGenerator', ['getQuery', 'getQueryWithFilter', 'setQuery', 'setQueryWithFilter']) },
        { provide: PersistenceSearchService, useValue: jasmine.createSpyObj('PersistenceSearchService', ['isRouteToEditSummaryFromMgmntRoute', 'isRouteToMgmntPageFromEditSummaryRoute', 'isRouteComingFromValidRouteList']) }
      ]
    });

    service = TestBed.inject(BaseQueryGenerator);
    queryGen = TestBed.inject(QueryGenerator);
    persistanceService = TestBed.inject(PersistenceSearchService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should persist query and queryWithOrFilter', () => {
    const route = 'testRoute';
    (queryGen.getQuery as jasmine.Spy).and.returnValue('testQuery');
    (queryGen.getQueryWithFilter as jasmine.Spy).and.returnValue(['filter1', 'filter2']);

    service.persistedQueryObj[route] = { query: '', queryWithOrFilter: [], defaultSort: '' };
    service.persistQueryAndQueryWithFilter(route);

    expect(service.persistedQueryObj[route].query).toBe('testQuery');
    expect(service.persistedQueryObj[route].queryWithOrFilter).toEqual(['filter1', 'filter2']);
  });

  it('should get persisted query for route', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'testQuery',
      queryWithOrFilter: ['filter1', 'filter2'],
      defaultSort: 'defaultSort'
    };

    const result = service.getPersistedQueryForRoute(route);

    expect(result).toEqual({
      query: 'testQuery',
      queryWithOrFilter: ['filter1', 'filter2']
    });
  });

  it('should replace or add param to persisted query', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'testQuery;',
      queryWithOrFilter: [],
      defaultSort: 'defaultSort'
    };

    service.replaceOrAddParamToPersistedQry({ parameter: 'param', value: 'value' }, route);

    expect(service.persistedQueryObj[route].query).toBe('testQuery;param=value;');
  });

  it('should remove key from persisted query', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'param1=value1;param2=value2;',
      queryWithOrFilter: [],
      defaultSort: 'defaultSort'
    };

    service.removeKeyFromPersistQry('param1', route);

    expect(service.persistedQueryObj[route].query).toBe('param2=value2;');
  });

  it('should save query for routing', () => {
    const route = 'testRoute';
    const defaultSortByValue = 'defaultSort';
    spyOn(service, 'persistQueryAndQueryWithFilter');
    spyOn(service, 'replaceOrAddParamToPersistedQry');
    spyOn(service, 'removeMultiples');

    service.persistedQueryObj[route] = { query: '', queryWithOrFilter: [], defaultSort: '' };
    service.saveQueryForRouting(route, defaultSortByValue);

    expect(service.persistQueryAndQueryWithFilter).toHaveBeenCalledWith(route);
    expect(service.replaceOrAddParamToPersistedQry).toHaveBeenCalledWith({ parameter: CONSTANTS.SORT_BY, value: defaultSortByValue }, route);
    expect(service.removeMultiples).toHaveBeenCalledWith([CONSTANTS.NEXT, CONSTANTS.SID], route);
  });

  it('should set original query with persisted query', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'testQuery',
      queryWithOrFilter: ['filter1', 'filter2'],
      defaultSort: 'defaultSort'
    };

    service.setOriginalQueryWithPersistedQuery(route);

    expect(queryGen.setQuery).toHaveBeenCalledWith('testQuery');
    expect(queryGen.setQueryWithFilter).toHaveBeenCalledWith(['filter1', 'filter2']);
  });

  it('should check if key exists in query', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'param1=value1;param2=value2;',
      queryWithOrFilter: [],
      defaultSort: 'defaultSort'
    };

    const result = service.checkIfKeyExistIQuery(route, 'param1');

    expect(result).toEqual({ isExist: true, query: 'param1=value1;param2=value2;', index: 0 });
  });

  it('should get query param value', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'param1=(value1);param2=value2;',
      queryWithOrFilter: [],
      defaultSort: 'defaultSort'
    };

    const result = service.getQueryParamValue('param1', route);

    expect(result).toBe('value1');
  });

  it('should persist or clear query based on route info', () => {
    const routeInfo = {
      route: 'testRoute',
      validRouteList: [],
      currentUrl: '',
      mgmntRoute: '',
      previousUrl: ''
    };
    service.persistedQueryObj['testRoute'] = { defaultSort: 'defaultSort', query: '', queryWithOrFilter: [] };
    spyOn(service, 'saveQueryForRouting');
    spyOn(service, 'persistQueryAndQueryWithFilter');
    (persistanceService.isRouteToEditSummaryFromMgmntRoute as jasmine.Spy).and.returnValue(true);
    (persistanceService.isRouteToMgmntPageFromEditSummaryRoute as jasmine.Spy).and.returnValue(false);

    service.doPersistOnRoute(routeInfo);

    expect(service.saveQueryForRouting).toHaveBeenCalledWith('testRoute', 'defaultSort');
    expect(service.persistQueryAndQueryWithFilter).not.toHaveBeenCalled();
  });

  it('should remove multiple keys from persisted query', () => {
    const route = 'testRoute';
    service.persistedQueryObj[route] = {
      query: 'param1=value1;param2=value2;param3=value3;',
      queryWithOrFilter: [],
      defaultSort: 'defaultSort'
    };

    service.removeMultiples(['param1', 'param3'], route);

    expect(service.persistedQueryObj[route].query).toBe('param2=value2;');
  });

  it('should update persisted query', () => {
    const route = 'testRoute';
    const newQuery = 'newQuery';

    service.persistedQueryObj[route] = {
      query: 'oldQuery',
      queryWithOrFilter: [],
      defaultSort: 'defaultSort'
    };

    service.updatePersistQuery(newQuery, route);

    expect(service.persistedQueryObj[route].query).toBe(newQuery);
  });

  it('should check if route is coming from valid route list', () => {
    const previousUrl = 'previousUrl';
    const validRouteList = ['validRoute1', 'validRoute2'];

    (persistanceService.isRouteComingFromValidRouteList as jasmine.Spy).and.returnValue(true);

    const result = service.isRouteComingFromValidRouteList(previousUrl, validRouteList);

    expect(result).toBe(true);
    expect(persistanceService.isRouteComingFromValidRouteList).toHaveBeenCalledWith(previousUrl, validRouteList);
  });
});
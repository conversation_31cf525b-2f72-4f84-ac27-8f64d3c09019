/* Purpose: To dynamically form the querystring */
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { PERSISTED_QUERY_BASED_ON_ROUTE } from '../../constants/persistant-query';
import { QueryGenerator } from '../common/queryGenerator.service';
import { PersistenceSearchService } from './persistence-search.service';

@Injectable({
    providedIn: 'root'
})
export class BaseQueryGenerator {
    persistedQueryObj = PERSISTED_QUERY_BASED_ON_ROUTE;
    constructor(private queryGen: QueryGenerator, private persistanceService: PersistenceSearchService) {

    }
    /**
     * 
     * @param route The route for which you want to persist query and queryWithOrFilter
     * @param isReset set it to trye, if you want to clear persisted query and queryWithOrFilter.
     */
    persistQueryAndQueryWithFilter(route, isReset = false) {
        if(route) {
            this.persistedQueryObj[route]["query"] = isReset ? null : this.queryGen.getQuery();
            this.persistedQueryObj[route]["queryWithOrFilter"] = isReset ? [] : this.queryGen.getQueryWithFilter();
        }
    }
    /**
     * 
     * @param route The route for which you want to get persisted query and queryWithOrFilter
     * @returns The persisted query and queryWithOrFilter for given route
     */
    getPersistedQueryForRoute(route) {
        if(route) {
            return {
                query: this.persistedQueryObj?.[route]?.["query"],
                queryWithOrFilter: this.persistedQueryObj?.[route]["queryWithOrFilter"]
            }
        }
    }
    replaceOrAddParamToPersistedQry(param, route) {
        const { query } = route && this.getPersistedQueryForRoute(route);
        if (query && param) {
            this.removeKeyFromPersistQry(param.parameter, route);
            this.updatePersistQuery(`${this.persistedQueryObj[route]["query"]}${param.parameter}=${param.value};`, route)
        }
    }
    /**
     * 
     * @param route 
     * Based on route, it will persist the query
     * Then set the default sortBy Value
     * Then clear the next and sid param from query as we are not persisting pagination
     */
    saveQueryForRouting(route, defaultSortByValue) {
        this.persistQueryAndQueryWithFilter(route);
        this.replaceOrAddParamToPersistedQry({parameter: CONSTANTS.SORT_BY , value: defaultSortByValue }, route);
        this.removeMultiples([CONSTANTS.NEXT,CONSTANTS.SID], route);
    }
    removeKeyFromPersistQry(param: string, route) {
        const { isExist, query, index } = this.checkIfKeyExistIQuery(route, param);
        if (isExist && query) {
            const sliceStr = query?.slice(index);
            this.updatePersistQuery(`${query?.slice(0, index)}${sliceStr?.slice(sliceStr?.indexOf(';') + 1)}`, route);
        }
    }
    removeMultiples(keyList, route) {
        if (keyList?.length) {
            keyList.forEach(param => this.removeKeyFromPersistQry(param, route));
        }
    }
    updatePersistQuery(query, route) {
        if (query) {
            this.persistedQueryObj[route]["query"] = query;
        }
    }
    /**
     * 
     * @param route 
     * @param keyName 
     * @returns This will return whether the key exist in query or not
     */
    checkIfKeyExistIQuery(route, keyName) {
        if (keyName) {
            const { query } = this.getPersistedQueryForRoute(route),
            index = query?.indexOf(`${keyName}=`);
            return { isExist: index !== -1, query, index }
        }
    }
    getQueryParamValue(paramKey, route) {
        const {query} = this.getPersistedQueryForRoute(route);
        if(query) {
          const index = query.indexOf(paramKey),firstSlash = query.indexOf("(", index), cloaseSlash = query.indexOf(");", firstSlash);
          if (index !== -1) {
              return query.slice(firstSlash + 1, cloaseSlash);
          }
        }
        return '';
    }
    /**
     * 
     * @param routeInfo sampe -  {
     *  validRouteList- [],
     *  currentUrl - the url user is routing to,
     *  route - Offers / request / template,
     *  mgmntRoute - offer management or request managemnt routh path
     *  previousUrl - what is the previous url user routed
     * }
     * 
     * Based on routing info it will persist or clear the query
     * 
     */
    doPersistOnRoute(routeInfo) {
        const { route } = routeInfo,
        {defaultSort} = this.persistedQueryObj[route],
            isRouteToEditSummaryFromMgmntRoute = this.isRouteToEditSummary(routeInfo),
            isRouteToMgmntPageFromEditSummaryRoute = this.isRouteToMgmntRoute(routeInfo);
        if (isRouteToEditSummaryFromMgmntRoute) {
            this.saveQueryForRouting(route, defaultSort)
        }
        if (isRouteToMgmntPageFromEditSummaryRoute) {
            this.persistQueryAndQueryWithFilter(route, true);
        }
    }
    isRouteToEditSummary(routeInfo) {
        return this.persistanceService.isRouteToEditSummaryFromMgmntRoute(routeInfo);
    }
    isRouteToMgmntRoute(routeInfo) {
        return this.persistanceService.isRouteToMgmntPageFromEditSummaryRoute(routeInfo);
    }

    /**
     * 
     * @param route 
     * This function will get persisted query based on route and set in original query and queryWithOrFilter
     */
    setOriginalQueryWithPersistedQuery(route) {
        const {query, queryWithOrFilter} = this.getPersistedQueryForRoute(route);
        query && this.queryGen.setQuery(query);
        queryWithOrFilter?.length && this.queryGen.setQueryWithFilter(queryWithOrFilter);
    }
      /**
   * 
   * @param previousUrl 
   * @param validRouteList  - Is the array consist of route path of routes where a persistance should work - like offer edit/ summary
   * @returns This function will check whether user is comint to managemnt page from valid route list
   */
  isRouteComingFromValidRouteList(previousUrl, validRouteList) {
    return this.persistanceService.isRouteComingFromValidRouteList(previousUrl, validRouteList);
  }
}
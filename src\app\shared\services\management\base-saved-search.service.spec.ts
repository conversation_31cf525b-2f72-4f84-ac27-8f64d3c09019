
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { BaseSavedSearchService } from './base-saved-search.service';
import { AuthService } from '@appServices/common/auth.service';
import { InitialDataService } from '@appServices/common/initial.data.service';
import { AppInjector } from '@appServices/common/app.injector.service';
import { CONSTANTS } from '@appConstants/constants';
import { HttpClient } from '@angular/common/http';

class InitialDataServiceMock {
  getConfigUrls(param) {
    return `${param}-mock-url`;
  }
}

describe('BaseSavedSearchService', () => {
  let service: BaseSavedSearchService;
  let httpMock: HttpTestingController;
  let authServiceMock: jasmine.SpyObj<AuthService>;

  beforeEach(() => {
    authServiceMock = jasmine.createSpyObj('AuthService', ['getTokenString', 'getUserId']);
    authServiceMock.getTokenString.and.returnValue('mock-token');
  authServiceMock.getUserId.and.returnValue('mock-user-id');

  const   initialDataServiceMock = new InitialDataServiceMock();
     // Mock AppInjector
     spyOn(AppInjector, 'getInjector').and.returnValue({
      get: (token: any) => {
        if (token === AuthService) return authServiceMock;
        if (token === HttpClient) return TestBed.inject(HttpClient);
        if (token === InitialDataService) return initialDataServiceMock;
      },
    } as any);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        BaseSavedSearchService,
        { provide: AuthService, useValue: authServiceMock },
        { provide: InitialDataService, useValue: initialDataServiceMock },
      ],
    });

    service = TestBed.inject(BaseSavedSearchService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should call saveSavedSearch with correct payload', () => {
    const searchQuery = 'query';
    const name = 'test-name';
    const type = 'test-type';
    const savedSearchFlag = true;

    service.saveSavedSearch(searchQuery, name, type, savedSearchFlag).subscribe();

    const req = httpMock.expectOne(`${CONSTANTS.SAVED_SEARCH_OFFER_API}-mock-url`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      searchQuery,
      reqObj: { headers: jasmine.any(Object) },
      name,
      type,
      savedSearchFlag,
    });

    req.flush({});
  });

  it('should call fetchSavedSearch with correct query string', () => {
    const query = 'base-query;';
    const userType = 'S';

    service.fetchSavedSearch(query, userType).subscribe();

    const req = httpMock.expectOne(`${CONSTANTS.SAVED_SEARCH_RETRIEVE}-mock-url`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      reqObj: { headers: jasmine.any(Object) },
      query: 'base-query;flag=S;',
    });

    req.flush({});
  });

  it('should call deleteSaveSearch with correct payload', () => {

    const name = 'test-name';
    const type = 'test-type';

    service.deleteSaveSearch(name, type).subscribe();

    const req = httpMock.expectOne(`${CONSTANTS.SAVED_SEARCH_DELETE_OFFER_API}-mock-url`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      reqObj: { headers: jasmine.any(Object) },
      name,
      type,
    });

    req.flush({});
  });

  it('should call updateSavedSearch with correct payload', () => {
    const searchQuery = 'query';
    const name = 'test-name';
    const type = 'test-type';
    const savedSearchFlag = false;

    service.updateSavedSearch(searchQuery, name, type, savedSearchFlag).subscribe();

    const req = httpMock.expectOne(`${CONSTANTS.SAVED_SEARCH_OFFER_API}-mock-url`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual({
      searchQuery,
      reqObj: { headers: jasmine.any(Object) },
      name,
      type,
      savedSearchFlag,
    });

    req.flush({});
  });
});

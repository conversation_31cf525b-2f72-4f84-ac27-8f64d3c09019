import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CONSTANTS } from '@appConstants/constants';
import { AppInjector } from '@appServices/common/app.injector.service';
import { AuthService } from '@appServices/common/auth.service';
import { InitialDataService } from '@appServices/common/initial.data.service';

@Injectable({
  providedIn: 'root'
})
export class BaseSavedSearchService {
  public authService:AuthService;
  public http: HttpClient;
  public apiConfigService: InitialDataService;
  offerSearchAPI: string;
  saveSavedSearchAPI: string;
  deleteSaveSearchAPI: string;
  fetchSavedSearchAPI: string;
  headers;
  constructor() {
    const injector = AppInjector.getInjector();
    this.authService = injector.get(AuthService);
    this.http = injector.get(HttpClient);
    this.apiConfigService = injector.get(InitialDataService);
    this.saveSavedSearchAPI = this.apiConfigService.getConfigUrls(CONSTANTS.SAVED_SEARCH_OFFER_API);
    this.deleteSaveSearchAPI = this.apiConfigService.getConfigUrls(CONSTANTS.SAVED_SEARCH_DELETE_OFFER_API);
    this.fetchSavedSearchAPI = this.apiConfigService.getConfigUrls(CONSTANTS.SAVED_SEARCH_RETRIEVE);
    this.headers = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString()
    };
   }
  
  public saveSavedSearch(searchQuery, name, type,savedSearchFlag) {
   
    let payload = { searchQuery, reqObj: { headers:this.headers }, name, type, savedSearchFlag };
    return this.http.post(this.saveSavedSearchAPI, payload);
  }
  public fetchSavedSearch(query, userType) {
    const userId = this.authService.getUserId();
     query = `${query}${userType==='S'?'flag=S;':`createUserId=${userId}`}`
  
    return this.http.post(this.fetchSavedSearchAPI,  { reqObj: { headers:this.headers }, query});
  }

  public deleteSaveSearch(name, type) {
    const payload = { reqObj: { headers:this.headers },name, type };
    return this.http.post(this.deleteSaveSearchAPI, payload);
  }
  public updateSavedSearch(searchQuery, name, type,savedSearchFlag) {
   
    let payload = { searchQuery, reqObj: { headers:this.headers }, name, type, savedSearchFlag };
    return this.http.put(this.saveSavedSearchAPI, payload);
  }
}

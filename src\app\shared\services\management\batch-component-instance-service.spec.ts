import { TestBed } from '@angular/core/testing';
import { BatchComponentInstanceService } from './batch-component-instance-service';
import { OfferBatchActionsComponent } from '@appModules/offers/shared/components/batch-actions/offer-batch-action/offer-batch-action.component';
import { RequestBatchActionComponent } from '@appModules/request/shared/components/batch-actions/request-batch-action/request-batch-action.component';
import { TemplateBatchActionComponent } from '@appModules/templates/shared/components/template-batch-action/template-batch-action.component';

describe('BatchComponentInstanceService', () => {
    let service: BatchComponentInstanceService;

    beforeEach(() => {
        TestBed.configureTestingModule({});
        service = TestBed.inject(BatchComponentInstanceService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should set and get OfferBatchActionsComponent', () => {
        const component = service.getComponent('OfferBatchActionsComponent');
        expect(component).toBe(OfferBatchActionsComponent);
    });

    it('should set and get RequestBatchActionComponent', () => {
        const component = service.getComponent('RequestBatchActionComponent');
        expect(component).toBe(RequestBatchActionComponent);
    });

    it('should set and get TemplateBatchActionComponent', () => {
        const component = service.getComponent('TemplateBatchActionComponent');
        expect(component).toBe(TemplateBatchActionComponent);
    });

    it('should return undefined for non-existent component', () => {
        const component = service.getComponent('NonExistentComponent');
        expect(component).toBeUndefined();
    });

    it('should override existing component', () => {
        class NewComponent {}
        service.setComponent('OfferBatchActionsComponent', NewComponent);
        const component = service.getComponent('OfferBatchActionsComponent');
        expect(component).toBe(NewComponent);
    });
});
import { Injectable } from '@angular/core';
import { OfferBatchActionsComponent } from '@appModules/offers/shared/components/batch-actions/offer-batch-action/offer-batch-action.component';
import { RequestBatchActionComponent } from '@appModules/request/shared/components/batch-actions/request-batch-action/request-batch-action.component';
import { TemplateBatchActionComponent } from '@appModules/templates/shared/components/template-batch-action/template-batch-action.component';
@Injectable({
  providedIn: 'root'
})
export class BatchComponentInstanceService {

  componentInstances: Map<string, any> = new Map();

  constructor() {
    this.setComponent('OfferBatchActionsComponent', OfferBatchActionsComponent);
    this.setComponent('RequestBatchActionComponent', RequestBatchActionComponent);
    this.setComponent('TemplateBatchActionComponent', TemplateBatchActionComponent);
  }
  setComponent(name: string, component: any): void {
    this.componentInstances.set(name, component);
  }

  getComponent(name: string) {
    return this.componentInstances.get(name);
  }
}
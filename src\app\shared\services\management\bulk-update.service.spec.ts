import { TestBed } from "@angular/core/testing";
import { HttpClientTestingModule, HttpTestingController } from "@angular/common/http/testing";
import { BulkUpdateService } from "./bulk-update.service";
import { AuthService } from "@appServices/common/auth.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { CONSTANTS } from "@appConstants/constants";
import { HttpErrorResponse } from "@angular/common/http";

describe("BulkUpdateService", () => {
  let service: BulkUpdateService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let initialDataServiceSpy: jasmine.SpyObj<InitialDataService>;

  const mockSearchQuery = "test-query";

  beforeEach(() => {
    const authSpy = jasmine.createSpyObj("AuthService", ["getTokenString"]);
    const initialDataSpy = jasmine.createSpyObj("InitialDataService", ["getConfigUrls","getAppData"]);

    authSpy.getTokenString.and.returnValue("mock-token");
    
    initialDataSpy.getConfigUrls.and.callFake((key) => {
      const urls = {
        [CONSTANTS.BULK_ASSIGN]: "test/bulk/assign/",
        [CONSTANTS.BULK_SUBMIT]: "test/bulk/action/",
        [CONSTANTS.BULK_UNASSIGN]: "test/bulk/unassign/",
        [CONSTANTS.BULK_ASSIGN_DATE_UJ]: "bulkAssignDateUJ",
        [CONSTANTS.DEPLOY_DEFER_PUBLISH_OFFERS]: "test/bulk/deploy/defer/publish",
        [CONSTANTS.PRE_PUBLISH_OFFERS]: "test/bulk/pre/publish",
        [CONSTANTS.BATCH_EXPAND]: "test/bulk/expand",
        [CONSTANTS.BULK_ACTION_BPD_OR]: "test/bulk/action/bpd",
        [CONSTANTS.PRE_VALIDATE_TEMPLATES]: "validateTemplates",
      };
      return urls[key] || "";
    });

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        BulkUpdateService,
        { provide: AuthService, useValue: authSpy },
        { provide: InitialDataService, useValue: initialDataSpy },
      ],
    });

    service = TestBed.inject(BulkUpdateService);
    httpMock = TestBed.inject(HttpTestingController);
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    initialDataServiceSpy = TestBed.inject(InitialDataService) as jasmine.SpyObj<InitialDataService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
  });

  it("should return correct headers", () => {
    authServiceSpy.getTokenString.and.returnValue("mock-token");
    const headers = service.getHeaders();
    expect(headers).toEqual({
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token",
    });
  });

  it("should make a bulk action request", () => {
    const mockResponse = { success: true };
    const searchQuery = "test-query";
    const asyncAction = "test-action";
    const programCode = "TEST";
    
    service.bulkActionRequest(searchQuery, asyncAction, programCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = `${service.bulkSubmit}${programCode.toLowerCase()}/bulk/action/request`;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    req.flush(mockResponse);
  });

  it("should handle bulk action request error", () => {
    const searchQuery = "test-query";
    const asyncAction = "test-action";
    const programCode = "TEST";
    const errorMessage = "Network error";
    
    service.bulkActionRequest(searchQuery, asyncAction, programCode).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain(errorMessage);
      }
    );

    const expectedUrl = `${service.bulkSubmit}${programCode.toLowerCase()}/bulk/action/request`;
    const req = httpMock.expectOne(expectedUrl);
    req.error(new ErrorEvent("Network error"), { status: 0, statusText: "Network error" });
  });

  it("should make a bulk action BPD request", () => {
    const mockResponse = { success: true };
    const searchQuery = "test-query";
    const asyncAction = "test-action";
    const programCode = "TEST";
    const jobType = "test-job";

    service.bulkActionBpdRequest(searchQuery, asyncAction, programCode, jobType).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = service.bulkActionBPDAPI;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      searchQuery,
      jobType,
      jobSubType: asyncAction,
      programCodeType: programCode,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should make a bulk assign users request", () => {
    const mockResponse = { success: true };
    const assignUsers = ["user1", "user2"];
    const searchQuery = "test-query";

    service.bulkAssignUsers(assignUsers, searchQuery).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = service.bulkAssignUser;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("PUT");
    expect(req.request.body).toEqual({
      searchQuery,
      assignUsers,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle bulk assign users request error", () => {
    const assignUsers = ["user1", "user2"];
    const searchQuery = "test-query";
    const errorMessage = "Network error";

    service.bulkAssignUsers(assignUsers, searchQuery).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const expectedUrl = service.bulkAssignUser;
    const req = httpMock.expectOne(expectedUrl);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a bulk assign users UJ request", () => {
    const mockResponse = { success: true };
    const assignUsers = ["user1", "user2"];
    const searchQuery = "test-query";
    const jobType = "test-job";
    const jobSubType = "test-sub";
    const programCode = "TEST";

    service.bulkAssignUsersUJ(assignUsers, searchQuery, jobType, jobSubType, programCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = service.bulkJobsUJ;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      jobType,
      jobSubType,
      programCodeType: programCode,
      searchQuery,
      assignUsers,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should make a bulk assigned dates request", () => {
    const mockResponse = { success: true };
    const assignDates = ["2025-02-10", "2025-02-15"];
    const searchQuery = "test-query";

    service.bulkAssignedDates(assignDates, searchQuery).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkAssignDates);
    expect(req.request.method).toBe("PUT");
    expect(req.request.body).toEqual({
      searchQuery,
      assignDates,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should make a bulk unassign users request", () => {
    const mockResponse = { success: true };
    const unAssignUserTypes = ["userType1", "userType2"];
    const searchQuery = "test-query";

    service.bulkUnAssignUsers(unAssignUserTypes, searchQuery).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkUnAssignUser);
    expect(req.request.method).toBe("PUT");
    expect(req.request.body).toEqual({
      searchQuery,
      unAssignUserTypes,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle bulk unassign users request error", () => {
    const unAssignUserTypes = ["userType1", "userType2"];
    const searchQuery = "test-query";
    const errorMessage = "Network error";

    service.bulkUnAssignUsers(unAssignUserTypes, searchQuery).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.bulkUnAssignUser);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should return an observable from hideApiErrorOnRequestHome", (done) => {
    const testValue = true;
    
    service.hideApiErrorOnRequestHome().subscribe(value => {
      expect(value).toBe(testValue);
      done();
    });

    service.hideApiErrorOnRequestHome$.next(testValue);
  });

  it("should make a bulk unassign users UJ request", () => {
    const mockResponse = { success: true };
    const unAssignUserTypes = ["userType1", "userType2"];
    const searchQuery = "test-query";
    const jobType = "test-job";
    const jobSubType = "test-sub";
    const programCode = "TEST";

    service.bulkUnAssignUsersUJ(unAssignUserTypes, searchQuery, jobType, jobSubType, programCode)
      .subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      jobType,
      jobSubType,
      programCodeType: programCode,
      searchQuery,
      unAssignUserTypes,
      reqObj: { headers: service.getHeaders() }
    });

    req.flush(mockResponse);
  });

  it("should return an observable from hideApiErrorOnRequestHome", (done) => {
    service.hideApiErrorOnRequestHome().subscribe(value => {
      expect(value).toBe(true);
      done();
    });

    service.hideApiErrorOnRequestHome$.next(true);
  });

  it("should make a bulk assigned dates UJ request", () => {
    const mockResponse = { success: true };
    const assignDates = ["2025-02-10", "2025-02-15"];
    const searchQuery = "test-query";
    const jobType = "test-job";
    const jobSubType = "test-sub";
    const programCode = "TEST";

    service.bulkAssignedDatesUJ(assignDates, searchQuery, jobType, jobSubType, programCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkAssignDatesUJ);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      jobType,
      jobSubType,
      programCodeType: programCode,
      searchQuery,
      assignDates,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle bulk assigned dates UJ request error", () => {
    const assignDates = ["2025-02-10", "2025-02-15"];
    const searchQuery = "test-query";
    const jobType = "test-job";
    const jobSubType = "test-sub";
    const programCode = "TEST";
    const errorMessage = "Network error";

    service.bulkAssignedDatesUJ(assignDates, searchQuery, jobType, jobSubType, programCode).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.bulkAssignDatesUJ);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should handle defer deploy publish batch action request error", () => {
    const searchQuery = "test-query";
    const asyncAction = "test-action";
    const programCodeType = "TEST";
    const errorMessage = "Network error";

    service.deferDeployPublishBatchAction(searchQuery, asyncAction, programCodeType).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.deployDeferPublishOffer);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a bulk action offer request", () => {
    const mockResponse = { success: true };
    const searchQuery = "test-query";
    const asyncAction = "test-action";
    const programCodeType = "TEST";
    const jobType = "test-job";

    service.bulkActionOffer(searchQuery, asyncAction, programCodeType, jobType).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.deployDeferPublishOffer);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      searchQuery,
      asyncAction,
      programCodeType,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle bulk action offer request error", () => {
    const searchQuery = "test-query";
    const asyncAction = "test-action";
    const programCodeType = "TEST";
    const jobType = null;
    const errorMessage = "Network error";

    service.bulkActionOffer(searchQuery, asyncAction, programCodeType, jobType).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.deployDeferPublishOffer);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a preCheckBatch request", () => {
    const mockResponse = { success: true };
    const searchQuery = "test-query";
    const bulkProcessAction = "test-action";

    service.preCheckBatch(searchQuery, bulkProcessAction).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.prePublishOffer);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      searchQuery,
      bulkProcessAction,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle preCheckBatch request error", () => {
    const searchQuery = "test-query";
    const bulkProcessAction = "test-action";
    const errorMessage = "Network error";

    service.preCheckBatch(searchQuery, bulkProcessAction).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.prePublishOffer);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a preCheckBatchExpand request", () => {
    const mockResponse = { success: true };
    const searchQuery = "test-query";

    service.preCheckBatchExpand(searchQuery).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.batchExpand);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      searchQuery,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle preCheckBatchExpand request error", () => {
    const searchQuery = "test-query";
    const errorMessage = "Network error";

    service.preCheckBatchExpand(searchQuery).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.batchExpand);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a registerBatchExpand request", () => {
    const mockResponse = { success: true };
    const payload = { key1: "value1", key2: "value2" };

    service.registerBatchExpand(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkActionBPDAPI);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      ...payload,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle registerBatchExpand request error", () => {
    const payload = { key1: "value1", key2: "value2" };
    const errorMessage = "Network error";

    service.registerBatchExpand(payload).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const req = httpMock.expectOne(service.bulkActionBPDAPI);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a publish bulk offer request", () => {
    const mockResponse = { success: true };
    const id = "12345";
    const type = "publish";

    service.publishBulkOffers(id, type).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = `${service.publishBulkOffer}?requestId=${id}`;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      requestId: id,
      bulkProcessAction: type,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle publish bulk offer request error", () => {
    const id = "12345";
    const type = "publish";
    const errorMessage = "Network error";

    service.publishBulkOffers(id, type).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain(errorMessage);
      }
    );

    const expectedUrl = `${service.publishBulkOffer}?requestId=${id}`;
    const req = httpMock.expectOne(expectedUrl);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });

  it("should make a publish bulk offers UJ request", () => {
    const mockResponse = { success: true };
    const programCode = "TEST";
    const query = "test-query";
    const type = "MF_BATCH_CANCEL";

    service.publishBulkOffersUJ(programCode, query, type).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = service.bulkJobsUJ;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      jobType: "O",
      jobSubType: type,
      programCodeType: programCode,
      searchQuery: query,
      removeForAll: true,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });

  it("should handle publish bulk offers UJ request error", () => {
    const programCode = "TEST";
    const query = "test-query";
    const type = "MF_BATCH_CANCEL";
    const errorMessage = "Network error";

    service.publishBulkOffersUJ(programCode, query, type).subscribe(
      () => fail("Expected error, but got success response"),
      (error) => {
        expect(error.message).toContain("Network error");
      }
    );

    const expectedUrl = service.bulkJobsUJ;
    const req = httpMock.expectOne(expectedUrl);
    req.error(new ErrorEvent("Network error"), { status: 500, statusText: errorMessage });
  });
  
  it("should send the correct POST request to batchDeployPublishOffers", () => {
    const mockResponse = { success: true };
    const query = "test-query";
    const asyncActionKey = "ASYNC_KEY";
    const pcSelected = "PC_SELECTED";
    const jobType = "O";

    service.batchDeployPublishOffers(query, asyncActionKey, pcSelected, jobType).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = service.bulkActionBPDAPI;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      jobType: jobType,
      jobSubType: asyncActionKey,
      programCodeType: pcSelected,
      searchQuery: query,
      reqObj: { headers: service.getHeaders() }
    });
    req.flush(mockResponse);
  });
  it("should include headers in the POST request", () => {
    const mockResponse = { success: true };
    const query = "test-query";
    const asyncActionKey = "ASYNC_KEY";
    const pcSelected = "PC_SELECTED";
    const jobType = "O";

    const expectedHeaders = {
      "X-Albertsons-userAttributes": "user-attributes",
      "X-Albertsons-Client-ID": "client-id",
      "content-type": "application/json"
    };

    const getHeadersSpy = spyOn(service, 'getHeaders').and.returnValue(expectedHeaders);

    service.batchDeployPublishOffers(query, asyncActionKey, pcSelected, jobType).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedUrl = service.bulkActionBPDAPI;
    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe("POST");
    expect(req.request.body).toEqual({
      jobType: jobType,
      jobSubType: asyncActionKey,
      programCodeType: pcSelected,
      searchQuery: query,
      reqObj: { headers: expectedHeaders }
    });
    req.flush(mockResponse);
  });

  it("should handle error response", () => {
    const query = "test-query";
    const asyncActionKey = "ASYNC_KEY";
    const pcSelected = "PC_SELECTED";
    const jobType = "O";

    const errorResponse = { message: "Server error" };

    service.batchDeployPublishOffers(query, asyncActionKey, pcSelected, jobType).subscribe(
      () => fail("expected an error, not response"),
      (error: HttpErrorResponse) => {
        expect(error instanceof HttpErrorResponse).toBe(true);
        expect(error.status).toBe(500);
        expect(error.statusText).toBe('Server Error');
        expect(error.error).toEqual(errorResponse);
      }
    );

    const expectedUrl = service.bulkActionBPDAPI;
    const req = httpMock.expectOne(expectedUrl);
    req.flush(errorResponse, { status: 500, statusText: 'Server Error' });
  });

  it("should send POST request with the correct payload for templatePreBatch", () => {
    const mockResponse = { message: 'success' };
  
    const mockTokenString = "userId=testUser;firstName=John;lastName=Doe;email=<EMAIL>";

    authServiceSpy.getTokenString.and.returnValue(mockTokenString);

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": mockTokenString
    };

    service.templatePreBatch(mockSearchQuery).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedPayload = {
      searchQuery: mockSearchQuery,
      reqObj: { headers: expectedHeaders }
    };

    const req = httpMock.expectOne(service.preBulkValidateForTemplate);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for updateTestingOffers", () => {
    const mockResponse = { message: 'success' };
    const mockData = {
      offerId: 123,
      offerDetails: "Test offer details"
    };
  
    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    service.updateTestingOffers(mockData).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedPayload = {
      ...mockData,
      reqObj: { headers: expectedHeaders }
    };

    const req = httpMock.expectOne(service.publishBulkOffer);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for updateTestingOffersUJ", () => {
    const mockResponse = { message: 'success' };

    const mockSearchQuery = "test-query";
    const mockPayload = { data: "sample payload" };
    const mockJobType = "job-type";
    const mockJobSubType = "job-sub-type";
    const mockProgramCode = "program-code";

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    const expectedPayload = {
      jobType: mockJobType,
      jobSubType: mockJobSubType,
      programCodeType: mockProgramCode,
      searchQuery: mockSearchQuery,
      requestPayload: JSON.stringify(mockPayload),
      reqObj: { headers: expectedHeaders }
    };

    service.updateTestingOffersUJ(mockSearchQuery, mockPayload, mockJobType, mockJobSubType, mockProgramCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for publishAdInEmail", () => {
    const mockResponse = { message: 'success' };

    const mockRequestId = "123";
    const mockBulkProcessAction = "bulk-action";
    const mockRequestPayload = { data: "sample email ad payload" };

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    const expectedPayload = {
      requestId: mockRequestId,
      bulkProcessAction: mockBulkProcessAction,
      requestPayload: JSON.stringify(mockRequestPayload),
      reqObj: { headers: expectedHeaders }
    };

    service.publishAdInEmail(mockRequestId, mockBulkProcessAction, mockRequestPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.publishBulkOffer);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for publishAdInEmailUJ", () => {
    const mockResponse = { message: 'success' };

    const mockPayload = { data: "sample ad payload" };

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    const expectedPayload = {
      ...mockPayload,
      reqObj: { headers: expectedHeaders }
    };

    service.publishAdInEmailUJ(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);
    
    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for updateBulkPod", () => {
    const mockResponse = { message: 'success' };

    const mockSearchQuery = "test-query";
    const mockPodDetailsForm = { podName: "testPod", podStatus: "active" };

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    const expectedPayload = {
      searchQuery: mockSearchQuery,
      podDetails: mockPodDetailsForm,
      reqObj: { headers: expectedHeaders }
    };

    service.updateBulkPod(mockSearchQuery, mockPodDetailsForm).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.updateBulkPodAPI);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for updateBulkPodUJ", () => {
    const mockResponse = { message: 'success' };

    const mockPayload = { podId: 123, podName: "testPod" };

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    const expectedPayload = {
      ...mockPayload,
      reqObj: { headers: expectedHeaders }
    };

    service.updateBulkPodUJ(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it("should send POST request with the correct payload for updateBulkEvents", () => {
    const mockResponse = { message: 'success' };

    const mockPayload = { eventId: 456, eventName: "testEvent" };

    authServiceSpy.getTokenString.and.returnValue("mock-token");

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token"
    };

    const expectedPayload = {
      ...mockPayload,
      reqObj: { headers: expectedHeaders }
    };

    service.updateBulkEvents(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.publishBulkOffer);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should send POST request for updateBulkEventsUJ with correct payload', () => {
    const mockResponse = { message: 'success' };
    const mockPayload = { key: 'value' };

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token",
    };

    service.updateBulkEventsUJ(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedPayload = {
      ...mockPayload,
      reqObj: { headers: expectedHeaders }
    };

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should send POST request for updateBulkTerminals with correct payload', () => {
    const mockResponse = { message: 'success' };
    const mockPayload = { key: 'value' };

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token",
    };

    service.updateBulkTerminals(mockPayload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedPayload = {
      ...mockPayload,
      reqObj: { headers: expectedHeaders }
    };

    const req = httpMock.expectOne(service.publishBulkOffer);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should send POST request for updateBulkTerminalsUJ with correct payload', () => {
    const mockResponse = { message: 'success' };
    const searchQuery = 'search-query';
    const mockPayload = { key: 'value' };
    const jobType = 'O';
    const jobSubType = 'SUB_TYPE';
    const programCode = 'PROGRAM_CODE';

    const expectedHeaders = {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": "mock-token",
    };

    service.updateBulkTerminalsUJ(searchQuery, mockPayload, jobType, jobSubType, programCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const expectedPayload = {
      jobType,
      jobSubType,
      programCodeType: programCode,
      searchQuery,
      requestPayload: mockPayload,
      reqObj: { headers: expectedHeaders }
    };

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });
  
  it('should send POST request for doBatchCopyOR with correct payload', () => {
    const payload = { key: 'value' };  // Example payload
    const programCode = 'TEST';
    const expectedUrl = `${service.bulkSubmit}${programCode.toLowerCase()}/bulk/action/request`;
  
    // Mock response
    const mockResponse = { success: true };

    service.doBatchCopyOR(payload, programCode).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('POST');
    const headers = service.getHeaders();
    const expectedReqPayload = {
      ...payload,
      reqObj: { headers }
    };

    expect(req.request.body).toEqual(expectedReqPayload);

    req.flush(mockResponse);
  });

  it('should handle error response for doBatchCopyOR', () => {
    const payload = { key: 'value' };
    const programCode = 'TEST';
    const errorResponse = { message: 'Server error' };

    service.doBatchCopyOR(payload, programCode).subscribe(
      () => fail('expected an error, not response'),
      error => {
        expect(error.status).toBe(500);
        expect(error.error).toEqual(errorResponse);
      }
    );

    const req = httpMock.expectOne(`${service.bulkSubmit}${programCode.toLowerCase()}/bulk/action/request`);
    req.flush(errorResponse, { status: 500, statusText: 'Server Error' });
  });
  
  it('should send POST request for doBatchCopyBPDOR with correct payload', () => {
    const payload = { key: 'value' };
    const mockResponse = { success: true };
    const expectedUrl = service.bulkActionBPDAPI;

    spyOn(service, 'getHeaders').and.returnValue({
      "X-Albertsons-userAttributes": "mock-token",
      "X-Albertsons-Client-ID": "OMS",
      "content-type": "application/json"
    });

    const expectedPayload = {
      ...payload,
      reqObj: {
        headers: {
          "X-Albertsons-userAttributes": "mock-token",
          "X-Albertsons-Client-ID": "OMS",
          "content-type": "application/json"
        }
      }
    };

    service.doBatchCopyBPDOR(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should send POST request for doBatchTemplateUpdateStatus with correct payload', () => {
    const payload = { key: 'value' };
    const mockResponse = { success: true };
    const expectedUrl = service.bulkActionBPDAPI;

    spyOn(service, 'getHeaders').and.returnValue({
      "X-Albertsons-userAttributes": "mock-token",
      "X-Albertsons-Client-ID": "OMS",
      "content-type": "application/json"
    });

    const expectedPayload = {
      ...payload,
      reqObj: {
        headers: {
          "X-Albertsons-userAttributes": "mock-token",
          "X-Albertsons-Client-ID": "OMS",
          "content-type": "application/json"
        }
      }
    };

    service.doBatchTemplateUpdateStatus(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should send POST request for doBatchCopySC with correct payload', () => {
    const payload = { key: 'value' };
    const mockResponse = { success: true };
    const expectedUrl = service.bulkActionBPDAPI;

    spyOn(service, 'getHeaders').and.returnValue({
      "X-Albertsons-userAttributes": "mock-token",
      "X-Albertsons-Client-ID": "OMS",
      "content-type": "application/json"
    });

    const expectedPayload = {
      ...payload,
      reqObj: {
        headers: {
          "X-Albertsons-userAttributes": "mock-token",
          "X-Albertsons-Client-ID": "OMS",
          "content-type": "application/json"
        }
      }
    };

    service.doBatchCopySC(payload).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should send POST request for doRegionalCopy with correct payload', () => {
    const jobType = 'copy';
    const jobSubType = 'regional';
    const programCode = 'TEST';
    const searchQuery = 'region-search-query';
    const regionIds = ['region1', 'region2'];
    const mockResponse = { success: true };

    spyOn(service, 'getHeaders').and.returnValue({
      "X-Albertsons-userAttributes": "mock-token",
      "X-Albertsons-Client-ID": "OMS",
      "content-type": "application/json"
    });

    const expectedPayload = {
      jobType: jobType,
      jobSubType: jobSubType,
      programCodeType: programCode,
      searchQuery: searchQuery,
      regionIds: regionIds,
      reqObj: {
        headers: {
          "X-Albertsons-userAttributes": "mock-token",
          "X-Albertsons-Client-ID": "OMS",
          "content-type": "application/json"
        }
      }
    };

    service.doRegionalCopy(jobType, jobSubType, programCode, searchQuery, regionIds).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(service.bulkJobsUJ);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(expectedPayload);

    req.flush(mockResponse);
  });

  it('should return true if action is in featureFlagsUJ', () => {
    const action = 'some-action';
    const appData = { featureFlagsUJ: ['some-action', 'another-action'] };

    // Mock the getAppData method to return appData
    initialDataServiceSpy.getAppData.and.returnValue(appData);

    const result = service.checkIfActionEnabledForUniversalJob(action);

    // Assert that the function returns true when the action is found
    expect(result).toBe(true);
  });

  it('should return false if action is not in featureFlagsUJ', () => {
    const action = 'some-action';
    const appData = { featureFlagsUJ: ['another-action'] };

    // Mock the getAppData method to return appData
    initialDataServiceSpy.getAppData.and.returnValue(appData);

    const result = service.checkIfActionEnabledForUniversalJob(action);

    // Assert that the function returns false when the action is not found
    expect(result).toBe(false);
  });

  it('should return false if featureFlagsUJ is empty', () => {
    const action = 'some-action';
    const appData = { featureFlagsUJ: [] };

    // Mock the getAppData method to return appData
    initialDataServiceSpy.getAppData.and.returnValue(appData);

    const result = service.checkIfActionEnabledForUniversalJob(action);

    // Assert that the function returns false when the featureFlagsUJ array is empty
    expect(result).toBe(false);
  });

  it('should return false if appData is undefined', () => {
    const action = 'some-action';

    // Mock the getAppData method to return undefined
    initialDataServiceSpy.getAppData.and.returnValue(undefined);

    const result = service.checkIfActionEnabledForUniversalJob(action);

    // Assert that the function returns false when appData is undefined
    expect(result).toBe(false);
  });

  it('should return false if action is undefined or null', () => {
    const appData = { featureFlagsUJ: ['some-action', 'another-action'] };

    // Mock the getAppData method to return appData
    initialDataServiceSpy.getAppData.and.returnValue(appData);

    // Test when action is undefined
    let result = service.checkIfActionEnabledForUniversalJob(undefined);
    expect(result).toBe(false);

    // Test when action is null
    result = service.checkIfActionEnabledForUniversalJob(null);
    expect(result).toBe(false);
  });

  it('should return false if action is an empty string', () => {
    const action = '';
    const appData = { featureFlagsUJ: ['some-action', 'another-action'] };

    // Mock the getAppData method to return appData
    initialDataServiceSpy.getAppData.and.returnValue(appData);

    const result = service.checkIfActionEnabledForUniversalJob(action);

    // Assert that the function returns false for an empty string as action
    expect(result).toBe(false);
  });

});

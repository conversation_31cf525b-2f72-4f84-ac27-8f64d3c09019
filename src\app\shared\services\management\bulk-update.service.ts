import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { CONSTANTS } from "@appConstants/constants";
import { AuthService } from "@appServices/common/auth.service";
import { InitialDataService } from "@appServices/common/initial.data.service";
import { BehaviorSubject, Subject } from "rxjs";

@Injectable({
  providedIn: "root"
})
export class BulkUpdateService {
  requestIdsListSelected$ = new BehaviorSubject([]);
  createdAppIds$ = new BehaviorSubject([]);
  offerIdsListSelected$ = new BehaviorSubject([]);

  offerBulkSelection = new BehaviorSubject("");
  bulkSelectionForOffers = new BehaviorSubject("");
  isAllBatchSelected = new BehaviorSubject("");
  isSelectionReset = new BehaviorSubject(false)
  reqIdsOnPage;
  bulkSelection;
  userTypeArray: any = [];
  requestIdArr: any = [];
  createdAppIds: any = [];
  deliveryChannelArr:any = [];
  showDisplayEndDate:boolean = false;
  offersIdArr = [];
  OfferDatesArray: any = [];
  bulkAssignedUsers = {};
  savedSearchesFilter: string = '';
  displayPopup$ = new Subject();
  hideApiErrorOnRequestHome$ = new Subject();
  isSelectAcrossAllPages: boolean = false;
  
  constructor(
    private _initialDataService: InitialDataService,
    private authService: AuthService,
    private _http: HttpClient
  ) { 
    // intentionally left empty
  }

  bulkAssignUser: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_ASSIGN
  );
  bulkUnAssignUser: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_UNASSIGN
  );

  bulkAssignDates: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_ASSIGN_DATE
  );

  bulkAssignDatesUJ:string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_ASSIGN_DATE_UJ
  );
  
  bulkJobsUJ:string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_JOBS_UJ
  )
  prePublishOffer: string = this._initialDataService.getConfigUrls(
    CONSTANTS.PRE_PUBLISH_OFFERS
  );

  batchExpand: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BATCH_EXPAND
  );

  preBulkValidateForTemplate: string = this._initialDataService.getConfigUrls(
    CONSTANTS.PRE_VALIDATE_TEMPLATES
  );

  deployDeferPublishOffer: string = this._initialDataService.getConfigUrls(
    CONSTANTS.DEPLOY_DEFER_PUBLISH_OFFERS
  );

  publishBulkOffer: string = this._initialDataService.getConfigUrls(
    CONSTANTS.PUBLISH_BULK_OFFERS
  );

  updateBulkPodAPI: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_UPDATE_POD_API
  );

  updateBulkEventsAPI: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_UPDATE_EVENTS_API
  );

  bulkSubmit: string = this._initialDataService.getConfigUrls(
    CONSTANTS.BULK_SUBMIT
  );

  bulkActionBPDAPI: string = this._initialDataService.getConfigUrls(
      CONSTANTS.BULK_ACTION_BPD_OR
  )
  

  bulkSelected$ = new Subject();
  allOffersSelected$ = new Subject();

  getHeaders() {
    return {
      ...CONSTANTS.HTTP_HEADERS,
      "X-Albertsons-userAttributes": this.authService.getTokenString()
    };
  }

  public bulkActionRequest(searchQuery, asyncAction,programCode, jobType=null) {
    const reqBody = {
      searchQuery,
      asyncActionDetails: [],
      asyncAction
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(`${this.bulkSubmit}${programCode.toLowerCase()}/bulk/action/request`, searchInput);
  }
  public bulkActionBpdRequest(searchQuery, asyncAction,programCode, jobType) {
    const reqBody = {
      searchQuery,
      jobType,
      jobSubType: asyncAction,
      programCodeType: programCode,
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkActionBPDAPI, searchInput);
  }
 
  public bulkAssignUsers(assignUsers, searchQuery) {

    const reqBody = {
      searchQuery,
      assignUsers
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.put(this.bulkAssignUser, searchInput);
  }

  public bulkAssignUsersUJ(assignUsers, searchQuery,jobType,jobSubType,programCode) {

    const reqBody = {
      jobType:jobType,
      jobSubType:jobSubType,
      programCodeType:programCode,
      searchQuery,
      assignUsers
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);
  }

  public bulkUnAssignUsers(unAssignUserTypes, searchQuery) {

    const reqBody = {
      searchQuery,
      unAssignUserTypes
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.put(this.bulkUnAssignUser, searchInput);
  }

  public bulkUnAssignUsersUJ(unAssignUserTypes, searchQuery,jobType,jobSubType,programCode) {

    const reqBody = {
      jobType:jobType,
      jobSubType:jobSubType,
      programCodeType:programCode,
      searchQuery,
      unAssignUserTypes
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);
  }

  hideApiErrorOnRequestHome() {
    return this.hideApiErrorOnRequestHome$.asObservable();
  }
  public bulkAssignedDates(assignDates, searchQuery) {
    const reqBody = {
      searchQuery,
      assignDates
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.put(this.bulkAssignDates, searchInput);
  }

  public bulkAssignedDatesUJ(assignDates, searchQuery,jobType,jobSubType,programCode) {
    const reqBody = {
      jobType : jobType,
      jobSubType : jobSubType,
      programCodeType : programCode,
      searchQuery,
      assignDates
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkAssignDatesUJ, searchInput);
  }
  deferDeployPublishBatchAction(searchQuery, asyncAction,programCodeType){
    let payload = {
      searchQuery,
      asyncAction,
      programCodeType,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.deployDeferPublishOffer, payload);

  }
  bulkActionOffer(searchQuery, asyncAction,programCodeType, jobType = null){
    let payload = {
      searchQuery,
      asyncAction,
      programCodeType,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.deployDeferPublishOffer, payload);

  }
  public preCheckBatch(searchQuery, bulkProcessAction) {
    const reqBody = {
      searchQuery,
      bulkProcessAction
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.prePublishOffer, searchInput);
  }

  public preCheckBatchExpand(searchQuery) {
    
    const reqBody = {
      searchQuery
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.batchExpand, searchInput);
  }

  public registerBatchExpand(payload){
   
    let searchInput = {
      ...payload,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkActionBPDAPI, searchInput);
  }
  
  public publishBulkOffers(id, type) {
    let url = `${this.publishBulkOffer}${'?requestId='}${id}`;
    const reqBody = {
      requestId: id,
      bulkProcessAction: type
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(url, searchInput);
  }

  public publishBulkOffersUJ(programCode,query, type) {
    
    const reqBody = {
      "jobType": "O",
      "jobSubType":type,
      "programCodeType":programCode,
      "searchQuery": query,
      "removeForAll" : ["MF_BATCH_CANCEL","SC_IR_BATCH_CANCEL"].includes(type)
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);
  }

  batchDeployPublishOffers(query, asyncActionKey, pcSelected, jobType){
    let payLoad = {
      jobType: jobType,
      jobSubType: asyncActionKey,
      programCodeType: pcSelected,
      searchQuery: query
    }
    let bulkdeployPublish = {
      ...payLoad,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkActionBPDAPI, bulkdeployPublish);
  }

  public templatePreBatch(searchQuery) {
    const reqBody = {
      searchQuery
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.preBulkValidateForTemplate, searchInput);
  }

  public updateTestingOffers(data) {
    const reqBody = data;
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.publishBulkOffer, searchInput);
  }

  public updateTestingOffersUJ(searchQuery,payload,jobType,jobSubType,programCode) {
    
    const reqBody = {
      jobType : jobType,
      jobSubType : jobSubType,
      programCodeType : programCode,
      searchQuery,
      requestPayload:JSON.stringify(payload)
    };


    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);
  }

  public publishAdInEmail(requestId, bulkProcessAction, requestPayload) {
    requestPayload = JSON.stringify(requestPayload);
    let url = `${this.publishBulkOffer}`;
    const reqBody = {
      requestId,
      bulkProcessAction,
      requestPayload
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(url, searchInput);
  }
  public publishAdInEmailUJ(payload) {
    const reqBody = {
      ...payload
    };


    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);
  }
  public updateBulkPod(searchQuery, podDetailsForm) {
    const reqBody = {
      searchQuery,
      podDetails: podDetailsForm
    };
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.updateBulkPodAPI, searchInput);
  }
  public updateBulkPodUJ(payload:any) {
    const reqBody = {
      ...payload
    };
    
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);
  }
  
  public updateBulkEvents(payload) {
    let searchInput = {
      ...payload,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.publishBulkOffer, searchInput);
 }

 public updateBulkEventsUJ(payload:any) {
  const reqBody = {
    ...payload
  };
  
  let searchInput = {
    ...reqBody,
    reqObj: { headers: this.getHeaders() }
  };
  return this._http.post(this.bulkJobsUJ, searchInput);
}

  updateBulkTerminals(payload) {
    let searchInput = {
      ...payload,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.publishBulkOffer, searchInput);
 }
 updateBulkTerminalsUJ(searchQuery,payload,jobType,jobSubType,programCode) {
  const reqBody = {
    jobType : jobType,
    jobSubType : jobSubType,
    programCodeType : programCode,
    searchQuery,
    "requestPayload":payload
  };


  let searchInput = {
    ...reqBody,
    reqObj: { headers: this.getHeaders() }
  };
  return this._http.post(this.bulkJobsUJ, searchInput);
  
}
 doBatchCopyOR(payload, programCode) {
  let reqPayload = {
    ...payload,
    reqObj: { headers: this.getHeaders() }
  };
  // starting of batch copy api url is same as batch submit so using same.
  return this._http.post(`${this.bulkSubmit}${programCode.toLowerCase()}/bulk/action/request`, reqPayload);

    }
 doBatchCopyBPDOR(payload) {
        let reqPayload = {
            ...payload,
            reqObj: { headers: this.getHeaders() }
        };
        
     return this._http.post(this.bulkActionBPDAPI,reqPayload);
    }
    
 doBatchTemplateUpdateStatus(payload) {
  let reqPayload = {
    ...payload,
    reqObj: { headers: this.getHeaders() }
  };
  return this._http.post(this.bulkActionBPDAPI, reqPayload);
 }

 doBatchCopySC(payload) {
  let reqPayload = {
      ...payload,
      reqObj: { headers: this.getHeaders() }
    };
  return this._http.post(this.bulkActionBPDAPI,reqPayload);
  }
  doRegionalCopy(jobType,jobSubType,programCode,searchQuery,regionIds)
  {
    const reqBody = {
      jobType : jobType,
      jobSubType : jobSubType,
      programCodeType : programCode,
      searchQuery,
      "regionIds":regionIds
    };
    
    let searchInput = {
      ...reqBody,
      reqObj: { headers: this.getHeaders() }
    };
    return this._http.post(this.bulkJobsUJ, searchInput);    
  }
  checkIfActionEnabledForUniversalJob(action) {
    const appData  = this._initialDataService.getAppData();
    if(appData && action) {
      const { featureFlagsUJ = [] } = appData;
      return featureFlagsUJ?.includes(action);
    }
    return false;

  }

  
}

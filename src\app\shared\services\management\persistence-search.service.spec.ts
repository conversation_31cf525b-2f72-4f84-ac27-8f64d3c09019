
import { TestBed } from '@angular/core/testing';
import { PersistenceSearchService } from './persistence-search.service';
import { CommonRouteService } from '../common/common-route.service';
import { ROUTES_CONST } from '@appConstants/routes_constants';

describe('PersistenceSearchService', () => {
  let service: PersistenceSearchService;
  let commonRouteServiceMock: jasmine.SpyObj<CommonRouteService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('CommonRouteService', ['methodName']);  // Add any required methods here

    TestBed.configureTestingModule({
      providers: [
        PersistenceSearchService,
        { provide: CommonRouteService, useValue: spy },
      ],
    });

    service = TestBed.inject(PersistenceSearchService);
    commonRouteServiceMock = TestBed.inject(CommonRouteService) as jasmine.SpyObj<CommonRouteService>;
  });

  it('should return true if routing to Edit/Summary from Mgmnt route', () => {
    const routeInfo = {
      validRouteList: ['/edit', '/summary'],
      currentUrl: '/edit/123',
      mgmntRoute: '/management',
      previousUrl: '/management/overview',
    };

    const result = service.isRouteToEditSummaryFromMgmntRoute(routeInfo);

    expect(result).toBeTrue();
  });

  it('should return false if routing to Edit/Summary from non-Mgmnt route', () => {
    const routeInfo = {
      validRouteList: ['/edit', '/summary'],
      currentUrl: '/edit/123',
      mgmntRoute: '/management',
      previousUrl: '/dashboard',
    };

    const result = service.isRouteToEditSummaryFromMgmntRoute(routeInfo);

    expect(result).toBeFalse();
  });

  it('should return true if routing to Mgmnt route from Edit/Summary route', () => {
    const routeInfo = {
      validRouteList: ['/edit', '/summary'],
      currentUrl: '/management/overview',
      previousUrl: '/edit/123',
    };

    const result = service.isRouteToMgmntPageFromEditSummaryRoute(routeInfo);

    expect(result).toBeFalsy();
  });

  it('should return false if routing to Mgmnt route from non-Edit/Summary route', () => {
    const routeInfo = {
      validRouteList: ['/edit', '/summary'],
      currentUrl: '/management/overview',
      previousUrl: '/dashboard',
    };

    const result = service.isRouteToMgmntPageFromEditSummaryRoute(routeInfo);

    expect(result).toBeTruthy();
  });

  it('should return true if previous route is mgmnt route and not edit/summary/create', () => {
    const previousUrl = '/management/overview';
    const mgmntRoute = '/management';

    const result = service.isPreviousMgmntRoute(previousUrl, mgmntRoute);

    expect(result).toBeTrue();
  });

  it('should return false if previous route contains edit/summary/create', () => {
    const previousUrl = '/management/edit/123';
    const mgmntRoute = '/management';

    const result = service.isPreviousMgmntRoute(previousUrl, mgmntRoute);

    expect(result).toBeFalse();
  });

  it('should return true if coming from a valid route in validRouteList', () => {
    const previousUrl = '/edit/123';
    const validRouteList = ['/edit', '/summary'];

    const result = service.isRouteComingFromValidRouteList(previousUrl, validRouteList);

    expect(result).toBeTrue();
  });

  it('should return false if coming from an invalid route in validRouteList', () => {
    const previousUrl = '/dashboard';
    const validRouteList = ['/edit', '/summary'];

    const result = service.isRouteComingFromValidRouteList(previousUrl, validRouteList);

    expect(result).toBeFalse();
  });

});

import { Injectable } from '@angular/core';
import { ROUTES_CONST } from '@appConstants/routes_constants';
import { CommonRouteService } from '../common/common-route.service';

@Injectable({
  providedIn: 'root'
})
export class PersistenceSearchService {


  constructor(
    public commonRouteService: CommonRouteService) {
      // intentionally left empty
  }
  /**
   * 
   * @param routeInfo 
   * @returns This function will check if user is routing to Edit/summary route from Mgmnt route
   */
  isRouteToEditSummaryFromMgmntRoute(routeInfo) {
    const { validRouteList, currentUrl, mgmntRoute, previousUrl } = routeInfo;
    if (currentUrl && mgmntRoute && previousUrl) {
      const isRoutingToEditSummary = validRouteList?.some((elem) => currentUrl?.startsWith(elem)),
        isMagmntPrevious = this.isPreviousMgmntRoute(previousUrl, mgmntRoute);
      return isRoutingToEditSummary && isMagmntPrevious;
    }
  }
  /**
   * 
   * @param routeInfo 
   * @returns This function will check if user is routing to Mgmnt route from Edit/summary route
   */
  isRouteToMgmntPageFromEditSummaryRoute(routeInfo) {
    const { validRouteList, currentUrl, previousUrl } = routeInfo;
    const isFromEditSummary = this.isRouteComingFromValidRouteList(previousUrl, validRouteList),
      isRoutingToEditSummary = validRouteList?.some((elem) => currentUrl?.startsWith(elem));
    return !isFromEditSummary && !isRoutingToEditSummary
  }
  /**
   * 
   * @param previousUrl 
   * @param mgmntRoute 
   * @returns checks if previous route is mgmnt route only and doesnt have edit, summary and create page
   */
  isPreviousMgmntRoute(previousUrl, mgmntRoute) {
    const editSummaryKeys = [ROUTES_CONST.REQUEST.Edit, ROUTES_CONST.REQUEST.Summary, ROUTES_CONST.REQUEST.Create];
    return previousUrl.startsWith(mgmntRoute) && editSummaryKeys.every(key => !previousUrl.includes(key))
  }
  /**
   * 
   * @param previousUrl 
   * @param validRouteList  - Is the array consist of route path of routes where a persistance should work - like offer edit/ summary
   * @returns This function will check whether user is comint to managemnt page from valid route list
   */
  isRouteComingFromValidRouteList(previousUrl, validRouteList) {
    if (previousUrl) {
      return validRouteList.some((elem) => previousUrl?.startsWith(elem));
    }
    return false;
  }

}

import * as moment from "moment";
import "moment-timezone";

export function mmDdYyyyDateFormat(date) {
  return new Date(
    moment(date)
      .startOf("date")
      .format("MM-DD-YYYY")
  );
}

export function mmDdYyyySlash_DateFormat(date) {
  if (!date) {
    return null;
  }

  const parsedDate = moment(date, ["YYYY-MM-DDTHH:mm:ss.SSSZ", "MM/DD/YYYY"], true);

  if (!parsedDate.isValid()) {
    console.warn("Invalid date format detected:", date);
    return null;
  }

  return parsedDate.utc().format("MM/DD/YYYY");
}

export function mmDdYyyySlash_DateFormatWithoutUTC(date) {
  if (!date) {
    return null;
  }
  const parsedDate = moment(date, ["YYYY-MM-DD", "MM/DD/YYYY"], true); 
  if (!parsedDate.isValid()) {
    console.warn("Invalid date format:", date);
    return null;
  }
  return parsedDate.format("MM/DD/YYYY");
}

export function mmDdYySlash_DateFormat(date) {
  if (!date) {
    return null;
  }
  return moment(date)
    .utc()
    .format("MM/DD/YY");
}
export function LLLL_formatDate(date) {
  return new Date(
    moment()
      .startOf("date")
      .format("LLLL")
  );
}
export function convertUTCToLocalDateWithoutTZ(date) {
  if(!date) {
    return;
  }
  return moment(moment.utc(date).toDate()).local().format('MM/DD/YY');
}

export function convertUTCToLocalDateWithTime(date) {
  if(!date) {
    return "";
  }
  return moment.utc(date).local().format("MM/DD/YY h:mm a");
}

export function L_formatDate(date) {
  return moment(date).format("L");
}

export function toUTC(obj) {
  //While interacting  with API we always need to send it as UTC
  let date = obj.date;
  if (!date) {
    return;
  }
  date = moment(date).utc();
  if (obj.isStartDate) {
    date = date.startOf("day");
  } else {
    date = date.endOf("day");
  }
  return (date.format("YYYY-MM-DDTHH:mm:ss.SSS+00:00"));
}

export function nonUtc(obj) {
  //While interacting  with API we always need to send it as UTC
  let date = obj.date;
  if (!date) {
    return;
  }
  date = moment(date);
  if (obj.isStartDate) {
    date = date.startOf("day");
  } else {
    date = date.endOf("day");
  }
  return ( date.format("YYYY-MM-DDTHH:mm:ss.SSS+00:00"));
}
export function dateInOriginalFormat(obj) {
  let date = obj.date;
  if (!date) {
    return;
  }
  date = moment(date);
  date = date.startOf("day");

  return (date.format("YYYY-MM-DDTHH:mm:ss.SSS-07:00"));
}
export function toMonthWeekYearFormat(date) {
  date = moment(date).format("dddd, MMMM D YYYY, h:mm:ss A");
  return date;
}
export function stringToDate (inputDateString) {
  // convert date string to date object by ignoring the timezone -From StackOverflow
  let date = inputDateString && inputDateString.substring(0,10).split('-')
  date = date && date.length && `${date[1]}-${date[2]}-${date[0]}`
  return new Date(date);
}

export function compareDates (fromDate, toDate) {
  if(toDate && fromDate) {
    const today  = moment().startOf('day');
    const compareFromDate = fromDate == "today" ? today : moment(fromDate).startOf('day');
    const comparedWithDate = moment(toDate).startOf('day');
    return compareFromDate && comparedWithDate.diff(compareFromDate, 'days');
  }
}

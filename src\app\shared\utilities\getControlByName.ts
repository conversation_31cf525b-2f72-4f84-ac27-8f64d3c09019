import { UntypedFormGroup, UntypedFormArray } from "@angular/forms";

/* **
 * Iterates over a FormGroup or FormArray and gets the matching array of controls with  the lookup name 
 *
 * @param {(FormGroup | FormArray)} rootControl - Root form
 * group or form array
 * @param {boolean} [visitChildren=true] - Specify whether it should
 * iterate over nested controls
 * 
 * usage:   let control = this.getControlByName("amount");
 ** */
export function getControlByName(obj) {
  const { rootControl, visitChildren = true, controlName } = <
    { rootControl: UntypedFormGroup | UntypedFormArray; visitChildren: boolean; controlName: string }
  >obj;
  let stack: (UntypedFormGroup | UntypedFormArray)[] = [];
  let matchingControlsArr = [],
    matchingControl;

  // Stack the root FormGroup or FormArray
  if (rootControl && (rootControl instanceof UntypedFormGroup || rootControl instanceof UntypedFormArray)) {
    checkIfControlExists(rootControl);
    stack.push(rootControl);
  }

  while (stack.length > 0) {
    let currentControl = stack.pop();
    (<any>Object).values(currentControl.controls).forEach((control) => {
      // If there are nested forms or formArrays, stack them to visit later
      if (visitChildren && (control instanceof UntypedFormGroup || control instanceof UntypedFormArray)) {
        checkIfControlExists(control);

        stack.push(control);
      }
    });
  }

  function checkIfControlExists(ctrl) {
    matchingControl = ctrl.get(controlName);
    if (matchingControl) {
      matchingControlsArr.push(matchingControl);
    }
  }

  return matchingControlsArr;
}

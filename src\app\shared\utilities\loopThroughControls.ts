import { UntypedFormGroup, UntypedFormArray } from "@angular/forms";

/* **
 * Iterates over a FormGroup or FormArray and executes a callback().
 *
 * @param {(FormGroup | FormArray)} rootControl - Root form
 * group or form array
 * @param {boolean} [visitChildren=true] - Specify whether it should
 * iterate over nested controls
     usage:
     constobj = {
      rootControl: this.generalOfferTypeService.generalOfferTypeForm.get("offerRequestOffers"),
      callback: this.getFormErrors.bind(this),
    };

   loopThroughControls(obj);
 ** */

export function loopThroughControls(obj) {
  let stack: (UntypedFormGroup | UntypedFormArray)[] = [],
    { rootControl, visitChildren = true, callback } = <{ rootControl: UntypedFormGroup | UntypedFormArray; visitChildren: boolean; callback: Function }>(
      obj
    );

  // Stack the root FormGroup or FormArray
  if (rootControl && (rootControl instanceof UntypedFormGroup || rootControl instanceof UntypedFormArray)) {
    stack.push(rootControl);
  }

  while (stack.length > 0) {
    let currentControl = stack.pop();
    (<any>Object).values(currentControl.controls).forEach((control) => {
      // If there are nested forms or formArrays, stack them to visit later
      if (visitChildren && (control instanceof UntypedFormGroup || control instanceof UntypedFormArray)) {
        stack.push(control);
      } else {
        callback({ control });
      }
    });
  }
}

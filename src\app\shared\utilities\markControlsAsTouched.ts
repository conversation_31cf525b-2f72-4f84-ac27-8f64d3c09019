import { UntypedFormGroup, UntypedFormArray } from "@angular/forms";

/* **
 * Iterates over a FormGroup or FormArray and mark all controls as
 * touched, including its children.
 *
 * @param {(FormGroup | FormArray)} rootControl - Root form
 * group or form array
 * @param {boolean} [visitChildren=true] - Specify whether it should
 * iterate over nested controls
 ** */
export function markControlsAsTouched(rootControl: UntypedFormGroup | UntypedFormArray, visitChildren: boolean = true) {
  let stack: (UntypedFormGroup | UntypedFormArray)[] = [];

  // Stack the root FormGroup or FormArray
  if (rootControl && (rootControl instanceof UntypedFormGroup || rootControl instanceof UntypedFormArray)) {
    stack.push(rootControl);
  }

  while (stack.length > 0) {
    let currentControl = stack.pop();
    (<any>Object).values(currentControl.controls).forEach(control => {
      // If there are nested forms or formArrays, stack them to visit later
      if (visitChildren && (control instanceof UntypedFormGroup || control instanceof UntypedFormArray)) {
        stack.push(control);
      } else {
        control.markAsTouched();
      }
    });
  }
}

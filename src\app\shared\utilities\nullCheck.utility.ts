export function nullCheckProperty(object, prop) {
  /*    
    this method gets deep nested property value when property path is string.
    *obj* - *Object* Object that will look for a particular property
    *propertyPath* - *String* Property path to look
    will return the value of the property else undefined
    
    example: const grantMembership = nullCheckProperty(benefit,'groupMemberShip.customerGroupName');
    
    if(!grantMembership){
      this.benefitOption.push(CONSTANTS.BENEFIT_GRANT_MEMBERSHIP);
    } 
  */
  
  let propertyPath = prop;
  let obj = object;
  let i = 0;
  let part;
  if (!obj || typeof obj !== "object" || !propertyPath || typeof propertyPath !== "string") {
    return undefined;
  }
  propertyPath = propertyPath.replace(/\[(\w+)\]/g, ".$1");
  propertyPath = propertyPath.replace(/^\./, "");
  const path = propertyPath.split(".");
  const length = path.length;
  for (; i < length; i++) {
    part = path[i];
    if (obj && typeof obj === "object" && part in obj) {
      obj = obj[part];
    } else {
      return undefined;
    }
  }
  return obj;
}

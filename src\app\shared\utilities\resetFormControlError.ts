import {UntypedFormArray, UntypedFormGroup } from "@angular/forms";
/* **
 * Re-calculates the value and validation status of the entire controls tree.
 */ 
export function resetFormControlError(group: UntypedFormGroup | UntypedFormArray): void {
 if(group && group.controls){
  Object.keys(group?.controls).forEach((key: string) => {
    const abstractControl = group?.controls[key];
   if(abstractControl){
    if (abstractControl instanceof UntypedFormGroup || abstractControl instanceof UntypedFormArray) {
      resetFormControlError(abstractControl);
    } else {
      abstractControl?.setErrors(null);
    }
   }
    
  });
 }
 
}
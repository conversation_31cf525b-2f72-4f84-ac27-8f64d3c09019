//On form submit, focus would be set to the first invalid form element

export function scrollToErrorField(): void {
  let firstElementWithError;
  const isOverlayOpen = document.querySelector("body > modal-container");

  if (isOverlayOpen  && isOverlayOpen !== null) {
    //If any overlay is open, check if there are invalid fields
    firstElementWithError = getFirstErrorFieldInOverlay();
  }
  if (!firstElementWithError) {
    //Check on the main page for any invalid errors
    firstElementWithError = getFirstErrorFieldInPage();
  }

  scrollTo(firstElementWithError);
}

function getFirstErrorFieldInPage() {
  //Check on the main page for any invalid errors
  let firstElementWithError;

  firstElementWithError = document.querySelector(".c-pageLevelErrorWrap");

  if (!firstElementWithError) {
    firstElementWithError = document.querySelector(".border-red");
  }

  if (!firstElementWithError) {
    firstElementWithError = document.querySelector(".border-danger");
  }

  if (!firstElementWithError) {
    firstElementWithError = document.querySelector(".ng-invalid[formControlName]");
  }

  //In some cases in Offer page, we are using custom logic to display invalid messages
  if (!firstElementWithError) {
    firstElementWithError = document.querySelector(".text-danger");
  }

  
  return firstElementWithError;
}

function getFirstErrorFieldInOverlay() {
  //If any overlay is open, check if there are invalid fields
  let firstElementWithError;
  firstElementWithError = document.querySelector(" modal-container .c-pageLevelErrorWrap");

  if (!firstElementWithError) {
    firstElementWithError = document.querySelector(" modal-container .ng-invalid[formControlName]");
  }

  return firstElementWithError;
}

function scrollTo(el: Element): void {
  if (el) {
    el.scrollIntoView({ behavior: "smooth", block: "center" });
  }
}

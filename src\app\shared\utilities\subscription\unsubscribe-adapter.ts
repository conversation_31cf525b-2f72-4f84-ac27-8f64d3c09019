import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { SubSink } from './subscription-pool';

/**
 * A class that automatically unsubscribes all observables when the object gets destroyed
 */
// TO DO: Add Angular decorator.
@Injectable()
export class UnsubscribeAdapter implements OnDestroy {
  /**
   * The subscription sink object that stores all subscriptions
   */
  subs = new SubSink();

  /**
   * The lifecycle hook that unsubscribes all subscriptions when the component / object gets destroyed
   */
  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
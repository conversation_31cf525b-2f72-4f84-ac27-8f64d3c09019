import {UntypedFormArray, UntypedFormGroup } from "@angular/forms";
/* **
 * Re-calculates the value and validation status of the entire controls tree.
 */ 
export function updateTreeValidity(group: UntypedFormGroup | UntypedFormArray): void {
  Object.keys(group.controls).forEach((key: string) => {
    const abstractControl = group.controls[key];

    if (abstractControl instanceof UntypedFormGroup || abstractControl instanceof UntypedFormArray) {
      updateTreeValidity(abstractControl);
    } else {
      abstractControl.updateValueAndValidity();
    }
  });
}
import { Injectable } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ErrorModalComponent } from '../components/modals/error-modal/error-modal.component';

@Injectable({
    providedIn: 'root'
})
export class ErrorUtils {
    bsModalRef: BsModalRef;
    constructor(private _modalService: BsModalService) {
        // intentionally left empty
    }

    showErrorModal(errObj) {
        try {
            let {error:{errors = [],message='' } = {} } = errObj;
            if(errors?.length > 0){
                let errorsObject = errors.reduce((output,item)=>{ let _item = item.split(":"); output.push([_item[0],_item[1]].join(" : ")); return output;},[])
                const initialState = {
                    list: errorsObject,
                    title: "Error"
                };
                this.bsModalRef = this._modalService.show(ErrorModalComponent, {
                    initialState
                });
                this.bsModalRef.content.closeBtnName = "Close";
            }else{
                let errorsObject=[];
                errorsObject.push(message);
                const initialState = {
                    list: errorsObject,
                    title: "Error"
                };
                this.bsModalRef = this._modalService.show(ErrorModalComponent, {
                    initialState
                });
                this.bsModalRef.content.closeBtnName = "Close";
            }
           
        } 
        catch (ex) {
            const initialState = {
                list: ["Unknown error occured!"],
                title: "Error"
            };
            this.bsModalRef = this._modalService.show(ErrorModalComponent, {
                initialState
            });
            this.bsModalRef.content.closeBtnName = "Close";
        }
    }

    private closeAllOpenModals() {
        for (let i = 1; i <= this._modalService.getModalsCount(); i++) {
            this._modalService._hideModal(i);
            // document.body.classList.remove('modal-backdrop.show');
            Array.from(document.getElementsByClassName('modal-backdrop')).forEach((item) => {
                item.parentElement.removeChild(item);
            });
        }
    }

    mapErrorField(key) {
        switch (key) {
            case "productGroups":
                return "Product Group Not Found";
            case "pointsGroups":
                return "Incorrect Point Group";
            case "podStoreGroups":
                return "Incorrect Pod Store Group";
            case "redemptionStoreGroups":
                return "Incorrect Redemption Store Group";
            case "podStoresNotInRedemptionStoreGroups":
                return "Pod Store Group Not in Redemption store Group";
            case "productImageId":
                return "Image entered in pod details is not found";
            case "customerGroups":
                return "Customer Group entered in conditions is incorrect";
            default:
                return key;
        }
    }

}

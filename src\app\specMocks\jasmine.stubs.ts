import { InteractionType } from "@azure/msal-browser";
import { BehaviorSubject, Subject, of } from "rxjs";
  
export const imagePreviewServiceSpy = jasmine.createSpyObj('ImagePreviewService', ['searchImage', 'findImages']);
export const initialDataServiceSpy = jasmine.createSpyObj('InitialDataService', ['getAppData']);
export const sanitizerSpy = jasmine.createSpyObj('DomSanitizer', ['bypassSecurityTrustUrl']);

export const errorUtilsStub = () => ({ showErrorModal: err => ({}) });

export const bsModalRefStub = () => ({ hide: () => ({}) });

export const offerBenefitsServiceStub = () => ({
    benefitOptionValue: {},
    mapBenefitForm: () => ({}),
  });

  export const fileAttachServiceStub = () => ({  
    downloadFile: (string, url) => ({}),
    uploadImportFile: (arg, paramJSON) => ({ subscribe: f => f({}) }),
    getToken: () => ({ subscribe: f => f({}) }),
    uploadImportPAFile: (arg, urlParams, arg1) => ({ subscribe: f => f({}) }),
    uploadFileToOMS: arg => ({ subscribe: f => f({}) }),
    batchImportLogResponse$: { subscribe: (f) => f({}) },
    batchProcessDecline: (action, id) => ({ subscribe: (f) => f({}) }),
    importLog: (arg) => ({ subscribe: (f) => f({}) }),
    markDoneImport: (id) => ({ subscribe: (f) => f({}) }),
    getErrorFile: (id) => ({ subscribe: (f) => f({}) }),
    batchReupload: (arg, arg2) => ({ subscribe: (f) => f({}) }),   
    markDoneImportBpd: (id) => ({ subscribe: (f) => f({}) }),    
    batchReuploadBpd: (arg, arg2) => ({ subscribe: (f) => f({}) }),
    downloadFileWithRequestParams: group => ({ subscribe: f => f({}) })
  });

export  const authServiceStub = () => ({ 
    onUserDataAvailable: () => ({}),
    getTokenString: () => ({}) 
});

export const notificationServiceStub = () => ({
    showNotification: (string, string1) => ({})
});

export const offerConditionsServiceStub = () => ({
    removeConditionRow: { next: () => ({}) },
    conditionTabDelete: {},
    timesErrorMessage: { filter: () => ({}) },
    mapConditionForm: () => ({}),
});

export const searchOfferServiceStub = () => ({
    savedSearchForRetrieve: (reqType, userType) => ({
      subscribe: (f) => f({}),
    }),
    searchAllOffers: (arg, arg2) => ({
      subscribe: (f) => f({}),
      bind: () => ({}),
    }),
    savedSearchforOffer: (searchQuery, savedSearchName, type) => ({
      subscribe: (f) => f({}),
    }),
    updateSavedSearch: (modifyItem, savedSearchName) => ({
      subscribe: (f) => f({}),
    }),
    deleteSavedSearchOffer: (name, type) => ({ subscribe: (f) => f({}) }),
  });

export const iviePromotionServiceStub = () =>({
    getAdBugs: () => ({ subscribe: () => ({}) }),
    iviePromotionSearchObvl: { subscribe: (f) => f({}) },
    searchAllPromotions: () => ({ subscribe: () => ({}) }),
    getFacetCountsData: () => ({}),
    getPaginationSearch: () => ({}),
    submitPromotionArray: () => ({ subscribe: () => ({}) }),
    isRedirectRoute$: { subscribe: (f) => f({}), next: () => ({}) },
    onRouteChange$: { subscribe: (f) => f({}), next: () => ({}) },
    onSearchFilterChange$: { subscribe: (f) => f({}), next: () => ({}) },
    onFacetChipCloseChange$: { subscribe: (f) => f({}), next: () => ({}) },
    onInputSearchChange$: { subscribe: (f) => f({}), next: () => ({}) },
    globalSearchChange$: { subscribe: (f) => f({}), next: () => ({}) },
    searchPromotionPage: { subscribe: (f) => f({}), next: () => ({}) },
    submitNewPromotion: () => of({})
  });

  export const historyServiceStub = () => ({
    getORHistoryDetailsByReqId: (reqid) => ({ subscribe: () => ({}) }),
    getORHistoryPreviewByReqId: (reqid) => ({ subscribe: () => ({}) }),
    getHistoryDetailsDataForGroups: () => ({}),
    getGroupsHistoryById: (id, fetchFields, string) => ({}),
    getOfferHistoryPreviewByOfferId: (reqid) => ({ subscribe: () => ({}) }),
    getOROTHistoryPreviewById: arg => ({}),
    getORHistoryPreviewSubject: (reqid) => ({ subscribe: () => ({}) }),
    getHistoryGroupsPreviewData: () => ({}),
    getHistoryOffersPreviewData: () => ({})
  });


export const initialDataServiceStub = () => ({
    getAppData: () => ({
        regions: { filter: () => ({}), forEach: () => ({}) },
        customerFriendlyProductCategories: { '1': 'Category1' },
        inAdCouponTypes: { '1': 'CouponType1' },
        events: { '1': { hiddenIndicator: 'N' } },
        amountTypes: ["DOLLAR", "ITEMS"],
        offerType: ["SC"],
      }),
    getConfigUrls: () => ({}),
    getAppDataName: string => ({}),
    getOcrpConfig: () => ({ apimKey: "key"}), 
    getOffersLimitToExport: () => (5000),
    getOfferFieldsToExport: () => (["Category","Offer Description","Priority","Defer Evaluation Until EOS"]),
    getDefaultGroups: ()=>({}),
    getProductGroupsData: ()=>({})
  });

export const storeGroupServiceStub = {
    createInstance: () => ({}),
    createFacetInstance: () => ({}),
    setEnableForm: (arg) => ({}),
    searchStoreGroup: (groupName, arg) => ({ subscribe: () => ({}) }),
    setStoreQuery: (object) => ({}),
    getFeatureKeys: () => ({ length: {}, includes: () => ({}) }),
    setFeatureKeys: (arg) => ({}),
    populateStoreFacets: () => ({ subscribe: () => ({}) }),
    populateStoreFilterSearch: (object) => ({}),
    getStoreQuery: () => ({}),
    createStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
    updateStoreGroup: (requestPayload) => ({ subscribe: () => ({}) }),
    call: (arg) => ({}),
    getStoreIds: (object) => ({ subscribe: () => ({}) }),
};

export const productGroupServiceStub = () => ({
    searchExactProduct: (arg, arg1) => ({}),
    searchProductGroup: (arg, arg1) => ({})
  });

export const requestFormServiceStub = () => ({
    displayPgError$: { subscribe: (f) => f({}), next: () => ({}) },
    isReqSubmitAttempted$: new BehaviorSubject(false),
    isDraftSaveAttempted: new BehaviorSubject(false),
    showTotalAmountForAllDiscounts$: new BehaviorSubject({ value: 0, index: 1 }),
    createdApplicationId:"OMS",
    createdApplicationId$: new BehaviorSubject(["nonDigitalRedemptionStoreGroupIds", "id","productGroupName", "amount","programCode", "couponChannel", "customerSegment", "offerLimits","brandAndSize","billingOptions","type"]),
    disableUPPFormControls: () => ({})
});

  export const bsModalServiceStub = { show: (template, options) => ({}) };
  export const offerDetailsServiceStub = () => ({
    listOfferDetailsCode: () => ({ subscribe: f => f({}) }),
    fetchOdcList: () => ({ subscribe: f => f({}) }),
    addOfferDetailsCode: array => ({
      subscribe: f => f({}),
      bind: () => ({})
    }),
    editOfferDetailsCode: { bind: () => ({ subscribe: f => f({}) }) }
  });
  export const offerMappingServiceStub = () => ({
    offerDataSrc: { subscribe: f => f({
        info: {
          offerProgramCode: "GR"
        }
      }) },
    hideApiErrorOnOfferHome$: { subscribe: f => f({}), next: () => ({}) },
    getApprovePodChangeStatus: () => ({ subscribe: f => f({}) }),
    noWhiteSpaceValidator: () => ({}),
    getOfferStatus: () => ({}),
    inlinePodUpdate: () => ({}),
    offerPublish: () => ({}),
    podUpdationFromModal: () => ({}),
    closeOfferDetailModalAttempted: () => ({}),
    inlinePodEditForm:()=>({})
  });
  export const searchOfferRequestServiceStub = () => ({
    searchOffer: (externalOfferId) => ({ subscribe: () => ({}) }),
    paginationCriteria: (object) => ({}),
  });
  export const toastrServiceStub = () => ({
    warning: (string, string1, object) => ({}),
    success: (arg, string, object) => ({}),
    error: (string, string1, object) => ({}),
  });
  export const commonServiceStub = () => ({
    getDepartmentNameFromCode: (department) => ({}),
    getEventsData: () => ({subscribe: (f) => f({})}),
    getEventsListBasedOnFeatureFlag: () => ({}),
    sortProperties:()=>({})
  });
  
  export const offerTemplateBaseServiceStub = () =>{
    amountValueChange$: new BehaviorSubject(null)
  }

  export const changeDetectorRefStub = () => ({ detectChanges: () => ({}) });
  

  export  const activatedReqRouteStub = { snapshot: { params: { 'requestId': '123454' } } };

  export const featureFlagServiceStub = () => ({
    assignFeatureFlag: () => ({}),
    isFeatureFlagEnabled: (arg) => ({}),
    hasFlags: () => ({})
  });

  export const actionLogServiceStub = () => ({
    actionLogData$: { subscribe: (f) => f({}) },
    importLog: (arg) => ({ subscribe: (f) => f({}) }),
    importLogErrors: (id) => ({ subscribe: (f) => f({}) }),
  });
 
  export const queryGeneratorStub = () => ({
    setQuery: (string) => ({}),
    pushParameters: (object) => ({}),
    getQuery: () => ({}),
  });

  export const commonRouteServiceStub = () => ({});
  export const commonRequestRouteServiceStub = () => ({currentActivatedRoute: "request"})
  
  export const baseInputSearchServiceStub = () => ({
    setActiveCurrentSearchType: aCTION_LOG => ({}),
    createSubject: () => ({}),
    currentRouter: {},
    populateChipList: () => ({}),
    postDataForInputSearch: arg => ({}),
    getActiveCurrentSearchType: () => ({})
  });

 
  export const commonSearchServiceStub = () => ({
    batchActionActiveTab: {},
    setActiveCurrentSearchType: currentRouter => ({}),
    currentRouter: {},
    setAllFilterOptions: object => ({}),
    setFilters: object => ({}),
    resetAllFilterOptions: object => ({})
  });
  export const featureFlagsServiceStub = () => ({
    isFeatureFlagEnabled: () => (false)

  });
  export const permissionsServiceStub = () => ({
    loadPermissions: (adminPermissions) => ({}),
    getPermissions: (adminPermissions) => ({}),
  });

  export const bulkUpdateServiceStub = () => ({
    allOffersSelected$: { subscribe: f => f({}) },
    bulkSelectionForOffers: { subscribe: f => f({}) },
    displayPopup$: { next: () => ({}) },
    offersIdArr: {
      push: () => ({}),
      indexOf: () => ({}),
      splice: () => ({}),
      length: {}
    },
    offerIdsListSelected$: { next: () => ({}) },
    updateBulkTerminals: () => of({}),
    hideApiErrorOnRequestHome$: new Subject(),
    checkIfActionEnabledForUniversalJob: () => false,
    updateBulkTerminalsUJ: () => of({}),
    offerBulkSelection: new Subject(),
    isSelectionReset: new Subject(),
  });

  export const MsalServiceStub = () => ({ getUser: { subscribe: () => ({}) } });
  export const MSALGuardConfigFactoryStub = () => ({
    MsalGuardConfiguration: () => ({ 
      interactionType: InteractionType.Redirect,
      authRequest: {
        scopes: ["user.read", "openid", "profile"],
        account: null
      }
    })
  });
  export const mockMsalInstance = {
    addEventCallback: jasmine.createSpy('addEventCallback'),
    setActiveAccount: jasmine.createSpy('setActiveAccount')
  };

  export const activatedRouteStub = () => ({ snapshot: { params: {} } });

  export const addExtraToElement = {
    "addStarToStart": [],
    "addStarToEnd": [
        "cpg",
        "createUserId",
        "imageId",
        "nopaNumbers",
        "effectiveStartDate",
        "pluTriggerBarcode",
        "mobId",
        "userId",
        "externalOfferId",
        "hhid",
        "aggregatorOfferId",
        "saveValueTxt",
        "updatedByUser",
        "createdByUser"
    ],
    "OR": [
        "bggmDesc",
        "bugmDesc",
        "category",
        "headLine",
        "productDesc",
        "headLine2",
        "inEmail",
        "targeted",
        "eventids",
        "podStatus",
        "podDivisionRog",
        "offerRequestorGroup",
        "isApplicableToJ4U",
        "categories",
        "offerFailedState",
        "offerProtoType",
        "offerProgramCd",
        "actionFailedState",
        "regionId",
        "progSubType",
        "programType",
        "nonDigitalUiStatus",
        "digitalUiStatus",
        "adType",
        "deliveryChannel",
        "combinedNonDigitalUser",
        "combinedDigitalUser",
        "group",
        "requestType",
        "cpg",
        "cic",
        "discountType",
        "requestId",
        "templateId",
        "brandAndSize",
        "createUserId",
        "imageId",
        "programCode",
        "mobId",
        "periodWeek",
        "lastPeriodCreated",
        "productGroups",
        "combinedStoreGroups",
        "nopaNumbers",
        "pluTriggerBarcode",
        "rewardsRequired",
        "templateId",
        "userId",
        "externalOfferId",
        "offerName",
        "qualificationBenefitCombinedProductsGroup",
        "offerRequestId",
        "requestedUserId",
        "redemptionStoreId",
        "aggregatorOfferId",
        "saveValueTxt",
        "vehicleName"
    ],
    "TO": [
        "setOtStatusUntil",
        "priceUntil",
        "createTimeStamp",
        "lastUpdateTimestamp",
        "effectiveStartDate",
        "endDt",
        "startDt",
        "lastUpdateTimestamp"
    ],
    "addAsBracketStartAndEnd": [
        "category",
        "inEmail",
        "targeted",
        "eventids",
        "podStatus",
        "podDivisionRog",
        "offerRequestorGroup",
        "isApplicableToJ4U",
        "categories",
        "offerFailedState",
        "offerProtoType",
        "offerProgramCd",
        "actionFailedState",
        "regionId",
        "progSubType",
        "programType",
        "nonDigitalUiStatus",
        "digitalUiStatus",
        "adType",
        "deliveryChannel",
        "combinedNonDigitalUser",
        "combinedDigitalUser",
        "group",
        "requestType",
        "programCd",
        "discountType",
        "imageId",
        "templateId",
        "createUserId",
        "headLine",
        "headLine2",
        "productDesc",
        "verbiageForm",
        "requestId",
        "templateId",
        "brandAndSize",
        "imageId",
        "programCode",
        "mobId",
        "lastPeriodCreated",
        "periodWeek",
        "productGroups",
        "combinedStoreGroups",
        "cic",
        "nopaNumbers",
        "pluTriggerBarcode",
        "rewardsRequired",
        "userId",
        "externalOfferId",
        "offerName",
        "qualificationBenefitCombinedProductsGroup",
        "offerRequestId",
        "requestedUserId",
        "redemptionStoreId",
        "aggregatorOfferId",
        "saveValueTxt",
        "vehicleName",
        "status",
        "bggm",
        "bugm",
        "categoryId",
        "otStatus"
    ],
    "addAsArrayStartAndEnd": [
        "createTimeStamp",
        "lastUpdateTimestamp",
        "effectiveStartDate",
        "endDt",
        "startDt",
        "priceUntil",
        "setOtStatusUntil",
        "lastUpdateTimestamp",
        "createTs"
    ],
    "addAsQueryWithFilter": [
        {
            "status": [
                "digitalUiStatus",
                "nonDigitalUiStatus"
            ],
            "offerStatus": [
                "digitalUiStatus",
                "nonDigitalUiStatus",
                {
                    "offerFailedState": [
                        {
                            "IPU": "PUBLISHING"
                        }
                    ],
                    "minusOfferFailedState": [
                        {
                            "I": "PUBLISHING"
                        }
                    ]
                }
            ],
            "deliveryChannel": [
                {
                    "deliveryChannel": [
                        "CC",
                        "IS",
                        "O"
                    ],
                    "adType": [
                        "IA",
                        "NIA"
                    ]
                }
            ],
            "assignedTo": [
                "combinedDigitalUser",
                "combinedNonDigitalUser"
            ]
        }
    ],
    "addMinusSign": [
        "Was Not"
    ],
}

export const checkChipValuesKey = [
  "setOtStatusUntil",
  "priceUntil",
  "effectiveStartDate",
  "createTimeStamp",
  "startDt"
  
]
export const dateFields = [
  "createTimeStamp",
  "startDt",
  "effectiveStartDate",
  "lastUpdateTimestamp",
  "createTimestamp",
  "updatedTimestamp",
  "createTimestamp",
  "updatedTimestamp"
]

export const addExtraToField = {
  "joinWithHash": [
      "deliveryChannel",
      "status"
  ],
  "addStarToStart": [],
  "addStarToEnd": [
      "saveValueTxt",
      "externalOfferId",
      "aggregatorOfferId",
      "cpg",
      "createUserId",
      "imageId",
      "nopaNumbers",
      "pluTriggerBarcode",
      "mobId",
      "userId",
      "offerRequestId",
      "requestedUserId",
      "startDt",
      "combinedDigitalUser",
      "combinedNonDigitalUser"
  ],
  "addStarToStartAndEnd": [
      "category",
      "combinedStoreGroups",
      "headLine",
      "headLine2",
      "productDesc",
      "verbiageForm",
      "brandAndSize",
      "productGroups",
      "combinedStoreGroups",
      "offerName",
      "qualificationBenefitCombinedProductsGroup",
      "vehicleName",
      "name",
      "name"
  ]
}
export const inputGroupsLevel = {
  "createTimestamp": []
}

export const importLogItems = [
  {
      "jobId": "b6ee0548-bf29-4029-9121-8987f883c328",
      "jobType": "OR",
      "jobSubType": "ASSIGN",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"ASSIGN\",\"programCodeType\":\"SC\",\"searchQuery\":{\"query\":\"requestId=(1769750860)\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"assignUsers\":[]}",
      "programCode": "SC",
      "totalCount": 1,
      "failedCount": 1,
      "successCount": 0,
      "createdDate": "2024-04-10T09:43:01.274+00:00",
      "updatedDate": "2024-04-10T09:43:01.274+00:00",
      "createdByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "ddee8abf-d174-485c-a5ef-42729c186135",
      "jobType": "OR",
      "jobSubType": "ASSIGN",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"ASSIGN\",\"programCodeType\":\"SC\",\"searchQuery\":{\"query\":\"requestId=(1769750860)\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"assignUsers\":[]}",
      "programCode": "SC",
      "totalCount": 1,
      "failedCount": 1,
      "successCount": 0,
      "createdDate": "2024-04-10T09:41:28.612+00:00",
      "updatedDate": "2024-04-10T09:41:28.612+00:00",
      "createdByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "9f9b4d19-6a30-4fec-ae32-c80072e77924",
      "jobType": "OR",
      "jobSubType": "ASSIGN",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"ASSIGN\",\"programCodeType\":\"SC\",\"searchQuery\":{\"query\":\"requestId=(1769750860)\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"assignUsers\":[{\"userId\":\"vshik00\",\"userType\":\"ND\"}]}",
      "programCode": "SC",
      "totalCount": 1,
      "failedCount": 0,
      "successCount": 1,
      "createdDate": "2024-04-10T09:41:11.412+00:00",
      "updatedDate": "2024-04-10T09:41:11.412+00:00",
      "createdByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "6c6156fd-a723-480e-b3ed-dfa56b4b2bc1",
      "jobType": "OR",
      "jobSubType": "ASSIGN",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"ASSIGN\",\"programCodeType\":\"SC\",\"searchQuery\":{\"query\":\"requestId=(1769750860)\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"assignUsers\":[{\"userId\":\"vshik00\",\"userType\":\"DG\"},{\"userId\":\"lpoth00\",\"userType\":\"ND\"}]}",
      "programCode": "SC",
      "totalCount": 1,
      "failedCount": 1,
      "successCount": 0,
      "createdDate": "2024-04-10T09:27:56.741+00:00",
      "updatedDate": "2024-04-10T09:27:56.741+00:00",
      "createdByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "vshik00",
          "firstName": "Vinil",
          "lastName": "Shikhapalli",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "d469f7e0-c9bd-44ea-b2d7-e99f3b7e1708",
      "jobType": "O",
      "jobSubType": "UPDATE_DIV_ROGS",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"O\",\"jobSubType\":\"UPDATE_DIV_ROGS\",\"programCodeType\":\"NA\",\"searchQuery\":{\"query\":\"qualificationPodStoreGroup=1769900519;createdAppId=OMS;endDt=[2024-04-10T08:51:40.549617755Z TO *];offerStatusNegate=CN;\",\"includeTotalCount\":false,\"requiredFieldsToFetch\":[\"externalOfferId\"],\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"requestPayload\":\"{\\\"podDivisionRogs\\\":[{\\\"division\\\":\\\"34\\\",\\\"rogs\\\":[\\\"SWMA\\\"]},{\\\"division\\\":\\\"30\\\",\\\"rogs\\\":[\\\"AIMT\\\"]},{\\\"division\\\":\\\"20\\\",\\\"rogs\\\":[\\\"RDAL\\\"]},{\\\"division\\\":\\\"19\\\",\\\"rogs\\\":[\\\"SPRT\\\"]},{\\\"division\\\":\\\"6\\\",\\\"rogs\\\":[\\\"SE02\\\",\\\"SE03\\\"]},{\\\"division\\\":\\\"5\\\",\\\"rogs\\\":[\\\"SDEN\\\"]}],\\\"redemptionStoreIds\\\":[\\\"2672\\\",\\\"2671\\\",\\\"2792\\\",\\\"1460\\\",\\\"2670\\\",\\\"2791\\\",\\\"1580\\\",\\\"2790\\\",\\\"1459\\\",\\\"1458\\\",\\\"1579\\\",\\\"5817\\\",\\\"1578\\\",\\\"2667\\\",\\\"5818\\\",\\\"1577\\\",\\\"2666\\\",\\\"1331\\\",\\\"4602\\\",\\\"4603\\\",\\\"1231\\\",\\\"1230\\\",\\\"1351\\\",\\\"1590\\\",\\\"5706\\\",\\\"5827\\\",\\\"1227\\\",\\\"1469\\\",\\\"5828\\\",\\\"1588\\\",\\\"5709\\\",\\\"2798\\\",\\\"1103\\\",\\\"1466\\\",\\\"2555\\\",\\\"2797\\\",\\\"5\\\",\\\"6\\\",\\\"2795\\\",\\\"1463\\\",\\\"2794\\\",\\\"9\\\",\\\"801\\\",\\\"923\\\",\\\"4612\\\",\\\"5701\\\",\\\"5822\\\",\\\"803\\\",\\\"4613\\\",\\\"5702\\\",\\\"804\\\",\\\"4614\\\",\\\"5703\\\",\\\"3529\\\",\\\"4615\\\",\\\"4872\\\",\\\"3541\\\",\\\"1482\\\",\\\"1481\\\",\\\"1480\\\",\\\"2690\\\",\\\"2448\\\",\\\"1358\\\",\\\"1479\\\",\\\"5719\\\",\\\"3414\\\",\\\"1478\\\",\\\"1599\\\",\\\"1596\\\",\\\"3531\\\",\\\"2563\\\",\\\"812\\\",\\\"5832\\\",\\\"5713\\\",\\\"5714\\\",\\\"816\\\",\\\"5715\\\",\\\"3792\\\",\\\"2342\\\",\\\"1011\\\",\\\"1131\\\",\\\"2341\\\",\\\"2462\\\",\\\"1010\\\",\\\"1129\\\",\\\"1248\\\",\\\"1369\\\",\\\"1489\\\",\\\"1123\\\",\\\"1365\\\",\\\"2696\\\",\\\"3542\\\",\\\"942\\\",\\\"4631\\\",\\\"5720\\\",\\\"824\\\",\\\"945\\\",\\\"4513\\\",\\\"5603\\\",\\\"709\\\",\\\"1262\\\",\\\"2471\\\",\\\"2592\\\",\\\"1260\\\",\\\"1019\\\",\\\"2106\\\",\\\"3557\\\",\\\"1499\\\",\\\"2466\\\",\\\"3553\\\",\\\"832\\\",\\\"1012\\\",\\\"712\\\",\\\"714\\\",\\\"835\\\",\\\"836\\\",\\\"4404\\\",\\\"716\\\",\\\"4405\\\",\\\"717\\\",\\\"839\\\",\\\"719\\\",\\\"3211\\\",\\\"2485\\\",\\\"2484\\\",\\\"1031\\\",\\\"2482\\\",\\\"3447\\\",\\\"2599\\\",\\\"840\\\",\\\"1267\\\",\\\"2477\\\",\\\"841\\\",\\\"2475\\\",\\\"3564\\\",\\\"722\\\",\\\"1286\\\",\\\"1285\\\",\\\"3342\\\",\\\"1041\\\",\\\"1283\\\",\\\"1160\\\",\\\"1281\\\",\\\"3339\\\",\\\"3217\\\",\\\"3337\\\",\\\"1158\\\",\\\"3215\\\",\\\"2246\\\",\\\"1399\\\",\\\"2004\\\",\\\"3212\\\",\\\"1276\\\",\\\"614\\\",\\\"617\\\",\\\"3595\\\",\\\"3593\\\",\\\"1048\\\",\\\"862\\\",\\\"1047\\\",\\\"1287\\\",\\\"866\\\",\\\"4313\\\",\\\"505\\\",\\\"4316\\\",\\\"508\\\",\\\"4318\\\",\\\"509\\\",\\\"4333\\\",\\\"1066\\\",\\\"1061\\\",\\\"3360\\\",\\\"870\\\",\\\"630\\\",\\\"631\\\",\\\"1177\\\",\\\"1298\\\",\\\"513\\\",\\\"876\\\",\\\"4202\\\",\\\"514\\\",\\\"515\\\",\\\"878\\\",\\\"637\\\",\\\"879\\\",\\\"5670\\\",\\\"4102\\\",\\\"4223\\\",\\\"5675\\\",\\\"3134\\\",\\\"1073\\\",\\\"3250\\\",\\\"1070\\\",\\\"881\\\",\\\"400\\\",\\\"521\\\",\\\"764\\\",\\\"885\\\",\\\"3367\\\",\\\"3004\\\",\\\"3366\\\",\\\"525\\\",\\\"5665\\\",\\\"5666\\\",\\\"406\\\",\\\"4218\\\",\\\"5680\\\",\\\"5681\\\",\\\"4231\\\",\\\"4112\\\",\\\"5685\\\",\\\"4234\\\",\\\"3145\\\",\\\"2051\\\",\\\"892\\\",\\\"412\\\",\\\"775\\\",\\\"3136\\\",\\\"3257\\\",\\\"897\\\",\\\"1078\\\",\\\"415\\\",\\\"536\\\",\\\"899\\\",\\\"4469\\\",\\\"5690\\\",\\\"5694\\\",\\\"5695\\\",\\\"4001\\\",\\\"4002\\\",\\\"4124\\\",\\\"3274\\\",\\\"1094\\\",\\\"782\\\",\\\"420\\\",\\\"3269\\\",\\\"424\\\",\\\"1089\\\",\\\"5688\\\",\\\"549\\\",\\\"429\\\",\\\"4133\\\",\\\"4012\\\",\\\"3042\\\",\\\"430\\\",\\\"3279\\\",\\\"556\\\",\\\"3399\\\",\\\"557\\\",\\\"4007\\\",\\\"4129\\\",\\\"4008\\\",\\\"4260\\\",\\\"4381\\\",\\\"4262\\\",\\\"4142\\\",\\\"4022\\\",\\\"4265\\\",\\\"4023\\\",\\\"4387\\\",\\\"4267\\\",\\\"4025\\\",\\\"3174\\\",\\\"560\\\",\\\"683\\\",\\\"322\\\",\\\"564\\\",\\\"444\\\",\\\"4139\\\",\\\"4150\\\",\\\"4151\\\",\\\"4272\\\",\\\"4030\\\",\\\"4152\\\",\\\"4033\\\",\\\"4277\\\",\\\"3067\\\",\\\"3065\\\",\\\"570\\\",\\\"4270\\\",\\\"571\\\",\\\"572\\\",\\\"331\\\",\\\"573\\\",\\\"574\\\",\\\"575\\\",\\\"577\\\",\\\"218\\\",\\\"339\\\",\\\"4040\\\",\\\"4041\\\",\\\"4163\\\",\\\"4286\\\",\\\"4288\\\",\\\"4289\\\",\\\"3199\\\",\\\"3197\\\",\\\"580\\\",\\\"3194\\\",\\\"581\\\",\\\"3193\\\",\\\"583\\\",\\\"101\\\",\\\"344\\\",\\\"103\\\",\\\"467\\\",\\\"105\\\",\\\"226\\\",\\\"106\\\",\\\"107\\\",\\\"4279\\\",\\\"4294\\\",\\\"4176\\\",\\\"590\\\",\\\"4290\\\",\\\"591\\\",\\\"232\\\",\\\"596\\\",\\\"15\\\",\\\"4187\\\",\\\"18\\\",\\\"19\\\",\\\"120\\\",\\\"244\\\",\\\"126\\\",\\\"128\\\",\\\"20\\\",\\\"24\\\",\\\"28\\\",\\\"4191\\\",\\\"130\\\",\\\"131\\\",\\\"135\\\",\\\"137\\\",\\\"379\\\",\\\"138\\\",\\\"259\\\",\\\"33\\\",\\\"35\\\",\\\"36\\\",\\\"37\\\",\\\"38\\\",\\\"39\\\",\\\"382\\\",\\\"1800\\\",\\\"386\\\",\\\"1920\\\",\\\"145\\\",\\\"269\\\",\\\"1928\\\",\\\"1926\\\",\\\"1804\\\",\\\"42\\\",\\\"43\\\",\\\"1801\\\",\\\"47\\\",\\\"390\\\",\\\"154\\\",\\\"155\\\",\\\"156\\\",\\\"159\\\",\\\"1939\\\",\\\"1938\\\",\\\"51\\\",\\\"1815\\\",\\\"1935\\\",\\\"160\\\",\\\"161\\\",\\\"162\\\",\\\"164\\\",\\\"165\\\",\\\"2911\\\",\\\"166\\\",\\\"2910\\\",\\\"168\\\",\\\"169\\\",\\\"2919\\\",\\\"2918\\\",\\\"60\\\",\\\"1828\\\",\\\"2917\\\",\\\"1827\\\",\\\"62\\\",\\\"2915\\\",\\\"1704\\\",\\\"64\\\",\\\"2913\\\",\\\"65\\\",\\\"1702\\\",\\\"2912\\\",\\\"66\\\",\\\"67\\\",\\\"171\\\",\\\"176\\\",\\\"177\\\",\\\"1710\\\",\\\"1951\\\",\\\"70\\\",\\\"2808\\\",\\\"73\\\",\\\"1716\\\",\\\"1715\\\",\\\"75\\\",\\\"1956\\\",\\\"1713\\\",\\\"180\\\",\\\"182\\\",\\\"183\\\",\\\"184\\\",\\\"2812\\\",\\\"1601\\\",\\\"189\\\",\\\"1721\\\",\\\"2810\\\",\\\"1842\\\",\\\"1960\\\",\\\"2817\\\",\\\"1606\\\",\\\"2816\\\",\\\"1847\\\",\\\"193\\\",\\\"1976\\\",\\\"1612\\\",\\\"1975\\\",\\\"1611\\\",\\\"1731\\\",\\\"1850\\\",\\\"2706\\\",\\\"1616\\\",\\\"1858\\\",\\\"1615\\\",\\\"1614\\\",\\\"2824\\\",\\\"2713\\\",\\\"1620\\\",\\\"1629\\\",\\\"2839\\\",\\\"1627\\\",\\\"1504\\\",\\\"2714\\\",\\\"1880\\\",\\\"1635\\\",\\\"1877\\\",\\\"1998\\\",\\\"1513\\\",\\\"2723\\\",\\\"2722\\\",\\\"1873\\\",\\\"1751\\\",\\\"2961\\\",\\\"1519\\\",\\\"2608\\\",\\\"1759\\\",\\\"2848\\\",\\\"1516\\\",\\\"1758\\\",\\\"2604\\\",\\\"1525\\\",\\\"1888\\\",\\\"1523\\\",\\\"2612\\\",\\\"1644\\\",\\\"1765\\\",\\\"1643\\\",\\\"2853\\\",\\\"1642\\\",\\\"1763\\\",\\\"1762\\\",\\\"1882\\\",\\\"1760\\\",\\\"1527\\\",\\\"2616\\\",\\\"2737\\\",\\\"2979\\\",\\\"1889\\\",\\\"2625\\\",\\\"1657\\\",\\\"3836\\\",\\\"2624\\\",\\\"1656\\\",\\\"2987\\\",\\\"2623\\\",\\\"1896\\\",\\\"1532\\\",\\\"1653\\\",\\\"1652\\\",\\\"1773\\\",\\\"1892\\\",\\\"2629\\\",\\\"1539\\\",\\\"1417\\\",\\\"1538\\\",\\\"2627\\\",\\\"1659\\\",\\\"3713\\\",\\\"1779\\\",\\\"1792\\\",\\\"1791\\\",\\\"2636\\\",\\\"1668\\\",\\\"2999\\\",\\\"1304\\\",\\\"2635\\\",\\\"1667\\\",\\\"3727\\\",\\\"2756\\\",\\\"1666\\\",\\\"3728\\\",\\\"4817\\\",\\\"1665\\\",\\\"3729\\\",\\\"2753\\\",\\\"1542\\\",\\\"2631\\\",\\\"2993\\\",\\\"3723\\\",\\\"4933\\\",\\\"1428\\\",\\\"1548\\\",\\\"2637\\\",\\\"1440\\\",\\\"2892\\\",\\\"1681\\\",\\\"1558\\\",\\\"1678\\\",\\\"1556\\\",\\\"4707\\\",\\\"1554\\\",\\\"2764\\\",\\\"1311\\\",\\\"1553\\\",\\\"1431\\\",\\\"2520\\\",\\\"1552\\\",\\\"2883\\\",\\\"2761\\\",\\\"2528\\\",\\\"1438\\\",\\\"1559\\\",\\\"3750\\\",\\\"2661\\\",\\\"2781\\\",\\\"1570\\\",\\\"1690\\\",\\\"1447\\\",\\\"1568\\\",\\\"1689\\\",\\\"1446\\\",\\\"1203\\\",\\\"1445\\\",\\\"1687\\\",\\\"1565\\\",\\\"1443\\\",\\\"4832\\\",\\\"3508\\\",\\\"5802\\\",\\\"3747\\\"]}\"}",
      "programCode": "NA",
      "totalCount": 1,
      "failedCount": 1,
      "successCount": 0,
      "createdDate": "2024-04-10T08:51:40.586+00:00",
      "updatedDate": "2024-04-10T08:51:40.586+00:00",
      "createdByUser": {
          "userId": "dghod00",
          "firstName": "Deepa",
          "lastName": "Ghodki",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "dghod00",
          "firstName": "Deepa",
          "lastName": "Ghodki",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "b13f343b-3616-4fff-8c24-ed3ab8028c23",
      "jobType": "O",
      "jobSubType": "PUBLISH",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"O\",\"jobSubType\":\"PUBLISH\",\"programCodeType\":\"SPD\",\"searchQuery\":{\"query\":\"externalOfferId=(78043402-D);\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false}",
      "programCode": "SPD",
      "totalCount": 1,
      "failedCount": 0,
      "successCount": 1,
      "createdDate": "2024-04-09T23:36:54.503+00:00",
      "updatedDate": "2024-04-09T23:36:54.504+00:00",
      "createdByUser": {
          "userId": "jgand20",
          "firstName": "Jatla",
          "lastName": "Gandhi",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "jgand20",
          "firstName": "Jatla",
          "lastName": "Gandhi",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "5e46b9dd-6465-484c-806e-c1149a61e046",
      "jobType": "O",
      "jobSubType": "UPDATE_DIV_ROGS",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"O\",\"jobSubType\":\"UPDATE_DIV_ROGS\",\"programCodeType\":\"NA\",\"searchQuery\":{\"query\":\"qualificationPodStoreGroup=1692313602;createdAppId=OMS;endDt=[2024-04-09T20:27:53.688549057Z TO *];offerStatusNegate=CN;\",\"includeTotalCount\":false,\"requiredFieldsToFetch\":[\"externalOfferId\"],\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"requestPayload\":\"{\\\"podDivisionRogs\\\":[{\\\"division\\\":\\\"24\\\",\\\"rogs\\\":[\\\"SHGN\\\"]},{\\\"division\\\":\\\"5\\\",\\\"rogs\\\":[\\\"SDEN\\\"]}],\\\"redemptionStoreIds\\\":[\\\"2792\\\",\\\"2791\\\",\\\"1975\\\",\\\"1578\\\",\\\"2667\\\",\\\"1577\\\",\\\"2666\\\",\\\"631\\\",\\\"876\\\",\\\"879\\\",\\\"637\\\",\\\"4603\\\",\\\"1615\\\",\\\"2824\\\",\\\"1614\\\",\\\"5670\\\",\\\"5675\\\",\\\"5827\\\",\\\"881\\\",\\\"244\\\",\\\"5709\\\",\\\"1466\\\",\\\"2555\\\",\\\"885\\\",\\\"1463\\\",\\\"5665\\\",\\\"801\\\",\\\"5666\\\",\\\"2839\\\",\\\"803\\\",\\\"4613\\\",\\\"804\\\",\\\"5702\\\",\\\"4614\\\",\\\"5703\\\",\\\"4615\\\",\\\"2714\\\",\\\"5680\\\",\\\"5681\\\",\\\"5685\\\",\\\"1480\\\",\\\"1877\\\",\\\"1635\\\",\\\"1998\\\",\\\"892\\\",\\\"2722\\\",\\\"1479\\\",\\\"1599\\\",\\\"1873\\\",\\\"137\\\",\\\"2563\\\",\\\"899\\\",\\\"812\\\",\\\"816\\\",\\\"2342\\\",\\\"2341\\\",\\\"2462\\\",\\\"2612\\\",\\\"1644\\\",\\\"1248\\\",\\\"1760\\\",\\\"5688\\\",\\\"1928\\\",\\\"549\\\",\\\"824\\\",\\\"5603\\\",\\\"390\\\",\\\"3836\\\",\\\"2624\\\",\\\"1656\\\",\\\"1499\\\",\\\"1532\\\",\\\"2466\\\",\\\"556\\\",\\\"557\\\",\\\"3399\\\",\\\"1892\\\",\\\"835\\\",\\\"836\\\",\\\"839\\\",\\\"1792\\\",\\\"1791\\\",\\\"3604\\\",\\\"2911\\\",\\\"2910\\\",\\\"1667\\\",\\\"3727\\\",\\\"683\\\",\\\"3728\\\",\\\"322\\\",\\\"3729\\\",\\\"840\\\",\\\"841\\\",\\\"1267\\\",\\\"722\\\",\\\"2919\\\",\\\"2918\\\",\\\"1828\\\",\\\"60\\\",\\\"2917\\\",\\\"62\\\",\\\"2915\\\",\\\"3723\\\",\\\"64\\\",\\\"2913\\\",\\\"1548\\\",\\\"65\\\",\\\"66\\\",\\\"67\\\",\\\"1286\\\",\\\"1440\\\",\\\"1681\\\",\\\"1554\\\",\\\"2246\\\",\\\"2004\\\",\\\"1552\\\",\\\"2520\\\",\\\"617\\\",\\\"1438\\\",\\\"581\\\",\\\"583\\\",\\\"2812\\\",\\\"1568\\\",\\\"1446\\\",\\\"1721\\\",\\\"2810\\\",\\\"344\\\",\\\"862\\\",\\\"467\\\",\\\"2817\\\",\\\"2816\\\"]}\"}",
      "programCode": "NA",
      "totalCount": 1662,
      "failedCount": 72,
      "successCount": 1590,
      "createdDate": "2024-04-09T20:27:53.732+00:00",
      "updatedDate": "2024-04-09T20:27:53.732+00:00",
      "createdByUser": {
          "userId": "jgand20",
          "firstName": "Jatla",
          "lastName": "Gandhi",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "jgand20",
          "firstName": "Jatla",
          "lastName": "Gandhi",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "68fd575c-2219-463d-975c-cbd5d2ef7e88",
      "jobType": "O",
      "jobSubType": "UPDATE_DIV_ROGS",
      "jobStatus": "COMPLETED",
      "jobPayload": "{\"jobType\":\"O\",\"jobSubType\":\"UPDATE_DIV_ROGS\",\"programCodeType\":\"NA\",\"searchQuery\":{\"query\":\"qualificationPodStoreGroup=1692313602;createdAppId=OMS;endDt=[2024-04-09T20:17:50.237766662Z TO *];offerStatusNegate=CN;\",\"includeTotalCount\":false,\"requiredFieldsToFetch\":[\"externalOfferId\"],\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false,\"requestPayload\":\"{\\\"podDivisionRogs\\\":[{\\\"division\\\":\\\"24\\\",\\\"rogs\\\":[\\\"SHGN\\\"]},{\\\"division\\\":\\\"6\\\",\\\"rogs\\\":[\\\"SE01\\\"]},{\\\"division\\\":\\\"5\\\",\\\"rogs\\\":[\\\"SDEN\\\"]}],\\\"redemptionStoreIds\\\":[\\\"2792\\\",\\\"2791\\\",\\\"1975\\\",\\\"1578\\\",\\\"2667\\\",\\\"1577\\\",\\\"2666\\\",\\\"631\\\",\\\"876\\\",\\\"879\\\",\\\"637\\\",\\\"914\\\",\\\"4603\\\",\\\"1615\\\",\\\"2824\\\",\\\"10\\\",\\\"1614\\\",\\\"5670\\\",\\\"17\\\",\\\"5675\\\",\\\"5827\\\",\\\"881\\\",\\\"244\\\",\\\"5709\\\",\\\"1466\\\",\\\"2555\\\",\\\"885\\\",\\\"920\\\",\\\"1463\\\",\\\"8\\\",\\\"5665\\\",\\\"801\\\",\\\"5666\\\",\\\"2839\\\",\\\"803\\\",\\\"4613\\\",\\\"804\\\",\\\"5702\\\",\\\"4614\\\",\\\"5703\\\",\\\"4615\\\",\\\"2714\\\",\\\"5680\\\",\\\"5681\\\",\\\"27\\\",\\\"5685\\\",\\\"1480\\\",\\\"1877\\\",\\\"1635\\\",\\\"1998\\\",\\\"892\\\",\\\"1116\\\",\\\"2722\\\",\\\"1479\\\",\\\"1599\\\",\\\"1873\\\",\\\"137\\\",\\\"2563\\\",\\\"899\\\",\\\"812\\\",\\\"816\\\",\\\"2342\\\",\\\"2341\\\",\\\"2462\\\",\\\"2612\\\",\\\"1644\\\",\\\"1248\\\",\\\"1760\\\",\\\"5688\\\",\\\"1928\\\",\\\"549\\\",\\\"824\\\",\\\"5603\\\",\\\"41\\\",\\\"390\\\",\\\"3836\\\",\\\"2624\\\",\\\"1656\\\",\\\"1499\\\",\\\"1532\\\",\\\"2466\\\",\\\"556\\\",\\\"557\\\",\\\"3399\\\",\\\"1892\\\",\\\"835\\\",\\\"836\\\",\\\"839\\\",\\\"1792\\\",\\\"1791\\\",\\\"3604\\\",\\\"2911\\\",\\\"2910\\\",\\\"1667\\\",\\\"3727\\\",\\\"683\\\",\\\"3728\\\",\\\"322\\\",\\\"3729\\\",\\\"840\\\",\\\"841\\\",\\\"1267\\\",\\\"722\\\",\\\"2919\\\",\\\"2918\\\",\\\"1828\\\",\\\"60\\\",\\\"2917\\\",\\\"62\\\",\\\"2915\\\",\\\"3723\\\",\\\"64\\\",\\\"2913\\\",\\\"1548\\\",\\\"65\\\",\\\"66\\\",\\\"67\\\",\\\"1286\\\",\\\"1440\\\",\\\"1681\\\",\\\"1038\\\",\\\"1554\\\",\\\"2246\\\",\\\"2004\\\",\\\"1552\\\",\\\"2520\\\",\\\"617\\\",\\\"1438\\\",\\\"581\\\",\\\"583\\\",\\\"2812\\\",\\\"1568\\\",\\\"1446\\\",\\\"1721\\\",\\\"2810\\\",\\\"344\\\",\\\"862\\\",\\\"467\\\",\\\"1045\\\",\\\"2817\\\",\\\"2816\\\"]}\"}",
      "programCode": "NA",
      "totalCount": 1662,
      "failedCount": 72,
      "successCount": 1590,
      "createdDate": "2024-04-09T20:17:50.289+00:00",
      "updatedDate": "2024-04-09T20:17:50.289+00:00",
      "createdByUser": {
          "userId": "jgand20",
          "firstName": "Jatla",
          "lastName": "Gandhi",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "jgand20",
          "firstName": "Jatla",
          "lastName": "Gandhi",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "emailSent": true,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "f45b704d-7587-474e-9dd8-f8cdc2b77a80",
      "jobType": "OR",
      "jobSubType": "EXPORT",
      "jobStatus": "IN_PROGRESS",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"EXPORT\",\"programCodeType\":\"SPD\",\"searchQuery\":{\"query\":\"requestId=(1543984179);\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false}",
      "programCode": "SPD",
      "totalCount": 1,
      "failedCount": 0,
      "successCount": 0,
      "createdDate": "2024-04-09T13:25:34.725+00:00",
      "updatedDate": "2024-04-09T13:25:34.726+00:00",
      "createdByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "85b77757-f2d8-47ff-b0b8-f5f211fd6d84",
      "jobType": "OR",
      "jobSubType": "EXPORT",
      "jobStatus": "IN_PROGRESS",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"EXPORT\",\"programCodeType\":\"SPD\",\"searchQuery\":{\"query\":\"requestId=(1543984179);\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false}",
      "programCode": "SPD",
      "totalCount": 1,
      "failedCount": 0,
      "successCount": 0,
      "createdDate": "2024-04-09T13:15:38.306+00:00",
      "updatedDate": "2024-04-09T13:15:38.306+00:00",
      "createdByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "ab94814c-c738-41f1-9a87-26ac8698d626",
      "jobType": "OR",
      "jobSubType": "EXPORT",
      "jobStatus": "FAILED",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"EXPORT\",\"programCodeType\":\"SPD\",\"searchQuery\":{\"query\":\"requestId=(1543984179);\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false}",
      "programCode": "SPD",
      "createdDate": "2024-04-09T13:14:52.757+00:00",
      "updatedDate": "2024-04-09T13:14:52.757+00:00",
      "createdByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "removeForAll": false,
      "batchWarnEmail": false
  },
  {
      "jobId": "332b2172-8ce7-40b6-8a32-153ba7857db8",
      "jobType": "OR",
      "jobSubType": "EXPORT",
      "jobStatus": "FAILED",
      "jobPayload": "{\"jobType\":\"OR\",\"jobSubType\":\"EXPORT\",\"programCodeType\":\"SPD\",\"searchQuery\":{\"query\":\"requestId=(1543984179);\",\"includeTotalCount\":false,\"retrieveRequiredFields\":false,\"includeFacetCounts\":false,\"exactSearch\":false,\"idsOnly\":false,\"showExpired\":false,\"fetchInvalidStoreGroup\":false},\"removeForAll\":false}",
      "programCode": "SPD",
      "createdDate": "2024-04-09T13:14:05.321+00:00",
      "updatedDate": "2024-04-09T13:14:05.321+00:00",
      "createdByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "updatedByUser": {
          "userId": "ssara37",
          "firstName": "Shalini",
          "lastName": "Saravanan",
          "email": "<EMAIL>"
      },
      "reason": {
          "changeReason": null,
          "changeType": null,
          "reasonComment": null
      },
      "isImport": false,
      "removeForAll": false,
      "batchWarnEmail": false
  }
]
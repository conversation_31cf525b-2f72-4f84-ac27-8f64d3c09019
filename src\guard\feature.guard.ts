import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, UrlTree } from '@angular/router';
import { FeatureFlagsService } from '@appServices/common/feature-flags.service';
@Injectable({
    providedIn: 'root'
})
export class FeatureFlagGuard  {
    constructor(
        private _featureFlags: FeatureFlagsService,
        private _router: Router
    ) { }
    canActivate(next: ActivatedRouteSnapshot): boolean | UrlTree {
        const requiredFeatureFlag: string = next.data.features[
            "requiredFeatureFlag"
        ] as string;
        const featureFlagRedirect: string =
            (next.data.features["featureFlagRedirect"] as string) || "/";
        return this._featureFlags.hasFlags(requiredFeatureFlag)
            ? true
            : this._router.createUrlTree([featureFlagRedirect]);
    }
}
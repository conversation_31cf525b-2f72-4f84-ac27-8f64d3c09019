/****************************************************************************************
                                      Mixins
*****************************************************************************************/
@import "scss/_variables";

@mixin pod-grid {

    .offer-details {
        display: block;
        margin-left: 0 !important;
        margin-top: -4px;
    }

    .reward-grid-item {
        width: 298px;
        height: 220px;
        margin: 0 10px 0 0;
        display: block;
        float: left;
    }
  
    .reward-type {
        font-family: $font-family;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 0px;
      }
      .reward-title { 
        width: 95%;
        height: auto;
        font-family: $font-family;
        font-size: 14px;
        font-weight: bold;
        line-height: 35px;
        float: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
      }
      .reward-limit { 
        width: 100%;
        height: auto;
        display: table;
        float: left;
      }
      .reward-clip-data { 
        width: 100%;
        height: auto;
        display: table;
        float: left;
        line-height: 30px;
      }
      .reward-use-btn-container {
        width: 100%;
        margin-top: 5px;
        display: flex;
        justify-content: center;
      }
      .reward-use-btn {
        font-family: $font-family;
        font-weight: 800;
        padding: 8px 20px;
        border: 1px solid #00529F;
      }
      .reward-desc {
        font-family: $font-family;
        font-size: 12px;
        line-height: 16px;
        display: flex;
        margin: 0 0 0 0;
        padding: 0 0 0 0;
      }
      .reward-desc-text {
        width: calc(100% - 100px);
        display: flex;
        font-size: 12px;
        flex-direction: column;
      }
      .reward-header-img {
        height: 100px;
        width: 100px;
      }
      .reward-img { 
        max-height: 100px;
        max-width: 100px;
        margin: 0 auto;
      }
      .grTile .card, .grTile.card {
        border: none;
      }
      .reward-wrapper {
        width: 100%;
        height: auto;
        padding: 15px;
        float: left;
        box-sizing: border-box;
        border: 2px solid #f6f6f6;
      }
}

@mixin spd-pod-grid-item {
    .spdTile.coupon-grid-offer {
        width: 298px;
        height: auto;
        display: block;
        float: left;
    }
    .spdTile {
        .grid-coupon-container {
            width: 100%;
            display: block;
            float: left;
            border: 2px solid #f6f6f6;
        }
        .grid-coupon-wrapper {
            width: 100%;
            height: unset;
            display: block;
            float: left;
        }
        .grid-coupon-heading {
            width: 100%;
            height: auto;
            display: table;
            float: left;
        }
        .grid-coupon-heading-text-wrapper {
            width: 100%;
            height: auto;
            display: table;
            float: left;
            padding-top: 11px;
        }
        .grid-coupon-heading-text {
            width: 100%;
            height: 100%;
            font-size: $main-font-size;
            margin: 10px 0;
            font-weight: 700;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .svg-icon {
            display: table;
            float: left;
            height: 25px;
            // width: 28px;
            margin: 0 5px 0 5px;
        }
        .grid-coupon-heading-offer-price {
            display: block;
            width: 80%;
            max-width: 200px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
        .grid-coupon-item-description {
            width: 296px;
            height: 111px;
            display: flex;
            justify-content: space-between;
            float: left;
            padding: 8px 14px 8px 14px;
        }
        .grid-coupon-text {
            width: 168px;
            height: 95px;
            display: block;
            float: left;
        }
        .grid-coupon-description-text {
            width: 164px;
            height: auto;
            display: block;
            float: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding-right: 5px;
            //padding-bottom: 5px;
        }
        .grid-coupon-description-text-title {
            overflow: hidden;
            width: 100%;
            height: 20px;
            font-size: 13px;
            font-weight: 700;
        }
        .grid-coupon-description-text-details-wrapper {
            width: 100%;
            min-height: 68px;
            display: block;
            float: left;
            font-size: 12px;
        }
        .grid-coupon-heading-text .in-store-price {
            font-size: 12px;
            padding-left: 43px;
            visibility: hidden;
            color: #757575;
        }
        .grid-coupon-description-text-details {
            display: block;
            overflow: inherit;
            word-break: break-word;
        }
        .grid-coupon-details-link {
            border-bottom: 1px solid #4b4b4b;
        }
        .grid-coupon-description-image > img {
            max-height: 100px;
            max-width: 100px;
        }
        
        .grid-coupon-details {
            width: 100%;
            height: auto;
            display: table;
            padding: 0 14px 14px 14px;
            font-size: 11px;
        }
        .grid-coupon-exp-date {
            width: 45%;
            margin-top: 10px;
            float: right;
        }
        .grid-coupon-details-expiration {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .grid-coupon-details-expiration-date {
            white-space: nowrap;
        }
        .grid-coupon-clip-button-wrapper {
            width: 55%;
            display: block;
            float: left;
        }
        .grid-coupon-btn {
            width: 90%;
            margin-right: 10%;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 !important;
            font-size: $main-font-size;
            font-weight: 800;
            text-shadow: none;
            line-height: 20px;
            border-radius: 2px;
        }
    }
}
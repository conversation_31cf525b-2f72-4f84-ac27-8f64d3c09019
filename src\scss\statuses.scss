@import "mixins";
@import "_variables";
@import "_colors";
@import "radioButton";
@import "media-queries.scss";

.green-status {
  border: 1px solid green;
  color: green;
  font-weight: 800;
  width: 80px;
  text-align: center;
}

.yellow-status {
  width: 80px;
  text-align: center;
  border: 1px solid #e79023;
  color: #e79023;
  font-weight: 800;
}
.purple-status {
  width: 80px;
  text-align: center;
  border: 1px solid #841fa9;
  color: #841fa9;
  font-weight: 800;
}
.blue-status {
  width: 80px;
  text-align: center;
  border: 1px solid #59b1e3;
  color: #59b1e3;
  font-weight: 800;
}
.red-status {
  border: 1px solid $red-primary-rgb;
  color: $red-primary-rgb;
  font-weight: 800;
  width: 80px;
  text-align: center;
}
.deploy-status-background {
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #11c0d0;
  color: #11c0d0;
  font-weight: bold;
}
.publish-status-background {
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #53821e;
  color: #53821e;
  font-weight: bold;
}
.expired-status-background {
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #b75aff;
  color: #b75aff;
  font-weight: bold;
}
.preview-status-background {
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #18c419;
  color: #18c419;
  font-weight: bold;
}
.cancelled-status-background {
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #df353b;
  color: #df353b;
  font-weight: bold;
}
.draft-status-background {
  padding: 0 0 0 2px;
  width: 80px;
  text-align: left;
  border: 1px solid #ff6000;
  color: #ff6000;
  font-weight: bold;
}